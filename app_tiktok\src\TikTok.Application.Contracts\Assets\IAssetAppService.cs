using System;
using System.Threading.Tasks;
using TikTok.Permissions;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.Assets
{
    /// <summary>
    /// Interface cho Asset App Service
    /// </summary>
    public interface IAssetAppService :
        ICrudAppService<
            AssetDto,
            Guid,
            GetAssetListDto,
            CreateAssetDto,
            UpdateAssetDto>
    {
        /// <summary>
        /// Lấy tài sản theo Asset ID
        /// </summary>
        /// <param name="assetId">ID của tài sản</param>
        /// <returns>Tài sản</returns>
        Task<AssetDto> GetByAssetIdAsync(string assetId);

        /// <summary>
        /// Lấy danh sách tài sản theo Business Center ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Danh sách tài sản</returns>
        Task<PagedResultDto<AssetDto>> GetByBcIdAsync(string bcId);
    }
}