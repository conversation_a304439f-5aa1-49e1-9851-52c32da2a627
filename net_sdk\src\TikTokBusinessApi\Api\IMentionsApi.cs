/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Mentions operations
    /// </summary>
    public interface IMentionsApi
    {
        /// <summary>
        /// Retrieve a list of up to 1,000 posts that mention a business in the post caption.
        /// By default, the returned list will be sorted by the highest number of likes.
        /// </summary>
        /// <param name="request">Request parameters for getting mentioned posts</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing mentioned posts</returns>
        Task<GetMentionedPostsResponse> GetMentionedPostsAsync(
            GetMentionedPostsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve the details of a mentioned post obtained from the mentions webhook,
        /// enabling real-time monitoring of business mentions within post captions.
        /// </summary>
        /// <param name="request">Request parameters for getting mentioned post details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing mentioned post details</returns>
        Task<GetMentionedPostDetailsResponse> GetMentionedPostDetailsAsync(
            GetMentionedPostDetailsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve the top 20 keywords in the captions of mentioned posts.
        /// </summary>
        /// <param name="request">Request parameters for getting top keywords</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing top keywords</returns>
        Task<GetTopKeywordsResponse> GetTopKeywordsAsync(
            GetTopKeywordsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve the top 20 hashtags in the captions of mentioned posts.
        /// </summary>
        /// <param name="request">Request parameters for getting top hashtags</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing top hashtags</returns>
        Task<GetTopHashtagsResponse> GetTopHashtagsAsync(
            GetTopHashtagsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve a list of up to 1,000 posts that contain one or more brand hashtags.
        /// A brand hashtag is a hashtag that matches any of the hashtags enabled for your business.
        /// </summary>
        /// <param name="request">Request parameters for getting brand hashtag content</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing brand hashtag content</returns>
        Task<GetBrandHashtagContentResponse> GetBrandHashtagContentAsync(
            GetBrandHashtagContentRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve a list of valid hashtags that you can enable for a brand.
        /// The results will include up to 100 top hashtags related to the Business Account.
        /// </summary>
        /// <param name="request">Request parameters for getting valid brand hashtags</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing valid brand hashtags</returns>
        Task<GetValidBrandHashtagsResponse> GetValidBrandHashtagsAsync(
            GetValidBrandHashtagsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Enable hashtags for a Business Account. Once enabled, the hashtags will be factored
        /// into the results of brand hashtag content retrieval.
        /// </summary>
        /// <param name="request">Request parameters for enabling brand hashtags</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing enabled brand hashtags</returns>
        // Task<EnableBrandHashtagsResponse> EnableBrandHashtagsAsync(
        //     EnableBrandHashtagsRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve the hashtags that have been enabled for a Business Account.
        /// </summary>
        /// <param name="request">Request parameters for getting enabled hashtags</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing enabled hashtags</returns>
        Task<GetEnabledHashtagsResponse> GetEnabledHashtagsAsync(
            GetEnabledHashtagsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete enabled hashtags for a Business Account.
        /// Note: You can only delete hashtags that were enabled at least seven days ago.
        /// </summary>
        /// <param name="request">Request parameters for deleting enabled hashtags</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response for deleting enabled hashtags</returns>
        // Task<DeleteEnabledHashtagsResponse> DeleteEnabledHashtagsAsync(
        //     DeleteEnabledHashtagsRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve a list of up to 1,000 post comments or replies that mention a business's handle.
        /// If more than 1,000 comments mention the business's handle, the top 1,000 with the highest
        /// number of comment likes will be returned.
        /// </summary>
        /// <param name="request">Request parameters for getting mentions in comments</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing mentions in comments</returns>
        Task<GetMentionsInCommentsResponse> GetMentionsInCommentsAsync(
            GetMentionsInCommentsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve the details of a mention in a comment obtained from the mentions webhook,
        /// enabling real-time monitoring of business mentions in comment threads.
        /// </summary>
        /// <param name="request">Request parameters for getting comment mention details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing comment mention details</returns>
        Task<GetCommentMentionDetailsResponse> GetCommentMentionDetailsAsync(
            GetCommentMentionDetailsRequest request,
            CancellationToken cancellationToken = default);
    }
}
