/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for updating ad statuses
    /// </summary>
    public class AdStatusUpdateBody
    {
        /// <summary>
        /// A list of ad IDs. Allowed quantity: 1-20. Either ad_ids or aco_ad_ids has to be set.
        /// </summary>
        [JsonPropertyName("ad_ids")]
        public List<string>? AdIds { get; set; }

        /// <summary>
        /// A list of ACO ad IDs. Only support ENABLE and DISABLE for ACO ads. Allowed quantity: 1-20. Either ad_ids or aco_ad_ids has to be set.
        /// </summary>
        [JsonPropertyName("aco_ad_ids")]
        public List<string>? AcoAdIds { get; set; }

        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The operation being made. Enum values: DELETE (delete), DISABLE (pause), ENABLE (enable).
        /// Note: The status of deleted ads or ads in deleted campaigns or ad groups cannot be modified.
        /// </summary>
        [JsonPropertyName("operation_status")]
        [Required]
        public string OperationStatus { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the AdStatusUpdateBody class
        /// </summary>
        public AdStatusUpdateBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdStatusUpdateBody class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="operationStatus">Operation status</param>
        public AdStatusUpdateBody(string advertiserId, string operationStatus)
        {
            AdvertiserId = advertiserId;
            OperationStatus = operationStatus;
        }

        /// <summary>
        /// Sets the advertiser ID and returns this instance for method chaining
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>This instance</returns>
        public AdStatusUpdateBody SetAdvertiserId(string advertiserId)
        {
            AdvertiserId = advertiserId;
            return this;
        }

        /// <summary>
        /// Sets the operation status and returns this instance for method chaining
        /// </summary>
        /// <param name="operationStatus">Operation status</param>
        /// <returns>This instance</returns>
        public AdStatusUpdateBody SetOperationStatus(string operationStatus)
        {
            OperationStatus = operationStatus;
            return this;
        }

        /// <summary>
        /// Sets the ad IDs and returns this instance for method chaining
        /// </summary>
        /// <param name="adIds">List of ad IDs</param>
        /// <returns>This instance</returns>
        public AdStatusUpdateBody SetAdIds(List<string> adIds)
        {
            AdIds = adIds;
            return this;
        }

        /// <summary>
        /// Sets the ACO ad IDs and returns this instance for method chaining
        /// </summary>
        /// <param name="acoAdIds">List of ACO ad IDs</param>
        /// <returns>This instance</returns>
        public AdStatusUpdateBody SetAcoAdIds(List<string> acoAdIds)
        {
            AcoAdIds = acoAdIds;
            return this;
        }

        /// <summary>
        /// Adds an ad ID to the list and returns this instance for method chaining
        /// </summary>
        /// <param name="adId">Ad ID to add</param>
        /// <returns>This instance</returns>
        public AdStatusUpdateBody AddAdId(string adId)
        {
            AdIds ??= new List<string>();
            AdIds.Add(adId);
            return this;
        }

        /// <summary>
        /// Adds an ACO ad ID to the list and returns this instance for method chaining
        /// </summary>
        /// <param name="acoAdId">ACO ad ID to add</param>
        /// <returns>This instance</returns>
        public AdStatusUpdateBody AddAcoAdId(string acoAdId)
        {
            AcoAdIds ??= new List<string>();
            AcoAdIds.Add(acoAdId);
            return this;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdStatusUpdateBody(AdvertiserId={AdvertiserId}, OperationStatus={OperationStatus}, AdIds={AdIds?.Count ?? 0} items, AcoAdIds={AcoAdIds?.Count ?? 0} items)";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not AdStatusUpdateBody other)
                return false;

            return AdvertiserId == other.AdvertiserId &&
                   OperationStatus == other.OperationStatus &&
                   Equals(AdIds, other.AdIds) &&
                   Equals(AcoAdIds, other.AcoAdIds);
        }

        /// <summary>
        /// Returns the hash code for this instance
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AdvertiserId, OperationStatus, AdIds, AcoAdIds);
        }
    }
}
