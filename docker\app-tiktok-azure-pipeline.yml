trigger: none

pool:
  name: EdTech_Win

variables:
  - group: CustomArtifacts
  - name: outputFolder
    value: "output/$(Build.BuildNumber)"

steps:
  - checkout: self
    persistCredentials: true   # 👈 Bật quyền dùng System.AccessToken

  - powershell: |
      $npmrcPath = "$env:USERPROFILE\.npmrc"
      Write-Host "⚙️ Creating temporary .npmrc at $npmrcPath"

      Add-Content -Path $npmrcPath -Value "@tsp:registry=https://pkgs.dev.azure.com/bpn25106/_packaging/node_modules/npm/registry/"
      Add-Content -Path $npmrcPath -Value "//pkgs.dev.azure.com/bpn25106/_packaging/node_modules/npm/registry/:_authToken=$env:SYSTEM_ACCESSTOKEN"
      Add-Content -Path $npmrcPath -Value "always-auth=true"

      Write-Host "✅ .npmrc created successfully"
    displayName: "Generate .npmrc with System.AccessToken"

  - task: PowerShell@2
    displayName: "Build WebApp & DbMigrator"
    inputs:
      filePath: "docker/app-tiktok/build-images-locally.ps1"
      arguments: "-version $(Build.BuildNumber) -modulePath 'app_tiktok' -webAppFolder 'src/TikTok.Web' -outputFolder '$(outputFolder)' -artifactName 'tiktok'"

  - task: PublishPipelineArtifact@1
    inputs:
      targetPath: "$(Build.SourcesDirectory)/$(outputFolder)"
      artifact: "tiktok-$(Build.BuildNumber)"
  - task: PowerShell@2
    displayName: "Clean up output folder"
    inputs:
      targetType: inline
      script: |
        $outputPath = "$(Build.SourcesDirectory)/$(outputFolder)"
        Write-Host "🧹 Cleaning up output folder: $outputPath"
        if (Test-Path $outputPath) {
          Remove-Item -Recurse -Force $outputPath
          Write-Host "✅ Output folder removed"
        } else {
          Write-Host "⚠️ Output folder does not exist, nothing to clean"
        }
