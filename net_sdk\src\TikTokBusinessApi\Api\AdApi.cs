/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Ad operations
    /// </summary>
    public class AdApi : IAdApi
    {
        private readonly IApiClient _apiClient;

        /// <summary>
        /// Initializes a new instance of the AdApi class
        /// </summary>
        /// <param name="apiClient">API client for making requests</param>
        public AdApi(IApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new System.ArgumentNullException(nameof(apiClient));
        }

        /// <inheritdoc />
        public async Task<AdGetResponse> GetAdsAsync(
            string advertiserId,
            List<string>? fields = null,
            List<string>? excludeFieldTypesInResponse = null,
            AdGetFiltering? filtering = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            if (page < 1)
                throw new System.ArgumentException("Page must be greater than 0", nameof(page));

            if (pageSize < 1 || pageSize > 1000)
                throw new System.ArgumentException("Page size must be between 1 and 1000", nameof(pageSize));

            var request = new AdGetRequest
            {
                AdvertiserId = advertiserId,
                Fields = fields,
                ExcludeFieldTypesInResponse = excludeFieldTypesInResponse,
                Filtering = filtering,
                Page = page,
                PageSize = pageSize
            };

            return await _apiClient.CallApiAsync<AdGetResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Ad.Get}",
                System.Net.Http.HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <inheritdoc />
        // public async Task<AdCreateResponse> CreateAdsAsync(
        //     AdCreateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     System.ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.AdgroupId))
        //         throw new System.ArgumentException("Ad group ID cannot be null or empty", nameof(request));

        //     if (request.Creatives == null || request.Creatives.Count == 0)
        //         throw new System.ArgumentException("At least one creative must be provided", nameof(request));

        //     return await _apiClient.CallApiAsync<AdCreateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Ad.Create}",
        //         System.Net.Http.HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <inheritdoc />
        // public async Task<AdUpdateResponse> UpdateAdsAsync(
        //     AdUpdateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     System.ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.AdgroupId))
        //         throw new System.ArgumentException("Ad group ID cannot be null or empty", nameof(request));

        //     if (request.Creatives == null || request.Creatives.Count == 0)
        //         throw new System.ArgumentException("At least one creative must be provided", nameof(request));

        //     return await _apiClient.CallApiAsync<AdUpdateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Ad.Update}",
        //         System.Net.Http.HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <inheritdoc />
        // public async Task<AdStatusUpdateResponse> UpdateAdStatusAsync(
        //     AdStatusUpdateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     System.ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     // Either AdIds or AcoAdIds must be provided, but not both
        //     if ((request.AdIds == null || request.AdIds.Count == 0) &&
        //         (request.AcoAdIds == null || request.AcoAdIds.Count == 0))
        //         throw new System.ArgumentException("At least one ad ID or ACO ad ID must be provided", nameof(request));

        //     if (request.AdIds?.Count > 0 && request.AcoAdIds?.Count > 0)
        //         throw new System.ArgumentException("Cannot specify both ad IDs and ACO ad IDs in the same request", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.OperationStatus))
        //         throw new System.ArgumentException("Operation status cannot be null or empty", nameof(request));

        //     return await _apiClient.CallApiAsync<AdStatusUpdateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Ad.UpdateStatus}",
        //         System.Net.Http.HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }
    }
}
