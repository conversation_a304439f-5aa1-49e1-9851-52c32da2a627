using System;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// DTO để cập nhật tài khoản quảng cáo với thông tin số dư và ngân sách
    /// </summary>
    public class UpdateBalanceAdAccountDto
    {
        /// <summary>
        /// ID tài khoản nhà quảng cáo
        /// </summary>
        [Required(ErrorMessage = "AdvertiserId là bắt buộc")]
        [StringLength(50, ErrorMessage = "AdvertiserId không được vượt quá 50 ký tự")]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// Tên tài khoản nhà quảng cáo
        /// </summary>
        [Required(ErrorMessage = "AdvertiserName là bắt buộc")]
        [StringLength(200, ErrorMessage = "AdvertiserName không được vượt quá 200 ký tự")]
        public string AdvertiserName { get; set; }

        /// <summary>
        /// Trạng thái hiển thị của tài khoản nhà quảng cáo
        /// </summary>
        [Required(ErrorMessage = "AdvertiserStatus là bắt buộc")]
        public AdvertiserAccountStatus AdvertiserStatus { get; set; }

        /// <summary>
        /// Loại tài khoản nhà quảng cáo
        /// </summary>
        [Required(ErrorMessage = "AdvertiserType là bắt buộc")]
        public AdAccountType AdvertiserType { get; set; }

        /// <summary>
        /// Múi giờ của tài khoản nhà quảng cáo
        /// </summary>
        [Required(ErrorMessage = "Timezone là bắt buộc")]
        [StringLength(50, ErrorMessage = "Timezone không được vượt quá 50 ký tự")]
        public string Timezone { get; set; }

        /// <summary>
        /// Tiền tệ của tài khoản nhà quảng cáo
        /// </summary>
        [Required(ErrorMessage = "Currency là bắt buộc")]
        [StringLength(10, ErrorMessage = "Currency không được vượt quá 10 ký tự")]
        public string Currency { get; set; }

        /// <summary>
        /// Số ngày tài khoản nhà quảng cáo đã được mở
        /// </summary>
        [Required(ErrorMessage = "AccountOpenDays là bắt buộc")]
        [Range(0, int.MaxValue, ErrorMessage = "AccountOpenDays phải lớn hơn hoặc bằng 0")]
        public int AccountOpenDays { get; set; }

        /// <summary>
        /// Cảnh báo khi số dư đạt ngưỡng
        /// </summary>
        [Required(ErrorMessage = "BalanceReminder là bắt buộc")]
        public bool BalanceReminder { get; set; }

        /// <summary>
        /// Tên công ty của tài khoản nhà quảng cáo
        /// </summary>
        [StringLength(200, ErrorMessage = "Company không được vượt quá 200 ký tự")]
        public string? Company { get; set; }

        /// <summary>
        /// Tên liên hệ của tài khoản nhà quảng cáo
        /// </summary>
        [StringLength(100, ErrorMessage = "ContactName không được vượt quá 100 ký tự")]
        public string? ContactName { get; set; }

        /// <summary>
        /// Email liên hệ của tài khoản nhà quảng cáo
        /// </summary>
        [StringLength(100, ErrorMessage = "ContactEmail không được vượt quá 100 ký tự")]
        [EmailAddress(ErrorMessage = "ContactEmail không đúng định dạng email")]
        public string? ContactEmail { get; set; }

        /// <summary>
        /// Thời gian mở tài khoản nhà quảng cáo (UTC+0)
        /// </summary>
        [Required(ErrorMessage = "CreateTime là bắt buộc")]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// Tổng số dư tài khoản nhà quảng cáo
        /// </summary>
        [Required(ErrorMessage = "AccountBalance là bắt buộc")]
        [Range(0, double.MaxValue, ErrorMessage = "AccountBalance phải lớn hơn hoặc bằng 0")]
        public decimal AccountBalance { get; set; }

        /// <summary>
        /// Số dư tài khoản hợp lệ
        /// </summary>
        [Required(ErrorMessage = "ValidAccountBalance là bắt buộc")]
        [Range(0, double.MaxValue, ErrorMessage = "ValidAccountBalance phải lớn hơn hoặc bằng 0")]
        public decimal ValidAccountBalance { get; set; }

        /// <summary>
        /// Số dư bị đóng băng
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "FrozenBalance phải lớn hơn hoặc bằng 0")]
        public decimal FrozenBalance { get; set; }

        /// <summary>
        /// Thuế của tài khoản nhà quảng cáo
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Tax phải lớn hơn hoặc bằng 0")]
        public decimal Tax { get; set; }

        /// <summary>
        /// Số dư tiền mặt
        /// </summary>
        [Required(ErrorMessage = "CashBalance là bắt buộc")]
        [Range(0, double.MaxValue, ErrorMessage = "CashBalance phải lớn hơn hoặc bằng 0")]
        public decimal CashBalance { get; set; }

        /// <summary>
        /// Số dư tiền mặt hợp lệ
        /// </summary>
        [Required(ErrorMessage = "ValidCashBalance là bắt buộc")]
        [Range(0, double.MaxValue, ErrorMessage = "ValidCashBalance phải lớn hơn hoặc bằng 0")]
        public decimal ValidCashBalance { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "GrantBalance phải lớn hơn hoặc bằng 0")]
        public decimal GrantBalance { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher hợp lệ
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "ValidGrantBalance phải lớn hơn hoặc bằng 0")]
        public decimal ValidGrantBalance { get; set; }

        /// <summary>
        /// Số tiền có thể chuyển từ tài khoản nhà quảng cáo
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "TransferableAmount phải lớn hơn hoặc bằng 0")]
        public decimal? TransferableAmount { get; set; }

        /// <summary>
        /// Chế độ ngân sách của tài khoản quảng cáo
        /// </summary>
        [Required(ErrorMessage = "BudgetMode là bắt buộc")]
        public BudgetMode BudgetMode { get; set; }

        /// <summary>
        /// Ngân sách (tùy theo budget_mode)
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Budget phải lớn hơn hoặc bằng 0")]
        public decimal Budget { get; set; }

        /// <summary>
        /// Chi phí ngân sách đã sử dụng
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "BudgetCost phải lớn hơn hoặc bằng 0")]
        public decimal BudgetCost { get; set; }

        /// <summary>
        /// Ngân sách còn lại
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "BudgetRemaining phải lớn hơn hoặc bằng 0")]
        public decimal BudgetRemaining { get; set; }

        /// <summary>
        /// Thông tin hạn chế tần suất thay đổi ngân sách
        /// </summary>
        public BudgetFrequencyRestrictionDto BudgetFrequencyRestriction { get; set; }

        /// <summary>
        /// Thông tin hạn chế số tiền thay đổi ngân sách
        /// </summary>
        public BudgetAmountRestrictionDto BudgetAmountRestriction { get; set; }

        /// <summary>
        /// Số tiền tối thiểu có thể chuyển
        /// </summary>
        public MinTransferableAmountDto MinTransferableAmount { get; set; }

        /// <summary>
        /// ID của Business Center
        /// </summary>
        [Required(ErrorMessage = "BcId là bắt buộc")]
        [StringLength(100, ErrorMessage = "BcId không được vượt quá 100 ký tự")]
        public string BcId { get; set; }

        /// <summary>
        /// Thời gian nạp tiền lần cuối
        /// </summary>
        public DateTime? LatestRechargeTime { get; set; }

        /// <summary>
        /// Số tiền nạp lần đầu
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "FirstRechargeAmount phải lớn hơn hoặc bằng 0")]
        public decimal FirstRechargeAmount { get; set; }

        /// <summary>
        /// Số tiền đã nạp
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "RechargeAmount phải lớn hơn hoặc bằng 0")]
        public decimal RechargeAmount { get; set; }

        /// <summary>
        /// Số lần nạp tiền
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "RechargeCount phải lớn hơn hoặc bằng 0")]
        public int RechargeCount { get; set; }

        /// <summary>
        /// Thời gian nhận ngân sách
        /// </summary>
        public DateTime? Date { get; set; }
    }
}