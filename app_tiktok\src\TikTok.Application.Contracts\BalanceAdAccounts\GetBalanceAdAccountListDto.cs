using System;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// DTO cho việc lấy danh sách tài khoản nhà quảng cáo
    /// </summary>
    public class GetBalanceAdAccountListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Từ khóa tìm kiếm
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// Advertiser ID
        /// </summary>
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Advertiser Name
        /// </summary>
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// Business Center ID
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// Advertiser Status
        /// </summary>
        public AdvertiserAccountStatus? AdvertiserStatus { get; set; }

        /// <summary>
        /// Advertiser Type
        /// </summary>
        public AdAccountType? AdvertiserType { get; set; }

        /// <summary>
        /// Budget Mode
        /// </summary>
        public BudgetMode? BudgetMode { get; set; }

        /// <summary>
        /// Currency
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Timezone
        /// </summary>
        public string? Timezone { get; set; }

        /// <summary>
        /// Thời gian tạo từ
        /// </summary>
        public DateTime? CreateTimeFrom { get; set; }

        /// <summary>
        /// Thời gian tạo đến
        /// </summary>
        public DateTime? CreateTimeTo { get; set; }

        /// <summary>
        /// Số dư tài khoản từ
        /// </summary>
        public decimal? AccountBalanceFrom { get; set; }

        /// <summary>
        /// Số dư tài khoản đến
        /// </summary>
        public decimal? AccountBalanceTo { get; set; }

        /// <summary>
        /// Thời gian nhận ngân sách từ
        /// </summary>
        public DateTime? DateFrom { get; set; }

        /// <summary>
        /// Thời gian nhận ngân sách đến
        /// </summary>
        public DateTime? DateTo { get; set; }

        /// <summary>
        /// Ngân sách từ
        /// </summary>
        public decimal? BudgetFrom { get; set; }

        /// <summary>
        /// Ngân sách đến
        /// </summary>
        public decimal? BudgetTo { get; set; }

        /// <summary>
        /// Số ngày mở tài khoản từ
        /// </summary>
        public int? AccountOpenDaysFrom { get; set; }

        /// <summary>
        /// Số ngày mở tài khoản đến
        /// </summary>
        public int? AccountOpenDaysTo { get; set; }
    }
}