﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace TikTok.Notifications
{
    /// <summary>
    /// Thông tin về việc l<PERSON>y dữ liệu
    /// </summary>
    public class DataRetrievalInfo
    {
        [JsonPropertyName("startTime")]
        public DateTime StartTime { get; set; }

        [JsonPropertyName("endTime")]
        public DateTime? EndTime { get; set; }

        [JsonPropertyName("duration")]
        public TimeSpan? Duration { get; set; }

        [JsonPropertyName("bcId")]
        public string BcId { get; set; }

        [JsonPropertyName("advertiserIdsCount")]
        public int AdvertiserIdsCount { get; set; }

        [JsonPropertyName("retrievedDataTypes")]
        public List<string> RetrievedDataTypes { get; set; } = new List<string>();

        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("errorMessage")]
        public string ErrorMessage { get; set; }

        [JsonPropertyName("performanceMetrics")]
        public Dictionary<string, object> PerformanceMetrics { get; set; } = new Dictionary<string, object>();
    }
}
