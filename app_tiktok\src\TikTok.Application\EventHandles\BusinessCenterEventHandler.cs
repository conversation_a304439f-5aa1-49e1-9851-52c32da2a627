using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.BusinessCenters;
using TikTok.Entities;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Entities.Events;
using Volo.Abp.EventBus;
using Volo.Abp.EventBus.Distributed;

namespace TikTok.EventHandles
{
    public class BusinessCenterEventHandler : ILocalEventHandler<EntityChangedEventData<RawBusinessCenterEntity>>, ITransientDependency
    {
        private readonly IBusinessCenterCache _cache;

        public BusinessCenterEventHandler(IBusinessCenterCache cache)
        {
            _cache = cache;
        }

        public async Task HandleEventAsync(EntityChangedEventData<RawBusinessCenterEntity> eventData)
        {
            if (eventData == null || eventData.Entity == null)
            {
                return;
            }

            await _cache.CleanCache();
        }
    }
}