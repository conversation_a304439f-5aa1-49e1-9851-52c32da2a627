# AdReviewApi Documentation

The `AdReviewApi` provides methods to manage ad review operations in the TikTok Business API, including getting review information for ad groups and ads, and appealing rejection decisions.

## Overview

The AdReviewApi allows you to:
- Get review information for ad groups
- Get review information for ads
- Appeal rejection decisions for ad groups

## Available Methods

### GetAdGroupReviewInfoAsync

Retrieves review information for ad groups. An ad group needs to be approved before it can be deployed, and it may get rejected due to various reasons, such as incorrect placement or targeting selections.

**Endpoint:** `/v1.3/adgroup/review_info/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

- `accessToken` (string, required): Authorized access token
- `advertiserId` (string, required): Advertiser ID
- `adGroupIds` (List<string>, required): List of ad group IDs (maximum 20)
- `language` (string, optional): Language for the response
- `cancellationToken` (CancellationToken, optional): Cancellation token

#### Example

```csharp
var client = new TikTokBusinessApiClient("your-access-token");

var adGroupIds = new List<string> { "adgroup-id-1", "adgroup-id-2" };

var response = await client.AdReview.GetAdGroupReviewInfoAsync(
    accessToken: "your-access-token",
    advertiserId: "your-advertiser-id",
    adGroupIds: adGroupIds,
    language: "EN"
);

foreach (var adGroupReview in response.Data.AdGroupReviewMap)
{
    Console.WriteLine($"Ad Group {adGroupReview.Key}: {adGroupReview.Value.ReviewStatus}");
    if (adGroupReview.Value.RejectInfo != null)
    {
        foreach (var reject in adGroupReview.Value.RejectInfo)
        {
            Console.WriteLine($"Rejection reason: {reject.Suggestion}");
        }
    }
}
```

### GetAdReviewInfoAsync

Retrieves review information for ads.

**Endpoint:** `/v1.3/ad/review_info/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

- `accessToken` (string, required): Authorized access token
- `advertiserId` (string, required): Advertiser ID
- `adIds` (List<string>, required): List of ad IDs (1-100)
- `language` (string, optional): Language for the response
- `cancellationToken` (CancellationToken, optional): Cancellation token

#### Example

```csharp
var client = new TikTokBusinessApiClient("your-access-token");

var adIds = new List<string> { "ad-id-1", "ad-id-2" };

var response = await client.AdReview.GetAdReviewInfoAsync(
    accessToken: "your-access-token",
    advertiserId: "your-advertiser-id",
    adIds: adIds,
    language: "EN"
);

foreach (var adReview in response.Data.AdReviewMap)
{
    Console.WriteLine($"Ad {adReview.Key}: {adReview.Value.ReviewStatus}");
    Console.WriteLine($"Approved: {adReview.Value.IsApproved}");
}
```

### AppealAdGroupRejectionAsync

Appeals the rejection decision for an ad group, requesting re-evaluation when your ad group is rejected during review.

**Endpoint:** `/v1.3/adgroup/appeal/`  
**Method:** POST  
**Version:** v1.3

#### Parameters

- `accessToken` (string, required): Authorized access token
- `body` (AdGroupAppealRequest, required): Appeal request body
- `cancellationToken` (CancellationToken, optional): Cancellation token

#### Example

```csharp
var client = new TikTokBusinessApiClient("your-access-token");

var appealRequest = new AdGroupAppealRequest
{
    AdvertiserId = "your-advertiser-id",
    AdGroupId = "your-adgroup-id",
    AdId = "your-ad-id", // Optional
    AppealReason = "The ad content complies with TikTok policies",
    AttachmentList = new List<string> { "https://example.com/evidence.pdf" }
};

var response = await client.AdReview.AppealAdGroupRejectionAsync(
    accessToken: "your-access-token",
    body: appealRequest
);

Console.WriteLine("Appeal submitted successfully");
```

## Review Status Values

The review status can have the following values:

- `ALL_AVAILABLE`: The ad/ad group has been reviewed and can be delivered
- `PART_AVAILABLE`: The ad/ad group has been reviewed and can only be delivered to some of the specified targeting countries
- `UNAVAILABLE`: The ad/ad group has been reviewed and cannot be delivered

## Appeal Status Values

The appeal status can have the following values:

- `NOT_APPEALED`: No appeal has been submitted
- `APPEALING`: Appeal is in progress
- `APPEAL_APPROVED`: Appeal has been approved
- `APPEAL_REJECTED`: Appeal has been rejected

## Error Handling

All methods may throw the following exceptions:
- `ArgumentException` - When required parameters are null or empty
- `ArgumentNullException` - When required objects are null
- `SdkException` - When the API returns an error response
- `ApiException` - When the HTTP request fails

## API Versions

The AdReviewApi uses TikTok Business API v1.3. Key features:
- String-based IDs (advertiser_id, adgroup_id, ad_id, etc.)
- Enhanced response fields with detailed rejection information
- Support for content-specific rejection details

For more information, refer to the [TikTok Business API documentation](https://business-api.tiktok.com/portal/docs).
