/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request parameters for getting video packages
    /// </summary>
    public class VideoPackageGetRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog video package ID (optional)
        /// </summary>
        public string? ShoppingAdsVideoPackageId { get; set; }
    }

    /// <summary>
    /// Response for getting video packages
    /// </summary>
    public class VideoPackageGetResponse
    {
        /// <summary>
        /// List of video packages
        /// </summary>
        [JsonPropertyName("list")]
        public List<VideoPackage> List { get; set; } = new List<VideoPackage>();
    }

    /// <summary>
    /// Video package information
    /// </summary>
    public class VideoPackage
    {
        /// <summary>
        /// Catalog video package ID
        /// </summary>
        [JsonPropertyName("shopping_ads_video_package_id")]
        public string ShoppingAdsVideoPackageId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Video package name
        /// </summary>
        [JsonPropertyName("video_package_name")]
        public string VideoPackageName { get; set; } = string.Empty;

        /// <summary>
        /// Creation time
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;

        /// <summary>
        /// Update time
        /// </summary>
        [JsonPropertyName("update_time")]
        public string UpdateTime { get; set; } = string.Empty;

        /// <summary>
        /// Status of the video package
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Video package type
        /// </summary>
        [JsonPropertyName("video_package_type")]
        public string VideoPackageType { get; set; } = string.Empty;

        /// <summary>
        /// Audit information
        /// </summary>
        [JsonPropertyName("audit")]
        public VideoPackageAudit Audit { get; set; } = new VideoPackageAudit();
    }

    /// <summary>
    /// Video package audit information
    /// </summary>
    public class VideoPackageAudit
    {
        /// <summary>
        /// Audit status
        /// </summary>
        [JsonPropertyName("audit_status")]
        public string AuditStatus { get; set; } = string.Empty;

        /// <summary>
        /// Rejection information (only when audit_status is REJECTED)
        /// </summary>
        [JsonPropertyName("reject_info")]
        public List<VideoPackageRejectInfo>? RejectInfo { get; set; }
    }

    /// <summary>
    /// Video package rejection information
    /// </summary>
    public class VideoPackageRejectInfo
    {
        /// <summary>
        /// Object that was rejected
        /// </summary>
        [JsonPropertyName("rejected_object")]
        public string RejectedObject { get; set; } = string.Empty;

        /// <summary>
        /// Reason for the rejection
        /// </summary>
        [JsonPropertyName("reason")]
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request body for creating a video package (Deprecated)
    /// </summary>
    public class VideoPackageCreateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Video package type (currently only GREEN_SCREEN is supported)
        /// </summary>
        [JsonPropertyName("video_package_type")]
        public string VideoPackageType { get; set; } = string.Empty;

        /// <summary>
        /// List of videos for the package (required when video_package_type is GREEN_SCREEN)
        /// </summary>
        [JsonPropertyName("videos")]
        public List<VideoPackageVideo>? Videos { get; set; }

        /// <summary>
        /// Music ID (required when video_package_type is GREEN_SCREEN)
        /// </summary>
        [JsonPropertyName("music_id")]
        public string? MusicId { get; set; }

        /// <summary>
        /// Template ID (required when video_package_type is GREEN_SCREEN)
        /// </summary>
        [JsonPropertyName("template_id")]
        public string? TemplateId { get; set; }

        /// <summary>
        /// Video package name (optional)
        /// </summary>
        [JsonPropertyName("video_package_name")]
        public string? VideoPackageName { get; set; }
    }

    /// <summary>
    /// Video information for video package
    /// </summary>
    public class VideoPackageVideo
    {
        /// <summary>
        /// Video ID (required when video_package_type is GREEN_SCREEN)
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Whether this is a green screen video (required when video_package_type is GREEN_SCREEN)
        /// </summary>
        [JsonPropertyName("is_green_screen_video")]
        public bool IsGreenScreenVideo { get; set; }
    }

    /// <summary>
    /// Response for creating a video package (Deprecated)
    /// </summary>
    public class VideoPackageCreateResponse
    {
        /// <summary>
        /// ID of the created video package
        /// </summary>
        [JsonPropertyName("shopping_ads_video_package_id")]
        public string ShoppingAdsVideoPackageId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request body for updating a video package (Deprecated)
    /// </summary>
    public class VideoPackageUpdateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Video package ID to update
        /// </summary>
        [JsonPropertyName("shopping_ads_video_package_id")]
        public string ShoppingAdsVideoPackageId { get; set; } = string.Empty;

        /// <summary>
        /// New video package name
        /// </summary>
        [JsonPropertyName("video_package_name")]
        public string VideoPackageName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request body for deleting a video package (Deprecated)
    /// </summary>
    public class VideoPackageDeleteBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Video package ID to delete
        /// </summary>
        [JsonPropertyName("shopping_ads_video_package_id")]
        public string ShoppingAdsVideoPackageId { get; set; } = string.Empty;
    }
}
