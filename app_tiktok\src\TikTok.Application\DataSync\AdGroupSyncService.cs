﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities.Raws;
using TikTok.Enums;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu nhóm quảng cáo (Ad Groups)
    ///
    /// Lưu ý về xử lý thời gian:
    /// - CreateTime và ModifyTime từ API sẽ được convert sang UTC trước khi lưu vào database
    /// - Timezone lưu trữ múi giờ gốc để có thể convert ngược lại từ UTC khi cần thiết
    /// - CreateTime và ModifyTime từ API là Unix timestamp, được parse và convert sang UTC
    /// </summary>
    public class AdGroupSyncService : BaseSyncService, IAdGroupSyncService
    {
        private readonly IAdGroupRepository _adGroupRepository;
        private readonly IRepository<RawAdGroupEntity, Guid> _adGroupEntityRepository;
        private readonly IAdAccountRepository _adAccountRepository;
        private readonly ICampaignRepository _campaignRepository;

        public AdGroupSyncService(
            IServiceProvider serviceProvider,
            ILogger<AdGroupSyncService> logger,
            IAdGroupRepository adGroupRepository,
            IRepository<RawAdGroupEntity, Guid> adGroupEntityRepository,
            IAdAccountRepository adAccountRepository,
            ICampaignRepository campaignRepository) : base(serviceProvider, logger)
        {
            _adGroupRepository = adGroupRepository;
            _adGroupEntityRepository = adGroupEntityRepository;
            _adAccountRepository = adAccountRepository;
            _campaignRepository = campaignRepository;
        }

        /// <summary>
        /// Đồng bộ nhóm quảng cáo theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AdGroupSyncResult> SyncAdGroupAsync(string bcId)
        {
            return await SyncAdGroupsAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ nhóm quảng cáo với filtering
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserId">ID của Advertiser (optional)</param>
        /// <param name="campaignId">ID của Campaign (optional)</param>
        /// <param name="operationStatus">Trạng thái hoạt động (optional)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AdGroupSyncResult> SyncAdGroupsAsync(string bcId, string? advertiserId = null, string? campaignId = null, string? operationStatus = null)
        {
            var result = new AdGroupSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ nhóm quảng cáo cho BC: {BcId}", bcId);

                // 1. Lấy danh sách AdAccount cho BC này
                var adAccounts = await _adAccountRepository.GetByOwnerBcIdAsync(bcId, false);
                if (!adAccounts.Any())
                {
                    result.ErrorMessage = $"Không tìm thấy tài khoản quảng cáo nào cho BC: {bcId}";
                    _logger.LogWarning(result.ErrorMessage);
                    return result;
                }

                // 2. Tạo TikTok client
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // 3. Đồng bộ nhóm quảng cáo cho từng AdAccount
                foreach (var adAccount in adAccounts)
                {
                    if (!string.IsNullOrEmpty(advertiserId) && adAccount.AdvertiserId != advertiserId)
                        continue;

                    try
                    {
                        var adAccountResult = await SyncAdGroupsForAdvertiserAsync(tikTokClient, adAccount.AdvertiserId, bcId, campaignId, operationStatus);
                        result.TotalSynced += adAccountResult.TotalSynced;
                        result.NewRecords += adAccountResult.NewRecords;
                        result.UpdatedRecords += adAccountResult.UpdatedRecords;
                        result.AdGroupCount += adAccountResult.AdGroupCount;
                        result.AdvertiserCount++;
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ nhóm quảng cáo cho Advertiser: {AdvertiserId}", adAccount.AdvertiserId);
                        result.ErrorRecords++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi không xác định khi đồng bộ nhóm quảng cáo cho Advertiser: {AdvertiserId}", adAccount.AdvertiserId);
                        result.ErrorRecords++;
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ nhóm quảng cáo cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords);
            }
            catch (BusinessException ex)
            {
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ nhóm quảng cáo cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ nhóm quảng cáo: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ nhóm quảng cáo cho BC: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ nhóm quảng cáo cho một Advertiser cụ thể
        /// </summary>
        /// <param name="client">TikTok client</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="campaignId">ID của Campaign (optional)</param>
        /// <param name="operationStatus">Trạng thái hoạt động (optional)</param>
        /// <returns>Kết quả đồng bộ</returns>
        private async Task<AdGroupSyncResult> SyncAdGroupsForAdvertiserAsync(TikTokBusinessApiClient client, string advertiserId, string bcId, string? campaignId = null, string? operationStatus = null)
        {
            var result = new AdGroupSyncResult();

            // 1. Lấy danh sách Campaign cho Advertiser
            var campaigns = await _campaignRepository.GetListAsync(advertiserId: advertiserId);
            if (!campaigns.Any())
            {
                _logger.LogWarning("Không tìm thấy chiến dịch nào cho Advertiser: {AdvertiserId}", advertiserId);
                return result;
            }

            // 2. Lọc theo campaignId nếu có
            if (!string.IsNullOrEmpty(campaignId))
            {
                campaigns = campaigns.Where(x => x.CampaignId == campaignId).ToList();
            }

            // 3. Đồng bộ nhóm quảng cáo cho từng Campaign
            foreach (var campaign in campaigns)
            {
                try
                {
                    var campaignResult = await SyncAdGroupsForCampaignAsync(client, advertiserId, campaign.CampaignId, bcId, operationStatus);
                    result.TotalSynced += campaignResult.TotalSynced;
                    result.NewRecords += campaignResult.NewRecords;
                    result.UpdatedRecords += campaignResult.UpdatedRecords;
                    result.AdGroupCount += campaignResult.AdGroupCount;
                    result.CampaignCount++;
                }
                catch (BusinessException bex)
                {
                    _logger.LogError(bex, "Lỗi khi đồng bộ nhóm quảng cáo cho Campaign: {CampaignId}", campaign.CampaignId);
                    result.ErrorRecords++;
                }
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ nhóm quảng cáo cho một Campaign cụ thể
        /// </summary>
        /// <param name="client">TikTok client</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="campaignId">ID của Campaign</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="operationStatus">Trạng thái hoạt động (optional)</param>
        /// <returns>Kết quả đồng bộ</returns>
        private async Task<AdGroupSyncResult> SyncAdGroupsForCampaignAsync(TikTokBusinessApiClient client, string advertiserId, string campaignId, string bcId, string? operationStatus = null)
        {
            var result = new AdGroupSyncResult();

            try
            {
                // 1. Tạo filtering cho API
                var filtering = new AdGroupFiltering();
                // Note: AdGroupFiltering doesn't support PrimaryStatus filtering

                // 2. Đồng bộ từng trang
                var page = 1;
                var pageSize = 50;
                var hasMorePages = true;

                while (hasMorePages)
                {
                    try
                    {
                        // 3. Gọi API lấy danh sách nhóm quảng cáo
                        var response = await client.AdGroup.GetAdGroupsAsync(
                            advertiserId,
                            new List<string> { campaignId },
                            filtering: filtering,
                            page: page,
                            pageSize: pageSize);

                        if (!TikTokApiCodes.IsSuccess(response.Code))
                        {
                            throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu nhóm quảng cáo cho trang {page}: {response.Message}");
                        }

                        if (response?.Data?.List == null || !response.Data.List.Any())
                        {
                            _logger.LogDebug("Không có dữ liệu nhóm quảng cáo cho trang {Page}", page);
                            break;
                        }

                        _logger.LogDebug("Nhận được {Count} nhóm quảng cáo từ API (trang {Page})", response.Data.List.Count, page);

                        // 4. Đồng bộ dữ liệu nhóm quảng cáo
                        await SyncAdGroupsDataAsync(response.Data.List, advertiserId, campaignId, bcId, result);

                        // 5. Kiểm tra có trang tiếp theo không
                        if (response.Data.List.Count < pageSize)
                        {
                            hasMorePages = false;
                        }
                        else
                        {
                            page++;
                        }
                    }
                    catch (BusinessException)
                    {
                        throw;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ nhóm quảng cáo cho trang {Page}", page);
                        throw;
                    }
                }
            }
            catch (BusinessException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ nhóm quảng cáo cho Campaign: {CampaignId}", campaignId);
                throw;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ dữ liệu nhóm quảng cáo
        /// </summary>
        /// <param name="adGroups">Danh sách nhóm quảng cáo từ API</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="campaignId">ID của Campaign</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task SyncAdGroupsDataAsync(List<AdGroupInfo> adGroups, string advertiserId, string campaignId, string bcId, AdGroupSyncResult result)
        {
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var newEntities = new List<RawAdGroupEntity>();
                var updatedEntities = new List<RawAdGroupEntity>();

                foreach (var apiAdGroup in adGroups)
                {
                    try
                    {
                        // 1. Kiểm tra nhóm quảng cáo đã tồn tại chưa
                        var existingAdGroup = await _adGroupRepository.FindByAdGroupIdAsync(apiAdGroup.AdgroupId);
                        if (existingAdGroup == null)
                        {
                            // 2. Tạo mới nếu chưa tồn tại
                            var newAdGroup = MapApiAdGroupToEntity(apiAdGroup, advertiserId, campaignId, bcId);
                            newEntities.Add(newAdGroup);
                            result.NewRecords++;
                        }
                        else
                        {
                            // 3. Cập nhật nếu đã tồn tại
                            UpdateAdGroupFromApi(existingAdGroup, apiAdGroup);
                            updatedEntities.Add(existingAdGroup);
                            result.UpdatedRecords++;
                        }

                        result.TotalSynced++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý nhóm quảng cáo {AdGroupId}", apiAdGroup.AdgroupId);
                        result.ErrorRecords++;
                    }
                }

                // 4. Lưu dữ liệu
                if (newEntities.Any())
                {
                    await _adGroupEntityRepository.InsertManyAsync(newEntities);
                }

                if (updatedEntities.Any())
                {
                    await _adGroupEntityRepository.UpdateManyAsync(updatedEntities);
                }

                await uow.CompleteAsync();

                _logger.LogDebug("Tạo mới {NewCount} nhóm quảng cáo, cập nhật {UpdatedCount} nhóm quảng cáo",
                    newEntities.Count, updatedEntities.Count);
            }
        }

        /// <summary>
        /// Map dữ liệu từ API sang entity
        /// </summary>
        /// <param name="apiAdGroup">Dữ liệu từ API</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="campaignId">ID của Campaign</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Entity nhóm quảng cáo</returns>
        private RawAdGroupEntity MapApiAdGroupToEntity(AdGroupInfo apiAdGroup, string advertiserId, string campaignId, string bcId)
        {
            // Note: AdGroupInfo doesn't have CreateTime/ModifyTime properties
            var originalCreateTime = DateTime.UtcNow;
            var originalModifyTime = DateTime.UtcNow;

            return new RawAdGroupEntity(Guid.NewGuid())
            {
                AdvertiserId = advertiserId,
                CampaignId = campaignId,
                CampaignName = apiAdGroup.CampaignName ?? string.Empty,
                CampaignSystemOrigin = apiAdGroup.CampaignSystemOrigin,
                IsSmartPerformanceCampaign = apiAdGroup.IsSmartPerformanceCampaign,
                AdgroupId = apiAdGroup.AdgroupId ?? string.Empty,
                AdgroupName = apiAdGroup.AdgroupName ?? string.Empty,
                CreateTime = originalCreateTime,
                ModifyTime = originalModifyTime,
                ShoppingAdsType = apiAdGroup.ShoppingAdsType,
                IdentityId = apiAdGroup.IdentityId,
                IdentityType = apiAdGroup.IdentityType,
                IdentityAuthorizedBcId = apiAdGroup.IdentityAuthorizedBcId,
                ProductSource = apiAdGroup.ProductSource,
                CatalogId = apiAdGroup.CatalogId,
                CatalogAuthorizedBcId = apiAdGroup.CatalogAuthorizedBcId,
                StoreId = apiAdGroup.StoreId,
                StoreAuthorizedBcId = apiAdGroup.StoreAuthorizedBcId,
                PromotionType = apiAdGroup.PromotionType,
                PromotionTargetType = apiAdGroup.PromotionTargetType,
                MessagingAppType = apiAdGroup.MessagingAppType,
                MessagingAppAccountId = apiAdGroup.MessagingAppAccountId,
                PhoneRegionCode = apiAdGroup.PhoneRegionCode,
                PhoneNumber = apiAdGroup.PhoneNumber
                // Note: AdGroupInfo doesn't have PrimaryStatus property
            };
        }

        /// <summary>
        /// Cập nhật entity từ dữ liệu API
        /// </summary>
        /// <param name="entity">Entity hiện tại</param>
        /// <param name="apiAdGroup">Dữ liệu từ API</param>
        private void UpdateAdGroupFromApi(RawAdGroupEntity entity, AdGroupInfo apiAdGroup)
        {
            // Note: AdGroupInfo doesn't have CreateTime/ModifyTime properties
            var originalCreateTime = entity.CreateTime;
            var originalModifyTime = entity.ModifyTime;

            entity.CampaignName = apiAdGroup.CampaignName ?? entity.CampaignName;
            entity.CampaignSystemOrigin = apiAdGroup.CampaignSystemOrigin;
            entity.IsSmartPerformanceCampaign = apiAdGroup.IsSmartPerformanceCampaign;
            entity.AdgroupName = apiAdGroup.AdgroupName ?? entity.AdgroupName;
            entity.CreateTime = originalCreateTime;
            entity.ModifyTime = originalModifyTime;
            entity.ShoppingAdsType = apiAdGroup.ShoppingAdsType;
            entity.IdentityId = apiAdGroup.IdentityId;
            entity.IdentityType = apiAdGroup.IdentityType;
            entity.IdentityAuthorizedBcId = apiAdGroup.IdentityAuthorizedBcId;
            entity.ProductSource = apiAdGroup.ProductSource;
            entity.CatalogId = apiAdGroup.CatalogId;
            entity.CatalogAuthorizedBcId = apiAdGroup.CatalogAuthorizedBcId;
            entity.StoreId = apiAdGroup.StoreId;
            entity.StoreAuthorizedBcId = apiAdGroup.StoreAuthorizedBcId;
            entity.PromotionType = apiAdGroup.PromotionType;
            entity.PromotionTargetType = apiAdGroup.PromotionTargetType;
            // Note: AdGroupInfo doesn't have PrimaryStatus property
        }
    }
}
