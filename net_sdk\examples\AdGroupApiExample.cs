/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the AdGroup API
    /// </summary>
    public class AdGroupApiExample
    {
        /// <summary>
        /// Example of getting ad groups with filtering
        /// </summary>
        /// <param name="accessToken">Your TikTok Business API access token</param>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task GetAdGroupsExample(string accessToken, string advertiserId)
        {
            try
            {
                // Create the TikTok Business API client
                using var client = new TikTokBusinessApiClient(accessToken);

                // Set up filtering conditions (optional)
                var filtering = new AdGroupFiltering
                {
                    Statuses = new List<string> { "ENABLE", "DISABLE" },
                    ObjectiveType = new List<string> { "TRAFFIC", "CONVERSIONS" }
                };

                // Specify which fields to return (optional)
                var fields = new List<string>
                {
                    "adgroup_id",
                    "adgroup_name",
                    "campaign_id",
                    "status",
                    "budget",
                    "bid_price",
                    "optimization_event",
                    "create_time"
                };

                Console.WriteLine("Getting ad groups...");

                var response = await client.AdGroup.GetAdGroupsAsync(
                    accessToken,
                    advertiserId,
                    filtering: filtering,
                    fields: fields,
                    page: 1,
                    pageSize: 20);

                if (response.Data?.List != null)
                {
                    Console.WriteLine($"Found {response.Data.List.Count} ad groups:");
                    foreach (var adGroup in response.Data.List)
                    {
                        Console.WriteLine($"- ID: {adGroup.AdgroupId}, Name: {adGroup.AdgroupName}, Status: {adGroup.Status}");
                        Console.WriteLine($"  Budget: {adGroup.Budget}, Bid Price: {adGroup.BidPrice}");
                        Console.WriteLine($"  Campaign ID: {adGroup.CampaignId}");
                        Console.WriteLine();
                    }

                    // Display pagination info
                    if (response.Data.PageInfo != null)
                    {
                        Console.WriteLine($"Page: {response.Data.PageInfo.Page}/{response.Data.PageInfo.TotalPage}");
                        Console.WriteLine($"Total ad groups: {response.Data.PageInfo.TotalNumber}");
                    }
                }
                else
                {
                    Console.WriteLine("No ad groups found.");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting ad group quota
        /// </summary>
        /// <param name="accessToken">Your TikTok Business API access token</param>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task GetAdGroupQuotaExample(string accessToken, string advertiserId)
        {
            try
            {
                using var client = new TikTokBusinessApiClient(accessToken);

                Console.WriteLine("Getting ad group quota...");

                var response = await client.AdGroup.GetAdGroupQuotaAsync(
                    accessToken,
                    advertiserId);

                if (response.Data != null)
                {
                    Console.WriteLine($"Total ad group quota: {response.Data.TotalAdgroupQuota}");
                    Console.WriteLine($"Used ad group quota: {response.Data.UsedAdgroupQuota}");
                    Console.WriteLine($"Available quota: {response.Data.TotalAdgroupQuota - response.Data.UsedAdgroupQuota}");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of estimating audience size
        /// </summary>
        /// <param name="accessToken">Your TikTok Business API access token</param>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="campaignId">Your campaign ID</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task EstimateAudienceSizeExample(string accessToken, string advertiserId, string campaignId)
        {
            try
            {
                using var client = new TikTokBusinessApiClient(accessToken);

                // Set up targeting for audience estimation
                var targeting = new AudienceTargeting
                {
                    AudienceType = "BROAD",
                    Placements = new List<string> { "PLACEMENT_TIKTOK" },
                    LocationIds = new List<string> { "6252001" }, // United States
                    AgeGroups = new List<string> { "AGE_18_24", "AGE_25_34" },
                    Genders = new List<string> { "GENDER_MALE", "GENDER_FEMALE" }
                };

                var request = new AudienceSizeEstimateRequest
                {
                    AdvertiserId = advertiserId,
                    CampaignId = campaignId,
                    Targeting = targeting
                };

                Console.WriteLine("Estimating audience size...");

                var response = await client.AdGroup.EstimateAudienceSizeAsync(
                    accessToken,
                    request);

                if (response.Data != null)
                {
                    Console.WriteLine($"Estimated audience size: {response.Data.AudienceSize:N0}");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of creating ad groups
        /// </summary>
        /// <param name="accessToken">Your TikTok Business API access token</param>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="campaignId">Your campaign ID</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task CreateAdGroupsExample(string accessToken, string advertiserId, string campaignId)
        {
            try
            {
                using var client = new TikTokBusinessApiClient(accessToken);

                // Create ad group data
                var adGroupData = new AdGroupCreateBody
                {
                    AdgroupName = "My Test Ad Group",
                    CampaignId = campaignId,
                    OptimizationEvent = "CLICK",
                    BidType = "BID_TYPE_NO_BID",
                    Budget = 10000000, // $10 in micros
                    BudgetMode = "BUDGET_MODE_DAY",
                    Placements = new List<string> { "PLACEMENT_TIKTOK" },
                    LocationIds = new List<string> { "6252001" }, // United States
                    AgeGroups = new List<string> { "AGE_18_24", "AGE_25_34" },
                    Genders = new List<string> { "GENDER_MALE", "GENDER_FEMALE" }
                };

                var request = new AdGroupCreateRequest
                {
                    AdvertiserId = advertiserId,
                    Adgroups = new List<AdGroupCreateBody> { adGroupData }
                };

                Console.WriteLine("Creating ad group...");

                var response = await client.AdGroup.CreateAdGroupsAsync(
                    accessToken,
                    request);

                if (response.Data?.List != null)
                {
                    foreach (var result in response.Data.List)
                    {
                        if (result.Errors == null || result.Errors.Count == 0)
                        {
                            Console.WriteLine($"Successfully created ad group:");
                            Console.WriteLine($"- ID: {result.AdgroupId}");
                            Console.WriteLine($"- Name: {result.AdgroupName}");
                        }
                        else
                        {
                            Console.WriteLine($"Failed to create ad group '{result.AdgroupName}':");
                            foreach (var error in result.Errors)
                            {
                                Console.WriteLine($"- Error: {error.Message} (Code: {error.Code})");
                            }
                        }
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of updating ad group status
        /// </summary>
        /// <param name="accessToken">Your TikTok Business API access token</param>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="adGroupIds">List of ad group IDs to update</param>
        /// <param name="operation">Operation to perform (ENABLE, DISABLE, DELETE)</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task UpdateAdGroupStatusExample(string accessToken, string advertiserId, List<string> adGroupIds, string operation)
        {
            try
            {
                using var client = new TikTokBusinessApiClient(accessToken);

                var request = new AdGroupStatusUpdateRequest
                {
                    AdvertiserId = advertiserId,
                    AdgroupIds = adGroupIds,
                    Operation = operation
                };

                Console.WriteLine($"Updating ad group status to {operation}...");

                var response = await client.AdGroup.UpdateAdGroupStatusAsync(
                    accessToken,
                    request);

                if (response.Data?.List != null)
                {
                    foreach (var result in response.Data.List)
                    {
                        if (result.Errors == null || result.Errors.Count == 0)
                        {
                            Console.WriteLine($"Successfully updated ad group {result.AdgroupId}");
                        }
                        else
                        {
                            Console.WriteLine($"Failed to update ad group {result.AdgroupId}:");
                            foreach (var error in result.Errors)
                            {
                                Console.WriteLine($"- Error: {error.Message} (Code: {error.Code})");
                            }
                        }
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of updating ad group budgets
        /// </summary>
        /// <param name="accessToken">Your TikTok Business API access token</param>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="adGroupId">Ad group ID to update</param>
        /// <param name="newBudget">New budget in micros</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task UpdateAdGroupBudgetExample(string accessToken, string advertiserId, string adGroupId, long newBudget)
        {
            try
            {
                using var client = new TikTokBusinessApiClient(accessToken);

                var budgetUpdate = new AdGroupBudgetUpdateBody
                {
                    AdgroupId = adGroupId,
                    Budget = newBudget,
                    BudgetMode = "BUDGET_MODE_DAY"
                };

                var request = new AdGroupBudgetUpdateRequest
                {
                    AdvertiserId = advertiserId,
                    Adgroups = new List<AdGroupBudgetUpdateBody> { budgetUpdate }
                };

                Console.WriteLine($"Updating ad group budget to ${newBudget / 1000000.0:F2}...");

                var response = await client.AdGroup.UpdateAdGroupBudgetAsync(
                    accessToken,
                    request);

                if (response.Data?.List != null)
                {
                    foreach (var result in response.Data.List)
                    {
                        if (result.Errors == null || result.Errors.Count == 0)
                        {
                            Console.WriteLine($"Successfully updated budget for ad group {result.AdgroupId}");
                        }
                        else
                        {
                            Console.WriteLine($"Failed to update budget for ad group {result.AdgroupId}:");
                            foreach (var error in result.Errors)
                            {
                                Console.WriteLine($"- Error: {error.Message} (Code: {error.Code})");
                            }
                        }
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }
    }
}
