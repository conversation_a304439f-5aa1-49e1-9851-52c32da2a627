/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Catalogs endpoints
    /// </summary>
    public static class CatalogsEndpoints
    {
        /// <summary>
        /// Create a catalog endpoint (v1.3)
        /// </summary>
        public const string CreateCatalog = "/catalog/create/";

        /// <summary>
        /// Update the name of a catalog endpoint (v1.3)
        /// </summary>
        public const string UpdateCatalog = "/catalog/update/";

        /// <summary>
        /// Delete a catalog endpoint (v1.3)
        /// </summary>
        public const string DeleteCatalog = "/catalog/delete/";

        /// <summary>
        /// Get catalogs endpoint (v1.3)
        /// </summary>
        public const string GetCatalogs = "/catalog/get/";

        /// <summary>
        /// Get the lexicon list for a catalog endpoint (v1.3)
        /// </summary>
        public const string GetCatalogLexicon = "/catalog/lexicon/get/";

        /// <summary>
        /// Migrate a catalog to a Business Center endpoint (v1.3)
        /// </summary>
        public const string MigrateCatalogToBC = "/catalog/capitalize/";

        /// <summary>
        /// Get available regions endpoint (v1.3)
        /// </summary>
        public const string GetAvailableRegions = "/catalog/available_country/get/";

        /// <summary>
        /// Get locations and currencies endpoint (v1.3)
        /// </summary>
        public const string GetLocationsCurrencies = "/catalog/location_currency/get/";

        /// <summary>
        /// Get the overview of a catalog endpoint (v1.3)
        /// </summary>
        public const string GetCatalogOverview = "/catalog/overview/";
    }
}
