/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API User operations
    /// </summary>
    public class UserApi : IUserApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<UserApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the UserApi class
        /// </summary>
        /// <param name="apiClient">API client instance</param>
        /// <param name="logger">Logger instance</param>
        public UserApi(IApiClient apiClient, ILogger<UserApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <inheritdoc />
        public async Task<UserInfoResponse> GetUserInfoAsync(CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting user information");

            var response = await _apiClient.CallApiAsync<UserInfoResponse>(
                UserEndpoints.GetUserInfo,
                HttpMethod.Get,
                queryParams: null,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }
    }
}
