using System;
using System.Collections.Generic;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.Customers
{
    /// <summary>
    /// DTO cho việc tìm kiếm tài khoản quảng cáo
    /// </summary>
    public class SearchAdAccountDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Từ khóa tìm kiếm chung
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// ID Business Center
        /// </summary>
        public string? OwnerBcId { get; set; }

        /// <summary>
        /// Tên tài khoản quảng cáo
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// Tên công ty
        /// </summary>
        public string? Company { get; set; }

        /// <summary>
        /// Trạng thái tài khoản
        /// </summary>
        public AdAccountStatus? Status { get; set; }

        /// <summary>
        /// Vai trò tài khoản
        /// </summary>
        public AdAccountRole? Role { get; set; }

        /// <summary>
        /// Ngành nghề
        /// </summary>
        public string? Industry { get; set; }

        /// <summary>
        /// Quốc gia
        /// </summary>
        public string? Country { get; set; }

        /// <summary>
        /// Loại tài khoản
        /// </summary>
        public AdAccountType? AdvertiserAccountType { get; set; }

        /// <summary>
        /// Tiền tệ
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Ngôn ngữ
        /// </summary>
        public string? Language { get; set; }

        /// <summary>
        /// Số giấy phép
        /// </summary>
        public string? LicenseNo { get; set; }

        /// <summary>
        /// Thời gian tạo từ
        /// </summary>
        public DateTime? CreateTimeFrom { get; set; }

        /// <summary>
        /// Thời gian tạo đến
        /// </summary>
        public DateTime? CreateTimeTo { get; set; }

        /// <summary>
        /// Số dư từ
        /// </summary>
        public decimal? BalanceFrom { get; set; }

        /// <summary>
        /// Số dư đến
        /// </summary>
        public decimal? BalanceTo { get; set; }

        /// <summary>
        /// Bao gồm tài khoản đã bị xóa
        /// </summary>
        public bool IncludeRemoved { get; set; } = false;
    }

    /// <summary>
    /// DTO cho kết quả tìm kiếm tài khoản quảng cáo
    /// </summary>
    public class AdAccountSearchResultDto
    {
        /// <summary>
        /// ID của tài khoản quảng cáo
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID Business Center
        /// </summary>
        public string OwnerBcId { get; set; }

        /// <summary>
        /// Tên tài khoản quảng cáo
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Tên công ty
        /// </summary>
        public string? Company { get; set; }

        /// <summary>
        /// Trạng thái tài khoản
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Vai trò tài khoản
        /// </summary>
        public string Role { get; set; }

        /// <summary>
        /// Quốc gia
        /// </summary>
        public string Country { get; set; }

        /// <summary>
        /// Loại tài khoản
        /// </summary>
        public string AdvertiserAccountType { get; set; }

        /// <summary>
        /// Tiền tệ
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Số dư
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// Thời gian tạo
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// Cờ đánh dấu đã bị xóa
        /// </summary>
        public bool IsRemoved { get; set; }
    }

    /// <summary>
    /// DTO cho kết quả tìm kiếm tài khoản quảng cáo với phân trang
    /// </summary>
    public class AdAccountSearchResultListDto : PagedResultDto<AdAccountSearchResultDto>
    {
        public AdAccountSearchResultListDto()
        {
        }

        public AdAccountSearchResultListDto(long totalCount, List<AdAccountSearchResultDto> items) : base(totalCount, items)
        {
        }
    }
}
