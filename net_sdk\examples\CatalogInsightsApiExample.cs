/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the Catalog Insights API
    /// </summary>
    public class CatalogInsightsApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public CatalogInsightsApiExample(string appId, string accessToken)
        {
            var configuration = Configuration.CreateWithCredentials(appId, accessToken);
            _client = new TikTokBusinessApiClient(configuration);
        }

        /// <summary>
        /// Example: Get available filters for catalog insights
        /// </summary>
        public async Task GetFiltersExample(string bcId, string catalogId)
        {
            try
            {
                Console.WriteLine("=== Getting Catalog Insight Filters ===");

                // Get brand filters
                Console.WriteLine("Getting brand filters...");
                var brandFilters = await _client.CatalogInsights.GetFiltersAsync(
                    bcId: bcId,
                    catalogId: catalogId,
                    filterType: "BRAND",
                    page: 1,
                    pageSize: 50);

                Console.WriteLine($"Available brands: {brandFilters.Brands?.Count ?? 0}");
                if (brandFilters.Brands != null)
                {
                    foreach (var brand in brandFilters.Brands)
                    {
                        Console.WriteLine($"  - {brand}");
                    }
                }

                // Get availability filters
                Console.WriteLine("\nGetting availability filters...");
                var availabilityFilters = await _client.CatalogInsights.GetFiltersAsync(
                    bcId: bcId,
                    catalogId: catalogId,
                    filterType: "AVAILABILITY",
                    page: 1,
                    pageSize: 50);

                Console.WriteLine($"Available statuses: {availabilityFilters.Availabilities?.Count ?? 0}");
                if (availabilityFilters.Availabilities != null)
                {
                    foreach (var availability in availabilityFilters.Availabilities)
                    {
                        Console.WriteLine($"  - {availability}");
                    }
                }

                // Get category filters
                Console.WriteLine("\nGetting category filters...");
                var categoryFilters = await _client.CatalogInsights.GetFiltersAsync(
                    bcId: bcId,
                    catalogId: catalogId,
                    filterType: "CATEGORY_ID",
                    page: 1,
                    pageSize: 50);

                Console.WriteLine($"Available categories: {categoryFilters.Categories?.Count ?? 0}");
                if (categoryFilters.Categories != null)
                {
                    foreach (var category in categoryFilters.Categories)
                    {
                        Console.WriteLine($"  - {category.CategoryId}: {category.LevelInfo?.LevelName1} > {category.LevelInfo?.LevelName2} > {category.LevelInfo?.LevelName3}");
                    }
                }
            }
            catch (TikTokBusinessApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get trending products
        /// </summary>
        public async Task GetTrendingProductsExample(string bcId, string catalogId)
        {
            try
            {
                Console.WriteLine("\n=== Getting Trending Products ===");

                // Get all trending products without filtering
                Console.WriteLine("Getting all trending products...");
                var allProducts = await _client.CatalogInsights.GetTrendingProductsAsync(
                    bcId: bcId,
                    catalogId: catalogId);

                Console.WriteLine($"Total trending products: {allProducts.ProductInsights?.Count ?? 0}");

                // Get trending products with filtering
                Console.WriteLine("\nGetting trending products with filters...");
                var filtering = new CatalogInsightProductFiltering
                {
                    Availabilities = new List<string> { "IN_STOCK", "AVAILABLE_FOR_ORDER" },
                    Brands = new List<string> { "Nike", "Adidas" } // Example brands
                };

                var filteredProducts = await _client.CatalogInsights.GetTrendingProductsAsync(
                    bcId: bcId,
                    catalogId: catalogId,
                    filtering: filtering);

                Console.WriteLine($"Filtered trending products: {filteredProducts.ProductInsights?.Count ?? 0}");

                // Display product details
                if (filteredProducts.ProductInsights != null)
                {
                    for (int i = 0; i < Math.Min(5, filteredProducts.ProductInsights.Count); i++)
                    {
                        var product = filteredProducts.ProductInsights[i];
                        Console.WriteLine($"\nProduct #{i + 1}:");
                        Console.WriteLine($"  ID: {product.ProductId}");
                        Console.WriteLine($"  Title: {product.Title}");
                        Console.WriteLine($"  Brand: {product.Brand}");
                        Console.WriteLine($"  Price: {product.Price?.Price} {product.Price?.Currency}");
                        Console.WriteLine($"  Availability: {product.Availability}");
                        Console.WriteLine($"  Category: {product.CategoryInfo?.LevelInfo?.LevelName1} > {product.CategoryInfo?.LevelInfo?.LevelName2} > {product.CategoryInfo?.LevelInfo?.LevelName3}");
                    }
                }
            }
            catch (TikTokBusinessApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get trending categories
        /// </summary>
        public async Task GetTrendingCategoriesExample(string bcId, string catalogId)
        {
            try
            {
                Console.WriteLine("\n=== Getting Trending Categories ===");

                // Get all trending categories
                Console.WriteLine("Getting all trending categories...");
                var allCategories = await _client.CatalogInsights.GetTrendingCategoriesAsync(
                    bcId: bcId,
                    catalogId: catalogId);

                Console.WriteLine($"Total trending categories: {allCategories.CategoryInsights?.Count ?? 0}");

                // Display category details
                if (allCategories.CategoryInsights != null)
                {
                    for (int i = 0; i < Math.Min(10, allCategories.CategoryInsights.Count); i++)
                    {
                        var category = allCategories.CategoryInsights[i];
                        Console.WriteLine($"\nCategory #{i + 1}:");
                        Console.WriteLine($"  ID: {category.CategoryId}");
                        Console.WriteLine($"  Name: {category.LevelInfo?.LevelName1} > {category.LevelInfo?.LevelName2} > {category.LevelInfo?.LevelName3}");
                        Console.WriteLine($"  Product Count: {category.TotalProductCount}");
                        Console.WriteLine($"  Availability Rate: {category.ProductAvailabilityRate:P2}");
                    }
                }

                // Get trending categories with filtering
                if (allCategories.CategoryInsights?.Count > 0)
                {
                    Console.WriteLine("\nGetting specific categories with filtering...");
                    var categoryIds = new List<string>();
                    for (int i = 0; i < Math.Min(3, allCategories.CategoryInsights.Count); i++)
                    {
                        if (!string.IsNullOrEmpty(allCategories.CategoryInsights[i].CategoryId))
                        {
                            categoryIds.Add(allCategories.CategoryInsights[i].CategoryId!);
                        }
                    }

                    if (categoryIds.Count > 0)
                    {
                        var filtering = new CatalogInsightCategoryFiltering
                        {
                            CategoryIds = categoryIds
                        };

                        var filteredCategories = await _client.CatalogInsights.GetTrendingCategoriesAsync(
                            bcId: bcId,
                            catalogId: catalogId,
                            filtering: filtering);

                        Console.WriteLine($"Filtered categories: {filteredCategories.CategoryInsights?.Count ?? 0}");
                    }
                }
            }
            catch (TikTokBusinessApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Comprehensive example showing all Catalog Insights API features
        /// </summary>
        public async Task RunAllExamplesAsync(string bcId, string catalogId)
        {
            Console.WriteLine("Starting Catalog Insights API Examples...");
            Console.WriteLine($"Business Center ID: {bcId}");
            Console.WriteLine($"Catalog ID: {catalogId}");

            await GetFiltersExample(bcId, catalogId);
            await GetTrendingProductsExample(bcId, catalogId);
            await GetTrendingCategoriesExample(bcId, catalogId);

            Console.WriteLine("\nCatalog Insights API Examples completed!");
        }
    }

    /// <summary>
    /// Program entry point for running examples
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Replace with your actual app ID, access token, business center ID, and catalog ID
            var appId = "your-app-id";
            var accessToken = "your-access-token";
            var bcId = "your-business-center-id";
            var catalogId = "your-catalog-id";

            var example = new CatalogInsightsApiExample(appId, accessToken);

            try
            {
                await example.RunAllExamplesAsync(bcId, catalogId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Example failed: {ex.Message}");
            }
        }
    }
}
