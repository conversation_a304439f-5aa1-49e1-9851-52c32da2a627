# AdGroupApi Documentation

The `AdGroup<PERSON><PERSON>` provides methods to manage TikTok Business API ad groups, including creation, updating, status management, and audience estimation.

## Overview

The AdGroupApi allows you to:
- Get ad groups with filtering and pagination
- Retrieve ad group quota information
- Estimate audience size for targeting configurations
- Create new ad groups
- Update existing ad groups
- Manage ad group status (enable, disable, delete)
- Update ad group budgets

## Available Methods

### GetAdGroupsAsync

Retrieves ad groups under an advertiser account with optional filtering.

**Endpoint:** `/v1.3/adgroup/get/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

- `accessToken` (string, required): Authorized access token
- `advertiserId` (string, required): Advertiser ID
- `filtering` (AdGroupFiltering, optional): Filtering conditions
- `fields` (List<string>, optional): Fields to return in response
- `page` (int, optional): Current page number (default: 1)
- `pageSize` (int, optional): Page size (default: 10, range: 1-1000)

#### Example

```csharp
var filtering = new AdGroupFiltering
{
    Statuses = new List<string> { "ENABLE", "DISABLE" },
    CampaignIds = new List<string> { "campaign_id_1", "campaign_id_2" }
};

var response = await client.AdGroup.GetAdGroupsAsync(
    accessToken,
    advertiserId,
    filtering: filtering,
    page: 1,
    pageSize: 20);
```

### GetAdGroupQuotaAsync

Gets the dynamic quota on active ad groups under an advertiser account.

**Endpoint:** `/v1.3/adgroup/quota/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

- `accessToken` (string, required): Authorized access token
- `advertiserId` (string, required): Advertiser ID

#### Example

```csharp
var response = await client.AdGroup.GetAdGroupQuotaAsync(
    accessToken,
    advertiserId);

Console.WriteLine($"Total quota: {response.Data.TotalAdgroupQuota}");
Console.WriteLine($"Used quota: {response.Data.UsedAdgroupQuota}");
```

### EstimateAudienceSizeAsync

Estimates audience size for a specific targeting configuration.

**Endpoint:** `/v1.3/ad/audience_size/estimate/`  
**Method:** POST  
**Version:** v1.3

#### Parameters

- `accessToken` (string, required): Authorized access token
- `body` (AudienceSizeEstimateRequest, required): Targeting configuration

#### Example

```csharp
var targeting = new AudienceTargeting
{
    Placements = new List<string> { "PLACEMENT_TIKTOK" },
    LocationIds = new List<string> { "6252001" }, // United States
    AgeGroups = new List<string> { "AGE_18_24", "AGE_25_34" }
};

var request = new AudienceSizeEstimateRequest
{
    AdvertiserId = advertiserId,
    CampaignId = campaignId,
    Targeting = targeting
};

var response = await client.AdGroup.EstimateAudienceSizeAsync(
    accessToken,
    request);
```

### CreateAdGroupsAsync

Creates new ad groups under an advertiser account.

**Endpoint:** `/v1.3/adgroup/create/`  
**Method:** POST  
**Version:** v1.3

#### Parameters

- `accessToken` (string, required): Authorized access token
- `body` (AdGroupCreateRequest, required): Ad group creation data

#### Example

```csharp
var adGroupData = new AdGroupCreateBody
{
    AdgroupName = "My Test Ad Group",
    CampaignId = campaignId,
    OptimizationEvent = "CLICK",
    BidType = "BID_TYPE_NO_BID",
    Budget = ********, // $10 in micros
    BudgetMode = "BUDGET_MODE_DAY",
    Placements = new List<string> { "PLACEMENT_TIKTOK" }
};

var request = new AdGroupCreateRequest
{
    AdvertiserId = advertiserId,
    Adgroups = new List<AdGroupCreateBody> { adGroupData }
};

var response = await client.AdGroup.CreateAdGroupsAsync(
    accessToken,
    request);
```

### UpdateAdGroupsAsync

Updates existing ad groups.

**Endpoint:** `/v1.3/adgroup/update/`  
**Method:** POST  
**Version:** v1.3

#### Parameters

- `accessToken` (string, required): Authorized access token
- `body` (AdGroupUpdateRequest, required): Ad group update data

### UpdateAdGroupStatusAsync

Updates the status of ad groups (enable, disable, or delete).

**Endpoint:** `/v1.3/adgroup/status/update/`  
**Method:** POST  
**Version:** v1.3

#### Parameters

- `accessToken` (string, required): Authorized access token
- `body` (AdGroupStatusUpdateRequest, required): Status update data

#### Example

```csharp
var request = new AdGroupStatusUpdateRequest
{
    AdvertiserId = advertiserId,
    AdgroupIds = new List<string> { "adgroup_id_1", "adgroup_id_2" },
    Operation = "ENABLE" // or "DISABLE", "DELETE"
};

var response = await client.AdGroup.UpdateAdGroupStatusAsync(
    accessToken,
    request);
```

### UpdateAdGroupBudgetAsync

Updates budgets for ad groups.

**Endpoint:** `/v1.3/adgroup/budget/update/`  
**Method:** POST  
**Version:** v1.3

#### Parameters

- `accessToken` (string, required): Authorized access token
- `body` (AdGroupBudgetUpdateRequest, required): Budget update data

#### Example

```csharp
var budgetUpdate = new AdGroupBudgetUpdateBody
{
    AdgroupId = adGroupId,
    Budget = 20000000, // $20 in micros
    BudgetMode = "BUDGET_MODE_DAY"
};

var request = new AdGroupBudgetUpdateRequest
{
    AdvertiserId = advertiserId,
    Adgroups = new List<AdGroupBudgetUpdateBody> { budgetUpdate }
};

var response = await client.AdGroup.UpdateAdGroupBudgetAsync(
    accessToken,
    request);
```

## Models

### Key Models

- **AdGroupInfo**: Complete ad group information
- **AdGroupFiltering**: Filtering conditions for ad group queries
- **AdGroupCreateBody**: Data for creating ad groups
- **AdGroupUpdateBody**: Data for updating ad groups
- **AudienceTargeting**: Targeting configuration for audience estimation
- **AdGroupQuotaResponse**: Quota information response

### Response Models

- **AdGroupGetResponse**: Response for get ad groups requests
- **AdGroupCreateResponse**: Response for create ad groups requests
- **AdGroupUpdateResponse**: Response for update ad groups requests
- **AdGroupStatusUpdateResponse**: Response for status update requests
- **AdGroupBudgetUpdateResponse**: Response for budget update requests
- **AudienceSizeEstimateResponse**: Response for audience size estimation

## Error Handling

All methods throw `SdkException` when the API returns an error response. The exception includes:
- `RequestId`: Unique request identifier for tracking
- `ErrorCode`: TikTok API error code
- `Message`: Error description

## Complete Example

See `AdGroupApiExample.cs` for comprehensive usage examples of all AdGroup API methods.

## Notes

- All budget values are in micros (1 USD = 1,000,000 micros)
- Ad group names must be unique within a campaign
- Some targeting options may not be available in all regions
- Check quota before creating new ad groups to avoid limits
