/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for publishing a photo post to an owned account
    /// </summary>
    public class PhotoPublishRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// A list of up to 35 publicly accessible HTTP(s) URLs for the photo content to be published
        /// </summary>
        [JsonPropertyName("photo_images")]
        public List<string> PhotoImages { get; set; } = new List<string>();

        /// <summary>
        /// The index of the photo to be used as the cover for the post
        /// </summary>
        [JsonPropertyName("photo_cover_index")]
        public int? PhotoCoverIndex { get; set; }

        /// <summary>
        /// Information about the photo post
        /// </summary>
        [JsonPropertyName("post_info")]
        public PhotoPostInfo PostInfo { get; set; } = new PhotoPostInfo();
    }
}
