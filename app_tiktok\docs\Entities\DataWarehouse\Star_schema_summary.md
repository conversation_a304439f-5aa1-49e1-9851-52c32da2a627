# Tổng hợp Thiết kế Star Schema - Phiên bản Đơn giản hóa

## 🎯 Nguyên tắc thiết kế

- **Dim là tiêu chí để định lượng Fact**
- **Chỉ dùng dữ liệu thực tế có trong Raw entities**
- **Type field BẮT BUỘC cho ETL đa nguồn**

## Sơ đồ Star Schema

```
                    Dim_Date ────────────────────────── Dim_Transaction
                        │                                       │
                        │                                       │
    Dim_BusinessCenter──┼──────── Fact_ChiTieu                  │
             │          │              │                       │
             │          │              │                       │
             │          └─────── Fact_SoDu                     │
             │                         │                       │
             └─── Dim_AdAccount ───────┼──────── Fact_GiaoDich─┘
                      │                │              │
                      │                │              │
                      └─── Dim_Campaign─┼─── Fact_ChienDich
                               │        │              │
                               │        │              │
                               └─ Dim_AdGroup ─────────┤
                                       │               │
                                       │               │
                                    Dim_Ad ──── Fact_QuangCao
```

## Tổng quan 5 Fact Tables

| Fact Table         | Grain              | Type Field                                        | Source Tables                                                                                                                           | ETL Rule                |
| ------------------ | ------------------ | ------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------- | ----------------------- |
| **Fact_ChiTieu**   | 1 Ad Account/Ngày  | ❌ No                                             | RawCostProfileEntity                                                                                                                    | 1 nguồn, không cần Type |
| **Fact_SoDu**      | 1 Entity/Thời điểm | ✅ "AdAccount", "BusinessCenter"                  | RawBalanceAdAccountEntity<br/>RawBalanceBusinessCenterEntity                                                                            | **Type BẮT BUỘC**       |
| **Fact_QuangCao**  | 1 Entity/Ngày      | ✅ "BC", "AdAccount", "Campaign", "AdGroup", "Ad" | RawReportIntegratedBcEntity<br/>RawReportIntegratedAdAccountEntity<br/>RawReportIntegratedAdGroupEntity<br/>RawReportIntegratedAdEntity | **Type BẮT BUỘC**       |
| **Fact_GiaoDich**  | 1 Giao dịch        | ✅ "BC", "AdAccount", "General"                   | RawRecordTransactionBcEntity<br/>RawRecordTransactionAdAccountEntity<br/>RawTransactionEntity                                           | **Type BẮT BUỘC**       |
| **Fact_ChienDich** | 1 Campaign/Ngày    | ✅ "Performance", "Configuration"                 | RawReportIntegratedCampaignEntity<br/>RawCampaignEntity                                                                                 | **Type BẮT BUỘC**       |

## Tổng quan 7 Dimension Tables (đã bỏ Dim_Asset)

| Dimension              | Grain                | SCD Type | Key Fields Only                                           | Source                           |
| ---------------------- | -------------------- | -------- | --------------------------------------------------------- | -------------------------------- |
| **Dim_Date**           | 1 Ngày               | Static   | DateKey, Date, Year, Quarter, Month, YearMonth, IsWeekend | Pre-generated                    |
| **Dim_AdAccount**      | 1 Ad Account/Version | Type 2   | AdvertiserId, Name, Status, Country, Currency             | RawAdAccountEntity               |
| **Dim_BusinessCenter** | 1 BC/Version         | Type 2   | BcId, Name, Company, RegisteredArea, Status               | RawBusinessCenterEntity          |
| **Dim_Campaign**       | 1 Campaign/Version   | Type 2   | CampaignId, Name, ObjectiveType, BudgetMode               | RawCampaignEntity                |
| **Dim_AdGroup**        | 1 AdGroup/Version    | Type 2   | AdGroupId, Name, PlacementType, BidStrategy               | RawReportIntegratedAdGroupEntity |
| **Dim_Ad**             | 1 Ad/Version         | Type 2   | AdId, Name, AdText, CallToAction, IsAco                   | RawReportIntegratedAdEntity      |
| **Dim_Transaction**    | 1 Transaction Type   | Static   | TransactionTypeCode, FundsTypeCode, IsDebit               | Pre-defined                      |

## Chi tiết cấu trúc Fact Tables (chỉ trường cần thiết)

### Fact_ChiTieu (Chi tiêu)

| Trường            | Kiểu          | Mô tả                   |
| ----------------- | ------------- | ----------------------- |
| DateKey           | int           | FK → Dim_Date           |
| AdAccountKey      | int           | FK → Dim_AdAccount      |
| BusinessCenterKey | int           | FK → Dim_BusinessCenter |
| AdvertiserId      | string(50)    | Business Key            |
| TotalAmount       | decimal(18,2) | Tổng chi phí            |
| CashAmount        | decimal(18,2) | Chi phí tiền mặt        |
| GrantAmount       | decimal(18,2) | Chi phí tín dụng        |
| TaxAmount         | decimal(18,2) | Chi phí thuế            |
| Currency          | string(10)    | Tiền tệ                 |

### Fact_SoDu (Số dư)

| Trường              | Kiểu           | Mô tả                             |
| ------------------- | -------------- | --------------------------------- |
| DateKey             | int            | FK → Dim_Date                     |
| BusinessCenterKey   | int            | FK → Dim_BusinessCenter           |
| AdAccountKey        | int            | FK → Dim_AdAccount (nullable)     |
| **Type**            | **string(20)** | **"AdAccount", "BusinessCenter"** |
| EntityId            | string(100)    | BcId hoặc AdvertiserId            |
| AccountBalance      | decimal(18,2)  | Tổng số dư                        |
| ValidAccountBalance | decimal(18,2)  | Số dư khả dụng                    |
| Budget              | decimal(18,2)  | Ngân sách (chỉ AdAccount)         |
| Currency            | string(10)     | Tiền tệ                           |

### Fact_QuangCao (Quảng cáo)

| Trường                   | Kiểu           | Mô tả                                              |
| ------------------------ | -------------- | -------------------------------------------------- |
| DateKey                  | int            | FK → Dim_Date                                      |
| BusinessCenterKey        | int            | FK → Dim_BusinessCenter                            |
| AdAccountKey             | int            | FK → Dim_AdAccount                                 |
| CampaignKey              | int            | FK → Dim_Campaign (nullable)                       |
| AdGroupKey               | int            | FK → Dim_AdGroup (nullable)                        |
| AdKey                    | int            | FK → Dim_Ad (nullable)                             |
| **Type**                 | **string(20)** | **"BC", "AdAccount", "Campaign", "AdGroup", "Ad"** |
| EntityId                 | string(100)    | ID theo từng cấp độ                                |
| Spend                    | decimal(18,2)  | Chi tiêu                                           |
| Impressions              | long           | Lượt hiển thị                                      |
| Clicks                   | long           | Lượt nhấp                                          |
| Conversion               | long           | Lượt chuyển đổi                                    |
| TotalOnsiteShoppingValue | decimal(18,2)  | Doanh thu TikTok Shop                              |
| Currency                 | string(10)     | Tiền tệ                                            |

### Fact_GiaoDich (Giao dịch)

| Trường            | Kiểu           | Mô tả                            |
| ----------------- | -------------- | -------------------------------- |
| DateKey           | int            | FK → Dim_Date                    |
| BusinessCenterKey | int            | FK → Dim_BusinessCenter          |
| AdAccountKey      | int            | FK → Dim_AdAccount (nullable)    |
| TransactionKey    | int            | FK → Dim_Transaction             |
| **Type**          | **string(20)** | **"BC", "AdAccount", "General"** |
| EntityId          | string(100)    | BcId hoặc AdvertiserId           |
| Amount            | decimal(18,2)  | Số tiền giao dịch                |
| FundsType         | string(50)     | Loại quỹ                         |
| Currency          | string(10)     | Tiền tệ                          |

### Fact_ChienDich (Chiến dịch)

| Trường            | Kiểu          | Mô tả                       |
| ----------------- | ------------- | --------------------------- |
| DateKey           | int           | FK → Dim_Date               |
| CampaignKey       | int           | FK → Dim_Campaign           |
| AdAccountKey      | int           | FK → Dim_AdAccount          |
| BusinessCenterKey | int           | FK → Dim_BusinessCenter     |
| CampaignId        | string(100)   | Business Key                |
| Spend             | decimal(18,2) | Chi tiêu (Performance)      |
| Impressions       | long          | Lượt hiển thị (Performance) |
| Budget            | decimal(18,2) | Ngân sách (Configuration)   |
| OperationStatus   | string(20)    | Trạng thái                  |

## Key Business Questions được hỗ trợ

### 1. Dữ liệu chi tiêu hàng ngày ✅

```sql
SELECT d.Date, a.AdvertiserName, a.Country, a.Currency,
       fc.TotalAmount as DailySpend,
       fc.TotalAmount - fc.GrantAmount as NonCreditSpending
FROM Fact_ChiTieu fc
JOIN Dim_Date d ON fc.DateKey = d.DateKey
JOIN Dim_AdAccount a ON fc.AdAccountKey = a.AdAccountKey
WHERE d.Date >= '2025-07-01' AND a.IsCurrent = 1
```

### 2. Chi tiết hiệu suất GMV Max ✅

```sql
-- Chi phí, doanh thu theo từng VIDEO/LIVE/Product level
SELECT fq.Type, fq.EntityName,
       SUM(fq.Spend) as TotalCost,
       SUM(fq.TotalOnsiteShoppingValue) as TotalRevenue,
       SUM(fq.TotalOnsiteShoppingValue)/SUM(fq.Spend) as ROAS
FROM Fact_QuangCao fq
WHERE fq.Type IN ('Ad', 'AdGroup', 'Campaign')  -- Drill down levels
GROUP BY fq.Type, fq.EntityName
ORDER BY ROAS DESC
```

### 3. Dashboard thống kê tổng hợp ✅

```sql
-- Thống kê chi tiêu theo ngày/tuần/tháng/quý
SELECT d.YearMonth, bc.BcName,
       SUM(fc.TotalAmount) as TotalSpend,
       COUNT(DISTINCT fc.AdAccountKey) as ActiveAccounts
FROM Fact_ChiTieu fc
JOIN Dim_Date d ON fc.DateKey = d.DateKey
JOIN Dim_BusinessCenter bc ON fc.BusinessCenterKey = bc.BusinessCenterKey
WHERE bc.IsCurrent = 1
GROUP BY d.YearMonth, bc.BcName
```

### 4. Cảnh báo số dư sắp hết ✅

```sql
-- Tài khoản sắp hết tiền cần nạp thêm
SELECT a.AdvertiserName, fs.ValidAccountBalance,
       bc.BcName, a.Currency
FROM Fact_SoDu fs
JOIN Dim_AdAccount a ON fs.AdAccountKey = a.AdAccountKey
JOIN Dim_BusinessCenter bc ON fs.BusinessCenterKey = bc.BusinessCenterKey
WHERE fs.Type = 'AdAccount'
AND fs.ValidAccountBalance < 1000000  -- Threshold
AND a.IsCurrent = 1 AND bc.IsCurrent = 1
AND fs.DateKey = (SELECT MAX(DateKey) FROM Fact_SoDu)  -- Latest date
```

## ETL Rules cho Type Field

### Fact_GiaoDich ETL

```sql
-- Type = "BC": từ RawRecordTransactionBcEntity
INSERT INTO Fact_GiaoDich (Type, EntityId, Amount, ...)
SELECT 'BC', [lookup BcId], Amount, ... FROM RawRecordTransactionBcEntity

-- Type = "AdAccount": từ RawRecordTransactionAdAccountEntity
INSERT INTO Fact_GiaoDich (Type, EntityId, Amount, ...)
SELECT 'AdAccount', AdvertiserId, Amount, ... FROM RawRecordTransactionAdAccountEntity

-- Type = "General": từ RawTransactionEntity
INSERT INTO Fact_GiaoDich (Type, EntityId, Amount, ...)
SELECT 'General', BcId, Amount, ... FROM RawTransactionEntity
```

### Fact_QuangCao ETL

```sql
-- Type = "BC": từ RawReportIntegratedBcEntity
INSERT INTO Fact_QuangCao (Type, EntityId, Spend, ...)
SELECT 'BC', BcId, Spend, ... FROM RawReportIntegratedBcEntity

-- Type = "AdAccount": từ RawReportIntegratedAdAccountEntity
INSERT INTO Fact_QuangCao (Type, EntityId, Spend, ...)
SELECT 'AdAccount', AdvertiserId, Spend, ... FROM RawReportIntegratedAdAccountEntity

-- Type = "Campaign": từ RawReportIntegratedCampaignEntity
INSERT INTO Fact_QuangCao (Type, EntityId, Spend, ...)
SELECT 'Campaign', CampaignId, Spend, ... FROM RawReportIntegratedCampaignEntity

-- Type = "AdGroup": từ RawReportIntegratedAdGroupEntity
INSERT INTO Fact_QuangCao (Type, EntityId, Spend, ...)
SELECT 'AdGroup', AdGroupId, Spend, ... FROM RawReportIntegratedAdGroupEntity

-- Type = "Ad": từ RawReportIntegratedAdEntity
INSERT INTO Fact_QuangCao (Type, EntityId, Spend, ...)
SELECT 'Ad', AdId, Spend, ... FROM RawReportIntegratedAdEntity
```

## Lưu ý quan trọng

### ✅ Đã sửa theo feedback

- **Bỏ Dim_Asset**: Không cần thiết
- **Type field**: Bắt buộc trong Fact_GiaoDich và Fact_QuangCao cho ETL
- **Chỉ dữ liệu thực tế**: Tất cả trường đều có trong Raw entities
- **Đơn giản hóa**: Bỏ hết trường thừa thãi

### 🎯 Nguyên tắc Dim-Fact

- **Dim_Date**: Tiêu chí thời gian → định lượng theo ngày/tháng/quý
- **Dim_AdAccount**: Tiêu chí tài khoản → định lượng theo account
- **Dim_BusinessCenter**: Tiêu chí tổ chức → định lượng theo BC
- **Dim_Campaign**: Tiêu chí chiến dịch → định lượng theo campaign
- **Type field**: Tiêu chí nguồn dữ liệu → định lượng theo cấp độ

### 📊 Performance & Scalability

- **Estimated Volume**: 10-50M records/month per Fact table
- **Indexing**: DateKey + Type + EntityId cho performance
- **Partitioning**: Monthly theo DateKey
- **ETL Strategy**: Hourly incremental với Type-based routing
