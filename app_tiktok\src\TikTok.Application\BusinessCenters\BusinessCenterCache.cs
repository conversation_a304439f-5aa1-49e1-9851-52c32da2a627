using Microsoft.Extensions.Caching.Distributed;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Repositories;
using TikTok.Cache;
using Volo.Abp.Caching;
using Volo.Abp.ObjectMapping;

namespace TikTok.BusinessCenters
{
    public class BusinessCenterCache : IBusinessCenterCache, ICacheService
    {
        private readonly IBusinessCenterRepository _repository;
        private readonly IDistributedCache<List<BusinessCenterDto>, string> _businessCenterListCache;
        private readonly IObjectMapper _mapper;
        private const string BusinessCenterAllCacheKey = "BusinessCenter:All";

        public BusinessCenterCache(
            IBusinessCenterRepository repository,
            IDistributedCache<List<BusinessCenterDto>, string> businessCenterListCache,
            IObjectMapper mapper)
        {
            _repository = repository;
            _businessCenterListCache = businessCenterListCache;
            _mapper = mapper;
        }

        public async Task<BusinessCenterDto?> GetByBcIdAsync(string bcId)
        {
            var businessCenters = await GetAllAsync();
            var businessCenter = businessCenters.FirstOrDefault(x => x.BcId == bcId);
            return businessCenter;
        }

        public async Task<BusinessCenterDto?> GetById(Guid id)
        {
            var businessCenters = await GetAllAsync();
            var businessCenter = businessCenters.FirstOrDefault(x => x.Id == id);
            return businessCenter;
        }

        public async Task CleanCache()
        {
            await _businessCenterListCache.RemoveAsync(BusinessCenterAllCacheKey, hideErrors: true);
        }

        // ICacheService implementation
        public string CacheName => "Business Center Cache";

        public async Task<List<string>> GetCacheKeysAsync()
        {
            return new List<string> { BusinessCenterAllCacheKey };
        }

        public async Task<bool> ClearCacheAsync()
        {
            try
            {
                await _businessCenterListCache.RemoveAsync(BusinessCenterAllCacheKey, hideErrors: true);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ClearCacheByKeyAsync(string cacheKey)
        {
            try
            {
                if (cacheKey == BusinessCenterAllCacheKey)
                {
                    await _businessCenterListCache.RemoveAsync(cacheKey, hideErrors: true);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<CacheInfoDto> GetCacheInfoAsync()
        {
            var cacheKeys = await GetCacheKeysAsync();
            return new CacheInfoDto
            {
                CacheName = CacheName,
                ActiveKeysCount = cacheKeys.Count,
                CacheKeys = cacheKeys,
                CreatedAt = DateTime.UtcNow,
                LastAccessedAt = DateTime.UtcNow,
                LastModifiedAt = DateTime.UtcNow,
                Status = "Active",
                Description = "Cache cho danh sách Business Center"
            };
        }

        public async Task<List<BusinessCenterDto>> GetAllAsync()
        {
            var cacheKey = BusinessCenterAllCacheKey;
            var businessCenters = await _businessCenterListCache.GetOrAddAsync(
                cacheKey,
                async () =>
                {
                    var businessCenterEntities = await _repository.GetListAsync();
                    return _mapper.Map<List<RawBusinessCenterEntity>, List<BusinessCenterDto>>(businessCenterEntities);
                },
                () => new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = CacheConst.CACHE_EXPIRATION
                });
            return businessCenters;
        }
    }
}