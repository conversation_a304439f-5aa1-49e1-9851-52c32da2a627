/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Create Advertiser Models

    /// <summary>
    /// Request for creating an ad account in Business Center
    /// </summary>
    public class CreateAdvertiserRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Whether to add the ad account to a billing group. Default value: false
        /// </summary>
        [JsonPropertyName("tied_to_billing_group")]
        public bool? TiedToBillingGroup { get; set; }

        /// <summary>
        /// Ad Account information (Required)
        /// </summary>
        [JsonPropertyName("advertiser_info")]
        public CreateAdvertiserInfo AdvertiserInfo { get; set; } = new();

        /// <summary>
        /// Business Information (Required)
        /// </summary>
        [JsonPropertyName("customer_info")]
        public CustomerInfo CustomerInfo { get; set; } = new();

        /// <summary>
        /// Qualification information (Conditional - Required for AGENCY or SELF_SERVICE_AGENCY)
        /// </summary>
        [JsonPropertyName("qualification_info")]
        public QualificationInfo? QualificationInfo { get; set; }

        /// <summary>
        /// Contact Information
        /// </summary>
        [JsonPropertyName("contact_info")]
        public ContactInfo? ContactInfo { get; set; }

        /// <summary>
        /// Billing information (Conditional - Required for France, Brazil, or Mexico)
        /// </summary>
        [JsonPropertyName("billing_info")]
        public BillingInfo? BillingInfo { get; set; }

        /// <summary>
        /// Billing group information
        /// </summary>
        [JsonPropertyName("billing_group_info")]
        public BillingGroupInfo? BillingGroupInfo { get; set; }
    }

    /// <summary>
    /// Ad Account information for creating advertiser
    /// </summary>
    public class CreateAdvertiserInfo
    {
        /// <summary>
        /// Ad Account name (Required) - length cannot exceed 100 characters
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Ad Account currency (Required) - need to be consistent with the Business Center
        /// </summary>
        [JsonPropertyName("currency")]
        public string Currency { get; set; } = string.Empty;

        /// <summary>
        /// Ad Account time zone code (Required)
        /// </summary>
        [JsonPropertyName("timezone")]
        public string Timezone { get; set; } = string.Empty;

        /// <summary>
        /// Type of ad account. Enum values: RESERVATION, AUCTION (default)
        /// </summary>
        [JsonPropertyName("type")]
        public string? Type { get; set; }
    }

    /// <summary>
    /// Business Information
    /// </summary>
    public class CustomerInfo
    {
        /// <summary>
        /// Ad Account company name (Required) - Length cannot exceed 255 characters
        /// </summary>
        [JsonPropertyName("company")]
        public string Company { get; set; } = string.Empty;

        /// <summary>
        /// Ad Account industry ID (Required)
        /// </summary>
        [JsonPropertyName("industry")]
        public int Industry { get; set; }

        /// <summary>
        /// Ad Account registration code (Required)
        /// </summary>
        [JsonPropertyName("registered_area")]
        public string RegisteredArea { get; set; } = string.Empty;
    }

    /// <summary>
    /// Qualification information
    /// </summary>
    public class QualificationInfo
    {
        /// <summary>
        /// Promotion link (Conditional - Required for AGENCY or SELF_SERVICE_AGENCY)
        /// </summary>
        [JsonPropertyName("promotion_link")]
        public string? PromotionLink { get; set; }

        /// <summary>
        /// Business license number (Conditional - Required for Chinese mainland, Hong Kong, Brazil, Mexico)
        /// </summary>
        [JsonPropertyName("license_no")]
        public string? LicenseNo { get; set; }

        /// <summary>
        /// Qualified Document picture ID (Conditional - Required for Chinese mainland or Hong Kong)
        /// </summary>
        [JsonPropertyName("license_image_id")]
        public string? LicenseImageId { get; set; }

        /// <summary>
        /// Other Qualified Documents picture IDs (Conditional - Required for France, Brazil, or Mexico)
        /// </summary>
        [JsonPropertyName("qualification_image_ids")]
        public List<string>? QualificationImageIds { get; set; }

        /// <summary>
        /// Qualification id for reusing qualification information of other ad accounts
        /// </summary>
        [JsonPropertyName("qualification_id")]
        public string? QualificationId { get; set; }
    }

    /// <summary>
    /// Contact Information
    /// </summary>
    public class ContactInfo
    {
        /// <summary>
        /// Contact name - No more than 100 characters in length
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Contact email (Conditional - Required for France, Brazil, or Mexico)
        /// </summary>
        [JsonPropertyName("email")]
        public string? Email { get; set; }

        /// <summary>
        /// Contact phone
        /// </summary>
        [JsonPropertyName("number")]
        public string? Number { get; set; }
    }

    /// <summary>
    /// Billing information
    /// </summary>
    public class BillingInfo
    {
        /// <summary>
        /// Billing address - no more than 512 characters in length
        /// </summary>
        [JsonPropertyName("address")]
        public string? Address { get; set; }

        /// <summary>
        /// Billing and invoicing tax number
        /// </summary>
        [JsonPropertyName("tax_field_dict")]
        public Dictionary<string, string>? TaxFieldDict { get; set; }
    }

    /// <summary>
    /// Billing group information
    /// </summary>
    public class BillingGroupInfo
    {
        /// <summary>
        /// Invoicing mode (Required). Enum values: ACCOUNT, ADVERTISER
        /// </summary>
        [JsonPropertyName("invoice_group_by")]
        public string InvoiceGroupBy { get; set; } = string.Empty;

        /// <summary>
        /// Billing group ID. Valid when invoice_group_by is ACCOUNT
        /// </summary>
        [JsonPropertyName("billing_group_id")]
        public string? BillingGroupId { get; set; }

        /// <summary>
        /// Party who pays the invoices (Conditional - Required for France). Enum values: AGENCY, ADVERTISER
        /// </summary>
        [JsonPropertyName("invoice_payer")]
        public string? InvoicePayer { get; set; }
    }

    /// <summary>
    /// Response for creating an ad account
    /// </summary>
    public class CreateAdvertiserResponse
    {
        /// <summary>
        /// Ad Account ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }
    }

    #endregion

    #region Update Advertiser Models

    /// <summary>
    /// Request for updating an ad account
    /// </summary>
    public class UpdateAdvertiserRequest
    {
        /// <summary>
        /// Ad account ID (Conditional - Either advertiser_id or bc_id has to be set)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Ad account name
        /// </summary>
        [JsonPropertyName("advertiser_name")]
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// Company name
        /// </summary>
        [JsonPropertyName("company")]
        public string? Company { get; set; }

        /// <summary>
        /// Name of the contact person
        /// </summary>
        [JsonPropertyName("contact_name")]
        public string? ContactName { get; set; }

        /// <summary>
        /// Contact email
        /// </summary>
        [JsonPropertyName("contact_email")]
        public string? ContactEmail { get; set; }

        /// <summary>
        /// Contact phone number
        /// </summary>
        [JsonPropertyName("contact_number")]
        public string? ContactNumber { get; set; }

        /// <summary>
        /// Promotion link - Length cannot exceed 255 characters
        /// </summary>
        [JsonPropertyName("promotion_link")]
        public string? PromotionLink { get; set; }

        /// <summary>
        /// Business license number
        /// </summary>
        [JsonPropertyName("license_no")]
        public string? LicenseNo { get; set; }

        /// <summary>
        /// Image ID of the business license
        /// </summary>
        [JsonPropertyName("license_image_id")]
        public string? LicenseImageId { get; set; }

        /// <summary>
        /// Additional business certificates
        /// </summary>
        [JsonPropertyName("qualification_images")]
        public List<QualificationImage>? QualificationImages { get; set; }

        /// <summary>
        /// Business address that is shown on receipts
        /// </summary>
        [JsonPropertyName("address")]
        public string? Address { get; set; }

        /// <summary>
        /// Billing and invoicing tax number
        /// </summary>
        [JsonPropertyName("tax_map")]
        public Dictionary<string, string>? TaxMap { get; set; }

        /// <summary>
        /// Whether you want to submit the new certificate images for review
        /// </summary>
        [JsonPropertyName("need_submit_certificate")]
        public bool? NeedSubmitCertificate { get; set; }

        /// <summary>
        /// Business Center ID (Conditional - Either advertiser_id or bc_id has to be set)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string? BcId { get; set; }

        /// <summary>
        /// The type of changes you want to perform on your budget
        /// </summary>
        [JsonPropertyName("budget_update_type")]
        public string? BudgetUpdateType { get; set; }

        /// <summary>
        /// Information about the new budget settings of one or more ad accounts
        /// </summary>
        [JsonPropertyName("advertiser_budgets")]
        public List<AdvertiserBudget>? AdvertiserBudgets { get; set; }
    }

    /// <summary>
    /// Qualification image information
    /// </summary>
    public class QualificationImage
    {
        /// <summary>
        /// Image ID of the business certificate
        /// </summary>
        [JsonPropertyName("image_id")]
        public string? ImageId { get; set; }
    }

    /// <summary>
    /// Advertiser budget information
    /// </summary>
    public class AdvertiserBudget
    {
        /// <summary>
        /// Ad account ID (Required when advertiser_budgets is passed)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Budget mode of the ad account
        /// </summary>
        [JsonPropertyName("budget_mode")]
        public string? BudgetMode { get; set; }

        /// <summary>
        /// Budget amount
        /// </summary>
        [JsonPropertyName("budget")]
        public float? Budget { get; set; }
    }

    /// <summary>
    /// Response for updating an ad account
    /// </summary>
    public class UpdateAdvertiserResponse
    {
        /// <summary>
        /// List of ad accounts of which the budget settings are updated
        /// </summary>
        [JsonPropertyName("list")]
        public List<AdvertiserUpdateResult>? List { get; set; }
    }

    /// <summary>
    /// Advertiser update result
    /// </summary>
    public class AdvertiserUpdateResult
    {
        /// <summary>
        /// Ad account ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Status of update. Enum values: SUCCESS, FAILED
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("error_msg")]
        public string? ErrorMsg { get; set; }

        /// <summary>
        /// The minimum budget amount that you have set for the ad account
        /// </summary>
        [JsonPropertyName("one_click_set_amount")]
        public float? OneClickSetAmount { get; set; }
    }

    #endregion

    #region Disable Advertiser Models

    /// <summary>
    /// Request for disabling an ad account
    /// </summary>
    public class DisableAdvertiserRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// IDs of the ad accounts in the Business Center that you want to disable (Required)
        /// </summary>
        [JsonPropertyName("advertiser_ids")]
        public List<string> AdvertiserIds { get; set; } = new();
    }

    /// <summary>
    /// Response for disabling an ad account
    /// </summary>
    public class DisableAdvertiserResponse
    {
        /// <summary>
        /// IDs of the ad accounts that have been successfully disabled
        /// </summary>
        [JsonPropertyName("disabled_advertiser_ids")]
        public List<string>? DisabledAdvertiserIds { get; set; }

        /// <summary>
        /// Information about the ad accounts that have not been successfully disabled
        /// </summary>
        [JsonPropertyName("failed_infos")]
        public Dictionary<string, List<string>>? FailedInfos { get; set; }
    }

    #endregion

    #region Upload Business Certificate Models

    /// <summary>
    /// Response for uploading a business certificate
    /// </summary>
    public class UploadBusinessCertificateResponse
    {
        /// <summary>
        /// The ID of the uploaded image
        /// </summary>
        [JsonPropertyName("image_id")]
        public string? ImageId { get; set; }

        /// <summary>
        /// The preview link to the uploaded certificate image
        /// </summary>
        [JsonPropertyName("image_url")]
        public string? ImageUrl { get; set; }
    }

    #endregion

    #region Get Qualifications Models

    /// <summary>
    /// Request for getting qualifications within a Business Center
    /// </summary>
    public class GetQualificationsRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public QualificationFiltering? Filtering { get; set; }

        /// <summary>
        /// Current number of pages. Default value: 1. Value range: ≥ 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10. Value range: 1-100
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering conditions for qualifications
    /// </summary>
    public class QualificationFiltering
    {
        /// <summary>
        /// Whether the qualifications are verified or not
        /// </summary>
        [JsonPropertyName("verified")]
        public bool? Verified { get; set; }
    }

    /// <summary>
    /// Response for getting qualifications
    /// </summary>
    public class GetQualificationsResponse
    {
        /// <summary>
        /// Qualification information
        /// </summary>
        [JsonPropertyName("qualifications")]
        public List<QualificationDetails>? Qualifications { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Qualification details
    /// </summary>
    public class QualificationDetails
    {
        /// <summary>
        /// Qualification id
        /// </summary>
        [JsonPropertyName("qualification_id")]
        public string? QualificationId { get; set; }

        /// <summary>
        /// Company name
        /// </summary>
        [JsonPropertyName("company_name")]
        public string? CompanyName { get; set; }

        /// <summary>
        /// Qualifications verification status. Enum values: VERIFIED, UNVERIFIED
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Advertiser ID of the qualifications owner
        /// </summary>
        [JsonPropertyName("owner_advertiser_id")]
        public string? OwnerAdvertiserId { get; set; }

        /// <summary>
        /// Number of linked advertiser accounts
        /// </summary>
        [JsonPropertyName("linked_advertiser_count")]
        public int? LinkedAdvertiserCount { get; set; }

        /// <summary>
        /// Country or region code
        /// </summary>
        [JsonPropertyName("region_code")]
        public string? RegionCode { get; set; }
    }

    #endregion

    #region UnionPay Verification Models

    /// <summary>
    /// Request for checking UnionPay verification requirement
    /// </summary>
    public class CheckUnionPayVerificationRequest
    {
        /// <summary>
        /// Business license number (Required)
        /// </summary>
        [JsonPropertyName("license_no")]
        public string LicenseNo { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for checking UnionPay verification requirement
    /// </summary>
    public class CheckUnionPayVerificationResponse
    {
        /// <summary>
        /// Whether UnionPay verification is required for the business license
        /// </summary>
        [JsonPropertyName("unionpay_verification_required")]
        public bool? UnionpayVerificationRequired { get; set; }
    }

    /// <summary>
    /// Request for submitting UnionPay verification
    /// </summary>
    public class SubmitUnionPayVerificationRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Ad account ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The Chinese name of the legal representative (Required)
        /// </summary>
        [JsonPropertyName("representative_name")]
        public string RepresentativeName { get; set; } = string.Empty;

        /// <summary>
        /// The type of the representative document
        /// </summary>
        [JsonPropertyName("representative_document_type")]
        public string? RepresentativeDocumentType { get; set; }

        /// <summary>
        /// The ID of the representative document (Required)
        /// </summary>
        [JsonPropertyName("representative_id")]
        public string RepresentativeId { get; set; } = string.Empty;

        /// <summary>
        /// The UnionPay account number in 12 to 19 digits (Required)
        /// </summary>
        [JsonPropertyName("unionpay_account")]
        public string UnionpayAccount { get; set; } = string.Empty;

        /// <summary>
        /// The phone number of the legal representative in 11 digits (Required)
        /// </summary>
        [JsonPropertyName("representative_phone_number")]
        public string RepresentativePhoneNumber { get; set; } = string.Empty;
    }

    #endregion

    #region Get Assets Models

    /// <summary>
    /// Request for getting assets
    /// </summary>
    public class GetAssetsRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Type of asset that you want to get (Required)
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public AssetFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Value range: 1-50. Default value: 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering conditions for assets
    /// </summary>
    public class AssetFiltering
    {
        /// <summary>
        /// ID of the user that you want to get assets for
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// Email of the user that you want to get assets for
        /// </summary>
        [JsonPropertyName("user_email")]
        public string? UserEmail { get; set; }

        /// <summary>
        /// Keyword of the asset you are looking for
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }
    }

    /// <summary>
    /// Response for getting assets
    /// </summary>
    public class GetAssetsResponse
    {
        /// <summary>
        /// List of assets
        /// </summary>
        [JsonPropertyName("list")]
        public List<AssetInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Asset information
    /// </summary>
    public class AssetInfo
    {
        /// <summary>
        /// Asset type
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string? AssetType { get; set; }

        /// <summary>
        /// Asset ID
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string? AssetId { get; set; }

        /// <summary>
        /// Asset name
        /// </summary>
        [JsonPropertyName("asset_name")]
        public string? AssetName { get; set; }

        /// <summary>
        /// Type of the ad account
        /// </summary>
        [JsonPropertyName("advertiser_account_type")]
        public string? AdvertiserAccountType { get; set; }

        /// <summary>
        /// Business Center user's permissions to the ad account
        /// </summary>
        [JsonPropertyName("advertiser_role")]
        public string? AdvertiserRole { get; set; }

        /// <summary>
        /// Business Center user's permissions to the catalog
        /// </summary>
        [JsonPropertyName("catalog_role")]
        public string? CatalogRole { get; set; }

        /// <summary>
        /// Whether the catalog can be used in ads
        /// </summary>
        [JsonPropertyName("ad_creation_eligible")]
        public string? AdCreationEligible { get; set; }

        /// <summary>
        /// Business Center user's permission to the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_role")]
        public string? StoreRole { get; set; }

        /// <summary>
        /// Business Center user's permission to the TikTok account
        /// </summary>
        [JsonPropertyName("tt_account_roles")]
        public List<string>? TtAccountRoles { get; set; }

        /// <summary>
        /// The owner Business Center of this asset
        /// </summary>
        [JsonPropertyName("owner_bc_name")]
        public string? OwnerBcName { get; set; }
    }

    #endregion

    #region Get Assets As Admin Models

    /// <summary>
    /// Request for getting assets as admin
    /// </summary>
    public class GetAssetsAsAdminRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// The type of asset that you want to get (Required)
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public AdminAssetFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Value range: 1-50. Default value: 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering conditions for admin assets
    /// </summary>
    public class AdminAssetFiltering
    {
        /// <summary>
        /// Ad account display status
        /// </summary>
        [JsonPropertyName("advertiser_show_status")]
        public string? AdvertiserShowStatus { get; set; }

        /// <summary>
        /// The type of the relation between the Business Center and the asset
        /// </summary>
        [JsonPropertyName("relation_type")]
        public string? RelationType { get; set; }

        /// <summary>
        /// The status of the relation between the Business Center account and the asset
        /// </summary>
        [JsonPropertyName("relation_status")]
        public string? RelationStatus { get; set; }
    }

    /// <summary>
    /// Response for getting assets as admin
    /// </summary>
    public class GetAssetsAsAdminResponse
    {
        /// <summary>
        /// List of assets
        /// </summary>
        [JsonPropertyName("list")]
        public List<AdminAssetInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Admin asset information
    /// </summary>
    public class AdminAssetInfo
    {
        /// <summary>
        /// Asset type
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string? AssetType { get; set; }

        /// <summary>
        /// Asset ID
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string? AssetId { get; set; }

        /// <summary>
        /// Asset name
        /// </summary>
        [JsonPropertyName("asset_name")]
        public string? AssetName { get; set; }

        /// <summary>
        /// The type of the relation between the Business Center and the asset
        /// </summary>
        [JsonPropertyName("relation_type")]
        public string? RelationType { get; set; }

        /// <summary>
        /// Status of the relation between the Business Center account and the asset
        /// </summary>
        [JsonPropertyName("relation_status")]
        public string? RelationStatus { get; set; }

        /// <summary>
        /// Advertiser status
        /// </summary>
        [JsonPropertyName("advertiser_status")]
        public string? AdvertiserStatus { get; set; }

        /// <summary>
        /// The type of the ad account
        /// </summary>
        [JsonPropertyName("advertiser_account_type")]
        public string? AdvertiserAccountType { get; set; }

        /// <summary>
        /// Business Center user's permissions to the ad account
        /// </summary>
        [JsonPropertyName("advertiser_role")]
        public string? AdvertiserRole { get; set; }

        /// <summary>
        /// Business Center user's permissions to the catalog
        /// </summary>
        [JsonPropertyName("catalog_role")]
        public string? CatalogRole { get; set; }

        /// <summary>
        /// Whether the catalog can be used in ads
        /// </summary>
        [JsonPropertyName("ad_creation_eligible")]
        public string? AdCreationEligible { get; set; }

        /// <summary>
        /// Business Center user's permission to the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_role")]
        public string? StoreRole { get; set; }

        /// <summary>
        /// The pixel code
        /// </summary>
        [JsonPropertyName("pixel_code")]
        public string? PixelCode { get; set; }

        /// <summary>
        /// Business Center user's permission to the TikTok account
        /// </summary>
        [JsonPropertyName("tt_account_roles")]
        public List<string>? TtAccountRoles { get; set; }

        /// <summary>
        /// The owner Business Center of this asset
        /// </summary>
        [JsonPropertyName("owner_bc_name")]
        public string? OwnerBcName { get; set; }
    }

    #endregion

    #region Assign Asset Models

    /// <summary>
    /// Request for assigning an asset
    /// </summary>
    public class AssignAssetRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the Business Center user that you want to assign the asset to (Required)
        /// </summary>
        [JsonPropertyName("user_id")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type (Required)
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Asset ID (Required)
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Advertiser role (Conditional - Required when asset_type is ADVERTISER)
        /// </summary>
        [JsonPropertyName("advertiser_role")]
        public string? AdvertiserRole { get; set; }

        /// <summary>
        /// Catalog role (Conditional - Required when asset_type is CATALOG)
        /// </summary>
        [JsonPropertyName("catalog_role")]
        public string? CatalogRole { get; set; }

        /// <summary>
        /// Form library role (Conditional - Required when asset_type is LEAD)
        /// </summary>
        [JsonPropertyName("form_library_role")]
        public string? FormLibraryRole { get; set; }

        /// <summary>
        /// TikTok account roles (Conditional - Required when asset_type is TT_ACCOUNT)
        /// </summary>
        [JsonPropertyName("tt_account_roles")]
        public List<string>? TtAccountRoles { get; set; }

        /// <summary>
        /// Store role (Conditional - Required when asset_type is TIKTOK_SHOP)
        /// </summary>
        [JsonPropertyName("store_role")]
        public string? StoreRole { get; set; }
    }

    #endregion

    #region Unassign Asset Models

    /// <summary>
    /// Request for unassigning an asset
    /// </summary>
    public class UnassignAssetRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the Business Center user that you want to revoke the access to the asset from (Required)
        /// </summary>
        [JsonPropertyName("user_id")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type (Required)
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Asset ID (Required)
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;
    }

    #endregion

    #region Pixel Management Models

    /// <summary>
    /// Request for transferring a pixel
    /// </summary>
    public class TransferPixelRequest
    {
        /// <summary>
        /// The BC id that you would like to transfer the pixel to (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// The pixel code that you would like to transfer to BC (Required)
        /// </summary>
        [JsonPropertyName("pixel_code")]
        public string PixelCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for transferring a pixel
    /// </summary>
    public class TransferPixelResponse
    {
        /// <summary>
        /// Asset ID
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string? AssetId { get; set; }
    }

    /// <summary>
    /// Request for updating pixel link
    /// </summary>
    public class UpdatePixelLinkRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Pixel Code (Required)
        /// </summary>
        [JsonPropertyName("pixel_code")]
        public string PixelCode { get; set; } = string.Empty;

        /// <summary>
        /// Advertiser ID list (Required)
        /// </summary>
        [JsonPropertyName("advertiser_ids")]
        public List<string> AdvertiserIds { get; set; } = new();

        /// <summary>
        /// Relation status (Required). Enum values: LINK, UNLINK
        /// </summary>
        [JsonPropertyName("relation_status")]
        public string RelationStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for getting pixel links
    /// </summary>
    public class GetPixelLinksRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Pixel Code (Required)
        /// </summary>
        [JsonPropertyName("pixel_code")]
        public string PixelCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting pixel links
    /// </summary>
    public class GetPixelLinksResponse
    {
        /// <summary>
        /// List of advertisers
        /// </summary>
        [JsonPropertyName("list")]
        public List<PixelLinkedAdvertiser>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Pixel linked advertiser information
    /// </summary>
    public class PixelLinkedAdvertiser
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }
    }

    #endregion

    #region Asset Partner/Member Models

    /// <summary>
    /// Request for getting asset partners
    /// </summary>
    public class GetAssetPartnersRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type (Required). Enum values: ADVERTISER, CATALOG
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Asset ID (Required)
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering object
        /// </summary>
        [JsonPropertyName("filtering")]
        public PartnerFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Partner filtering conditions
    /// </summary>
    public class PartnerFiltering
    {
        /// <summary>
        /// Partner name keyword that you want to filter by (Required)
        /// </summary>
        [JsonPropertyName("keyword")]
        public string Keyword { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting asset partners
    /// </summary>
    public class GetAssetPartnersResponse
    {
        /// <summary>
        /// List of partners
        /// </summary>
        [JsonPropertyName("list")]
        public List<PartnerInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Partner information
    /// </summary>
    public class PartnerInfo
    {
        /// <summary>
        /// Partner name
        /// </summary>
        [JsonPropertyName("bc_name")]
        public string? BcName { get; set; }

        /// <summary>
        /// Partner ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string? BcId { get; set; }
    }

    #endregion

    #region Asset Member Models

    /// <summary>
    /// Request for getting asset members
    /// </summary>
    public class GetAssetMembersRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type (Required). Enum values: ADVERTISER, CATALOG, LEAD
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Asset ID (Required)
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public MemberFiltering? Filtering { get; set; }
    }

    /// <summary>
    /// Member filtering conditions
    /// </summary>
    public class MemberFiltering
    {
        /// <summary>
        /// Keyword to filter by
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }
    }

    /// <summary>
    /// Response for getting asset members
    /// </summary>
    public class GetAssetMembersResponse
    {
        /// <summary>
        /// List of members
        /// </summary>
        [JsonPropertyName("list")]
        public List<MemberInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Member information
    /// </summary>
    public class MemberInfo
    {
        /// <summary>
        /// User ID
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// User name
        /// </summary>
        [JsonPropertyName("user_name")]
        public string? UserName { get; set; }

        /// <summary>
        /// Ad account role. Enum values: ADMIN, OPERATOR, ANALYST
        /// </summary>
        [JsonPropertyName("advertiser_role")]
        public string? AdvertiserRole { get; set; }

        /// <summary>
        /// Business Center user's permissions to the catalog
        /// </summary>
        [JsonPropertyName("catalog_role")]
        public string? CatalogRole { get; set; }

        /// <summary>
        /// User email
        /// </summary>
        [JsonPropertyName("user_email")]
        public string? UserEmail { get; set; }
    }

    #endregion

    #region Delete Assets Models

    /// <summary>
    /// Request for deleting assets from a BC
    /// </summary>
    public class DeleteAssetsRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// List of asset IDs that you want to delete (Required)
        /// </summary>
        [JsonPropertyName("asset_ids")]
        public List<string> AssetIds { get; set; } = new();

        /// <summary>
        /// Type of asset you want to delete (Required). Enum values: LEAD, TT_ACCOUNT
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;
    }

    #endregion

    #region Asset Binding Quota Models

    /// <summary>
    /// Request for getting binding info of an asset
    /// </summary>
    public class GetAssetBindingQuotaRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Asset ID (Required)
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type (Required). Enum values: IDENTITY
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting binding info of an asset
    /// </summary>
    public class GetAssetBindingQuotaResponse
    {
        /// <summary>
        /// Total number of ads an asset can bind to
        /// </summary>
        [JsonPropertyName("total_quota")]
        public int? TotalQuota { get; set; }

        /// <summary>
        /// Number of ads an asset has binded to
        /// </summary>
        [JsonPropertyName("used_quota")]
        public int? UsedQuota { get; set; }

        /// <summary>
        /// Number of ads an asset is available to bind to
        /// </summary>
        [JsonPropertyName("available_quota")]
        public int? AvailableQuota { get; set; }
    }

    #endregion

    #region Asset Groups Models

    /// <summary>
    /// Request for creating an Asset Group
    /// </summary>
    public class CreateAssetGroupRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Asset Group name (Required)
        /// </summary>
        [JsonPropertyName("asset_group_name")]
        public string AssetGroupName { get; set; } = string.Empty;

        /// <summary>
        /// Assets you want to add to the Asset Group (Required). Max size is 200.
        /// </summary>
        [JsonPropertyName("assets")]
        public List<AssetGroupAsset> Assets { get; set; } = new();

        /// <summary>
        /// Business Center members you want to grant access to your Asset Group (Required). Max size is 50.
        /// </summary>
        [JsonPropertyName("members")]
        public List<AssetGroupMember> Members { get; set; } = new();
    }

    /// <summary>
    /// Asset information for Asset Group
    /// </summary>
    public class AssetGroupAsset
    {
        /// <summary>
        /// Asset ID (Required). When the asset type is ADVERTISER, this is the advertiser ID.
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type. Currently, the only supported type is ADVERTISER. Default value: ADVERTISER
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string? AssetType { get; set; }
    }

    /// <summary>
    /// Member information for Asset Group
    /// </summary>
    public class AssetGroupMember
    {
        /// <summary>
        /// Member ID (Required)
        /// </summary>
        [JsonPropertyName("member_id")]
        public string MemberId { get; set; } = string.Empty;

        /// <summary>
        /// The roles you want to assign to the member according to the asset type.
        /// Key: Asset type (ADVERTISER). Value: Assigned role.
        /// Default value: "ADVERTISER: ADVERTISER_ROLE_ADMIN"
        /// </summary>
        [JsonPropertyName("asset_roles")]
        public Dictionary<string, string>? AssetRoles { get; set; }
    }

    /// <summary>
    /// Response for creating an Asset Group
    /// </summary>
    public class CreateAssetGroupResponse
    {
        /// <summary>
        /// ID of the Asset Group that was created
        /// </summary>
        [JsonPropertyName("asset_group_id")]
        public string AssetGroupId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for updating an Asset Group
    /// </summary>
    public class UpdateAssetGroupRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the Asset Group you want to update (Required)
        /// </summary>
        [JsonPropertyName("asset_group_id")]
        public string AssetGroupId { get; set; } = string.Empty;

        /// <summary>
        /// The entity that you want to update (Required). Enum values: ASSET, MEMBER, NAME
        /// </summary>
        [JsonPropertyName("update_entity")]
        public string UpdateEntity { get; set; } = string.Empty;

        /// <summary>
        /// Asset Group name. Required when the update_entity is NAME.
        /// </summary>
        [JsonPropertyName("asset_group_name")]
        public string? AssetGroupName { get; set; }

        /// <summary>
        /// Type of action to be performed. Enum values: ADD, DELETE. Required when the update_entity is ASSET or MEMBER.
        /// </summary>
        [JsonPropertyName("action")]
        public string? Action { get; set; }

        /// <summary>
        /// Asset information. Max size is 200. Required when the update_entity is ASSET.
        /// </summary>
        [JsonPropertyName("assets")]
        public List<AssetGroupAsset>? Assets { get; set; }

        /// <summary>
        /// Member information. Max size is 50. Required when the update_entity is MEMBER.
        /// </summary>
        [JsonPropertyName("members")]
        public List<AssetGroupMember>? Members { get; set; }
    }

    /// <summary>
    /// Response for updating an Asset Group
    /// </summary>
    public class UpdateAssetGroupResponse
    {
        /// <summary>
        /// Status of the update operation
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Error message if update failed
        /// </summary>
        [JsonPropertyName("error_msg")]
        public string? ErrorMsg { get; set; }
    }

    /// <summary>
    /// Request for listing Asset Groups
    /// </summary>
    public class ListAssetGroupsRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public AssetGroupListFiltering? Filtering { get; set; }

        /// <summary>
        /// Page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering conditions for listing Asset Groups
    /// </summary>
    public class AssetGroupListFiltering
    {
        /// <summary>
        /// Keyword to filter by. Currently you can only use exact matching by specifying asset_group_name or asset_group_id here.
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }
    }

    /// <summary>
    /// Response for listing Asset Groups
    /// </summary>
    public class ListAssetGroupsResponse
    {
        /// <summary>
        /// Asset Group information
        /// </summary>
        [JsonPropertyName("asset_groups")]
        public List<AssetGroupInfo> AssetGroups { get; set; } = new();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new();
    }

    /// <summary>
    /// Asset Group information
    /// </summary>
    public class AssetGroupInfo
    {
        /// <summary>
        /// Asset Group ID
        /// </summary>
        [JsonPropertyName("asset_group_id")]
        public string AssetGroupId { get; set; } = string.Empty;

        /// <summary>
        /// Asset Group name
        /// </summary>
        [JsonPropertyName("asset_group_name")]
        public string AssetGroupName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for getting the details of an Asset Group
    /// </summary>
    public class GetAssetGroupRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Asset Group ID (Required)
        /// </summary>
        [JsonPropertyName("asset_group_id")]
        public string AssetGroupId { get; set; } = string.Empty;

        /// <summary>
        /// The entity that you want to query (Required). Enum values: ASSET, MEMBER
        /// </summary>
        [JsonPropertyName("query_entity")]
        public string QueryEntity { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public AssetGroupGetFiltering? Filtering { get; set; }

        /// <summary>
        /// Page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering conditions for getting Asset Group details
    /// </summary>
    public class AssetGroupGetFiltering
    {
        /// <summary>
        /// Keyword to filter by.
        /// If the query_entity is ASSET, you can either use fuzzy search by specifying asset_name related keywords or use exact search by specifying asset_id to filter the results.
        /// If the query_entity is MEMBER, you can either use fuzzy search by specifying user_name related keywords or use exact search by specifying user_email to filter the results.
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }
    }

    /// <summary>
    /// Response for getting the details of an Asset Group
    /// </summary>
    public class GetAssetGroupResponse
    {
        /// <summary>
        /// Asset Group information
        /// </summary>
        [JsonPropertyName("asset_group")]
        public AssetGroupDetails AssetGroup { get; set; } = new();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new();
    }

    /// <summary>
    /// Detailed Asset Group information
    /// </summary>
    public class AssetGroupDetails
    {
        /// <summary>
        /// Asset Group name
        /// </summary>
        [JsonPropertyName("asset_group_name")]
        public string AssetGroupName { get; set; } = string.Empty;

        /// <summary>
        /// Asset Group ID
        /// </summary>
        [JsonPropertyName("asset_group_id")]
        public string AssetGroupId { get; set; } = string.Empty;

        /// <summary>
        /// Asset information. Returned when query_entity is ASSET.
        /// </summary>
        [JsonPropertyName("assets")]
        public List<AssetGroupAssetDetails>? Assets { get; set; }

        /// <summary>
        /// Member information. Returned when query_entity is MEMBER.
        /// </summary>
        [JsonPropertyName("members")]
        public List<AssetGroupMemberDetails>? Members { get; set; }
    }

    /// <summary>
    /// Detailed asset information in Asset Group
    /// </summary>
    public class AssetGroupAssetDetails
    {
        /// <summary>
        /// Asset type
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Asset ID
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Detailed member information in Asset Group
    /// </summary>
    public class AssetGroupMemberDetails
    {
        /// <summary>
        /// The role that you have assigned to the member according to the asset type
        /// </summary>
        [JsonPropertyName("asset_roles")]
        public Dictionary<string, string> AssetRoles { get; set; } = new();

        /// <summary>
        /// Member ID
        /// </summary>
        [JsonPropertyName("member_id")]
        public string MemberId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for deleting Asset Groups
    /// </summary>
    public class DeleteAssetGroupsRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the Asset Group that you want to delete (Required). Currently, only one ID is allowed.
        /// </summary>
        [JsonPropertyName("asset_group_ids")]
        public List<string> AssetGroupIds { get; set; } = new();
    }

    /// <summary>
    /// Response for deleting Asset Groups
    /// </summary>
    public class DeleteAssetGroupsResponse
    {
        /// <summary>
        /// Status of the delete operation
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;
    }

    #endregion

    #region Asset Sharing Models

    /// <summary>
    /// Request for sharing assets
    /// </summary>
    public class ShareAssetsRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// List of assets to share (Required)
        /// </summary>
        [JsonPropertyName("assets")]
        public List<ShareAssetInfo> Assets { get; set; } = new();

        /// <summary>
        /// List of Business Centers to share assets with (Required)
        /// </summary>
        [JsonPropertyName("share_to_bcs")]
        public List<ShareToBcInfo> ShareToBcs { get; set; } = new();
    }

    /// <summary>
    /// Asset information for sharing
    /// </summary>
    public class ShareAssetInfo
    {
        /// <summary>
        /// Asset ID (Required)
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type (Required)
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Business Center information for sharing
    /// </summary>
    public class ShareToBcInfo
    {
        /// <summary>
        /// Business Center ID to share with (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for sharing assets
    /// </summary>
    public class ShareAssetsResponse
    {
        /// <summary>
        /// List of sharing results
        /// </summary>
        [JsonPropertyName("list")]
        public List<ShareAssetResult> List { get; set; } = new();
    }

    /// <summary>
    /// Result of sharing an asset
    /// </summary>
    public class ShareAssetResult
    {
        /// <summary>
        /// Asset ID
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Business Center ID that the asset was shared with
        /// </summary>
        [JsonPropertyName("share_to_bc_id")]
        public string ShareToBcId { get; set; } = string.Empty;

        /// <summary>
        /// Status of the sharing operation
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Error message if sharing failed
        /// </summary>
        [JsonPropertyName("error_msg")]
        public string? ErrorMsg { get; set; }
    }

    /// <summary>
    /// Request for unsharing assets
    /// </summary>
    public class UnshareAssetsRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// List of assets to unshare (Required)
        /// </summary>
        [JsonPropertyName("assets")]
        public List<ShareAssetInfo> Assets { get; set; } = new();

        /// <summary>
        /// List of Business Centers to unshare assets from (Required)
        /// </summary>
        [JsonPropertyName("unshare_from_bcs")]
        public List<ShareToBcInfo> UnshareFromBcs { get; set; } = new();
    }

    /// <summary>
    /// Response for unsharing assets
    /// </summary>
    public class UnshareAssetsResponse
    {
        /// <summary>
        /// List of unsharing results
        /// </summary>
        [JsonPropertyName("list")]
        public List<UnshareAssetResult> List { get; set; } = new();
    }

    /// <summary>
    /// Result of unsharing an asset
    /// </summary>
    public class UnshareAssetResult
    {
        /// <summary>
        /// Asset ID
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Business Center ID that the asset was unshared from
        /// </summary>
        [JsonPropertyName("unshare_from_bc_id")]
        public string UnshareFromBcId { get; set; } = string.Empty;

        /// <summary>
        /// Status of the unsharing operation
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Error message if unsharing failed
        /// </summary>
        [JsonPropertyName("error_msg")]
        public string? ErrorMsg { get; set; }
    }

    /// <summary>
    /// Request for getting shared assets
    /// </summary>
    public class GetSharedAssetsRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type (Required)
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Share type (Required). Enum values: SHARED, SHARING
        /// </summary>
        [JsonPropertyName("share_type")]
        public string ShareType { get; set; } = string.Empty;

        /// <summary>
        /// Current page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Value range: 1-50. Default value: 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Response for getting shared assets
    /// </summary>
    public class GetSharedAssetsResponse
    {
        /// <summary>
        /// List of shared assets
        /// </summary>
        [JsonPropertyName("list")]
        public List<SharedAssetInfo> List { get; set; } = new();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new();
    }

    /// <summary>
    /// Shared asset information
    /// </summary>
    public class SharedAssetInfo
    {
        /// <summary>
        /// Asset ID
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Asset name
        /// </summary>
        [JsonPropertyName("asset_name")]
        public string AssetName { get; set; } = string.Empty;

        /// <summary>
        /// Asset type
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Business Center name
        /// </summary>
        [JsonPropertyName("bc_name")]
        public string BcName { get; set; } = string.Empty;

        /// <summary>
        /// Share status
        /// </summary>
        [JsonPropertyName("share_status")]
        public string ShareStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for getting asset sharing history
    /// </summary>
    public class GetAssetSharingHistoryRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Asset ID (Required)
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type (Required)
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Current page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Value range: 1-50. Default value: 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Response for getting asset sharing history
    /// </summary>
    public class GetAssetSharingHistoryResponse
    {
        /// <summary>
        /// List of sharing history records
        /// </summary>
        [JsonPropertyName("list")]
        public List<AssetSharingHistoryRecord> List { get; set; } = new();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new();
    }

    /// <summary>
    /// Asset sharing history record
    /// </summary>
    public class AssetSharingHistoryRecord
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Business Center name
        /// </summary>
        [JsonPropertyName("bc_name")]
        public string BcName { get; set; } = string.Empty;

        /// <summary>
        /// Share operation type
        /// </summary>
        [JsonPropertyName("operation_type")]
        public string OperationType { get; set; } = string.Empty;

        /// <summary>
        /// Share status
        /// </summary>
        [JsonPropertyName("share_status")]
        public string ShareStatus { get; set; } = string.Empty;

        /// <summary>
        /// Operation time
        /// </summary>
        [JsonPropertyName("operation_time")]
        public string OperationTime { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for deleting assets
    /// </summary>
    public class DeleteAssetsResponse
    {
        /// <summary>
        /// List of deletion results
        /// </summary>
        [JsonPropertyName("list")]
        public List<DeleteAssetResult> List { get; set; } = new();
    }

    /// <summary>
    /// Result of deleting an asset
    /// </summary>
    public class DeleteAssetResult
    {
        /// <summary>
        /// Asset ID
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string AssetId { get; set; } = string.Empty;

        /// <summary>
        /// Status of the deletion operation
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Error message if deletion failed
        /// </summary>
        [JsonPropertyName("error_msg")]
        public string? ErrorMsg { get; set; }
    }

    /// <summary>
    /// Response for submitting UnionPay verification
    /// </summary>
    public class SubmitUnionPayVerificationResponse
    {
        /// <summary>
        /// Verification submission status
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for getting UnionPay verification status
    /// </summary>
    public class GetUnionPayStatusRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Ad account ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting UnionPay verification status
    /// </summary>
    public class GetUnionPayStatusResponse
    {
        /// <summary>
        /// UnionPay verification status
        /// </summary>
        [JsonPropertyName("unionpay_verification_status")]
        public string UnionpayVerificationStatus { get; set; } = string.Empty;

        /// <summary>
        /// Verification details
        /// </summary>
        [JsonPropertyName("verification_details")]
        public UnionPayVerificationDetails? VerificationDetails { get; set; }
    }

    /// <summary>
    /// UnionPay verification details
    /// </summary>
    public class UnionPayVerificationDetails
    {
        /// <summary>
        /// Verification submission time
        /// </summary>
        [JsonPropertyName("submit_time")]
        public string? SubmitTime { get; set; }

        /// <summary>
        /// Verification review time
        /// </summary>
        [JsonPropertyName("review_time")]
        public string? ReviewTime { get; set; }

        /// <summary>
        /// Rejection reason if verification failed
        /// </summary>
        [JsonPropertyName("rejection_reason")]
        public string? RejectionReason { get; set; }
    }

    #endregion
}
