using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTok.ReportIntegratedAdAccounts;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.ReportIntegratedAdAccounts
{
    /// <summary>
    /// Service implementation cho báo cáo tích hợp AdAccount
    /// </summary>
    public class ReportIntegratedAdAccountAppService :
        CrudAppService<
            RawReportIntegratedAdAccountEntity,
            ReportIntegratedAdAccountDto,
            Guid,
            GetReportIntegratedAdAccountListDto,
            CreateReportIntegratedAdAccountDto,
            UpdateReportIntegratedAdAccountDto>,
        IReportIntegratedAdAccountAppService
    {
        private readonly IRawReportIntegratedAdAccountRepository _reportIntegratedAdAccountRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="repository">Repository</param>
        /// <param name="reportIntegratedAdAccountRepository">Report Integrated AdAccount Repository</param>
        public ReportIntegratedAdAccountAppService(
            IRepository<RawReportIntegratedAdAccountEntity, Guid> repository,
            IRawReportIntegratedAdAccountRepository reportIntegratedAdAccountRepository) : base(repository)
        {
            _reportIntegratedAdAccountRepository = reportIntegratedAdAccountRepository;

            // Cấu hình permission
            GetPolicyName = TikTokPermissions.ReportIntegratedAdAccounts.Default;
            GetListPolicyName = TikTokPermissions.ReportIntegratedAdAccounts.Default;
            CreatePolicyName = TikTokPermissions.ReportIntegratedAdAccounts.Create;
            UpdatePolicyName = TikTokPermissions.ReportIntegratedAdAccounts.Edit;
            DeletePolicyName = TikTokPermissions.ReportIntegratedAdAccounts.Delete;
        }

        /// <summary>
        /// Ghi đè phương thức GetListAsync để sử dụng repository
        /// </summary>
        /// <param name="input">Input để lấy danh sách</param>
        /// <returns>Danh sách báo cáo tích hợp AdAccount</returns>
        public override async Task<PagedResultDto<ReportIntegratedAdAccountDto>> GetListAsync(GetReportIntegratedAdAccountListDto input)
        {
            var totalCount = await _reportIntegratedAdAccountRepository.GetCountAsync(
                input.AdvertiserId,
                input.AdvertiserName,
                input.BcId,
                input.StartDate,
                input.EndDate,
                input.Filter);

            var items = await _reportIntegratedAdAccountRepository.GetListAsync(
                input.AdvertiserId,
                input.AdvertiserName,
                input.BcId,
                input.StartDate,
                input.EndDate,
                input.Sorting,
                input.MaxResultCount,
                input.SkipCount,
                input.Filter);

            return new PagedResultDto<ReportIntegratedAdAccountDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<RawReportIntegratedAdAccountEntity[], ReportIntegratedAdAccountDto[]>(items.ToArray())
            };
        }

        /// <summary>
        /// Lấy danh sách theo Advertiser IDs và khoảng thời gian
        /// </summary>
        /// <param name="advertiserIds">Danh sách ID tài khoản nhà quảng cáo</param>
        /// <param name="startDate">Ngày bắt đầu</param>
        /// <param name="endDate">Ngày kết thúc</param>
        /// <returns>Danh sách báo cáo tích hợp AdAccount</returns>
        public async Task<List<ReportIntegratedAdAccountDto>> GetByAdvertiserIdsAndDateRangeAsync(
            List<string> advertiserIds,
            DateTime startDate,
            DateTime endDate)
        {
            var items = await _reportIntegratedAdAccountRepository.GetByAdvertiserIdsAndDateRangeAsync(
                advertiserIds,
                startDate,
                endDate);

            return ObjectMapper.Map<List<RawReportIntegratedAdAccountEntity>, List<ReportIntegratedAdAccountDto>>(items);
        }

        /// <summary>
        /// Lấy báo cáo theo Advertiser ID và ngày
        /// </summary>
        /// <param name="advertiserId">ID tài khoản nhà quảng cáo</param>
        /// <param name="date">Ngày báo cáo</param>
        /// <returns>Báo cáo tích hợp AdAccount</returns>
        public async Task<ReportIntegratedAdAccountDto?> GetByAdvertiserIdAndDateAsync(string advertiserId, DateTime date)
        {
            var entity = await _reportIntegratedAdAccountRepository.GetByAdvertiserIdAndDateAsync(advertiserId, date);
            return entity != null ? ObjectMapper.Map<RawReportIntegratedAdAccountEntity, ReportIntegratedAdAccountDto>(entity) : null;
        }

        /// <summary>
        /// Lấy danh sách báo cáo theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID tài khoản nhà quảng cáo</param>
        /// <returns>Danh sách báo cáo tích hợp AdAccount</returns>
        public async Task<List<ReportIntegratedAdAccountDto>> GetByAdvertiserIdAsync(string advertiserId)
        {
            var items = await _reportIntegratedAdAccountRepository.GetByAdvertiserIdAsync(advertiserId);
            return ObjectMapper.Map<List<RawReportIntegratedAdAccountEntity>, List<ReportIntegratedAdAccountDto>>(items);
        }
    }
} 