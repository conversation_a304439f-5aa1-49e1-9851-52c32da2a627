/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Tools operations
    /// </summary>
    public class ToolsApi : IToolsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<ToolsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the ToolsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public ToolsApi(IApiClient apiClient, ILogger<ToolsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        // /// <summary>
        // /// Search for location targeting tags by keyword
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing search parameters</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing targeting tags</returns>
        // public async Task<Response<SearchTargetingResponse>> SearchTargetingAsync(
        //     string accessToken,
        //     SearchTargetingRequest body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrEmpty(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogDebug("Searching for location targeting tags for advertiser {AdvertiserId}", body.AdvertiserId);

        //     var parameters = new Dictionary<string, object>
        //     {
        //         ["advertiser_id"] = body.AdvertiserId,
        //         ["objective_type"] = body.ObjectiveType,
        //         ["placements"] = body.Placements,
        //         ["search_type"] = body.SearchType,
        //         ["keywords"] = body.Keywords
        //     };

        //     if (!string.IsNullOrEmpty(body.PromotionType))
        //         parameters["promotion_type"] = body.PromotionType;
        //     if (body.GeoTypes != null)
        //         parameters["geo_types"] = body.GeoTypes;
        //     if (body.RegionCodes != null)
        //         parameters["region_codes"] = body.RegionCodes;
        //     if (!string.IsNullOrEmpty(body.OperatingSystem))
        //         parameters["operating_system"] = body.OperatingSystem;
        //     if (!string.IsNullOrEmpty(body.BrandSafetyType))
        //         parameters["brand_safety_type"] = body.BrandSafetyType;
        //     if (!string.IsNullOrEmpty(body.BrandSafetyPartner))
        //         parameters["brand_safety_partner"] = body.BrandSafetyPartner;

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<SearchTargetingResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.SearchTargeting}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: parameters,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <summary>
        // /// Get information about location targeting tags by ID
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing targeting IDs</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing targeting tag information</returns>
        // public async Task<Response<GetTargetingInfoResponse>> GetTargetingInfoAsync(
        //     string accessToken,
        //     GetTargetingInfoRequest body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrEmpty(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogDebug("Getting targeting tag information for advertiser {AdvertiserId}", body.AdvertiserId);

        //     var parameters = new Dictionary<string, object>
        //     {
        //         ["advertiser_id"] = body.AdvertiserId,
        //         ["targeting_ids"] = body.TargetingIds
        //     };

        //     if (!string.IsNullOrEmpty(body.Scene))
        //         parameters["scene"] = body.Scene;
        //     if (!string.IsNullOrEmpty(body.ObjectiveType))
        //         parameters["objective_type"] = body.ObjectiveType;
        //     if (!string.IsNullOrEmpty(body.PromotionType))
        //         parameters["promotion_type"] = body.PromotionType;
        //     if (body.Placements != null)
        //         parameters["placements"] = body.Placements;
        //     if (!string.IsNullOrEmpty(body.OperatingSystem))
        //         parameters["operating_system"] = body.OperatingSystem;
        //     if (!string.IsNullOrEmpty(body.BrandSafetyType))
        //         parameters["brand_safety_type"] = body.BrandSafetyType;
        //     if (!string.IsNullOrEmpty(body.BrandSafetyPartner))
        //         parameters["brand_safety_partner"] = body.BrandSafetyPartner;

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<GetTargetingInfoResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetTargetingInfo}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: parameters,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get available locations by different settings
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="placements">The apps where you want to deliver your ads</param>
        /// <param name="objectiveType">Your objective type</param>
        /// <param name="levelRange">Location level you want to get</param>
        /// <param name="language">The language of the returned location name</param>
        /// <param name="shoppingAdsType">Shopping ads type</param>
        /// <param name="promotionType">Promotion type</param>
        /// <param name="promotionTargetType">The promotion type for Lead Generation objective</param>
        /// <param name="operatingSystem">Operating systems that you want to target</param>
        /// <param name="brandSafetyType">Brand safety type</param>
        /// <param name="brandSafetyPartner">Brand safety partner</param>
        /// <param name="rfCampaignType">Campaign type for RF_REACH objective</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing available locations</returns>
        public async Task<Response<GetRegionResponse>> GetRegionAsync(
            string accessToken,
            string advertiserId,
            List<string> placements,
            string objectiveType,
            string? levelRange = null,
            string? language = null,
            string? shoppingAdsType = null,
            string? promotionType = null,
            string? promotionTargetType = null,
            string? operatingSystem = null,
            string? brandSafetyType = null,
            string? brandSafetyPartner = null,
            string? rfCampaignType = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (placements == null || placements.Count == 0)
                throw new ArgumentException("Placements cannot be null or empty", nameof(placements));
            if (string.IsNullOrEmpty(objectiveType))
                throw new ArgumentException("Objective type cannot be null or empty", nameof(objectiveType));

            _logger?.LogDebug("Getting available locations for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["placements"] = placements,
                ["objective_type"] = objectiveType
            };

            if (!string.IsNullOrEmpty(levelRange))
                parameters["level_range"] = levelRange;
            if (!string.IsNullOrEmpty(language))
                parameters["language"] = language;
            if (!string.IsNullOrEmpty(shoppingAdsType))
                parameters["shopping_ads_type"] = shoppingAdsType;
            if (!string.IsNullOrEmpty(promotionType))
                parameters["promotion_type"] = promotionType;
            if (!string.IsNullOrEmpty(promotionTargetType))
                parameters["promotion_target_type"] = promotionTargetType;
            if (!string.IsNullOrEmpty(operatingSystem))
                parameters["operating_system"] = operatingSystem;
            if (!string.IsNullOrEmpty(brandSafetyType))
                parameters["brand_safety_type"] = brandSafetyType;
            if (!string.IsNullOrEmpty(brandSafetyPartner))
                parameters["brand_safety_partner"] = brandSafetyPartner;
            if (!string.IsNullOrEmpty(rfCampaignType))
                parameters["rf_campaign_type"] = rfCampaignType;

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<GetRegionResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetRegion}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get available locations by advertiser ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="language">The language you want the returned region name to be translated into</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing available locations</returns>
        public async Task<Response<SearchRegionResponse>> SearchRegionAsync(
            string accessToken,
            string advertiserId,
            string? language = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogDebug("Searching regions for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId
            };

            if (!string.IsNullOrEmpty(language))
                parameters["language"] = language;

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<SearchRegionResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.SearchRegion}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the enum values of language codes
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing language codes</returns>
        public async Task<Response<GetLanguageResponse>> GetLanguageAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogDebug("Getting languages for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<GetLanguageResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetLanguage}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Search for targeting categories and hashtags for interests and behaviors
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="targetingType">The primary targeting type</param>
        /// <param name="subTargetingTypes">The secondary targeting types</param>
        /// <param name="searchKeywords">A list of seed keywords</param>
        /// <param name="language">The language of the seed keywords</param>
        /// <param name="filtering">Filtering conditions</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing targeting categories and hashtags</returns>
        public async Task<Response<SearchInterestAndBehaviorResponse>> SearchInterestAndBehaviorAsync(
            string accessToken,
            string advertiserId,
            string targetingType,
            List<string>? subTargetingTypes = null,
            List<string>? searchKeywords = null,
            string? language = null,
            InterestBehaviorFiltering? filtering = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrEmpty(targetingType))
                throw new ArgumentException("Targeting type cannot be null or empty", nameof(targetingType));

            _logger?.LogDebug("Searching interest and behavior targeting for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["targeting_type"] = targetingType
            };

            if (subTargetingTypes != null)
                parameters["sub_targeting_types"] = subTargetingTypes;
            if (searchKeywords != null)
                parameters["search_keywords"] = searchKeywords;
            if (!string.IsNullOrEmpty(language))
                parameters["language"] = language;
            if (filtering != null)
                parameters["filtering"] = filtering;

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<SearchInterestAndBehaviorResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.SearchInterestAndBehavior}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get general interest category enumeration values
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="version">Version of interest category</param>
        /// <param name="language">Category name language in response</param>
        /// <param name="placements">The apps where you want to deliver your ads</param>
        /// <param name="specialIndustries">Special ad categories</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing interest categories</returns>
        public async Task<Response<GetInterestCategoryResponse>> GetInterestCategoryAsync(
            string accessToken,
            string advertiserId,
            int? version = null,
            string? language = null,
            List<string>? placements = null,
            List<string>? specialIndustries = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogDebug("Getting interest categories for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId
            };

            if (version.HasValue)
                parameters["version"] = version.Value;
            if (!string.IsNullOrEmpty(language))
                parameters["language"] = language;
            if (placements != null)
                parameters["placements"] = placements;
            if (specialIndustries != null)
                parameters["special_industries"] = specialIndustries;

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<GetInterestCategoryResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetInterestCategory}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Search for additional interest categories
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="keywords">A list of seed keywords</param>
        /// <param name="keyword">One seed keyword</param>
        /// <param name="mode">Search mode</param>
        /// <param name="language">Keyword language</param>
        /// <param name="limit">Number of additional interest categories you want to get</param>
        /// <param name="audienceType">Audience type</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing recommended keywords</returns>
        //public async Task<Response<RecommendInterestKeywordResponse>> RecommendInterestKeywordAsync(
        //    string accessToken,
        //    string advertiserId,
        //    List<string>? keywords = null,
        //    string? keyword = null,
        //    string? mode = null,
        //    string? language = null,
        //    int? limit = null,
        //    string? audienceType = null,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrEmpty(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (string.IsNullOrEmpty(advertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

        //    _logger?.LogDebug("Recommending interest keywords for advertiser {AdvertiserId}", advertiserId);

        //    var parameters = new Dictionary<string, object>
        //    {
        //        ["advertiser_id"] = advertiserId
        //    };

        //    if (keywords != null)
        //        parameters["keywords"] = keywords;
        //    if (!string.IsNullOrEmpty(keyword))
        //        parameters["keyword"] = keyword;
        //    if (!string.IsNullOrEmpty(mode))
        //        parameters["mode"] = mode;
        //    if (!string.IsNullOrEmpty(language))
        //        parameters["language"] = language;
        //    if (limit.HasValue)
        //        parameters["limit"] = limit.Value;
        //    if (!string.IsNullOrEmpty(audienceType))
        //        parameters["audience_type"] = audienceType;

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    var queryParams = new Dictionary<string, string>();
        //    foreach (var param in parameters)
        //    {
        //        if (param.Value != null)
        //        {
        //            queryParams[param.Key] = param.Value.ToString()!;
        //        }
        //    }

        //    return await _apiClient.CallApiAsync<Response<RecommendInterestKeywordResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.RecommendInterestKeyword}",
        //        HttpMethod.Get,
        //        queryParams: queryParams,
        //        body: null,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get additional interest categories by ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="keywordQuery">Information of the additional interest category you want to get</param>
        /// <param name="filtering">Filtering conditions</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing interest keywords</returns>
        public async Task<Response<GetInterestKeywordResponse>> GetInterestKeywordAsync(
            string accessToken,
            string advertiserId,
            List<InterestKeywordQuery> keywordQuery,
            InterestKeywordFiltering? filtering = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (keywordQuery == null || keywordQuery.Count == 0)
                throw new ArgumentException("Keyword query cannot be null or empty", nameof(keywordQuery));

            _logger?.LogDebug("Getting interest keywords for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["keyword_query"] = keywordQuery
            };

            if (filtering != null)
                parameters["filtering"] = filtering;

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<GetInterestKeywordResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetInterestKeyword}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get action category enumeration values
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="specialIndustries">Special ad categories</param>
        /// <param name="language">Category name language in response</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing action categories</returns>
        public async Task<Response<GetActionCategoryResponse>> GetActionCategoryAsync(
            string accessToken,
            string advertiserId,
            List<string>? specialIndustries = null,
            string? language = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogDebug("Getting action categories for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId
            };

            if (specialIndustries != null)
                parameters["special_industries"] = specialIndustries;
            if (!string.IsNullOrEmpty(language))
                parameters["language"] = language;

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<GetActionCategoryResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetActionCategory}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Search for targeting hashtags based on seed keywords
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="keywords">Keywords that you want to get recommended hashtags for</param>
        /// <param name="operatorType">The operator to be used between the keywords</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing recommended hashtags</returns>
        //public async Task<Response<RecommendHashtagResponse>> RecommendHashtagAsync(
        //    string accessToken,
        //    string advertiserId,
        //    List<string> keywords,
        //    string? operatorType = null,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrEmpty(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (string.IsNullOrEmpty(advertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
        //    if (keywords?.Any() != true)
        //        throw new ArgumentException("Keywords cannot be null or empty", nameof(keywords));

        //    _logger?.LogDebug("Recommending hashtags for advertiser {AdvertiserId}", advertiserId);

        //    var parameters = new Dictionary<string, object>
        //    {
        //        ["advertiser_id"] = advertiserId,
        //        ["keywords"] = keywords
        //    };

        //    if (!string.IsNullOrEmpty(operatorType))
        //        parameters["operator"] = operatorType;

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    var queryParams = new Dictionary<string, string>();
        //    foreach (var param in parameters)
        //    {
        //        if (param.Value != null)
        //        {
        //            queryParams[param.Key] = param.Value.ToString()!;
        //        }
        //    }

        //    return await _apiClient.CallApiAsync<Response<RecommendHashtagResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.RecommendHashtag}",
        //        HttpMethod.Get,
        //        queryParams: queryParams,
        //        body: null,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get the targeting hashtag names and statuses by ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="keywordIds">List of keyword IDs</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing hashtag information</returns>
        public async Task<Response<GetHashtagResponse>> GetHashtagAsync(
            string accessToken,
            string advertiserId,
            List<string> keywordIds,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (keywordIds?.Any() != true)
                throw new ArgumentException("Keyword IDs cannot be null or empty", nameof(keywordIds));

            _logger?.LogDebug("Getting hashtags for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["keyword_ids"] = keywordIds
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<GetHashtagResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetHashtag}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get recommended interest and action categories based on historical performance data
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing advertiser ID, region codes, and optional app ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing recommended categories</returns>
        // public async Task<Response<RecommendTargetingCategoryResponse>> RecommendTargetingCategoryAsync(
        //     string accessToken,
        //     RecommendTargetingCategoryRequest body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrEmpty(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));
        //     if (string.IsNullOrEmpty(body.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(body));
        //     if (body.RegionCodes?.Any() != true)
        //         throw new ArgumentException("Region codes cannot be null or empty", nameof(body));

        //     _logger?.LogDebug("Getting recommended targeting categories for advertiser {AdvertiserId}", body.AdvertiserId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken,
        //         ["Content-Type"] = "application/json"
        //     };

        //     return await _apiClient.CallApiAsync<Response<RecommendTargetingCategoryResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.RecommendTargetingCategory}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get the enumeration values of operating system version
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="osType">OS type</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing OS versions</returns>
        public async Task<Response<GetOsVersionResponse>> GetOsVersionAsync(
            string accessToken,
            string advertiserId,
            string osType,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrEmpty(osType))
                throw new ArgumentException("OS type cannot be null or empty", nameof(osType));

            _logger?.LogDebug("Getting OS versions for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["os_type"] = osType
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<GetOsVersionResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetOsVersion}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get device model enumeration values
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing device models</returns>
        public async Task<Response<GetDeviceModelResponse>> GetDeviceModelAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogDebug("Getting device models for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<GetDeviceModelResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetDeviceModel}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the carrier enumeration values that represents carriers in different countries or locations
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing carriers</returns>
        public async Task<Response<GetCarrierResponse>> GetCarrierAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogDebug("Getting carriers for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<GetCarrierResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetCarrier}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get targeting tags (ISP IDs)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="locationIds">IDs of the locations that you want to get the supported ISP IDs for</param>
        /// <param name="scene">The targeting type that the targeting tags are used for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing targeting tags</returns>
        public async Task<Response<GetTargetingListResponse>> GetTargetingListAsync(
            string accessToken,
            string advertiserId,
            List<string> locationIds,
            string scene,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (locationIds?.Count == 0)
                throw new ArgumentException("Location IDs cannot be null or empty", nameof(locationIds));
            if (string.IsNullOrEmpty(scene))
                throw new ArgumentException("Scene cannot be null or empty", nameof(scene));

            _logger?.LogDebug("Getting targeting list for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["location_ids"] = locationIds,
                ["scene"] = scene
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken,
                ["Content-Type"] = "application/json"
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<GetTargetingListResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetTargetingList}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the contextual tags that you can target
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="objectiveType">Advertising objective</param>
        /// <param name="regionCodes">Country or region codes</param>
        /// <param name="brandSafetyType">Brand safety type</param>
        /// <param name="rfCampaignType">Campaign type for RF_REACH objective</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing contextual tags</returns>
        public async Task<Response<GetContextualTagResponse>> GetContextualTagAsync(
            string accessToken,
            string advertiserId,
            string objectiveType,
            List<string>? regionCodes = null,
            string? brandSafetyType = null,
            string? rfCampaignType = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrEmpty(objectiveType))
                throw new ArgumentException("Objective type cannot be null or empty", nameof(objectiveType));

            _logger?.LogDebug("Getting contextual tags for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["objective_type"] = objectiveType
            };

            if (regionCodes != null)
                parameters["region_codes"] = regionCodes;
            if (!string.IsNullOrEmpty(brandSafetyType))
                parameters["brand_safety_type"] = brandSafetyType;
            if (!string.IsNullOrEmpty(rfCampaignType))
                parameters["rf_campaign_type"] = rfCampaignType;

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<GetContextualTagResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetContextualTag}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the detailed information of contextual tags
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="contextualTagIds">Contextual tag IDs</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing contextual tag details</returns>
        public async Task<Response<GetContextualTagInfoResponse>> GetContextualTagInfoAsync(
            string accessToken,
            string advertiserId,
            List<string> contextualTagIds,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (contextualTagIds?.Count == 0)
                throw new ArgumentException("Contextual tag IDs cannot be null or empty", nameof(contextualTagIds));

            _logger?.LogDebug("Getting contextual tag info for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["contextual_tag_ids"] = contextualTagIds
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<GetContextualTagInfoResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetContextualTagInfo}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get region calling codes and region codes for phone numbers
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing phone region codes</returns>
        public async Task<Response<PhoneRegionCodeResponse>> GetPhoneRegionCodeAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogDebug("Getting phone region codes for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<PhoneRegionCodeResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetPhoneRegionCode}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get time zone enumeration values, and their offsets from GMT
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing time zones</returns>
        public async Task<Response<TimezoneResponse>> GetTimezoneAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogDebug("Getting timezones for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<TimezoneResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetTimezone}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the corresponding TikTok in-app link for an open TikTok URL
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="url">Open URL that you want to get the internal link for</param>
        /// <param name="urlType">Type of the open TikTok URL</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TikTok in-app link</returns>
        public async Task<Response<OpenUrlResponse>> GetOpenUrlAsync(
            string accessToken,
            string advertiserId,
            string url,
            string urlType,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrEmpty(url))
                throw new ArgumentException("URL cannot be null or empty", nameof(url));
            if (string.IsNullOrEmpty(urlType))
                throw new ArgumentException("URL type cannot be null or empty", nameof(urlType));

            _logger?.LogDebug("Getting open URL for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["url"] = url,
                ["url_type"] = urlType
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<OpenUrlResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetOpenUrl}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get content categories or vertical sensitivity categories that can be excluded from appearing next to your ads
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="objectiveType">Advertising objective</param>
        /// <param name="brandSafetyType">Brand safety type</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing content exclusion categories</returns>
        public async Task<Response<ContentExclusionResponse>> GetContentExclusionAsync(
            string accessToken,
            string advertiserId,
            string objectiveType,
            string? brandSafetyType = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrEmpty(objectiveType))
                throw new ArgumentException("Objective type cannot be null or empty", nameof(objectiveType));

            _logger?.LogDebug("Getting content exclusion categories for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["objective_type"] = objectiveType
            };

            if (!string.IsNullOrEmpty(brandSafetyType))
                parameters["brand_safety_type"] = brandSafetyType;

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<ContentExclusionResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetContentExclusion}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the detailed information of content categories or vertical categories that can be excluded from appearing next to your ads
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="categoryIds">IDs of the content exclusion categories or vertical sensitivity categories</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing content exclusion category details</returns>
        public async Task<Response<ContentExclusionInfoResponse>> GetContentExclusionInfoAsync(
            string accessToken,
            string advertiserId,
            List<string> categoryIds,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (categoryIds == null || categoryIds.Count == 0)
                throw new ArgumentException("Category IDs cannot be null or empty", nameof(categoryIds));

            _logger?.LogDebug("Getting content exclusion info for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["category_ids"] = categoryIds
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<ContentExclusionInfoResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetContentExclusionInfo}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get a suggested bid value for your ad group based on basic campaign and ad group settings
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing bid recommendation parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing suggested bid</returns>
        // public async Task<Response<BidRecommendResponse>> RecommendBidAsync(
        //     string accessToken,
        //     BidRecommendRequest body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrEmpty(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogDebug("Getting bid recommendation for advertiser {AdvertiserId}", body.AdvertiserId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken,
        //         ["Content-Type"] = "application/json"
        //     };

        //     return await _apiClient.CallApiAsync<Response<BidRecommendResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.RecommendBid}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Check whether certain settings are eligible for Value-Based Optimization (VBO), and get the available VBO bidding strategies
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="objectiveType">Advertising objective</param>
        /// <param name="promotionType">Promotion type</param>
        /// <param name="placements">The apps where you want to deliver your ads</param>
        /// <param name="appPromotionType">App promotion type (required when objective_type is APP_PROMOTION)</param>
        /// <param name="campaignType">Campaign type</param>
        /// <param name="isAdvancedDedicatedCampaign">Whether the campaign is an Advanced Dedicated Campaign</param>
        /// <param name="disableSkanCampaign">Whether to disable SKAN (SKAdNetwork) attribution</param>
        /// <param name="bidAlignType">The attribution type for the Dedicated Campaign</param>
        /// <param name="ios14QuotaType">Whether the campaign will be counted towards the iOS 14 Dedicated Campaign quota</param>
        /// <param name="campaignAppProfilePageState">Whether to use App Profile Page at the campaign level to optimize delivery</param>
        /// <param name="pixelId">Pixel ID</param>
        /// <param name="appId">The Application ID of the promoted app</param>
        /// <param name="optimizationEvent">Conversion event for the ad group</param>
        /// <param name="storeId">ID of the TikTok Shop</param>
        /// <param name="isSmartPerformanceCampaign">Whether the campaign is a Smart+ Campaign or not</param>
        /// <param name="budgetOptimizeOn">Whether to enable Campaign Budget Optimization (CBO)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing VBO status information</returns>
        public async Task<Response<VboStatusResponse>> GetVboStatusAsync(
            string accessToken,
            string advertiserId,
            string objectiveType,
            string promotionType,
            List<string> placements,
            string? appPromotionType = null,
            string? campaignType = null,
            bool? isAdvancedDedicatedCampaign = null,
            bool? disableSkanCampaign = null,
            string? bidAlignType = null,
            string? ios14QuotaType = null,
            string? campaignAppProfilePageState = null,
            string? pixelId = null,
            string? appId = null,
            string? optimizationEvent = null,
            string? storeId = null,
            bool? isSmartPerformanceCampaign = null,
            bool? budgetOptimizeOn = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrEmpty(objectiveType))
                throw new ArgumentException("Objective type cannot be null or empty", nameof(objectiveType));
            if (string.IsNullOrEmpty(promotionType))
                throw new ArgumentException("Promotion type cannot be null or empty", nameof(promotionType));
            if (placements == null || placements.Count == 0)
                throw new ArgumentException("Placements cannot be null or empty", nameof(placements));

            _logger?.LogDebug("Getting VBO status for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["objective_type"] = objectiveType,
                ["promotion_type"] = promotionType,
                ["placements"] = placements
            };

            if (!string.IsNullOrEmpty(appPromotionType))
                parameters["app_promotion_type"] = appPromotionType;
            if (!string.IsNullOrEmpty(campaignType))
                parameters["campaign_type"] = campaignType;
            if (isAdvancedDedicatedCampaign.HasValue)
                parameters["is_advanced_dedicated_campaign"] = isAdvancedDedicatedCampaign.Value;
            if (disableSkanCampaign.HasValue)
                parameters["disable_skan_campaign"] = disableSkanCampaign.Value;
            if (!string.IsNullOrEmpty(bidAlignType))
                parameters["bid_align_type"] = bidAlignType;
            if (!string.IsNullOrEmpty(ios14QuotaType))
                parameters["ios14_quota_type"] = ios14QuotaType;
            if (!string.IsNullOrEmpty(campaignAppProfilePageState))
                parameters["campaign_app_profile_page_state"] = campaignAppProfilePageState;
            if (!string.IsNullOrEmpty(pixelId))
                parameters["pixel_id"] = pixelId;
            if (!string.IsNullOrEmpty(appId))
                parameters["app_id"] = appId;
            if (!string.IsNullOrEmpty(optimizationEvent))
                parameters["optimization_event"] = optimizationEvent;
            if (!string.IsNullOrEmpty(storeId))
                parameters["store_id"] = storeId;
            if (isSmartPerformanceCampaign.HasValue)
                parameters["is_smart_performance_campaign"] = isSmartPerformanceCampaign.Value;
            if (budgetOptimizeOn.HasValue)
                parameters["budget_optimize_on"] = budgetOptimizeOn.Value;

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<VboStatusResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetVboStatus}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the authorization status of your Brand Safety partner
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="partner">Brand Safety post bid measurement partner</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing brand safety partner status</returns>
        public async Task<Response<BrandSafetyPartnerStatusResponse>> GetBrandSafetyPartnerStatusAsync(
            string accessToken,
            string advertiserId,
            string partner,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrEmpty(partner))
                throw new ArgumentException("Partner cannot be null or empty", nameof(partner));

            _logger?.LogDebug("Getting brand safety partner status for advertiser {AdvertiserId}", advertiserId);

            var parameters = new Dictionary<string, object>
            {
                ["advertiser_id"] = advertiserId,
                ["partner"] = partner
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>();
            foreach (var param in parameters)
            {
                if (param.Value != null)
                {
                    queryParams[param.Key] = param.Value.ToString()!;
                }
            }

            return await _apiClient.CallApiAsync<Response<BrandSafetyPartnerStatusResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.GetBrandSafetyPartnerStatus}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Check whether a URL is a custom URL scheme, Apple's universal link, or Android App Link, and whether the URL is valid
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="url">The URL that you want to get verification results for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing URL validation results</returns>
        //public async Task<Response<UrlValidationResponse>> ValidateUrlAsync(
        //    string accessToken,
        //    string advertiserId,
        //    string url,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrEmpty(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (string.IsNullOrEmpty(advertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
        //    if (string.IsNullOrEmpty(url))
        //        throw new ArgumentException("URL cannot be null or empty", nameof(url));

        //    _logger?.LogDebug("Validating URL for advertiser {AdvertiserId}", advertiserId);

        //    var parameters = new Dictionary<string, object>
        //    {
        //        ["advertiser_id"] = advertiserId,
        //        ["url"] = url
        //    };

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    var queryParams = new Dictionary<string, string>();
        //    foreach (var param in parameters)
        //    {
        //        if (param.Value != null)
        //        {
        //            queryParams[param.Key] = param.Value.ToString()!;
        //        }
        //    }

        //    return await _apiClient.CallApiAsync<Response<UrlValidationResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ToolsEndpoints.ValidateUrl}",
        //        HttpMethod.Get,
        //        queryParams: queryParams,
        //        body: null,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}
    }
}
