using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu quản lý Business Center
    /// </summary>
    public class BcManagementSyncService : BaseSyncService, IBcManagementSyncService
    {
        private readonly IBusinessCenterRepository _businessCenterRepository;
        private readonly IRepository<RawBusinessCenterEntity, Guid> _businessCenterEntityRepository;

        public BcManagementSyncService(
            IServiceProvider serviceProvider,
            IBusinessCenterRepository businessCenterRepository,
            IRepository<RawBusinessCenterEntity, Guid> businessCenterEntityRepository,
            ILogger<BcManagementSyncService> logger) : base(serviceProvider, logger)
        {
            _businessCenterRepository = businessCenterRepository;
            _businessCenterEntityRepository = businessCenterEntityRepository;
        }

        /// <summary>
        /// Đồng bộ Business Center theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BcManagementSyncResult> SyncBusinessCenterAsync(string bcId)
        {
            var result = new BcManagementSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ Business Center: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // 3. Đồng bộ Business Center
                var bcResult = await SyncBusinessCenterFromApiAsync(tikTokClient, bcId);
                result.TotalSynced = bcResult.TotalSynced;
                result.NewRecords = bcResult.NewRecords;
                result.UpdatedRecords = bcResult.UpdatedRecords;
                result.BcCount = bcResult.BcCount;

                _logger.LogDebug("Hoàn thành đồng bộ Business Center: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ Business Center: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ Business Center: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ Business Center: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BcManagementSyncResult> SyncAllBusinessCentersAsync()
        {
            var result = new BcManagementSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả Business Centers");

                // 1. Lấy tất cả Business Centers có ứng dụng active
                var allBusinessApplications = await _businessApplicationCache.GetAllActiveAsync();
                var bcGroups = allBusinessApplications.GroupBy(x => x.BcId);

                foreach (var bcGroup in bcGroups)
                {
                    var bcId = bcGroup.Key;
                    var activeApplication = bcGroup.FirstOrDefault(x => x.IsActive);

                    if (activeApplication == null || string.IsNullOrEmpty(activeApplication.AccessToken))
                    {
                        _logger.LogWarning("Bỏ qua BC {BcId} - không có ứng dụng active hoặc access token", bcId);
                        continue;
                    }

                    try
                    {
                        // 2. Tạo TikTok client cho từng BC
                        using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                        // 3. Đồng bộ Business Center cho BC này
                        var bcResult = await SyncBusinessCenterFromApiAsync(tikTokClient, bcId);
                        result.TotalSynced += bcResult.TotalSynced;
                        result.NewRecords += bcResult.NewRecords;
                        result.UpdatedRecords += bcResult.UpdatedRecords;
                        result.BcCount += bcResult.BcCount;

                        _logger.LogDebug("Hoàn thành đồng bộ Business Center: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                            bcId, bcResult.TotalSynced, bcResult.NewRecords, bcResult.UpdatedRecords);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ Business Center: {BcId}", bcId);
                        // Tiếp tục với BC tiếp theo
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ tất cả Business Centers. Tổng BC: {BcCount}, Tổng bản ghi: {Total}",
                    result.BcCount, result.TotalSynced);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tất cả Business Centers: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả Business Centers");
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ changelog của Business Center
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate">Ngày bắt đầu (optional)</param>
        /// <param name="endDate">Ngày kết thúc (optional)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BcManagementSyncResult> SyncBusinessCenterChangelogAsync(string bcId, string? startDate = null, string? endDate = null)
        {
            var result = new BcManagementSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ changelog Business Center: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // 3. Đồng bộ changelog
                var changelogResult = await SyncBusinessCenterChangelogFromApiAsync(tikTokClient, bcId, startDate, endDate);
                result.TotalSynced = changelogResult.TotalSynced;
                result.NewRecords = changelogResult.NewRecords;
                result.UpdatedRecords = changelogResult.UpdatedRecords;
                result.ChangelogCount = changelogResult.ChangelogCount;

                _logger.LogDebug("Hoàn thành đồng bộ changelog Business Center: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ changelog Business Center: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ changelog Business Center: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ changelog Business Center: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ Business Center từ API theo BC ID
        /// </summary>
        /// <param name="client">TikTok client</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        private async Task<BcManagementSyncResult> SyncBusinessCenterFromApiAsync(TikTokBusinessApiClient client, string bcId)
        {
            var result = new BcManagementSyncResult();

            try
            {
                // 1. Lấy danh sách Business Centers
                var businessCentersResponse = await client.BcManagement.GetBusinessCentersAsync(bcId);

                if (!TikTokApiCodes.IsSuccess(businessCentersResponse.Code))
                {
                    throw new BusinessException(businessCentersResponse.Code.ToString(), string.Format("Lỗi khi đồng bộ Business Center từ API cho BC: {0}", bcId));
                }

                if (businessCentersResponse.Data?.List == null || businessCentersResponse.Data.List.Count == 0)
                {
                    _logger.LogWarning("Không có Business Center nào được trả về cho BC: {BcId}", bcId);
                    return result;
                }

                // 2. Đồng bộ từng Business Center
                using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
                {
                    foreach (var bcInfo in businessCentersResponse.Data.List)
                    {
                        if (bcInfo.BcInfo != null)
                        {
                            await SyncBusinessCenterEntityAsync(bcInfo, result);
                        }
                    }
                    await uow.CompleteAsync();
                }

                result.BcCount = businessCentersResponse.Data.List.Count;
            }
            catch (BusinessException ex)
            {
                _logger.LogError(ex, ex.Message);
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ changelog Business Center từ API
        /// </summary>
        /// <param name="client">TikTok client</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate">Ngày bắt đầu</param>
        /// <param name="endDate">Ngày kết thúc</param>
        /// <returns>Kết quả đồng bộ</returns>
        private async Task<BcManagementSyncResult> SyncBusinessCenterChangelogFromApiAsync(TikTokBusinessApiClient client, string bcId, string? startDate = null, string? endDate = null)
        {
            var result = new BcManagementSyncResult();

            try
            {
                // 1. Tạo request cho changelog
                var changelogRequest = new GetBusinessCenterChangelogRequest
                {
                    BcId = bcId,
                    Page = 1,
                    PageSize = 50
                };

                if (!string.IsNullOrEmpty(startDate) || !string.IsNullOrEmpty(endDate))
                {
                    changelogRequest.Filtering = new ChangelogFiltering
                    {
                        StartDate = startDate,
                        EndDate = endDate
                    };
                }

                // 2. Lấy changelog
                var changelogResponse = await client.BcManagement.GetBusinessCenterChangelogAsync(changelogRequest);
                if (!TikTokApiCodes.IsSuccess(changelogResponse.Code))
                {
                    throw new BusinessException(changelogResponse.Code.ToString(), string.Format("Lỗi khi đồng bộ changelog từ API cho BC: {0}", bcId));
                }

                if (changelogResponse.Data?.ChangelogList == null || changelogResponse.Data.ChangelogList.Count == 0)
                {
                    _logger.LogWarning("Không có changelog nào được trả về cho BC: {BcId}", bcId);
                    return result;
                }

                // 3. Xử lý changelog (có thể lưu vào bảng riêng hoặc cập nhật Business Center)
                using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);
                // TODO: Implement changelog processing logic
                // Có thể tạo entity riêng cho changelog hoặc cập nhật Business Center dựa trên changelog
                result.ChangelogCount = changelogResponse.Data.ChangelogList.Count;
                result.TotalSynced = changelogResponse.Data.ChangelogList.Count;
                await uow.CompleteAsync();
            }
            catch (BusinessException ex)
            {
                _logger.LogError(ex, ex.Message);
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ entity Business Center
        /// </summary>
        /// <param name="bcInfo">Dữ liệu từ API</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task SyncBusinessCenterEntityAsync(BusinessCenterInfo bcInfo, BcManagementSyncResult result)
        {
            if (bcInfo.BcInfo == null || string.IsNullOrEmpty(bcInfo.BcInfo.BcId))
            {
                _logger.LogWarning("Bỏ qua Business Center - BcInfo hoặc BcId rỗng");
                return;
            }

            var existingEntity = await _businessCenterRepository.GetByBcIdAsync(bcInfo.BcInfo.BcId);
            // Tạo mới
            var newEntity = MapApiBusinessCenterToEntity(bcInfo);

            if (existingEntity == null)
            {
                await _businessCenterEntityRepository.InsertAsync(newEntity);
                result.NewRecords++;
            }
            else
            {
                if (existingEntity.HasIsChanged(newEntity))
                {
                    // Cập nhật
                    UpdateBusinessCenterFromApi(existingEntity, bcInfo);
                    await _businessCenterEntityRepository.UpdateAsync(existingEntity);
                    result.UpdatedRecords++;
                }
            }

            result.TotalSynced++;
        }

        /// <summary>
        /// Map dữ liệu từ API sang entity
        /// </summary>
        /// <param name="bcInfo">Dữ liệu từ API</param>
        /// <returns>Entity Business Center</returns>
        private RawBusinessCenterEntity MapApiBusinessCenterToEntity(BusinessCenterInfo bcInfo)
        {
            var bc = bcInfo.BcInfo;
            return new RawBusinessCenterEntity(Guid.NewGuid())
            {
                BcId = bc.BcId ?? string.Empty,
                Name = bc.Name ?? string.Empty,
                Company = bc.Company ?? string.Empty,
                Currency = bc.Currency ?? string.Empty,
                RegisteredArea = bc.RegisteredArea ?? string.Empty,
                Status = ParseBusinessCenterStatus(bc.Status),
                Timezone = bc.Timezone ?? string.Empty,
                Type = ParseBusinessCenterType(bc.Type),
                UserRole = ParseUserRole(bcInfo.UserRole),
                ExtUserFinanceRole = ParseExtUserFinanceRole(bcInfo.ExtUserRole),
            };
        }

        /// <summary>
        /// Cập nhật entity từ dữ liệu API
        /// </summary>
        /// <param name="entity">Entity hiện tại</param>
        /// <param name="bcInfo">Dữ liệu từ API</param>
        private void UpdateBusinessCenterFromApi(RawBusinessCenterEntity entity, BusinessCenterInfo bcInfo)
        {
            var bc = bcInfo.BcInfo;
            entity.Name = bc.Name ?? entity.Name;
            entity.Company = bc.Company ?? entity.Company;
            entity.Currency = bc.Currency ?? entity.Currency;
            entity.RegisteredArea = bc.RegisteredArea ?? entity.RegisteredArea;
            entity.Status = ParseBusinessCenterStatus(bc.Status);
            entity.Timezone = bc.Timezone ?? entity.Timezone;
            entity.Type = ParseBusinessCenterType(bc.Type);
            entity.UserRole = ParseUserRole(bcInfo.UserRole);
            entity.ExtUserFinanceRole = ParseExtUserFinanceRole(bcInfo.ExtUserRole);
        }

        /// <summary>
        /// Parse trạng thái Business Center
        /// </summary>
        /// <param name="status">Trạng thái từ API</param>
        /// <returns>Enum trạng thái</returns>
        private BusinessCenterStatus ParseBusinessCenterStatus(string? status)
        {
            if (string.IsNullOrEmpty(status))
                return BusinessCenterStatus.ENABLE;

            if (Enum.TryParse<BusinessCenterStatus>(status, out var businessCenterStatus))
                return businessCenterStatus;

            return BusinessCenterStatus.ENABLE;
        }

        /// <summary>
        /// Parse loại Business Center
        /// </summary>
        /// <param name="type">Loại từ API</param>
        /// <returns>Enum loại</returns>
        private BusinessCenterType ParseBusinessCenterType(string? type)
        {
            if (string.IsNullOrEmpty(type))
                return BusinessCenterType.NORMAL;

            if (Enum.TryParse<BusinessCenterType>(type, out var businessCenterType))
                return businessCenterType;

            return BusinessCenterType.NORMAL;
        }

        /// <summary>
        /// Parse vai trò người dùng
        /// </summary>
        /// <param name="userRole">Vai trò từ API</param>
        /// <returns>Enum vai trò</returns>
        private Enums.UserRole ParseUserRole(string? userRole)
        {
            if (string.IsNullOrEmpty(userRole))
                return Enums.UserRole.STANDARD;

            if (Enum.TryParse<Enums.UserRole>(userRole, out var userRoleEnum))
                return userRoleEnum;

            return Enums.UserRole.STANDARD;
        }

        /// <summary>
        /// Parse vai trò tài chính mở rộng
        /// </summary>
        /// <param name="extUserRole">Vai trò mở rộng từ API</param>
        /// <returns>Enum vai trò tài chính mở rộng</returns>
        private ExtUserFinanceRole? ParseExtUserFinanceRole(ExtendedUserRole? extUserRole)
        {
            if (extUserRole == null)
                return null;

            if (Enum.TryParse<ExtUserFinanceRole>(extUserRole.FinanceRole, out var financeRole))
                return financeRole;

            return null;
        }
    }
}