/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Catalog Diagnostics endpoints
    /// </summary>
    public static class CatalogDiagnosticsEndpoints
    {
        /// <summary>
        /// Get synchronous catalog product diagnostic information endpoint (v1.3)
        /// </summary>
        public const string GetCatalogDiagnostics = "/v1.3/diagnostic/catalog/";

        /// <summary>
        /// Create an asynchronous download task for catalog product diagnostic information endpoint (v1.3)
        /// </summary>
        public const string CreateDiagnosticTask = "/v1.3/diagnostic/catalog/product/task/create/";

        /// <summary>
        /// Download asynchronous catalog product diagnostic information endpoint (v1.3)
        /// </summary>
        public const string GetDiagnosticTask = "/v1.3/diagnostic/catalog/product/task/get/";

        /// <summary>
        /// Get catalog event source diagnostic information endpoint (v1.3)
        /// </summary>
        public const string GetEventSourceIssues = "/v1.3/diagnostic/catalog/eventsource/issue/";

        /// <summary>
        /// Get catalog event trends and match rate endpoint (v1.3)
        /// </summary>
        public const string GetEventSourceMetrics = "/v1.3/diagnostic/catalog/eventsource/metric/";
    }
}
