using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu báo cáo GMV Max Live Campaign
    /// </summary>
    public class GmvMaxLiveCampaignSyncApplicationAppService : ApplicationService, IGmvMaxLiveCampaignSyncApplicationAppService
    {
        private readonly IGmvMaxLiveCampaignSyncService _gmvMaxLiveCampaignSyncService;

        public GmvMaxLiveCampaignSyncApplicationAppService(
            IGmvMaxLiveCampaignSyncService gmvMaxLiveCampaignSyncService)
        {
            _gmvMaxLiveCampaignSyncService = gmvMaxLiveCampaignSyncService;
        }

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Campaign theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> b<PERSON><PERSON> đầ<PERSON> (tùy chọn)</param>
        /// <param name="endDate">Ngày kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxLiveCampaignSyncResult> SyncGmvMaxLiveCampaignAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _gmvMaxLiveCampaignSyncService.SyncGmvMaxLiveCampaignAsync(bcId, startDate, endDate);
        }

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxLiveCampaignSyncResult> SyncAllGmvMaxLiveCampaignForAllBcsAsync()
        {
            return await _gmvMaxLiveCampaignSyncService.SyncAllGmvMaxLiveCampaignForAllBcsAsync();
        }
    }
}
