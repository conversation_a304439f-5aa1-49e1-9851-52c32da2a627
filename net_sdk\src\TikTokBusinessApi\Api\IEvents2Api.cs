/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Events 2.0 operations
    /// Events API 2.0 provides a unified endpoint for reporting App, Web, Offline, and CRM events
    /// </summary>
    public interface IEvents2Api
    {
        /// <summary>
        /// Report App, Web, Offline, or CRM events using Events API 2.0
        /// This is the unified endpoint that supports all event types through a single API call
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Events 2.0 track request containing event source, source ID, and event data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure with detailed error information</returns>
        /// <remarks>
        /// Events API 2.0 supports:
        /// - Web events (event_source = "web") measured by Pixel Code
        /// - App events (event_source = "app") measured by TikTok App ID (allowlist-only feature)
        /// - Offline events (event_source = "offline") measured by Offline Event Set ID
        /// - CRM events (event_source = "crm") measured by CRM Event Set ID
        /// 
        /// You can report up to 1,000 events in a single request for batch processing.
        /// For optimal campaign performance, it's recommended to send events in real-time.
        /// </remarks>
        //Task<Response<Events2TrackResponse>> TrackEventsAsync(
        //    string accessToken,
        //    Events2TrackRequest request,
        //    CancellationToken cancellationToken = default);

        #region Convenience Methods for Specific Event Types

        /// <summary>
        /// Report web events using Events API 2.0
        /// Convenience method for reporting web events measured by Pixel Code
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="pixelCode">Pixel Code for measuring web events</param>
        /// <param name="eventData">Web event data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<Events2TrackResponse>> TrackWebEventsAsync(
        //    string accessToken,
        //    string pixelCode,
        //    Events2EventData[] eventData,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Report app events using Events API 2.0
        /// Convenience method for reporting app events measured by TikTok App ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="tikTokAppId">TikTok App ID for measuring app events</param>
        /// <param name="eventData">App event data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        /// <remarks>
        /// Note: Reporting App Events using Events API 2.0 is currently an allowlist-only feature.
        /// Contact your TikTok representative to access this feature.
        /// </remarks>
        //Task<Response<Events2TrackResponse>> TrackAppEventsAsync(
        //    string accessToken,
        //    string tikTokAppId,
        //    Events2EventData[] eventData,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Report offline events using Events API 2.0
        /// Convenience method for reporting offline events measured by Offline Event Set ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="offlineEventSetId">Offline Event Set ID for measuring offline events</param>
        /// <param name="eventData">Offline event data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<Events2TrackResponse>> TrackOfflineEventsAsync(
        //    string accessToken,
        //    string offlineEventSetId,
        //    Events2EventData[] eventData,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Report CRM events using Events API 2.0
        /// Convenience method for reporting CRM events measured by CRM Event Set ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="crmEventSetId">CRM Event Set ID for measuring CRM events</param>
        /// <param name="eventData">CRM event data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<Events2TrackResponse>> TrackCrmEventsAsync(
        //    string accessToken,
        //    string crmEventSetId,
        //    Events2EventData[] eventData,
        //    CancellationToken cancellationToken = default);

        #endregion
    }
}
