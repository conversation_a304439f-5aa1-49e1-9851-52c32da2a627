/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Negative Keywords endpoints
    /// </summary>
    public static class NegativeKeywordsEndpoints
    {
        /// <summary>
        /// Get negative keywords endpoint (v1.3)
        /// </summary>
        public const string GetNegativeKeywords = "/search_ad/negative_keyword/get/";

        /// <summary>
        /// Create negative keywords endpoint (v1.3)
        /// </summary>
        public const string CreateNegativeKeywords = "/search_ad/negative_keyword/add/";

        /// <summary>
        /// Update negative keyword endpoint (v1.3)
        /// </summary>
        public const string UpdateNegativeKeyword = "/search_ad/negative_keyword/update/";

        /// <summary>
        /// Delete negative keywords endpoint (v1.3)
        /// </summary>
        public const string DeleteNegativeKeywords = "/search_ad/negative_keyword/delete/";

        /// <summary>
        /// Download negative keywords endpoint (v1.3)
        /// </summary>
        public const string DownloadNegativeKeywords = "/search_ad/negative_keyword/download/";
    }
}
