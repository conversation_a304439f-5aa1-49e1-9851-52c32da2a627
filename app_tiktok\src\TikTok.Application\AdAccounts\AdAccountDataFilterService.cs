﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static TikTok.Permissions.TikTokPermissions;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;
using TikTok.Entities.AdAccounts;
using System.Threading;

namespace TikTok.AdAccounts
{
    public class AdAccountDataFilterService : IAdAccountDataFilterService, ISingletonDependency
    {
        private static readonly ThreadLocal<List<Guid>> _currentSupporterIds = new ThreadLocal<List<Guid>>(() => new List<Guid>());
        private static readonly ThreadLocal<bool> _isFilterDisabled = new ThreadLocal<bool>(() => false);

        public Task SetCurrentSupporterAsync(Guid supporterId)
        {
            return SetMultipleSupportersAsync(new List<Guid> { supporterId });
        }

        public Task SetMultipleSupportersAsync(List<Guid> supporterIds)
        {
            _currentSupporterIds.Value = supporterIds ?? new List<Guid>();
            return Task.CompletedTask;
        }

        public List<Guid> GetCurrentSupporterIds()
        {
            if (_isFilterDisabled.Value)
                return new List<Guid>();

            return _currentSupporterIds.Value ?? new List<Guid>();
        }

        public IDisposable DisableFilter()
        {
            return new FilterDisabler();
        }

        public void ClearFilter()
        {
            _currentSupporterIds.Value = new List<Guid>();
        }

        private class FilterDisabler : IDisposable
        {
            private readonly bool _previousState;

            public FilterDisabler()
            {
                _previousState = _isFilterDisabled.Value;
                _isFilterDisabled.Value = true;
            }

            public void Dispose()
            {
                _isFilterDisabled.Value = _previousState;
            }
        }

        public static bool IsFilterDisabled => _isFilterDisabled.Value;
    }
}
