/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response data for ad get requests
    /// </summary>
    public class AdGetResponse
    {
        /// <summary>
        /// A List of ads. The returned list is sorted by ad ID (ad_id) in reverse order by default.
        /// </summary>
        [JsonPropertyName("list")]
        public List<AdInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGetResponse class
        /// </summary>
        public AdGetResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGetResponse(List={List?.Count ?? 0} items, PageInfo={PageInfo})";
        }
    }

    /// <summary>
    /// Ad information returned in get responses
    /// </summary>
    public class AdInfo
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// Campaign name
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// The origin (source) of the campaign that the ad belongs to
        /// </summary>
        [JsonPropertyName("campaign_system_origin")]
        public string? CampaignSystemOrigin { get; set; }

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdgroupId { get; set; }

        /// <summary>
        /// Ad group name
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdgroupName { get; set; }

        /// <summary>
        /// Ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// Ad name
        /// </summary>
        [JsonPropertyName("ad_name")]
        public string? AdName { get; set; }

        /// <summary>
        /// Time at which the ad was created
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Time at which the ad was modified
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string? ModifyTime { get; set; }

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Identity type. Enum values: CUSTOMIZED_USER, AUTH_CODE, TT_USER, BC_AUTH_TT
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// The catalog you choose for your ads
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }

        /// <summary>
        /// Different dimensions to choose products
        /// </summary>
        [JsonPropertyName("product_specific_type")]
        public string? ProductSpecificType { get; set; }

        /// <summary>
        /// Product SPU IDs
        /// </summary>
        [JsonPropertyName("item_group_ids")]
        public List<string>? ItemGroupIds { get; set; }

        /// <summary>
        /// The ID of the product set
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string? ProductSetId { get; set; }

        /// <summary>
        /// IDs of the SKUs
        /// </summary>
        [JsonPropertyName("sku_ids")]
        public List<string>? SkuIds { get; set; }

        /// <summary>
        /// IDs of vehicles
        /// </summary>
        [JsonPropertyName("vehicle_ids")]
        public List<string>? VehicleIds { get; set; }

        /// <summary>
        /// The list of Showcase products that you use in your ad
        /// </summary>
        [JsonPropertyName("showcase_products")]
        public List<ShowcaseProduct>? ShowcaseProducts { get; set; }

        /// <summary>
        /// The ad format. Enum values: SINGLE_IMAGE, SINGLE_VIDEO, LIVE_CONTENT, CAROUSEL_ADS, CATALOG_CAROUSEL
        /// </summary>
        [JsonPropertyName("ad_format")]
        public string? AdFormat { get; set; }

        /// <summary>
        /// The video type that you use for Product Sales scenarios
        /// </summary>
        [JsonPropertyName("vertical_video_strategy")]
        public string? VerticalVideoStrategy { get; set; }

        /// <summary>
        /// Dynamic format
        /// </summary>
        [JsonPropertyName("dynamic_format")]
        public string? DynamicFormat { get; set; }

        /// <summary>
        /// The video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// A list of image IDs
        /// </summary>
        [JsonPropertyName("image_ids")]
        public List<string>? ImageIds { get; set; }

        /// <summary>
        /// The index that specifies the additional images to be used in the VSA Carousel ad
        /// </summary>
        [JsonPropertyName("carousel_image_index")]
        public int? CarouselImageIndex { get; set; }

        /// <summary>
        /// Call-to-action for the end card
        /// </summary>
        [JsonPropertyName("end_card_cta")]
        public string? EndCardCta { get; set; }

        /// <summary>
        /// The type of disclaimer to show in the Automotive Carousel Ads for Models
        /// </summary>
        [JsonPropertyName("auto_disclaimer_types")]
        public List<string>? AutoDisclaimerTypes { get; set; }

        /// <summary>
        /// A list of product details to display in your Automotive Carousel Ad for Inventory
        /// </summary>
        [JsonPropertyName("product_display_field_list")]
        public List<string>? ProductDisplayFieldList { get; set; }

        /// <summary>
        /// The ID of the piece of music that is used in the TikTok Carousel Ad
        /// </summary>
        [JsonPropertyName("music_id")]
        public string? MusicId { get; set; }

        /// <summary>
        /// The ID of the TikTok post that is used as Spark Ad
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TiktokItemId { get; set; }

        /// <summary>
        /// Whether to disable the promotional use of the music in the Spark Ad post
        /// </summary>
        [JsonPropertyName("promotional_music_disabled")]
        public bool? PromotionalMusicDisabled { get; set; }

        /// <summary>
        /// Whether to enable dueting for the Spark Ad post
        /// </summary>
        [JsonPropertyName("item_duet_status")]
        public string? ItemDuetStatus { get; set; }

        /// <summary>
        /// Whether to enable stitching for the Spark Ad post
        /// </summary>
        [JsonPropertyName("item_stitch_status")]
        public string? ItemStitchStatus { get; set; }

        /// <summary>
        /// Indicates whether the ad is a Spark Ads dark post
        /// </summary>
        [JsonPropertyName("dark_post_status")]
        public string? DarkPostStatus { get; set; }

        /// <summary>
        /// Default is false. If branded_content_disabled is true, you cannot modify dark_post_status
        /// </summary>
        [JsonPropertyName("branded_content_disabled")]
        public bool? BrandedContentDisabled { get; set; }

        /// <summary>
        /// Catalog video template ID
        /// </summary>
        [JsonPropertyName("shopping_ads_video_package_id")]
        public string? ShoppingAdsVideoPackageId { get; set; }

        /// <summary>
        /// The ad text. It is shown to your audience as part of your ad creative
        /// </summary>
        [JsonPropertyName("ad_text")]
        public string? AdText { get; set; }

        /// <summary>
        /// A list of ad texts
        /// </summary>
        [JsonPropertyName("ad_texts")]
        public string? AdTexts { get; set; }

        /// <summary>
        /// For call-to-action text, see Enumeration - Call-to-action
        /// </summary>
        [JsonPropertyName("call_to_action")]
        public string? CallToAction { get; set; }

        /// <summary>
        /// The ID of the CTA portfolio that you want to use in your ads
        /// </summary>
        [JsonPropertyName("call_to_action_id")]
        public string? CallToActionId { get; set; }

        /// <summary>
        /// Creative portfolio ID
        /// </summary>
        [JsonPropertyName("card_id")]
        public string? CardId { get; set; }

        /// <summary>
        /// Landing page URL
        /// </summary>
        [JsonPropertyName("landing_page_url")]
        public string? LandingPageUrl { get; set; }

        /// <summary>
        /// A list of URL parameters
        /// </summary>
        [JsonPropertyName("utm_params")]
        public List<UtmParam>? UtmParams { get; set; }

        /// <summary>
        /// Page ID
        /// </summary>
        [JsonPropertyName("page_id")]
        public long? PageId { get; set; }

        /// <summary>
        /// The Custom Product Page (CPP) URL
        /// </summary>
        [JsonPropertyName("cpp_url")]
        public string? CppUrl { get; set; }

        /// <summary>
        /// The category of the TikTok pages that you want to promote
        /// </summary>
        [JsonPropertyName("tiktok_page_category")]
        public string? TiktokPageCategory { get; set; }

        /// <summary>
        /// The region code for the phone number that the audience can click on the ad to call
        /// </summary>
        [JsonPropertyName("phone_region_code")]
        public string? PhoneRegionCode { get; set; }

        /// <summary>
        /// The phone number that the audience can click on the ad to call
        /// </summary>
        [JsonPropertyName("phone_region_calling_code")]
        public string? PhoneRegionCallingCode { get; set; }

        /// <summary>
        /// The phone number that the audience can click on the ad to call
        /// </summary>
        [JsonPropertyName("phone_number")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// The specific location where you want your audience to go if they have your app installed
        /// </summary>
        [JsonPropertyName("deeplink")]
        public string? Deeplink { get; set; }

        /// <summary>
        /// The deeplink type
        /// </summary>
        [JsonPropertyName("deeplink_type")]
        public string? DeeplinkType { get; set; }

        /// <summary>
        /// The format type of the deeplink
        /// </summary>
        [JsonPropertyName("deeplink_format_type")]
        public string? DeeplinkFormatType { get; set; }

        /// <summary>
        /// The source of the deeplink to be used in the Shopping Ads
        /// </summary>
        [JsonPropertyName("shopping_ads_deeplink_type")]
        public string? ShoppingAdsDeeplinkType { get; set; }

        /// <summary>
        /// A list of deeplink URL parameters
        /// </summary>
        [JsonPropertyName("deeplink_utm_params")]
        public List<UtmParam>? DeeplinkUtmParams { get; set; }

        /// <summary>
        /// In the Shopping Ads retargeting scenario, the fallback website type when the deeplink fails to be triggered
        /// </summary>
        [JsonPropertyName("shopping_ads_fallback_type")]
        public string? ShoppingAdsFallbackType { get; set; }

        /// <summary>
        /// Fallback Type. If the audience do not have the app installed
        /// </summary>
        [JsonPropertyName("fallback_type")]
        public string? FallbackType { get; set; }

        /// <summary>
        /// Dynamic destination strategy
        /// </summary>
        [JsonPropertyName("dynamic_destination")]
        public string? DynamicDestination { get; set; }

        /// <summary>
        /// The ID of the automatic message to use in a TikTok Direct Messaging Ad
        /// </summary>
        [JsonPropertyName("auto_message_id")]
        public string? AutoMessageId { get; set; }

        /// <summary>
        /// Whether to turn on the AIGC self-disclosure toggle
        /// </summary>
        [JsonPropertyName("aigc_disclosure_type")]
        public string? AigcDisclosureType { get; set; }

        /// <summary>
        /// Type of disclaimer in the ad
        /// </summary>
        [JsonPropertyName("disclaimer_type")]
        public string? DisclaimerType { get; set; }

        /// <summary>
        /// The text-only disclaimer in the ad
        /// </summary>
        [JsonPropertyName("disclaimer_text")]
        public DisclaimerText? DisclaimerTextObj { get; set; }

        /// <summary>
        /// The clickable disclaimer or clickable disclaimers in the ad
        /// </summary>
        [JsonPropertyName("disclaimer_clickable_texts")]
        public List<DisclaimerClickableText>? DisclaimerClickableTexts { get; set; }

        /// <summary>
        /// The pixel ID that is tracked
        /// </summary>
        [JsonPropertyName("tracking_pixel_id")]
        public long? TrackingPixelId { get; set; }

        /// <summary>
        /// The ID of the application that is tracked
        /// </summary>
        [JsonPropertyName("tracking_app_id")]
        public string? TrackingAppId { get; set; }

        /// <summary>
        /// A list of Offline Event set IDs that are tracked
        /// </summary>
        [JsonPropertyName("tracking_offline_event_set_ids")]
        public List<string>? TrackingOfflineEventSetIds { get; set; }

        /// <summary>
        /// The ID of the message event set that is measured in the Instant Messaging Ad
        /// </summary>
        [JsonPropertyName("tracking_message_event_set_id")]
        public string? TrackingMessageEventSetId { get; set; }

        /// <summary>
        /// Whether Moat Viewability Verification is enabled for the ad
        /// </summary>
        [JsonPropertyName("vast_moat_enabled")]
        public bool? VastMoatEnabled { get; set; }

        /// <summary>
        /// Post-bid third-party viewability measurement partner
        /// </summary>
        [JsonPropertyName("viewability_postbid_partner")]
        public string? ViewabilityPostbidPartner { get; set; }

        /// <summary>
        /// The wrapped VAST URL used by the post-bid third-party partner to measure viewability
        /// </summary>
        [JsonPropertyName("viewability_vast_url")]
        public string? ViewabilityVastUrl { get; set; }

        /// <summary>
        /// Post-bid third-party brand safety measurement partner
        /// </summary>
        [JsonPropertyName("brand_safety_postbid_partner")]
        public string? BrandSafetyPostbidPartner { get; set; }

        /// <summary>
        /// The wrapped VAST URL used by the post-bid third-party partner to measure brand safety
        /// </summary>
        [JsonPropertyName("brand_safety_vast_url")]
        public string? BrandSafetyVastUrl { get; set; }

        /// <summary>
        /// Default Impression Tracking URL
        /// </summary>
        [JsonPropertyName("impression_tracking_url")]
        public string? ImpressionTrackingUrl { get; set; }

        /// <summary>
        /// Click Tracking URL
        /// </summary>
        [JsonPropertyName("click_tracking_url")]
        public string? ClickTrackingUrl { get; set; }

        /// <summary>
        /// Playable material URL
        /// </summary>
        [JsonPropertyName("playable_url")]
        public string? PlayableUrl { get; set; }

        /// <summary>
        /// Operation status
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string? OperationStatus { get; set; }

        /// <summary>
        /// Ad status (Secondary status)
        /// </summary>
        [JsonPropertyName("secondary_status")]
        public string? SecondaryStatus { get; set; }

        /// <summary>
        /// Creative type for Live Shopping Ads, Product Shopping Ads, App Pre-Registration, Automotive Ads for Inventory, or Automotive Ads for Models scenarios
        /// </summary>
        [JsonPropertyName("creative_type")]
        public string? CreativeType { get; set; }

        /// <summary>
        /// App name that is displayed in the ad
        /// </summary>
        [JsonPropertyName("app_name")]
        public string? AppName { get; set; }

        /// <summary>
        /// The display name of landing page or pure exposure ad
        /// </summary>
        [JsonPropertyName("display_name")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// Avatar URL
        /// </summary>
        [JsonPropertyName("profile_image_url")]
        public string? ProfileImageUrl { get; set; }

        /// <summary>
        /// Whether you grant displaying some of your ads in our TikTok For Business Creative Center
        /// </summary>
        [JsonPropertyName("creative_authorized")]
        public bool? CreativeAuthorized { get; set; }

        /// <summary>
        /// Whether the ad is an automated ad or Smart Creative ad
        /// </summary>
        [JsonPropertyName("is_aco")]
        public bool? IsAco { get; set; }

        /// <summary>
        /// Whether the campaign is a new structure
        /// </summary>
        [JsonPropertyName("is_new_structure")]
        public bool? IsNewStructure { get; set; }

        /// <summary>
        /// Conversion event for the ad group
        /// </summary>
        [JsonPropertyName("optimization_event")]
        public string? OptimizationEvent { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdInfo(AdId={AdId}, AdName={AdName}, AdvertiserId={AdvertiserId})";
        }
    }

    /// <summary>
    /// Showcase product information
    /// </summary>
    public class ShowcaseProduct
    {
        /// <summary>
        /// SPU ID of the product
        /// </summary>
        [JsonPropertyName("item_group_id")]
        public string? ItemGroupId { get; set; }

        /// <summary>
        /// The ID of the store that the product belongs to
        /// </summary>
        [JsonPropertyName("store_id")]
        public string? StoreId { get; set; }

        /// <summary>
        /// The ID of the catalog that the product belongs to
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }
    }
}
