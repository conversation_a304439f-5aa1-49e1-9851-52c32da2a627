using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.JobManagement
{
    /// <summary>
    /// DTO cho job đang chờ xử lý
    /// </summary>
    public class PendingJobDto : EntityDto<Guid>
    {
        /// <summary>
        /// ID của job
        /// </summary>
        public string JobId { get; set; }

        /// <summary>
        /// Loại command
        /// </summary>
        public string CommandType { get; set; }

        /// <summary>
        /// Đ<PERSON> ưu tiên
        /// </summary>
        public string Priority { get; set; }

        /// <summary>
        /// Thời gian tạo
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// Tên Business Application
        /// </summary>
        public string BusinessApplicationName { get; set; }

        /// <summary>
        /// Trạng thái
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// ID của Business Application
        /// </summary>
        public Guid? BusinessApplicationId { get; set; }
    }
}