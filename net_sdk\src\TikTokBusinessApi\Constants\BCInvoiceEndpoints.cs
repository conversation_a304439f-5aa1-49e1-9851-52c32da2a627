/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API BC Invoice endpoints
    /// </summary>
    public static class BCInvoiceEndpoints
    {
        /// <summary>
        /// Get the invoices of a BC endpoint (v1.3)
        /// </summary>
        public const string GetInvoices = "/v1.3/bc/invoice/get/";

        /// <summary>
        /// Get the unpaid amount of a BC endpoint (v1.3)
        /// </summary>
        public const string GetUnpaidAmount = "/v1.3/bc/invoice/unpaid/get/";

        /// <summary>
        /// Download an individual invoice synchronously endpoint (v1.3)
        /// </summary>
        public const string DownloadInvoice = "/v1.3/bc/invoice/download/";

        /// <summary>
        /// Create an asynchronous download task endpoint (v1.3)
        /// </summary>
        public const string CreateDownloadTask = "/v1.3/bc/invoice/task/create/";

        /// <summary>
        /// Get asynchronous download task (BILLING_REPORT) endpoint (v1.3)
        /// </summary>
        public const string GetDownloadTask = "/v1.3/bc/invoice/task/get/";

        /// <summary>
        /// Get asynchronous download task list (INVOICE_LIST and INVOICE_BATCH) endpoint (v1.3)
        /// </summary>
        public const string GetDownloadTaskList = "/v1.3/bc/invoice/task/list/";
    }
}
