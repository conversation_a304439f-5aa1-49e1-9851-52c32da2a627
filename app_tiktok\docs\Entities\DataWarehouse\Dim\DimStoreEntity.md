# DimStoreEntity

## Thông tin bảng dữ liệu

-   **Table**: `Dim_Store`
-   **TablePrefix**: `Dim_`
-   **TableName**: `Dim_Store`
-   **DbSchema**: `null`

## Mô tả

Dimension table cho TikTok Shop Store. Lưu trữ thông tin các cửa hàng TikTok Shop để phân tích hiệu suất GMV Max Campaign theo từng cửa hàng.

## Bảng dữ liệu: Dim_Store

| Tên Field            | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                             |
| -------------------- | ------------ | -------- | ------ | ------------------------------------------------- |
| Id                   | Guid         | ✅       | -      | ID duy nhất của bản <PERSON>hi (Primary Key)             |
| StoreId              | string       | ✅       | 100    | Business Key - Store ID từ TikTok Shop            |
| StoreName            | string       | ❌       | 500    | Tên cửa hàng                                      |
| Description          | string       | ❌       | 1000   | Mô tả cửa hàng                                    |
| StoreUrl             | string       | ❌       | 500    | URL cửa hàng                                      |
| StoreType            | string       | ❌       | 50     | Loại cửa hàng (OFFICIAL, AUTHORIZED, etc.)        |
| Status               | string       | ❌       | 20     | Trạng thái cửa hàng (ACTIVE, INACTIVE, SUSPENDED) |
| Country              | string       | ❌       | 50     | Quốc gia của cửa hàng                             |
| Region               | string       | ❌       | 100    | Khu vực của cửa hàng                              |
| CreatedAt            | DateTime?    | ❌       | -      | Thời gian tạo cửa hàng                            |
| UpdatedAt            | DateTime?    | ❌       | -      | Thời gian cập nhật gần nhất                       |
| EffectiveStartDate   | DateTime     | ✅       | -      | Ngày bắt đầu hiệu lực (SCD Type 2)                |
| EffectiveEndDate     | DateTime?    | ❌       | -      | Ngày kết thúc hiệu lực (SCD Type 2)               |
| CreationTime         | DateTime     | ✅       | -      | Thời gian tạo bản ghi (AuditedEntity)             |
| CreatorId            | Guid?        | ❌       | -      | ID người tạo (AuditedEntity)                      |
| LastModificationTime | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (AuditedEntity)            |
| LastModifierId       | Guid?        | ❌       | -      | ID người sửa đổi cuối (AuditedEntity)             |

## Cấu trúc dữ liệu

### Business Key

-   **StoreId**: ID cửa hàng từ TikTok Shop API

### Thông tin cơ bản

-   **StoreName**: Tên hiển thị của cửa hàng
-   **Description**: Mô tả chi tiết về cửa hàng
-   **StoreUrl**: Link trực tiếp đến cửa hàng TikTok Shop

### Phân loại và trạng thái

-   **StoreType**: Loại cửa hàng (OFFICIAL, AUTHORIZED, PARTNER, etc.)
-   **Status**: Trạng thái hoạt động hiện tại
-   **Country**: Quốc gia đăng ký cửa hàng
-   **Region**: Khu vực địa lý

### SCD Type 2 (Slowly Changing Dimension)

-   **EffectiveStartDate**: Ngày bắt đầu hiệu lực của phiên bản
-   **EffectiveEndDate**: Ngày kết thúc hiệu lực (NULL nếu là phiên bản hiện tại)

## Navigation Properties

-   **FactGmvMaxCampaign**: One-to-Many, các bản ghi hiệu suất chiến dịch
-   **FactGmvMaxProduct**: One-to-Many, các bản ghi hiệu suất sản phẩm

## Mục đích sử dụng

-   Phân tích hiệu suất GMV Max theo từng cửa hàng
-   So sánh hiệu suất giữa các cửa hàng
-   Quản lý danh sách cửa hàng TikTok Shop
-   Hỗ trợ drill-down analysis từ campaign xuống store level
-   Tracking lịch sử thay đổi thông tin cửa hàng

## Quan hệ với các entity khác

-   **FactGmvMaxCampaignEntity**: One-to-Many, hiệu suất chiến dịch theo cửa hàng
-   **FactGmvMaxProductEntity**: One-to-Many, hiệu suất sản phẩm theo cửa hàng

## Use Cases thực tế

### Store Performance Analysis

```sql
-- Phân tích hiệu suất theo cửa hàng
SELECT
    s.StoreName,
    s.Country,
    s.Status,
    SUM(f.GrossRevenue) as TotalRevenue,
    AVG(f.ROI) as AverageROI,
    SUM(f.Orders) as TotalOrders,
    SUM(f.Cost) as TotalCost
FROM Fact_GmvMaxCampaign f
JOIN Dim_Store s ON f.StoreKey = s.Id
GROUP BY s.StoreName, s.Country, s.Status
ORDER BY TotalRevenue DESC
```

### Store Comparison

```sql
-- So sánh hiệu suất giữa các cửa hàng
SELECT
    s1.StoreName as Store1,
    s2.StoreName as Store2,
    f1.ROI as ROI1,
    f2.ROI as ROI2,
    f1.Orders as Orders1,
    f2.Orders as Orders2
FROM Fact_GmvMaxCampaign f1
JOIN Dim_Store s1 ON f1.StoreKey = s1.Id
JOIN Fact_GmvMaxCampaign f2 ON f1.DateKey = f2.DateKey
JOIN Dim_Store s2 ON f2.StoreKey = s2.Id
WHERE s1.Id != s2.Id
```

### Store History Tracking

```sql
-- Theo dõi lịch sử thay đổi thông tin cửa hàng
SELECT
    StoreId,
    StoreName,
    Status,
    EffectiveStartDate,
    EffectiveEndDate
FROM Dim_Store
WHERE StoreId = @StoreId
ORDER BY EffectiveStartDate DESC
```

### Regional Performance

```sql
-- Phân tích hiệu suất theo khu vực
SELECT
    s.Region,
    s.Country,
    COUNT(DISTINCT s.Id) as StoreCount,
    SUM(f.GrossRevenue) as TotalRevenue,
    AVG(f.ROI) as AverageROI
FROM Fact_GmvMaxCampaign f
JOIN Dim_Store s ON f.StoreKey = s.Id
GROUP BY s.Region, s.Country
ORDER BY TotalRevenue DESC
```
