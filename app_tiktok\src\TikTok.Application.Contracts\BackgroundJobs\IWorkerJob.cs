using System.Threading.Tasks;
using TikTok.Enums;

namespace TikTok.BackgroundJobs
{
    /// <summary>
    /// Interface cho worker job
    /// </summary>
    public interface IWorkerJob
    {
        /// <summary>
        /// Loại lệnh mà worker này xử lý
        /// </summary>
        CommandType CommandType { get; }

        /// <summary>
        /// Xử lý công việc
        /// </summary>
        /// <param name="args">Arguments chứa thông tin công việc</param>
        /// <returns>Kết quả xử lý</returns>
        Task<JobResult> ExecuteAsync(WorkerJobArgs args);
    }

    /// <summary>
    /// Kết quả xử lý công việc
    /// </summary>
    public class JobResult
    {
        /// <summary>
        /// Thành công hay không
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Thông báo lỗi nếu có
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Dữ liệu kết quả
        /// </summary>
        public string ResultData { get; set; }

        /// <summary>
        /// Thời gian xử lý (milliseconds)
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// Tạo kết quả thành công
        /// </summary>
        /// <param name="resultData">Dữ liệu kết quả</param>
        /// <param name="processingTimeMs">Thời gian xử lý</param>
        /// <returns>JobResult</returns>
        public static JobResult Success(string resultData = null, long processingTimeMs = 0)
        {
            return new JobResult
            {
                IsSuccess = true,
                ResultData = resultData,
                ProcessingTimeMs = processingTimeMs
            };
        }

        /// <summary>
        /// Tạo kết quả lỗi
        /// </summary>
        /// <param name="errorMessage">Thông báo lỗi</param>
        /// <param name="processingTimeMs">Thời gian xử lý</param>
        /// <returns>JobResult</returns>
        public static JobResult Error(string errorMessage, long processingTimeMs = 0)
        {
            return new JobResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ProcessingTimeMs = processingTimeMs,
            };
        }

        /// <summary>
        /// Tạo kết quả lỗi
        /// </summary>
        /// <param name="errorMessage">Thông báo lỗi</param>
        /// <param name="processingTimeMs">Thời gian xử lý</param>
        /// <returns>JobResult</returns>
        public static JobResult Error(string errorMessage, string resultData)
        {
            return new JobResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ResultData = resultData,
            };
        }
    }
}