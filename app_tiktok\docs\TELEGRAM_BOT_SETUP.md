# Hướng dẫn tạo và cấu hình Telegram Bot

## Bước 1: Tạo Telegram Bot

### 1.1. T<PERSON><PERSON>tFather trên Telegram
- Mở Telegram và tìm kiếm `@BotFather`
- Hoặc truy cập: https://t.me/botfather

### 1.2. <PERSON><PERSON><PERSON> bot mới
1. <PERSON><PERSON><PERSON> lệnh `/newbot` cho BotFather
2. Nhập tên cho bot (ví dụ: "TikTok Alert Bot")
3. Nhập username cho bot (ph<PERSON><PERSON> kết thúc bằng 'bot', ví dụ: "tiktok_alert_bot")

### 1.3. <PERSON><PERSON><PERSON> <PERSON><PERSON>
- BotFather sẽ trả về một token dạng: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`
- **Lưu ý**: Token này rất quan trọng, không chia sẻ cho người khác

## Bước 2: Tạo Chat/Channel

### 2.1. Tạo Channel (Khuyến nghị)
1. <PERSON>rong Telegram, nhấn vào menu (3 gạch ngang)
2. <PERSON><PERSON><PERSON> "New Channel"
3. Đặt tên channel (ví dụ: "TikTok Alerts")
4. Thêm mô tả (tùy chọn)
5. Chọn "Public" hoặc "Private"

### 2.2. Thêm Bot vào Channel
1. Mở channel vừa tạo
2. Nhấn vào tên channel ở đầu trang
3. Chọn "Administrators"
4. Nhấn "Add Administrator"
5. Tìm và thêm bot vừa tạo
6. Cấp quyền "Post Messages" cho bot

## Bước 3: Lấy Chat ID

### 3.1. Cách 1: Sử dụng Bot API
1. Gửi một tin nhắn bất kỳ vào channel
2. Truy cập URL: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
3. Tìm `chat_id` trong response JSON
4. Chat ID sẽ có dạng: `-100xxxxxxxxxx` (cho channel)

### 3.2. Cách 2: Sử dụng @userinfobot
1. Tìm và thêm `@userinfobot` vào Telegram
2. Forward một tin nhắn từ channel đến bot này
3. Bot sẽ trả về thông tin chat ID

## Bước 4: Cấu hình trong ứng dụng

### 4.1. Cập nhật appsettings.json
```json
{
  "MessageProviderOption": {
    "ApplicationName": "Module VBDH",
    "Telegram": {
      "Enabled": true,
      "BotToken": "YOUR_BOT_TOKEN_HERE",
      "ChatId": "YOUR_CHAT_ID_HERE"
    }
  }
}
```

### 4.2. Test bot
1. Chạy ứng dụng
2. Gửi POST request đến: `POST /api/message/send`
```json
{
  "message": "Test message from TikTok application",
  "provider": "Telegram"
}
```

## Bước 5: Các loại thông báo

Hệ thống sẽ tự động gửi các thông báo sau:

### 5.1. Worker Timeout Alert
```
⚠️ Worker Timeout Alert

Found X timeout workers
Time: 2024-01-01 12:00:00 UTC
```

### 5.2. Stuck Jobs Alert
```
🚨 Stuck Jobs Alert

Found X stuck jobs
Time: 2024-01-01 12:00:00 UTC
```

### 5.3. Manager Job Alert
```
⚠️ Manager Job Alert

Manager Job has not run recently
Last run: 2024-01-01 11:50:00 UTC
Time: 2024-01-01 12:00:00 UTC
```

### 5.4. Register Job Alert
```
⚠️ Register Job Alert

Register Job has not run recently
Last run: 2024-01-01 11:50:00 UTC
Time: 2024-01-01 12:00:00 UTC
```

## Lưu ý quan trọng

1. **Bảo mật**: Không chia sẻ Bot Token với người khác
2. **Rate Limiting**: Telegram có giới hạn 30 messages/giây
3. **Message Format**: Hỗ trợ HTML formatting
4. **Error Handling**: Hệ thống sẽ log lỗi nếu không gửi được tin nhắn
5. **Testing**: Luôn test bot trước khi deploy production

## Troubleshooting

### Bot không gửi được tin nhắn
1. Kiểm tra Bot Token có đúng không
2. Kiểm tra Chat ID có đúng không
3. Kiểm tra bot có quyền gửi tin nhắn trong channel không
4. Kiểm tra logs của ứng dụng

### Chat ID không đúng
- Chat ID cho channel thường bắt đầu bằng `-100`
- Chat ID cho group thường bắt đầu bằng `-`
- Chat ID cho user là số dương

### Bot bị block
- Nếu bot bị block, cần tạo bot mới
- Hoặc liên hệ admin của channel để unblock bot 