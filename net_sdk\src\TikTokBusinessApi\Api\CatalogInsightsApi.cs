/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Catalog Insights operations
    /// </summary>
    public class CatalogInsightsApi : ICatalogInsightsApi
    {
        private readonly IApiClient _apiClient;

        /// <summary>
        /// Initializes a new instance of the CatalogInsightsApi class
        /// </summary>
        /// <param name="apiClient">API client for making requests</param>
        public CatalogInsightsApi(IApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new System.ArgumentNullException(nameof(apiClient));
        }

        /// <inheritdoc />
        public async Task<CatalogInsightFiltersResponse> GetFiltersAsync(
            string bcId,
            string catalogId,
            string filterType,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(bcId))
                throw new System.ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));

            if (string.IsNullOrWhiteSpace(catalogId))
                throw new System.ArgumentException("Catalog ID cannot be null or empty", nameof(catalogId));

            if (string.IsNullOrWhiteSpace(filterType))
                throw new System.ArgumentException("Filter type cannot be null or empty", nameof(filterType));

            if (page < 1)
                throw new System.ArgumentException("Page must be greater than 0", nameof(page));

            if (pageSize < 1 || pageSize > 200)
                throw new System.ArgumentException("Page size must be between 1 and 200", nameof(pageSize));

            var request = new CatalogInsightFiltersRequest
            {
                BcId = bcId,
                CatalogId = catalogId,
                FilterType = filterType,
                Page = page,
                PageSize = pageSize
            };

            return await _apiClient.CallApiAsync<CatalogInsightFiltersResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogInsightsEndpoints.GetFilters}",
                System.Net.Http.HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<CatalogInsightProductsResponse> GetTrendingProductsAsync(
            string bcId,
            string catalogId,
            CatalogInsightProductFiltering? filtering = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(bcId))
                throw new System.ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));

            if (string.IsNullOrWhiteSpace(catalogId))
                throw new System.ArgumentException("Catalog ID cannot be null or empty", nameof(catalogId));

            var request = new CatalogInsightProductsRequest
            {
                BcId = bcId,
                CatalogId = catalogId,
                Filtering = filtering
            };

            return await _apiClient.CallApiAsync<CatalogInsightProductsResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogInsightsEndpoints.GetTrendingProducts}",
                System.Net.Http.HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<CatalogInsightCategoriesResponse> GetTrendingCategoriesAsync(
            string bcId,
            string catalogId,
            CatalogInsightCategoryFiltering? filtering = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(bcId))
                throw new System.ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));

            if (string.IsNullOrWhiteSpace(catalogId))
                throw new System.ArgumentException("Catalog ID cannot be null or empty", nameof(catalogId));

            var request = new CatalogInsightCategoriesRequest
            {
                BcId = bcId,
                CatalogId = catalogId,
                Filtering = filtering
            };

            return await _apiClient.CallApiAsync<CatalogInsightCategoriesResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogInsightsEndpoints.GetTrendingCategories}",
                System.Net.Http.HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
