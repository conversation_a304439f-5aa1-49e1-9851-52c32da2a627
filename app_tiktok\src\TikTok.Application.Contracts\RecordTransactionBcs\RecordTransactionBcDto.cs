using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.RecordTransactionBcs
{
    /// <summary>
    /// DTO cho RecordTransactionBc
    /// </summary>
    public class RecordTransactionBcDto : EntityDto<Guid>
    {
        public DateTime Date { get; set; }
        public decimal Amount { get; set; }
        public string Timezone { get; set; }
        public string Currency { get; set; }
        public int FundsType { get; set; }
        public string InvoiceId { get; set; }
        public string InvoiceSerialNumber { get; set; }
    }
}