using System;
using TikTok.Enums;

namespace TikTok.BackgroundJobs
{
    /// <summary>
    /// Arguments cho worker job
    /// </summary>
    public class WorkerJobArgs
    {
        /// <summary>
        /// ID của công việc
        /// </summary>
        public Guid JobId { get; set; }

        /// <summary>
        /// ID của worker
        /// </summary>
        public string WorkerId { get; set; }

        /// <summary>
        /// Loại lệnh
        /// </summary>
        public CommandType CommandType { get; set; }

        /// <summary>
        /// Tham số JSON
        /// </summary>
        public string? Parameters { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="jobId">ID của công việc</param>
        /// <param name="workerId">ID của worker</param>
        /// <param name="commandType">Lo<PERSON><PERSON> l<PERSON></param>
        /// <param name="parameters">Tham số JSON</param>
        public WorkerJobArgs(Guid jobId, string workerId, CommandType commandType, string? parameters = null)
        {
            JobId = jobId;
            WorkerId = workerId;
            CommandType = commandType;
            Parameters = parameters;
        }
    }
}