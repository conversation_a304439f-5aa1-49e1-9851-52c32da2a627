/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Ad Benchmarks Models

    /// <summary>
    /// Request model for getting ad benchmarks
    /// </summary>
    public class AdBenchmarkRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Time window for comparison (in days). Enum: 7 (default), 14, 30, 60
        /// </summary>
        [JsonPropertyName("compare_time_window")]
        public string? CompareTimeWindow { get; set; }

        /// <summary>
        /// List of dimensions to compare. 1-4 dimensions are allowed. 
        /// Enum: LOCATION, AD_CATEGORY, EXTERNAL_ACTION, PLACEMENT
        /// </summary>
        [JsonPropertyName("dimensions")]
        [Required]
        public List<string> Dimensions { get; set; } = new();

        /// <summary>
        /// Metrics that you want to get. By default, all metrics will be returned.
        /// </summary>
        [JsonPropertyName("metrics_fields")]
        public List<string>? MetricsFields { get; set; }

        /// <summary>
        /// Filtering conditions. You must specify one and only one out of the three conditions allowed
        /// </summary>
        [JsonPropertyName("filtering")]
        [Required]
        public AdBenchmarkFiltering Filtering { get; set; } = new();

        /// <summary>
        /// Field to sort by. By default, results will be sorted by CREATE_TIME
        /// </summary>
        [JsonPropertyName("sort_field")]
        public string? SortField { get; set; }

        /// <summary>
        /// Sorting type. Enum: ASC (starting from the one that was created earliest), DES (starting from the one that was created latest)
        /// </summary>
        [JsonPropertyName("sort_type")]
        public string? SortType { get; set; }

        /// <summary>
        /// Page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering conditions for ad benchmarks
    /// </summary>
    public class AdBenchmarkFiltering
    {
        /// <summary>
        /// IDs of the ads that you want to get benchmark results for
        /// </summary>
        [JsonPropertyName("ad_ids")]
        public List<string>? AdIds { get; set; }

        /// <summary>
        /// IDs of the ad groups that you want to get benchmark results for
        /// </summary>
        [JsonPropertyName("adgroup_ids")]
        public List<string>? AdgroupIds { get; set; }

        /// <summary>
        /// IDs of the campaigns that you want to get benchmark results for
        /// </summary>
        [JsonPropertyName("campaign_ids")]
        public List<string>? CampaignIds { get; set; }
    }

    /// <summary>
    /// Response model for ad benchmarks
    /// </summary>
    public class AdBenchmarkResponse
    {
        /// <summary>
        /// Date when the comparison is performed
        /// </summary>
        [JsonPropertyName("compare_date")]
        public string? CompareDate { get; set; }

        /// <summary>
        /// List of benchmark results
        /// </summary>
        [JsonPropertyName("list")]
        public List<AdBenchmarkResult>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Individual ad benchmark result
    /// </summary>
    public class AdBenchmarkResult
    {
        /// <summary>
        /// Information about the subject to get benchmark data for
        /// </summary>
        [JsonPropertyName("info")]
        public AdBenchmarkInfo? Info { get; set; }

        /// <summary>
        /// Metrics and values
        /// </summary>
        [JsonPropertyName("metrics")]
        public Dictionary<string, double>? Metrics { get; set; }
    }

    /// <summary>
    /// Information about the ad for benchmark data
    /// </summary>
    public class AdBenchmarkInfo
    {
        /// <summary>
        /// Ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// Two-letter country or region code
        /// </summary>
        [JsonPropertyName("location")]
        public string? Location { get; set; }

        /// <summary>
        /// Placement
        /// </summary>
        [JsonPropertyName("placement")]
        public string? Placement { get; set; }

        /// <summary>
        /// Level 2 interest category ID
        /// </summary>
        [JsonPropertyName("ad_category")]
        public int? AdCategory { get; set; }

        /// <summary>
        /// External action (conversion event) for the ad
        /// </summary>
        [JsonPropertyName("external_action")]
        public string? ExternalAction { get; set; }
    }

    #endregion

    #region Video Performance Models

    /// <summary>
    /// Request model for getting video performance data
    /// </summary>
    public class VideoPerformanceRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Report type. Enum values: AD (to-be-deprecated), VIDEO. Default value: AD.
        /// </summary>
        [JsonPropertyName("report_type")]
        public string? ReportType { get; set; }

        /// <summary>
        /// Metrics that you want to get. By default, all metrics supported for the report_type will be returned.
        /// </summary>
        [JsonPropertyName("metrics_fields")]
        public List<string>? MetricsFields { get; set; }

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        [Required]
        public VideoPerformanceFiltering Filtering { get; set; } = new();

        /// <summary>
        /// Field to sort by. By default, results will be sorted by CREATE_TIME.
        /// </summary>
        [JsonPropertyName("sort_field")]
        public string? SortField { get; set; }

        /// <summary>
        /// Sorting type. Enum values: ASC, DES
        /// </summary>
        [JsonPropertyName("sort_type")]
        public string? SortType { get; set; }

        /// <summary>
        /// Page number. Default value: 1.
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Value range: 1-500. Default value: 10.
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering conditions for video performance
    /// </summary>
    public class VideoPerformanceFiltering
    {
        /// <summary>
        /// IDs of the ads that you want to get in-second performance data for (when report_type is AD or not passed)
        /// </summary>
        [JsonPropertyName("ad_ids")]
        public List<string>? AdIds { get; set; }

        /// <summary>
        /// IDs of the ad groups that you want to get in-second performance data for (when report_type is AD or not passed)
        /// </summary>
        [JsonPropertyName("adgroup_ids")]
        public List<string>? AdgroupIds { get; set; }

        /// <summary>
        /// IDs of the campaigns that you want to get in-second performance data for (when report_type is AD or not passed)
        /// </summary>
        [JsonPropertyName("campaign_ids")]
        public List<string>? CampaignIds { get; set; }

        /// <summary>
        /// Material IDs of the videos that you want to get Video Insights data on (when report_type is VIDEO)
        /// </summary>
        [JsonPropertyName("material_ids")]
        public List<string>? MaterialIds { get; set; }

        /// <summary>
        /// IDs of the videos that you want to get Video Insights data on (when report_type is VIDEO)
        /// </summary>
        [JsonPropertyName("video_ids")]
        public List<string>? VideoIds { get; set; }

        /// <summary>
        /// Query start time (closed interval) in the format of YYYY-MM-DD hh:mm:ss (UTC+0 Time)
        /// </summary>
        [JsonPropertyName("start_time")]
        public string? StartTime { get; set; }

        /// <summary>
        /// Query end time (closed interval) in the format of YYYY-MM-DD hh:mm:ss (UTC+0 Time)
        /// </summary>
        [JsonPropertyName("end_time")]
        public string? EndTime { get; set; }

        /// <summary>
        /// Whether to request lifetime data for metrics. Default value: true.
        /// </summary>
        [JsonPropertyName("lifetime")]
        public bool? Lifetime { get; set; }
    }

    /// <summary>
    /// Response model for video performance data
    /// </summary>
    public class VideoPerformanceResponse
    {
        /// <summary>
        /// List of video performance results
        /// </summary>
        [JsonPropertyName("list")]
        public List<VideoPerformanceResult>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Individual video performance result
    /// </summary>
    public class VideoPerformanceResult
    {
        /// <summary>
        /// Information about the subject to get performance data for
        /// </summary>
        [JsonPropertyName("info")]
        public VideoPerformanceInfo? Info { get; set; }

        /// <summary>
        /// Metrics and values (sequential in-seconds values)
        /// </summary>
        [JsonPropertyName("metrics")]
        public Dictionary<string, List<int>>? Metrics { get; set; }
    }

    /// <summary>
    /// Information about the video for performance data
    /// </summary>
    public class VideoPerformanceInfo
    {
        /// <summary>
        /// Ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Video duration, in seconds
        /// </summary>
        [JsonPropertyName("duration")]
        public int? Duration { get; set; }
    }

    #endregion
}
