using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using TikTok.Enums;
using Volo.Abp.DependencyInjection;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Factory để tạo worker jobs dựa trên CommandType
    /// </summary>
    public class WorkerJobFactory : ITransientDependency
    {
        private readonly ILogger<WorkerJobFactory> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly Dictionary<CommandType, Type> _workerTypes;

        public WorkerJobFactory(
            ILogger<WorkerJobFactory> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _workerTypes = new Dictionary<CommandType, Type>();

            // Register worker types
            RegisterWorkerTypes();
        }

        /// <summary>
        /// Đăng ký các loại worker
        /// </summary>
        private void RegisterWorkerTypes()
        {
            _workerTypes[CommandType.SyncBusinessCenter] = typeof(SyncBusinessCenterWorker);
            _workerTypes[CommandType.SyncTransaction] = typeof(SyncTransactionWorker);
            _workerTypes[CommandType.SyncBalance] = typeof(SyncBalanceWorker);
            _workerTypes[CommandType.SyncAsset] = typeof(SyncAssetWorker);
            _workerTypes[CommandType.SyncCampaign] = typeof(SyncCampaignWorker);
            _workerTypes[CommandType.SyncReportIntegratedAdAccount] = typeof(SyncReportIntegratedAdAccountWorker);
            _workerTypes[CommandType.SyncReportIntegratedCampaign] = typeof(SyncReportIntegratedCampaignWorker);
            _workerTypes[CommandType.SyncReportIntegratedAdGroup] = typeof(SyncReportIntegratedAdGroupWorker);
            _workerTypes[CommandType.SyncLatestBalance] = typeof(SyncLatestBalanceWorker);
            _workerTypes[CommandType.SyncDetailedAdAccount] = typeof(SyncDetailedAdAccountWorker);
            _workerTypes[CommandType.SyncGmvMax] = typeof(SyncGmvMaxWorker);
            _logger.LogDebug("Registered {Count} worker types", _workerTypes.Count);
        }

        /// <summary>
        /// Tạo worker job dựa trên CommandType
        /// </summary>
        /// <param name="commandType">Loại lệnh</param>
        /// <returns>Worker job instance</returns>
        public IWorkerJob CreateWorker(CommandType commandType)
        {
            if (!_workerTypes.TryGetValue(commandType, out var workerType))
            {
                throw new ArgumentException($"No worker registered for CommandType: {commandType}");
            }

            try
            {
                var worker = (IWorkerJob)_serviceProvider.GetRequiredService(workerType);
                _logger.LogDebug("Created worker {WorkerType} for CommandType {CommandType}",
                    workerType.Name, commandType);
                return worker;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create worker for CommandType {CommandType}", commandType);
                throw;
            }
        }

        /// <summary>
        /// Kiểm tra xem có worker cho CommandType không
        /// </summary>
        /// <param name="commandType">Loại lệnh</param>
        /// <returns>True nếu có worker</returns>
        public bool HasWorker(CommandType commandType)
        {
            return _workerTypes.ContainsKey(commandType);
        }

        /// <summary>
        /// Lấy danh sách các CommandType được hỗ trợ
        /// </summary>
        /// <returns>Danh sách CommandType</returns>
        public IEnumerable<CommandType> GetSupportedCommandTypes()
        {
            return _workerTypes.Keys;
        }

        /// <summary>
        /// Lấy thông tin về worker
        /// </summary>
        /// <param name="commandType">Loại lệnh</param>
        /// <returns>Thông tin worker</returns>
        public WorkerInfo GetWorkerInfo(CommandType commandType)
        {
            if (!_workerTypes.TryGetValue(commandType, out var workerType))
            {
                return null;
            }

            return new WorkerInfo
            {
                CommandType = commandType,
                WorkerTypeName = workerType.Name,
                IsAvailable = true
            };
        }

        /// <summary>
        /// Lấy danh sách thông tin tất cả workers
        /// </summary>
        /// <returns>Danh sách WorkerInfo</returns>
        public IEnumerable<WorkerInfo> GetAllWorkerInfos()
        {
            return _workerTypes.Select(kvp => new WorkerInfo
            {
                CommandType = kvp.Key,
                WorkerTypeName = kvp.Value.Name,
                IsAvailable = true
            });
        }
    }
}