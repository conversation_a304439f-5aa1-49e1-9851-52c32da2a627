/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Ad Review operations
    /// </summary>
    public interface IAdReviewApi
    {
        /// <summary>
        /// Get review information for ad groups. An ad group needs to be approved before it can be deployed,
        /// and it may get rejected due to various reasons, such as incorrect placement or targeting selections.
        /// Based on the rejection reasons and suggestions you get, you can adjust your ad group accordingly.
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="adGroupIds">List of ad group IDs (maximum 20)</param>
        /// <param name="language">Language for the response (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad group review information response</returns>
        Task<AdGroupReviewInfoResponse> GetAdGroupReviewInfoAsync(
            string advertiserId,
            List<string> adGroupIds,
            string? language = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get review information for ads.
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="adIds">List of ad IDs (1-100)</param>
        /// <param name="language">Language for the response (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad review information response</returns>
        Task<AdReviewInfoResponse> GetAdReviewInfoAsync(
            string advertiserId,
            List<string> adIds,
            string? language = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Appeal the rejection decision for an ad group, requesting re-evaluation when your ad group is rejected during review.
        /// After the appeal, you need to wait for some time and call the GetAdGroupReviewInfoAsync method to check the appeal status
        /// and see if the review status is updated.
        /// </summary>
        /// <param name="body">Appeal request body</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad group appeal response</returns>
        //Task<AdGroupAppealResponse> AppealAdGroupRejectionAsync(
        //    AdGroupAppealRequest body,
        //    CancellationToken cancellationToken = default);
    }
}
