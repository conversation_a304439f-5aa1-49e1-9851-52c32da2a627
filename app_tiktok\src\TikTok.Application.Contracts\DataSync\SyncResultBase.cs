﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Consts;

namespace TikTok.DataSync
{
    public class SyncResultBase
    {
        public string Code { get; set; } = TikTokApiCodes.Success.ToString();

        /// <summary>
        /// Thông báo lỗi (nếu có)
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// Thành công hay không
        /// </summary>
        public bool IsSuccess => string.IsNullOrEmpty(ErrorMessage);

        /// <summary>
        /// Tổng số bản ghi đã đồng bộ
        /// </summary>
        public virtual int TotalSynced { get; set; }

        /// <summary>
        /// Số bản ghi mới được thêm
        /// </summary>
        public int NewRecords { get; set; }

        /// <summary>
        /// Số bản ghi được cập nhật
        /// </summary>
        public int UpdatedRecords { get; set; }

        /// <summary>
        /// Số bản ghi lỗi
        /// </summary>
        public int ErrorRecords { get; set; }
    }
}
