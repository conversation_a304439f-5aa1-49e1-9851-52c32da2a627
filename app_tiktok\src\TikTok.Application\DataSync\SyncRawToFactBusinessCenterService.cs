using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;
using TikTok.Entities;
using TikTok.Repositories;
using TikTok.Enums;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu từ Raw sang Fact
    /// </summary>
    public class SyncRawToFactBusinessCenterService : ISyncRawToFactBusinessCenterService
    {
        private readonly ILogger<SyncRawToFactBusinessCenterService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private const int PAGE_SIZE = 200;

        public SyncRawToFactBusinessCenterService(
            ILogger<SyncRawToFactBusinessCenterService> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Đồng bộ Business Centers từ Raw sang Fact
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BusinessCenterSyncResult> SyncBusinessCentersAsync()
        {
            var result = new BusinessCenterSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ Business Centers từ Raw sang Fact...");

                using var scope = _serviceProvider.CreateScope();
                var rawBusinessCenterRepository = scope.ServiceProvider.GetRequiredService<IBusinessCenterRepository>();
                var dimBusinessCenterRepository = scope.ServiceProvider.GetRequiredService<IRepository<DimBusinessCenterEntity, Guid>>();
                var unitOfWorkManager = scope.ServiceProvider.GetRequiredService<IUnitOfWorkManager>();

                using var uow = unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);
                
                // Get all raw business centers
                var rawBusinessCenters = await rawBusinessCenterRepository.GetListAsync();
                _logger.LogDebug($"Found {rawBusinessCenters.Count} raw business centers to sync");

                var mapListRawBusinessCenterToDimBusinessCenter = MapListRawBusinessCenterToDimBusinessCenter(rawBusinessCenters);

                // Get all existing current dim business centers for comparison
                var existingDimBcs = await dimBusinessCenterRepository.GetListAsync(x => x.IsCurrent);
                var existingDimBcDict = existingDimBcs.ToDictionary(x => x.BcId, x => x);

                int createdCount = 0;
                int updatedCount = 0;

                var listInsert = new List<DimBusinessCenterEntity>();
                var listUpdate = new List<DimBusinessCenterEntity>();

                foreach (var dimBc in mapListRawBusinessCenterToDimBusinessCenter)
                {
                    try
                    {
                        // Find existing current record in dim table
                        existingDimBcDict.TryGetValue(dimBc.BcId, out var existingDimBc);

                        if (existingDimBc == null)
                        {
                            listInsert.Add(dimBc);
                            createdCount++;
                            _logger.LogDebug($"Created new dim business center record for BC ID: {dimBc.BcId}");
                        }
                        else
                        {
                            if (existingDimBc.HasChange(dimBc))
                            {
                                existingDimBc.UpdateFrom(dimBc);
                                listUpdate.Add(existingDimBc);
                                updatedCount++;
                                _logger.LogDebug($"Updated dim business center record for BC ID: {dimBc.BcId}");
                            }
                            else
                            {
                                _logger.LogDebug($"No changes found for BC ID: {dimBc.BcId}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error processing business center {dimBc.BcId}");
                        result.ErrorRecords++;
                    }
                }

                if (listInsert.Count > 0)
                {
                    await dimBusinessCenterRepository.InsertManyAsync(listInsert);
                }

                if (listUpdate.Count > 0)
                {
                    await dimBusinessCenterRepository.UpdateManyAsync(listUpdate);
                }

                await uow.CompleteAsync();

                result.BusinessCenterCount = createdCount + updatedCount;
                result.NewRecords = createdCount;
                result.UpdatedRecords = updatedCount;
                result.EndTime = DateTime.UtcNow;

                _logger.LogDebug($"Successfully synced business centers from Raw to Dim. Created: {createdCount}, Updated: {updatedCount}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing business centers from Raw to Dim");
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ Ad Accounts từ Raw sang Fact
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BusinessCenterSyncResult> SyncAdAccountsAsync()
        {
            var result = new BusinessCenterSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ Ad Accounts từ Raw sang Fact...");

                using var scope = _serviceProvider.CreateScope();
                var rawAdAccountRepository = scope.ServiceProvider.GetRequiredService<IAdAccountRepository>();
                var dimAdAccountRepository = scope.ServiceProvider.GetRequiredService<IRepository<DimAdAccountEntity, Guid>>();
                var unitOfWorkManager = scope.ServiceProvider.GetRequiredService<IUnitOfWorkManager>();

                int pageSize = PAGE_SIZE;
                int pageIndex = 0;
                int totalCreated = 0;
                int totalUpdated = 0;

                while (true)
                {
                    using var uow = unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);
                    try
                    {
                        var rawAdAccounts = await rawAdAccountRepository.GetListAsync(skipCount: pageIndex * pageSize, maxResultCount: pageSize);
                        if (rawAdAccounts.Count == 0)
                        {
                            break;
                        }

                        var mapListRawAdAccountToDimAdAccount = MapListRawAdAccountToDimAdAccount(rawAdAccounts);

                        var rawAdAccountIds = rawAdAccounts.Select(x => x.AdvertiserId).ToList();

                        var existingDimAdAccounts = await dimAdAccountRepository.GetListAsync(x => rawAdAccountIds.Contains(x.AdvertiserId));
                        var existingDimAdAccountDict = existingDimAdAccounts.ToDictionary(x => x.AdvertiserId, x => x);

                        var listInsert = new List<DimAdAccountEntity>();
                        var listUpdate = new List<DimAdAccountEntity>();

                        foreach (var dimAdAccount in mapListRawAdAccountToDimAdAccount)
                        {
                            try
                            {
                                existingDimAdAccountDict.TryGetValue(dimAdAccount.AdvertiserId, out var existingDimAdAccount);

                                if (existingDimAdAccount == null)
                                {
                                    listInsert.Add(dimAdAccount);
                                    _logger.LogDebug($"Created new dim ad account record for Advertiser ID: {dimAdAccount.AdvertiserId}");
                                }
                                else
                                {
                                    if (existingDimAdAccount.HasChange(dimAdAccount))
                                    {
                                        existingDimAdAccount.UpdateFrom(dimAdAccount);
                                        listUpdate.Add(existingDimAdAccount);
                                        _logger.LogDebug($"Updated dim ad account record for Advertiser ID: {dimAdAccount.AdvertiserId}");
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, $"Error processing ad account {dimAdAccount.AdvertiserId}");
                                result.ErrorRecords++;
                            }
                        }

                        if (listInsert.Count > 0)
                        {
                            await dimAdAccountRepository.InsertManyAsync(listInsert);
                            totalCreated += listInsert.Count;
                        }

                        if (listUpdate.Count > 0)
                        {
                            await dimAdAccountRepository.UpdateManyAsync(listUpdate);
                            totalUpdated += listUpdate.Count;
                        }

                        await uow.CompleteAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error syncing ad accounts from Raw to Dim");
                        result.ErrorRecords++;
                    }

                    pageIndex++;
                }

                result.AdAccountCount = totalCreated + totalUpdated;
                result.NewRecords = totalCreated;
                result.UpdatedRecords = totalUpdated;
                result.EndTime = DateTime.UtcNow;

                _logger.LogDebug($"Successfully synced ad accounts from Raw to Dim. Created: {totalCreated}, Updated: {totalUpdated}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing ad accounts from Raw to Dim");
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả dữ liệu từ Raw sang Fact
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BusinessCenterSyncResult> SyncAllAsync()
        {
            var result = new BusinessCenterSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả dữ liệu từ Raw sang Fact...");

                // Sync Business Centers
                var bcResult = await SyncBusinessCentersAsync();
                result.BusinessCenterCount = bcResult.BusinessCenterCount;
                result.NewRecords += bcResult.NewRecords;
                result.UpdatedRecords += bcResult.UpdatedRecords;
                result.ErrorRecords += bcResult.ErrorRecords;

                // Sync Ad Accounts
                var adAccountResult = await SyncAdAccountsAsync();
                result.AdAccountCount = adAccountResult.AdAccountCount;
                result.NewRecords += adAccountResult.NewRecords;
                result.UpdatedRecords += adAccountResult.UpdatedRecords;
                result.ErrorRecords += adAccountResult.ErrorRecords;

                result.EndTime = DateTime.UtcNow;

                _logger.LogDebug($"Successfully synced all data from Raw to Fact. BC: {result.BusinessCenterCount}, AdAccounts: {result.AdAccountCount}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing all data from Raw to Fact");
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ Business Centers từ Raw sang Fact theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BusinessCenterSyncResult> SyncBusinessCentersAsync(string bcId)
        {
            var result = new BusinessCenterSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ Business Center từ Raw sang Fact cho BC ID: {BcId}", bcId);

                using var scope = _serviceProvider.CreateScope();
                var rawBusinessCenterRepository = scope.ServiceProvider.GetRequiredService<IBusinessCenterRepository>();
                var dimBusinessCenterRepository = scope.ServiceProvider.GetRequiredService<IRepository<DimBusinessCenterEntity, Guid>>();
                var unitOfWorkManager = scope.ServiceProvider.GetRequiredService<IUnitOfWorkManager>();

                using var uow = unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);
                
                // Get specific raw business center by BC ID
                var rawBusinessCenters = await rawBusinessCenterRepository.GetListAsync(x => x.BcId == bcId);
                if (rawBusinessCenters.Count == 0)
                {
                    _logger.LogWarning("Không tìm thấy Business Center với BC ID: {BcId}", bcId);
                    result.ErrorMessage = $"Business Center with ID {bcId} not found";
                    result.EndTime = DateTime.UtcNow;
                    return result;
                }

                _logger.LogDebug($"Found {rawBusinessCenters.Count} raw business center(s) to sync for BC ID: {bcId}");

                var mapListRawBusinessCenterToDimBusinessCenter = MapListRawBusinessCenterToDimBusinessCenter(rawBusinessCenters);

                // Get existing current dim business center for comparison
                var existingDimBc = await dimBusinessCenterRepository.FirstOrDefaultAsync(x => x.BcId == bcId && x.IsCurrent);

                int createdCount = 0;
                int updatedCount = 0;

                var listInsert = new List<DimBusinessCenterEntity>();
                var listUpdate = new List<DimBusinessCenterEntity>();

                foreach (var dimBc in mapListRawBusinessCenterToDimBusinessCenter)
                {
                    try
                    {
                        if (existingDimBc == null)
                        {
                            listInsert.Add(dimBc);
                            createdCount++;
                            _logger.LogDebug($"Created new dim business center record for BC ID: {dimBc.BcId}");
                        }
                        else
                        {
                            if (existingDimBc.HasChange(dimBc))
                            {
                                existingDimBc.UpdateFrom(dimBc);
                                listUpdate.Add(existingDimBc);
                                updatedCount++;
                                _logger.LogDebug($"Updated dim business center record for BC ID: {dimBc.BcId}");
                            }
                            else
                            {
                                _logger.LogDebug($"No changes found for BC ID: {dimBc.BcId}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error processing business center {dimBc.BcId}");
                        result.ErrorRecords++;
                    }
                }

                if (listInsert.Count > 0)
                {
                    await dimBusinessCenterRepository.InsertManyAsync(listInsert);
                }

                if (listUpdate.Count > 0)
                {
                    await dimBusinessCenterRepository.UpdateManyAsync(listUpdate);
                }

                await uow.CompleteAsync();

                result.BusinessCenterCount = createdCount + updatedCount;
                result.NewRecords = createdCount;
                result.UpdatedRecords = updatedCount;
                result.EndTime = DateTime.UtcNow;

                _logger.LogDebug($"Successfully synced business center from Raw to Dim for BC ID: {bcId}. Created: {createdCount}, Updated: {updatedCount}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing business center from Raw to Dim for BC ID: {BcId}", bcId);
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ Ad Accounts từ Raw sang Fact theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BusinessCenterSyncResult> SyncAdAccountsAsync(string bcId)
        {
            var result = new BusinessCenterSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ Ad Accounts từ Raw sang Fact cho BC ID: {BcId}", bcId);

                using var scope = _serviceProvider.CreateScope();
                var rawAdAccountRepository = scope.ServiceProvider.GetRequiredService<IAdAccountRepository>();
                var dimAdAccountRepository = scope.ServiceProvider.GetRequiredService<IRepository<DimAdAccountEntity, Guid>>();
                var unitOfWorkManager = scope.ServiceProvider.GetRequiredService<IUnitOfWorkManager>();

                using var uow = unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);
                
                // Get ad accounts for specific BC ID
                var rawAdAccounts = await rawAdAccountRepository.GetListAsync(x => x.OwnerBcId == bcId);
                if (rawAdAccounts.Count == 0)
                {
                    _logger.LogWarning("Không tìm thấy Ad Account nào cho BC ID: {BcId}", bcId);
                    result.EndTime = DateTime.UtcNow;
                    return result;
                }

                _logger.LogDebug($"Found {rawAdAccounts.Count} raw ad account(s) to sync for BC ID: {bcId}");

                var mapListRawAdAccountToDimAdAccount = MapListRawAdAccountToDimAdAccount(rawAdAccounts);

                var rawAdAccountIds = rawAdAccounts.Select(x => x.AdvertiserId).ToList();

                var existingDimAdAccounts = await dimAdAccountRepository.GetListAsync(x => rawAdAccountIds.Contains(x.AdvertiserId));
                var existingDimAdAccountDict = existingDimAdAccounts.ToDictionary(x => x.AdvertiserId, x => x);

                var listInsert = new List<DimAdAccountEntity>();
                var listUpdate = new List<DimAdAccountEntity>();

                foreach (var dimAdAccount in mapListRawAdAccountToDimAdAccount)
                {
                    try
                    {
                        existingDimAdAccountDict.TryGetValue(dimAdAccount.AdvertiserId, out var existingDimAdAccount);

                        if (existingDimAdAccount == null)
                        {
                            listInsert.Add(dimAdAccount);
                            _logger.LogDebug($"Created new dim ad account record for Advertiser ID: {dimAdAccount.AdvertiserId}");
                        }
                        else
                        {
                            if (existingDimAdAccount.HasChange(dimAdAccount))
                            {
                                existingDimAdAccount.UpdateFrom(dimAdAccount);
                                listUpdate.Add(existingDimAdAccount);
                                _logger.LogDebug($"Updated dim ad account record for Advertiser ID: {dimAdAccount.AdvertiserId}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error processing ad account {dimAdAccount.AdvertiserId}");
                        result.ErrorRecords++;
                    }
                }

                if (listInsert.Count > 0)
                {
                    await dimAdAccountRepository.InsertManyAsync(listInsert);
                }

                if (listUpdate.Count > 0)
                {
                    await dimAdAccountRepository.UpdateManyAsync(listUpdate);
                }

                await uow.CompleteAsync();

                result.AdAccountCount = listInsert.Count + listUpdate.Count;
                result.NewRecords = listInsert.Count;
                result.UpdatedRecords = listUpdate.Count;
                result.EndTime = DateTime.UtcNow;

                _logger.LogDebug($"Successfully synced ad accounts from Raw to Dim for BC ID: {bcId}. Created: {listInsert.Count}, Updated: {listUpdate.Count}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing ad accounts from Raw to Dim for BC ID: {BcId}", bcId);
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả dữ liệu từ Raw sang Fact theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BusinessCenterSyncResult> SyncAllAsync(string bcId)
        {
            var result = new BusinessCenterSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả dữ liệu từ Raw sang Fact cho BC ID: {BcId}", bcId);

                // Sync Business Centers
                var bcResult = await SyncBusinessCentersAsync(bcId);
                result.BusinessCenterCount = bcResult.BusinessCenterCount;
                result.NewRecords += bcResult.NewRecords;
                result.UpdatedRecords += bcResult.UpdatedRecords;
                result.ErrorRecords += bcResult.ErrorRecords;

                // Sync Ad Accounts
                var adAccountResult = await SyncAdAccountsAsync(bcId);
                result.AdAccountCount = adAccountResult.AdAccountCount;
                result.NewRecords += adAccountResult.NewRecords;
                result.UpdatedRecords += adAccountResult.UpdatedRecords;
                result.ErrorRecords += adAccountResult.ErrorRecords;

                result.EndTime = DateTime.UtcNow;

                _logger.LogDebug($"Successfully synced all data from Raw to Fact for BC ID: {bcId}. BC: {result.BusinessCenterCount}, AdAccounts: {result.AdAccountCount}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing all data from Raw to Fact for BC ID: {BcId}", bcId);
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// Map danh sách Raw Business Center sang Dim Business Center
        /// </summary>
        /// <param name="rawBusinessCenters">Danh sách Raw Business Centers</param>
        /// <returns>Danh sách Dim Business Centers</returns>
        private List<DimBusinessCenterEntity> MapListRawBusinessCenterToDimBusinessCenter(List<RawBusinessCenterEntity> rawBusinessCenters)
        {
            return rawBusinessCenters.Select(x => new DimBusinessCenterEntity(Guid.NewGuid())
            {
                BcId = x.BcId,
                BcName = x.Name,
                Company = x.Company,
                Currency = x.Currency,
                RegisteredArea = x.RegisteredArea,
                Status = x.Status.ToString(),
                Timezone = x.Timezone,
                Type = x.Type.ToString(),
                UserRole = x.UserRole.ToString(),
                ExtUserFinanceRole = x.ExtUserFinanceRole?.ToString(),
                IsCurrent = true,
                ExpirationDate = null,
                RowVersion = 1
            }).ToList();
        }

        /// <summary>
        /// Map danh sách Raw Ad Account sang Dim Ad Account
        /// </summary>
        /// <param name="rawAdAccounts">Danh sách Raw Ad Accounts</param>
        /// <returns>Danh sách Dim Ad Accounts</returns>
        private List<DimAdAccountEntity> MapListRawAdAccountToDimAdAccount(List<RawAdAccountEntity> rawAdAccounts)
        {
            return rawAdAccounts.Select(x => new DimAdAccountEntity(Guid.NewGuid())
            {
                AdvertiserId = x.AdvertiserId,
                AdvertiserName = x.Name,
                Currency = x.Currency ?? string.Empty,
                Timezone = x.Timezone ?? string.Empty,
                Country = x.Country??string.Empty,
                Status = x.Status.ToString(),
                Company = x.Company,
                OwnerBcId = x.OwnerBcId,
                IsActive = !x.IsRemoved,
                ExpiryDate = null,
                IsCurrent = true,
                RowVersion = 1,
                
            }).ToList();
        }
    }
}
