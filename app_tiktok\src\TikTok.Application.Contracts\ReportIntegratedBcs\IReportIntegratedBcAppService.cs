using System;
using System.Threading.Tasks;
using TikTok.ReportIntegratedBcs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.ReportIntegratedBcs
{
    /// <summary>
    /// Interface service cho báo cáo tích hợp Business Center
    /// </summary>
    public interface IReportIntegratedBcAppService :
        ICrudAppService<
            ReportIntegratedBcDto,
            Guid,
            GetReportIntegratedBcListDto,
            CreateReportIntegratedBcDto,
            UpdateReportIntegratedBcDto>
    {
        /// <summary>
        /// Lấy danh sách báo cáo tích hợp BC theo điều kiện
        /// </summary>
        /// <param name="input">Điều kiện tìm kiếm</param>
        /// <returns>Danh sách báo cáo tích hợp BC</returns>
        Task<PagedResultDto<ReportIntegratedBcDto>> GetListAsync(GetReportIntegratedBcListDto input);

        /// <summary>
        /// Lấy báo cáo theo BC ID và ngày
        /// </summary>
        /// <param name="bcId">ID Business Center</param>
        /// <param name="date">Ngày báo cáo</param>
        /// <returns>Báo cáo tích hợp BC</returns>
        Task<ReportIntegratedBcDto?> GetByBcIdAndDateAsync(string bcId, DateTime date);

        /// <summary>
        /// Lấy danh sách báo cáo theo BC ID
        /// </summary>
        /// <param name="bcId">ID Business Center</param>
        /// <returns>Danh sách báo cáo tích hợp BC</returns>
        Task<PagedResultDto<ReportIntegratedBcDto>> GetByBcIdAsync(string bcId);
    }
}