using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TikTok.AdGroups
{
    /// <summary>
    /// DTO để cập nhật nhóm quảng cáo
    /// </summary>
    public class UpdateAdGroupDto
    {
        /// <summary>
        /// ID nhóm quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdgroupId { get; set; }

        /// <summary>
        /// Tên nhóm quảng cáo
        /// </summary>
        [StringLength(255)]
        public string AdgroupName { get; set; }

        /// <summary>
        /// Chế độ ngân sách
        /// </summary>
        [StringLength(50)]
        public string BudgetMode { get; set; }

        /// <summary>
        /// Ngân sách nhóm quảng cáo
        /// </summary>
        public decimal? Budget { get; set; }

        /// <summary>
        /// Ngân sách quảng cáo được lên lịch cho ngày hôm sau
        /// </summary>
        public decimal? ScheduledBudget { get; set; }

        /// <summary>
        /// Loại lịch trình
        /// </summary>
        [StringLength(50)]
        public string ScheduleType { get; set; }

        /// <summary>
        /// Thời gian bắt đầu phân phối quảng cáo
        /// </summary>
        public DateTime? ScheduleStartTime { get; set; }

        /// <summary>
        /// Thời gian kết thúc phân phối quảng cáo
        /// </summary>
        public DateTime? ScheduleEndTime { get; set; }

        /// <summary>
        /// Loại phân phối cho ngày
        /// </summary>
        [StringLength(1000)]
        public string Dayparting { get; set; }

        /// <summary>
        /// Mục tiêu tối ưu hóa
        /// </summary>
        [StringLength(100)]
        public string OptimizationGoal { get; set; }

        /// <summary>
        /// Mục tiêu thứ cấp khi optimization_goal là INSTALL hoặc VALUE
        /// </summary>
        [StringLength(100)]
        public string SecondaryOptimizationEvent { get; set; }

        /// <summary>
        /// Tần suất
        /// </summary>
        public int? Frequency { get; set; }

        /// <summary>
        /// Lịch trình tần suất
        /// </summary>
        public int? FrequencySchedule { get; set; }

        /// <summary>
        /// Chiến lược đặt giá thầu
        /// </summary>
        [StringLength(50)]
        public string BidType { get; set; }

        /// <summary>
        /// Giá thầu
        /// </summary>
        public decimal? BidPrice { get; set; }

        /// <summary>
        /// Chi phí mục tiêu cho mỗi chuyển đổi cho oCPM
        /// </summary>
        public decimal? ConversionBidPrice { get; set; }

        /// <summary>
        /// Chiến lược đặt giá thầu cho sự kiện trong ứng dụng
        /// </summary>
        [StringLength(50)]
        public string DeepBidType { get; set; }

        /// <summary>
        /// Mục tiêu ROAS cho Tối ưu hóa giá trị
        /// </summary>
        public decimal? RoasBid { get; set; }

        /// <summary>
        /// Cửa sổ thời gian của chiến lược đặt giá thầu chỉ định cho VBO
        /// </summary>
        [StringLength(50)]
        public string VboWindow { get; set; }

        /// <summary>
        /// Cách tính toán và đo lường Cost per View
        /// </summary>
        [StringLength(50)]
        public string BidDisplayMode { get; set; }

        /// <summary>
        /// Giá thầu Deep CPA
        /// </summary>
        public decimal? DeepCpaBid { get; set; }

        /// <summary>
        /// Thời lượng phát video được tối ưu hóa
        /// </summary>
        [StringLength(50)]
        public string CpvVideoDuration { get; set; }

        /// <summary>
        /// Tỷ lệ giữ chân ngày 2
        /// </summary>
        public decimal? NextDayRetention { get; set; }

        /// <summary>
        /// Cửa sổ attribution nhấp chuột cho nhóm quảng cáo
        /// </summary>
        [StringLength(50)]
        public string ClickAttributionWindow { get; set; }

        /// <summary>
        /// Cửa sổ attribution engaged view-through cho nhóm quảng cáo
        /// </summary>
        [StringLength(50)]
        public string EngagedViewAttributionWindow { get; set; }

        /// <summary>
        /// Cửa sổ attribution view-through cho nhóm quảng cáo
        /// </summary>
        [StringLength(50)]
        public string ViewAttributionWindow { get; set; }

        /// <summary>
        /// Số lượng sự kiện (Loại thống kê) cho nhóm quảng cáo
        /// </summary>
        [StringLength(50)]
        public string AttributionEventCount { get; set; }

        /// <summary>
        /// Sự kiện tính phí
        /// </summary>
        [StringLength(50)]
        public string BillingEvent { get; set; }

        /// <summary>
        /// Tốc độ
        /// </summary>
        [StringLength(50)]
        public string Pacing { get; set; }

        /// <summary>
        /// Loại thống kê giá thầu chuyển đổi
        /// </summary>
        [StringLength(50)]
        public string StatisticType { get; set; }

        /// <summary>
        /// Chiến lược phân phối creative
        /// </summary>
        [StringLength(50)]
        public string CreativeMaterialMode { get; set; }

        /// <summary>
        /// Tùy chọn loại feed
        /// </summary>
        [StringLength(50)]
        public string FeedType { get; set; }

        /// <summary>
        /// Có bỏ qua giai đoạn học hay không
        /// </summary>
        public bool? SkipLearningPhase { get; set; }

        /// <summary>
        /// ID các vị trí được nhắm mục tiêu
        /// </summary>
        public List<string> LocationIds { get; set; }

        /// <summary>
        /// ID mã ZIP hoặc mã bưu điện của các vị trí được nhắm mục tiêu
        /// </summary>
        public List<string> ZipcodeIds { get; set; }

        /// <summary>
        /// Mã ngôn ngữ bạn muốn nhắm mục tiêu
        /// </summary>
        public List<string> Languages { get; set; }

        /// <summary>
        /// Giới tính bạn muốn nhắm mục tiêu
        /// </summary>
        [StringLength(20)]
        public string Gender { get; set; }

        /// <summary>
        /// Nhóm tuổi bạn muốn nhắm mục tiêu
        /// </summary>
        public List<string> AgeGroups { get; set; }

        /// <summary>
        /// Sức mua bạn muốn nhắm mục tiêu
        /// </summary>
        [StringLength(50)]
        public string SpendingPower { get; set; }

        /// <summary>
        /// Thu nhập hộ gia đình bạn muốn nhắm mục tiêu
        /// </summary>
        public List<string> HouseholdIncome { get; set; }

        /// <summary>
        /// Danh sách ID đối tượng
        /// </summary>
        public List<string> AudienceIds { get; set; }

        /// <summary>
        /// Có bật Smart audience hay không
        /// </summary>
        public bool? SmartAudienceEnabled { get; set; }

        /// <summary>
        /// Danh sách ID đối tượng loại trừ
        /// </summary>
        public List<string> ExcludedAudienceIds { get; set; }

        /// <summary>
        /// ID từ khóa sở thích chung để nhắm mục tiêu đối tượng
        /// </summary>
        public List<string> InterestCategoryIds { get; set; }

        /// <summary>
        /// ID từ khóa sở thích bổ sung để nhắm mục tiêu đối tượng
        /// </summary>
        public List<string> InterestKeywordIds { get; set; }

        /// <summary>
        /// ID danh mục ý định mua hàng
        /// </summary>
        public List<string> PurchaseIntentionKeywordIds { get; set; }

        /// <summary>
        /// Có bật Smart interests & behaviors hay không
        /// </summary>
        public bool? SmartInterestBehaviorEnabled { get; set; }

        /// <summary>
        /// Constructor mặc định
        /// </summary>
        public UpdateAdGroupDto()
        {
            LocationIds = new List<string>();
            ZipcodeIds = new List<string>();
            Languages = new List<string>();
            AgeGroups = new List<string>();
            HouseholdIncome = new List<string>();
            AudienceIds = new List<string>();
            ExcludedAudienceIds = new List<string>();
            InterestCategoryIds = new List<string>();
            InterestKeywordIds = new List<string>();
            PurchaseIntentionKeywordIds = new List<string>();
        }
    }
}