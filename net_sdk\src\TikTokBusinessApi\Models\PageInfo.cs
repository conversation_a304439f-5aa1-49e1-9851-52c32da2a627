/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Pagination information for API responses
    /// </summary>
    public class PageInfo
    {
        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Paging size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Total number of results
        /// </summary>
        [JsonPropertyName("total_number")]
        public int? TotalNumber { get; set; }

        /// <summary>
        /// Total pages of results
        /// </summary>
        [JsonPropertyName("total_page")]
        public int? TotalPage { get; set; }

        /// <summary>
        /// Initializes a new instance of the PageInfo class
        /// </summary>
        public PageInfo()
        {
        }

        /// <summary>
        /// Initializes a new instance of the PageInfo class with parameters
        /// </summary>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="totalNumber">Total number of results</param>
        /// <param name="totalPage">Total pages</param>
        public PageInfo(int? page = null, int? pageSize = null, int? totalNumber = null, int? totalPage = null)
        {
            Page = page;
            PageSize = pageSize;
            TotalNumber = totalNumber;
            TotalPage = totalPage;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"PageInfo(Page={Page}, PageSize={PageSize}, TotalNumber={TotalNumber}, TotalPage={TotalPage})";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not PageInfo other)
                return false;

            return Page == other.Page &&
                   PageSize == other.PageSize &&
                   TotalNumber == other.TotalNumber &&
                   TotalPage == other.TotalPage;
        }

        /// <summary>
        /// Returns the hash code for this instance
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(Page, PageSize, TotalNumber, TotalPage);
        }
    }
}
