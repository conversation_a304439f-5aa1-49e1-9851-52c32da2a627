/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Net.Http;

namespace TikTokBusinessApi.Auth
{
    /// <summary>
    /// API Key authentication implementation
    /// </summary>
    public class ApiKeyAuth : IAuthentication
    {
        /// <summary>
        /// Location of the API key (header or query)
        /// </summary>
        public enum KeyLocation
        {
            Header,
            Query
        }

        /// <summary>
        /// Location where the API key should be placed
        /// </summary>
        public KeyLocation Location { get; set; }

        /// <summary>
        /// Parameter name for the API key
        /// </summary>
        public string ParameterName { get; set; }

        /// <summary>
        /// API key value
        /// </summary>
        public string? ApiKey { get; set; }

        /// <summary>
        /// Initializes a new instance of the ApiKeyAuth class
        /// </summary>
        /// <param name="location">Location of the API key</param>
        /// <param name="parameterName">Parameter name</param>
        public ApiKeyAuth(KeyLocation location, string parameterName)
        {
            Location = location;
            ParameterName = parameterName ?? throw new ArgumentNullException(nameof(parameterName));
        }

        /// <summary>
        /// Initializes a new instance of the ApiKeyAuth class with an API key
        /// </summary>
        /// <param name="location">Location of the API key</param>
        /// <param name="parameterName">Parameter name</param>
        /// <param name="apiKey">API key value</param>
        public ApiKeyAuth(KeyLocation location, string parameterName, string apiKey)
            : this(location, parameterName)
        {
            ApiKey = apiKey;
        }

        /// <summary>
        /// Applies API key authentication to the HTTP request
        /// </summary>
        /// <param name="request">HTTP request message</param>
        public void ApplyToRequest(HttpRequestMessage request)
        {
            if (string.IsNullOrEmpty(ApiKey))
                return;

            switch (Location)
            {
                case KeyLocation.Header:
                    request.Headers.TryAddWithoutValidation(ParameterName, ApiKey);
                    break;
                case KeyLocation.Query:
                    // For query parameters, we need to modify the URI
                    var uriBuilder = new UriBuilder(request.RequestUri!);
                    var query = uriBuilder.Query;
                    if (string.IsNullOrEmpty(query))
                    {
                        uriBuilder.Query = $"{ParameterName}={Uri.EscapeDataString(ApiKey)}";
                    }
                    else
                    {
                        uriBuilder.Query = query.TrimStart('?') + $"&{ParameterName}={Uri.EscapeDataString(ApiKey)}";
                    }
                    request.RequestUri = uriBuilder.Uri;
                    break;
            }
        }
    }
}
