/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using TikTokBusinessApi.Utilities;
using Xunit;

namespace TikTokBusinessApi.Tests.Utilities
{
    public class RateLimiterTests : IDisposable
    {
        private readonly Mock<ILogger<RateLimiter>> _mockLogger;
        private readonly RateLimiter _rateLimiter;

        public RateLimiterTests()
        {
            _mockLogger = new Mock<ILogger<RateLimiter>>();
            _rateLimiter = new RateLimiter(_mockLogger.Object);
        }

        [Fact]
        public async Task WaitForPermissionAsync_WithValidParameters_ShouldAllowRequest()
        {
            // Arrange
            var key = "test-key";
            var maxRequests = 5;
            var timeWindow = TimeSpan.FromSeconds(1);

            // Act & Assert
            await _rateLimiter.WaitForPermissionAsync(key, maxRequests, timeWindow);
            // Should complete without throwing
        }

        [Fact]
        public async Task WaitForPermissionAsync_WithNullKey_ShouldThrowArgumentException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() =>
                _rateLimiter.WaitForPermissionAsync(null!, 5, TimeSpan.FromSeconds(1)));
        }

        [Fact]
        public async Task WaitForPermissionAsync_WithZeroMaxRequests_ShouldThrowArgumentException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() =>
                _rateLimiter.WaitForPermissionAsync("test", 0, TimeSpan.FromSeconds(1)));
        }

        [Fact]
        public async Task WaitForPermissionAsync_WithZeroTimeWindow_ShouldThrowArgumentException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() =>
                _rateLimiter.WaitForPermissionAsync("test", 5, TimeSpan.Zero));
        }

        [Fact]
        public void IsRequestAllowed_WithinLimit_ShouldReturnTrue()
        {
            // Arrange
            var key = "test-key";
            var maxRequests = 5;
            var timeWindow = TimeSpan.FromSeconds(1);

            // Act
            var result = _rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void IsRequestAllowed_ExceedingLimit_ShouldReturnFalse()
        {
            // Arrange
            var key = "test-key";
            var maxRequests = 2;
            var timeWindow = TimeSpan.FromSeconds(10); // Long window to ensure no refill

            // Act - consume all tokens
            _rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow);
            _rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow);
            var result = _rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void GetStatus_WithExistingKey_ShouldReturnStatus()
        {
            // Arrange
            var key = "test-key";
            var maxRequests = 5;
            var timeWindow = TimeSpan.FromSeconds(1);

            // Act
            _rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow);
            var status = _rateLimiter.GetStatus(key);

            // Assert
            Assert.NotNull(status);
            Assert.Equal(key, status.Key);
            Assert.True(status.AvailableTokens >= 0);
        }

        [Fact]
        public void GetStatus_WithNonExistentKey_ShouldReturnEmptyStatus()
        {
            // Arrange
            var key = "non-existent-key";

            // Act
            var status = _rateLimiter.GetStatus(key);

            // Assert
            Assert.NotNull(status);
            Assert.Equal(key, status.Key);
            Assert.Equal(0, status.AvailableTokens);
        }

        [Fact]
        public void Reset_WithExistingKey_ShouldResetLimiter()
        {
            // Arrange
            var key = "test-key";
            var maxRequests = 2;
            var timeWindow = TimeSpan.FromSeconds(10);

            // Consume all tokens
            _rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow);
            _rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow);
            Assert.False(_rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow));

            // Act
            _rateLimiter.Reset(key);

            // Assert
            Assert.True(_rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow));
        }

        [Fact]
        public void ResetAll_ShouldResetAllLimiters()
        {
            // Arrange
            var key1 = "test-key-1";
            var key2 = "test-key-2";
            var maxRequests = 1;
            var timeWindow = TimeSpan.FromSeconds(10);

            // Consume tokens for both keys
            _rateLimiter.IsRequestAllowed(key1, maxRequests, timeWindow);
            _rateLimiter.IsRequestAllowed(key2, maxRequests, timeWindow);
            Assert.False(_rateLimiter.IsRequestAllowed(key1, maxRequests, timeWindow));
            Assert.False(_rateLimiter.IsRequestAllowed(key2, maxRequests, timeWindow));

            // Act
            _rateLimiter.ResetAll();

            // Assert
            Assert.True(_rateLimiter.IsRequestAllowed(key1, maxRequests, timeWindow));
            Assert.True(_rateLimiter.IsRequestAllowed(key2, maxRequests, timeWindow));
        }

        [Fact]
        public async Task WaitForPermissionAsync_WithCancellation_ShouldThrowOperationCanceledException()
        {
            // Arrange
            var key = "test-key";
            var maxRequests = 1;
            var timeWindow = TimeSpan.FromSeconds(10);
            var cts = new CancellationTokenSource();

            // Consume the only token
            _rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow);

            // Act & Assert
            cts.Cancel();
            await Assert.ThrowsAsync<OperationCanceledException>(() =>
                _rateLimiter.WaitForPermissionAsync(key, maxRequests, timeWindow, cts.Token));
        }

        [Fact]
        public async Task MultipleKeys_ShouldBeIndependent()
        {
            // Arrange
            var key1 = "key-1";
            var key2 = "key-2";
            var maxRequests = 1;
            var timeWindow = TimeSpan.FromSeconds(10);

            // Act
            var result1 = _rateLimiter.IsRequestAllowed(key1, maxRequests, timeWindow);
            var result2 = _rateLimiter.IsRequestAllowed(key2, maxRequests, timeWindow);
            var result3 = _rateLimiter.IsRequestAllowed(key1, maxRequests, timeWindow);
            var result4 = _rateLimiter.IsRequestAllowed(key2, maxRequests, timeWindow);

            // Assert
            Assert.True(result1); // First request for key1 should succeed
            Assert.True(result2); // First request for key2 should succeed
            Assert.False(result3); // Second request for key1 should fail
            Assert.False(result4); // Second request for key2 should fail
        }

        [Fact]
        public async Task TokenRefill_ShouldAllowNewRequests()
        {
            // Arrange
            var key = "test-key";
            var maxRequests = 1;
            var timeWindow = TimeSpan.FromMilliseconds(100); // Short window for quick test

            // Consume the token
            Assert.True(_rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow));
            Assert.False(_rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow));

            // Act - wait for refill
            await Task.Delay(timeWindow.Add(TimeSpan.FromMilliseconds(50)));

            // Assert
            Assert.True(_rateLimiter.IsRequestAllowed(key, maxRequests, timeWindow));
        }

        public void Dispose()
        {
            _rateLimiter?.Dispose();
        }
    }
}
