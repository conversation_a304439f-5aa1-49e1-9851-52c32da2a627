# RawGmvMaxProductDetailProductReportEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawGmvMaxProductDetailProductReports`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawGmvMaxProductDetailProductReports`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho báo cáo chi tiết cấp sản phẩm của Product GMV Max Campaign. Lưu trữ thông tin chi tiết về hiệu suất từng sản phẩm trong chiến dịch Product GMV Max, bao gồm thông tin sản phẩm, trạng thái và các metrics hiệu suất theo thời gian.

## Bảng dữ liệu: RawGmvMaxProductDetailProductReports

| Tên Field                       | Kiểu dữ liệu              | <PERSON><PERSON><PERSON> bu<PERSON> | <PERSON><PERSON> dài | <PERSON><PERSON> tả                                                                    |
| ------------------------------- | ------------------------- | -------- | ------ | ------------------------------------------------------------------------ |
| Id                              | Guid                      | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                   |
| BcId                            | string                    | ✅       | 100    | ID Business Center                                                       |
| AdvertiserId                    | string                    | ✅       | 100    | ID nhà quảng cáo                                                         |
| StoreId                         | string                    | ✅       | 100    | ID TikTok Shop                                                           |
| CampaignId                      | string                    | ✅       | 100    | ID chiến dịch Product GMV Max                                            |
| ItemGroupId                     | string                    | ✅       | 100    | ID sản phẩm SPU                                                          |
| ProductName                     | string                    | ❌       | 500    | Tên sản phẩm                                                             |
| ProductImageUrl                 | string                    | ❌       | 1000   | URL hình ảnh sản phẩm                                                    |
| ProductStatus                   | ProductStatus?            | ❌       | -      | Trạng thái sản phẩm (available, unavailable)                            |
| BidType                         | ProductCampaignBidType?   | ❌       | -      | Chế độ tối ưu (CUSTOM: Target ROI, NO_BID: Maximum delivery)             |
| Orders                          | int?                      | ❌       | -      | Số lượng đơn hàng SKU cá nhân cho sản phẩm này                           |
| GrossRevenue                    | decimal?                  | ❌       | -      | Tổng doanh thu gộp từ sản phẩm này                                       |
| Currency                        | string                    | ✅       | 10     | Tiền tệ - Mã tiền tệ, ví dụ: USD                                         |
| Date                            | DateTime                  | ✅       | -      | Ngày giờ tổng hợp báo cáo, UTC format yyyy-MM-dd HH:mm:ss (theo giờ)    |
| CreationTime                    | DateTime                  | ✅       | -      | Thời gian tạo bản ghi (Audit)                                           |
| CreatorId                       | Guid?                     | ❌       | -      | ID người tạo (Audit)                                                     |
| LastModificationTime            | DateTime?                 | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                          |
| LastModifierId                  | Guid?                     | ❌       | -      | ID người sửa đổi cuối (Audit)                                           |

## Enums liên quan

### ProductStatus

-   **available**: Sản phẩm có sẵn
-   **unavailable**: Sản phẩm không có sẵn

### ProductCampaignBidType

-   **CUSTOM**: Target ROI - Tối ưu hóa theo mục tiêu ROI
-   **NO_BID**: Maximum delivery - Phân phối tối đa

## Cấu trúc dữ liệu

### Thông tin nhận dạng
-   **BcId**: ID Business Center quản lý
-   **AdvertiserId**: ID tài khoản nhà quảng cáo
-   **StoreId**: ID TikTok Shop liên kết
-   **CampaignId**: ID chiến dịch Product GMV Max
-   **ItemGroupId**: ID sản phẩm SPU (Stock Keeping Unit Group)

### Thông tin sản phẩm
-   **ProductName**: Tên hiển thị của sản phẩm
-   **ProductImageUrl**: URL hình ảnh đại diện sản phẩm
-   **ProductStatus**: Trạng thái hiện tại (có sẵn/không có sẵn)

### Cấu hình tối ưu hóa
-   **BidType**: Chế độ tối ưu hóa (Target ROI/Maximum delivery)

### Metrics hiệu suất
-   **Orders**: Số lượng đơn hàng SKU riêng lẻ
-   **GrossRevenue**: Tổng doanh thu gộp từ sản phẩm

### Thông tin báo cáo
-   **Currency**: Mã tiền tệ
-   **Date**: Thời điểm tổng hợp báo cáo

## Phương thức đặc biệt

### HasChanged()

```csharp
public bool HasChanged(RawGmvMaxProductDetailProductReportEntity other)
```

So sánh entity hiện tại với entity khác để phát hiện thay đổi. Bao gồm tất cả các trường nhận dạng, thông tin sản phẩm, cấu hình và metrics hiệu suất.

## Đặc điểm chính của Product Detail Product Report

### Individual Product Performance
-   **Product-Level Tracking**: Theo dõi hiệu suất từng sản phẩm riêng biệt
-   **SKU Granularity**: Phân tích cấp độ SKU chi tiết
-   **Product Attribution**: Gán đơn hàng cho sản phẩm cụ thể

### Product Management
-   **Status Monitoring**: Theo dõi trạng thái sản phẩm real-time
-   **Availability Tracking**: Quản lý tình trạng có sẵn
-   **Image Management**: Quản lý hình ảnh sản phẩm

### Performance Analytics
-   **Revenue Attribution**: Doanh thu được gán cho từng sản phẩm
-   **Order Volume**: Số lượng đơn hàng per sản phẩm
-   **Conversion Analysis**: Phân tích chuyển đổi sản phẩm

## Mục đích sử dụng

-   Báo cáo hiệu suất chi tiết từng sản phẩm trong campaign
-   Phân tích performance sản phẩm individual
-   So sánh hiệu suất giữa các sản phẩm khác nhau
-   Tối ưu hóa catalog sản phẩm cho GMV Max
-   Theo dõi trạng thái và availability sản phẩm
-   Hỗ trợ quyết định inventory và product mix

## Quan hệ với các entity khác

-   **RawGmvMaxProductCampaignReportEntity**: Many-to-One, thuộc về báo cáo campaign
-   **RawGmvMaxCampaignsEntity**: Many-to-One, thuộc về chiến dịch GMV Max
-   **RawGmvMaxProductCreativeReportEntity**: One-to-Many, sản phẩm có nhiều creative

## Use Cases thực tế

### Product Performance Analysis
```
Phân tích hiệu suất từng sản phẩm:
- So sánh Orders và GrossRevenue giữa các sản phẩm
- Xác định sản phẩm bestseller và underperformer
- Phân tích ProductStatus để quản lý inventory
```

### Product Catalog Optimization
```
Tối ưu hóa catalog sản phẩm:
- Loại bỏ sản phẩm có ProductStatus = unavailable
- Ưu tiên quảng cáo sản phẩm có GrossRevenue cao
- Điều chỉnh BidType dựa trên performance sản phẩm
```

### Inventory Management
```
Quản lý tồn kho dựa trên performance:
- Theo dõi Orders để dự báo nhu cầu
- Cập nhật ProductStatus khi hết hàng
- Tối ưu hóa ProductImageUrl cho sản phẩm hot
```

### Revenue Attribution
```
Phân bổ doanh thu theo sản phẩm:
- Tracking GrossRevenue per ItemGroupId
- Phân tích contribution margin của từng sản phẩm
- Optimization budget allocation dựa trên product performance
```