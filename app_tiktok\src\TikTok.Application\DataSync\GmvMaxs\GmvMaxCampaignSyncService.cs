using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.MessageProviders;
using TikTok.Application.MessageProviders;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.TikTokApiClients;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu GMV Max Campaign
    /// </summary>
    public class GmvMaxCampaignSyncService : BaseSyncService, IGmvMaxCampaignSyncService
    {
        private readonly IRawGmvMaxCampaignsRepository _gmvMaxCampaignsRepository;
        private readonly IAssetRepository _assetRepository;
        private readonly IBusinessCenterRepository _businessCenterRepository;
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="serviceProvider">Service provider</param>
        /// <param name="gmvMaxCampaignsRepository">GMV Max Campaigns repository</param>
        /// <param name="assetRepository">Asset repository</param>
        /// <param name="businessCenterRepository">Business Center repository</param>
        /// <param name="logger">Logger</param>
        public GmvMaxCampaignSyncService(
            IServiceProvider serviceProvider,
            IRawGmvMaxCampaignsRepository gmvMaxCampaignsRepository,
            IAssetRepository assetRepository,
            IBusinessCenterRepository businessCenterRepository,
            ILogger<GmvMaxCampaignSyncService> logger
            ) : base(serviceProvider, logger)
        {
            _gmvMaxCampaignsRepository = gmvMaxCampaignsRepository;
            _assetRepository = assetRepository;
            _businessCenterRepository = businessCenterRepository;
        }

        /// <summary>
        /// Đồng bộ GMV Max Campaign theo Advertiser ID và BC ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxCampaignSyncResult> SyncGmvMaxCampaignsAsync(string advertiserId, string bcId,List<string>? unauthorizedAdvertiserIds = null)
        {
            var result = new GmvMaxCampaignSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ GMV Max Campaign cho Advertiser: {AdvertiserId}", advertiserId);

                // Tạo TikTok client từ Singleton
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // Tạo filtering cho API
                var filtering = new GMVMaxCampaignGetFiltering()
                {
                    GMVMaxPromotionTypes = new List<string>
                    {
                        "PRODUCT_GMV_MAX","LIVE_GMV_MAX"
                    }
                };

                // Đồng bộ từng trang
                var page = 1;
                var pageSize = PAGE_SIZE_SYNC_DEFAULT;
                var hasMorePages = true;

                while (hasMorePages)
                {
                    try
                    {
                        // Gọi API lấy danh sách GMV Max Campaign
                        var response = await tikTokClient.GMVMax.GetCampaignsAsync(
                            advertiserId,
                            filtering,
                            fields: null,
                            page: page,
                            pageSize: pageSize);

                        if (!TikTokApiCodes.IsSuccess(response.Code))
                        {
                            // Nếu có lỗi quyền truy cập, lưu lại danh sách tài khoản không có quyền
                            if (AdAccountSyncHelper.IsPermissionError(response)&& unauthorizedAdvertiserIds!=null)
                            {
                                var batchUnauthorizedIds = AdAccountSyncHelper.ParseUnauthorizedAdvertiserIds(response.Message);
                                unauthorizedAdvertiserIds.AddRange(batchUnauthorizedIds);
                            }
                            throw new BusinessException(response.Code.ToString(), response.Message);
                        }                        
                        if (response?.Data?.List == null || !response.Data.List.Any())
                        {
                            _logger.LogDebug("Không có GMV Max Campaign nào được trả về từ API cho trang {Page}", page);
                            hasMorePages = false;
                            continue;
                        }

                        _logger.LogDebug("Nhận được {Count} GMV Max Campaign từ API (trang {Page})", response.Data.List.Count, page);

                        // Lấy chi tiết của từng campaign và kết hợp với thông tin cơ bản
                        var campaignDataList = new List<(GMVMaxCampaign basic, GMVMaxCampaignInfo detail)>();
                        foreach (var campaign in response.Data.List)
                        {
                            try
                            {
                                var detailResponse = await tikTokClient.GMVMax.GetCampaignInfoAsync(
                                    advertiserId,
                                    campaign.CampaignId);

                                if (!TikTokApiCodes.IsSuccess(detailResponse.Code))
                                {
                                    // Nếu có lỗi quyền truy cập, lưu lại danh sách tài khoản không có quyền
                                    if (AdAccountSyncHelper.IsPermissionError(response) && unauthorizedAdvertiserIds != null)
                                    {
                                        var batchUnauthorizedIds = AdAccountSyncHelper.ParseUnauthorizedAdvertiserIds(response.Message);
                                        unauthorizedAdvertiserIds.AddRange(batchUnauthorizedIds);
                                    }
                                    throw new BusinessException(detailResponse.Code.ToString(), detailResponse.Message);
                                }

                                if (detailResponse?.Data != null)
                                {
                                    campaignDataList.Add((campaign, detailResponse.Data));
                                }
                            }
                            catch (BusinessException ex)
                            {
                                _logger.LogError(ex, "Lỗi khi gọi API GMV Max Campaign cho trang {Page}", page);
                                result.ErrorRecords++;
                            }
                        }

                        // Xử lý và lưu dữ liệu
                        await ProcessAndSaveCampaigns(campaignDataList, result, bcId);

                        // Kiểm tra có trang tiếp theo không
                        hasMorePages = response.Data.List.Count == pageSize;
                        page++;
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi gọi API GMV Max Campaign cho trang {Page}", page);
                        result.ErrorRecords++;
                        hasMorePages = false;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi gọi API GMV Max Campaign cho trang {Page}", page);
                        result.ErrorRecords++;
                        hasMorePages = false;
                    }
                }

                result.EndTime = DateTime.UtcNow;
                _logger.LogDebug("Hoàn thành đồng bộ GMV Max Campaign cho Advertiser: {AdvertiserId}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Lỗi: {Error}",
                    advertiserId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.ErrorRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ GMV Max Campaign cho Advertiser: {AdvertiserId}", advertiserId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ GMV Max Campaign: {ex.Message}";
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Campaign cho Advertiser: {AdvertiserId}", advertiserId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ nhiều GMV Max Campaign cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds">Danh sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxCampaignSyncResult> SyncManyGmvMaxCampaignsAsync(string bcId, List<string>? advertiserIds = null)
        {
            var result = new GmvMaxCampaignSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Nếu advertiserIds là null, lấy tất cả advertiser IDs từ asset repository
                if (advertiserIds == null)
                {
                    var advertisers = await _assetRepository.GetByBcIdAsync(bcId, assetType: Enums.AssetType.ADVERTISER);
                    advertiserIds = advertisers.Select(x => x.AssetId).ToList();
                    _logger.LogDebug("Lấy danh sách Advertiser IDs từ Asset Repository cho BC: {BcId}, Số Advertiser: {Count}", bcId, advertiserIds.Count);
                }

                if (!advertiserIds.Any())
                {
                    _logger.LogWarning("Không tìm thấy Advertiser nào cho BC: {BcId}", bcId);
                    result.ErrorMessage = $"Không tìm thấy Advertiser nào cho BC: {bcId}";
                    return result;
                }

                _logger.LogDebug("Bắt đầu đồng bộ nhiều GMV Max Campaign cho BC: {BcId}, Số Advertiser: {Count}", bcId, advertiserIds.Count);

                foreach (var advertiserId in advertiserIds)
                {
                    try
                    {
                        var singleResult = await SyncGmvMaxCampaignsAsync(advertiserId, bcId);
                        result.NewRecords += singleResult.NewRecords;
                        result.UpdatedRecords += singleResult.UpdatedRecords;
                        result.ErrorRecords += singleResult.ErrorRecords;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Campaign cho Advertiser: {AdvertiserId}", advertiserId);
                        result.ErrorRecords++;
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ nhiều GMV Max Campaign cho BC: {BcId}, Tổng: {Total}", bcId, result.TotalSynced);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi nghiệp vụ khi đồng bộ nhiều GMV Max Campaign cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ nhiều GMV Max Campaign: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ nhiều GMV Max Campaign cho BC: {BcId}", bcId);
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả GMV Max Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxCampaignSyncResult> SyncAllGmvMaxCampaignsAsync()
        {
            var result = new GmvMaxCampaignSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả GMV Max Campaign cho tất cả BC");

                // Lấy tất cả Business Centers
                var businessCenters = await _businessCenterRepository.GetListAsync();

                if (!businessCenters.Any())
                {
                    _logger.LogWarning("Không tìm thấy Business Center nào");
                    result.ErrorMessage = "Không tìm thấy Business Center nào";
                    return result;
                }

                foreach (var bc in businessCenters)
                {
                    try
                    {
                        var bcResult = await SyncManyGmvMaxCampaignsAsync(bc.BcId);
                        result.NewRecords += bcResult.NewRecords;
                        result.UpdatedRecords += bcResult.UpdatedRecords;
                        result.ErrorRecords += bcResult.ErrorRecords;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ GMV Max Campaign cho BC: {BcId}", bc.BcId);
                        result.ErrorRecords++;
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ tất cả GMV Max Campaign cho tất cả BC, Tổng: {Total}", result.TotalSynced);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tất cả GMV Max Campaign: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả GMV Max Campaign cho tất cả BC");
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        /// <summary>
        /// Xử lý và lưu danh sách GMV Max Campaign theo trang
        /// </summary>
        /// <param name="campaignDataList">Danh sách campaign từ API (basic + detail)</param>
        /// <param name="result">Kết quả đồng bộ</param>
        /// <param name="bcId">ID Business Center</param>
        private async Task ProcessAndSaveCampaigns(List<(GMVMaxCampaign basic, GMVMaxCampaignInfo detail)> campaignDataList, GmvMaxCampaignSyncResult result, string bcId)
        {
            if (!campaignDataList.Any())
                return;

            // Xử lý theo trang để tránh quá tải memory
            var pageSize = PAGE_SIZE_HANDLE_SAVE_TO_DATABASE;
            var totalPages = (int)Math.Ceiling((double)campaignDataList.Count / pageSize);
            _logger.LogDebug("Bắt đầu xử lý {TotalRecords} bản ghi GMV Max Campaign cho {TotalPages} trang", campaignDataList.Count, totalPages);

            for (int page = 0; page < totalPages; page++)
            {
                var pageData = campaignDataList.Skip(page * pageSize).Take(pageSize).ToList();
                if (pageData.Any())
                {
                    try
                    {
                        await ProcessPageDataAsync(pageData, result, bcId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý dữ liệu GMV Max Campaign cho {Total} bản ghi, Trang: {Page}", pageData.Count, page + 1);
                        result.ErrorRecords += pageData.Count;
                        continue; // Bỏ qua lỗi và tiếp tục với trang tiếp theo
                    }
                }
            }
        }

        /// <summary>
        /// Xử lý dữ liệu một trang
        /// </summary>
        /// <param name="pageData">Dữ liệu một trang</param>
        /// <param name="result">Kết quả đồng bộ</param>
        /// <param name="bcId">ID Business Center</param>
        private async Task ProcessPageDataAsync(List<(GMVMaxCampaign basic, GMVMaxCampaignInfo detail)> pageData, GmvMaxCampaignSyncResult result, string bcId)
        {
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);

            // Convert dữ liệu từ API thành danh sách entity
            var mappedEntities = MapListApiDataToEntities(pageData, bcId);

            // Lấy danh sách campaign IDs để query DB
            var campaignIds = mappedEntities.Select(x => x.CampaignId).ToList();

            // Lấy danh sách đã có trong DB
            var existingEntities = await GetExistingEntitiesByCampaignIds(campaignIds);
            var existingDict = existingEntities.ToDictionary(x => x.CampaignId, x => x);

            var insertedEntities = new List<RawGmvMaxCampaignsEntity>();
            var updatedEntities = new List<RawGmvMaxCampaignsEntity>();

            // Duyệt từng bản ghi xử lý thêm mới/cập nhật
            foreach (var mappedEntity in mappedEntities)
            {
                try
                {
                    if (!existingDict.TryGetValue(mappedEntity.CampaignId, out var existingEntity))
                    {
                        // Thêm mới
                        mappedEntity.SyncedAt = DateTime.UtcNow;
                        insertedEntities.Add(mappedEntity);
                        result.NewRecords++;
                    }
                    else
                    {
                        // Cập nhật nếu có thay đổi
                        if (existingEntity.HasChanged(mappedEntity))
                        {
                            UpdateEntityFromNewData(existingEntity, mappedEntity);
                            existingEntity.SyncedAt = DateTime.UtcNow;
                            updatedEntities.Add(existingEntity);
                            result.UpdatedRecords++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi xử lý GMV Max Campaign: {CampaignId}", mappedEntity.CampaignId);
                    result.ErrorRecords++;
                }
            }

            // Thêm các bản ghi mới vào kho dữ liệu
            if (insertedEntities.Any())
            {
                await _gmvMaxCampaignsRepository.InsertManyAsync(insertedEntities, autoSave: true);
                _logger.LogDebug("Thêm mới {Count} GMV Max Campaign", insertedEntities.Count);
            }

            // Cập nhật các bản ghi đã tồn tại
            if (updatedEntities.Any())
            {
                await _gmvMaxCampaignsRepository.UpdateManyAsync(updatedEntities, autoSave: true);
                _logger.LogDebug("Cập nhật {Count} GMV Max Campaign", updatedEntities.Count);
            }

            await uow.CompleteAsync();
        }

        /// <summary>
        /// Lấy danh sách entity đã tồn tại theo campaign IDs
        /// </summary>
        /// <param name="campaignIds">Danh sách campaign IDs</param>
        /// <returns>Danh sách entity đã tồn tại</returns>
        private async Task<List<RawGmvMaxCampaignsEntity>> GetExistingEntitiesByCampaignIds(List<string> campaignIds)
        {
            return await _gmvMaxCampaignsRepository.GetByCampaignIdsAsync(campaignIds);
        }

        /// <summary>
        /// Convert danh sách dữ liệu API thành danh sách entity
        /// </summary>
        /// <param name="campaignDataList">Danh sách dữ liệu từ API</param>
        /// <param name="bcId">ID Business Center</param>
        /// <returns>Danh sách entity</returns>
        private List<RawGmvMaxCampaignsEntity> MapListApiDataToEntities(List<(GMVMaxCampaign basic, GMVMaxCampaignInfo detail)> campaignDataList, string bcId)
        {
            var entities = new List<RawGmvMaxCampaignsEntity>();

            foreach (var (basic, detail) in campaignDataList)
            {
                try
                {
                    var entity = MapApiDataToEntity(basic, detail, bcId);
                    entities.Add(entity);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi map dữ liệu API sang Entity cho Campaign: {CampaignId}", basic.CampaignId);
                }
            }

            return entities;
        }

        /// <summary>
        /// Map dữ liệu từ API sang Entity (thủ công)
        /// </summary>
        /// <param name="basic">Thông tin cơ bản từ GetCampaignsAsync</param>
        /// <param name="detail">Thông tin chi tiết từ GetCampaignInfoAsync</param>
        /// <param name="bcId">ID Business Center</param>
        /// <returns>Entity</returns>
        private RawGmvMaxCampaignsEntity MapApiDataToEntity(GMVMaxCampaign basic, GMVMaxCampaignInfo detail, string bcId)
        {
            var entity = new RawGmvMaxCampaignsEntity(Guid.NewGuid())
            {
                // Thông tin Business Center
                BcId = bcId,

                // Thông tin từ GetCampaignsAsync (basic)
                AdvertiserId = basic.AdvertiserId,
                CampaignId = basic.CampaignId,
                CampaignName = basic.CampaignName,
                OperationStatus = basic.OperationStatus,
                CreateTime = ParseDateTime(basic.CreateTime),
                ModifyTime = ParseDateTime(basic.ModifyTime),
                ObjectiveType = basic.ObjectiveType,
                SecondaryStatus = basic.SecondaryStatus,

                // Thông tin từ GetCampaignInfoAsync (detail)
                StoreId = detail.StoreId,
                StoreAuthorizedBcId = detail.StoreAuthorizedBcId,
                ShoppingAdsType = ParseGmvMaxShoppingAdsType(detail.ShoppingAdsType),
                ProductSpecificType = ParseGmvMaxProductSpecificType(detail.ProductSpecificType),
                OptimizationGoal = ParseOptimizationGoal(detail.OptimizationGoal),
                RoiProtectionEnabled = detail.RoiProtectionEnabled ?? false,
                DeepBidType = ParseDeepBidType(detail.DeepBidType),
                RoasBid = detail.RoasBid.HasValue ? (decimal?)detail.RoasBid.Value : null,
                Budget = (decimal)detail.Budget,
                ScheduleType = ParseGmvMaxScheduleType(detail.ScheduleType),
                ScheduleStartTime = ParseDateTime(detail.ScheduleStartTime),
                ScheduleEndTime = ParseNullableDateTime(detail.ScheduleEndTime),
                Placements = detail.Placements?.Any() == true ? detail.Placements : null,
                LocationIds = detail.LocationIds?.Any() == true ? detail.LocationIds : null,
                AgeGroups = detail.AgeGroups?.Any() == true ? detail.AgeGroups : null,
                ItemGroupIds = detail.ItemGroupIds?.Any() == true ? detail.ItemGroupIds : null,
                ProductVideoSpecificType = ParseGmvMaxVideoSpecificType(detail.ProductVideoSpecificType),
                AffiliatePostsEnabled = detail.AffiliatePostsEnabled,
                CampaignCustomAnchorVideoId = detail.CampaignCustomAnchorVideoId
            };

            // thông tin Identity
            //if (detail.IdentityList != null && detail.IdentityList.Any())
            //{
            //    entity.Identities = detail.IdentityList.Select(identity => new RawGmvMaxCampaignIdentitiesEntity(Guid.NewGuid())
            //    {
            //        CampaignId = entity.CampaignId,
            //        IdentityAuthorizedBcId = identity.IdentityAuthorizedBcId,
            //        IdentityAuthorizedShopId = identity.IdentityAuthorizedShopId,
            //        IdentityId = identity.IdentityId,
            //        IdentityType = identity.IdentityType,
            //        StoreId = identity.StoreId,
            //    }).ToList();
            //}

            // thông tin Item
            //if (detail.ItemList != null && detail.ItemList.Any())
            //{
            //    entity.Items = detail.ItemList.Select(item =>
            //    {
            //        var itemEntity = new RawGmvMaxCampaignItemsEntity(Guid.NewGuid())
            //        {
            //            CampaignId = entity.CampaignId,
            //            ItemId = item.ItemId,
            //            Text = item.Text,
            //            SpuIdList = item.SpuIdList,
            //        };

            //        if (item.IdentityInfo != null)
            //        {
            //            itemEntity.IdentityId = item.IdentityInfo.IdentityId;
            //            itemEntity.IdentityType = item.IdentityInfo.IdentityType;
            //            itemEntity.IdentityAuthorizedBcId = item.IdentityInfo.IdentityAuthorizedBcId;
            //            itemEntity.IdentityAuthorizedShopId = item.IdentityInfo.IdentityAuthorizedShopId;
            //            itemEntity.IdentityStoreId = item.IdentityInfo.StoreId;
            //            itemEntity.ProfileImage = item.IdentityInfo.ProfileImage;
            //            itemEntity.UserName = item.IdentityInfo.UserName;
            //        }

            //        if (item.VideoInfo != null)
            //        {
            //            itemEntity.VideoId = item.VideoInfo.VideoId;
            //            itemEntity.VideoCoverUrl = item.VideoInfo.VideoCoverUrl;
            //            itemEntity.PreviewUrl = item.VideoInfo.PreviewUrl;
            //            itemEntity.Height = item.VideoInfo.Height;
            //            itemEntity.Width = item.VideoInfo.Width;
            //            itemEntity.BitRate = item.VideoInfo.BitRate;
            //            itemEntity.Duration = item.VideoInfo.Duration;
            //            itemEntity.Size = item.VideoInfo.Size;
            //            itemEntity.Signature = item.VideoInfo.Signature;
            //            itemEntity.Format = item.VideoInfo.Format;
            //            itemEntity.Definition = item.VideoInfo.Definition;
            //            itemEntity.Fps = item.VideoInfo.Fps;
            //        }

            //        return itemEntity;
            //    }).ToList();
            //}

            //// Thông tin Video Anchor
            //if (detail.CustomAnchorVideoList != null && detail.CustomAnchorVideoList.Any())
            //{
            //    entity.CustomAnchorVideos = detail.CustomAnchorVideoList.Select(video =>
            //    {
            //        var videoEntity = new RawGmvMaxCampaignCustomAnchorVideosEntity(Guid.NewGuid())
            //        {
            //            CampaignId = entity.CampaignId,
            //            ItemId = video.ItemId,
            //            SpuIdList = video.SpuIdList,
            //        };

            //        if (video.IdentityInfo != null)
            //        {
            //            videoEntity.IdentityId = video.IdentityInfo.IdentityId;
            //            videoEntity.IdentityType = video.IdentityInfo.IdentityType;
            //            videoEntity.IdentityAuthorizedBcId = video.IdentityInfo.IdentityAuthorizedBcId;
            //            videoEntity.IdentityAuthorizedShopId = video.IdentityInfo.IdentityAuthorizedShopId;
            //            videoEntity.IdentityStoreId = video.IdentityInfo.StoreId;
            //        }

            //        return videoEntity;
            //    }).ToList();
            //}

            return entity;
        }

        /// <summary>
        /// Cập nhật entity từ dữ liệu mới
        /// </summary>
        /// <param name="existingEntity">Entity hiện tại</param>
        /// <param name="newEntity">Entity mới</param>
        private void UpdateEntityFromNewData(RawGmvMaxCampaignsEntity existingEntity, RawGmvMaxCampaignsEntity newEntity)
        {
            existingEntity.BcId = newEntity.BcId;
            existingEntity.CampaignName = newEntity.CampaignName;
            existingEntity.OperationStatus = newEntity.OperationStatus;
            existingEntity.ModifyTime = newEntity.ModifyTime;
            existingEntity.ObjectiveType = newEntity.ObjectiveType;
            existingEntity.SecondaryStatus = newEntity.SecondaryStatus;
            existingEntity.RoiProtectionCompensationStatus = newEntity.RoiProtectionCompensationStatus;
            existingEntity.StoreId = newEntity.StoreId;
            existingEntity.StoreAuthorizedBcId = newEntity.StoreAuthorizedBcId;
            existingEntity.ShoppingAdsType = newEntity.ShoppingAdsType;
            existingEntity.ProductSpecificType = newEntity.ProductSpecificType;
            existingEntity.OptimizationGoal = newEntity.OptimizationGoal;
            existingEntity.RoiProtectionEnabled = newEntity.RoiProtectionEnabled;
            existingEntity.DeepBidType = newEntity.DeepBidType;
            existingEntity.RoasBid = newEntity.RoasBid;
            existingEntity.Budget = newEntity.Budget;
            existingEntity.ScheduleType = newEntity.ScheduleType;
            existingEntity.ScheduleStartTime = newEntity.ScheduleStartTime;
            existingEntity.ScheduleEndTime = newEntity.ScheduleEndTime;
            existingEntity.Placements = newEntity.Placements;
            existingEntity.LocationIds = newEntity.LocationIds;
            existingEntity.AgeGroups = newEntity.AgeGroups;
            existingEntity.ItemGroupIds = newEntity.ItemGroupIds;
            existingEntity.ProductVideoSpecificType = newEntity.ProductVideoSpecificType;
            existingEntity.AffiliatePostsEnabled = newEntity.AffiliatePostsEnabled;
            existingEntity.CampaignCustomAnchorVideoId = newEntity.CampaignCustomAnchorVideoId;

            // update items
            Dictionary<string, RawGmvMaxCampaignItemsEntity> itemOldDict = existingEntity.Items.ToDictionary(x => x.ItemId) ?? new Dictionary<string, RawGmvMaxCampaignItemsEntity>();
            foreach (var newItem in newEntity.Items)
            {

                if (itemOldDict.TryGetValue(newItem.ItemId, out var oldItem) && oldItem.HasChanged(newItem))
                {
                    oldItem.UpdateFromNewData(newItem);
                }
                else
                {
                    itemOldDict.TryAdd(newItem.ItemId, newItem);
                }
            }

            // update identities
            Dictionary<string, RawGmvMaxCampaignIdentitiesEntity> identityOldDict = existingEntity.Identities.ToDictionary(x => x.IdentityId) ?? new Dictionary<string, RawGmvMaxCampaignIdentitiesEntity>();
            foreach (var newIdentity in newEntity.Identities)
            {
                if (identityOldDict.TryGetValue(newIdentity.IdentityId, out var oldIdentity) && oldIdentity.HasChanged(newIdentity))
                {
                    oldIdentity.UpdateFromNewData(newIdentity);
                }
                else
                {
                    identityOldDict.TryAdd(newIdentity.IdentityId, newIdentity);
                }
            }

            // update custom anchor videos
            Dictionary<string, RawGmvMaxCampaignCustomAnchorVideosEntity> customAnchorVideoOldDict = existingEntity.CustomAnchorVideos.ToDictionary(x => x.ItemId) ?? new Dictionary<string, RawGmvMaxCampaignCustomAnchorVideosEntity>();
            foreach (var newVideo in newEntity.CustomAnchorVideos)
            {
                if (customAnchorVideoOldDict.TryGetValue(newVideo.ItemId, out var oldVideo) && oldVideo.HasChanged(newVideo))
                {
                    oldVideo.UpdateFromNewData(newVideo);
                }
                else
                {
                    customAnchorVideoOldDict.TryAdd(newVideo.ItemId, newVideo);
                }
            }
            // Cập nhật lại các danh sách
            existingEntity.Items = itemOldDict.Values.ToList();
            existingEntity.Identities = identityOldDict.Values.ToList();
            existingEntity.CustomAnchorVideos = customAnchorVideoOldDict.Values.ToList();
        }

        #region Helper Methods

        /// <summary>
        /// Parse DateTime từ string
        /// </summary>
        private DateTime ParseDateTime(string? dateTimeString)
        {
            if (string.IsNullOrEmpty(dateTimeString))
                return DateTime.UtcNow;

            if (DateTime.TryParse(dateTimeString, out var result))
                return result;

            return DateTime.UtcNow;
        }

        /// <summary>
        /// Parse nullable DateTime từ string
        /// </summary>
        private DateTime? ParseNullableDateTime(string? dateTimeString)
        {
            if (string.IsNullOrEmpty(dateTimeString))
                return null;

            if (DateTime.TryParse(dateTimeString, out var result))
                return result;

            return null;
        }

        /// <summary>
        /// Parse GmvMaxShoppingAdsType từ string
        /// </summary>
        private GmvMaxShoppingAdsType ParseGmvMaxShoppingAdsType(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return GmvMaxShoppingAdsType.PRODUCT;

            return Enum.TryParse(value, true, out GmvMaxShoppingAdsType result) ? result : GmvMaxShoppingAdsType.PRODUCT;
        }

        /// <summary>
        /// Parse GmvMaxProductSpecificType từ string
        /// </summary>
        private GmvMaxProductSpecificType? ParseGmvMaxProductSpecificType(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return null;

            return Enum.TryParse(value, true, out GmvMaxProductSpecificType result) ? result : null;
        }

        /// <summary>
        /// Parse OptimizationGoal từ string
        /// </summary>
        private OptimizationGoal ParseOptimizationGoal(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return OptimizationGoal.VALUE;

            return Enum.TryParse(value, true, out OptimizationGoal result) ? result : OptimizationGoal.VALUE;
        }

        /// <summary>
        /// Parse DeepBidType từ string
        /// </summary>
        private DeepBidType ParseDeepBidType(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return DeepBidType.VO_MIN_ROAS;

            return Enum.TryParse(value, true, out DeepBidType result) ? result : DeepBidType.VO_MIN_ROAS;
        }

        /// <summary>
        /// Parse GmvMaxScheduleType từ string
        /// </summary>
        private GmvMaxScheduleType ParseGmvMaxScheduleType(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return GmvMaxScheduleType.SCHEDULE_FROM_NOW;

            return Enum.TryParse(value, true, out GmvMaxScheduleType result) ? result : GmvMaxScheduleType.SCHEDULE_FROM_NOW;
        }

        /// <summary>
        /// Parse GmvMaxVideoSpecificType từ string
        /// </summary>
        private GmvMaxVideoSpecificType? ParseGmvMaxVideoSpecificType(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return null;

            return Enum.TryParse(value, true, out GmvMaxVideoSpecificType result) ? result : null;
        }

        #endregion Helper Methods
    }
}