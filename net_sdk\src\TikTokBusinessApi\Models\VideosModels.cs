/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Video Upload Models

    /// <summary>
    /// Request model for uploading a video
    /// </summary>
    public class VideoUploadRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Video name. Length limit: 1 - 100 characters
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }

        /// <summary>
        /// Video upload method. Default: UPLOAD_BY_FILE
        /// Enum values: UPLOAD_BY_FILE, UPLOAD_BY_URL, UPLOAD_BY_FILE_ID, UPLOAD_BY_VIDEO_ID
        /// </summary>
        [JsonPropertyName("upload_type")]
        public string UploadType { get; set; } = "UPLOAD_BY_FILE";

        /// <summary>
        /// Video MD5 (used for server verification). Required when upload_type is UPLOAD_BY_FILE
        /// </summary>
        [JsonPropertyName("video_signature")]
        public string? VideoSignature { get; set; }

        /// <summary>
        /// Video URL address. Required when upload_type is UPLOAD_BY_URL
        /// </summary>
        [JsonPropertyName("video_url")]
        public string? VideoUrl { get; set; }

        /// <summary>
        /// File ID of the file to upload. Required when upload_type is UPLOAD_BY_FILE_ID
        /// </summary>
        [JsonPropertyName("file_id")]
        public string? FileId { get; set; }

        /// <summary>
        /// Video ID. Required when upload_type is UPLOAD_BY_VIDEO_ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Whether the video is third party or not
        /// </summary>
        [JsonPropertyName("is_third_party")]
        public bool? IsThirdParty { get; set; }

        /// <summary>
        /// Whether to automatically detect an issue in your video. Default: false
        /// </summary>
        [JsonPropertyName("flaw_detect")]
        public bool FlawDetect { get; set; } = false;

        /// <summary>
        /// Whether to automatically fix the detected issue. Default: false
        /// </summary>
        [JsonPropertyName("auto_fix_enabled")]
        public bool AutoFixEnabled { get; set; } = false;

        /// <summary>
        /// Whether to automatically upload the fixed video to your creative library. Default: false
        /// Valid only when flaw_detect = true and auto_fix_enabled = true
        /// </summary>
        [JsonPropertyName("auto_bind_enabled")]
        public bool AutoBindEnabled { get; set; } = false;
    }

    /// <summary>
    /// Response model for video upload
    /// </summary>
    public class VideoUploadResponse
    {
        /// <summary>
        /// Video ID, which can be used to create ads
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Temporary URL for video cover, valid for six hours
        /// </summary>
        [JsonPropertyName("video_cover_url")]
        public string? VideoCoverUrl { get; set; }

        /// <summary>
        /// Video format
        /// </summary>
        [JsonPropertyName("format")]
        public string? Format { get; set; }

        /// <summary>
        /// Video preview link, valid for six hours
        /// </summary>
        [JsonPropertyName("preview_url")]
        public string? PreviewUrl { get; set; }

        /// <summary>
        /// The expiration time of the video preview link, in the format of YYYY-MM-DD HH:MM:SS (UTC+0)
        /// </summary>
        [JsonPropertyName("preview_url_expire_time")]
        public string? PreviewUrlExpireTime { get; set; }

        /// <summary>
        /// Video name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }

        /// <summary>
        /// Whether it can be displayed on the platform
        /// </summary>
        [JsonPropertyName("displayable")]
        public bool? Displayable { get; set; }

        /// <summary>
        /// Video height
        /// </summary>
        [JsonPropertyName("height")]
        public int? Height { get; set; }

        /// <summary>
        /// Video width
        /// </summary>
        [JsonPropertyName("width")]
        public int? Width { get; set; }

        /// <summary>
        /// Bit rate in bps
        /// </summary>
        [JsonPropertyName("bit_rate")]
        public long? BitRate { get; set; }

        /// <summary>
        /// Creation time. UTC time. Format: 2020-06-10T07:39:14Z
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Modification time. UTC time. Format: 2020-06-10T07:39:14Z
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string? ModifyTime { get; set; }

        /// <summary>
        /// Video file MD5
        /// </summary>
        [JsonPropertyName("signature")]
        public string? Signature { get; set; }

        /// <summary>
        /// Video duration, in seconds
        /// </summary>
        [JsonPropertyName("duration")]
        public float? Duration { get; set; }

        /// <summary>
        /// Video size, in bytes
        /// </summary>
        [JsonPropertyName("size")]
        public long? Size { get; set; }

        /// <summary>
        /// Material ID
        /// </summary>
        [JsonPropertyName("material_id")]
        public string? MaterialId { get; set; }

        /// <summary>
        /// Available placements
        /// </summary>
        [JsonPropertyName("allowed_placements")]
        public List<string>? AllowedPlacements { get; set; }

        /// <summary>
        /// Whether the video is downloadable
        /// </summary>
        [JsonPropertyName("allow_download")]
        public bool? AllowDownload { get; set; }

        /// <summary>
        /// Fix task ID. Returned only when flaw_detect and auto_fix_enabled are true and video issues are detected
        /// </summary>
        [JsonPropertyName("fix_task_id")]
        public string? FixTaskId { get; set; }

        /// <summary>
        /// Video issue types. Returned only when flaw_detect and auto_fix_enabled are true and video issues are detected
        /// </summary>
        [JsonPropertyName("flaw_types")]
        public List<string>? FlawTypes { get; set; }
    }

    #endregion

    #region Video Update Models

    /// <summary>
    /// Request model for updating video name
    /// </summary>
    public class VideoUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Video name. Length limit: 1 - 100 characters
        /// </summary>
        [Required]
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Video ID
        /// </summary>
        [Required]
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;
    }

    #endregion

    #region Video Info Models

    /// <summary>
    /// Request model for getting video info
    /// </summary>
    public class VideoDetailsRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Video ID list. Max size: 60
        /// </summary>
        [Required]
        [JsonPropertyName("video_ids")]
        public List<string> VideoIds { get; set; } = new List<string>();
    }

    /// <summary>
    /// Response model for video info
    /// </summary>
    public class VideoDetailsResponse
    {
        /// <summary>
        /// Video data list
        /// </summary>
        [JsonPropertyName("list")]
        public List<VideoDetails> List { get; set; } = new List<VideoDetails>();
    }

    /// <summary>
    /// Video information model
    /// </summary>
    public class VideoDetails
    {
        /// <summary>
        /// Whether it can be displayed on the platform
        /// </summary>
        [JsonPropertyName("displayable")]
        public bool? Displayable { get; set; }

        /// <summary>
        /// Video width
        /// </summary>
        [JsonPropertyName("width")]
        public int? Width { get; set; }

        /// <summary>
        /// Temporary URL for video cover, valid for six hours
        /// </summary>
        [JsonPropertyName("video_cover_url")]
        public string? VideoCoverUrl { get; set; }

        /// <summary>
        /// Bit rate in bps
        /// </summary>
        [JsonPropertyName("bit_rate")]
        public long? BitRate { get; set; }

        /// <summary>
        /// Video format
        /// </summary>
        [JsonPropertyName("format")]
        public string? Format { get; set; }

        /// <summary>
        /// Video preview link, valid for six hours
        /// </summary>
        [JsonPropertyName("preview_url")]
        public string? PreviewUrl { get; set; }

        /// <summary>
        /// The expiration time of the video preview link, in the format of YYYY-MM-DD HH:MM:SS (UTC+0)
        /// </summary>
        [JsonPropertyName("preview_url_expire_time")]
        public string? PreviewUrlExpireTime { get; set; }

        /// <summary>
        /// Video duration, in seconds
        /// </summary>
        [JsonPropertyName("duration")]
        public float? Duration { get; set; }

        /// <summary>
        /// Video height
        /// </summary>
        [JsonPropertyName("height")]
        public int? Height { get; set; }

        /// <summary>
        /// Video file MD5
        /// </summary>
        [JsonPropertyName("signature")]
        public string? Signature { get; set; }

        /// <summary>
        /// Video ID, can be used to create ad in ad delivery
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Video size in bytes
        /// </summary>
        [JsonPropertyName("size")]
        public long? Size { get; set; }

        /// <summary>
        /// Material ID
        /// </summary>
        [JsonPropertyName("material_id")]
        public string? MaterialId { get; set; }

        /// <summary>
        /// Available placements
        /// </summary>
        [JsonPropertyName("allowed_placements")]
        public List<string>? AllowedPlacements { get; set; }

        /// <summary>
        /// Whether the video is downloadable
        /// </summary>
        [JsonPropertyName("allow_download")]
        public bool? AllowDownload { get; set; }

        /// <summary>
        /// Video name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }

        /// <summary>
        /// Creation time. UTC time. Format: 2020-06-10T07:39:14Z
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Modification time. UTC time. Format: 2020-06-10T07:39:14Z
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string? ModifyTime { get; set; }
    }

    #endregion

    #region Video Search Models

    /// <summary>
    /// Request model for searching videos
    /// </summary>
    public class VideoSearchRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Filters on the data
        /// </summary>
        [JsonPropertyName("filtering")]
        public VideoSearchFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number. Default: 1. Value range: ≥ 1
        /// </summary>
        [JsonPropertyName("page")]
        public int Page { get; set; } = 1;

        /// <summary>
        /// Page size. Default: 20. Value range: 1-100
        /// </summary>
        [JsonPropertyName("page_size")]
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// Filtering options for video search
    /// </summary>
    public class VideoSearchFiltering
    {
        /// <summary>
        /// A list of video IDs. Max size: 100
        /// </summary>
        [JsonPropertyName("video_ids")]
        public List<string>? VideoIds { get; set; }

        /// <summary>
        /// A list of material IDs. Max size: 20
        /// </summary>
        [JsonPropertyName("material_ids")]
        public List<string>? MaterialIds { get; set; }

        /// <summary>
        /// Video name. You can perform a fuzzy search by entering the video name
        /// </summary>
        [JsonPropertyName("video_name")]
        public string? VideoName { get; set; }

        /// <summary>
        /// List of video material sources
        /// </summary>
        [JsonPropertyName("video_material_sources")]
        public List<string>? VideoMaterialSources { get; set; }
    }

    /// <summary>
    /// Response model for video search
    /// </summary>
    public class VideoSearchResponse
    {
        /// <summary>
        /// A list of video information
        /// </summary>
        [JsonPropertyName("list")]
        public List<VideoDetails> List { get; set; } = new List<VideoDetails>();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new PageInfo();
    }

    #endregion

    #region Video Thumbnail Models

    /// <summary>
    /// Request model for getting suggested video thumbnails
    /// </summary>
    public class VideoThumbnailRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Video ID
        /// </summary>
        [Required]
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Number of cover candidates you want to get. Range: 1-10. Default: 10
        /// </summary>
        [JsonPropertyName("poster_number")]
        public int PosterNumber { get; set; } = 10;
    }

    /// <summary>
    /// Response model for video thumbnail suggestions
    /// </summary>
    public class VideoThumbnailResponse
    {
        /// <summary>
        /// A list of image information
        /// </summary>
        [JsonPropertyName("list")]
        public List<ThumbnailInfo> List { get; set; } = new List<ThumbnailInfo>();
    }

    /// <summary>
    /// Thumbnail information model
    /// </summary>
    public class ThumbnailInfo
    {
        /// <summary>
        /// Image width
        /// </summary>
        [JsonPropertyName("width")]
        public int Width { get; set; }

        /// <summary>
        /// Image height
        /// </summary>
        [JsonPropertyName("height")]
        public int Height { get; set; }

        /// <summary>
        /// Image ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Picture preview address, valid for an hour
        /// </summary>
        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;
    }

    #endregion
}
