using System;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.Customers
{
    /// <summary>
    /// DTO cho việc tìm kiếm và lọc danh sách khách hàng
    /// </summary>
    public class GetCustomerListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Từ khóa tìm kiếm
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// Mã khách hàng
        /// </summary>
        public string? CustomerId { get; set; }

        /// <summary>
        /// Tên khách hàng
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// Tên tài khoản
        /// </summary>
        public string? AccountName { get; set; }

        /// <summary>
        /// ID Shop (deprecated - now in AdAccount collection)
        /// </summary>
        public string? ShopId { get; set; }

        /// <summary>
        /// Tên Shop (deprecated - now in AdAccount collection)
        /// </summary>
        public string? ShopName { get; set; }

        /// <summary>
        /// Số điện thoại
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Loại khách hàng
        /// </summary>
        public CustomerType? CustomerType { get; set; }

        /// <summary>
        /// Trang web
        /// </summary>
        public string? Website { get; set; }

        /// <summary>
        /// Ngày tạo từ
        /// </summary>
        public DateTime? CreationTimeFrom { get; set; }

        /// <summary>
        /// Ngày tạo đến
        /// </summary>
        public DateTime? CreationTimeTo { get; set; }
    }
}
