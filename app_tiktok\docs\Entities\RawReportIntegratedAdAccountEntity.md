# RawReportIntegratedAdAccountEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawReportIntegratedAdAccounts`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawReportIntegratedAdAccounts`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho báo cáo tích hợp của AdAccount. Lưu trữ thông tin báo cáo chi tiêu theo giờ tổng hợp của ADVERTISER, bao gồm các chỉ số hiệu suất quảng cáo, chi phí, và các metrics quan trọng khác.

## Bảng dữ liệu: RawReportIntegratedAdAccount

| Tên Field            | Kiểu dữ liệu | Bắt buộc | Độ dài | <PERSON>ô tả                                                                                                                                                                                                                                                           |
| -------------------- | ------------ | -------- | ------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Id                   | Guid         | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                                                                                                                                                                                                           |
| AdvertiserId         | string       | ✅       | 100    | ID tài khoản nhà quảng cáo - Được hỗ trợ ở cấp độ Nhà quảng cáo, Chiến dịch, Nhóm quảng cáo và Quảng cáo                                                                                                                                                        |
| AdvertiserName       | string       | ✅       | 255    | Tên tài khoản nhà quảng cáo - Được hỗ trợ ở cấp độ Nhà quảng cáo, Chiến dịch, Nhóm quảng cáo và Quảng cáo                                                                                                                                                       |
| Date                 | DateTime     | ✅       | -      | Ngày giờ tổng hợp báo cáo, theo timezone của BC, format yyyy-MM-dd HH:mm:ss (theo giờ)                                                                                                                                                                          |
| Spend                | decimal      | ✅       | 18,2   | Chi tiêu tổng - Tổng chi phí quảng cáo của bạn                                                                                                                                                                                                                  |
| CashSpend            | decimal      | ✅       | 18,2   | Chi phí Thanh toán bằng Tiền mặt - Số tiền ước tính bạn đã chi cho chiến dịch, nhóm quảng cáo hoặc quảng cáo trong lịch trình của nó được thanh toán bằng tiền mặt. **[OBSOLETE]** - Không được đồng bộ khi lấy dữ liệu theo giờ (API không hỗ trợ)             |
| VoucherSpend         | decimal      | ✅       | 18,2   | Chi phí Thanh toán bằng Phiếu giảm giá - Số tiền ước tính bạn đã chi cho chiến dịch, nhóm quảng cáo hoặc quảng cáo trong lịch trình của nó được thanh toán bằng phiếu giảm giá. **[OBSOLETE]** - Không được đồng bộ khi lấy dữ liệu theo giờ (API không hỗ trợ) |
| BilledCost           | decimal      | ✅       | 18,2   | Chi phí thuần - Tổng chi phí quảng cáo của bạn, không bao gồm tín dụng quảng cáo hoặc phiếu giảm giá đã sử dụng                                                                                                                                                 |
| Impressions          | long         | ✅       | -      | Lần hiển thị - Số lần quảng cáo của bạn được hiển thị                                                                                                                                                                                                           |
| Clicks               | long         | ✅       | -      | Lần nhấp (đích đến) - Số lần nhấp từ quảng cáo của bạn đến một đích đến được chỉ định                                                                                                                                                                           |
| Ctr                  | decimal      | ✅       | 18,4   | CTR (đích đến) - Tỷ lệ phần trăm lần hiển thị dẫn đến một lần nhấp đích đến trên tổng số lần hiển thị                                                                                                                                                           |
| Cpm                  | decimal      | ✅       | 18,2   | CPM - Số tiền trung bình bạn chi cho 1.000 lần hiển thị                                                                                                                                                                                                         |
| Cpc                  | decimal      | ✅       | 18,2   | CPC (đích đến) - Chi phí trung bình cho mỗi lần nhấp đến một đích đến được chỉ định                                                                                                                                                                             |
| Reach                | long         | ✅       | -      | Tiếp cận - Số lượng người dùng duy nhất đã xem quảng cáo của bạn ít nhất một lần                                                                                                                                                                                |
| Frequency            | decimal      | ✅       | 18,4   | Tần suất - Số lần trung bình mỗi người dùng xem quảng cáo của bạn trong một khoảng thời gian nhất định                                                                                                                                                          |
| Currency             | string       | ✅       | 10     | Tiền tệ - Mã tiền tệ, ví dụ: USD                                                                                                                                                                                                                                |
| BcId                 | string       | ✅       | 100    | Id Business Center                                                                                                                                                                                                                                              |
| CreationTime         | DateTime     | ✅       | -      | Thời gian tạo bản ghi (Audit)                                                                                                                                                                                                                                   |
| CreatorId            | Guid?        | ❌       | -      | ID người tạo (Audit)                                                                                                                                                                                                                                            |
| LastModificationTime | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                                                                                                                                                                                                                  |
| LastModifierId       | Guid?        | ❌       | -      | ID người sửa đổi cuối (Audit)                                                                                                                                                                                                                                   |

## Cấu trúc dữ liệu

### Thông tin định danh

-   **AdvertiserId**: ID duy nhất của tài khoản quảng cáo từ TikTok API
-   **AdvertiserName**: Tên hiển thị của tài khoản quảng cáo
-   **BcId**: ID của Business Center sở hữu tài khoản quảng cáo
-   **Date**: Ngày giờ tổng hợp báo cáo theo timezone của Business Center (lưu theo giờ)

### Thông tin chi phí

-   **Spend**: Tổng chi phí quảng cáo đã chi
-   **CashSpend**: Chi phí thanh toán bằng tiền mặt (**[OBSOLETE]** - Không được đồng bộ khi lấy dữ liệu theo giờ)
-   **VoucherSpend**: Chi phí thanh toán bằng phiếu giảm giá (**[OBSOLETE]** - Không được đồng bộ khi lấy dữ liệu theo giờ)
-   **BilledCost**: Chi phí thuần (không bao gồm tín dụng/phiếu giảm giá)
-   **Currency**: Đơn vị tiền tệ sử dụng

### Chỉ số hiệu suất cơ bản

-   **Impressions**: Số lần quảng cáo được hiển thị
-   **Clicks**: Số lần nhấp vào quảng cáo
-   **Ctr**: Tỷ lệ nhấp (Click-Through Rate)
-   **Cpm**: Chi phí cho 1000 lần hiển thị
-   **Cpc**: Chi phí cho mỗi lần nhấp

### Chỉ số tiếp cận

-   **Reach**: Số lượng người dùng duy nhất đã xem quảng cáo
-   **Frequency**: Tần suất trung bình mỗi người dùng xem quảng cáo

## Mục đích sử dụng

-   Lưu trữ báo cáo tổng hợp hàng ngày cho tài khoản quảng cáo
-   Theo dõi hiệu suất quảng cáo theo thời gian
-   Phân tích chi phí và ROI của các chiến dịch
-   Đánh giá hiệu quả tiếp cận và tương tác
-   Cung cấp dữ liệu cho báo cáo và dashboard
-   Hỗ trợ ra quyết định tối ưu hóa quảng cáo

## Mối quan hệ

-   **RawAdAccountEntity**: Liên kết với tài khoản quảng cáo (AdvertiserId)
-   **RawBusinessCenterEntity**: Liên kết với Business Center (BcId)
-   **RawReportIntegratedCampaignEntity**: Báo cáo chi tiết cấp chiến dịch
-   **RawReportIntegratedAdGroupEntity**: Báo cáo chi tiết cấp nhóm quảng cáo
-   **RawReportIntegratedAdEntity**: Báo cáo chi tiết cấp quảng cáo

## Indexes

-   **Unique Index**: `(AdvertiserId, Date)` - Đảm bảo không có báo cáo trùng lặp cho cùng một tài khoản và ngày
-   **Index**: `AdvertiserId` - Tối ưu truy vấn theo tài khoản quảng cáo
-   **Index**: `Date` - Tối ưu truy vấn theo ngày
-   **Index**: `Spend` - Tối ưu truy vấn theo chi phí
-   **Index**: `Impressions` - Tối ưu truy vấn theo số lần hiển thị
-   **Index**: `Clicks` - Tối ưu truy vấn theo số lần nhấp
-   **Index**: `BcId` - Tối ưu truy vấn theo Business Center

## Lưu ý

-   Entity này kế thừa từ AuditedEntity để có đầy đủ thông tin audit
-   Tất cả các trường decimal được cấu hình với precision 18,2 hoặc 18,4 tùy theo yêu cầu
-   Date được lưu trữ theo timezone của Business Center
-   Báo cáo được tổng hợp theo giờ và không có báo cáo trùng lặp
-   AdvertiserId là khóa ngoại quan trọng để liên kết với các entity khác
-   Entity này cung cấp cái nhìn tổng quan về hiệu suất quảng cáo ở cấp độ tài khoản
-   **CashSpend** và **VoucherSpend** đã được đánh dấu **[OBSOLETE]** do API không hỗ trợ khi lấy dữ liệu theo giờ
