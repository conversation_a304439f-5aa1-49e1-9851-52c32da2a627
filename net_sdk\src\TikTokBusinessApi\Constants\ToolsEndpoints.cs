/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Tools endpoints
    /// </summary>
    public static class ToolsEndpoints
    {
        /// <summary>
        /// Search for location targeting tags endpoint (v1.3)
        /// </summary>
        public const string SearchTargeting = "/tool/targeting/search/";

        /// <summary>
        /// Get targeting tag information by ID endpoint (v1.3)
        /// </summary>
        public const string GetTargetingInfo = "/tool/targeting/info/";

        /// <summary>
        /// Get available locations by different settings endpoint (v1.3)
        /// </summary>
        public const string GetRegion = "/tool/region/";

        /// <summary>
        /// Get available locations by advertiser ID endpoint (v1.3)
        /// </summary>
        public const string SearchRegion = "/search/region/";

        /// <summary>
        /// Get languages endpoint (v1.3)
        /// </summary>
        public const string GetLanguage = "/tool/language/";

        /// <summary>
        /// Search for targeting categories and hashtags for interests and behaviors endpoint (v1.3)
        /// </summary>
        public const string SearchInterestAndBehavior = "/targeting/search/";

        /// <summary>
        /// Get general interest categories endpoint (v1.3)
        /// </summary>
        public const string GetInterestCategory = "/tool/interest_category/";

        /// <summary>
        /// Search for additional interest categories endpoint (v1.3)
        /// </summary>
        public const string RecommendInterestKeyword = "/tool/interest_keyword/recommend/";

        /// <summary>
        /// Get additional interest categories by ID endpoint (v1.3)
        /// </summary>
        public const string GetInterestKeyword = "/tool/interest_keyword/get/";

        /// <summary>
        /// Get action categories endpoint (v1.3)
        /// </summary>
        public const string GetActionCategory = "/tool/action_category/";

        /// <summary>
        /// Search for targeting hashtags endpoint (v1.3)
        /// </summary>
        public const string RecommendHashtag = "/tool/hashtag/recommend/";

        /// <summary>
        /// Get targeting hashtags by ID endpoint (v1.3)
        /// </summary>
        public const string GetHashtag = "/tool/hashtag/get/";

        /// <summary>
        /// Get recommended interest and action categories endpoint (v1.3)
        /// </summary>
        public const string RecommendTargetingCategory = "/tool/targeting_category/recommend/";

        /// <summary>
        /// Get OS versions endpoint (v1.3)
        /// </summary>
        public const string GetOsVersion = "/tool/os_version/";

        /// <summary>
        /// Get device models endpoint (v1.3)
        /// </summary>
        public const string GetDeviceModel = "/tool/device_model/";

        /// <summary>
        /// Get carriers endpoint (v1.3)
        /// </summary>
        public const string GetCarrier = "/tool/carrier/";

        /// <summary>
        /// Get internet service providers endpoint (v1.3)
        /// </summary>
        public const string GetTargetingList = "/tool/targeting/list/";

        /// <summary>
        /// Get available contextual tags endpoint (v1.3)
        /// </summary>
        public const string GetContextualTag = "/tool/contextual_tag/get/";

        /// <summary>
        /// Get details of contextual tags endpoint (v1.3)
        /// </summary>
        public const string GetContextualTagInfo = "/tool/contextual_tag/info/";

        /// <summary>
        /// Get region calling codes and region codes for phone numbers endpoint (v1.3)
        /// </summary>
        public const string GetPhoneRegionCode = "/tool/phone_region_code/";

        /// <summary>
        /// Get time zones endpoint (v1.3)
        /// </summary>
        public const string GetTimezone = "/tool/timezone/";

        /// <summary>
        /// Get a TikTok in-app link endpoint (v1.3)
        /// </summary>
        public const string GetOpenUrl = "/tool/open_url/";

        /// <summary>
        /// Get available content exclusion categories endpoint (v1.3)
        /// </summary>
        public const string GetContentExclusion = "/tool/content_exclusion/get/";

        /// <summary>
        /// Get details of content exclusion categories endpoint (v1.3)
        /// </summary>
        public const string GetContentExclusionInfo = "/tool/content_exclusion/info/";

        /// <summary>
        /// Get a suggested bid endpoint (v1.3)
        /// </summary>
        public const string RecommendBid = "/tool/bid/recommend/";

        /// <summary>
        /// Check Value-Based Optimization eligibility endpoint (v1.3)
        /// </summary>
        public const string GetVboStatus = "/tool/vbo_status/";

        /// <summary>
        /// Get the authorization status of a Brand Safety partner endpoint (v1.3)
        /// </summary>
        public const string GetBrandSafetyPartnerStatus = "/tool/brand_safety/partner/authorize/status/";

        /// <summary>
        /// Get the verification results of a URL endpoint (v1.3)
        /// </summary>
        public const string ValidateUrl = "/tool/url_validate/";
    }
}
