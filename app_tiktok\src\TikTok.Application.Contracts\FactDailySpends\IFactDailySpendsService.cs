﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.FactDailySpends.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.FactDailySpends
{
    public interface IFactDailySpendsService : IApplicationService, ICrudAppService<FactDailySpendDto, Guid, GetFactDailySpendDto, GetFactDailySpendInput, GetFactDailySpendInput>
    {
        Task<FactDailySpendReportDto> GetDailySpendReportAsync(DateTime from, DateTime to);
    }
}
