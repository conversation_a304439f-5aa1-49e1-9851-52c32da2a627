using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TikTok.Application.Contracts.MessageProviders;

namespace TikTok.Application.MessageProviders
{
    public class SlackMessageProvider : IMessageProvider
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<SlackMessageProvider> _logger;
        private readonly SlackOptions _options;

        public string Name => "Slack";
        public bool IsEnabled => _options.Enabled;

        public SlackMessageProvider(
            ILogger<SlackMessageProvider> logger,
            IOptions<MessageProviderOptions> options,
            IHttpClientFactory httpClientFactory)
        {
            _httpClient = httpClientFactory.CreateClient("SlackMessageProvider");
            _logger = logger;
            _options = options.Value.Slack;
        }

        public async Task SendMessageAsync(string message)
        {
            await SendMessageAsync(message, null);
        }

        public async Task SendMessageAsync(string message, object additionalData)
        {
            if (!_options.Enabled)
            {
                _logger.LogWarning("Slack provider is disabled");
                return;
            }

            if (string.IsNullOrEmpty(_options.WebhookUrl))
            {
                _logger.LogError("Slack webhook URL is not configured");
                return;
            }

            try
            {
                var requestData = new
                {
                    text = message
                };

                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(_options.WebhookUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogDebug("Message sent successfully to Slack");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to send message to Slack. Status: {Status}, Error: {Error}",
                        response.StatusCode, errorContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message to Slack");
            }
        }
    }
}