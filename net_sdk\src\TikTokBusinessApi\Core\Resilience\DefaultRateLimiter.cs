/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TikTokBusinessApi.Core.Resilience
{
    /// <summary>
    /// Default implementation of rate limiter using sliding window algorithm
    /// </summary>
    public class DefaultRateLimiter : IRateLimiter
    {
        private readonly RateLimiterOptions _options;
        private readonly ILogger<DefaultRateLimiter>? _logger;
        private readonly ConcurrentQueue<DateTimeOffset> _requestTimestamps = new();
        private readonly SemaphoreSlim _semaphore;
        private readonly object _lock = new();
        private DateTimeOffset _windowStart;

        /// <summary>
        /// Initializes a new instance of the DefaultRateLimiter class
        /// </summary>
        /// <param name="options">Rate limiter options</param>
        /// <param name="logger">Logger instance</param>
        public DefaultRateLimiter(RateLimiterOptions? options = null, ILogger<DefaultRateLimiter>? logger = null)
        {
            _options = options ?? new RateLimiterOptions();
            _logger = logger;
            _semaphore = new SemaphoreSlim(_options.RequestsPerWindow, _options.RequestsPerWindow);
            _windowStart = DateTimeOffset.UtcNow;
        }

        /// <summary>
        /// Waits for permission to proceed with an operation
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task that completes when permission is granted</returns>
        public async Task WaitAsync(CancellationToken cancellationToken = default)
        {
            var startTime = DateTimeOffset.UtcNow;
            
            while (true)
            {
                if (TryAcquire())
                {
                    _logger?.LogDebug("Rate limit permission granted");
                    return;
                }

                var status = GetStatus();
                var waitTime = status.TimeToWait;

                if (waitTime <= TimeSpan.Zero)
                {
                    // Window should have reset, try again
                    continue;
                }

                if (DateTimeOffset.UtcNow - startTime > _options.MaxWaitTime)
                {
                    _logger?.LogWarning("Rate limit wait timeout exceeded");
                    throw new TimeoutException($"Rate limit wait timeout exceeded after {_options.MaxWaitTime}");
                }

                _logger?.LogDebug("Rate limit exceeded, waiting {WaitTime}ms", waitTime.TotalMilliseconds);
                await Task.Delay(waitTime, cancellationToken);
            }
        }

        /// <summary>
        /// Tries to acquire permission immediately without waiting
        /// </summary>
        /// <returns>True if permission was granted, false otherwise</returns>
        public bool TryAcquire()
        {
            lock (_lock)
            {
                var now = DateTimeOffset.UtcNow;
                
                if (_options.UseSlidingWindow)
                {
                    CleanupOldRequests(now);
                }
                else
                {
                    // Fixed window - reset if window has expired
                    if (now - _windowStart >= _options.WindowDuration)
                    {
                        Reset();
                    }
                }

                if (_requestTimestamps.Count >= _options.RequestsPerWindow)
                {
                    _logger?.LogDebug("Rate limit exceeded: {CurrentRequests}/{MaxRequests}", 
                        _requestTimestamps.Count, _options.RequestsPerWindow);
                    return false;
                }

                _requestTimestamps.Enqueue(now);
                _logger?.LogDebug("Rate limit permission granted: {CurrentRequests}/{MaxRequests}", 
                    _requestTimestamps.Count, _options.RequestsPerWindow);
                return true;
            }
        }

        /// <summary>
        /// Gets the current rate limit status
        /// </summary>
        public RateLimitStatus GetStatus()
        {
            lock (_lock)
            {
                var now = DateTimeOffset.UtcNow;
                
                if (_options.UseSlidingWindow)
                {
                    CleanupOldRequests(now);
                }

                var remainingRequests = Math.Max(0, _options.RequestsPerWindow - _requestTimestamps.Count);
                var windowResetTime = _options.UseSlidingWindow 
                    ? GetOldestRequestTime().Add(_options.WindowDuration)
                    : _windowStart.Add(_options.WindowDuration);

                return new RateLimitStatus
                {
                    RemainingRequests = remainingRequests,
                    TotalRequests = _options.RequestsPerWindow,
                    WindowResetTime = windowResetTime,
                    WindowDuration = _options.WindowDuration
                };
            }
        }

        /// <summary>
        /// Resets the rate limiter state
        /// </summary>
        public void Reset()
        {
            lock (_lock)
            {
                _requestTimestamps.Clear();
                _windowStart = DateTimeOffset.UtcNow;
                _logger?.LogDebug("Rate limiter reset");
            }
        }

        private void CleanupOldRequests(DateTimeOffset now)
        {
            var cutoffTime = now - _options.WindowDuration;
            
            while (_requestTimestamps.TryPeek(out var oldestRequest) && oldestRequest < cutoffTime)
            {
                _requestTimestamps.TryDequeue(out _);
            }
        }

        private DateTimeOffset GetOldestRequestTime()
        {
            return _requestTimestamps.TryPeek(out var oldest) ? oldest : DateTimeOffset.UtcNow;
        }

        /// <summary>
        /// Disposes the rate limiter
        /// </summary>
        public void Dispose()
        {
            _semaphore?.Dispose();
        }
    }

    /// <summary>
    /// No-operation rate limiter that allows all requests
    /// </summary>
    public class NoOpRateLimiter : IRateLimiter
    {
        /// <summary>
        /// Always returns immediately
        /// </summary>
        public Task WaitAsync(CancellationToken cancellationToken = default) => Task.CompletedTask;

        /// <summary>
        /// Always returns true
        /// </summary>
        public bool TryAcquire() => true;

        /// <summary>
        /// Returns unlimited status
        /// </summary>
        public RateLimitStatus GetStatus() => new()
        {
            RemainingRequests = int.MaxValue,
            TotalRequests = int.MaxValue,
            WindowResetTime = DateTimeOffset.MaxValue,
            WindowDuration = TimeSpan.MaxValue
        };

        /// <summary>
        /// No-op reset
        /// </summary>
        public void Reset() { }
    }
}
