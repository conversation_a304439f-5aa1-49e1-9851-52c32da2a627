/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating TikTok Store API usage
    /// </summary>
    public class StoreApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public StoreApiExample()
        {
            _client = TikTokBusinessApiClient.Create();
        }

        /// <summary>
        /// Example: Get available stores under an ad account
        /// </summary>
        public async Task GetStoreListExample()
        {
            try
            {
                Console.WriteLine("=== Get Store List Example ===");

                const string accessToken = "your-access-token";
                const string advertiserId = "your-advertiser-id";

                // Get all stores
                var response = await _client.Store.GetStoreListAsync(accessToken, advertiserId);

                if (response.Data?.Stores != null && response.Data.Stores.Count > 0)
                {
                    Console.WriteLine($"Found {response.Data.Stores.Count} stores:");
                    foreach (var store in response.Data.Stores)
                    {
                        Console.WriteLine($"- Store ID: {store.StoreId}");
                        Console.WriteLine($"  Name: {store.StoreName}");
                        Console.WriteLine($"  Type: {store.StoreType}");
                        Console.WriteLine($"  Store Code: {store.StoreCode}");
                        Console.WriteLine($"  Authorized BC ID: {store.StoreAuthorizedBcId}");
                        Console.WriteLine($"  Targeting Regions: {string.Join(", ", store.TargetingRegionCodes)}");
                        Console.WriteLine();
                    }
                }
                else
                {
                    Console.WriteLine("No stores found for this advertiser.");
                }

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting store list: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get products within a TikTok Shop
        /// </summary>
        public async Task GetStoreProductsExample()
        {
            try
            {
                Console.WriteLine("=== Get Store Products Example ===");

                const string accessToken = "your-access-token";
                const string bcId = "your-business-center-id";
                const string storeId = "your-store-id";

                // Get products with basic parameters
                var response = await _client.Store.GetStoreProductsAsync(
                    accessToken, 
                    bcId, 
                    storeId,
                    page: 1,
                    pageSize: 20);

                if (response.Data?.StoreProducts != null && response.Data.StoreProducts.Count > 0)
                {
                    Console.WriteLine($"Found {response.Data.StoreProducts.Count} products:");
                    foreach (var product in response.Data.StoreProducts)
                    {
                        Console.WriteLine($"- Product ID: {product.ItemGroupId}");
                        Console.WriteLine($"  Title: {product.Title}");
                        Console.WriteLine($"  Category: {product.Category}");
                        Console.WriteLine($"  Price Range: {product.MinPrice} - {product.MaxPrice} {product.Currency}");
                        Console.WriteLine($"  Status: {product.Status}");
                        Console.WriteLine($"  Historical Sales: {product.HistoricalSales}");
                        Console.WriteLine($"  Image URL: {product.ProductImageUrl}");
                        Console.WriteLine();
                    }

                    if (response.Data.PageInfo != null)
                    {
                        Console.WriteLine($"Page {response.Data.PageInfo.Page} of {response.Data.PageInfo.TotalPage}");
                        Console.WriteLine($"Total products: {response.Data.PageInfo.TotalNumber}");
                    }
                }
                else
                {
                    Console.WriteLine("No products found in this store.");
                }

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting store products: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get products eligible for Shopping Ads
        /// </summary>
        public async Task GetShoppingAdsEligibleProductsExample()
        {
            try
            {
                Console.WriteLine("=== Get Shopping Ads Eligible Products Example ===");

                const string accessToken = "your-access-token";
                const string bcId = "your-business-center-id";
                const string storeId = "your-store-id";
                const string advertiserId = "your-advertiser-id";

                var filtering = new StoreProductFiltering
                {
                    AdCreationEligible = "CUSTOM_SHOP_ADS"
                };

                var response = await _client.Store.GetStoreProductsAsync(
                    accessToken,
                    bcId,
                    storeId,
                    filtering,
                    advertiserId,
                    sortField: "historical_sales",
                    sortType: "DESC",
                    page: 1,
                    pageSize: 50);

                if (response.Data?.StoreProducts != null && response.Data.StoreProducts.Count > 0)
                {
                    Console.WriteLine($"Found {response.Data.StoreProducts.Count} products eligible for Shopping Ads:");
                    foreach (var product in response.Data.StoreProducts)
                    {
                        Console.WriteLine($"- {product.Title}");
                        Console.WriteLine($"  Sales: {product.HistoricalSales}");
                        Console.WriteLine($"  Price: {product.MinPrice} {product.Currency}");
                        Console.WriteLine($"  GMV Max Status: {product.GmvMaxAdsStatus}");
                        Console.WriteLine();
                    }
                }
                else
                {
                    Console.WriteLine("No products eligible for Shopping Ads found.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting Shopping Ads eligible products: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get products eligible for GMV Max campaigns
        /// </summary>
        public async Task GetGMVMaxEligibleProductsExample()
        {
            try
            {
                Console.WriteLine("=== Get GMV Max Eligible Products Example ===");

                const string accessToken = "your-access-token";
                const string bcId = "your-business-center-id";
                const string storeId = "your-store-id";
                const string advertiserId = "your-advertiser-id";

                var filtering = new StoreProductFiltering
                {
                    AdCreationEligible = "GMV_MAX"
                };

                var response = await _client.Store.GetStoreProductsAsync(
                    accessToken,
                    bcId,
                    storeId,
                    filtering,
                    advertiserId,
                    sortField: "min_price",
                    sortType: "ASC",
                    page: 1,
                    pageSize: 100);

                if (response.Data?.StoreProducts != null && response.Data.StoreProducts.Count > 0)
                {
                    Console.WriteLine($"Found {response.Data.StoreProducts.Count} products eligible for GMV Max:");
                    
                    var availableProducts = response.Data.StoreProducts
                        .Where(p => p.Status == "AVAILABLE" && p.GmvMaxAdsStatus == "UNOCCUPIED")
                        .ToList();

                    Console.WriteLine($"Available for new GMV Max campaigns: {availableProducts.Count}");
                    
                    foreach (var product in availableProducts.Take(10)) // Show first 10
                    {
                        Console.WriteLine($"- {product.Title}");
                        Console.WriteLine($"  Price: {product.MinPrice} {product.Currency}");
                        Console.WriteLine($"  Running Custom Shop Ads: {product.IsRunningCustomShopAds}");
                        Console.WriteLine();
                    }
                }
                else
                {
                    Console.WriteLine("No products eligible for GMV Max found.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting GMV Max eligible products: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Search for specific products by name
        /// </summary>
        public async Task SearchProductsByNameExample()
        {
            try
            {
                Console.WriteLine("=== Search Products by Name Example ===");

                const string accessToken = "your-access-token";
                const string bcId = "your-business-center-id";
                const string storeId = "your-store-id";
                const string searchTerm = "shirt";

                var filtering = new StoreProductFiltering
                {
                    ProductName = searchTerm
                };

                var response = await _client.Store.GetStoreProductsAsync(
                    accessToken,
                    bcId,
                    storeId,
                    filtering,
                    page: 1,
                    pageSize: 20);

                if (response.Data?.StoreProducts != null && response.Data.StoreProducts.Count > 0)
                {
                    Console.WriteLine($"Found {response.Data.StoreProducts.Count} products matching '{searchTerm}':");
                    foreach (var product in response.Data.StoreProducts)
                    {
                        Console.WriteLine($"- {product.Title}");
                        Console.WriteLine($"  Category: {product.Category}");
                        Console.WriteLine($"  Price: {product.MinPrice} - {product.MaxPrice} {product.Currency}");
                        Console.WriteLine();
                    }
                }
                else
                {
                    Console.WriteLine($"No products found matching '{searchTerm}'.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error searching products: {ex.Message}");
            }
        }

        /// <summary>
        /// Run all Store API examples
        /// </summary>
        public async Task RunAllExamples()
        {
            await GetStoreListExample();
            Console.WriteLine();
            
            await GetStoreProductsExample();
            Console.WriteLine();
            
            await GetShoppingAdsEligibleProductsExample();
            Console.WriteLine();
            
            await GetGMVMaxEligibleProductsExample();
            Console.WriteLine();
            
            await SearchProductsByNameExample();
        }
    }
}
