using System;
using System.Collections.Generic;
using TikTokBusinessApi;

namespace TikTok.DataSync
{
    /// <summary>
    /// Parameters cho việc đồng bộ GMV Max
    /// </summary>
    public class GmvMaxSyncParameters
    {
        /// <summary>
        /// ID của Business Center
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// TikTok Client đã được khởi tạo
        /// </summary>
        public TikTokBusinessApiClient TikTokClient { get; set; }

        /// <summary>
        /// Danh sách Advertiser cần đồng bộ
        /// </summary>
        public List<GmvMaxAdvertiserDto> Advertisers { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public GmvMaxSyncParameters()
        {
            Advertisers = new List<GmvMaxAdvertiserDto>();
        }

        /// <summary>
        /// Constructor với parameters
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="tikTokClient">TikTok Client</param>
        /// <param name="advertisers">Danh sách Advertiser</param>
        public GmvMaxSyncParameters(string bcId, TikTokBusinessApiClient tikTokClient, List<GmvMaxAdvertiserDto> advertisers)
        {
            BcId = bcId;
            TikTokClient = tikTokClient;
            Advertisers = advertisers ?? new List<GmvMaxAdvertiserDto>();
        }
    }
} 