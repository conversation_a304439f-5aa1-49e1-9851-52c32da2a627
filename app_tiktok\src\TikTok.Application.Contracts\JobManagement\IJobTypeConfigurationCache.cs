﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Enums;
using TikTok.JobManagement;
using Volo.Abp.DependencyInjection;

namespace TikTok.BusinessApplications
{
    public interface IJobTypeConfigurationCache : IScopedDependency
    {
        public Task<JobTypeConfigurationDto?> GetByIdAsync(Guid id);

        public Task<JobTypeConfigurationDto?> GetByCommandTypeAsync(CommandType commandType);

        public Task<List<JobTypeConfigurationDto>> GetAllActiveAsync();

        public Task CleanCache();
    }
}
