/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Videos operations
    /// </summary>
    public interface IVideosApi
    {
        /// <summary>
        /// Upload a video to the Asset Library and use the obtained video ID for creating ads
        /// </summary>
        /// <param name="request">Video upload request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Video upload response containing video ID and metadata</returns>
        // Task<VideoUploadResponse> UploadVideoAsync(
        //     VideoUploadRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Update the name of a video
        /// </summary>
        /// <param name="request">Video update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        // Task UpdateVideoAsync(
        //     VideoUpdateRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the information about a list of videos that can be used in ads from the Asset Library
        /// </summary>
        /// <param name="request">Video info request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Video info response containing video details</returns>
        Task<VideoDetailsResponse> GetVideoInfoAsync(
            VideoDetailsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Search for video creatives that can be used in ads in the Asset Library of an ad account
        /// </summary>
        /// <param name="request">Video search request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Video search response containing matching videos and pagination info</returns>
        Task<VideoSearchResponse> SearchVideosAsync(
            VideoSearchRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a list of suggested video thumbnails based on a video creative
        /// </summary>
        /// <param name="request">Video thumbnail request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Video thumbnail response containing suggested thumbnails</returns>
        Task<VideoThumbnailResponse> GetVideoThumbnailsAsync(
            VideoThumbnailRequest request,
            CancellationToken cancellationToken = default);
    }
}
