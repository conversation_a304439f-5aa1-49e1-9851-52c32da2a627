/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for publishing a video post to an owned account
    /// </summary>
    public class VideoPublishRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// A publicly accessible HTTP(s) URL for the video content to be published
        /// </summary>
        [JsonPropertyName("video_url")]
        public string VideoUrl { get; set; } = string.Empty;

        /// <summary>
        /// A publicly accessible HTTP(s) URL for a custom image to be used as the video's cover photo
        /// </summary>
        [JsonPropertyName("custom_thumbnail_url")]
        public string? CustomThumbnailUrl { get; set; }

        /// <summary>
        /// Information about the post
        /// </summary>
        [JsonPropertyName("post_info")]
        public VideoPostInfo PostInfo { get; set; } = new VideoPostInfo();
    }
}
