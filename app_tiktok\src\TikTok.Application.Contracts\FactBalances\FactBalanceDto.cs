﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.DimAdAccounts;
using TikTok.DimBusinessCenters;
using TikTok.DimDates;
using Volo.Abp.Application.Dtos;

namespace TikTok.FactBalances
{
    public class FactBalanceDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// Loại số dư: "AdAccount", "BusinessCenter"
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// ID của entity (AdvertiserId hoặc BcId)
        /// </summary>
        public string EntityId { get; set; }

        /// <summary>
        /// Tên của entity
        /// </summary>
        public string EntityName { get; set; }

        /// <summary>
        /// Tổng số dư tài khoản
        /// </summary>
        public decimal? AccountBalance { get; set; }

        /// <summary>
        /// Tổng số dư tài khoản (VND)
        /// </summary>
        public decimal? AccountBalanceVND { get; set; }

        /// <summary>
        /// Tổng số dư tài khoản (USD)
        /// </summary>
        public decimal? AccountBalanceUSD { get; set; }

        /// <summary>
        /// Số dư tài khoản hợp lệ
        /// </summary>
        public decimal? ValidAccountBalance { get; set; }

        /// <summary>
        /// Số dư tài khoản hợp lệ (VND)
        /// </summary>
        public decimal? ValidAccountBalanceVND { get; set; }

        /// <summary>
        /// Số dư tài khoản hợp lệ (USD)
        /// </summary>
        public decimal? ValidAccountBalanceUSD { get; set; }

        /// <summary>
        /// Số dư bị đóng băng
        /// </summary>
        public decimal? FrozenBalance { get; set; }

        /// <summary>
        /// Số dư bị đóng băng (VND)
        /// </summary>
        public decimal? FrozenBalanceVND { get; set; }

        /// <summary>
        /// Số dư bị đóng băng (USD)
        /// </summary>
        public decimal? FrozenBalanceUSD { get; set; }

        /// <summary>
        /// Thuế
        /// </summary>
        public decimal? Tax { get; set; }

        /// <summary>
        /// Thuế (VND)
        /// </summary>
        public decimal? TaxVND { get; set; }

        /// <summary>
        /// Thuế (USD)
        /// </summary>
        public decimal? TaxUSD { get; set; }

        /// <summary>
        /// Số dư tiền mặt
        /// </summary>
        public decimal? CashBalance { get; set; }

        /// <summary>
        /// Số dư tiền mặt (VND)
        /// </summary>
        public decimal? CashBalanceVND { get; set; }

        /// <summary>
        /// Số dư tiền mặt (USD)
        /// </summary>
        public decimal? CashBalanceUSD { get; set; }

        /// <summary>
        /// Số dư tiền mặt hợp lệ
        /// </summary>
        public decimal? ValidCashBalance { get; set; }

        /// <summary>
        /// Số dư tiền mặt hợp lệ (VND)
        /// </summary>
        public decimal? ValidCashBalanceVND { get; set; }

        /// <summary>
        /// Số dư tiền mặt hợp lệ (USD)
        /// </summary>
        public decimal? ValidCashBalanceUSD { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher
        /// </summary>
        public decimal? GrantBalance { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher (VND)
        /// </summary>
        public decimal? GrantBalanceVND { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher (USD)
        /// </summary>
        public decimal? GrantBalanceUSD { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher hợp lệ
        /// </summary>
        public decimal? ValidGrantBalance { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher hợp lệ (VND)
        /// </summary>
        public decimal? ValidGrantBalanceVND { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher hợp lệ (USD)
        /// </summary>
        public decimal? ValidGrantBalanceUSD { get; set; }

        /// <summary>
        /// Số tiền có thể chuyển (chỉ AdAccount)
        /// </summary>
        public decimal? TransferableAmount { get; set; }

        /// <summary>
        /// Số tiền có thể chuyển (VND) (chỉ AdAccount)
        /// </summary>
        public decimal? TransferableAmountVND { get; set; }

        /// <summary>
        /// Số tiền có thể chuyển (USD) (chỉ AdAccount)
        /// </summary>
        public decimal? TransferableAmountUSD { get; set; }

        /// <summary>
        /// Ngân sách (chỉ AdAccount)
        /// </summary>
        public decimal? Budget { get; set; }

        /// <summary>
        /// Ngân sách (VND) (chỉ AdAccount)
        /// </summary>
        public decimal? BudgetVND { get; set; }

        /// <summary>
        /// Ngân sách (USD) (chỉ AdAccount)
        /// </summary>
        public decimal? BudgetUSD { get; set; }

        /// <summary>
        /// Chi phí ngân sách đã sử dụng (chỉ AdAccount)
        /// </summary>
        public decimal? BudgetCost { get; set; }

        /// <summary>
        /// Chi phí ngân sách đã sử dụng (VND) (chỉ AdAccount)
        /// </summary>
        public decimal? BudgetCostVND { get; set; }

        /// <summary>
        /// Chi phí ngân sách đã sử dụng (USD) (chỉ AdAccount)
        /// </summary>
        public decimal? BudgetCostUSD { get; set; }

        /// <summary>
        /// Ngân sách còn lại (chỉ AdAccount)
        /// </summary>
        public decimal? BudgetRemaining { get; set; }

        /// <summary>
        /// Ngân sách còn lại (VND) (chỉ AdAccount)
        /// </summary>
        public decimal? BudgetRemainingVND { get; set; }

        /// <summary>
        /// Ngân sách còn lại (USD) (chỉ AdAccount)
        /// </summary>
        public decimal? BudgetRemainingUSD { get; set; }

        /// <summary>
        /// Tiền tệ theo mã ISO 4217
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Múi giờ
        /// </summary>
        public string? Timezone { get; set; }

        /// <summary>
        /// Ngày số dư (UTC)
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_Date (YYYYMMDD)
        /// </summary>
        public int DimDateId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_AdAccount (null nếu Type = BusinessCenter)
        /// </summary>
        public Guid? DimAdAccountId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_BusinessCenter
        /// </summary>
        public Guid DimBusinessCenterId { get; set; }
    }
}
