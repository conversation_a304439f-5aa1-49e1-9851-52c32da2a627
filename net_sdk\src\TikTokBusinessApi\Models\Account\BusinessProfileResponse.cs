/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for business profile data
    /// </summary>
    public class BusinessProfileResponse
    {
        /// <summary>
        /// Whether the TikTok account is a TikTok Business Account
        /// </summary>
        [JsonPropertyName("is_business_account")]
        public bool? IsBusinessAccount { get; set; }

        /// <summary>
        /// The URL to the profile photo of the TikTok account
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string? ProfileImage { get; set; }

        /// <summary>
        /// The username (handle) of the TikTok account
        /// </summary>
        [JsonPropertyName("username")]
        public string? Username { get; set; }

        /// <summary>
        /// The link to the profile page of the TikTok account
        /// </summary>
        [JsonPropertyName("profile_deep_link")]
        public string? ProfileDeepLink { get; set; }

        /// <summary>
        /// The display name (nickname) of the TikTok account
        /// </summary>
        [JsonPropertyName("display_name")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// The bio description of the TikTok account
        /// </summary>
        [JsonPropertyName("bio_description")]
        public string? BioDescription { get; set; }

        /// <summary>
        /// Whether TikTok has provided a verified badge to the TikTok account
        /// </summary>
        [JsonPropertyName("is_verified")]
        public bool? IsVerified { get; set; }

        /// <summary>
        /// The number of accounts that the TikTok account is following
        /// </summary>
        [JsonPropertyName("following_count")]
        public int? FollowingCount { get; set; }

        /// <summary>
        /// The number of followers for the TikTok account
        /// </summary>
        [JsonPropertyName("followers_count")]
        public int? FollowersCount { get; set; }

        /// <summary>
        /// Total likes. The total number of times people have liked your published videos
        /// </summary>
        [JsonPropertyName("total_likes")]
        public int? TotalLikes { get; set; }

        /// <summary>
        /// The lifetime number of public videos posted by the TikTok account
        /// </summary>
        [JsonPropertyName("videos_count")]
        public int? VideosCount { get; set; }

        /// <summary>
        /// All requested daily metrics
        /// </summary>
        [JsonPropertyName("metrics")]
        public List<BusinessProfileMetrics>? Metrics { get; set; }

        /// <summary>
        /// Follower age distribution
        /// </summary>
        [JsonPropertyName("audience_ages")]
        public List<AudienceAge>? AudienceAges { get; set; }

        /// <summary>
        /// Follower gender distribution
        /// </summary>
        [JsonPropertyName("audience_genders")]
        public List<AudienceGender>? AudienceGenders { get; set; }

        /// <summary>
        /// Follower top countries or regions
        /// </summary>
        [JsonPropertyName("audience_countries")]
        public List<AudienceCountry>? AudienceCountries { get; set; }

        /// <summary>
        /// Follower top cities
        /// </summary>
        [JsonPropertyName("audience_cities")]
        public List<AudienceCity>? AudienceCities { get; set; }
    }

    /// <summary>
    /// Daily metrics for business profile
    /// </summary>
    public class BusinessProfileMetrics
    {
        /// <summary>
        /// The date for this set of daily metrics - in YYYY-MM-DD format - in UTC time zone
        /// </summary>
        [JsonPropertyName("date")]
        public string Date { get; set; } = string.Empty;

        /// <summary>
        /// Daily video views
        /// </summary>
        [JsonPropertyName("video_views")]
        public int? VideoViews { get; set; }

        /// <summary>
        /// Daily reached audience
        /// </summary>
        [JsonPropertyName("unique_video_views")]
        public int? UniqueVideoViews { get; set; }

        /// <summary>
        /// Daily profile views
        /// </summary>
        [JsonPropertyName("profile_views")]
        public int? ProfileViews { get; set; }

        /// <summary>
        /// Daily likes
        /// </summary>
        [JsonPropertyName("likes")]
        public int? Likes { get; set; }

        /// <summary>
        /// Daily comments
        /// </summary>
        [JsonPropertyName("comments")]
        public int? Comments { get; set; }

        /// <summary>
        /// Daily shares
        /// </summary>
        [JsonPropertyName("shares")]
        public int? Shares { get; set; }

        /// <summary>
        /// Daily phone number clicks
        /// </summary>
        [JsonPropertyName("phone_number_clicks")]
        public int? PhoneNumberClicks { get; set; }

        /// <summary>
        /// Daily lead submissions
        /// </summary>
        [JsonPropertyName("lead_submissions")]
        public int? LeadSubmissions { get; set; }

        /// <summary>
        /// Daily app download link clicks
        /// </summary>
        [JsonPropertyName("app_download_clicks")]
        public int? AppDownloadClicks { get; set; }

        /// <summary>
        /// Daily bio link clicks
        /// </summary>
        [JsonPropertyName("bio_link_clicks")]
        public int? BioLinkClicks { get; set; }

        /// <summary>
        /// Daily email clicks
        /// </summary>
        [JsonPropertyName("email_clicks")]
        public int? EmailClicks { get; set; }

        /// <summary>
        /// Daily address clicks
        /// </summary>
        [JsonPropertyName("address_clicks")]
        public int? AddressClicks { get; set; }

        /// <summary>
        /// Daily net growth
        /// </summary>
        [JsonPropertyName("daily_total_followers")]
        public int? DailyTotalFollowers { get; set; }

        /// <summary>
        /// Daily new followers
        /// </summary>
        [JsonPropertyName("daily_new_followers")]
        public int? DailyNewFollowers { get; set; }

        /// <summary>
        /// Daily lost followers
        /// </summary>
        [JsonPropertyName("daily_lost_followers")]
        public int? DailyLostFollowers { get; set; }

        /// <summary>
        /// The total number of people who are following your account on the date
        /// </summary>
        [JsonPropertyName("followers_count")]
        public int? FollowersCount { get; set; }

        /// <summary>
        /// Hourly follower activity
        /// </summary>
        [JsonPropertyName("audience_activity")]
        public List<AudienceActivity>? AudienceActivity { get; set; }

        /// <summary>
        /// Daily engaged audience
        /// </summary>
        [JsonPropertyName("engaged_audience")]
        public int? EngagedAudience { get; set; }
    }

    /// <summary>
    /// Audience age distribution
    /// </summary>
    public class AudienceAge
    {
        /// <summary>
        /// Age group
        /// </summary>
        [JsonPropertyName("age")]
        public string Age { get; set; } = string.Empty;

        /// <summary>
        /// Percentage of followers associated with the age group
        /// </summary>
        [JsonPropertyName("percentage")]
        public float Percentage { get; set; }
    }

    /// <summary>
    /// Audience gender distribution
    /// </summary>
    public class AudienceGender
    {
        /// <summary>
        /// Gender
        /// </summary>
        [JsonPropertyName("gender")]
        public string Gender { get; set; } = string.Empty;

        /// <summary>
        /// Percentage of followers associated with the gender
        /// </summary>
        [JsonPropertyName("percentage")]
        public float Percentage { get; set; }
    }

    /// <summary>
    /// Audience country distribution
    /// </summary>
    public class AudienceCountry
    {
        /// <summary>
        /// ISO 3166-1 alpha-2 country or region code
        /// </summary>
        [JsonPropertyName("country")]
        public string Country { get; set; } = string.Empty;

        /// <summary>
        /// Percentage of followers registered from a specified country or region
        /// </summary>
        [JsonPropertyName("percentage")]
        public float Percentage { get; set; }
    }

    /// <summary>
    /// Audience city distribution
    /// </summary>
    public class AudienceCity
    {
        /// <summary>
        /// City name
        /// </summary>
        [JsonPropertyName("city_name")]
        public string CityName { get; set; } = string.Empty;

        /// <summary>
        /// Percentage of followers associated with the city
        /// </summary>
        [JsonPropertyName("percentage")]
        public float Percentage { get; set; }
    }

    /// <summary>
    /// Hourly audience activity
    /// </summary>
    public class AudienceActivity
    {
        /// <summary>
        /// The specific hour in the day
        /// </summary>
        [JsonPropertyName("hour")]
        public string Hour { get; set; } = string.Empty;

        /// <summary>
        /// The number of followers active on TikTok during the hour
        /// </summary>
        [JsonPropertyName("count")]
        public int Count { get; set; }
    }
}
