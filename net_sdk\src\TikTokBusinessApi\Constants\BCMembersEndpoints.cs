/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API BC Members endpoints
    /// </summary>
    public static class BCMembersEndpoints
    {
        /// <summary>
        /// Get the members of a BC endpoint (v1.3)
        /// </summary>
        public const string GetMembers = "/v1.3/bc/member/get/";

        /// <summary>
        /// Invite members to a BC endpoint (v1.3)
        /// </summary>
        public const string InviteMembers = "/v1.3/bc/member/invite/";

        /// <summary>
        /// Update info of a BC member endpoint (v1.3)
        /// </summary>
        public const string UpdateMember = "/v1.3/bc/member/update/";

        /// <summary>
        /// Delete a member from a BC endpoint (v1.3)
        /// </summary>
        public const string DeleteMember = "/v1.3/bc/member/delete/";
    }
}
