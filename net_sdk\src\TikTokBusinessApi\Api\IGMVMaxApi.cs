/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API GMV Max operations
    /// </summary>
    public interface IGMVMaxApi
    {
        /// <summary>
        /// Retrieve the GMV Max Campaigns within an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="filtering">Filters on the data</param>
        /// <param name="fields">Fields that you want to get</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing GMV Max campaigns</returns>
        Task<TikTokApiResponse<GMVMaxCampaignGetResponse>> GetCampaignsAsync(
            string advertiserId,
            GMVMaxCampaignGetFiltering filtering,
            List<string>? fields = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve the details of a GMV Max Campaign
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="campaignId">The ID of a GMV Max Campaign</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing GMV Max campaign details</returns>
        Task<TikTokApiResponse<GMVMaxCampaignInfo>> GetCampaignInfoAsync(
            string advertiserId,
            string campaignId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a GMV Max Campaign
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing campaign details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created campaign details</returns>
        //Task<Response<GMVMaxCampaignInfo>> CreateCampaignAsync(
        //    string accessToken,
        //    GMVMaxCampaignCreateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update a GMV Max Campaign
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing campaign updates</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated campaign details</returns>
        //Task<Response<GMVMaxCampaignInfo>> UpdateCampaignAsync(
        //    string accessToken,
        //    GMVMaxCampaignUpdateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update the status of a GMV Max Campaign
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing status update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated campaign details</returns>
        //Task<Response<GMVMaxCampaignInfo>> UpdateCampaignStatusAsync(
        //    string accessToken,
        //    GMVMaxCampaignStatusUpdateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Obtain a list of TikTok Shops that an ad account has access to and whether the TikTok Shops can be used to create GMV Max Campaigns
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TikTok Shops list</returns>
        Task<Response<GMVMaxStoreListResponse>> GetStoresAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Obtain the recommended ROI target and budget for a Product GMV Max or Live GMV Campaign that uses a specific TikTok Shop
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="storeId">The ID of the TikTok Shop</param>
        /// <param name="shoppingAdsType">The type of the GMV Max Campaign</param>
        /// <param name="optimizationGoal">Optimization goal</param>
        /// <param name="itemGroupIds">The list of SPU IDs for specific products</param>
        /// <param name="identityId">The LIVE source identity (required for LIVE campaigns)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing bid recommendations</returns>
        Task<Response<GMVMaxBidRecommendationResponse>> GetBidRecommendationAsync(
            string accessToken,
            string advertiserId,
            string storeId,
            string shoppingAdsType,
            string optimizationGoal,
            List<string>? itemGroupIds = null,
            string? identityId = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Obtain a list of identities that are associated with a TikTok Shop and whether the identities are available for GMV Max Campaigns
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="storeId">The ID of the TikTok Shop</param>
        /// <param name="storeAuthorizedBcId">ID of the Business Center that is authorized to access the TikTok Shop</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing identities list</returns>
        Task<Response<GMVMaxIdentityListResponse>> GetIdentitiesAsync(
            string accessToken,
            string advertiserId,
            string storeId,
            string storeAuthorizedBcId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Run a report on GMV Max Campaigns
        /// </summary>
        /// <param name="request">Request containing all parameters for the report</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing GMV Max Campaign report data</returns>
        Task<TikTokApiResponse<GMVMaxReportResponse>> GetReportAsync(
            GMVMaxReportRequest request,
            CancellationToken cancellationToken = default);
    }
}
