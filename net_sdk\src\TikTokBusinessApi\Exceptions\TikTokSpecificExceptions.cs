/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;

namespace TikTokBusinessApi.Exceptions
{
    /// <summary>
    /// Exception thrown for authentication and authorization errors
    /// </summary>
    public class TikTokAuthenticationException : SdkException
    {
        public TikTokAuthenticationException(string requestId, int errorCode, string message, 
            int? httpStatusCode = null, string? rawResponse = null) 
            : base(requestId, errorCode, message, httpStatusCode, rawResponse)
        {
        }
    }

    /// <summary>
    /// Exception thrown for request validation errors
    /// </summary>
    public class TikTokValidationException : SdkException
    {
        /// <summary>
        /// Field that failed validation
        /// </summary>
        public string? FieldName { get; set; }

        /// <summary>
        /// Validation error details
        /// </summary>
        public List<ValidationError> ValidationErrors { get; set; } = new();

        public TikTokValidationException(string requestId, int errorCode, string message, 
            int? httpStatusCode = null, string? rawResponse = null) 
            : base(requestId, errorCode, message, httpStatusCode, rawResponse)
        {
        }

        public TikTokValidationException(string requestId, int errorCode, string message, string fieldName,
            int? httpStatusCode = null, string? rawResponse = null) 
            : base(requestId, errorCode, message, httpStatusCode, rawResponse)
        {
            FieldName = fieldName;
        }
    }

    /// <summary>
    /// Exception thrown for rate limiting errors
    /// </summary>
    public class TikTokRateLimitException : SdkException
    {
        /// <summary>
        /// Time when the rate limit resets
        /// </summary>
        public DateTimeOffset? ResetTime { get; set; }

        /// <summary>
        /// Number of requests remaining
        /// </summary>
        public int? RemainingRequests { get; set; }

        /// <summary>
        /// Rate limit window duration
        /// </summary>
        public TimeSpan? WindowDuration { get; set; }

        public TikTokRateLimitException(string requestId, int errorCode, string message, 
            int? httpStatusCode = null, string? rawResponse = null) 
            : base(requestId, errorCode, message, httpStatusCode, rawResponse)
        {
        }

        public TikTokRateLimitException(string requestId, int errorCode, string message, 
            DateTimeOffset? resetTime, int? remainingRequests, TimeSpan? windowDuration,
            int? httpStatusCode = null, string? rawResponse = null) 
            : base(requestId, errorCode, message, httpStatusCode, rawResponse)
        {
            ResetTime = resetTime;
            RemainingRequests = remainingRequests;
            WindowDuration = windowDuration;
        }
    }

    /// <summary>
    /// Exception thrown for resource-related errors
    /// </summary>
    public class TikTokResourceException : SdkException
    {
        /// <summary>
        /// Type of resource that caused the error
        /// </summary>
        public string? ResourceType { get; set; }

        /// <summary>
        /// ID of the resource that caused the error
        /// </summary>
        public string? ResourceId { get; set; }

        public TikTokResourceException(string requestId, int errorCode, string message, 
            int? httpStatusCode = null, string? rawResponse = null) 
            : base(requestId, errorCode, message, httpStatusCode, rawResponse)
        {
        }

        public TikTokResourceException(string requestId, int errorCode, string message, 
            string resourceType, string resourceId,
            int? httpStatusCode = null, string? rawResponse = null) 
            : base(requestId, errorCode, message, httpStatusCode, rawResponse)
        {
            ResourceType = resourceType;
            ResourceId = resourceId;
        }
    }

    /// <summary>
    /// Exception thrown for business logic errors
    /// </summary>
    public class TikTokBusinessException : SdkException
    {
        /// <summary>
        /// Business context that caused the error
        /// </summary>
        public Dictionary<string, object> BusinessContext { get; set; } = new();

        public TikTokBusinessException(string requestId, int errorCode, string message, 
            int? httpStatusCode = null, string? rawResponse = null) 
            : base(requestId, errorCode, message, httpStatusCode, rawResponse)
        {
        }

        public TikTokBusinessException(string requestId, int errorCode, string message, 
            Dictionary<string, object> businessContext,
            int? httpStatusCode = null, string? rawResponse = null) 
            : base(requestId, errorCode, message, httpStatusCode, rawResponse)
        {
            BusinessContext = businessContext;
        }
    }

    /// <summary>
    /// Exception thrown for network and infrastructure errors
    /// </summary>
    public class TikTokNetworkException : SdkException
    {
        /// <summary>
        /// Network error details
        /// </summary>
        public NetworkErrorDetails? NetworkDetails { get; set; }

        public TikTokNetworkException(string requestId, int errorCode, string message, 
            int? httpStatusCode = null, string? rawResponse = null) 
            : base(requestId, errorCode, message, httpStatusCode, rawResponse)
        {
        }

        public TikTokNetworkException(string requestId, int errorCode, string message, 
            NetworkErrorDetails networkDetails,
            int? httpStatusCode = null, string? rawResponse = null) 
            : base(requestId, errorCode, message, httpStatusCode, rawResponse)
        {
            NetworkDetails = networkDetails;
        }
    }

    /// <summary>
    /// Validation error details
    /// </summary>
    public class ValidationError
    {
        /// <summary>
        /// Field name that failed validation
        /// </summary>
        public string FieldName { get; set; } = string.Empty;

        /// <summary>
        /// Validation error message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Invalid value that was provided
        /// </summary>
        public object? InvalidValue { get; set; }

        /// <summary>
        /// Expected value or format
        /// </summary>
        public string? ExpectedFormat { get; set; }
    }

    /// <summary>
    /// Network error details
    /// </summary>
    public class NetworkErrorDetails
    {
        /// <summary>
        /// Type of network error
        /// </summary>
        public string ErrorType { get; set; } = string.Empty;

        /// <summary>
        /// Target host or endpoint
        /// </summary>
        public string? TargetHost { get; set; }

        /// <summary>
        /// Connection timeout duration
        /// </summary>
        public TimeSpan? Timeout { get; set; }

        /// <summary>
        /// Number of retry attempts made
        /// </summary>
        public int RetryAttempts { get; set; }

        /// <summary>
        /// Additional network context
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// Factory for creating specific TikTok exceptions based on error codes
    /// </summary>
    public static class TikTokExceptionFactory
    {
        /// <summary>
        /// Creates the appropriate TikTok exception based on error code
        /// </summary>
        /// <param name="requestId">Request ID</param>
        /// <param name="errorCode">TikTok error code</param>
        /// <param name="message">Error message</param>
        /// <param name="httpStatusCode">HTTP status code</param>
        /// <param name="rawResponse">Raw response content</param>
        /// <returns>Specific TikTok exception</returns>
        public static SdkException CreateException(string requestId, int errorCode, string message, 
            int? httpStatusCode = null, string? rawResponse = null)
        {
            return TikTokErrorCodes.GetErrorCategory(errorCode) switch
            {
                TikTokErrorCategory.Authentication => new TikTokAuthenticationException(requestId, errorCode, message, httpStatusCode, rawResponse),
                TikTokErrorCategory.Validation => new TikTokValidationException(requestId, errorCode, message, httpStatusCode, rawResponse),
                TikTokErrorCategory.RateLimit => new TikTokRateLimitException(requestId, errorCode, message, httpStatusCode, rawResponse),
                TikTokErrorCategory.Resource => new TikTokResourceException(requestId, errorCode, message, httpStatusCode, rawResponse),
                TikTokErrorCategory.Business => new TikTokBusinessException(requestId, errorCode, message, httpStatusCode, rawResponse),
                TikTokErrorCategory.Network => new TikTokNetworkException(requestId, errorCode, message, httpStatusCode, rawResponse),
                _ => SdkException.FromTikTokResponse(requestId, errorCode, message, httpStatusCode, rawResponse)
            };
        }
    }
}
