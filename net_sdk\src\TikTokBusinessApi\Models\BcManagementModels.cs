/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using TikTokBusinessApi.Core;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request parameters for getting Business Centers
    /// </summary>
    public class GetBusinessCentersRequest
    {
        /// <summary>
        /// The Business Center ID. When not passed, returns the user's entire list of Business Centers by default
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string? BcId { get; set; }

        /// <summary>
        /// Current number of pages. Default value: 1. Value range: ≥ 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10. Value range: 1-50
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Response for getting Business Centers
    /// </summary>
    public class GetBusinessCentersResponse
    {
        /// <summary>
        /// Business Center list
        /// </summary>
        [JsonPropertyName("list")]
        public List<BusinessCenterInfo>? List { get; set; }

        /// <summary>
        /// Page information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Business Center information
    /// </summary>
    public class BusinessCenterInfo
    {
        /// <summary>
        /// Business Center details
        /// </summary>
        [JsonPropertyName("bc_info")]
        public BusinessCenter? BcInfo { get; set; }

        /// <summary>
        /// Business Center user type. Enum values: ADMIN (administrator), STANDARD (standard)
        /// </summary>
        [JsonPropertyName("user_role")]
        public string? UserRole { get; set; }

        /// <summary>
        /// Advanced role of the user in the Business Center other than basic role
        /// </summary>
        [JsonPropertyName("ext_user_role")]
        public ExtendedUserRole? ExtUserRole { get; set; }
    }

    /// <summary>
    /// Business Center details
    /// </summary>
    public class BusinessCenter
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string? BcId { get; set; }

        /// <summary>
        /// Business Center name
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Business Center Company Name
        /// </summary>
        [JsonPropertyName("company")]
        public string? Company { get; set; }

        /// <summary>
        /// Business Center payment currency
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// Business Center registration code
        /// </summary>
        [JsonPropertyName("registered_area")]
        public string? RegisteredArea { get; set; }

        /// <summary>
        /// Business Center status. Enum values: REVIEWING (under review), DENY (rejected), ENABLE (passed), PUNISH (disabled)
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// The time zone where the Business Center is located
        /// </summary>
        [JsonPropertyName("timezone")]
        public string? Timezone { get; set; }

        /// <summary>
        /// Business Center type. Enum values: NORMAL, DIRECT, AGENCY, SELF_SERVICE, SELF_SERVICE_AGENCY
        /// </summary>
        [JsonPropertyName("type")]
        public string? Type { get; set; }
    }

    /// <summary>
    /// Extended user role information
    /// </summary>
    public class ExtendedUserRole
    {
        /// <summary>
        /// User finance role. Enum values: MANAGER (Finance Manager), ANALYST (Finance Analyst)
        /// </summary>
        [JsonPropertyName("finance_role")]
        public string? FinanceRole { get; set; }
    }

    /// <summary>
    /// Request parameters for getting Business Center changelog
    /// </summary>
    public class GetBusinessCenterChangelogRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public ChangelogFiltering? Filtering { get; set; }

        /// <summary>
        /// The language of the activity log. Default value: en
        /// </summary>
        [JsonPropertyName("lang")]
        public string? Lang { get; set; }

        /// <summary>
        /// Field to sort by. Default value: operation_time
        /// </summary>
        [JsonPropertyName("sort_field")]
        public string? SortField { get; set; }

        /// <summary>
        /// Sorting order. Enum values: DESC (Descending), ASC (Ascending). Default value: DESC
        /// </summary>
        [JsonPropertyName("sort_type")]
        public string? SortType { get; set; }

        /// <summary>
        /// Current page number. Value range: ≥ 1. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Value range: 1-50. Default value: 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering conditions for changelog
    /// </summary>
    public class ChangelogFiltering
    {
        /// <summary>
        /// Query start date, in the format of YYYY-MM-DD (UTC+0)
        /// </summary>
        [JsonPropertyName("start_date")]
        public string? StartDate { get; set; }

        /// <summary>
        /// Query end date, in the format of YYYY-MM-DD (UTC+0)
        /// </summary>
        [JsonPropertyName("end_date")]
        public string? EndDate { get; set; }

        /// <summary>
        /// Activity type to query. Enum values: ALL, USER, ACCOUNT, ASSET, BUSINESS. Default value: ALL
        /// </summary>
        [JsonPropertyName("activity_type")]
        public string? ActivityType { get; set; }
    }

    /// <summary>
    /// Response for getting Business Center changelog
    /// </summary>
    public class GetBusinessCenterChangelogResponse
    {
        /// <summary>
        /// Change log information
        /// </summary>
        [JsonPropertyName("changelog_list")]
        public List<ChangelogEntry>? ChangelogList { get; set; }

        /// <summary>
        /// Page information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Changelog entry information
    /// </summary>
    public class ChangelogEntry
    {
        /// <summary>
        /// The time of the activity, in the format of YYYY-MM-DD HH:MM:SS (UTC+0)
        /// </summary>
        [JsonPropertyName("time")]
        public string? Time { get; set; }

        /// <summary>
        /// Activity type. Enum values: USER, ACCOUNT, ASSET, BUSINESS
        /// </summary>
        [JsonPropertyName("activity_type")]
        public string? ActivityType { get; set; }

        /// <summary>
        /// The user ID of the operator
        /// </summary>
        [JsonPropertyName("operator_id")]
        public string? OperatorId { get; set; }

        /// <summary>
        /// Activity log details
        /// </summary>
        [JsonPropertyName("activity_log")]
        public string? ActivityLog { get; set; }
    }
    // ===== BC Member API Models =====

    /// <summary>
    /// Request parameters for getting BC members
    /// </summary>
    public class GetBcMembersRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Current page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Maximum page size is 20. Default value: 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public BcMemberFiltering? Filtering { get; set; }
    }

    /// <summary>
    /// Filtering conditions for BC members
    /// </summary>
    public class BcMemberFiltering
    {
        /// <summary>
        /// Keyword to filter by
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }

        /// <summary>
        /// Basic roles within Business Center. Enum values: ADMIN, STANDARD
        /// </summary>
        [JsonPropertyName("user_role")]
        public string? UserRole { get; set; }

        /// <summary>
        /// User's relation to the Business Center. Enum values: BOUND, PENDING, REJECTED
        /// </summary>
        [JsonPropertyName("relation_status")]
        public string? RelationStatus { get; set; }
    }

    /// <summary>
    /// Response for getting BC members
    /// </summary>
    public class GetBcMembersResponse
    {
        /// <summary>
        /// Member information list
        /// </summary>
        [JsonPropertyName("list")]
        public List<BcMemberInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// BC Member information
    /// </summary>
    public class BcMemberInfo
    {
        /// <summary>
        /// User ID
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// User name
        /// </summary>
        [JsonPropertyName("user_name")]
        public string? UserName { get; set; }

        /// <summary>
        /// Basic roles within Business Center. Enum values: ADMIN, STANDARD
        /// </summary>
        [JsonPropertyName("user_role")]
        public string? UserRole { get; set; }

        /// <summary>
        /// User's relation to the Business Center. Enum values: BOUND, PENDING, REJECTED
        /// </summary>
        [JsonPropertyName("relation_status")]
        public string? RelationStatus { get; set; }

        /// <summary>
        /// User email address
        /// </summary>
        [JsonPropertyName("user_email")]
        public string? UserEmail { get; set; }

        /// <summary>
        /// Advanced role of the user in the Business Center other than basic role
        /// </summary>
        [JsonPropertyName("ext_user_role")]
        public ExtendedUserRole? ExtUserRole { get; set; }
    }

    /// <summary>
    /// Request parameters for inviting BC members
    /// </summary>
    public class InviteBcMembersRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// List of emails (Required). User emails that have been invited earlier will be ignored
        /// </summary>
        [JsonPropertyName("emails")]
        public List<string> Emails { get; set; } = new List<string>();

        /// <summary>
        /// Basic roles within Business Center. Enum values: ADMIN, STANDARD
        /// </summary>
        [JsonPropertyName("user_role")]
        public string? UserRole { get; set; }

        /// <summary>
        /// List of ad account IDs assigned to the invited members. Maximum number is 50
        /// </summary>
        [JsonPropertyName("asset_ids")]
        public List<long>? AssetIds { get; set; }

        /// <summary>
        /// Ad account role assigned to the members invited
        /// </summary>
        [JsonPropertyName("advertiser_role")]
        public string? AdvertiserRole { get; set; }

        /// <summary>
        /// Advanced role of the user in the Business Center other than basic role
        /// </summary>
        [JsonPropertyName("ext_user_role")]
        public ExtendedUserRole? ExtUserRole { get; set; }
    }

    /// <summary>
    /// Response for inviting BC members
    /// </summary>
    public class InviteBcMembersResponse
    {
        // Empty response object as per API documentation
    }

    /// <summary>
    /// Request parameters for updating BC member
    /// </summary>
    public class UpdateBcMemberRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// User ID of the Business Center's member (Required)
        /// </summary>
        [JsonPropertyName("user_id")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// New basic role for the user in this Business Center. Enum values: ADMIN, STANDARD
        /// </summary>
        [JsonPropertyName("user_role")]
        public string? UserRole { get; set; }

        /// <summary>
        /// New name of the user
        /// </summary>
        [JsonPropertyName("user_name")]
        public string? UserName { get; set; }

        /// <summary>
        /// Advanced role of the user in the Business Center other than basic role
        /// </summary>
        [JsonPropertyName("ext_user_role")]
        public ExtendedUserRole? ExtUserRole { get; set; }
    }

    /// <summary>
    /// Response for updating BC member
    /// </summary>
    public class UpdateBcMemberResponse
    {
        // Empty response object as per API documentation
    }

    /// <summary>
    /// Request parameters for deleting BC member
    /// </summary>
    public class DeleteBcMemberRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// User ID (Conditional). For members that have accepted the invitation to join the Business Center, delete by user_id
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// User email (Conditional). For members that haven't accepted the invitation, delete by user_email
        /// </summary>
        [JsonPropertyName("user_email")]
        public string? UserEmail { get; set; }
    }

    /// <summary>
    /// Response for deleting BC member
    /// </summary>
    public class DeleteBcMemberResponse
    {
        // Empty response object as per API documentation
    }
}
