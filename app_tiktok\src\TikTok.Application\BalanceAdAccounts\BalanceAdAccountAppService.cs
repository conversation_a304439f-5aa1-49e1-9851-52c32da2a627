using System;
using System.Linq;
using System.Threading.Tasks;
using TikTok.BalanceAdAccounts;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Permissions;
using TikTok.Repositories;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// Application Service cho BalanceAdAccount
    /// </summary>
    public class BalanceAdAccountAppService :
        CrudAppService<
            RawBalanceAdAccountEntity,
            BalanceAdAccountDto,
            Guid,
            GetBalanceAdAccountListDto,
            CreateBalanceAdAccountDto,
            UpdateBalanceAdAccountDto>,
        IBalanceAdAccountAppService
    {
        private readonly IBalanceAdAccountRepository _balanceAdAccountRepository;

        public BalanceAdAccountAppService(IBalanceAdAccountRepository balanceAdAccountRepository)
            : base(balanceAdAccountRepository)
        {
            _balanceAdAccountRepository = balanceAdAccountRepository;

            // Cấu hình quyền truy cập
            GetPolicyName = TikTokPermissions.BalanceAdAccounts.Default;
            GetListPolicyName = TikTokPermissions.BalanceAdAccounts.Default;
            CreatePolicyName = TikTokPermissions.BalanceAdAccounts.Create;
            UpdatePolicyName = TikTokPermissions.BalanceAdAccounts.Edit;
            DeletePolicyName = TikTokPermissions.BalanceAdAccounts.Delete;
        }

        /// <summary>
        /// Ghi đè phương thức GetListAsync để sử dụng repository
        /// </summary>
        /// <param name="input">Input để lấy danh sách</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo</returns>
        public override async Task<PagedResultDto<BalanceAdAccountDto>> GetListAsync(GetBalanceAdAccountListDto input)
        {
            // Sử dụng repository để lấy danh sách và tổng số
            var entities = await _balanceAdAccountRepository.GetListAsync(
                sorting: input.Sorting,
                maxResultCount: input.MaxResultCount,
                skipCount: input.SkipCount,
                filter: input.Filter,
                advertiserId: input.AdvertiserId,
                advertiserName: input.AdvertiserName,
                bcId: input.BcId,
                advertiserStatus: input.AdvertiserStatus,
                advertiserType: input.AdvertiserType,
                budgetMode: input.BudgetMode,
                currency: input.Currency,
                timezone: input.Timezone,
                createTimeFrom: input.CreateTimeFrom,
                createTimeTo: input.CreateTimeTo,
                dateFrom: input.DateFrom,
                dateTo: input.DateTo,
                accountBalanceFrom: input.AccountBalanceFrom,
                accountBalanceTo: input.AccountBalanceTo,
                budgetFrom: input.BudgetFrom,
                budgetTo: input.BudgetTo,
                accountOpenDaysFrom: input.AccountOpenDaysFrom,
                accountOpenDaysTo: input.AccountOpenDaysTo);

            var totalCount = await _balanceAdAccountRepository.GetCountAsync(
                filter: input.Filter,
                advertiserId: input.AdvertiserId,
                advertiserName: input.AdvertiserName,
                bcId: input.BcId,
                advertiserStatus: input.AdvertiserStatus,
                advertiserType: input.AdvertiserType,
                budgetMode: input.BudgetMode,
                currency: input.Currency,
                timezone: input.Timezone,
                createTimeFrom: input.CreateTimeFrom,
                createTimeTo: input.CreateTimeTo,
                dateFrom: input.DateFrom,
                dateTo: input.DateTo,
                accountBalanceFrom: input.AccountBalanceFrom,
                accountBalanceTo: input.AccountBalanceTo,
                budgetFrom: input.BudgetFrom,
                budgetTo: input.BudgetTo,
                accountOpenDaysFrom: input.AccountOpenDaysFrom,
                accountOpenDaysTo: input.AccountOpenDaysTo);

            var dtos = ObjectMapper.Map<RawBalanceAdAccountEntity[], BalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<BalanceAdAccountDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }

        public override async Task<BalanceAdAccountDto> CreateAsync(CreateBalanceAdAccountDto input)
        {
            // Kiểm tra AdvertiserId đã tồn tại chưa
            if (await _balanceAdAccountRepository.ExistsByAdvertiserIdAsync(input.AdvertiserId))
            {
                throw new UserFriendlyException("AdvertiserId đã tồn tại trong hệ thống.");
            }

            // Đảm bảo Date lưu đầy đủ thời gian
            var entity = ObjectMapper.Map<CreateBalanceAdAccountDto, RawBalanceAdAccountEntity>(input);
            var result = await _balanceAdAccountRepository.InsertAsync(entity);
            return ObjectMapper.Map<RawBalanceAdAccountEntity, BalanceAdAccountDto>(result);
        }

        public override async Task<BalanceAdAccountDto> UpdateAsync(Guid id, UpdateBalanceAdAccountDto input)
        {
            // Kiểm tra AdvertiserId đã tồn tại chưa (trừ record hiện tại)
            if (await _balanceAdAccountRepository.ExistsByAdvertiserIdAsync(input.AdvertiserId, id))
            {
                throw new UserFriendlyException("AdvertiserId đã tồn tại trong hệ thống.");
            }

            var entity = await _balanceAdAccountRepository.GetAsync(id);
            ObjectMapper.Map(input, entity);

            // Đảm bảo Date lưu đầy đủ thời gian
            if (input.Date.HasValue)
            {
                entity.Date = input.Date.Value; // Lưu đầy đủ thời gian
            }

            var result = await _balanceAdAccountRepository.UpdateAsync(entity);
            return ObjectMapper.Map<RawBalanceAdAccountEntity, BalanceAdAccountDto>(result);
        }

        public async Task<BalanceAdAccountDto> GetByAdvertiserIdAsync(string advertiserId)
        {
            var entity = await _balanceAdAccountRepository.GetByAdvertiserIdAsync(advertiserId);
            if (entity == null)
            {
                throw new UserFriendlyException("Không tìm thấy tài khoản với AdvertiserId: " + advertiserId);
            }

            return ObjectMapper.Map<RawBalanceAdAccountEntity, BalanceAdAccountDto>(entity);
        }

        public async Task<BalanceAdAccountDto> GetByAdvertiserIdAndDateAsync(string advertiserId, DateTime date)
        {
            var entity = await _balanceAdAccountRepository.GetByAdvertiserIdAndDateAsync(advertiserId, date);
            if (entity == null)
            {
                throw new UserFriendlyException($"Không tìm thấy tài khoản với AdvertiserId: {advertiserId} và ngày: {date:yyyy-MM-dd}");
            }

            return ObjectMapper.Map<RawBalanceAdAccountEntity, BalanceAdAccountDto>(entity);
        }

        public async Task<BalanceAdAccountDto> GetLatestByAdvertiserIdAsync(string advertiserId)
        {
            var entity = await _balanceAdAccountRepository.GetLatestByAdvertiserIdAsync(advertiserId);
            if (entity == null)
            {
                throw new UserFriendlyException($"Không tìm thấy tài khoản với AdvertiserId: {advertiserId}");
            }

            return ObjectMapper.Map<RawBalanceAdAccountEntity, BalanceAdAccountDto>(entity);
        }

        public async Task<PagedResultDto<BalanceAdAccountDto>> GetByBcIdAsync(string bcId)
        {
            var entities = await _balanceAdAccountRepository.GetByBcIdAsync(bcId);
            var dtos = ObjectMapper.Map<RawBalanceAdAccountEntity[], BalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<BalanceAdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<PagedResultDto<BalanceAdAccountDto>> GetByAdvertiserStatusAsync(AdvertiserAccountStatus advertiserStatus)
        {
            var entities = await _balanceAdAccountRepository.GetByAdvertiserStatusAsync(advertiserStatus);
            var dtos = ObjectMapper.Map<RawBalanceAdAccountEntity[], BalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<BalanceAdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<PagedResultDto<BalanceAdAccountDto>> GetByAdvertiserTypeAsync(AdAccountType advertiserType)
        {
            var entities = await _balanceAdAccountRepository.GetByAdvertiserTypeAsync(advertiserType);
            var dtos = ObjectMapper.Map<RawBalanceAdAccountEntity[], BalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<BalanceAdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<PagedResultDto<BalanceAdAccountDto>> GetByCurrencyAsync(string currency)
        {
            var entities = await _balanceAdAccountRepository.GetByCurrencyAsync(currency);
            var dtos = ObjectMapper.Map<RawBalanceAdAccountEntity[], BalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<BalanceAdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<PagedResultDto<BalanceAdAccountDto>> GetLowBalanceAccountsAsync(decimal minBalance)
        {
            var entities = await _balanceAdAccountRepository.GetLowBalanceAccountsAsync(minBalance);
            var dtos = ObjectMapper.Map<RawBalanceAdAccountEntity[], BalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<BalanceAdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<PagedResultDto<BalanceAdAccountDto>> GetByCreateTimeRangeAsync(DateTime fromDate, DateTime toDate)
        {
            var entities = await _balanceAdAccountRepository.GetByCreateTimeRangeAsync(fromDate, toDate);
            var dtos = ObjectMapper.Map<RawBalanceAdAccountEntity[], BalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<BalanceAdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<bool> ExistsByAdvertiserIdAsync(string advertiserId, Guid? excludeId = null)
        {
            return await _balanceAdAccountRepository.ExistsByAdvertiserIdAsync(advertiserId, excludeId);
        }

        public async Task<BalanceAdAccountDto> UpdateStatusAsync(Guid id, AdvertiserAccountStatus status)
        {
            var entity = await _balanceAdAccountRepository.GetAsync(id);
            entity.AdvertiserStatus = status;

            var result = await _balanceAdAccountRepository.UpdateAsync(entity);
            return ObjectMapper.Map<RawBalanceAdAccountEntity, BalanceAdAccountDto>(result);
        }

        public async Task<BalanceAdAccountDto> UpdateBalanceAsync(Guid id, decimal newBalance)
        {
            if (newBalance < 0)
            {
                throw new UserFriendlyException("Số dư không thể âm.");
            }

            var entity = await _balanceAdAccountRepository.GetAsync(id);
            entity.AccountBalance = newBalance;

            var result = await _balanceAdAccountRepository.UpdateAsync(entity);
            return ObjectMapper.Map<RawBalanceAdAccountEntity, BalanceAdAccountDto>(result);
        }

        public async Task<BalanceAdAccountDto> UpdateBudgetAsync(Guid id, decimal budget)
        {
            if (budget < 0)
            {
                throw new UserFriendlyException("Ngân sách không thể âm.");
            }

            var entity = await _balanceAdAccountRepository.GetAsync(id);
            entity.Budget = budget;

            var result = await _balanceAdAccountRepository.UpdateAsync(entity);
            return ObjectMapper.Map<RawBalanceAdAccountEntity, BalanceAdAccountDto>(result);
        }

        public async Task<BalanceAdAccountStatisticsDto> GetStatisticsAsync()
        {
            var allAccounts = await _balanceAdAccountRepository.GetListAsync();
            var statistics = new BalanceAdAccountStatisticsDto();

            if (allAccounts.Any())
            {
                statistics.TotalAccounts = allAccounts.Count;
                statistics.TotalAccountBalance = allAccounts.Sum(x => x.AccountBalance);
                statistics.TotalBudget = allAccounts.Sum(x => x.Budget);
                statistics.AverageAccountBalance = allAccounts.Average(x => x.AccountBalance);
                statistics.AverageBudget = allAccounts.Average(x => x.Budget);

                // Thống kê theo trạng thái
                statistics.AccountsByStatus = allAccounts
                    .GroupBy(x => x.AdvertiserStatus)
                    .ToDictionary(g => g.Key, g => (long)g.Count());

                // Thống kê theo loại
                statistics.AccountsByType = allAccounts
                    .GroupBy(x => x.AdvertiserType)
                    .ToDictionary(g => g.Key, g => (long)g.Count());

                // Thống kê theo tiền tệ
                statistics.AccountsByCurrency = allAccounts
                    .GroupBy(x => x.Currency)
                    .ToDictionary(g => g.Key, g => (long)g.Count());

                // Tài khoản có số dư thấp
                statistics.LowBalanceAccounts = allAccounts.Count(x => x.AccountBalance < 100);

                // Tài khoản mới trong tháng này
                var thisMonth = DateTime.UtcNow.AddDays(-DateTime.UtcNow.Day + 1);
                statistics.NewAccountsThisMonth = allAccounts.Count(x => x.CreateTime >= thisMonth);

                // Tài khoản mới trong tuần này
                var thisWeek = DateTime.UtcNow.AddDays(-(int)DateTime.UtcNow.DayOfWeek);
                statistics.NewAccountsThisWeek = allAccounts.Count(x => x.CreateTime >= thisWeek);
            }

            return statistics;
        }
    }
}