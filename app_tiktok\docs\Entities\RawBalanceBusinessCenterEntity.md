# RawBalanceBusinessCenterEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawBalanceBusinessCenters`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawBalanceBusinessCenters`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho ngân sách của Business Center (Balance Business Center). Lưu trữ thông tin về số dư và ngân sách tổng hợp của Business Center, bao gồm các loại số dư khác nhau như tiền mặt, voucher, và số dư bị đóng băng.

**Lưu ý quan trọng về logic đồng bộ:**

-   Entity này lưu trữ lịch sử thay đổi dữ liệu theo thời gian thực
-   Mỗi khi có thay đổi dữ liệu từ API, một bản ghi mới sẽ được tạo thay vì cập nhật bản ghi cũ
-   Trường `Date` lưu đầy đủ thời gian UTC khi dữ liệu được đồng bộ
-   Dữ liệu thời gian từ API sẽ được chuyển đổi về UTC trước khi lưu vào database

## Bảng dữ liệu: RawBalanceBusinessCenter

| Tên Field            | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                                                                      |
| -------------------- | ------------ | -------- | ------ | ------------------------------------------------------------------------------------------ |
| Id                   | Guid         | ✓        | -      | ID duy nhất của bản ghi (Primary Key)                                                      |
| BcId                 | string       | ✓        | 100    | ID của Business Center                                                                     |
| Currency             | string       | ✓        | 10     | Tiền tệ của Business Center                                                                |
| AccountBalance       | decimal?     | ❌       | -      | Tổng số dư của Business Center, làm tròn đến hai chữ số thập phân                          |
| ValidAccountBalance  | decimal?     | ❌       | -      | Số dư tài khoản hợp lệ của Business Center, làm tròn đến hai chữ số thập phân              |
| FrozenBalance        | decimal?     | ❌       | -      | Số dư bị đóng băng của Business Center, làm tròn đến hai chữ số thập phân                  |
| Tax                  | decimal?     | ❌       | -      | Thuế của Business Center, làm tròn đến hai chữ số thập phân                                |
| CashBalance          | decimal?     | ❌       | -      | Số dư tiền mặt của Business Center, làm tròn đến hai chữ số thập phân                      |
| ValidCashBalance     | decimal?     | ❌       | -      | Số dư tiền mặt hợp lệ của Business Center, làm tròn đến hai chữ số thập phân               |
| GrantBalance         | decimal?     | ❌       | -      | Số dư phiếu giảm giá/voucher của Business Center, làm tròn đến hai chữ số thập phân        |
| ValidGrantBalance    | decimal?     | ❌       | -      | Số dư phiếu giảm giá/voucher hợp lệ của Business Center, làm tròn đến hai chữ số thập phân |
| Date                 | DateTime?    | ❌       | -      | Thời gian đồng bộ dữ liệu (UTC) - lưu đầy đủ thời gian                                     |
| Timezone             | string       | ❌       | 50     | Múi giờ của ngân sách                                                                      |
| CreationTime         | DateTime     | ✓        | -      | Thời gian tạo bản ghi (Audit)                                                              |
| CreatorId            | Guid?        | ❌       | -      | ID người tạo (Audit)                                                                       |
| LastModificationTime | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                                             |
| LastModifierId       | Guid?        | ❌       | -      | ID người sửa đổi cuối (Audit)                                                              |

## Cấu trúc dữ liệu

### Các loại số dư

-   **AccountBalance**: Tổng số dư của Business Center
-   **ValidAccountBalance**: Số dư hợp lệ có thể sử dụng
-   **FrozenBalance**: Số dư bị đóng băng, không thể sử dụng
-   **CashBalance**: Số dư tiền mặt
-   **ValidCashBalance**: Số dư tiền mặt hợp lệ
-   **GrantBalance**: Số dư phiếu giảm giá/voucher
-   **ValidGrantBalance**: Số dư phiếu giảm giá/voucher hợp lệ

### Thông tin bổ sung

-   **Tax**: Thuế phải nộp
-   **Currency**: Đơn vị tiền tệ
-   **Timezone**: Múi giờ để tính toán thời gian
-   **Date**: Thời điểm cập nhật thông tin ngân sách (UTC)

## Logic đồng bộ mới

### Cách thức hoạt động

1. **Lấy dữ liệu mới nhất**: Hệ thống sẽ lấy bản ghi mới nhất theo `BcId`
2. **So sánh thay đổi**: So sánh dữ liệu từ API với bản ghi mới nhất
3. **Tạo bản ghi mới**: Nếu có thay đổi, tạo bản ghi mới thay vì cập nhật bản ghi cũ
4. **Lưu thời gian UTC**: Tất cả thời gian được chuyển đổi về UTC trước khi lưu

### Các trường được so sánh để phát hiện thay đổi

-   AccountBalance, ValidAccountBalance, FrozenBalance, Tax
-   CashBalance, ValidCashBalance, GrantBalance, ValidGrantBalance
-   Currency

### Lợi ích của logic mới

-   **Lịch sử đầy đủ**: Lưu trữ toàn bộ lịch sử thay đổi
-   **Truy vết thời gian**: Biết chính xác thời điểm dữ liệu thay đổi
-   **Phân tích xu hướng**: Có thể phân tích xu hướng thay đổi theo thời gian
-   **Độ chính xác**: Không mất dữ liệu lịch sử khi cập nhật

## Mục đích sử dụng

-   Theo dõi tổng số dư và ngân sách của Business Center theo thời gian
-   Quản lý các loại số dư khác nhau (tiền mặt, voucher, đóng băng)
-   Cung cấp thông tin tài chính tổng hợp cho Business Center
-   Hỗ trợ báo cáo và phân tích tài chính
-   Theo dõi lịch sử thay đổi ngân sách theo thời gian
-   Phân tích xu hướng thay đổi số dư và ngân sách

## Lưu ý

-   Tất cả các trường số dư đều được làm tròn đến hai chữ số thập phân
-   Các trường số dư có thể null để đảm bảo tính linh hoạt khi chưa có dữ liệu
-   Entity này kế thừa từ AuditedEntity để có đầy đủ thông tin audit
-   Trường `Date` lưu đầy đủ thời gian UTC thay vì chỉ ngày tháng năm
