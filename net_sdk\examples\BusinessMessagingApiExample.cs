/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the Business Messaging API
    /// </summary>
    public class BusinessMessagingApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public BusinessMessagingApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example of sending a text message to a conversation
        /// </summary>
        /// <param name="businessId">Your business ID</param>
        /// <param name="conversationId">The conversation ID to send message to</param>
        /// <param name="messageText">The text message to send</param>
        /// <returns>Task representing the async operation</returns>
        public async Task SendTextMessageExample(string businessId, string conversationId, string messageText)
        {
            try
            {
                Console.WriteLine("=== Send Text Message Example ===");

                var request = new MessageSendRequest
                {
                    BusinessId = businessId,
                    RecipientType = RecipientType.Conversation,
                    Recipient = conversationId,
                    MessageType = MessageType.Text,
                    Text = new MessageText { Body = messageText }
                };

                Console.WriteLine($"Sending text message: {messageText}");
                Console.WriteLine($"To conversation: {conversationId}");

                var response = await _client.BusinessMessaging.SendMessageAsync(
                    _client.AccessToken,
                    request);

                if (response.Data?.Message != null)
                {
                    Console.WriteLine($"Message sent successfully!");
                    Console.WriteLine($"Message ID: {response.Data.Message.MessageId}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of sending an image message to a conversation
        /// </summary>
        /// <param name="businessId">Your business ID</param>
        /// <param name="conversationId">The conversation ID to send message to</param>
        /// <param name="imagePath">Path to the image file</param>
        /// <returns>Task representing the async operation</returns>
        public async Task SendImageMessageExample(string businessId, string conversationId, string imagePath)
        {
            try
            {
                Console.WriteLine("=== Send Image Message Example ===");

                // First, upload the image
                Console.WriteLine($"Uploading image: {imagePath}");
                
                using var fileStream = File.OpenRead(imagePath);
                var fileName = Path.GetFileName(imagePath);

                var uploadResponse = await _client.BusinessMessaging.UploadMediaAsync(
                    _client.AccessToken,
                    businessId,
                    fileStream,
                    fileName,
                    MediaType.Image);

                if (uploadResponse.Data?.MediaId == null)
                {
                    Console.WriteLine("Failed to upload image");
                    return;
                }

                Console.WriteLine($"Image uploaded successfully! Media ID: {uploadResponse.Data.MediaId}");

                // Now send the image message
                var request = new MessageSendRequest
                {
                    BusinessId = businessId,
                    RecipientType = RecipientType.Conversation,
                    Recipient = conversationId,
                    MessageType = MessageType.Image,
                    Image = new MessageImage { MediaId = uploadResponse.Data.MediaId }
                };

                Console.WriteLine($"Sending image message to conversation: {conversationId}");

                var response = await _client.BusinessMessaging.SendMessageAsync(
                    _client.AccessToken,
                    request);

                if (response.Data?.Message != null)
                {
                    Console.WriteLine($"Image message sent successfully!");
                    Console.WriteLine($"Message ID: {response.Data.Message.MessageId}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of sending a template message with buttons
        /// </summary>
        /// <param name="businessId">Your business ID</param>
        /// <param name="conversationId">The conversation ID to send message to</param>
        /// <returns>Task representing the async operation</returns>
        public async Task SendTemplateMessageExample(string businessId, string conversationId)
        {
            try
            {
                Console.WriteLine("=== Send Template Message Example ===");

                var request = new MessageSendRequest
                {
                    BusinessId = businessId,
                    RecipientType = RecipientType.Conversation,
                    Recipient = conversationId,
                    MessageType = MessageType.Template,
                    Template = new MessageTemplate
                    {
                        Type = TemplateType.QaButtonCard,
                        Title = "How can we help you today?",
                        Buttons = new List<MessageTemplateButton>
                        {
                            new MessageTemplateButton
                            {
                                Type = TemplateButtonType.Reply,
                                Title = "Product Information",
                                Id = "product_info"
                            },
                            new MessageTemplateButton
                            {
                                Type = TemplateButtonType.Reply,
                                Title = "Support",
                                Id = "support"
                            },
                            new MessageTemplateButton
                            {
                                Type = TemplateButtonType.Reply,
                                Title = "Pricing",
                                Id = "pricing"
                            }
                        }
                    }
                };

                Console.WriteLine($"Sending template message to conversation: {conversationId}");

                var response = await _client.BusinessMessaging.SendMessageAsync(
                    _client.AccessToken,
                    request);

                if (response.Data?.Message != null)
                {
                    Console.WriteLine($"Template message sent successfully!");
                    Console.WriteLine($"Message ID: {response.Data.Message.MessageId}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting conversations for a business account
        /// </summary>
        /// <param name="businessId">Your business ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task GetConversationsExample(string businessId)
        {
            try
            {
                Console.WriteLine("=== Get Conversations Example ===");

                Console.WriteLine($"Getting conversations for business: {businessId}");

                var response = await _client.BusinessMessaging.GetConversationsAsync(
                    _client.AccessToken,
                    businessId,
                    ConversationType.Single,
                    limit: 20,
                    cursor: 0);

                if (response.Data?.Conversations != null)
                {
                    Console.WriteLine($"Found {response.Data.Conversations.Count} conversations");
                    Console.WriteLine($"Has more: {response.Data.HasMore}");
                    Console.WriteLine($"Next cursor: {response.Data.Cursor}");

                    foreach (var conversation in response.Data.Conversations)
                    {
                        Console.WriteLine($"- Conversation ID: {conversation.ConversationId}");
                        Console.WriteLine($"  Last update: {conversation.UpdateTime}");
                        
                        if (conversation.Referral?.Ad != null)
                        {
                            foreach (var ad in conversation.Referral.Ad)
                            {
                                Console.WriteLine($"  Referral Ad: {ad.AdName} (ID: {ad.AdId})");
                            }
                        }
                    }

                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting messages in a conversation
        /// </summary>
        /// <param name="businessId">Your business ID</param>
        /// <param name="conversationId">The conversation ID to get messages from</param>
        /// <returns>Task representing the async operation</returns>
        public async Task GetMessagesExample(string businessId, string conversationId)
        {
            try
            {
                Console.WriteLine("=== Get Messages Example ===");

                Console.WriteLine($"Getting messages for conversation: {conversationId}");

                var response = await _client.BusinessMessaging.GetMessagesAsync(
                    _client.AccessToken,
                    businessId,
                    conversationId);

                if (response.Data?.Messages != null)
                {
                    Console.WriteLine($"Found {response.Data.Messages.Count} messages");

                    foreach (var message in response.Data.Messages)
                    {
                        Console.WriteLine($"- Message ID: {message.MessageId}");
                        Console.WriteLine($"  Type: {message.MessageType}");
                        Console.WriteLine($"  Sender: {message.Sender}");
                        Console.WriteLine($"  Timestamp: {message.Timestamp}");

                        if (message.Text != null)
                        {
                            Console.WriteLine($"  Text: {message.Text.Body}");
                        }

                        if (message.Image != null)
                        {
                            Console.WriteLine($"  Image Media ID: {message.Image.MediaId}");
                        }
                    }

                    if (response.Data.Participants != null)
                    {
                        Console.WriteLine($"Participants ({response.Data.Participants.Count}):");
                        foreach (var participant in response.Data.Participants)
                        {
                            Console.WriteLine($"- {participant.DisplayName} ({participant.Role})");
                            Console.WriteLine($"  ID: {participant.Id}");
                            Console.WriteLine($"  Is Follower: {participant.IsFollower}");
                        }
                    }

                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of creating a welcome message auto-message
        /// </summary>
        /// <param name="businessId">Your business ID</param>
        /// <param name="welcomeText">The welcome message text</param>
        /// <returns>Task representing the async operation</returns>
        public async Task CreateWelcomeMessageExample(string businessId, string welcomeText)
        {
            try
            {
                Console.WriteLine("=== Create Welcome Message Example ===");

                var request = new AutoMessageCreateRequest
                {
                    BusinessId = businessId,
                    AutoMessageType = AutoMessageType.WelcomeMessage,
                    WelcomeMessage = new AutoMessageWelcome
                    {
                        Content = welcomeText
                    }
                };

                Console.WriteLine($"Creating welcome message: {welcomeText}");

                var response = await _client.BusinessMessaging.CreateAutoMessageAsync(
                    _client.AccessToken,
                    request);

                if (response.Data?.AutoMessage != null)
                {
                    Console.WriteLine($"Welcome message created successfully!");
                    Console.WriteLine($"Auto Message ID: {response.Data.AutoMessage.AutoMessageId}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of creating suggested questions
        /// </summary>
        /// <param name="businessId">Your business ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task CreateSuggestedQuestionsExample(string businessId)
        {
            try
            {
                Console.WriteLine("=== Create Suggested Questions Example ===");

                var questions = new[]
                {
                    new { Question = "What are your business hours?", Answer = "We're open Monday-Friday 9AM-6PM EST." },
                    new { Question = "How can I contact support?", Answer = "You can reach our support <NAME_EMAIL> or through this chat." },
                    new { Question = "Do you offer free shipping?", Answer = "Yes! We offer free shipping on orders over $50." }
                };

                foreach (var qa in questions)
                {
                    var request = new AutoMessageCreateRequest
                    {
                        BusinessId = businessId,
                        AutoMessageType = AutoMessageType.SuggestedQuestion,
                        SuggestedQuestion = new AutoMessageSuggestedQuestion
                        {
                            Question = qa.Question,
                            Answer = qa.Answer
                        }
                    };

                    Console.WriteLine($"Creating suggested question: {qa.Question}");

                    var response = await _client.BusinessMessaging.CreateAutoMessageAsync(
                        _client.AccessToken,
                        request);

                    if (response.Data?.AutoMessage != null)
                    {
                        Console.WriteLine($"Suggested question created! ID: {response.Data.AutoMessage.AutoMessageId}");
                    }
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of setting up a webhook for direct messages
        /// </summary>
        /// <param name="appId">Your app ID</param>
        /// <param name="appSecret">Your app secret</param>
        /// <param name="callbackUrl">Your webhook callback URL</param>
        /// <returns>Task representing the async operation</returns>
        public async Task SetupWebhookExample(string appId, string appSecret, string callbackUrl)
        {
            try
            {
                Console.WriteLine("=== Setup Webhook Example ===");

                var request = new BusinessMessagingWebhookRequest
                {
                    AppId = appId,
                    Secret = appSecret,
                    EventType = BusinessMessagingWebhookEventType.DirectMessage,
                    CallbackUrl = callbackUrl
                };

                Console.WriteLine($"Setting up webhook for direct messages");
                Console.WriteLine($"Callback URL: {callbackUrl}");

                var response = await _client.BusinessMessaging.CreateWebhookAsync(request);

                if (response.Data != null)
                {
                    Console.WriteLine($"Webhook created successfully!");
                    Console.WriteLine($"App ID: {response.Data.AppId}");
                    Console.WriteLine($"Event Type: {response.Data.EventType}");
                    Console.WriteLine($"Callback URL: {response.Data.CallbackUrl}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of running all Business Messaging API features
        /// </summary>
        /// <param name="businessId">Your business ID</param>
        /// <param name="conversationId">A conversation ID to test with</param>
        /// <param name="appId">Your app ID</param>
        /// <param name="appSecret">Your app secret</param>
        /// <param name="webhookUrl">Your webhook URL</param>
        /// <returns>Task representing the async operation</returns>
        public async Task RunAllExamples(string businessId, string conversationId, string appId, string appSecret, string webhookUrl)
        {
            Console.WriteLine("=== Running All Business Messaging API Examples ===\n");

            // Send text message
            await SendTextMessageExample(businessId, conversationId, "Hello! This is a test message from the Business Messaging API.");
            Console.WriteLine();

            // Send template message
            await SendTemplateMessageExample(businessId, conversationId);
            Console.WriteLine();

            // Get conversations
            await GetConversationsExample(businessId);
            Console.WriteLine();

            // Get messages
            await GetMessagesExample(businessId, conversationId);
            Console.WriteLine();

            // Create welcome message
            await CreateWelcomeMessageExample(businessId, "Welcome to our business! How can we help you today?");
            Console.WriteLine();

            // Create suggested questions
            await CreateSuggestedQuestionsExample(businessId);
            Console.WriteLine();

            // Setup webhook
            await SetupWebhookExample(appId, appSecret, webhookUrl);
            Console.WriteLine();

            Console.WriteLine("=== All examples completed! ===");
        }
    }
}
