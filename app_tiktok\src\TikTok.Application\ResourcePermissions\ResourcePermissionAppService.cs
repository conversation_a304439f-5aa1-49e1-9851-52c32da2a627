using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities.Permissions;
using TikTok.Permissions;
using TikTok.Repositories;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.ResourcePermissions
{
    /// <summary>
    /// Service implementation cho quyền tài nguyên
    /// </summary>
    [RemoteService(IsMetadataEnabled = false)]
    public class ResourcePermissionAppService :
        CrudAppService<
            ResourcePermissionEntity,
            ResourcePermissionDto,
            Guid,
            GetResourcePermissionListDto,
            CreateResourcePermissionDto,
            UpdateResourcePermissionDto>,
        IResourcePermissionAppService
    {
        private readonly IResourcePermissionRepository _resourcePermissionRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="repository">Repository</param>
        /// <param name="resourcePermissionRepository">Resource Permission Repository</param>
        public ResourcePermissionAppService(
            IRepository<ResourcePermissionEntity, Guid> repository,
            IResourcePermissionRepository resourcePermissionRepository) : base(repository)
        {
            _resourcePermissionRepository = resourcePermissionRepository;
        }

        /// <summary>
        /// Lấy danh sách quyền tài nguyên với phân trang
        /// </summary>
        public override async Task<PagedResultDto<ResourcePermissionDto>> GetListAsync(GetResourcePermissionListDto input)
        {
            var totalCount = await _resourcePermissionRepository.GetCountAsync(
                input.Filter,
                input.UserId,
                input.ResourceId,
                input.Permission,
                input.ResourceType,
                input.UserIds,
                input.ResourceIds,
                input.Permissions);

            var entities = await _resourcePermissionRepository.GetListAsync(
                input.Sorting,
                input.MaxResultCount,
                input.SkipCount,
                input.Filter,
                input.UserId,
                input.ResourceId,
                input.Permission,
                input.ResourceType,
                input.UserIds,
                input.ResourceIds,
                input.Permissions);

            var dtos = ObjectMapper.Map<List<ResourcePermissionEntity>, List<ResourcePermissionDto>>(entities.ToList());

            return new PagedResultDto<ResourcePermissionDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }

        /// <summary>
        /// Lấy quyền tài nguyên theo người dùng và tài nguyên
        /// </summary>
        public async Task<IList<ResourcePermissionDto>> GetByUserAndResourceAsync(Guid userId, string resourceId, string resourceType)
        {
            var entity = await _resourcePermissionRepository.GetByUserAndResourceAsync(userId, resourceId, resourceType);
            return ObjectMapper.Map<IList<ResourcePermissionEntity>, IList<ResourcePermissionDto>>(entity);
        }

        /// <summary>
        /// Lấy danh sách quyền tài nguyên theo người dùng
        /// </summary>
        public async Task<PagedResultDto<ResourcePermissionDto>> GetByUserIdAsync(Guid userId)
        {
            var entities = await _resourcePermissionRepository.GetByUserIdAsync(userId);
            var dtos = ObjectMapper.Map<IList<ResourcePermissionEntity>, IList<ResourcePermissionDto>>(entities);

            return new PagedResultDto<ResourcePermissionDto>
            {
                TotalCount = entities.Count,
                Items = dtos.ToList()
            };
        }

        /// <summary>
        /// Lấy danh sách quyền tài nguyên theo tài nguyên
        /// </summary>
        public async Task<PagedResultDto<ResourcePermissionDto>> GetByResourceAsync(string resourceId, string resourceType)
        {
            var entities = await _resourcePermissionRepository.GetByResourceAsync(resourceId, resourceType);
            var dtos = ObjectMapper.Map<IList<ResourcePermissionEntity>, IList<ResourcePermissionDto>>(entities);

            return new PagedResultDto<ResourcePermissionDto>
            {
                TotalCount = entities.Count,
                Items = dtos.ToList()
            };
        }

        /// <summary>
        /// Kiểm tra quyền của người dùng đối với tài nguyên
        /// </summary>
        public async Task<bool> HasPermissionAsync(Guid userId, string resourceId, string resourceType, string permission)
        {
            return await _resourcePermissionRepository.HasPermissionAsync(userId, resourceId, resourceType, permission);
        }

        /// <summary>
        /// Gán quyền cho người dùng đối với tài nguyên
        /// </summary>
        public async Task AssignPermissionsAsync(Guid userId, string resourceId, string resourceType, List<string> permissions)
        {
            // Xóa quyền hiện tại
            await _resourcePermissionRepository.DeleteByUserAndResourceAsync(userId, resourceId, resourceType);

            // Tạo quyền mới
            foreach (var permission in permissions)
            {
                var entity = new ResourcePermissionEntity
                {
                    UserId = userId,
                    ResourceId = resourceId,
                    ResourceType = resourceType,
                    Permission = permission
                };

                await Repository.InsertAsync(entity);
            }
        }

        public async Task RevokePermissionsAsync(Guid userId, string resourceId, string resourceType)
        {
            await _resourcePermissionRepository.DeleteByUserAndResourceAsync(userId, resourceId, resourceType);
        }

        public async Task<bool> AssignPermissionsAsync(List<AssignPermissionForResourceDto> input)
        {

            var listInsert = new List<ResourcePermissionEntity>();

            // Duyệt qua từng người dùng và tài nguyên
            foreach (var item in input)
            {
                // Xóa quyền hiện tại
                await _resourcePermissionRepository.DeleteByUserAndResourceAsync(item.UserId, item.ResourceId, item.ResourceType);

                // Tạo quyền mới
                foreach (var permission in item.Permissions)
                {
                    var entity = new ResourcePermissionEntity
                    {
                        UserId = item.UserId,
                        ResourceId = item.ResourceId,
                        ResourceType = item.ResourceType,
                        Permission = permission
                    };
                    listInsert.Add(entity);
                }
            }

            if (listInsert.Any())
            {
                await Repository.InsertManyAsync(listInsert);
            }

            return true;
        }

        public async Task<bool> RevokePermissionsAsync(List<RemovePermissionForResourceDto> input)
        {
            foreach (var item in input)
            {
                await _resourcePermissionRepository.DeleteByUserAndResourceAsync(item.UserId, item.ResourceId, item.ResourceType);
            }

            return true;
        }

        /// <summary>
        /// Tạo quyền tài nguyên mới
        /// </summary>
        public override async Task<ResourcePermissionDto> CreateAsync(CreateResourcePermissionDto input)
        {
            // Kiểm tra xem quyền đã tồn tại chưa
            var existingPermission = await _resourcePermissionRepository.GetByUserAndResourceAsync(
                input.UserId, input.ResourceId, input.ResourceType);

            if (existingPermission != null && existingPermission.Any(x => input.Permission.Equals(x.Permission)))
            {
                throw new BusinessException("Quyền tài nguyên đã tồn tại cho người dùng và tài nguyên này.");
            }

            return await base.CreateAsync(input);
        }

        public async Task<IList<ResourcePermissionDto>> GetAllAsync(GetResourcePermissionListDto input)
        {
            var response = await _resourcePermissionRepository.GetAllAsync(
                input.Filter,
                input.UserId,
                input.ResourceId,
                input.Permission,
                input.ResourceType,
                input.UserIds,
                input.ResourceIds,
                input.Permissions);

            return ObjectMapper.Map<IList<ResourcePermissionEntity>, IList<ResourcePermissionDto>>(response);
        }
    }
}