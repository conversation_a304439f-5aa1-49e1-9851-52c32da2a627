using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.BalanceAdAccounts;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// Interface cho LatestBalanceAdAccount App Service
    /// Chỉ cung cấp các thao tác đọc dữ liệu mới nhất
    /// </summary>
    public interface ILatestBalanceAdAccountAppService :
        IReadOnlyAppService<
            LatestBalanceAdAccountDto,
            Guid,
            GetLatestBalanceAdAccountListDto>
    {
        /// <summary>
        /// Lấy tài khoản nhà quảng cáo mới nhất theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của nhà quảng cáo</param>
        /// <returns>Tài khoản nhà quảng cáo mới nhất</returns>
        Task<LatestBalanceAdAccountDto> GetByAdvertiserIdAsync(string advertiserId);

        /// <summary>
        /// Lấy danh sách tài khoản nhà quảng cáo mới nhất theo Business Center ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo mới nhất</returns>
        Task<PagedResultDto<LatestBalanceAdAccountDto>> GetByBcIdAsync(string bcId);

        /// <summary>
        /// Lấy danh sách tài khoản nhà quảng cáo mới nhất theo trạng thái
        /// </summary>
        /// <param name="status">Trạng thái tài khoản</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo mới nhất</returns>
        Task<PagedResultDto<LatestBalanceAdAccountDto>> GetByStatusAsync(AdvertiserAccountStatus status);

        /// <summary>
        /// Lấy danh sách tài khoản nhà quảng cáo mới nhất theo loại tài khoản
        /// </summary>
        /// <param name="accountType">Loại tài khoản</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo mới nhất</returns>
        Task<PagedResultDto<LatestBalanceAdAccountDto>> GetByAccountTypeAsync(AdAccountType accountType);

        /// <summary>
        /// Lấy danh sách tài khoản nhà quảng cáo mới nhất theo chế độ ngân sách
        /// </summary>
        /// <param name="budgetMode">Chế độ ngân sách</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo mới nhất</returns>
        Task<PagedResultDto<LatestBalanceAdAccountDto>> GetByBudgetModeAsync(BudgetMode budgetMode);

        /// <summary>
        /// Lấy danh sách tài khoản nhà quảng cáo mới nhất theo khoảng thời gian đồng bộ
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo mới nhất</returns>
        Task<PagedResultDto<LatestBalanceAdAccountDto>> GetBySyncDateRangeAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Lấy thống kê tổng quan về tài khoản nhà quảng cáo mới nhất
        /// </summary>
        /// <returns>Thống kê tổng quan</returns>
        Task<LatestBalanceAdAccountStatisticsDto> GetStatisticsAsync();

        /// <summary>
        /// Lấy danh sách tài khoản nhà quảng cáo mới nhất theo nhiều Advertiser ID
        /// </summary>
        /// <param name="advertiserIds">Danh sách Advertiser ID</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo mới nhất</returns>
        Task<List<LatestBalanceAdAccountDto>> GetByManyAdvertiserIdsAsync(IEnumerable<string> advertiserIds);
    }
}
