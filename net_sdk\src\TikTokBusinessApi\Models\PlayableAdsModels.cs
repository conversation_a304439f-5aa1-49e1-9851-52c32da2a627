/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Upload Playable Models

    /// <summary>
    /// Request model for uploading a playable creative
    /// </summary>
    public class PlayableUploadRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The way you upload your playable ads. Default value: UPLOAD_BY_FILE
        /// </summary>
        [JsonPropertyName("upload_type")]
        public string? UploadType { get; set; } = "UPLOAD_BY_FILE";

        /// <summary>
        /// The file_id of the file that you want to upload. Required when upload_type is UPLOAD_BY_FILE_ID
        /// </summary>
        [JsonPropertyName("file_id")]
        public string? FileId { get; set; }
    }

    /// <summary>
    /// Response model for uploading a playable creative
    /// </summary>
    public class PlayableUploadResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public PlayableUploadData? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string? RequestId { get; set; }
    }

    /// <summary>
    /// Data model for playable upload response
    /// </summary>
    public class PlayableUploadData
    {
        /// <summary>
        /// Playable creative ID
        /// </summary>
        [JsonPropertyName("playable_id")]
        public string? PlayableId { get; set; }
    }

    #endregion

    #region Validate Playable Models

    /// <summary>
    /// Request model for validating a playable creative
    /// </summary>
    public class PlayableValidateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Playable creative ID
        /// </summary>
        [Required]
        [JsonPropertyName("playable_id")]
        public string PlayableId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for validating a playable creative
    /// </summary>
    public class PlayableValidateResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public PlayableValidateData? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string? RequestId { get; set; }
    }

    /// <summary>
    /// Data model for playable validate response
    /// </summary>
    public class PlayableValidateData
    {
        /// <summary>
        /// Playable creative ID
        /// </summary>
        [JsonPropertyName("playable_id")]
        public string? PlayableId { get; set; }

        /// <summary>
        /// Playable creative URL
        /// </summary>
        [JsonPropertyName("playable_url")]
        public string? PlayableUrl { get; set; }

        /// <summary>
        /// Playable creative direction. Enum values: BOTH, LANDSCAPE, PORTRAIT
        /// </summary>
        [JsonPropertyName("playable_orientation")]
        public string? PlayableOrientation { get; set; }

        /// <summary>
        /// Playable creative status. Enum values: AUDIT_FAIL, AUDIT_SUCCESS, VALIDATE_FAIL, VALIDATE_SUCCESS, VALIDATING
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Verify the cause of failure
        /// </summary>
        [JsonPropertyName("fail_message")]
        public string? FailMessage { get; set; }
    }

    #endregion

    #region Save Playable Models

    /// <summary>
    /// Request model for saving a playable creative
    /// </summary>
    public class PlayableSaveRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Playable creative ID
        /// </summary>
        [Required]
        [JsonPropertyName("playable_id")]
        public string PlayableId { get; set; } = string.Empty;

        /// <summary>
        /// Playable creative name
        /// </summary>
        [Required]
        [JsonPropertyName("playable_name")]
        public string PlayableName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for saving a playable creative
    /// </summary>
    public class PlayableSaveResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public PlayableSaveData? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string? RequestId { get; set; }
    }

    /// <summary>
    /// Data model for playable save response
    /// </summary>
    public class PlayableSaveData
    {
        /// <summary>
        /// Playable creative ID
        /// </summary>
        [JsonPropertyName("playable_id")]
        public string? PlayableId { get; set; }

        /// <summary>
        /// Playable creative name
        /// </summary>
        [JsonPropertyName("playable_name")]
        public string? PlayableName { get; set; }

        /// <summary>
        /// Playable creative direction. Enum values: BOTH, LANDSCAPE, PORTRAIT
        /// </summary>
        [JsonPropertyName("playable_orientation")]
        public string? PlayableOrientation { get; set; }

        /// <summary>
        /// Playable creative URL
        /// </summary>
        [JsonPropertyName("playable_url")]
        public string? PlayableUrl { get; set; }

        /// <summary>
        /// Playable creative state. Enum values: AUDIT_FAIL, AUDIT_SUCCESS, VALIDATE_FAIL, VALIDATE_SUCCESS, VALIDATING
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }
    }

    #endregion

    #region Get Playables Models

    /// <summary>
    /// Request model for getting playable creatives
    /// </summary>
    public class PlayableGetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Playable creative ID, if passed only return ID matching Playable clip
        /// </summary>
        [JsonPropertyName("playable_id")]
        public string? PlayableId { get; set; }

        /// <summary>
        /// Playable creative name (filter the Playable creative material with the name 'playable_name')
        /// </summary>
        [JsonPropertyName("playable_name")]
        public string? PlayableName { get; set; }

        /// <summary>
        /// Current number of pages. Default value: 1. Range: ≥ 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; } = 1;

        /// <summary>
        /// Pagination size. Default value: 10. Range: 1-100
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; } = 10;

        /// <summary>
        /// Playable creative URL
        /// </summary>
        [JsonPropertyName("playable_url")]
        public string? PlayableUrl { get; set; }

        /// <summary>
        /// Playable creative state. Enum values: AUDIT_FAIL, AUDIT_SUCCESS, VALIDATE_FAIL, VALIDATE_SUCCESS, VALIDATING
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Playable creative operation state. Enum values: UPLOADED, SAVED, DELETED. Default value: SAVED
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string? OperationStatus { get; set; } = "SAVED";
    }

    /// <summary>
    /// Response model for getting playable creatives
    /// </summary>
    public class PlayableGetResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public PlayableGetData? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string? RequestId { get; set; }
    }

    /// <summary>
    /// Data model for playable get response
    /// </summary>
    public class PlayableGetData
    {
        /// <summary>
        /// Playable creative list
        /// </summary>
        [JsonPropertyName("list")]
        public List<PlayableInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Playable creative information
    /// </summary>
    public class PlayableInfo
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Playable creative ID
        /// </summary>
        [JsonPropertyName("playable_id")]
        public string? PlayableId { get; set; }

        /// <summary>
        /// Playable creative name
        /// </summary>
        [JsonPropertyName("playable_name")]
        public string? PlayableName { get; set; }

        /// <summary>
        /// Playable creative orientation direction. Enum values: BOTH, LANDSCAPE, PORTRAIT
        /// </summary>
        [JsonPropertyName("playable_orientation")]
        public string? PlayableOrientation { get; set; }

        /// <summary>
        /// Playable creative state. Enum values: AUDIT_FAIL, AUDIT_SUCCESS, VALIDATE_FAIL, VALIDATE_SUCCESS, VALIDATING
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Playable creative URL
        /// </summary>
        [JsonPropertyName("playable_url")]
        public string? PlayableUrl { get; set; }

        /// <summary>
        /// Playable Creative operation state. Enum values: UPLOADED, SAVED, DELETED
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string? OperationStatus { get; set; }
    }

    #endregion

    #region Delete Playable Models

    /// <summary>
    /// Request model for deleting a playable creative
    /// </summary>
    public class PlayableDeleteRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the playable ad
        /// </summary>
        [Required]
        [JsonPropertyName("playable_id")]
        public string PlayableId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for deleting a playable creative
    /// </summary>
    public class PlayableDeleteResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public PlayableDeleteData? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string? RequestId { get; set; }
    }

    /// <summary>
    /// Data model for playable delete response
    /// </summary>
    public class PlayableDeleteData
    {
        /// <summary>
        /// Playable creative ID
        /// </summary>
        [JsonPropertyName("playable_id")]
        public string? PlayableId { get; set; }
    }

    #endregion
}
