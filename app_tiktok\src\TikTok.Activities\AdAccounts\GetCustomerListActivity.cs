using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using TikTok.Customers;
using TikTok.Entities;
using TikTok.Repositories;
using Volo.Abp.ObjectMapping;

namespace TikTok.Activities.AdAccounts
{
    [Activity(Category = "Khách hàng", DisplayName = "Lấy danh sách khách hàng", Outcomes = new[] { OutcomeNames.Done })]
    public class GetCustomerListActivity : UnitOfWorkActivity
    {
        private readonly ICustomerQueryService _customerQueryService;

        public GetCustomerListActivity(ICustomerQueryService customerQueryService)
        {
            _customerQueryService = customerQueryService;
        }

        [ActivityInput(Hint = "AdvertiserIds", SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public List<string> AdvertiserIds { get; set; }

        [ActivityInput(Hint = "Business Center ID", SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public string BcId { get; set; }

        [ActivityInput(Hint = "Có bao gồm thông tin AdAccounts không", DefaultValue = true, SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public bool IncludeAdAccounts { get; set; } = true;

        [ActivityOutput(Hint = "Danh sách khách hàng")]
        public List<CustomerAdvertiserDto> Output { get; set; } = new List<CustomerAdvertiserDto>();

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            // Sử dụng service để lấy dữ liệu khách hàng
            if (!string.IsNullOrEmpty(BcId))
            {
                if (AdvertiserIds != null && AdvertiserIds.Any())
                {
                    // Lấy theo cả BcId và AdvertiserIds
                    Output = await _customerQueryService.GetCustomersByAdvertiserIdsAsync(AdvertiserIds, IncludeAdAccounts);
                }
                else
                {
                    // Lấy theo BcId
                    Output = await _customerQueryService.GetCustomersByBcIdAsync(BcId, IncludeAdAccounts);
                }
            }
            else
            {
                // Lấy theo AdvertiserIds hoặc tất cả
                Output = await _customerQueryService.GetCustomersByAdvertiserIdsAsync(AdvertiserIds, IncludeAdAccounts);
            }

            return Done();
        }
    }
}