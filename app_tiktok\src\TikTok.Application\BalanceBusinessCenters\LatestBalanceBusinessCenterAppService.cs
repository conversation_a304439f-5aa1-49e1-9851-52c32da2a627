using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTok.BalanceBusinessCenters;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.BalanceBusinessCenters
{
    /// <summary>
    /// Application Service cho LatestBalanceBusinessCenter
    /// Chỉ cung cấp các thao tác đọc dữ liệu mới nhất
    /// </summary>
    public class LatestBalanceBusinessCenterAppService :
        ReadOnlyAppService<
            RawLatestBalanceBusinessCenterEntity,
            LatestBalanceBusinessCenterDto,
            Guid,
            GetLatestBalanceBusinessCenterListDto>,
        ILatestBalanceBusinessCenterAppService
    {
        private readonly ILatestBalanceBusinessCenterRepository _latestBalanceBusinessCenterRepository;

        public LatestBalanceBusinessCenterAppService(ILatestBalanceBusinessCenterRepository latestBalanceBusinessCenterRepository)
            : base(latestBalanceBusinessCenterRepository)
        {
            _latestBalanceBusinessCenterRepository = latestBalanceBusinessCenterRepository;

            // Cấu hình quyền truy cập
            GetPolicyName = TikTokPermissions.BalanceBusinessCenters.Default;
            GetListPolicyName = TikTokPermissions.BalanceBusinessCenters.Default;
        }

        public override async Task<PagedResultDto<LatestBalanceBusinessCenterDto>> GetListAsync(GetLatestBalanceBusinessCenterListDto input)
        {
            var entities = await _latestBalanceBusinessCenterRepository.GetListAsync(
                sorting: input.Sorting,
                maxResultCount: input.MaxResultCount,
                skipCount: input.SkipCount,
                filter: input.Filter,
                bcId: input.BcId,
                bcName: input.BcName,
                currency: input.Currency,
                minAccountBalance: input.MinAccountBalance,
                maxAccountBalance: input.MaxAccountBalance,
                minCashBalance: input.MinCashBalance,
                maxCashBalance: input.MaxCashBalance,
                dateFrom: input.DateFrom,
                dateTo: input.DateTo,
                timezone: input.Timezone);

            var totalCount = await _latestBalanceBusinessCenterRepository.GetCountAsync(
                filter: input.Filter,
                bcId: input.BcId,
                bcName: input.BcName,
                currency: input.Currency,
                minAccountBalance: input.MinAccountBalance,
                maxAccountBalance: input.MaxAccountBalance,
                minCashBalance: input.MinCashBalance,
                maxCashBalance: input.MaxCashBalance,
                dateFrom: input.DateFrom,
                dateTo: input.DateTo,
                timezone: input.Timezone);

            var dtos = ObjectMapper.Map<RawLatestBalanceBusinessCenterEntity[], LatestBalanceBusinessCenterDto[]>(entities.ToArray());

            return new PagedResultDto<LatestBalanceBusinessCenterDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }

        public async Task<LatestBalanceBusinessCenterDto> GetByBcIdAsync(string bcId)
        {
            var entity = await _latestBalanceBusinessCenterRepository.GetByBcIdAsync(bcId);
            if (entity == null)
            {
                throw new UserFriendlyException("Không tìm thấy Business Center với BcId: " + bcId);
            }

            return ObjectMapper.Map<RawLatestBalanceBusinessCenterEntity, LatestBalanceBusinessCenterDto>(entity);
        }

        public async Task<PagedResultDto<LatestBalanceBusinessCenterDto>> GetByCurrencyAsync(string currency)
        {
            var entities = await _latestBalanceBusinessCenterRepository.GetByCurrencyAsync(currency);
            var dtos = ObjectMapper.Map<RawLatestBalanceBusinessCenterEntity[], LatestBalanceBusinessCenterDto[]>(entities.ToArray());

            return new PagedResultDto<LatestBalanceBusinessCenterDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<PagedResultDto<LatestBalanceBusinessCenterDto>> GetBySyncDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            var entities = await _latestBalanceBusinessCenterRepository.GetListAsync(
                dateFrom: fromDate,
                dateTo: toDate);
            var dtos = ObjectMapper.Map<RawLatestBalanceBusinessCenterEntity[], LatestBalanceBusinessCenterDto[]>(entities.ToArray());

            return new PagedResultDto<LatestBalanceBusinessCenterDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<LatestBalanceBusinessCenterStatisticsDto> GetStatisticsAsync()
        {
            var allEntities = await _latestBalanceBusinessCenterRepository.GetListAsync();
            var statistics = new LatestBalanceBusinessCenterStatisticsDto();

            if (!allEntities.Any())
            {
                return statistics;
            }

            // Tính toán thống kê tổng quan
            statistics.TotalBusinessCenters = allEntities.Count;
            statistics.TotalAccountBalance = allEntities.Sum(x => x.AccountBalance ?? 0);
            statistics.TotalValidAccountBalance = allEntities.Sum(x => x.ValidAccountBalance ?? 0);
            statistics.TotalFrozenBalance = allEntities.Sum(x => x.FrozenBalance ?? 0);
            statistics.TotalTax = allEntities.Sum(x => x.Tax ?? 0);
            statistics.TotalCashBalance = allEntities.Sum(x => x.CashBalance ?? 0);
            statistics.TotalValidCashBalance = allEntities.Sum(x => x.ValidCashBalance ?? 0);
            statistics.TotalGrantBalance = allEntities.Sum(x => x.GrantBalance ?? 0);
            statistics.TotalValidGrantBalance = allEntities.Sum(x => x.ValidGrantBalance ?? 0);
            statistics.LastSyncTime = allEntities.Max(x => x.Date);
            statistics.BusinessCentersWithLowBalance = allEntities.Count(x => (x.AccountBalance ?? 0) < 1000);
            statistics.BusinessCentersWithHighBalance = allEntities.Count(x => (x.AccountBalance ?? 0) > 10000);

            // Thống kê theo tiền tệ
            statistics.BusinessCentersByCurrency = allEntities
                .GroupBy(x => x.Currency)
                .ToDictionary(g => g.Key, g => g.Count());

            // Thống kê theo múi giờ
            statistics.BusinessCentersByTimezone = allEntities
                .Where(x => !string.IsNullOrEmpty(x.Timezone))
                .GroupBy(x => x.Timezone)
                .ToDictionary(g => g.Key, g => g.Count());

            return statistics;
        }

        public async Task<List<LatestBalanceBusinessCenterDto>> GetByManyBcIdsAsync(IEnumerable<string> bcIds)
        {
            var entities = new List<RawLatestBalanceBusinessCenterEntity>();
            
            foreach (var bcId in bcIds)
            {
                var entity = await _latestBalanceBusinessCenterRepository.GetByBcIdAsync(bcId);
                if (entity != null)
                {
                    entities.Add(entity);
                }
            }

            return ObjectMapper.Map<List<RawLatestBalanceBusinessCenterEntity>, List<LatestBalanceBusinessCenterDto>>(entities);
        }
    }
}
