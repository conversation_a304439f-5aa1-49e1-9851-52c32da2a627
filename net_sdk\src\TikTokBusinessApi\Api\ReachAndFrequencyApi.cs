/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Reach and Frequency operations
    /// </summary>
    public class ReachAndFrequencyApi : IReachAndFrequencyApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<ReachAndFrequencyApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the ReachAndFrequencyApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public ReachAndFrequencyApi(IApiClient apiClient, ILogger<ReachAndFrequencyApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get inventory estimates for Reach & Frequency ads
        /// </summary>
        /// <param name="request">Inventory estimate request</param>
        /// <returns>Inventory estimate response</returns>
        public async Task<RFInventoryEstimateResponse> GetInventoryEstimateAsync(RFInventoryEstimateRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting inventory estimate for advertiser {AdvertiserId}", request.AdvertiserId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["audience_info"] = JsonSerializer.Serialize(request.AudienceInfo),
                ["schedule_start_time"] = request.ScheduleStartTime,
                ["schedule_end_time"] = request.ScheduleEndTime,
                ["frequency"] = request.Frequency.ToString(),
                ["frequency_schedule"] = request.FrequencySchedule.ToString(),
                ["objective_type"] = request.ObjectiveType,
                ["rf_purchased_type"] = request.RfPurchasedType
            };

            if (!string.IsNullOrEmpty(request.CpvVideoDuration))
                queryParams["cpv_video_duration"] = request.CpvVideoDuration;

            if (!string.IsNullOrEmpty(request.FeedType))
                queryParams["feed_type"] = request.FeedType;

            if (request.Budget.HasValue)
                queryParams["budget"] = request.Budget.Value.ToString();

            if (request.PurchasedImpression.HasValue)
                queryParams["purchased_impression"] = request.PurchasedImpression.Value.ToString();

            if (request.PurchasedReach.HasValue)
                queryParams["purchased_reach"] = request.PurchasedReach.Value.ToString();

            if (!string.IsNullOrEmpty(request.RfCampaignType))
                queryParams["rf_campaign_type"] = request.RfCampaignType;

            var path = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ReachAndFrequencyEndpoints.GetInventoryEstimate}";

            return await _apiClient.CallApiAsync<RFInventoryEstimateResponse>(
                path: path,
                method: HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: new[] { "tiktok" }
            );
        }

        /// <summary>
        /// Create a Reach & Frequency ad group
        /// </summary>
        /// <param name="request">RF ad group creation request</param>
        /// <returns>RF ad group creation response</returns>
        // public async Task<RFAdGroupCreateResponse> CreateRFAdGroupAsync(RFAdGroupCreateRequest request)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogDebug("Creating RF ad group {AdGroupName} for advertiser {AdvertiserId}", 
        //         request.AdGroupName, request.AdvertiserId);

        //     var path = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ReachAndFrequencyEndpoints.CreateRFAdGroup}";

        //     return await _apiClient.CallApiAsync<RFAdGroupCreateResponse>(
        //         path: path,
        //         method: HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: new[] { "tiktok" }
        //     );
        // }

        /// <summary>
        /// Update Reach & Frequency ad groups
        /// </summary>
        /// <param name="request">RF ad group update request</param>
        /// <returns>RF ad group creation response</returns>
        // public async Task<RFAdGroupCreateResponse> UpdateRFAdGroupAsync(RFAdGroupUpdateRequest request)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogDebug("Updating RF ad group {AdGroupId} for advertiser {AdvertiserId}", 
        //         request.AdGroupId, request.AdvertiserId);

        //     var path = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ReachAndFrequencyEndpoints.UpdateRFAdGroup}";

        //     return await _apiClient.CallApiAsync<RFAdGroupCreateResponse>(
        //         path: path,
        //         method: HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: new[] { "tiktok" }
        //     );
        // }

        /// <summary>
        /// Cancel the R&F ad order to suspend its delivery
        /// </summary>
        /// <param name="request">RF order cancel request</param>
        /// <returns>RF order cancel response</returns>
        // public async Task<RFOrderCancelResponse> CancelRFOrderAsync(RFOrderCancelRequest request)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogDebug("Canceling RF order for advertiser {AdvertiserId} with {Count} ad groups", 
        //         request.AdvertiserId, request.AdGroupIds.Count);

        //     var path = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ReachAndFrequencyEndpoints.CancelRFOrder}";

        //     return await _apiClient.CallApiAsync<RFOrderCancelResponse>(
        //         path: path,
        //         method: HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: new[] { "tiktok" }
        //     );
        // }

        /// <summary>
        /// Get estimated info of R&F ad groups
        /// </summary>
        /// <param name="request">RF estimated info request</param>
        /// <returns>RF estimated info response</returns>
        public async Task<RFEstimatedInfoResponse> GetRFEstimatedInfoAsync(RFEstimatedInfoRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting RF estimated info for advertiser {AdvertiserId} with {Count} ad groups", 
                request.AdvertiserId, request.AdGroupIds.Count);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["adgroup_ids"] = JsonSerializer.Serialize(request.AdGroupIds)
            };

            var path = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ReachAndFrequencyEndpoints.GetRFEstimatedInfo}";

            return await _apiClient.CallApiAsync<RFEstimatedInfoResponse>(
                path: path,
                method: HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: new[] { "tiktok" }
            );
        }

        /// <summary>
        /// Query contracts
        /// </summary>
        /// <param name="request">RF contract query request</param>
        /// <returns>RF contract query response</returns>
        public async Task<RFContractQueryResponse> QueryContractsAsync(RFContractQueryRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Querying contracts for advertiser {AdvertiserId} on date {IncludedDate}", 
                request.AdvertiserId, request.IncludedDate);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["included_date"] = request.IncludedDate
            };

            if (!string.IsNullOrEmpty(request.RfCampaignType))
                queryParams["rf_campaign_type"] = request.RfCampaignType;

            var path = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ReachAndFrequencyEndpoints.QueryContracts}";

            return await _apiClient.CallApiAsync<RFContractQueryResponse>(
                path: path,
                method: HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: new[] { "tiktok" }
            );
        }

        /// <summary>
        /// Get R&F time zones according to location
        /// </summary>
        /// <param name="request">RF time zone request</param>
        /// <returns>RF time zone response</returns>
        public async Task<RFTimeZoneResponse> GetRFTimeZonesAsync(RFTimeZoneRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting RF time zones for advertiser {AdvertiserId} with {Count} region codes", 
                request.AdvertiserId, request.RegionCodes.Count);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["region_codes"] = JsonSerializer.Serialize(request.RegionCodes)
            };

            var path = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ReachAndFrequencyEndpoints.GetRFTimeZones}";

            return await _apiClient.CallApiAsync<RFTimeZoneResponse>(
                path: path,
                method: HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: new[] { "tiktok" }
            );
        }
    }
}
