# CurrencyExchangeRateEntity

## Thông tin bảng dữ liệu

-   **Table**: `CurrencyExchangeRates`
-   **TablePrefix**: `App`
-   **TableName**: `AppCurrencyExchangeRates`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho bảng tỷ giá quy đổi tiền tệ trong hệ thống. Mỗi bản ghi là thông tin quy đổi từ một đơn vị tiền tệ cụ thể sang đơn vị tiền tệ gốc (base currency). <PERSON><PERSON><PERSON><PERSON> sử dụng để chuẩn hóa dữ liệu tài chính từ nhiều nguồn có đơn vị tiền tệ khác nhau về một đơn vị chung.

## Bảng dữ liệu: CurrencyExchangeRates

| Tên Field | Kiểu dữ liệu  | <PERSON><PERSON><PERSON> bu<PERSON> | <PERSON><PERSON> dà<PERSON> | <PERSON><PERSON> tả                                                       |
| --------- | ------------- | -------- | ------ | ----------------------------------------------------------- |
| Id        | Guid          | ✅       | -      | Khóa chính của bảng                                         |
| Currency  | string        | ✅       | 10     | Mã tiền tệ ISO 4217 (ví dụ: USD, VND, EUR)                  |
| Rate      | decimal(18,2) | ✅       | -      | Tỷ giá: **1 đơn vị base = Rate đơn vị này**                 |
| IsBase    | bool          | ✅       | -      | Đánh dấu đây có phải là loại tiền tệ gốc (duy nhất có TRUE) |
| Symbol    | string        | ✅       | 5      | Ký hiệu tiền tệ để hiển thị (ví dụ: $, ₫, €)                |

**Lưu ý**: Entity kế thừa từ `AuditedEntity<Guid>` nên có sẵn các trường audit: `CreationTime`, `CreatorId`, `LastModificationTime`, `LastModifierId`.

## Cách sử dụng

-   **Tất cả tỷ giá đều quy đổi từ Base Currency (ví dụ USD) sang đơn vị khác**
-   Nếu muốn quy đổi từ bất kỳ loại A → B, thực hiện:
    1. A → Base (chia cho `Rate` của A)
    2. Base → B (nhân với `Rate` của B)

### Ví dụ:

| Currency | Rate     | IsBase | Ý nghĩa            |
| -------- | -------- | ------ | ------------------ |
| USD      | 1.00     | ✅     | Là đơn vị gốc      |
| VND      | 26209.00 | ❌     | 1 USD = 26,209 VND |
| EUR      | 0.87     | ❌     | 1 USD = 0.87 EUR   |

→ Để quy đổi 100 EUR → VND:  
→ `100 / 0.87 * 26209 = 3,012,528.74 VND`

## Mục đích sử dụng

-   Chuẩn hóa đơn vị tiền tệ cho toàn bộ hệ thống tài chính
-   Cho phép người dùng chọn đơn vị hiển thị
-   Hỗ trợ báo cáo Power BI và các hệ thống tính toán khác

## Mối quan hệ

-   Sử dụng bởi các entity có chứa giá trị tài chính như:
    -   `RawBusinessCenterEntity` (Currency)
    -   `RawAdAccountEntity`
    -   `RawTransactionEntity`, v.v.

## Lưu ý

-   Chỉ **duy nhất một dòng có `IsBase = TRUE`**
-   `Rate` luôn tính theo: **1 Base = x Currency**
-   Không nên cập nhật hàng ngày bằng tay, nên cập nhật từ API bên ngoài (VD: openexchangerates.org)
-   Bảng này có thể mở rộng thêm `Source`, `IsManual`, `IsActive` nếu cần
