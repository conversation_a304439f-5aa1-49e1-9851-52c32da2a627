/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Ad Group operations
    /// </summary>
    public class AdGroupApi : IAdGroupApi
    {
        private readonly IApiClient _apiClient;

        /// <summary>
        /// Initializes a new instance of the AdGroupApi class
        /// </summary>
        /// <param name="apiClient">API client for making requests</param>
        public AdGroupApi(IApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        }

        /// <inheritdoc />
        public async Task<AdGroupGetResponse> GetAdGroupsAsync(
            string advertiserId,
            List<string>? fields = null,
            AdGroupFiltering? filtering = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            if (page < 1)
                throw new ArgumentException("Page must be greater than 0", nameof(page));

            if (pageSize < 1 || pageSize > 1000)
                throw new ArgumentException("Page size must be between 1 and 1000", nameof(pageSize));

            var request = new AdGroupGetRequest
            {
                AdvertiserId = advertiserId,
                Fields = fields,
                Filtering = filtering,
                Page = page,
                PageSize = pageSize
            };

            return await _apiClient.CallApiAsync<AdGroupGetResponse>(
                $"{ApiEndpoints.AdGroup.Get}",
                HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<AdGroupQuotaResponse> GetAdGroupQuotaAsync(
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            var request = new AdGroupQuotaRequest
            {
                AdvertiserId = advertiserId
            };

            return await _apiClient.CallApiAsync<AdGroupQuotaResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdGroup.GetQuota}",
                HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <inheritdoc />
        // public async Task<AudienceSizeEstimateResponse> EstimateAudienceSizeAsync(
        //     AudienceSizeEstimateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.CampaignId))
        //         throw new ArgumentException("Campaign ID cannot be null or empty", nameof(request));

        //     if (request.Targeting == null)
        //         throw new ArgumentException("Targeting cannot be null", nameof(request));

        //     return await _apiClient.CallApiAsync<AudienceSizeEstimateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdGroup.EstimateAudienceSize}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <inheritdoc />
        // public async Task<AdGroupCreateResponse> CreateAdGroupsAsync(
        //     AdGroupCreateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.Adgroups == null || request.Adgroups.Count == 0)
        //         throw new ArgumentException("At least one ad group must be provided", nameof(request));

        //     return await _apiClient.CallApiAsync<AdGroupCreateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdGroup.Create}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <inheritdoc />
        // public async Task<AdGroupUpdateResponse> UpdateAdGroupsAsync(
        //     AdGroupUpdateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.Adgroups == null || request.Adgroups.Count == 0)
        //         throw new ArgumentException("At least one ad group must be provided", nameof(request));

        //     return await _apiClient.CallApiAsync<AdGroupUpdateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdGroup.Update}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <inheritdoc />
        // public async Task<AdGroupStatusUpdateResponse> UpdateAdGroupStatusAsync(
        //     AdGroupStatusUpdateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.AdgroupIds == null || request.AdgroupIds.Count == 0)
        //         throw new ArgumentException("At least one ad group ID must be provided", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.Operation))
        //         throw new ArgumentException("Operation cannot be null or empty", nameof(request));

        //     return await _apiClient.CallApiAsync<AdGroupStatusUpdateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdGroup.UpdateStatus}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <inheritdoc />
        // public async Task<AdGroupBudgetUpdateResponse> UpdateAdGroupBudgetAsync(
        //     AdGroupBudgetUpdateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.Adgroups == null || request.Adgroups.Count == 0)
        //         throw new ArgumentException("At least one ad group budget update must be provided", nameof(request));

        //     return await _apiClient.CallApiAsync<AdGroupBudgetUpdateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdGroup.UpdateBudget}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }
    }
}
