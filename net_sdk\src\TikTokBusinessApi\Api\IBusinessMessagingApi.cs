/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.IO;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Business Messaging operations
    /// </summary>
    public interface IBusinessMessagingApi
    {
        /// <summary>
        /// Send a message to a conversation
        /// </summary>
        /// <param name="request">Message send request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing message ID</returns>
        //Task<MessageSendResponse> SendMessageAsync(
        //    MessageSendRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a list of conversations
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok Business Account</param>
        /// <param name="conversationType">Conversation type (STRANGER or SINGLE)</param>
        /// <param name="limit">The maximum number of conversations to return (1-100, default: 100)</param>
        /// <param name="cursor">Cursor for pagination (default: 0)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of conversations</returns>
        Task<ConversationListResponse> GetConversationsAsync(
            string businessId,
            string conversationType,
            int limit = 100,
            int cursor = 0,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a list of messages in a conversation
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok Business Account</param>
        /// <param name="conversationId">Conversation ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of messages and participants</returns>
        Task<MessageContentListResponse> GetMessagesAsync(
            string businessId,
            string conversationId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Upload an image for use in messages
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok Business Account</param>
        /// <param name="fileStream">Image file stream</param>
        /// <param name="fileName">Name of the file</param>
        /// <param name="mediaType">Media type (currently only IMAGE is supported)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing media ID</returns>
        //Task<MediaUploadResponse> UploadMediaAsync(
        //    string businessId,
        //    Stream fileStream,
        //    string fileName,
        //    string mediaType,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Download an image from a message
        /// </summary>
        /// <param name="request">Media download request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing download URL</returns>
        //Task<MediaDownloadResponse> DownloadMediaAsync(
        //    MediaDownloadRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a Business Messaging Webhook configuration
        /// </summary>
        /// <param name="request">Webhook configuration request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing webhook configuration</returns>
        //Task<BusinessMessagingWebhookResponse> CreateWebhookAsync(
        //    BusinessMessagingWebhookRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a Business Messaging Webhook configuration
        /// </summary>
        /// <param name="appId">ID of your developer application</param>
        /// <param name="secret">Secret of your developer application</param>
        /// <param name="eventType">The type of Webhook event (DIRECT_MESSAGE)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing webhook configuration</returns>
        Task<BusinessMessagingWebhookResponse> GetWebhookAsync(
            string appId,
            string secret,
            string eventType,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a Business Messaging Webhook configuration
        /// </summary>
        /// <param name="request">Webhook delete request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deleted webhook configuration</returns>
        //Task<BusinessMessagingWebhookResponse> DeleteWebhookAsync(
        //    BusinessMessagingWebhookDeleteRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Create an automatic message for a Business Account
        /// </summary>
        /// <param name="request">Auto message create request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing auto message ID</returns>
        //Task<AutoMessageResponse> CreateAutoMessageAsync(
        //    AutoMessageCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update the automatic message for a Business Account
        /// </summary>
        /// <param name="request">Auto message update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing auto message ID</returns>
        //Task<AutoMessageResponse> UpdateAutoMessageAsync(
        //    AutoMessageUpdateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Turn on or turn off an automatic message for a Business Account
        /// </summary>
        /// <param name="request">Auto message status update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Empty response indicating success</returns>
        //Task UpdateAutoMessageStatusAsync(
        //    AutoMessageStatusUpdateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the automatic messages for a Business Account
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok Business Account</param>
        /// <param name="autoMessageType">The type of automatic message</param>
        /// <param name="autoMessageId">The ID of the automatic message (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing auto messages</returns>
        Task<AutoMessageGetResponse> GetAutoMessagesAsync(
            string businessId,
            string autoMessageType,
            string? autoMessageId = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete the automatic message for a Business Account
        /// </summary>
        /// <param name="request">Auto message delete request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Empty response indicating success</returns>
        //Task DeleteAutoMessageAsync(
        //    AutoMessageDeleteRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Sort the automatic message for a Business Account
        /// </summary>
        /// <param name="request">Auto message sort request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Empty response indicating success</returns>
        //Task SortAutoMessagesAsync(
        //    AutoMessageSortRequest request,
        //    CancellationToken cancellationToken = default);
    }
}
