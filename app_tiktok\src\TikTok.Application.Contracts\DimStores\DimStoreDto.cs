using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.DimStores
{
    public class DimStoreDto : EntityDto<Guid>
    {
        /// <summary>
        /// ID store từ TikTok API
        /// </summary>
        public string StoreId { get; set; }

        /// <summary>
        /// Tên store
        /// </summary>
        public string StoreName { get; set; }

        /// <summary>
        /// Mô tả store
        /// </summary>
        public string? StoreDescription { get; set; }

        /// <summary>
        /// Loại store
        /// </summary>
        public string? StoreType { get; set; }

        /// <summary>
        /// Trạng thái store
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Quốc gia
        /// </summary>
        public string? Country { get; set; }

        /// <summary>
        /// Tiền tệ
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Múi giờ
        /// </summary>
        public string? Timezone { get; set; }

        /// <summary>
        /// Ngày tạo store
        /// </summary>
        public DateTime? CreatedDate { get; set; }

        /// <summary>
        /// Ng<PERSON>y cập nhật gần nhất
        /// </summary>
        public DateTime? LastModifiedDate { get; set; }
    }
}
