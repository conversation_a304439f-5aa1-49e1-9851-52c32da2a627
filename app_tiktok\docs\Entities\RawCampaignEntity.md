# RawCampaignEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawCampaigns`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawCampaigns`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho chiến dịch quảng cáo (Campaign). Lưu trữ thông tin chi tiết về chiến dịch quảng cáo bao gồm cấu hình, mục tiêu, ngân sách, trạng thái và các cài đặt nâng cao.

## Bảng dữ liệu: RawCampaign

| Tên Field                   | Kiểu dữ liệu                 | Bắt buộc | Độ dài | Mô tả                                                                    |
| --------------------------- | ---------------------------- | -------- | ------ | ------------------------------------------------------------------------ |
| Id                          | Guid                         | ✓        | -      | ID duy nhất của bản ghi (Primary Key)                                    |
| AdvertiserId                | string                       | ✓        | 100    | ID nhà quảng cáo                                                         |
| CampaignId                  | string                       | ✓        | 100    | ID chiến dịch                                                            |
| CampaignSystemOrigin        | CampaignSystemOrigin?        | ❌       | -      | Nguồn gốc của chiến dịch                                                 |
| CreateTime                  | DateTime                     | ✓        | -      | Thời gian tạo chiến dịch, định dạng UTC                                  |
| ModifyTime                  | DateTime                     | ✓        | -      | Thời gian sửa đổi chiến dịch, định dạng UTC                              |
| ObjectiveType               | string                       | ✓        | 100    | Mục tiêu quảng cáo                                                       |
| AppPromotionType            | AppPromotionType?            | ❌       | -      | Loại quảng bá ứng dụng                                                   |
| VirtualObjectiveType        | VirtualObjectiveType?        | ❌       | -      | Loại mục tiêu mới                                                        |
| SalesDestination            | SalesDestination?            | ❌       | -      | Điểm đến bán hàng                                                        |
| IsSearchCampaign            | bool                         | ❌       | -      | Cho biết chiến dịch có phải là Chiến dịch Tìm kiếm hay không             |
| IsSmartPerformanceCampaign  | bool                         | ❌       | -      | Cho biết chiến dịch có phải là loại chiến dịch tự động hay không         |
| CampaignType                | CampaignType                 | ✓        | -      | Loại chiến dịch                                                          |
| AppId                       | string                       | ❌       | 100    | ID ứng dụng được quảng bá                                                |
| IsAdvancedDedicatedCampaign | bool                         | ❌       | -      | Cho biết chiến dịch có phải là Chiến dịch chuyên biệt nâng cao hay không |
| DisableSkanCampaign         | bool                         | ❌       | -      | Cho biết có tắt tính năng phân bổ SKAN hay không                         |
| BidAlignType                | BidAlignType?                | ❌       | -      | Loại phân bổ cho Chiến dịch chuyên biệt                                  |
| CampaignAppProfilePageState | CampaignAppProfilePageState? | ❌       | -      | Trạng thái của Trang hồ sơ ứng dụng                                      |
| RfCampaignType              | RfCampaignType?              | ❌       | -      | Loại chiến dịch Đặt chỗ cụ thể                                           |
| CampaignProductSource       | CampaignProductSource?       | ❌       | -      | Nguồn sản phẩm của chiến dịch                                            |
| CatalogEnabled              | bool                         | ❌       | -      | Cho biết có sử dụng danh mục để tự động quảng cáo hay không              |
| CampaignName                | string                       | ✓        | 255    | Tên chiến dịch                                                           |
| SpecialIndustries           | List<SpecialIndustries>?     | ❌       | -      | Các danh mục quảng cáo đặc biệt                                          |
| BudgetOptimizeOn            | bool                         | ❌       | -      | Cho biết CBO có được bật hay không                                       |
| BidType                     | string                       | ❌       | 50     | Chiến lược đặt giá thầu ở cấp chiến dịch                                 |
| DeepBidType                 | string                       | ❌       | 50     | Chiến lược đặt giá thầu cho các sự kiện trong ứng dụng                   |
| RoasBid                     | decimal                      | ❌       | -      | Mục tiêu ROAS cho Tối ưu hóa giá trị                                     |
| OptimizationGoal            | string                       | ❌       | 100    | Mục tiêu tối ưu hóa                                                      |
| BudgetMode                  | string                       | ✓        | 50     | Chế độ ngân sách                                                         |
| Budget                      | decimal                      | ❌       | -      | Ngân sách chiến dịch                                                     |
| RtaId                       | string                       | ❌       | 100    | ID API thời gian thực (RTA)                                              |
| RtaBidEnabled               | bool                         | ❌       | -      | Cho biết có sử dụng đặt giá thầu RTA hay không                           |
| RtaProductSelectionEnabled  | bool                         | ❌       | -      | Cho biết có sử dụng RTA để tự động chọn sản phẩm hay không               |
| OperationStatus             | string                       | ✓        | 20     | Trạng thái hoạt động                                                     |
| SecondaryStatus             | string                       | ❌       | 100    | Trạng thái chiến dịch (Trạng thái thứ cấp)                               |
| PostbackWindowMode          | PostbackWindowMode?          | ❌       | -      | Chế độ xác định postback SKAN 4.0                                        |
| IsNewStructure              | bool                         | ❌       | -      | Cho biết chiến dịch có cấu trúc mới hay không                            |
| Objective                   | Objective                    | ✓        | -      | Loại chiến dịch (ứng dụng hoặc trang đích)                               |
| CreationTime                | DateTime                     | ✓        | -      | Thời gian tạo bản ghi (Audit)                                            |
| CreatorId                   | Guid?                        | ❌       | -      | ID người tạo (Audit)                                                     |
| LastModificationTime        | DateTime?                    | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                           |
| LastModifierId              | Guid?                        | ❌       | -      | ID người sửa đổi cuối (Audit)                                            |

## Enums liên quan

### CampaignSystemOrigin

-   Định nghĩa nguồn gốc của chiến dịch

### AppPromotionType

-   Định nghĩa loại quảng bá ứng dụng

### VirtualObjectiveType

-   Định nghĩa loại mục tiêu mới

### SalesDestination

-   Định nghĩa điểm đến bán hàng

### CampaignType

-   Định nghĩa loại chiến dịch

### BidAlignType

-   Định nghĩa loại phân bổ cho Chiến dịch chuyên biệt

### CampaignAppProfilePageState

-   Định nghĩa trạng thái của Trang hồ sơ ứng dụng

### RfCampaignType

-   Định nghĩa loại chiến dịch Đặt chỗ cụ thể

### CampaignProductSource

-   Định nghĩa nguồn sản phẩm của chiến dịch

### SpecialIndustries

-   Định nghĩa các danh mục quảng cáo đặc biệt

### PostbackWindowMode

-   Định nghĩa chế độ xác định postback SKAN 4.0

### Objective

-   Định nghĩa loại chiến dịch (ứng dụng hoặc trang đích)

## Cấu trúc dữ liệu

### Thông tin cơ bản

-   **AdvertiserId**: Liên kết với tài khoản nhà quảng cáo
-   **CampaignId**: ID duy nhất của chiến dịch từ TikTok API
-   **CampaignName**: Tên hiển thị của chiến dịch

### Cấu hình chiến dịch

-   **ObjectiveType**: Mục tiêu chính của chiến dịch
-   **CampaignType**: Loại chiến dịch (tìm kiếm, hiển thị, video, etc.)
-   **BudgetMode**: Chế độ ngân sách (daily, lifetime)
-   **Budget**: Số tiền ngân sách

### Tối ưu hóa và giá thầu

-   **BidType**: Chiến lược đặt giá thầu
-   **DeepBidType**: Chiến lược đặt giá thầu sâu
-   **RoasBid**: Mục tiêu ROAS
-   **OptimizationGoal**: Mục tiêu tối ưu hóa

### Trạng thái và hoạt động

-   **OperationStatus**: Trạng thái hoạt động hiện tại
-   **SecondaryStatus**: Trạng thái phụ
-   **CreateTime/ModifyTime**: Thời gian tạo và sửa đổi

### Tính năng nâng cao

-   **RTA**: Real-time API integration
-   **SKAN**: Apple Search Ads attribution
-   **CBO**: Campaign Budget Optimization

## Mục đích sử dụng

-   Lưu trữ thông tin chi tiết về chiến dịch quảng cáo
-   Theo dõi cấu hình và cài đặt chiến dịch
-   Quản lý ngân sách và mục tiêu chiến dịch
-   Hỗ trợ báo cáo hiệu suất chiến dịch
-   Tích hợp với TikTok Business API
