/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Page operations
    /// </summary>
    public class PageApi : IPageApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<PageApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the PageApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public PageApi(IApiClient apiClient, ILogger<PageApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get the Page ID. After a page is created, you can get the page ID and then use the page ID in your ads.
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting pages</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing page information</returns>
        public async Task<Response<PageGetResponse>> GetPagesAsync(
            string accessToken,
            PageGetRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            // Validate that either advertiser_id or library_id is provided
            if (string.IsNullOrWhiteSpace(request.AdvertiserId) && string.IsNullOrWhiteSpace(request.LibraryId))
                throw new ArgumentException("Either AdvertiserId or LibraryId must be provided", nameof(request));

            _logger?.LogInformation("Getting pages for advertiser {AdvertiserId} or library {LibraryId}", 
                request.AdvertiserId, request.LibraryId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            // For GET requests with complex parameters, use request body instead of query params
            // This follows the pattern used by other APIs in the codebase
            var requestBody = new
            {
                advertiser_id = request.AdvertiserId,
                library_id = request.LibraryId,
                page = request.Page,
                page_size = request.PageSize,
                status = request.Status,
                title = request.Title,
                update_time_range = request.UpdateTimeRange,
                business_type = request.BusinessType,
                business_types = request.BusinessTypes
            };

            return await _apiClient.CallApiAsync<Response<PageGetResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{PageEndpoints.GetPages}",
                HttpMethod.Get,
                queryParams: null,
                body: requestBody,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Create a TikTok Instant Page (TIP) Editor SDK access token. The TIP Editor SDK access token is required for loading the TIP Editor SDK.
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for creating TIP SDK access token</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the TIP SDK access token</returns>
        //public async Task<Response<TipSdkAccessTokenCreateResponse>> CreateTipSdkAccessTokenAsync(
        //    string accessToken,
        //    TipSdkAccessTokenCreateRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Creating TIP SDK access token for advertiser {AdvertiserId}", request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken,
        //        ["Content-Type"] = "application/json"
        //    };

        //    return await _apiClient.CallApiAsync<Response<TipSdkAccessTokenCreateResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{PageEndpoints.CreateTipSdkAccessToken}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Verify whether a TikTok Instant Page (TIP) Editor SDK access token has expired.
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for validating TIP SDK access token</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing validation result</returns>
        //public async Task<Response<TipSdkAccessTokenValidateResponse>> ValidateTipSdkAccessTokenAsync(
        //    string accessToken,
        //    TipSdkAccessTokenValidateRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.TipSdkAccessToken))
        //        throw new ArgumentException("TIP SDK access token cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Validating TIP SDK access token for advertiser {AdvertiserId}", request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken,
        //        ["Content-Type"] = "application/json"
        //    };

        //    return await _apiClient.CallApiAsync<Response<TipSdkAccessTokenValidateResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{PageEndpoints.ValidateTipSdkAccessToken}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Renew a TikTok Instant Page (TIP) Editor SDK access token. Once renewed, the same token will remain valid for another 24 hours.
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for renewing TIP SDK access token</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<object>> RenewTipSdkAccessTokenAsync(
        //    string accessToken,
        //    TipSdkAccessTokenRenewRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.TipSdkAccessToken))
        //        throw new ArgumentException("TIP SDK access token cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Renewing TIP SDK access token for advertiser {AdvertiserId}", request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken,
        //        ["Content-Type"] = "application/json"
        //    };

        //    return await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{PageEndpoints.RenewTipSdkAccessToken}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}
    }
}
