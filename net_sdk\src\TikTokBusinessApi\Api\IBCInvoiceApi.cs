/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API BC Invoice operations
    /// </summary>
    public interface IBCInvoiceApi
    {
        /// <summary>
        /// Get invoices of a Business Center account
        /// </summary>
        /// <param name="request">Get invoices request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing invoice information</returns>
        Task<GetInvoicesResponse> GetInvoicesAsync(
            GetInvoicesRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get total unpaid amount of a Business Center account
        /// </summary>
        /// <param name="request">Get unpaid amount request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing unpaid amount information</returns>
        Task<GetUnpaidAmountResponse> GetUnpaidAmountAsync(
            GetUnpaidAmountRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Download an individual invoice synchronously
        /// </summary>
        /// <param name="request">Download invoice request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>PDF file stream as byte array</returns>
        Task<byte[]> DownloadInvoiceAsync(
            DownloadInvoiceRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create an asynchronous download task
        /// </summary>
        /// <param name="request">Create download task request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task ID</returns>
        //Task<CreateDownloadTaskResponse> CreateDownloadTaskAsync(
        //    CreateDownloadTaskRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get asynchronous download task (BILLING_REPORT)
        /// </summary>
        /// <param name="request">Get download task request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing download task information</returns>
        Task<GetDownloadTaskResponse> GetDownloadTaskAsync(
            GetDownloadTaskRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get asynchronous download task list (INVOICE_LIST and INVOICE_BATCH)
        /// </summary>
        /// <param name="request">Get download task list request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing download task list</returns>
        Task<GetDownloadTaskListResponse> GetDownloadTaskListAsync(
            GetDownloadTaskListRequest request,
            CancellationToken cancellationToken = default);
    }
}
