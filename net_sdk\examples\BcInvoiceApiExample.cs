/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the BcInvoiceApi
    /// </summary>
    public class BcInvoiceApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public BcInvoiceApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example of getting invoices of a Business Center
        /// </summary>
        public async Task GetInvoicesExample()
        {
            try
            {
                var request = new GetInvoicesRequest
                {
                    BcId = "your-business-center-id",
                    InvoiceTypes = new List<string> { "RECON", "CREDIT" },
                    PayStatuses = new List<string> { "UNPAID", "PAID" },
                    StartTime = "2024-01-01 00:00:00",
                    EndTime = "2024-02-01 00:00:00",
                    Page = 1,
                    PageSize = 10
                };

                var response = await _client.BcInvoice.GetInvoicesAsync("your-access-token", request);

                Console.WriteLine($"Found {response.Data?.Data?.List?.Count ?? 0} invoices");
                Console.WriteLine($"Request ID: {response.RequestId}");

                if (response.Data?.Data?.List != null)
                {
                    foreach (var invoice in response.Data.Data.List)
                    {
                        Console.WriteLine($"Invoice ID: {invoice.InvoiceId}");
                        Console.WriteLine($"Serial Number: {invoice.SerialNumber}");
                        Console.WriteLine($"Amount: {invoice.Amount} {invoice.CurrencyCode}");
                        Console.WriteLine($"Status: {invoice.Status}");
                        Console.WriteLine($"Pay Status: {invoice.PayStatus}");
                        Console.WriteLine("---");
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.Code}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting unpaid amount of a Business Center
        /// </summary>
        public async Task GetUnpaidAmountExample()
        {
            try
            {
                var request = new GetUnpaidAmountRequest
                {
                    BcId = "your-business-center-id",
                    InvoiceType = "RECON"
                };

                var response = await _client.BcInvoice.GetUnpaidAmountAsync("your-access-token", request);

                Console.WriteLine($"Unpaid Amount: {response.Data?.Data?.Result?.Amount} {response.Data?.Data?.Result?.CurrencyCode}");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.Code}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of downloading an individual invoice synchronously
        /// </summary>
        public async Task DownloadInvoiceExample()
        {
            try
            {
                var request = new DownloadInvoiceRequest
                {
                    BcId = "your-business-center-id",
                    InvoiceId = "your-invoice-id"
                };

                var pdfBytes = await _client.BcInvoice.DownloadInvoiceAsync("your-access-token", request);

                // Save the PDF file
                var fileName = $"invoice_{request.InvoiceId}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
                await File.WriteAllBytesAsync(fileName, pdfBytes);

                Console.WriteLine($"Invoice downloaded successfully: {fileName}");
                Console.WriteLine($"File size: {pdfBytes.Length} bytes");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.Code}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of creating an asynchronous download task
        /// </summary>
        public async Task CreateDownloadTaskExample()
        {
            try
            {
                var request = new CreateDownloadTaskRequest
                {
                    BcId = "your-business-center-id",
                    DownloadType = "INVOICE_LIST",
                    Filtering = new DownloadTaskFiltering
                    {
                        PayStatuses = new List<string> { "UNPAID" },
                        StartTime = "2024-01-01",
                        EndTime = "2024-02-01"
                    }
                };

                var response = await _client.BcInvoice.CreateDownloadTaskAsync("your-access-token", request);

                Console.WriteLine($"Download task created successfully!");
                Console.WriteLine($"Task ID: {response.Data?.Data?.TaskId}");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.Code}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting download task status (for BILLING_REPORT)
        /// </summary>
        public async Task GetDownloadTaskExample()
        {
            try
            {
                var request = new GetDownloadTaskRequest
                {
                    BcId = "your-business-center-id",
                    TaskId = "your-task-id"
                };

                var response = await _client.BcInvoice.GetDownloadTaskAsync("your-access-token", request);

                Console.WriteLine($"Task Status: {response.Data?.Data?.Status}");
                Console.WriteLine($"Download URL: {response.Data?.Data?.DownloadUrl}");
                Console.WriteLine($"Request ID: {response.RequestId}");

                if (!string.IsNullOrEmpty(response.Data?.Data?.ErrorMsg))
                {
                    Console.WriteLine($"Error Message: {response.Data.Data.ErrorMsg}");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.Code}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting download task list (for INVOICE_LIST and INVOICE_BATCH)
        /// </summary>
        public async Task GetDownloadTaskListExample()
        {
            try
            {
                var request = new GetDownloadTaskListRequest
                {
                    BcId = "your-business-center-id",
                    Page = 1,
                    PageSize = 10
                };

                var response = await _client.BcInvoice.GetDownloadTaskListAsync("your-access-token", request);

                Console.WriteLine($"Found {response.Data?.Data?.List?.Count ?? 0} download tasks");
                Console.WriteLine($"Request ID: {response.RequestId}");

                if (response.Data?.Data?.List != null)
                {
                    foreach (var task in response.Data.Data.List)
                    {
                        Console.WriteLine($"Task ID: {task.TaskId}");
                        Console.WriteLine($"Status: {task.Status}");
                        Console.WriteLine($"Download Type: {task.DownloadType}");
                        Console.WriteLine($"Create Time: {task.CreateTime}");
                        Console.WriteLine($"Update Time: {task.UpdateTime}");
                        
                        if (!string.IsNullOrEmpty(task.DownloadUrl))
                        {
                            Console.WriteLine($"Download URL: {task.DownloadUrl}");
                        }
                        
                        Console.WriteLine("---");
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.Code}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Comprehensive example showing a complete workflow
        /// </summary>
        public async Task ComprehensiveWorkflowExample()
        {
            Console.WriteLine("=== BC Invoice API Comprehensive Example ===");

            // 1. Get invoices
            Console.WriteLine("\n1. Getting invoices...");
            await GetInvoicesExample();

            // 2. Get unpaid amount
            Console.WriteLine("\n2. Getting unpaid amount...");
            await GetUnpaidAmountExample();

            // 3. Create download task
            Console.WriteLine("\n3. Creating download task...");
            await CreateDownloadTaskExample();

            // 4. Get download task list
            Console.WriteLine("\n4. Getting download task list...");
            await GetDownloadTaskListExample();

            Console.WriteLine("\n=== Example completed ===");
        }

        public void Dispose()
        {
            _client?.Dispose();
        }
    }
}
