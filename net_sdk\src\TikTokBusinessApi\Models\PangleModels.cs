/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Get Pangle Block List Models

    /// <summary>
    /// Request for getting the Pangle block list
    /// </summary>
    public class GetPangleBlockListRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting the Pangle block list
    /// </summary>
    public class GetPangleBlockListResponse
    {
        /// <summary>
        /// App block list. iTunes ID for iOS app and package name for Android apps.
        /// </summary>
        [JsonPropertyName("app_list")]
        public List<string> AppList { get; set; } = new();

        /// <summary>
        /// App block list ID
        /// </summary>
        [JsonPropertyName("app_package_id")]
        public string AppPackageId { get; set; } = string.Empty;

        /// <summary>
        /// The time when the App block list was last updated, in ISO-8601 format.
        /// Example: "2024-01-01T00:00:01+00:00"
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string ModifyTime { get; set; } = string.Empty;
    }

    #endregion

    #region Update Pangle Block List Models

    /// <summary>
    /// Request for updating the Pangle block list
    /// </summary>
    public class UpdatePangleBlockListRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Apps to add in the block list. iTunes ID for iOS app and package name for Android apps.
        /// </summary>
        [JsonPropertyName("add_app_list")]
        public List<string>? AddAppList { get; set; }

        /// <summary>
        /// Apps to delete in the block list. iTunes ID for iOS app and package name for Android apps.
        /// </summary>
        [JsonPropertyName("delete_app_list")]
        public List<string>? DeleteAppList { get; set; }

        /// <summary>
        /// Whether to clear the original App block list, default value: False.
        /// If False, the corresponding App will be deleted and added based on the original list;
        /// if True, the original list will be cleared and the corresponding App will be added.
        /// </summary>
        [JsonPropertyName("clear_old_app")]
        public bool? ClearOldApp { get; set; }
    }

    /// <summary>
    /// Response for updating the Pangle block list
    /// </summary>
    public class UpdatePangleBlockListResponse
    {
        /// <summary>
        /// The number of apps that have been successfully added to the block list.
        /// The success count will not include deletion, regardless of whether the deletion is successful or not.
        /// </summary>
        [JsonPropertyName("success_count")]
        public int SuccessCount { get; set; }
    }

    #endregion

    #region Get Pangle Audience Packages Models

    /// <summary>
    /// Request for getting the Pangle audience packages
    /// </summary>
    public class GetPangleAudiencePackagesRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting the Pangle audience packages
    /// </summary>
    public class GetPangleAudiencePackagesResponse
    {
        /// <summary>
        /// Pangle audience package list
        /// </summary>
        [JsonPropertyName("packages")]
        public List<PangleAudiencePackage> Packages { get; set; } = new();
    }

    /// <summary>
    /// Pangle audience package information
    /// </summary>
    public class PangleAudiencePackage
    {
        /// <summary>
        /// Pangle audience package type. Enum values: EXCLUDE, INCLUDE
        /// </summary>
        [JsonPropertyName("bind_type")]
        public string BindType { get; set; } = string.Empty;

        /// <summary>
        /// Pangle audience package ID
        /// </summary>
        [JsonPropertyName("package_id")]
        public string PackageId { get; set; } = string.Empty;

        /// <summary>
        /// Pangle audience package name
        /// </summary>
        [JsonPropertyName("package_name")]
        public string PackageName { get; set; } = string.Empty;
    }

    #endregion
}
