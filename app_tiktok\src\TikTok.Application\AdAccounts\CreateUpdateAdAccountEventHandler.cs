﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.EventBus;

namespace TikTok.AdAccounts
{
    public class CreateUpdateAdAccountEventHandler : ILocalEventHandler<CreateUpdateAdAccountEto>
    {
        public Task HandleEventAsync(CreateUpdateAdAccountEto eventData)
        {
            throw new NotImplementedException();
        }
    }
}
