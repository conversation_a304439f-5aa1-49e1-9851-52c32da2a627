using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using TikTok.Customers;
using TikTok.Entities;
using TikTok.Repositories;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Customers
{
    /// <summary>
    /// AppService implementation cho CustomerAdAccount
    /// </summary>
    [Authorize]
    public class CustomerAdAccountAppService : 
        CrudAppService<
            CustomerAdAccountEntity,
            CustomerAdAccountDto,
            Guid,
            PagedAndSortedResultRequestDto,
            CreateCustomerAdAccountDto,
            UpdateCustomerAdAccountDto>,
        ICustomerAdAccountAppService
    {
        private readonly ICustomerAdAccountRepository _customerAdAccountRepository;
        private readonly ICustomerRepository _customerRepository;

        public CustomerAdAccountAppService(
            ICustomerAdAccountRepository customerAdAccountRepository,
            ICustomerRepository customerRepository) 
            : base(customerAdAccountRepository)
        {
            _customerAdAccountRepository = customerAdAccountRepository;
            _customerRepository = customerRepository;
        }

        public async Task<List<CustomerAdAccountDto>> GetByCustomerIdAsync(Guid customerId)
        {
            // Kiểm tra customer có tồn tại không
            var customer = await _customerRepository.GetAsync(customerId);
            if (customer == null)
            {
                throw new UserFriendlyException("Khách hàng không tồn tại");
            }

            var adAccounts = await _customerAdAccountRepository.GetByCustomerIdAsync(customerId);
            return ObjectMapper.Map<List<CustomerAdAccountEntity>, List<CustomerAdAccountDto>>(adAccounts);
        }

        public async Task<CustomerAdAccountDto?> GetByAdvertiserIdAsync(string advertiserId)
        {
            var adAccount = await _customerAdAccountRepository.GetByAdvertiserIdAsync(advertiserId);
            return adAccount != null ? ObjectMapper.Map<CustomerAdAccountEntity, CustomerAdAccountDto>(adAccount) : null;
        }

        public async Task<CustomerAdAccountDto?> GetByShopIdAsync(string shopId)
        {
            var adAccount = await _customerAdAccountRepository.GetByShopIdAsync(shopId);
            return adAccount != null ? ObjectMapper.Map<CustomerAdAccountEntity, CustomerAdAccountDto>(adAccount) : null;
        }

        public async Task<PagedResultDto<CustomerAdAccountFlatDto>> GetFlatListAsync(GetCustomerAdAccountListDto input)
        {
            // Get the total count
            var totalCount = await _customerAdAccountRepository.GetCountAsync(
                input.Filter,
                input.CustomerId,
                input.AdvertiserId,
                input.ShopId
            );

            // Get the ad accounts with customer information using optimized query
            var adAccountsWithCustomers = await _customerAdAccountRepository.GetListWithCustomerInfoAsync(
                input.Sorting,
                input.MaxResultCount,
                input.SkipCount,
                input.Filter,
                input.CustomerId,
                input.AdvertiserId,
                input.ShopId
            );

            // Map to flat DTOs efficiently
            var flatDtos = adAccountsWithCustomers.Select(adAccount => new CustomerAdAccountFlatDto
            {
                Id = adAccount.Id,
                CustomerId = adAccount.CustomerId,
                CustomerIdCode = adAccount.Customer?.CustomerId,
                CustomerName = adAccount.Customer?.CustomerName,
                AdvertiserId = adAccount.AdvertiserId,
                AdvertiserName = adAccount.AdvertiserName,
                ShopId = adAccount.ShopId,
                ShopName = adAccount.ShopName,
                CreationTime = adAccount.CreationTime,
                CreatorId = adAccount.CreatorId,
                LastModificationTime = adAccount.LastModificationTime,
                LastModifierId = adAccount.LastModifierId
            }).ToList();

            return new PagedResultDto<CustomerAdAccountFlatDto>
            {
                TotalCount = totalCount,
                Items = flatDtos
            };
        }

        public override async Task<CustomerAdAccountDto> CreateAsync(CreateCustomerAdAccountDto input)
        {
            // Kiểm tra customer có tồn tại không
            var customer = await _customerRepository.GetAsync(input.CustomerId);
            if (customer == null)
            {
                throw new UserFriendlyException("Khách hàng không tồn tại");
            }

            // Kiểm tra ít nhất phải có AdvertiserId hoặc ShopId
            if (string.IsNullOrEmpty(input.AdvertiserId) && string.IsNullOrEmpty(input.ShopId))
            {
                throw new UserFriendlyException("Phải nhập ít nhất Advertiser ID hoặc Shop ID");
            }

            // Kiểm tra AdvertiserId đã tồn tại chưa (chỉ khi có giá trị)
            if (!string.IsNullOrEmpty(input.AdvertiserId))
            {
                var existingAdvertiser = await _customerAdAccountRepository.IsAdvertiserIdExistsAsync(input.AdvertiserId);
                if (existingAdvertiser)
                {
                    throw new UserFriendlyException("Advertiser ID đã tồn tại");
                }
            }

            // Kiểm tra ShopId đã tồn tại chưa (chỉ khi có giá trị)
            if (!string.IsNullOrEmpty(input.ShopId))
            {
                var existingShop = await _customerAdAccountRepository.IsShopIdExistsAsync(input.ShopId);
                if (existingShop)
                {
                    throw new UserFriendlyException("Shop ID đã tồn tại");
                }
            }

            return await base.CreateAsync(input);
        }

        public override async Task<CustomerAdAccountDto> UpdateAsync(Guid id, UpdateCustomerAdAccountDto input)
        {
            // Kiểm tra ít nhất phải có AdvertiserId hoặc ShopId
            if (string.IsNullOrEmpty(input.AdvertiserId) && string.IsNullOrEmpty(input.ShopId))
            {
                throw new UserFriendlyException("Phải nhập ít nhất Advertiser ID hoặc Shop ID");
            }

            // Kiểm tra AdvertiserId đã tồn tại chưa (trừ record hiện tại, chỉ khi có giá trị)
            if (!string.IsNullOrEmpty(input.AdvertiserId))
            {
                var existingAdvertiser = await _customerAdAccountRepository.IsAdvertiserIdExistsAsync(input.AdvertiserId, id);
                if (existingAdvertiser)
                {
                    throw new UserFriendlyException("Advertiser ID đã tồn tại");
                }
            }

            // Kiểm tra ShopId đã tồn tại chưa (trừ record hiện tại, chỉ khi có giá trị)
            if (!string.IsNullOrEmpty(input.ShopId))
            {
                var existingShop = await _customerAdAccountRepository.IsShopIdExistsAsync(input.ShopId, id);
                if (existingShop)
                {
                    throw new UserFriendlyException("Shop ID đã tồn tại");
                }
            }

            return await base.UpdateAsync(id, input);
        }
    }
}
