using System;
using System.Collections.Generic;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.Customers
{
    /// <summary>
    /// DTO cho việc hiển thị thông tin khách hàng
    /// </summary>
    public class CustomerDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// Mã khách hàng (ví dụ: TT1010)
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        /// Tên khách hàng
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// Tên tài khoản
        /// </summary>
        public string? AccountName { get; set; }

        /// <summary>
        /// Số điện thoại
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Loại khách hàng (Đại lý, Khách lẻ)
        /// </summary>
        public CustomerType CustomerType { get; set; }

        /// <summary>
        /// Trang web
        /// </summary>
        public string? Website { get; set; }

        /// <summary>
        /// Danh sách tài khoản quảng cáo (mối quan hệ One-to-Many)
        /// </summary>
        public List<CustomerAdAccountDto> AdAccounts { get; set; }

        public CustomerDto()
        {
            AdAccounts = new List<CustomerAdAccountDto>();
        }
    }
}
