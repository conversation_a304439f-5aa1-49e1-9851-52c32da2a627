/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Spark Ads Recommendation operations
    /// </summary>
    public class SparkAdsRecommendationApi : ISparkAdsRecommendationApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<SparkAdsRecommendationApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the SparkAdsRecommendationApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public SparkAdsRecommendationApi(IApiClient apiClient, ILogger<SparkAdsRecommendationApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get Spark Ads video recommendations for a Business Account
        /// </summary>
        /// <param name="request">Request parameters for getting business video recommendations</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing business video recommendations</returns>
        public async Task<BusinessVideoRecommendationsResponse> GetBusinessVideoRecommendationsAsync(
            BusinessVideoRecommendationsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting business video recommendations for business_id: {BusinessId}", request.BusinessId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = request.BusinessId
            };

            if (request.VideoIds != null && request.VideoIds.Count > 0)
            {
                queryParams["video_ids"] = System.Text.Json.JsonSerializer.Serialize(request.VideoIds);
            }

            if (request.ExcludeVideoIds != null && request.ExcludeVideoIds.Count > 0)
            {
                queryParams["exclude_video_ids"] = System.Text.Json.JsonSerializer.Serialize(request.ExcludeVideoIds);
            }

            if (!string.IsNullOrEmpty(request.Time))
            {
                queryParams["time"] = request.Time;
            }

            if (!string.IsNullOrEmpty(request.ObjectiveType))
            {
                queryParams["objective_type"] = request.ObjectiveType;
            }

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SparkAdsRecommendationEndpoints.GetBusinessVideoRecommendations}";

            return await _apiClient.CallApiAsync<BusinessVideoRecommendationsResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }

        /// <summary>
        /// Get Spark Ads video recommendations for a TTO account
        /// </summary>
        /// <param name="request">Request parameters for getting TTO video recommendations</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TTO video recommendations</returns>
        public async Task<TTOVideoRecommendationsResponse> GetTTOVideoRecommendationsAsync(
            TTOVideoRecommendationsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting TTO video recommendations for tcm_account_id: {TcmAccountId}", request.TcmAccountId);

            var queryParams = new Dictionary<string, string>
            {
                ["tcm_account_id"] = request.TcmAccountId
            };

            if (request.VideoIds != null && request.VideoIds.Count > 0)
            {
                queryParams["video_ids"] = System.Text.Json.JsonSerializer.Serialize(request.VideoIds);
            }

            if (request.ExcludeVideoIds != null && request.ExcludeVideoIds.Count > 0)
            {
                queryParams["exclude_video_ids"] = System.Text.Json.JsonSerializer.Serialize(request.ExcludeVideoIds);
            }

            if (!string.IsNullOrEmpty(request.Time))
            {
                queryParams["time"] = request.Time;
            }

            if (!string.IsNullOrEmpty(request.ObjectiveType))
            {
                queryParams["objective_type"] = request.ObjectiveType;
            }

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SparkAdsRecommendationEndpoints.GetTTOVideoRecommendations}";

            return await _apiClient.CallApiAsync<TTOVideoRecommendationsResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }

        // /// <summary>
        // /// Create a campaign, an ad group, and a Spark Ad in one step
        // /// </summary>
        // /// <param name="request">Request parameters for creating all-in-one Spark Ad</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing the created campaign, ad group, and ad IDs</returns>
        // public async Task<AllInOneSparkAdResponse> CreateAllInOneSparkAdAsync(
        //     AllInOneSparkAdRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogDebug("Creating all-in-one Spark Ad for advertiser_id: {AdvertiserId}", request.AdvertiserId);

        //     var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SparkAdsRecommendationEndpoints.CreateAllInOneSparkAd}";

        //     return await _apiClient.CallApiAsync<AllInOneSparkAdResponse>(
        //         path,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken);
        // }
    }
}
