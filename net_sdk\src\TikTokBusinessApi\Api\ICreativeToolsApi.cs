/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Creative Tools operations
    /// </summary>
    public interface ICreativeToolsApi
    {
        /// <summary>
        /// Get the status of an asynchronous creative tool task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="taskId">Task ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task status</returns>
        Task<Response<TaskStatusResponse>> GetTaskStatusAsync(
            string accessToken,
            string advertiserId,
            string taskId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Edit an image
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Image edit request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing edited image information</returns>
        //Task<Response<ImageEditResponse>> EditImageAsync(
        //    string accessToken,
        //    ImageEditRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Preview an ad
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Ad preview request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing preview information</returns>
        //Task<Response<AdPreviewResponse>> PreviewAdAsync(
        //    string accessToken,
        //    AdPreviewRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a Smart Video Soundtrack task (Deprecated)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Video soundtrack creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task ID</returns>
        //Task<Response<TaskCreateResponse>> CreateVideoSoundtrackTaskAsync(
        //    string accessToken,
        //    VideoSoundtrackCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a Quick Optimization task (Deprecated)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Quick optimization creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task ID</returns>
        //Task<Response<TaskCreateResponse>> CreateQuickOptimizationTaskAsync(
        //    string accessToken,
        //    QuickOptimizationCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a Smart Video task (Deprecated)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Smart video creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task ID</returns>
        //Task<Response<TaskCreateResponse>> CreateSmartVideoTaskAsync(
        //    string accessToken,
        //    SmartVideoCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete creative assets
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Creative asset deletion request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deletion results</returns>
        //Task<Response<CreativeAssetDeleteResponse>> DeleteCreativeAssetsAsync(
        //    string accessToken,
        //    CreativeAssetDeleteRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get Smart Text recommendations
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Smart text generation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing text recommendations</returns>
        Task<Response<SmartTextGenerateResponse>> GetSmartTextRecommendationsAsync(
            string accessToken,
            SmartTextGenerateRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Send Smart Text feedback
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Smart text feedback request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> SendSmartTextFeedbackAsync(
        //    string accessToken,
        //    SmartTextFeedbackRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get recommended CTAs
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="newVersion">Whether to use the new version</param>
        /// <param name="assetType">Asset type</param>
        /// <param name="contentType">Content type</param>
        /// <param name="objectiveType">Objective type</param>
        /// <param name="promotionType">Promotion type</param>
        /// <param name="language">Language</param>
        /// <param name="appId">App ID</param>
        /// <param name="placements">Placements</param>
        /// <param name="regionCodes">Region codes</param>
        /// <param name="optimizationGoal">Optimization goal</param>
        /// <param name="adTexts">Ad texts</param>
        /// <param name="landingPageUrl">Landing page URL</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing CTA recommendations</returns>
        Task<Response<CtaRecommendResponse>> GetRecommendedCTAsAsync(
            string accessToken,
            string advertiserId,
            bool? newVersion = null,
            string? assetType = null,
            string? contentType = null,
            string? objectiveType = null,
            string? promotionType = null,
            string? language = null,
            string? appId = null,
            List<string>? placements = null,
            List<string>? regionCodes = null,
            string? optimizationGoal = null,
            List<string>? adTexts = null,
            string? landingPageUrl = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a Smart Fix task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Smart Fix creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task results</returns>
        //Task<Response<SmartFixCreateResponse>> CreateSmartFixTaskAsync(
        //    string accessToken,
        //    SmartFixCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the results of a Smart Fix task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="taskId">Fix task ID</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing Smart Fix results</returns>
        Task<Response<SmartFixResultsResponse>> GetSmartFixTaskResultsAsync(
            string accessToken,
            string taskId,
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get Creative Fatigue Detection results
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Creative fatigue request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing fatigue detection results</returns>
        Task<Response<CreativeFatigueResponse>> GetCreativeFatigueResultsAsync(
            string accessToken,
            CreativeFatigueRequest request,
            CancellationToken cancellationToken = default);
    }
}
