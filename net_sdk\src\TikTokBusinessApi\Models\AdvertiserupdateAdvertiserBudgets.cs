/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Advertiser budget information for updates
    /// </summary>
    public class AdvertiserupdateAdvertiserBudgets
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public long? AdvertiserId { get; set; }

        /// <summary>
        /// Budget amount
        /// </summary>
        [JsonPropertyName("budget")]
        public double? Budget { get; set; }

        /// <summary>
        /// Budget mode
        /// </summary>
        [JsonPropertyName("budget_mode")]
        public string? BudgetMode { get; set; }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="obj">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not AdvertiserupdateAdvertiserBudgets other)
                return false;

            return AdvertiserId == other.AdvertiserId &&
                   Budget == other.Budget &&
                   BudgetMode == other.BudgetMode;
        }

        /// <summary>
        /// Returns the hash code for this instance
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AdvertiserId, Budget, BudgetMode);
        }

        /// <summary>
        /// Returns the string representation of the object
        /// </summary>
        /// <returns>String representation of the object</returns>
        public override string ToString()
        {
            return $"AdvertiserupdateAdvertiserBudgets {{ AdvertiserId = {AdvertiserId}, Budget = {Budget}, BudgetMode = {BudgetMode} }}";
        }
    }
}
