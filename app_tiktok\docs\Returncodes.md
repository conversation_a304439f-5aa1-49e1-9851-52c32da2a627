# Tài Liệu Tham Khảo Mã Lỗi TikTok API for Business

Tài liệu này cung cấp hướng dẫn toàn diện về các mã trả về của TikTok API for Business, mô tả lỗi và chiến lược xử lý lỗi được khuyến nghị.

## Giới Thiệu

Mã trả về của TikTok API for Business là các mã trạng thái duy nhất được thiết kế đặc biệt để ghi lại trạng thái của lời gọi API và cung cấp thông tin về lý do thất bại nếu lời gọi không thành công.

**Lưu ý quan trọng**: Các mã trả về này có độ ưu tiên cao hơn mã trạng thái HTTP trong việc xác định xem lời gọi đến endpoint TikTok API có thành công hay không. Ví dụ, phản hồi với mã trạng thái HTTP 200 cùng với mã trả về 40002 cho biết lời gọi không thành công.

## Mã Thành Công

| Mã    | Mô Tả               |
| ----- | ------------------- |
| 0     | Thành công          |
| 20001 | Thành công một phần |

## Mã Lỗi Không Thành Công

### Lưu Ý Quan Trọng

-   Mã trả về bắt đầu bằng 5 (ví dụ: 51003) cho biết lỗi dịch vụ
-   Lỗi HTTP 404 có thể xảy ra khi:
    -   Sử dụng sai phương thức API (ví dụ: dùng POST thay vì GET)
    -   Sử dụng sai đường dẫn API (thiếu dấu gạch chéo "/" ở cuối)

## Phân Loại Mã Lỗi

### Lỗi Chung (40000-40059)

**40000 - Tham Số Không Hợp Lệ**

-   **Nguyên nhân**: Các tham số được cung cấp không hợp lệ
-   **Xử lý lỗi**: Tham khảo tài liệu API để sử dụng tham số chính xác

**40001 - Không Có Quyền**

-   **Nguyên nhân**: Không có quyền thực hiện thao tác liên quan
-   **Ví dụ**: "You don't have permission to operate this catalog:{catalog_id}"
-   **Xử lý lỗi**: Đảm bảo có đủ quyền cần thiết và thử lại

**40002 - Lỗi Tham Số**

-   **Nguyên nhân phổ biến**:
    -   Thiếu các trường bắt buộc
    -   Định dạng giá trị trường không chính xác
    -   Giá trị hoặc trường không được hỗ trợ
    -   Không tương thích giữa các trường
    -   Độ chính xác dữ liệu không hợp lệ
    -   Số không hợp lệ
    -   Vấn đề về audience
-   **Ví dụ**: "Audience is not editable, please check audience status"
-   **Xử lý lỗi**: Kiểm tra tài liệu API để sử dụng tham số chính xác

**40006 - Không Tương Thích Phiên Bản API**

-   **Nguyên nhân**:
    -   Sử dụng sản phẩm không được hỗ trợ trong phiên bản API được chỉ định
    -   Sử dụng phiên bản API không tồn tại
-   **Ví dụ**: "The old API version does not support DPA ad"
-   **Xử lý lỗi**: Đảm bảo sử dụng phiên bản API hiện tại

**40007 - Đối Tượng Không Tồn Tại**

-   **Nguyên nhân**:
    -   Chỉ định đối tượng không tồn tại (campaign, ad group, ad, v.v.)
    -   Chỉ định URL đã lỗi thời hoặc file không tồn tại
-   **Ví dụ**: "The ad {} does not exist"
-   **Xử lý lỗi**: Đảm bảo đối tượng tồn tại và xác thực các URL được sử dụng

**40008 - Interface Chưa Được Triển Khai**

-   **Nguyên nhân**:
    -   Sử dụng endpoint không tồn tại
    -   Sử dụng endpoint không được hỗ trợ trong môi trường sandbox
-   **Xử lý lỗi**: Đảm bảo đường dẫn endpoint chính xác

**40009 - Tham Số Không Được Hỗ Trợ Trong Sandbox**

-   **Ví dụ**: "Unsupported data_level in sandbox environment: %s"
-   **Xử lý lỗi**: Đảm bảo giá trị tham số được hỗ trợ trong môi trường sandbox

**40010 - Domain Không Được Hỗ Trợ**

-   **Xử lý lỗi**: Đảm bảo base_url chính xác: `https://business-api.tiktok.com/open_api`

**40011 - Quá Nhiều ID**

-   **Xử lý lỗi**: Giảm số lượng ID trong một request hoặc chia thành nhiều batch

**40013 - Tài Khoản Sandbox Không Tồn Tại**

-   **Xử lý lỗi**: Tham khảo tài liệu Sandbox accounts để thiết lập tài khoản sandbox

**40014 - Chức Năng Không Được Hỗ Trợ Trong Sandbox**

-   **Xử lý lỗi**: Tham khảo tài liệu Sandbox accounts để biết các tính năng được hỗ trợ

**40050 - Request Trùng Lặp**

**40051 - Phiên Bản API Không Hợp Lệ**

-   **Xử lý lỗi**: Đảm bảo sử dụng phiên bản API chính xác

**40052 - ACO Material Đã Tồn Tại**

-   **Xử lý lỗi**: Tham khảo tài liệu Automated Creative Optimization

**40053 - Video ID Không Hợp Lệ**

-   **Xử lý lỗi**: Sử dụng Video ID hợp lệ từ `/file/video/ad/info/` hoặc `/file/video/ad/search/`

### Giới Hạn Tốc Độ & Hạn Ngạch (40016, 40100, 40133, 40502, 40132)

**40016/40100 - Request Quá Thường Xuyên (Cấp Ứng Dụng)**

-   **Nguyên nhân**: Đạt giới hạn tốc độ ở cấp ứng dụng developer
-   **Xử lý lỗi**: Tham khảo tài liệu về giới hạn tốc độ

**40133 - Request Quá Thường Xuyên (Cấp Advertiser)**

-   **Nguyên nhân**: Đạt giới hạn tốc độ ở cấp advertiser
-   **Ví dụ**: "Advertiser ({ID})'s QPS reaches QPS limit for current path"
-   **Xử lý lỗi**: Giảm số lượng lời gọi mỗi giây qua tài khoản advertiser

**40502 - Đạt Giới Hạn Quảng Cáo Tối Đa**

-   **Xử lý lỗi**: Tạo ad trong ad group/campaign khác hoặc xóa ad hiện có

**40132 - Giới Hạn Tốc Độ Cho Giá Trị Trường Cụ Thể**

-   **Nguyên nhân**:
    -   Sử dụng pixel_code quá thường xuyên
    -   Access token có thể bị rò rỉ và sử dụng cho tấn công độc hại
-   **Xử lý lỗi**: Thử với giá trị trường mới hoặc gửi ticket hỗ trợ

### Lỗi Xác Thực (40101-40131)

**40101 - Giá Trị Tham Số Xác Thực Không Hợp Lệ**

-   **Nguyên nhân**:
    -   Secret và app ID không khớp
    -   Authorization code không hợp lệ
-   **Xử lý lỗi**: Đảm bảo sử dụng giá trị xác thực hợp lệ

**40102 - Access Token Đã Hết Hạn**

-   **Xử lý lỗi**: Sử dụng access token hợp lệ

**40103 - Refresh Token Đã Hết Hạn**

-   **Xử lý lỗi**: Yêu cầu người dùng ủy quyền lại ứng dụng

**40104 - Access Token Trống**

-   **Xử lý lỗi**: Cung cấp access token hợp lệ

**40105 - Access Token Không Hợp Lệ**

-   **Xử lý lỗi**: Sử dụng access token hợp lệ

**40106 - Core User Không Hợp Lệ**

-   **Xử lý lỗi**: Đảm bảo sử dụng access token hợp lệ cho advertiser ID được chỉ định

**40107 - Refresh Token Không Hợp Lệ**

-   **Nguyên nhân**:
    -   Sử dụng refresh token để làm mới access token dài hạn cho Marketing API
    -   Sử dụng refresh token không hợp lệ
-   **Xử lý lỗi**: Chỉ sử dụng refresh token hợp lệ cho Accounts API hoặc Creator Marketplace API

**40108 - Loại Ủy Quyền Không Hợp Lệ**

-   **Ví dụ**: "Invalid value for grant_type: {grant_type} is not supported"
-   **Xử lý lỗi**: Sử dụng giá trị grant_type hợp lệ

**40110 - Authorization Code Không Hợp Lệ**

-   **Nguyên nhân**: Authorization code bị hủy, đã sử dụng hoặc hết hạn
-   **Ví dụ**: "The auth_code is canceled. Please re-authorize"
-   **Xử lý lỗi**: Sử dụng authorization code hợp lệ

**40112 - Mật Khẩu Không Chính Xác**

**40113 - App Bị Chặn Hoặc Không Tồn Tại**

-   **Nguyên nhân**:
    -   App ID không chính xác
    -   Secret không chính xác
-   **Ví dụ**: "The app_id is inconsistent with the token's app information"
-   **Xử lý lỗi**: Tìm App ID và Secret chính xác trong My Apps > App Detail > Basic Information

**40115 - Timestamp Xác Thực Đã Hết Hạn**

-   **Nguyên nhân**:
    -   Authorization code không hợp lệ
    -   Authentication endpoint không khớp với authorization code
-   **Xử lý lỗi**: Đảm bảo sử dụng giá trị xác thực hợp lệ

**40116 - Chữ Ký Xác Thực Không Hợp Lệ**

**40117 - Phương Thức Được Chỉ Định Không Thể Sử Dụng**

**40118 - App Hoặc Advertiser Không Có Trong Allowlist**

-   **Nguyên nhân**:
    -   Tính năng chỉ có sẵn cho advertiser ở một số quốc gia/khu vực
    -   Endpoint chỉ dành cho allowlist và chưa đăng ký

**40119 - Developer Và Advertiser Không Thuộc Cùng Công Ty**

-   **Xử lý lỗi**: Đảm bảo sử dụng access token hợp lệ cho advertiser ID được chỉ định

**40121 - Người Dùng TikTok Không Phải Creator TCM**

-   **Nguyên nhân**: Endpoint yêu cầu thông tin creator TCM
-   **Xử lý lỗi**: Cung cấp thông tin creator TCM hoặc mời creator tham gia TCM

**40122 - Creator TCM Không Ở Khu Vực Hợp Lệ**

-   **Xử lý lỗi**: Cung cấp thông tin creator TCM ở khu vực/quốc gia khác

**40124 - Hồ Sơ Developer Chưa Được Điền Và Phê Duyệt**

-   **Xử lý lỗi**: Truy cập https://ads.tiktok.com/marketing_api/developer/register để hoàn thành hồ sơ

**40125 - Developer Không Có Quyền**

-   **Nguyên nhân**:
    -   Thiếu quyền cần thiết
    -   Trường không được hỗ trợ
-   **Xử lý lỗi**: Đảm bảo có quyền cần thiết và sử dụng trường được hỗ trợ

**40131 - Lỗi Xác Thực Khi Gọi `/tt_user/oauth2/token/` hoặc `/tt_user/oauth2/refresh_token/`**

-   **Nguyên nhân**:
    -   App ID hoặc secret không chính xác
    -   Authorization code đã hết hạn
    -   Refresh token không hợp lệ hoặc hết hạn

**41001 - Interface Offline**

-   **Xử lý lỗi**: Đảm bảo đường dẫn endpoint chính xác và được hỗ trợ

**41002 - Trường Được Cung Cấp Không Sử Dụng**

-   **Xử lý lỗi**: Tham khảo tài liệu API để sử dụng tham số chính xác

### Chính Sách Quảng Cáo (40065)

**40065 - Tùy Chọn Nhắm Mục Tiêu Không Được Hỗ Trợ**

-   **Nguyên nhân**: Tùy chọn nhắm mục tiêu không được hỗ trợ cho nhóm tuổi 13-17 ở Mỹ
-   **Ví dụ**: "Based on one or more of your choices, ad delivery to US audiences will be subject to age targeting restrictions"
-   **Xử lý lỗi**:
    -   Nếu muốn nhắm mục tiêu 13-17 ở Mỹ: chỉ sử dụng các tùy chọn được hỗ trợ
    -   Nếu không: chỉ định age_groups không chứa AGE_13_17

### Lỗi Tác Vụ Bất Đồng Bộ (40200-40201)

**40200 - Lỗi Tác Vụ**

-   **Xử lý lỗi**: Tham khảo tài liệu API để sử dụng tham số chính xác

**40201 - Tác Vụ Chưa Sẵn Sàng**

-   **Xử lý lỗi**: Đợi tác vụ hoàn thành và thử lại

### Lỗi Runtime (40202)

**40202 - Xung Đột Ghi Hoặc Cập Nhật Entity**

-   **Xử lý lỗi**: Thử lại có thể giải quyết vấn đề này

### Advertisers (40300-40301)

**40300 - Advertiser Không Tồn Tại Hoặc Đã Bị Xóa**

-   **Nguyên nhân**: advertiser_id không chính xác
-   **Xử lý lỗi**: Đảm bảo advertiser ID hợp lệ

**40301 - Không Thể Khớp Advertiser**

-   **Nguyên nhân**: advertiser_id không chính xác
-   **Xử lý lỗi**: Đảm bảo advertiser ID hợp lệ

### Lỗi Dịch Vụ Nội Bộ (40700)

**40700 - Lỗi Xác Thực Dịch Vụ Nội Bộ**

-   **Xử lý lỗi**: Tham khảo tài liệu API để sử dụng tham số chính xác

### Quản Lý File (40900-40915)

**40900 - Chữ Ký Không Khớp**

-   **Nguyên nhân**: Chữ ký không chính xác cho hình ảnh, video hoặc nhạc
-   **Xử lý lỗi**: Đảm bảo chữ ký khớp với file tương ứng

**40901 - Video Đang Được Chuyển Mã**

-   **Xử lý lỗi**: Đợi quá trình chuyển mã hoàn thành

**40902 - Không Thể Lấy URL**

-   **Xử lý lỗi**: Thử lại có thể giải quyết vấn đề

**40903 - URL Hình Ảnh Không Khả Dụng**

-   **Xử lý lỗi**: Thử lại có thể giải quyết vấn đề

**40904 - Nội Dung Hình Ảnh Bất Hợp Pháp**

-   **Nguyên nhân**: Định dạng hình ảnh không được hỗ trợ
-   **Xử lý lỗi**: Đảm bảo sử dụng định dạng hình ảnh được hỗ trợ

**40905 - File Không Tồn Tại**

-   **Xử lý lỗi**: Cung cấp đường dẫn file hoặc URL chính xác

**40906 - File Trống**

-   **Xử lý lỗi**: Cung cấp đường dẫn file hoặc URL chính xác

**40907 - File Quá Lớn**

-   **Xử lý lỗi**: Giảm kích thước file xuống dưới giới hạn

**40908 - Loại File Không Được Hỗ Trợ**

-   **Xử lý lỗi**: Đảm bảo sử dụng loại file được hỗ trợ

**40909 - Loại Mã Hóa Không Được Hỗ Trợ**

-   **Xử lý lỗi**: Đảm bảo sử dụng giá trị calculate_type được hỗ trợ

**40910 - File Đã Hết Hạn**

-   **Xử lý lỗi**: Cung cấp đường dẫn file hoặc URL chính xác

**40911 - Tên Material Trùng Lặp**

-   **Xử lý lỗi**: Chỉ định tên khác cho material. Có thể sử dụng `/file/name/check/` để kiểm tra

**40912 - URL Hình Ảnh Không Khả Dụng**

-   **Xử lý lỗi**: Đảm bảo sử dụng URL hình ảnh hợp lệ từ `/file/image/ad/info/` hoặc `/file/image/ad/search/`

**40913 - Không Thể Lấy Hình Ảnh**

-   **Xử lý lỗi**: Thử lại có thể giải quyết vấn đề

**40914 - File Upload Không Hợp Lệ**

-   **Xử lý lỗi**: Đảm bảo đường dẫn file hoặc URL hợp lệ

**40915 - File Không Đáp Ứng Thông Số Kỹ Thuật**

-   **Xử lý lỗi**: Đảm bảo file đáp ứng yêu cầu cụ thể của endpoint

### Truy Cập Bị Chặn (41000)

**41000 - IP Client Trong Danh Sách Quốc Gia Bị Cấm**

### Lỗi Hệ Thống (50000+)

**50000 - Lỗi Hệ Thống**

**50002 - Lỗi Xử Lý Request Phía TikTok**

-   **Xử lý lỗi**: Xem chi tiết trong thông báo lỗi

**51305 - Lỗi Dịch Vụ Vệ Tinh**

### Bảo Trì (60001)

**60001 - Hệ Thống Đang Bảo Trì**

-   **Xử lý lỗi**: Đợi quá trình bảo trì hoàn thành và thử lại

## Thực Hành Tốt Nhất

1. **Xử Lý Lỗi**: Luôn triển khai xử lý lỗi phù hợp cho tất cả lời gọi API
2. **Giới Hạn Tốc Độ**: Theo dõi và tuân thủ giới hạn tốc độ để tránh bị throttling
3. **Xác Thực**: Giữ token an toàn và làm mới khi cần thiết
4. **Xác Thực**: Xác thực tham số trước khi thực hiện lời gọi API
5. **Tài Liệu**: Tham khảo tài liệu API chính thức để cập nhật mới nhất

## Hỗ Trợ

Đối với các vấn đề không được đề cập trong hướng dẫn này:

-   Gửi ticket trên Developer Ticket Platform
-   Kiểm tra tài liệu chính thức TikTok Marketing API
-   Xác minh cấu hình app trong TikTok Ads Manager

## Liên Kết Tham Khảo

-   [Tài liệu API chính thức](https://business-api.tiktok.com/portal/docs)
-   [Developer Ticket Platform](https://ads.tiktok.com/marketing_api/docs)
-   [Đăng ký Developer](https://ads.tiktok.com/marketing_api/developer/register)
