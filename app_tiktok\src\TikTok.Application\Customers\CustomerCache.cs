using Microsoft.Extensions.Caching.Distributed;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.Cache;
using Volo.Abp.Caching;
using Volo.Abp.ObjectMapping;

namespace TikTok.Customers
{
    public class CustomerCache : ICustomerCache, ICacheService
    {
        private readonly ICustomerRepository _repository;
        private readonly IDistributedCache<List<CustomerDto>, string> _customerListCache;
        private readonly IObjectMapper _mapper;
        private const string CustomerAllCacheKey = "Customer:All";
        public CustomerCache(
            ICustomerRepository repository,
            IDistributedCache<List<CustomerDto>, string> customerListCache,
            IObjectMapper mapper)
        {
            _repository = repository;
            _customerListCache = customerListCache;
            _mapper = mapper;
        }

        public async Task<CustomerDto?> GetByCustomerIdAsync(string customerId)
        {
            var customers = await GetAllAsync();
            var customer = customers.FirstOrDefault(x => x.CustomerId == customerId);
            return customer;
        }

        public async Task<CustomerDto?> GetByAccountNameAsync(string accountName)
        {
            var customers = await GetAllAsync();
            var customer = customers.FirstOrDefault(x => x.AccountName == accountName);
            return customer;
        }

        public async Task<CustomerDto?> GetById(Guid id)
        {
            var customers = await GetAllAsync();
            var customer = customers.FirstOrDefault(x => x.Id == id);
            return customer;
        }

        public async Task<List<CustomerDto>> GetByCustomerTypeAsync(CustomerType customerType)
        {
            var customers = await GetAllAsync();
            var filteredCustomers = customers.Where(x => x.CustomerType == customerType).ToList();
            return filteredCustomers;
        }

        public async Task CleanCache()
        {
            await _customerListCache.RemoveAsync(CustomerAllCacheKey, hideErrors: true);
        }

        // ICacheService implementation
        public string CacheName => "Customer Cache";

        public async Task<List<string>> GetCacheKeysAsync()
        {
            return new List<string> { CustomerAllCacheKey };
        }

        public async Task<bool> ClearCacheAsync()
        {
            try
            {
                await _customerListCache.RemoveAsync(CustomerAllCacheKey, hideErrors: true);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ClearCacheByKeyAsync(string cacheKey)
        {
            try
            {
                if (cacheKey == CustomerAllCacheKey)
                {
                    await _customerListCache.RemoveAsync(cacheKey, hideErrors: true);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<CacheInfoDto> GetCacheInfoAsync()
        {
            var cacheKeys = await GetCacheKeysAsync();
            return new CacheInfoDto
            {
                CacheName = CacheName,
                ActiveKeysCount = cacheKeys.Count,
                CacheKeys = cacheKeys,
                CreatedAt = DateTime.UtcNow,
                LastAccessedAt = DateTime.UtcNow,
                LastModifiedAt = DateTime.UtcNow,
                Status = "Active",
                Description = "Cache cho danh sách Customer"
            };
        }

        public async Task<List<CustomerDto>> GetAllAsync()
        {
            var cacheKey = CustomerAllCacheKey;
            var customers = await _customerListCache.GetOrAddAsync(
                cacheKey,
                async () =>
                {
                    var customerEntities = await _repository.GetListAsync();
                    return _mapper.Map<List<CustomerEntity>, List<CustomerDto>>(customerEntities);
                },
                () => new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = CacheConst.CACHE_EXPIRATION
                });
            return customers;
        }
    }
}
