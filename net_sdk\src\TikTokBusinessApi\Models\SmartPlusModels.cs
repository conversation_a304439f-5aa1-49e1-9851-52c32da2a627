/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response for getting Smart+ Campaign quota
    /// </summary>
    public class SmartPlusQuotaResponse
    {
        /// <summary>
        /// The total quota of active Smart+ Campaigns for the ad account
        /// </summary>
        [JsonPropertyName("total_quota")]
        public int TotalQuota { get; set; }

        /// <summary>
        /// The number of active Smart+ Campaigns within the ad account
        /// </summary>
        [JsonPropertyName("used_quota")]
        public int UsedQuota { get; set; }

        /// <summary>
        /// The IDs of the active Smart+ Campaigns within the ad account
        /// </summary>
        [JsonPropertyName("active_campaign_ids")]
        public List<string>? ActiveCampaignIds { get; set; }
    }

    /// <summary>
    /// Request body for creating Smart+ Campaign
    /// </summary>
    public class SmartPlusCreateBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The status of the campaign when created
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string? OperationStatus { get; set; }

        /// <summary>
        /// Advertising objective
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string ObjectiveType { get; set; } = string.Empty;

        /// <summary>
        /// The web campaign type
        /// </summary>
        [JsonPropertyName("spc_type")]
        public string? SpcType { get; set; }

        /// <summary>
        /// The status of catalog in the Smart+ Web Campaign
        /// </summary>
        [JsonPropertyName("web_all_in_one_catalog_status")]
        public string? WebAllInOneCatalogStatus { get; set; }

        /// <summary>
        /// App promotion type
        /// </summary>
        [JsonPropertyName("app_promotion_type")]
        public string? AppPromotionType { get; set; }

        /// <summary>
        /// The new objective type
        /// </summary>
        [JsonPropertyName("virtual_objective_type")]
        public string? VirtualObjectiveType { get; set; }

        /// <summary>
        /// Sales destination
        /// </summary>
        [JsonPropertyName("sales_destination")]
        public string? SalesDestination { get; set; }

        /// <summary>
        /// Campaign type
        /// </summary>
        [JsonPropertyName("campaign_type")]
        public string? CampaignType { get; set; }

        /// <summary>
        /// Whether to use app profile page to optimize delivery
        /// </summary>
        [JsonPropertyName("campaign_app_profile_page_state")]
        public string? CampaignAppProfilePageState { get; set; }

        /// <summary>
        /// Whether the campaign is an Advanced Dedicated Campaign
        /// </summary>
        [JsonPropertyName("is_advanced_dedicated_campaign")]
        public bool? IsAdvancedDedicatedCampaign { get; set; }

        /// <summary>
        /// Whether to disable SKAN attribution
        /// </summary>
        [JsonPropertyName("disable_skan_campaign")]
        public bool? DisableSkanCampaign { get; set; }

        /// <summary>
        /// Campaign name
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string CampaignName { get; set; } = string.Empty;

        /// <summary>
        /// Ad categories
        /// </summary>
        [JsonPropertyName("special_industries")]
        public List<string>? SpecialIndustries { get; set; }

        /// <summary>
        /// Product source where you want to get products for promotion
        /// </summary>
        [JsonPropertyName("product_source")]
        public string? ProductSource { get; set; }

        /// <summary>
        /// The ID of the catalog to use in the Smart+ Catalog Ads
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }

        /// <summary>
        /// The ID of the Business Center that the catalog belongs to
        /// </summary>
        [JsonPropertyName("catalog_authorized_bc_id")]
        public string? CatalogAuthorizedBcId { get; set; }

        /// <summary>
        /// Different dimensions to choose products
        /// </summary>
        [JsonPropertyName("product_specific_type")]
        public string? ProductSpecificType { get; set; }

        /// <summary>
        /// The ID of a product set
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string? ProductSetId { get; set; }

        /// <summary>
        /// The product IDs of the catalog products
        /// </summary>
        [JsonPropertyName("product_ids")]
        public List<string>? ProductIds { get; set; }

        /// <summary>
        /// Promotion type (Optimization location)
        /// </summary>
        [JsonPropertyName("promotion_type")]
        public string PromotionType { get; set; } = string.Empty;

        /// <summary>
        /// The Application ID of the promoted app
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// TikTok Instant Page type
        /// </summary>
        [JsonPropertyName("promotion_website_type")]
        public string? PromotionWebsiteType { get; set; }

        /// <summary>
        /// The optimization location for Lead Generation objective
        /// </summary>
        [JsonPropertyName("promotion_target_type")]
        public string? PromotionTargetType { get; set; }

        /// <summary>
        /// The measurable results you'd like to drive with your campaigns
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string OptimizationGoal { get; set; } = string.Empty;

        /// <summary>
        /// Pixel ID
        /// </summary>
        [JsonPropertyName("pixel_id")]
        public string? PixelId { get; set; }

        /// <summary>
        /// Conversion event for the campaign
        /// </summary>
        [JsonPropertyName("optimization_event")]
        public string OptimizationEvent { get; set; } = string.Empty;

        /// <summary>
        /// The status of deep funnel optimization
        /// </summary>
        [JsonPropertyName("deep_funnel_optimization_status")]
        public string? DeepFunnelOptimizationStatus { get; set; }

        /// <summary>
        /// The event source type
        /// </summary>
        [JsonPropertyName("deep_funnel_event_source")]
        public string? DeepFunnelEventSource { get; set; }

        /// <summary>
        /// Event Source ID
        /// </summary>
        [JsonPropertyName("deep_funnel_event_source_id")]
        public string? DeepFunnelEventSourceId { get; set; }

        /// <summary>
        /// Deep funnel optimization event
        /// </summary>
        [JsonPropertyName("deep_funnel_optimization_event")]
        public string? DeepFunnelOptimizationEvent { get; set; }

        /// <summary>
        /// Bidding strategy
        /// </summary>
        [JsonPropertyName("bid_type")]
        public string? BidType { get; set; }

        /// <summary>
        /// The target cost per click
        /// </summary>
        [JsonPropertyName("bid_price")]
        public float? BidPrice { get; set; }

        /// <summary>
        /// The target cost per conversion or cost per landing page view
        /// </summary>
        [JsonPropertyName("conversion_bid_price")]
        public float? ConversionBidPrice { get; set; }

        /// <summary>
        /// Bidding strategy for in-app events
        /// </summary>
        [JsonPropertyName("deep_bid_type")]
        public string? DeepBidType { get; set; }

        /// <summary>
        /// Target ROAS for Value-Based Optimization
        /// </summary>
        [JsonPropertyName("roas_bid")]
        public float? RoasBid { get; set; }

        /// <summary>
        /// The time window of the specified bidding strategy for VBO
        /// </summary>
        [JsonPropertyName("vbo_window")]
        public string? VboWindow { get; set; }

        /// <summary>
        /// Click-through window
        /// </summary>
        [JsonPropertyName("click_attribution_window")]
        public string? ClickAttributionWindow { get; set; }

        /// <summary>
        /// Engaged view-through window
        /// </summary>
        [JsonPropertyName("engaged_view_attribution_window")]
        public string? EngagedViewAttributionWindow { get; set; }

        /// <summary>
        /// View-through window
        /// </summary>
        [JsonPropertyName("view_attribution_window")]
        public string? ViewAttributionWindow { get; set; }

        /// <summary>
        /// Event count (Statistic type)
        /// </summary>
        [JsonPropertyName("attribution_event_count")]
        public string? AttributionEventCount { get; set; }

        /// <summary>
        /// IDs of the locations that you want to target
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string>? LocationIds { get; set; }

        /// <summary>
        /// Codes of the languages that you want to target
        /// </summary>
        [JsonPropertyName("languages")]
        public List<string>? Languages { get; set; }

        /// <summary>
        /// Gender that you want to target
        /// </summary>
        [JsonPropertyName("gender")]
        public string? Gender { get; set; }

        /// <summary>
        /// The age group that the campaign targets
        /// </summary>
        [JsonPropertyName("spc_audience_age")]
        public string? SpcAudienceAge { get; set; }

        /// <summary>
        /// Whether to exclude the group aged under eighteen
        /// </summary>
        [JsonPropertyName("exclude_age_under_eighteen")]
        public bool? ExcludeAgeUnderEighteen { get; set; }

        /// <summary>
        /// List of audience IDs to be excluded
        /// </summary>
        [JsonPropertyName("excluded_audience_ids")]
        public List<string>? ExcludedAudienceIds { get; set; }

        /// <summary>
        /// The placement strategy that decides where your ads will be shown
        /// </summary>
        [JsonPropertyName("placement_type")]
        public string? PlacementType { get; set; }

        /// <summary>
        /// The apps where you want to deliver your ads
        /// </summary>
        [JsonPropertyName("placements")]
        public List<string>? Placements { get; set; }

        /// <summary>
        /// Whether to allow comments on your ads on TikTok
        /// </summary>
        [JsonPropertyName("comment_disabled")]
        public bool? CommentDisabled { get; set; }

        /// <summary>
        /// Whether sharing to third-party platforms is disabled for ads in this ad group
        /// </summary>
        [JsonPropertyName("share_disabled")]
        public bool? ShareDisabled { get; set; }

        /// <summary>
        /// Whether users can download your video ads on TikTok
        /// </summary>
        [JsonPropertyName("video_download_disabled")]
        public bool? VideoDownloadDisabled { get; set; }

        /// <summary>
        /// Pangle app block ID list
        /// </summary>
        [JsonPropertyName("blocked_pangle_app_ids")]
        public List<string>? BlockedPangleAppIds { get; set; }

        /// <summary>
        /// Inventory filter tier for the Smart+ Campaign
        /// </summary>
        [JsonPropertyName("brand_safety_type")]
        public string? BrandSafetyType { get; set; }

        /// <summary>
        /// The ID list of content categories that you want to avoid showing the Smart+ Campaign next to
        /// </summary>
        [JsonPropertyName("category_exclusion_ids")]
        public List<string>? CategoryExclusionIds { get; set; }

        /// <summary>
        /// Budget mode
        /// </summary>
        [JsonPropertyName("budget_mode")]
        public string? BudgetMode { get; set; }

        /// <summary>
        /// Campaign budget
        /// </summary>
        [JsonPropertyName("budget")]
        public float? Budget { get; set; }

        /// <summary>
        /// Schedule type
        /// </summary>
        [JsonPropertyName("schedule_type")]
        public string? ScheduleType { get; set; }

        /// <summary>
        /// Ad delivery start time
        /// </summary>
        [JsonPropertyName("schedule_start_time")]
        public string? ScheduleStartTime { get; set; }

        /// <summary>
        /// Ad delivery end time
        /// </summary>
        [JsonPropertyName("schedule_end_time")]
        public string? ScheduleEndTime { get; set; }

        /// <summary>
        /// Ad delivery arrangement
        /// </summary>
        [JsonPropertyName("dayparting")]
        public string? Dayparting { get; set; }

        /// <summary>
        /// Whether to skip the learning phase
        /// </summary>
        [JsonPropertyName("skip_learning_phase")]
        public bool? SkipLearningPhase { get; set; }

        /// <summary>
        /// Identity ID when you are not using Spark Ads
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Identity type when you are not using Spark Ads
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// List of media information
        /// </summary>
        [JsonPropertyName("media_info_list")]
        public List<SmartPlusMediaInfoWrapper>? MediaInfoList { get; set; }

        /// <summary>
        /// Whether to enable auto-selection of creatives from your catalog
        /// </summary>
        [JsonPropertyName("catalog_creative_toggle")]
        public bool? CatalogCreativeToggle { get; set; }

        /// <summary>
        /// List of ad titles (also called ad texts)
        /// </summary>
        [JsonPropertyName("title_list")]
        public List<SmartPlusTitle>? TitleList { get; set; }

        /// <summary>
        /// The ID of the CTA portfolio that you want to use in your ads
        /// </summary>
        [JsonPropertyName("call_to_action_id")]
        public string? CallToActionId { get; set; }

        /// <summary>
        /// Call-to-action list
        /// </summary>
        [JsonPropertyName("call_to_action_list")]
        public List<SmartPlusCallToAction>? CallToActionList { get; set; }

        /// <summary>
        /// Creative portfolio ID list
        /// </summary>
        [JsonPropertyName("card_list")]
        public List<SmartPlusCard>? CardList { get; set; }

        /// <summary>
        /// Whether to enable auto-selection of interactive add-ons
        /// </summary>
        [JsonPropertyName("automatic_add_on_enabled")]
        public bool? AutomaticAddOnEnabled { get; set; }

        /// <summary>
        /// Page ID list
        /// </summary>
        [JsonPropertyName("page_list")]
        public List<SmartPlusPage>? PageList { get; set; }

        /// <summary>
        /// The specific location where you want your audience to go if they have your app installed
        /// </summary>
        [JsonPropertyName("deeplink")]
        public string? Deeplink { get; set; }

        /// <summary>
        /// The deeplink type
        /// </summary>
        [JsonPropertyName("deeplink_type")]
        public string? DeeplinkType { get; set; }

        /// <summary>
        /// Landing page URL list
        /// </summary>
        [JsonPropertyName("landing_page_urls")]
        public List<SmartPlusLandingPageUrl>? LandingPageUrls { get; set; }

        /// <summary>
        /// Default Impression Tracking URL
        /// </summary>
        [JsonPropertyName("impression_tracking_url")]
        public string? ImpressionTrackingUrl { get; set; }

        /// <summary>
        /// Click Tracking URL
        /// </summary>
        [JsonPropertyName("click_tracking_url")]
        public string? ClickTrackingUrl { get; set; }
    }

    /// <summary>
    /// Media information wrapper for Smart+ Campaign
    /// </summary>
    public class SmartPlusMediaInfoWrapper
    {
        /// <summary>
        /// Material information
        /// </summary>
        [JsonPropertyName("media_info")]
        public SmartPlusMediaInfo MediaInfo { get; set; } = new SmartPlusMediaInfo();
    }

    /// <summary>
    /// Media information for Smart+ Campaign
    /// </summary>
    public class SmartPlusMediaInfo
    {
        /// <summary>
        /// Video information
        /// </summary>
        [JsonPropertyName("video_info")]
        public SmartPlusVideoInfo? VideoInfo { get; set; }

        /// <summary>
        /// Image information
        /// </summary>
        [JsonPropertyName("image_info")]
        public List<SmartPlusImageInfo>? ImageInfo { get; set; }

        /// <summary>
        /// Music information
        /// </summary>
        [JsonPropertyName("music_info")]
        public SmartPlusMusicInfo? MusicInfo { get; set; }

        /// <summary>
        /// Whether to turn on the AIGC self-disclosure toggle
        /// </summary>
        [JsonPropertyName("aigc_disclosure_type")]
        public string? AigcDisclosureType { get; set; }

        /// <summary>
        /// The ID of the TikTok post to be used as an ad (Spark Ad)
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TikTokItemId { get; set; }

        /// <summary>
        /// Identity type for Spark Ads
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// Identity ID for Spark Ads
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }
    }

    /// <summary>
    /// Video information for Smart+ Campaign
    /// </summary>
    public class SmartPlusVideoInfo
    {
        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Video name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }
    }

    /// <summary>
    /// Image information for Smart+ Campaign
    /// </summary>
    public class SmartPlusImageInfo
    {
        /// <summary>
        /// Image ID
        /// </summary>
        [JsonPropertyName("web_uri")]
        public string WebUri { get; set; } = string.Empty;
    }

    /// <summary>
    /// Music information for Smart+ Campaign
    /// </summary>
    public class SmartPlusMusicInfo
    {
        /// <summary>
        /// The ID of the piece of music to use in the auto-generated catalog carousel ads
        /// </summary>
        [JsonPropertyName("music_id")]
        public string MusicId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Ad title for Smart+ Campaign
    /// </summary>
    public class SmartPlusTitle
    {
        /// <summary>
        /// Ad title (ad text)
        /// </summary>
        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;
    }

    /// <summary>
    /// Call-to-action for Smart+ Campaign
    /// </summary>
    public class SmartPlusCallToAction
    {
        /// <summary>
        /// Call-to-action text
        /// </summary>
        [JsonPropertyName("call_to_action")]
        public string CallToAction { get; set; } = string.Empty;
    }

    /// <summary>
    /// Creative portfolio card for Smart+ Campaign
    /// </summary>
    public class SmartPlusCard
    {
        /// <summary>
        /// Creative portfolio ID
        /// </summary>
        [JsonPropertyName("card_id")]
        public string CardId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Page for Smart+ Campaign
    /// </summary>
    public class SmartPlusPage
    {
        /// <summary>
        /// Page ID
        /// </summary>
        [JsonPropertyName("page_id")]
        public string PageId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Landing page URL for Smart+ Campaign
    /// </summary>
    public class SmartPlusLandingPageUrl
    {
        /// <summary>
        /// Landing page URL
        /// </summary>
        [JsonPropertyName("landing_page_url")]
        public string LandingPageUrl { get; set; } = string.Empty;

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// The ID of the TikTok post to be used as an ad (Spark Ad)
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TikTokItemId { get; set; }
    }

    /// <summary>
    /// Request body for updating Smart+ Campaign
    /// </summary>
    public class SmartPlusUpdateBody : SmartPlusCreateBody
    {
        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string CampaignId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for Smart+ Campaign operations
    /// </summary>
    public class SmartPlusResponse
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// The status of the campaign
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string? OperationStatus { get; set; }

        /// <summary>
        /// Campaign status (Secondary status)
        /// </summary>
        [JsonPropertyName("campaign_secondary_status")]
        public string? CampaignSecondaryStatus { get; set; }

        /// <summary>
        /// Whether the campaign is an automated campaign type
        /// </summary>
        [JsonPropertyName("is_smart_performance_campaign")]
        public bool? IsSmartPerformanceCampaign { get; set; }

        /// <summary>
        /// Advertising objective
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string? ObjectiveType { get; set; }

        /// <summary>
        /// The web campaign type
        /// </summary>
        [JsonPropertyName("spc_type")]
        public string? SpcType { get; set; }

        /// <summary>
        /// The status of catalog in the Smart+ Web Campaign
        /// </summary>
        [JsonPropertyName("web_all_in_one_catalog_status")]
        public string? WebAllInOneCatalogStatus { get; set; }

        /// <summary>
        /// App promotion type
        /// </summary>
        [JsonPropertyName("app_promotion_type")]
        public string? AppPromotionType { get; set; }

        /// <summary>
        /// The new objective type
        /// </summary>
        [JsonPropertyName("virtual_objective_type")]
        public string? VirtualObjectiveType { get; set; }

        /// <summary>
        /// Sales destination
        /// </summary>
        [JsonPropertyName("sales_destination")]
        public string? SalesDestination { get; set; }

        /// <summary>
        /// Campaign Type
        /// </summary>
        [JsonPropertyName("campaign_type")]
        public string? CampaignType { get; set; }

        /// <summary>
        /// Indicates the status of the App Profile Page
        /// </summary>
        [JsonPropertyName("campaign_app_profile_page_state")]
        public string? CampaignAppProfilePageState { get; set; }

        /// <summary>
        /// Whether the campaign is an Advanced Dedicated Campaign
        /// </summary>
        [JsonPropertyName("is_advanced_dedicated_campaign")]
        public bool? IsAdvancedDedicatedCampaign { get; set; }

        /// <summary>
        /// Whether to disable SKAN attribution
        /// </summary>
        [JsonPropertyName("disable_skan_campaign")]
        public bool? DisableSkanCampaign { get; set; }

        /// <summary>
        /// The attribution type for the Dedicated Campaign
        /// </summary>
        [JsonPropertyName("bid_align_type")]
        public string? BidAlignType { get; set; }

        /// <summary>
        /// Campaign name
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// Ad categories
        /// </summary>
        [JsonPropertyName("special_industries")]
        public List<string>? SpecialIndustries { get; set; }

        /// <summary>
        /// Ad group status (Secondary status)
        /// </summary>
        [JsonPropertyName("adgroup_secondary_status")]
        public string? AdGroupSecondaryStatus { get; set; }

        /// <summary>
        /// Product source where you want to get products for promotion
        /// </summary>
        [JsonPropertyName("product_source")]
        public string? ProductSource { get; set; }

        /// <summary>
        /// The ID of the catalog to use in the Smart+ Catalog Ads
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }

        /// <summary>
        /// The ID of the Business Center that the catalog belongs to
        /// </summary>
        [JsonPropertyName("catalog_authorized_bc_id")]
        public string? CatalogAuthorizedBcId { get; set; }

        /// <summary>
        /// Different dimensions to choose products
        /// </summary>
        [JsonPropertyName("product_specific_type")]
        public string? ProductSpecificType { get; set; }

        /// <summary>
        /// The ID of a product set
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string? ProductSetId { get; set; }

        /// <summary>
        /// The product IDs of the catalog products
        /// </summary>
        [JsonPropertyName("product_ids")]
        public List<string>? ProductIds { get; set; }

        /// <summary>
        /// Promotion type (Optimization location)
        /// </summary>
        [JsonPropertyName("promotion_type")]
        public string? PromotionType { get; set; }

        /// <summary>
        /// The App ID of the promoted app
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// The type of the promoted app
        /// </summary>
        [JsonPropertyName("app_type")]
        public string? AppType { get; set; }

        /// <summary>
        /// TikTok Instant Page type
        /// </summary>
        [JsonPropertyName("promotion_website_type")]
        public string? PromotionWebsiteType { get; set; }

        /// <summary>
        /// The optimization location for Lead Generation objective
        /// </summary>
        [JsonPropertyName("promotion_target_type")]
        public string? PromotionTargetType { get; set; }

        /// <summary>
        /// The measurable results that you'd like to drive your ads with
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string? OptimizationGoal { get; set; }

        /// <summary>
        /// Pixel ID
        /// </summary>
        [JsonPropertyName("pixel_id")]
        public string? PixelId { get; set; }

        /// <summary>
        /// Conversion event for the ad group
        /// </summary>
        [JsonPropertyName("optimization_event")]
        public string? OptimizationEvent { get; set; }
    }
}
