/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Get Filters Request/Response Models

    /// <summary>
    /// Request model for getting catalog insight filters
    /// </summary>
    public class CatalogInsightFiltersRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        [Required]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID. The catalog needs to be an E-commerce catalog that contains at least 20 products.
        /// </summary>
        [JsonPropertyName("catalog_id")]
        [Required]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// The type of filter options. Enum values: CATEGORY_ID, BRAND, AVAILABILITY
        /// </summary>
        [JsonPropertyName("filter_type")]
        [Required]
        public string FilterType { get; set; } = string.Empty;

        /// <summary>
        /// Current page number. Default value: 1. Value range: ≥ 1.
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10. Value range: 1-200.
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogInsightFiltersRequest class
        /// </summary>
        public CatalogInsightFiltersRequest()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogInsightFiltersRequest(BcId={BcId}, CatalogId={CatalogId}, FilterType={FilterType})";
        }
    }

    /// <summary>
    /// Response model for catalog insight filters
    /// </summary>
    public class CatalogInsightFiltersResponse
    {
        /// <summary>
        /// Brand names by which you can filter products within the catalog (returned when filter_type is BRAND)
        /// </summary>
        [JsonPropertyName("brands")]
        public List<string>? Brands { get; set; }

        /// <summary>
        /// Availability statuses by which you can filter products within the catalog (returned when filter_type is AVAILABILITY)
        /// </summary>
        [JsonPropertyName("availabilities")]
        public List<string>? Availabilities { get; set; }

        /// <summary>
        /// Category information by which you can filter products within the catalog (returned when filter_type is CATEGORY_ID)
        /// </summary>
        [JsonPropertyName("categories")]
        public List<CategoryInfo>? Categories { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogInsightFiltersResponse class
        /// </summary>
        public CatalogInsightFiltersResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogInsightFiltersResponse(Brands={Brands?.Count ?? 0} items, Availabilities={Availabilities?.Count ?? 0} items, Categories={Categories?.Count ?? 0} items)";
        }
    }

    #endregion

    #region Get Trending Products Request/Response Models

    /// <summary>
    /// Request model for getting trending catalog products
    /// </summary>
    public class CatalogInsightProductsRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        [Required]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID. The catalog needs to be an E-commerce catalog that contains at least 20 products.
        /// </summary>
        [JsonPropertyName("catalog_id")]
        [Required]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public CatalogInsightProductFiltering? Filtering { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogInsightProductsRequest class
        /// </summary>
        public CatalogInsightProductsRequest()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogInsightProductsRequest(BcId={BcId}, CatalogId={CatalogId}, Filtering={Filtering})";
        }
    }

    /// <summary>
    /// Filtering conditions for catalog insight products
    /// </summary>
    public class CatalogInsightProductFiltering
    {
        /// <summary>
        /// A list of TikTok product category IDs to filter the results by. Max size: 50.
        /// </summary>
        [JsonPropertyName("category_ids")]
        public List<string>? CategoryIds { get; set; }

        /// <summary>
        /// A list of product brand names to filter the results by. Max size: 50.
        /// </summary>
        [JsonPropertyName("brands")]
        public List<string>? Brands { get; set; }

        /// <summary>
        /// Product availability statuses to filter the results by.
        /// Enum values: IN_STOCK, AVAILABLE_FOR_ORDER, PREORDER, OUT_OF_STOCK, DISCONTINUED
        /// </summary>
        [JsonPropertyName("availabilities")]
        public List<string>? Availabilities { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogInsightProductFiltering class
        /// </summary>
        public CatalogInsightProductFiltering()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogInsightProductFiltering(CategoryIds={CategoryIds?.Count ?? 0} items, Brands={Brands?.Count ?? 0} items, Availabilities={Availabilities?.Count ?? 0} items)";
        }
    }

    /// <summary>
    /// Response model for trending catalog products
    /// </summary>
    public class CatalogInsightProductsResponse
    {
        /// <summary>
        /// The list of up to 50 trending products within the E-commerce catalog, sorted in descending order by popularity
        /// </summary>
        [JsonPropertyName("product_insights")]
        public List<ProductInsight>? ProductInsights { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogInsightProductsResponse class
        /// </summary>
        public CatalogInsightProductsResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogInsightProductsResponse(ProductInsights={ProductInsights?.Count ?? 0} items)";
        }
    }

    #endregion

    #region Get Trending Categories Request/Response Models

    /// <summary>
    /// Request model for getting trending catalog product categories
    /// </summary>
    public class CatalogInsightCategoriesRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        [Required]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID. The catalog needs to be an E-commerce catalog that contains at least 20 products.
        /// </summary>
        [JsonPropertyName("catalog_id")]
        [Required]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public CatalogInsightCategoryFiltering? Filtering { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogInsightCategoriesRequest class
        /// </summary>
        public CatalogInsightCategoriesRequest()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogInsightCategoriesRequest(BcId={BcId}, CatalogId={CatalogId}, Filtering={Filtering})";
        }
    }

    /// <summary>
    /// Filtering conditions for catalog insight categories
    /// </summary>
    public class CatalogInsightCategoryFiltering
    {
        /// <summary>
        /// A list of product category IDs to filter the results by. Max size: 50.
        /// When filtering is specified, this field is required.
        /// </summary>
        [JsonPropertyName("category_ids")]
        public List<string>? CategoryIds { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogInsightCategoryFiltering class
        /// </summary>
        public CatalogInsightCategoryFiltering()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogInsightCategoryFiltering(CategoryIds={CategoryIds?.Count ?? 0} items)";
        }
    }

    /// <summary>
    /// Response model for trending catalog product categories
    /// </summary>
    public class CatalogInsightCategoriesResponse
    {
        /// <summary>
        /// The list of the top 50 trending product categories on TikTok, sorted in descending order by popularity
        /// </summary>
        [JsonPropertyName("category_insights")]
        public List<CategoryInsight>? CategoryInsights { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogInsightCategoriesResponse class
        /// </summary>
        public CatalogInsightCategoriesResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogInsightCategoriesResponse(CategoryInsights={CategoryInsights?.Count ?? 0} items)";
        }
    }

    #endregion

    #region Supporting Models

    /// <summary>
    /// Category information for filtering
    /// </summary>
    public class CategoryInfo
    {
        /// <summary>
        /// The TikTok product category ID assigned to the product, consisting of three levels separated by the number sign (#)
        /// </summary>
        [JsonPropertyName("category_id")]
        public string? CategoryId { get; set; }

        /// <summary>
        /// Details of the levels that the category belongs to
        /// </summary>
        [JsonPropertyName("level_info")]
        public LevelInfo? LevelInfo { get; set; }

        /// <summary>
        /// Initializes a new instance of the CategoryInfo class
        /// </summary>
        public CategoryInfo()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CategoryInfo(CategoryId={CategoryId}, LevelInfo={LevelInfo})";
        }
    }

    /// <summary>
    /// Level information for product categories
    /// </summary>
    public class LevelInfo
    {
        /// <summary>
        /// The ID of the level-1 TikTok product category
        /// </summary>
        [JsonPropertyName("level_id_1")]
        public string? LevelId1 { get; set; }

        /// <summary>
        /// The name of the level-1 TikTok product category
        /// </summary>
        [JsonPropertyName("level_name_1")]
        public string? LevelName1 { get; set; }

        /// <summary>
        /// The ID of the level-2 TikTok product category
        /// </summary>
        [JsonPropertyName("level_id_2")]
        public string? LevelId2 { get; set; }

        /// <summary>
        /// The name of the level-2 TikTok product category
        /// </summary>
        [JsonPropertyName("level_name_2")]
        public string? LevelName2 { get; set; }

        /// <summary>
        /// The ID of the level-3 TikTok product category
        /// </summary>
        [JsonPropertyName("level_id_3")]
        public string? LevelId3 { get; set; }

        /// <summary>
        /// The name of the level-3 TikTok product category
        /// </summary>
        [JsonPropertyName("level_name_3")]
        public string? LevelName3 { get; set; }

        /// <summary>
        /// Initializes a new instance of the LevelInfo class
        /// </summary>
        public LevelInfo()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"LevelInfo(Level1={LevelName1}, Level2={LevelName2}, Level3={LevelName3})";
        }
    }

    /// <summary>
    /// Product insight information
    /// </summary>
    public class ProductInsight
    {
        /// <summary>
        /// The system-generated product ID
        /// </summary>
        [JsonPropertyName("product_id")]
        public string? ProductId { get; set; }

        /// <summary>
        /// The URL for the product image
        /// </summary>
        [JsonPropertyName("image_url")]
        public string? ImageUrl { get; set; }

        /// <summary>
        /// The title of the product
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// A short description of the product
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Advertiser-defined unique SKU ID for the Product
        /// </summary>
        [JsonPropertyName("sku_id")]
        public string? SkuId { get; set; }

        /// <summary>
        /// Information about the TikTok product category assigned to the product
        /// </summary>
        [JsonPropertyName("category_info")]
        public CategoryInfo? CategoryInfo { get; set; }

        /// <summary>
        /// Brand name for the product
        /// </summary>
        [JsonPropertyName("brand")]
        public string? Brand { get; set; }

        /// <summary>
        /// Price information
        /// </summary>
        [JsonPropertyName("price")]
        public PriceInfo? Price { get; set; }

        /// <summary>
        /// The current availability of the product in your store
        /// </summary>
        [JsonPropertyName("availability")]
        public string? Availability { get; set; }

        /// <summary>
        /// Initializes a new instance of the ProductInsight class
        /// </summary>
        public ProductInsight()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductInsight(ProductId={ProductId}, Title={Title}, Brand={Brand})";
        }
    }

    /// <summary>
    /// Price information for products
    /// </summary>
    public class PriceInfo
    {
        /// <summary>
        /// The base price of the product
        /// </summary>
        [JsonPropertyName("price")]
        public float? Price { get; set; }

        /// <summary>
        /// Unit of currency
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// The discounted price of the product if it's on sale
        /// </summary>
        [JsonPropertyName("sale_price")]
        public float? SalePrice { get; set; }

        /// <summary>
        /// The start and end date and time of sale
        /// </summary>
        [JsonPropertyName("sale_price_effective_date")]
        public List<string>? SalePriceEffectiveDate { get; set; }

        /// <summary>
        /// Initializes a new instance of the PriceInfo class
        /// </summary>
        public PriceInfo()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"PriceInfo(Price={Price}, Currency={Currency}, SalePrice={SalePrice})";
        }
    }

    /// <summary>
    /// Category insight information
    /// </summary>
    public class CategoryInsight
    {
        /// <summary>
        /// The TikTok product category ID assigned to the product, consisting of three levels separated by the number sign (#)
        /// </summary>
        [JsonPropertyName("category_id")]
        public string? CategoryId { get; set; }

        /// <summary>
        /// Details of the levels that the category belongs to
        /// </summary>
        [JsonPropertyName("level_info")]
        public LevelInfo? LevelInfo { get; set; }

        /// <summary>
        /// The number of products in the catalog that match the product category
        /// </summary>
        [JsonPropertyName("total_product_count")]
        public int? TotalProductCount { get; set; }

        /// <summary>
        /// The total percentage of matching products in this category that are currently in stock in the catalog
        /// </summary>
        [JsonPropertyName("product_availability_rate")]
        public float? ProductAvailabilityRate { get; set; }

        /// <summary>
        /// Initializes a new instance of the CategoryInsight class
        /// </summary>
        public CategoryInsight()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CategoryInsight(CategoryId={CategoryId}, TotalProductCount={TotalProductCount}, AvailabilityRate={ProductAvailabilityRate})";
        }
    }

    #endregion
}
