/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Ad Comment operations
    /// </summary>
    public class AdCommentApi : IAdCommentApi
    {
        private readonly IApiClient _apiClient;

        /// <summary>
        /// Initializes a new instance of the AdCommentApi class
        /// </summary>
        /// <param name="apiClient">API client for making requests</param>
        public AdCommentApi(IApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        }

        /// <inheritdoc />
        public async Task<AdCommentListResponse> GetCommentsAsync(
            AdCommentListRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.SearchField))
                throw new ArgumentException("Search field cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.SearchValue))
                throw new ArgumentException("Search value cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.StartTime))
                throw new ArgumentException("Start time cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.EndTime))
                throw new ArgumentException("End time cannot be null or empty", nameof(request));

            return await _apiClient.CallApiAsync<AdCommentListResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.GetComments}",
                HttpMethod.Get,
                queryParams: ConvertToQueryParams(request),
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<AdCommentListResponse> GetRelatedCommentsAsync(
            AdCommentReferenceRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.CommentId))
                throw new ArgumentException("Comment ID cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.CommentType))
                throw new ArgumentException("Comment type cannot be null or empty", nameof(request));

            return await _apiClient.CallApiAsync<AdCommentListResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.GetRelatedComments}",
                HttpMethod.Get,
                queryParams: ConvertToQueryParams(request),
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <inheritdoc />
        // public async Task UpdateCommentStatusAsync(
        //     AdCommentStatusUpdateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.CommentIds == null || request.CommentIds.Count == 0)
        //         throw new ArgumentException("At least one comment ID must be provided", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.Operation))
        //         throw new ArgumentException("Operation cannot be null or empty", nameof(request));

        //     await _apiClient.CallApiAsync<object>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.UpdateCommentStatus}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <inheritdoc />
        // public async Task<AdCommentReplyResponse> ReplyToCommentAsync(
        //     AdCommentReplyRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.AdId))
        //         throw new ArgumentException("Ad ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.TikTokItemId))
        //         throw new ArgumentException("TikTok item ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.CommentId))
        //         throw new ArgumentException("Comment ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.Text))
        //         throw new ArgumentException("Text cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.IdentityType))
        //         throw new ArgumentException("Identity type cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.IdentityId))
        //         throw new ArgumentException("Identity ID cannot be null or empty", nameof(request));

        //     return await _apiClient.CallApiAsync<AdCommentReplyResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.ReplyToComment}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <inheritdoc />
        // public async Task DeleteCommentAsync(
        //     AdCommentDeleteRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.AdId))
        //         throw new ArgumentException("Ad ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.TikTokItemId))
        //         throw new ArgumentException("TikTok item ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.CommentId))
        //         throw new ArgumentException("Comment ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.IdentityType))
        //         throw new ArgumentException("Identity type cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.IdentityId))
        //         throw new ArgumentException("Identity ID cannot be null or empty", nameof(request));

        //     await _apiClient.CallApiAsync<object>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.DeleteComment}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task<AdCommentExportTaskResponse> CreateCommentExportTaskAsync(
        //     AdCommentExportTaskRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     return await _apiClient.CallApiAsync<AdCommentExportTaskResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.CreateCommentExportTask}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        public async Task<AdCommentExportTaskStatusResponse> GetCommentExportTaskStatusAsync(
            AdCommentExportTaskStatusRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.TaskId))
                throw new ArgumentException("Task ID cannot be null or empty", nameof(request));

            return await _apiClient.CallApiAsync<AdCommentExportTaskStatusResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.CheckCommentExportTaskStatus}",
                HttpMethod.Get,
                queryParams: ConvertToQueryParams(request),
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<AdCommentExportDownloadResponse> DownloadCommentExportAsync(
            AdCommentExportTaskStatusRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.TaskId))
                throw new ArgumentException("Task ID cannot be null or empty", nameof(request));

            return await _apiClient.CallApiAsync<AdCommentExportDownloadResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.DownloadCommentExport}",
                HttpMethod.Get,
                queryParams: ConvertToQueryParams(request),
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        // public async Task CreateBlockedWordsAsync(
        //     AdCommentBlockedWordsCreateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.BlockedWords == null || request.BlockedWords.Count == 0)
        //         throw new ArgumentException("At least one blocked word must be provided", nameof(request));

        //     await _apiClient.CallApiAsync<object>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.CreateBlockedWords}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task UpdateBlockedWordAsync(
        //     AdCommentBlockedWordUpdateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.OldWord))
        //         throw new ArgumentException("Old word cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.NewWord))
        //         throw new ArgumentException("New word cannot be null or empty", nameof(request));

        //     await _apiClient.CallApiAsync<object>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.UpdateBlockedWord}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task<AdCommentBlockedWordsCheckResponse> CheckBlockedWordsAsync(
        //     AdCommentBlockedWordsCheckRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.BlockedWords == null || request.BlockedWords.Count == 0)
        //         throw new ArgumentException("At least one blocked word must be provided", nameof(request));

        //     return await _apiClient.CallApiAsync<AdCommentBlockedWordsCheckResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.CheckBlockedWords}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        public async Task<AdCommentBlockedWordsListResponse> GetBlockedWordsAsync(
            AdCommentBlockedWordsGetRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            return await _apiClient.CallApiAsync<AdCommentBlockedWordsListResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.GetBlockedWords}",
                HttpMethod.Get,
                queryParams: ConvertToQueryParams(request),
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        // public async Task DeleteBlockedWordsAsync(
        //     AdCommentBlockedWordsDeleteRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.BlockedWords == null || request.BlockedWords.Count == 0)
        //         throw new ArgumentException("At least one blocked word must be provided", nameof(request));

        //     await _apiClient.CallApiAsync<object>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.DeleteBlockedWords}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task<AdCommentBlockedWordExportTaskResponse> CreateBlockedWordExportTaskAsync(
        //     AdCommentBlockedWordExportTaskRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     return await _apiClient.CallApiAsync<AdCommentBlockedWordExportTaskResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.CreateBlockedWordExportTask}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        public async Task<AdCommentBlockedWordExportTaskStatusResponse> GetBlockedWordExportTaskStatusAsync(
            AdCommentBlockedWordExportTaskStatusRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.TaskId))
                throw new ArgumentException("Task ID cannot be null or empty", nameof(request));

            return await _apiClient.CallApiAsync<AdCommentBlockedWordExportTaskStatusResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.CheckBlockedWordExportTaskStatus}",
                HttpMethod.Get,
                queryParams: ConvertToQueryParams(request),
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<AdCommentBlockedWordExportDownloadResponse> DownloadBlockedWordExportAsync(
            AdCommentBlockedWordExportTaskStatusRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.TaskId))
                throw new ArgumentException("Task ID cannot be null or empty", nameof(request));

            return await _apiClient.CallApiAsync<AdCommentBlockedWordExportDownloadResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdComment.DownloadBlockedWordExport}",
                HttpMethod.Get,
                queryParams: ConvertToQueryParams(request),
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Converts request object to query parameters dictionary
        /// </summary>
        /// <param name="request">Request object</param>
        /// <returns>Query parameters dictionary</returns>
        private static Dictionary<string, string> ConvertToQueryParams(object request)
        {
            var queryParams = new Dictionary<string, string>();
            var properties = request.GetType().GetProperties();

            foreach (var property in properties)
            {
                var value = property.GetValue(request);
                if (value != null)
                {
                    var jsonPropertyName = GetJsonPropertyName(property);
                    if (!string.IsNullOrEmpty(jsonPropertyName))
                    {
                        if (value is System.Collections.IEnumerable enumerable && !(value is string))
                        {
                            queryParams[jsonPropertyName] = System.Text.Json.JsonSerializer.Serialize(value);
                        }
                        else
                        {
                            queryParams[jsonPropertyName] = value.ToString() ?? string.Empty;
                        }
                    }
                }
            }

            return queryParams;
        }

        /// <summary>
        /// Gets the JSON property name from JsonPropertyName attribute
        /// </summary>
        /// <param name="property">Property info</param>
        /// <returns>JSON property name</returns>
        private static string GetJsonPropertyName(System.Reflection.PropertyInfo property)
        {
            var jsonPropertyAttribute = property.GetCustomAttribute<System.Text.Json.Serialization.JsonPropertyNameAttribute>();
            return jsonPropertyAttribute?.Name ?? property.Name;
        }
    }
}
