# EtlFlowStatusEntity

## Thông tin bảng dữ liệu

-   **Table**: `EtlFlowStatuses`
-   **TablePrefix**: `App`
-   **TableName**: `AppEtlFlowStatuses`
-   **DbSchema**: `null`

## Mô tả

Entity quản lý trạng thái ETL NIFI flow để đảm bảo ETL dữ liệu chính xác, không dư thừa lặp lại. Lưu trữ thông tin về trạng thái hoạt động, thời gian xử lý cuối cùng, và kết quả xử lý của các flow ETL.

## Bảng dữ liệu: EtlFlowStatus

| Tên Field                | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                                   |
| ------------------------ | ------------ | -------- | ------ | ------------------------------------------------------- |
| Id                       | Guid         | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                   |
| Name                     | string       | ✅       | 255    | Tên Flow ETL                                            |
| LastDateTimeProcessed    | DateTime     | ✅       | -      | Thời gian xử lý cuối cùng của Flow                      |
| IsActive                 | bool         | ✅       | -      | Trạng thái hoạt động của Flow                           |
| Description              | string       | ❌       | 1000   | Mô tả bổ sung về Flow                                   |
| LastProcessedRecordCount | int          | ❌       | -      | Số lượng bản ghi đã xử lý trong lần chạy cuối           |
| LastProcessingStartTime  | DateTime     | ❌       | -      | Thời gian bắt đầu xử lý lần cuối                        |
| LastProcessingEndTime    | DateTime     | ❌       | -      | Thời gian kết thúc xử lý lần cuối                       |
| LastProcessingStatus     | string       | ❌       | 50     | Trạng thái xử lý lần cuối (Success, Failed, InProgress) |
| LastErrorMessage         | string       | ❌       | 2000   | Thông báo lỗi nếu có                                    |
| CreationTime             | DateTime     | ✅       | -      | Thời gian tạo bản ghi (Audit)                           |
| CreatorId                | Guid?        | ❌       | -      | ID người tạo (Audit)                                    |
| LastModificationTime     | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (Audit)                          |
| LastModifierId           | Guid?        | ❌       | -      | ID người sửa đổi cuối (Audit)                           |

## Cấu trúc dữ liệu

### Thông tin định danh

-   **Id**: ID duy nhất của bản ghi trạng thái flow
-   **Name**: Tên của Flow ETL để phân biệt các flow khác nhau
-   **IsActive**: Trạng thái hoạt động của flow (true = đang hoạt động, false = tạm dừng)

### Thông tin xử lý

-   **LastDateTimeProcessed**: Thời gian xử lý cuối cùng của Flow (UTC)
-   **LastProcessingStartTime**: Thời gian bắt đầu xử lý lần cuối
-   **LastProcessingEndTime**: Thời gian kết thúc xử lý lần cuối
-   **LastProcessedRecordCount**: Số lượng bản ghi đã xử lý trong lần chạy cuối

### Trạng thái và lỗi

-   **LastProcessingStatus**: Trạng thái xử lý lần cuối (Success, Failed, InProgress)
-   **LastErrorMessage**: Thông báo lỗi chi tiết nếu xử lý thất bại

### Thông tin bổ sung

-   **Description**: Mô tả chi tiết về Flow ETL, mục đích và cách thức hoạt động

## Mục đích sử dụng

-   Quản lý trạng thái hoạt động của các Flow ETL
-   Theo dõi thời gian xử lý cuối cùng để tránh xử lý dư thừa
-   Giám sát hiệu suất xử lý (số bản ghi, thời gian xử lý)
-   Ghi nhận và theo dõi lỗi xử lý
-   Đảm bảo tính nhất quán và chính xác của dữ liệu ETL
-   Hỗ trợ việc khôi phục và xử lý lại dữ liệu khi cần thiết

## Mối quan hệ

-   **RawAdAccountEntity**: Flow ETL có thể xử lý dữ liệu từ tài khoản quảng cáo
-   **RawBusinessCenterEntity**: Flow ETL có thể xử lý dữ liệu từ Business Center
-   **RawCampaignEntity**: Flow ETL có thể xử lý dữ liệu chiến dịch
-   **RawTransactionEntity**: Flow ETL có thể xử lý dữ liệu giao dịch

## Lưu ý

-   Entity này kế thừa từ Entity<Guid> để có ID duy nhất
-   LastDateTimeProcessed được sử dụng để đảm bảo không xử lý lại dữ liệu đã được xử lý
-   IsActive cho phép tạm dừng/hủy kích hoạt flow mà không cần xóa bản ghi
-   LastProcessingStatus giúp theo dõi trạng thái xử lý và phát hiện lỗi
-   LastErrorMessage lưu trữ thông tin lỗi chi tiết để hỗ trợ debug
-   Tất cả thời gian được lưu trữ dưới dạng UTC để đảm bảo tính nhất quán
-   Description giúp ghi chú và mô tả chi tiết về mục đích của từng Flow ETL
