using System;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;

namespace TikTok.AdAccounts
{
    /// <summary>
    /// DTO để tạo mới tài khoản quảng cáo
    /// </summary>
    public class CreateAdAccountDto
    {
        /// <summary>
        /// ID duy nhất của tài khoản quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID của Business Center sở hữu tài khoản
        /// </summary>
        [Required]
        [StringLength(100)]
        public string OwnerBcId { get; set; }

        /// <summary>
        /// Trạng thái tài khoản
        /// </summary>
        [Required]
        public AdAccountStatus Status { get; set; }

        /// <summary>
        /// Vai trò tài khoản
        /// </summary>
        [Required]
        public SupporterRole Role { get; set; }

        /// <summary>
        /// L<PERSON> do bị từ chối (nếu có)
        /// </summary>
        [StringLength(500)]
        public string RejectionReason { get; set; }

        /// <summary>
        /// Tên tài khoản quảng cáo
        /// </summary>
        [Required]
        [StringLength(255)]
        public string Name { get; set; }

        /// <summary>
        /// Múi giờ với GMT offset hoặc định dạng "Region/City"
        /// </summary>
        [StringLength(50)]
        public string? Timezone { get; set; }

        /// <summary>
        /// Tên múi giờ hiển thị theo định dạng "Region/City"
        /// </summary>
        [StringLength(100)]
        public string? DisplayTimezone { get; set; }

        /// <summary>
        /// Tên công ty của tài khoản
        /// </summary>
        [Required]
        [StringLength(255)]
        public string Company { get; set; }

        /// <summary>
        /// Có thể chỉnh sửa tên công ty qua API hay không
        /// </summary>
        public bool CompanyNameEditable { get; set; }

        /// <summary>
        /// Mã danh mục ngành nghề
        /// </summary>
        [StringLength(50)]
        public string Industry { get; set; }

        /// <summary>
        /// Địa chỉ tài khoản
        /// </summary>
        [StringLength(500)]
        public string Address { get; set; }

        /// <summary>
        /// Mã quốc gia đăng ký (ví dụ: US, CN)
        /// </summary>
        [StringLength(10)]
        public string? Country { get; set; }

        /// <summary>
        /// Loại tài khoản
        /// </summary>
        [Required]
        public AdAccountType AdvertiserAccountType { get; set; }

        /// <summary>
        /// Loại tiền tệ theo mã ISO 4217 (ví dụ: USD, EUR)
        /// </summary>
        [StringLength(10)]
        public string? Currency { get; set; }

        /// <summary>
        /// Tên người liên hệ (đã che)
        /// </summary>
        [StringLength(255)]
        public string Contacter { get; set; }

        /// <summary>
        /// Email liên hệ (đã che)
        /// </summary>
        [StringLength(255)]
        [EmailAddress]
        public string Email { get; set; }

        /// <summary>
        /// Số điện thoại di động (đã che)
        /// </summary>
        [StringLength(20)]
        public string CellphoneNumber { get; set; }

        /// <summary>
        /// Số điện thoại cố định (đã che)
        /// </summary>
        [StringLength(20)]
        public string TelephoneNumber { get; set; }

        /// <summary>
        /// Mã ngôn ngữ sử dụng (ví dụ: en, zh)
        /// </summary>
        [StringLength(10)]
        public string? Language { get; set; }

        /// <summary>
        /// Số giấy phép kinh doanh
        /// </summary>
        [StringLength(100)]
        public string LicenseNo { get; set; }

        /// <summary>
        /// URL xem trước giấy phép (có hiệu lực 1 giờ)
        /// </summary>
        [StringLength(500)]
        public string LicenseUrl { get; set; }

        /// <summary>
        /// Mô tả thương hiệu/công ty
        /// </summary>
        [StringLength(1000)]
        public string Description { get; set; }

        /// <summary>
        /// Số dư khả dụng của tài khoản
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// Thời gian tạo tài khoản (Unix timestamp)
        /// </summary>
        public DateTime CreateTime { get; set; }
    }
} 