/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;
using TikTokBusinessApi.Serialization;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API GMV Max operations
    /// </summary>
    public class GMVMaxApi : IGMVMaxApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<GMVMaxApi>? _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// Initializes a new instance of the GMVMaxApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public GMVMaxApi(IApiClient apiClient, ILogger<GMVMaxApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
            _jsonOptions = JsonSerializerOptionsExtensions.CreateDefault();

        }

        /// <summary>
        /// Retrieve the GMV Max Campaigns within an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="filtering">Filters on the data</param>
        /// <param name="fields">Fields that you want to get</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing GMV Max campaigns</returns>
        public async Task<TikTokApiResponse<GMVMaxCampaignGetResponse>> GetCampaignsAsync(
            string advertiserId,
            GMVMaxCampaignGetFiltering filtering,
            List<string>? fields = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (filtering == null)
                throw new ArgumentNullException(nameof(filtering));

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["filtering"] = JsonSerializer.Serialize(filtering, _jsonOptions),
                ["page"] = page.ToString(),
                ["page_size"] = pageSize.ToString()
            };

            if (fields != null && fields.Count > 0)
            {
                queryParams["fields"] = string.Join(",", fields);
            }

            _logger?.LogDebug("Getting GMV Max campaigns for advertiser {AdvertiserId}", advertiserId);

            return await _apiClient.CallApiAsync<TikTokApiResponse<GMVMaxCampaignGetResponse>>(
                GMVMaxEndpoints.GetCampaigns,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Retrieve the details of a GMV Max Campaign
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="campaignId">The ID of a GMV Max Campaign</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing GMV Max campaign details</returns>
        public async Task<TikTokApiResponse<GMVMaxCampaignInfo>> GetCampaignInfoAsync(
            string advertiserId,
            string campaignId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrWhiteSpace(campaignId))
                throw new ArgumentException("Campaign ID cannot be null or empty", nameof(campaignId));

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["campaign_id"] = campaignId
            };

            _logger?.LogDebug("Getting GMV Max campaign info for campaign {CampaignId}", campaignId);

            return await _apiClient.CallApiAsync<TikTokApiResponse<GMVMaxCampaignInfo>>(
                GMVMaxEndpoints.GetCampaignInfo,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Create a GMV Max Campaign
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing campaign details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created campaign details</returns>
        //public async Task<Response<GMVMaxCampaignInfo>> CreateCampaignAsync(
        //    string accessToken,
        //    GMVMaxCampaignCreateBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    _logger?.LogDebug("Creating GMV Max campaign for advertiser {AdvertiserId}", body.AdvertiserId);

        //    return await _apiClient.CallApiAsync<Response<GMVMaxCampaignInfo>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{GMVMaxEndpoints.CreateCampaign}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Update a GMV Max Campaign
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing campaign updates</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated campaign details</returns>
        //public async Task<Response<GMVMaxCampaignInfo>> UpdateCampaignAsync(
        //    string accessToken,
        //    GMVMaxCampaignUpdateBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    _logger?.LogDebug("Updating GMV Max campaign {CampaignId}", body.CampaignId);

        //    return await _apiClient.CallApiAsync<Response<GMVMaxCampaignInfo>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{GMVMaxEndpoints.UpdateCampaign}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Update the status of a GMV Max Campaign
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing status update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated campaign details</returns>
        //public async Task<Response<GMVMaxCampaignInfo>> UpdateCampaignStatusAsync(
        //    string accessToken,
        //    GMVMaxCampaignStatusUpdateBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    _logger?.LogDebug("Updating GMV Max campaign status for campaign {CampaignId}", body.CampaignId);

        //    return await _apiClient.CallApiAsync<Response<GMVMaxCampaignInfo>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{GMVMaxEndpoints.UpdateCampaignStatus}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Obtain a list of TikTok Shops that an ad account has access to and whether the TikTok Shops can be used to create GMV Max Campaigns
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TikTok Shops list</returns>
        public async Task<Response<GMVMaxStoreListResponse>> GetStoresAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            _logger?.LogDebug("Getting TikTok Shops for advertiser {AdvertiserId}", advertiserId);

            return await _apiClient.CallApiAsync<Response<GMVMaxStoreListResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{GMVMaxEndpoints.GetStores}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Obtain the recommended ROI target and budget for a Product GMV Max or Live GMV Campaign that uses a specific TikTok Shop
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="storeId">The ID of the TikTok Shop</param>
        /// <param name="shoppingAdsType">The type of the GMV Max Campaign</param>
        /// <param name="optimizationGoal">Optimization goal</param>
        /// <param name="itemGroupIds">The list of SPU IDs for specific products</param>
        /// <param name="identityId">The LIVE source identity (required for LIVE campaigns)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing bid recommendations</returns>
        public async Task<Response<GMVMaxBidRecommendationResponse>> GetBidRecommendationAsync(
            string accessToken,
            string advertiserId,
            string storeId,
            string shoppingAdsType,
            string optimizationGoal,
            List<string>? itemGroupIds = null,
            string? identityId = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrWhiteSpace(storeId))
                throw new ArgumentException("Store ID cannot be null or empty", nameof(storeId));
            if (string.IsNullOrWhiteSpace(shoppingAdsType))
                throw new ArgumentException("Shopping ads type cannot be null or empty", nameof(shoppingAdsType));
            if (string.IsNullOrWhiteSpace(optimizationGoal))
                throw new ArgumentException("Optimization goal cannot be null or empty", nameof(optimizationGoal));

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["store_id"] = storeId,
                ["shopping_ads_type"] = shoppingAdsType,
                ["optimization_goal"] = optimizationGoal
            };

            if (itemGroupIds != null && itemGroupIds.Count > 0)
            {
                queryParams["item_group_ids"] = string.Join(",", itemGroupIds);
            }

            if (!string.IsNullOrWhiteSpace(identityId))
            {
                queryParams["identity_id"] = identityId;
            }

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            _logger?.LogDebug("Getting bid recommendation for store {StoreId}", storeId);

            return await _apiClient.CallApiAsync<Response<GMVMaxBidRecommendationResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{GMVMaxEndpoints.GetBidRecommendation}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Obtain a list of identities that are associated with a TikTok Shop and whether the identities are available for GMV Max Campaigns
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="storeId">The ID of the TikTok Shop</param>
        /// <param name="storeAuthorizedBcId">ID of the Business Center that is authorized to access the TikTok Shop</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing identities list</returns>
        public async Task<Response<GMVMaxIdentityListResponse>> GetIdentitiesAsync(
            string accessToken,
            string advertiserId,
            string storeId,
            string storeAuthorizedBcId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrWhiteSpace(storeId))
                throw new ArgumentException("Store ID cannot be null or empty", nameof(storeId));
            if (string.IsNullOrWhiteSpace(storeAuthorizedBcId))
                throw new ArgumentException("Store authorized BC ID cannot be null or empty", nameof(storeAuthorizedBcId));

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["store_id"] = storeId,
                ["store_authorized_bc_id"] = storeAuthorizedBcId
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            _logger?.LogDebug("Getting identities for store {StoreId}", storeId);

            return await _apiClient.CallApiAsync<Response<GMVMaxIdentityListResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{GMVMaxEndpoints.GetIdentities}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Run a report on GMV Max Campaigns
        /// </summary>
        /// <param name="request">Request containing all parameters for the report</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing GMV Max Campaign report data</returns>
        public async Task<TikTokApiResponse<GMVMaxReportResponse>> GetReportAsync(
            GMVMaxReportRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request.AdvertiserId));
            if (request.StoreIds == null || request.StoreIds.Count == 0)
                throw new ArgumentException("Store IDs cannot be null or empty", nameof(request.StoreIds));
            if (request.StoreIds.Count > 1)
                throw new ArgumentException("Store IDs list cannot contain more than 1 item", nameof(request.StoreIds));
            if (string.IsNullOrWhiteSpace(request.StartDate))
                throw new ArgumentException("Start date cannot be null or empty", nameof(request.StartDate));
            if (string.IsNullOrWhiteSpace(request.EndDate))
                throw new ArgumentException("End date cannot be null or empty", nameof(request.EndDate));
            if (request.Metrics == null || request.Metrics.Count == 0)
                throw new ArgumentException("Metrics cannot be null or empty", nameof(request.Metrics));
            if (request.Dimensions == null || request.Dimensions.Count == 0)
                throw new ArgumentException("Dimensions cannot be null or empty", nameof(request.Dimensions));

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["store_ids"] = JsonSerializer.Serialize(request.StoreIds, _jsonOptions),
                ["start_date"] = request.StartDate,
                ["end_date"] = request.EndDate,
                ["metrics"] = JsonSerializer.Serialize(request.Metrics, _jsonOptions),
                ["dimensions"] = JsonSerializer.Serialize(request.Dimensions, _jsonOptions),
                ["enable_total_metrics"] = request.EnableTotalMetrics.ToString().ToLower(),
                ["page"] = request.Page.ToString(),
                ["page_size"] = request.PageSize.ToString()
            };

            if (request.Filtering != null)
            {
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering, _jsonOptions);
            }

            if (!string.IsNullOrWhiteSpace(request.SortField))
            {
                queryParams["sort_field"] = request.SortField;
            }

            if (!string.IsNullOrWhiteSpace(request.SortType))
            {
                queryParams["sort_type"] = request.SortType;
            }

            _logger?.LogDebug("Getting GMV Max campaign report for advertiser {AdvertiserId}", request.AdvertiserId);

            return await _apiClient.CallApiAsync<TikTokApiResponse<GMVMaxReportResponse>>(
                GMVMaxEndpoints.GetReport,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
