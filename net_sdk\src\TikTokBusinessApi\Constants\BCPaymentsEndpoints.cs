/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API BC Payments endpoints
    /// </summary>
    public static class BCPaymentsEndpoints
    {
        /// <summary>
        /// Process a payment endpoint (v1.3)
        /// </summary>
        public const string ProcessPayment = "/bc/transfer/";

        /// <summary>
        /// Get the balance and budget of ad accounts endpoint (v1.3)
        /// </summary>
        public const string GetAdvertiserBalance = "/advertiser/balance/get/";

        /// <summary>
        /// Get the balance of a BC endpoint (v1.3)
        /// </summary>
        public const string GetBCBalance = "/bc/balance/get/";

        /// <summary>
        /// Get the transaction records of a BC or ad accounts endpoint (v1.3)
        /// </summary>
        public const string GetAccountTransactions = "/bc/account/transaction/get/";

        /// <summary>
        /// Get the transaction records of ad accounts endpoint (v1.3)
        /// </summary>
        public const string GetAdvertiserTransactions = "/advertiser/transaction/get/";

        /// <summary>
        /// Get the transaction records of a BC endpoint (v1.3)
        /// </summary>
        public const string GetBCTransactions = "/bc/transaction/get/";

        /// <summary>
        /// Get the budget change history of an ad account endpoint (v1.3)
        /// </summary>
        public const string GetBudgetChangeHistory = "/bc/account/budget/changelog/get/";

        /// <summary>
        /// Get the cost records of a BC and ad accounts endpoint (v1.3)
        /// </summary>
        public const string GetCostRecords = "/bc/account/cost/get/";
    }
}
