using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TikTok.Campaigns
{
    /// <summary>
    /// DTO cho việc lấy danh sách chiến dịch quảng cáo
    /// </summary>
    public class GetCampaignListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Từ khóa tìm kiếm
        /// </summary>
        [StringLength(255)]
        public string? Filter { get; set; }

        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Tên chiến dịch
        /// </summary>
        [StringLength(255)]
        public string? CampaignName { get; set; }

        /// <summary>
        /// Loại mục tiêu
        /// </summary>
        [StringLength(100)]
        public string? ObjectiveType { get; set; }

        /// <summary>
        /// Loại chiến dịch
        /// </summary>
        public int? CampaignType { get; set; }

        /// <summary>
        /// Trạng thái hoạt động
        /// </summary>
        [StringLength(20)]
        public string? OperationStatus { get; set; }

        /// <summary>
        /// Từ ngày
        /// </summary>
        public DateTime? FromDate { get; set; }

        /// <summary>
        /// Đến ngày
        /// </summary>
        public DateTime? ToDate { get; set; }
    }
}