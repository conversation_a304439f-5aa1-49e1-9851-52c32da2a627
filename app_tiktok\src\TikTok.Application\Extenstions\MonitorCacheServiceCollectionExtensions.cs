﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using TikTok.Cache;

namespace TikTok.Extenstions
{
    public static class MonitorCacheServiceCollectionExtensions
    {
        /// <summary>
        /// Tự động đăng ký tất cả các service implement IMonitorCache
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="moduleType">Module type để scan assembly</param>
        /// <param name="serviceLifetime">Lifetime của service (mặc định là Transient)</param>
        /// <returns></returns>
        public static IServiceCollection AddAutoMonitorCacheServices(
            this IServiceCollection services,
            Type moduleType,
            ServiceLifetime serviceLifetime = ServiceLifetime.Transient)
        {
            var assembly = moduleType.Assembly;
            return services.AddAutoMonitorCacheServices(assembly, serviceLifetime);
        }

        /// <summary>
        /// Tự động đăng ký tất cả các service implement IMonitorCache từ assembly
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="assembly">Assembly để scan</param>
        /// <param name="serviceLifetime">Lifetime của service (mặc định là Transient)</param>
        /// <returns></returns>
        public static IServiceCollection AddAutoMonitorCacheServices(
            this IServiceCollection services,
            Assembly assembly,
            ServiceLifetime serviceLifetime = ServiceLifetime.Transient)
        {
            // Tìm tất cả các type implement IMonitorCache
            var monitorCacheTypes = assembly.GetTypes()
                .Where(type =>
                    !type.IsInterface &&
                    !type.IsAbstract &&
                    typeof(ICacheService).IsAssignableFrom(type))
                .ToList();

            foreach (var implementationType in monitorCacheTypes)
            {
                // Đăng ký service với interface IMonitorCache
                services.Add(new ServiceDescriptor(
                    typeof(ICacheService),
                    implementationType,
                    serviceLifetime));

                // Đăng ký service với concrete type để có thể inject trực tiếp
                services.Add(new ServiceDescriptor(
                    implementationType,
                    implementationType,
                    serviceLifetime));

                Console.WriteLine($"Auto registered monitor cache service: {implementationType.Name}");
            }

            return services;
        }

        /// <summary>
        /// Tự động đăng ký tất cả các service implement IMonitorCache từ nhiều assembly
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="assemblies">Danh sách assembly để scan</param>
        /// <param name="serviceLifetime">Lifetime của service (mặc định là Transient)</param>
        /// <returns></returns>
        public static IServiceCollection AddAutoMonitorCacheServices(
            this IServiceCollection services,
            Assembly[] assemblies,
            ServiceLifetime serviceLifetime = ServiceLifetime.Transient)
        {
            foreach (var assembly in assemblies)
            {
                services.AddAutoMonitorCacheServices(assembly, serviceLifetime);
            }

            return services;
        }
    }
}
