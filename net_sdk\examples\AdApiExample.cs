/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the Ad API
    /// </summary>
    public class AdApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public AdApiExample(string appId, string accessToken)
        {
            var configuration = Configuration.CreateWithCredentials(appId, accessToken);
            _client = new TikTokBusinessApiClient(configuration);
        }

        /// <summary>
        /// Example of getting ads
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task GetAdsExample(string advertiserId)
        {
            try
            {
                Console.WriteLine("=== Get Ads Example ===");

                // Set up filtering conditions (optional)
                var filtering = new AdGetFiltering
                {
                    PrimaryStatus = "ENABLE",
                    ObjectiveType = "TRAFFIC"
                };

                // Specify which fields to return (optional)
                var fields = new List<string>
                {
                    "ad_id",
                    "ad_name",
                    "primary_status",
                    "secondary_status",
                    "adgroup_id",
                    "campaign_id",
                    "create_time",
                    "modify_time"
                };

                Console.WriteLine("Getting ads...");

                var response = await _client.Ad.GetAdsAsync(
                    advertiserId: advertiserId,
                    fields: fields,
                    filtering: filtering,
                    page: 1,
                    pageSize: 20);

                if (response?.List != null)
                {
                    Console.WriteLine($"Found {response.List.Count} ads:");
                    foreach (var ad in response.List)
                    {
                        Console.WriteLine($"- Ad ID: {ad.AdId}");
                        Console.WriteLine($"  Name: {ad.AdName}");
                        Console.WriteLine($"  Status: {ad.PrimaryStatus}");
                        Console.WriteLine($"  Campaign ID: {ad.CampaignId}");
                        Console.WriteLine($"  Ad Group ID: {ad.AdgroupId}");
                        Console.WriteLine();
                    }

                    if (response.PageInfo != null)
                    {
                        Console.WriteLine($"Page: {response.PageInfo.Page}");
                        Console.WriteLine($"Total: {response.PageInfo.TotalNumber}");
                    }
                }

                Console.WriteLine("Request completed successfully.");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of creating ads
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="adgroupId">Your ad group ID</param>
        /// <param name="videoId">Your video ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task CreateAdExample(string advertiserId, string adgroupId, string videoId)
        {
            try
            {
                Console.WriteLine("=== Create Ad Example ===");

                var adCreateBody = new AdCreateBody
                {
                    AdvertiserId = advertiserId,
                    AdgroupId = adgroupId,
                    Creatives = new List<AdcreateCreatives>
                    {
                        new AdcreateCreatives
                        {
                            AdName = "SDK Test Ad",
                            AdText = "Check out our amazing product!",
                            CallToAction = "LEARN_MORE",
                            VideoId = videoId,
                            LandingPageUrl = "https://example.com",
                            DisplayName = "Example Company"
                        }
                    }
                };

                var request = new AdCreateRequest
                {
                    AdvertiserId = advertiserId,
                    AdCreateBody = new List<AdCreateBody> { adCreateBody }
                };

                Console.WriteLine($"Creating ad for ad group: {adgroupId}");

                var response = await _client.Ad.CreateAdsAsync(request);

                if (response?.AdIds != null)
                {
                    Console.WriteLine("Ad creation results:");
                    foreach (var adId in response.AdIds)
                    {
                        Console.WriteLine($"✓ Ad created successfully!");
                        Console.WriteLine($"  Ad ID: {adId}");
                    }
                }

                Console.WriteLine("Request completed successfully.");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of updating ad status
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="adIds">List of ad IDs to update</param>
        /// <param name="operation">Operation to perform (ENABLE, DISABLE, DELETE)</param>
        /// <returns>Task representing the async operation</returns>
        public async Task UpdateAdStatusExample(string advertiserId, List<string> adIds, string operation)
        {
            try
            {
                Console.WriteLine("=== Update Ad Status Example ===");

                var request = new AdStatusUpdateRequest
                {
                    AdvertiserId = advertiserId,
                    AdIds = adIds,
                    Operation = operation
                };

                Console.WriteLine($"Updating status of {adIds.Count} ads to: {operation}");

                var response = await _client.Ad.UpdateAdStatusAsync(request);

                if (response?.AdIds != null)
                {
                    Console.WriteLine("Ad status update results:");
                    foreach (var adId in response.AdIds)
                    {
                        Console.WriteLine($"✓ Ad status updated successfully!");
                        Console.WriteLine($"  Ad ID: {adId}");
                    }
                }

                Console.WriteLine("Request completed successfully.");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Complete example workflow demonstrating ad management
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="adgroupId">Your ad group ID</param>
        /// <param name="videoId">Your video ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task CompleteWorkflowExample(string advertiserId, string adgroupId, string videoId)
        {
            Console.WriteLine("=== Complete Ad Management Workflow ===");

            // 1. Get existing ads
            await GetAdsExample(advertiserId);

            // 2. Create a new ad
            await CreateAdExample(advertiserId, adgroupId, videoId);

            // 3. Get ads again to see the new ad
            await GetAdsExample(advertiserId);

            Console.WriteLine("Workflow completed!");
        }

        /// <summary>
        /// Dispose of resources
        /// </summary>
        public void Dispose()
        {
            _client?.Dispose();
        }
    }

    /// <summary>
    /// Program entry point for running the examples
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Replace with your actual credentials
            const string appId = "your-app-id";
            const string accessToken = "your-access-token";
            const string advertiserId = "your-advertiser-id";
            const string adgroupId = "your-adgroup-id";
            const string videoId = "your-video-id";
            
            var example = new AdApiExample(appId, accessToken);
            
            try
            {
                Console.WriteLine("=== TikTok Business API - Ad Examples ===\n");
                
                // Example 1: Get ads
                await example.GetAdsExample(advertiserId);
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 2: Create an ad
                await example.CreateAdExample(advertiserId, adgroupId, videoId);
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 3: Update ad status
                var adIds = new List<string> { "your-ad-id" };
                await example.UpdateAdStatusExample(advertiserId, adIds, "ENABLE");
            }
            finally
            {
                example.Dispose();
            }
        }
    }
}
