/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Catalog Diagnostics operations
    /// </summary>
    public class CatalogDiagnosticsApi : ICatalogDiagnosticsApi
    {
        private readonly IApiClient _apiClient;

        /// <summary>
        /// Initializes a new instance of the CatalogDiagnosticsApi class
        /// </summary>
        /// <param name="apiClient">The API client</param>
        public CatalogDiagnosticsApi(IApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        }

        /// <summary>
        /// Get synchronous catalog product diagnostic information
        /// </summary>
        /// <param name="catalogId">ID of a catalog that you have permission to access</param>
        /// <param name="bcId">ID of a Business Center that either owns the catalog, or has been granted access to it as an asset</param>
        /// <param name="feedId">Feed ID. If not specified, the ID of the default feed for the catalog will be used. To retrieve the diagnostics for all feeds for the catalog, set this field to "ALL".</param>
        /// <param name="filtering">Filtering conditions</param>
        /// <param name="lang">The language you want to set for the returned issue_title and reason_and_suggestion. Default value: "en".</param>
        /// <param name="page">Current page number. Default value: 1. Value range: ≥1.</param>
        /// <param name="pageSize">Page size. Default value: 10. Value range: [1, 20].</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Catalog diagnostic information</returns>
        public async Task<GetCatalogDiagnosticsResponse> GetCatalogDiagnosticsAsync(
            string catalogId,
            string bcId,
            string? feedId = null,
            CatalogDiagnosticsFiltering? filtering = null,
            string? lang = null,
            int? page = null,
            int? pageSize = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(catalogId))
                throw new ArgumentException("Catalog ID cannot be null or empty", nameof(catalogId));
            if (string.IsNullOrEmpty(bcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));

            var queryParams = new Dictionary<string, string>
            {
                ["catalog_id"] = catalogId,
                ["bc_id"] = bcId
            };

            if (!string.IsNullOrEmpty(feedId))
                queryParams["feed_id"] = feedId;

            if (filtering != null)
            {
                if (!string.IsNullOrEmpty(filtering.IssueLevel))
                    queryParams["filtering[issue_level]"] = filtering.IssueLevel;
                if (!string.IsNullOrEmpty(filtering.IssueCategory))
                    queryParams["filtering[issue_category]"] = filtering.IssueCategory;
            }

            if (!string.IsNullOrEmpty(lang))
                queryParams["lang"] = lang;

            if (page.HasValue)
                queryParams["page"] = page.Value.ToString();

            if (pageSize.HasValue)
                queryParams["page_size"] = pageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetCatalogDiagnosticsResponse>(
                CatalogDiagnosticsEndpoints.GetCatalogDiagnostics,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Create an asynchronous download task for catalog product diagnostic information
        /// </summary>
        /// <param name="request">Request containing catalog and task information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task creation response containing task ID</returns>
        //public async Task<CreateDiagnosticTaskResponse> CreateDiagnosticTaskAsync(
        //    CreateDiagnosticTaskRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrEmpty(request.CatalogId))
        //        throw new ArgumentException("Catalog ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrEmpty(request.BcId))
        //        throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

        //    return await _apiClient.CallApiAsync<CreateDiagnosticTaskResponse>(
        //        CatalogDiagnosticsEndpoints.CreateDiagnosticTask,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Download asynchronous catalog product diagnostic information
        /// </summary>
        /// <param name="catalogId">Catalog ID that you used to create the download task for catalog diagnostic information</param>
        /// <param name="bcId">Business Center ID that you used to create the download task for catalog diagnostic information</param>
        /// <param name="taskId">ID of the download task for catalog diagnostic information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task status and download URL</returns>
        public async Task<GetDiagnosticTaskResponse> GetDiagnosticTaskAsync(
            string catalogId,
            string bcId,
            string taskId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(catalogId))
                throw new ArgumentException("Catalog ID cannot be null or empty", nameof(catalogId));
            if (string.IsNullOrEmpty(bcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));
            if (string.IsNullOrEmpty(taskId))
                throw new ArgumentException("Task ID cannot be null or empty", nameof(taskId));

            var queryParams = new Dictionary<string, string>
            {
                ["catalog_id"] = catalogId,
                ["bc_id"] = bcId,
                ["task_id"] = taskId
            };

            return await _apiClient.CallApiAsync<GetDiagnosticTaskResponse>(
                CatalogDiagnosticsEndpoints.GetDiagnosticTask,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get catalog event source diagnostic information
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="eventSourceType">The event source type. Enum values: "APP", "PIXEL"</param>
        /// <param name="appId">Required when event_source_type is set to "APP". The App ID.</param>
        /// <param name="pixelCode">Required when event_source_type is set to "PIXEL". The Pixel code.</param>
        /// <param name="eventType">The event type that you want to retrieve data for. Enum values: "VIEW_CONTENT", "ADD_TO_CART", "PURCHASE". Default value: "VIEW_CONTENT".</param>
        /// <param name="timeRange">The time range that you want to retrieve data for. Enum values: "YESTERDAY", "LAST_7_DAYS", "LAST_30_DAYS". Default value: "LAST_7_DAYS".</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Event source diagnostic issues</returns>
        public async Task<GetEventSourceIssuesResponse> GetEventSourceIssuesAsync(
            string bcId,
            string catalogId,
            string eventSourceType,
            string? appId = null,
            string? pixelCode = null,
            string? eventType = null,
            string? timeRange = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(bcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));
            if (string.IsNullOrEmpty(catalogId))
                throw new ArgumentException("Catalog ID cannot be null or empty", nameof(catalogId));
            if (string.IsNullOrEmpty(eventSourceType))
                throw new ArgumentException("Event source type cannot be null or empty", nameof(eventSourceType));

            // Validate conditional parameters
            if (eventSourceType == "APP" && string.IsNullOrEmpty(appId))
                throw new ArgumentException("App ID is required when event source type is APP", nameof(appId));
            if (eventSourceType == "PIXEL" && string.IsNullOrEmpty(pixelCode))
                throw new ArgumentException("Pixel code is required when event source type is PIXEL", nameof(pixelCode));

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = bcId,
                ["catalog_id"] = catalogId,
                ["event_source_type"] = eventSourceType
            };

            if (!string.IsNullOrEmpty(appId))
                queryParams["app_id"] = appId;

            if (!string.IsNullOrEmpty(pixelCode))
                queryParams["pixel_code"] = pixelCode;

            if (!string.IsNullOrEmpty(eventType))
                queryParams["event_type"] = eventType;

            if (!string.IsNullOrEmpty(timeRange))
                queryParams["time_range"] = timeRange;

            return await _apiClient.CallApiAsync<GetEventSourceIssuesResponse>(
                CatalogDiagnosticsEndpoints.GetEventSourceIssues,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get catalog event trends and match rate
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="eventSourceType">The event source type. Enum values: "APP", "PIXEL"</param>
        /// <param name="appId">Required when event_source_type is set to "APP". The App ID.</param>
        /// <param name="pixelCode">Required when event_source_type is set to "PIXEL". The Pixel code.</param>
        /// <param name="eventType">The event type that you want to retrieve data for. Enum values: "VIEW_CONTENT", "ADD_TO_CART", "PURCHASE". Default value: "VIEW_CONTENT".</param>
        /// <param name="timeRange">The time range that you want to retrieve data for. Enum values: "YESTERDAY", "LAST_7_DAYS", "LAST_30_DAYS". Default value: "LAST_7_DAYS".</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Event source metrics and trends</returns>
        public async Task<GetEventSourceMetricsResponse> GetEventSourceMetricsAsync(
            string bcId,
            string catalogId,
            string eventSourceType,
            string? appId = null,
            string? pixelCode = null,
            string? eventType = null,
            string? timeRange = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(bcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));
            if (string.IsNullOrEmpty(catalogId))
                throw new ArgumentException("Catalog ID cannot be null or empty", nameof(catalogId));
            if (string.IsNullOrEmpty(eventSourceType))
                throw new ArgumentException("Event source type cannot be null or empty", nameof(eventSourceType));

            // Validate conditional parameters
            if (eventSourceType == "APP" && string.IsNullOrEmpty(appId))
                throw new ArgumentException("App ID is required when event source type is APP", nameof(appId));
            if (eventSourceType == "PIXEL" && string.IsNullOrEmpty(pixelCode))
                throw new ArgumentException("Pixel code is required when event source type is PIXEL", nameof(pixelCode));

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = bcId,
                ["catalog_id"] = catalogId,
                ["event_source_type"] = eventSourceType
            };

            if (!string.IsNullOrEmpty(appId))
                queryParams["app_id"] = appId;

            if (!string.IsNullOrEmpty(pixelCode))
                queryParams["pixel_code"] = pixelCode;

            if (!string.IsNullOrEmpty(eventType))
                queryParams["event_type"] = eventType;

            if (!string.IsNullOrEmpty(timeRange))
                queryParams["time_range"] = timeRange;

            return await _apiClient.CallApiAsync<GetEventSourceMetricsResponse>(
                CatalogDiagnosticsEndpoints.GetEventSourceMetrics,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
