/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Information about the URL property
    /// </summary>
    public class UrlPropertyMeta
    {
        /// <summary>
        /// Type of the URL property
        /// Enum values: 1 (Domain), 2 (URL prefix)
        /// </summary>
        [JsonPropertyName("property_type")]
        public int PropertyType { get; set; }

        /// <summary>
        /// An owned URL
        /// </summary>
        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;
    }
}
