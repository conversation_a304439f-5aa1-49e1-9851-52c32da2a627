/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region App Events Models

    /// <summary>
    /// Request model for reporting an app event
    /// </summary>
    public class AppEventRequest
    {
        /// <summary>
        /// TikTok App ID
        /// </summary>
        [JsonPropertyName("tiktok_app_id")]
        public string TikTokAppId { get; set; } = string.Empty;

        /// <summary>
        /// App event name
        /// </summary>
        [JsonPropertyName("event")]
        public string? Event { get; set; }

        /// <summary>
        /// Timestamp with ISO 8601 format
        /// </summary>
        [JsonPropertyName("timestamp")]
        public string? Timestamp { get; set; }

        /// <summary>
        /// Information about ad data connection
        /// </summary>
        [JsonPropertyName("context")]
        public AppEventContext Context { get; set; } = new();

        /// <summary>
        /// Properties associated with the event
        /// </summary>
        [JsonPropertyName("properties")]
        public AppEventProperties? Properties { get; set; }
    }

    /// <summary>
    /// Request model for reporting app events in bulk
    /// </summary>
    public class AppEventBatchRequest
    {
        /// <summary>
        /// TikTok App ID
        /// </summary>
        [JsonPropertyName("tiktok_app_id")]
        public string TikTokAppId { get; set; } = string.Empty;

        /// <summary>
        /// Array of app events reported in batch
        /// </summary>
        [JsonPropertyName("batch")]
        public List<AppEventBatchItem> Batch { get; set; } = new();
    }

    /// <summary>
    /// Individual app event item in batch request
    /// </summary>
    public class AppEventBatchItem
    {
        /// <summary>
        /// App event type (currently only "track" is supported)
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = "track";

        /// <summary>
        /// App event name
        /// </summary>
        [JsonPropertyName("event")]
        public string? Event { get; set; }

        /// <summary>
        /// Timestamp with ISO 8601 format
        /// </summary>
        [JsonPropertyName("timestamp")]
        public string? Timestamp { get; set; }

        /// <summary>
        /// Information about ad data connection
        /// </summary>
        [JsonPropertyName("context")]
        public AppEventContext Context { get; set; } = new();

        /// <summary>
        /// Properties associated with the event
        /// </summary>
        [JsonPropertyName("properties")]
        public AppEventProperties? Properties { get; set; }
    }

    /// <summary>
    /// Context information for app events
    /// </summary>
    public class AppEventContext
    {
        /// <summary>
        /// Details about the app
        /// </summary>
        [JsonPropertyName("app")]
        public AppEventAppInfo App { get; set; } = new();

        /// <summary>
        /// Device information
        /// </summary>
        [JsonPropertyName("device")]
        public AppEventDeviceInfo? Device { get; set; }

        /// <summary>
        /// Locale information
        /// </summary>
        [JsonPropertyName("locale")]
        public string? Locale { get; set; }

        /// <summary>
        /// User IP address
        /// </summary>
        [JsonPropertyName("ip")]
        public string? Ip { get; set; }

        /// <summary>
        /// User agent
        /// </summary>
        [JsonPropertyName("user_agent")]
        public string? UserAgent { get; set; }

        /// <summary>
        /// User information
        /// </summary>
        [JsonPropertyName("user")]
        public AppEventUserInfo? User { get; set; }

        /// <summary>
        /// SDK version info
        /// </summary>
        [JsonPropertyName("library")]
        public AppEventLibraryInfo? Library { get; set; }

        /// <summary>
        /// Original attribution partner
        /// </summary>
        [JsonPropertyName("ori_attribution_partner")]
        public string? OriAttributionPartner { get; set; }

        /// <summary>
        /// Ad information
        /// </summary>
        [JsonPropertyName("ad")]
        public AppEventAdInfo? Ad { get; set; }

        /// <summary>
        /// Location information
        /// </summary>
        [JsonPropertyName("location")]
        public AppEventLocationInfo? Location { get; set; }
    }

    /// <summary>
    /// App information for app events
    /// </summary>
    public class AppEventAppInfo
    {
        /// <summary>
        /// Mobile App ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Application name
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Android package name or iOS Bundle ID
        /// </summary>
        [JsonPropertyName("namespace")]
        public string? Namespace { get; set; }

        /// <summary>
        /// App version number
        /// </summary>
        [JsonPropertyName("version")]
        public string? Version { get; set; }

        /// <summary>
        /// App build number
        /// </summary>
        [JsonPropertyName("build")]
        public string? Build { get; set; }
    }

    /// <summary>
    /// Device information for app events
    /// </summary>
    public class AppEventDeviceInfo
    {
        /// <summary>
        /// Operating system platform
        /// </summary>
        [JsonPropertyName("platform")]
        public string? Platform { get; set; }

        /// <summary>
        /// iOS IDFA
        /// </summary>
        [JsonPropertyName("idfa")]
        public string? Idfa { get; set; }

        /// <summary>
        /// iOS IDFV
        /// </summary>
        [JsonPropertyName("idfv")]
        public string? Idfv { get; set; }

        /// <summary>
        /// Google Advertising ID
        /// </summary>
        [JsonPropertyName("gaid")]
        public string? Gaid { get; set; }

        /// <summary>
        /// App tracking transparency status
        /// </summary>
        [JsonPropertyName("att_status")]
        public string? AttStatus { get; set; }
    }

    /// <summary>
    /// User information for app events
    /// </summary>
    public class AppEventUserInfo
    {
        /// <summary>
        /// External user ID (hashed with SHA256)
        /// </summary>
        [JsonPropertyName("external_id")]
        public string? ExternalId { get; set; }

        /// <summary>
        /// User phone number (hashed with SHA256)
        /// </summary>
        [JsonPropertyName("phone_number")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// User email (hashed with SHA256)
        /// </summary>
        [JsonPropertyName("email")]
        public string? Email { get; set; }
    }

    /// <summary>
    /// Library information for app events
    /// </summary>
    public class AppEventLibraryInfo
    {
        /// <summary>
        /// SDK name
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// SDK version
        /// </summary>
        [JsonPropertyName("version")]
        public string? Version { get; set; }
    }

    /// <summary>
    /// Ad information for app events
    /// </summary>
    public class AppEventAdInfo
    {
        /// <summary>
        /// Callback information
        /// </summary>
        [JsonPropertyName("callback")]
        public string? Callback { get; set; }

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// Creative ID
        /// </summary>
        [JsonPropertyName("creative_id")]
        public string? CreativeId { get; set; }

        /// <summary>
        /// Whether the user is a retargeting user
        /// </summary>
        [JsonPropertyName("isRetargeting")]
        public bool? IsRetargeting { get; set; }

        /// <summary>
        /// Whether the event is attributed
        /// </summary>
        [JsonPropertyName("attributed")]
        public bool? Attributed { get; set; }

        /// <summary>
        /// Attribution type
        /// </summary>
        [JsonPropertyName("attributionType")]
        public string? AttributionType { get; set; }

        /// <summary>
        /// Attribution provider
        /// </summary>
        [JsonPropertyName("attribution_provider")]
        public string? AttributionProvider { get; set; }
    }

    /// <summary>
    /// Location information for app events
    /// </summary>
    public class AppEventLocationInfo
    {
        /// <summary>
        /// Country or region
        /// </summary>
        [JsonPropertyName("country")]
        public string? Country { get; set; }

        /// <summary>
        /// City
        /// </summary>
        [JsonPropertyName("city")]
        public string? City { get; set; }
    }

    /// <summary>
    /// Properties associated with app events
    /// </summary>
    public class AppEventProperties
    {
        /// <summary>
        /// Related items in an app event
        /// </summary>
        [JsonPropertyName("contents")]
        public List<AppEventContentItem>? Contents { get; set; }

        /// <summary>
        /// Currency code (ISO 4217)
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// Total value of the order or items sold
        /// </summary>
        [JsonPropertyName("value")]
        public double? Value { get; set; }

        /// <summary>
        /// Description of the app event
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Search query text
        /// </summary>
        [JsonPropertyName("query")]
        public string? Query { get; set; }
    }

    /// <summary>
    /// Content item for app events
    /// </summary>
    public class AppEventContentItem
    {
        /// <summary>
        /// Price of the item
        /// </summary>
        [JsonPropertyName("price")]
        public double? Price { get; set; }

        /// <summary>
        /// Quantity of the item
        /// </summary>
        [JsonPropertyName("quantity")]
        public int? Quantity { get; set; }

        /// <summary>
        /// Type of the product item
        /// </summary>
        [JsonPropertyName("content_type")]
        public string? ContentType { get; set; }

        /// <summary>
        /// ID of the product item
        /// </summary>
        [JsonPropertyName("content_id")]
        public string? ContentId { get; set; }

        /// <summary>
        /// Brand name of the product item
        /// </summary>
        [JsonPropertyName("brand")]
        public string? Brand { get; set; }
    }

    #endregion

    #region App Info Models

    /// <summary>
    /// Request model for getting app information
    /// </summary>
    public class AppInfoRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// App ID
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for app information
    /// </summary>
    public class AppInfoResponse
    {
        /// <summary>
        /// App details
        /// </summary>
        [JsonPropertyName("app")]
        public AppInfo? App { get; set; }
    }

    /// <summary>
    /// Detailed app information
    /// </summary>
    public class AppInfo
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// App platform ID
        /// </summary>
        [JsonPropertyName("app_platform_id")]
        public string? AppPlatformId { get; set; }

        /// <summary>
        /// App name
        /// </summary>
        [JsonPropertyName("app_name")]
        public string? AppName { get; set; }

        /// <summary>
        /// Whether self-attribution is enabled
        /// </summary>
        [JsonPropertyName("self_attribution_enabled")]
        public bool? SelfAttributionEnabled { get; set; }

        /// <summary>
        /// Whether SKAN is allowed
        /// </summary>
        [JsonPropertyName("skan_allowed")]
        public string? SkanAllowed { get; set; }

        /// <summary>
        /// Whether advanced dedicated campaigns are allowed
        /// </summary>
        [JsonPropertyName("advanced_dedicated_campaign_allowed")]
        public bool? AdvancedDedicatedCampaignAllowed { get; set; }

        /// <summary>
        /// App download URL
        /// </summary>
        [JsonPropertyName("download_url")]
        public string? DownloadUrl { get; set; }

        /// <summary>
        /// App icon information
        /// </summary>
        [JsonPropertyName("icon")]
        public AppIconInfo? Icon { get; set; }

        /// <summary>
        /// App ID
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// App package name
        /// </summary>
        [JsonPropertyName("package_name")]
        public string? PackageName { get; set; }

        /// <summary>
        /// TikTok App information
        /// </summary>
        [JsonPropertyName("tiktok_apps")]
        public List<TikTokAppInfo>? TikTokApps { get; set; }

        /// <summary>
        /// Third-party tracking partner information
        /// </summary>
        [JsonPropertyName("partner")]
        public AppPartnerInfo? Partner { get; set; }

        /// <summary>
        /// Application platform
        /// </summary>
        [JsonPropertyName("platform")]
        public string? Platform { get; set; }

        /// <summary>
        /// Enable retargeting setting
        /// </summary>
        [JsonPropertyName("enable_retargeting")]
        public string? EnableRetargeting { get; set; }

        /// <summary>
        /// Tracking URLs from third-party partners
        /// </summary>
        [JsonPropertyName("tracking_url")]
        public AppTrackingUrlInfo? TrackingUrl { get; set; }

        /// <summary>
        /// Creation time
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }
    }

    /// <summary>
    /// App icon information
    /// </summary>
    public class AppIconInfo
    {
        /// <summary>
        /// Icon height
        /// </summary>
        [JsonPropertyName("height")]
        public int? Height { get; set; }

        /// <summary>
        /// Icon image ID
        /// </summary>
        [JsonPropertyName("web_uri")]
        public string? WebUri { get; set; }

        /// <summary>
        /// Icon width
        /// </summary>
        [JsonPropertyName("width")]
        public int? Width { get; set; }
    }

    /// <summary>
    /// TikTok App information
    /// </summary>
    public class TikTokAppInfo
    {
        /// <summary>
        /// TikTok App ID
        /// </summary>
        [JsonPropertyName("tiktok_app_id")]
        public string? TikTokAppId { get; set; }
    }

    /// <summary>
    /// App partner information
    /// </summary>
    public class AppPartnerInfo
    {
        /// <summary>
        /// Partner ID
        /// </summary>
        [JsonPropertyName("partner_id")]
        public string? PartnerId { get; set; }

        /// <summary>
        /// Partner logo URI
        /// </summary>
        [JsonPropertyName("partner_logo")]
        public string? PartnerLogo { get; set; }

        /// <summary>
        /// Partner name
        /// </summary>
        [JsonPropertyName("partner_name")]
        public string? PartnerName { get; set; }
    }

    /// <summary>
    /// App tracking URL information
    /// </summary>
    public class AppTrackingUrlInfo
    {
        /// <summary>
        /// Tracking link for clicks
        /// </summary>
        [JsonPropertyName("click_url")]
        public string? ClickUrl { get; set; }

        /// <summary>
        /// Tracking link for impressions
        /// </summary>
        [JsonPropertyName("impression_url")]
        public string? ImpressionUrl { get; set; }

        /// <summary>
        /// Tracking link for retargeting clicks
        /// </summary>
        [JsonPropertyName("retargeting_click_url")]
        public string? RetargetingClickUrl { get; set; }

        /// <summary>
        /// Tracking link for retargeting impressions
        /// </summary>
        [JsonPropertyName("retargeting_impression_url")]
        public string? RetargetingImpressionUrl { get; set; }
    }

    #endregion

    #region Web Events Models

    /// <summary>
    /// Request model for reporting a web event
    /// </summary>
    public class WebEventRequest
    {
        /// <summary>
        /// Pixel ID
        /// </summary>
        [JsonPropertyName("pixel_code")]
        public string PixelCode { get; set; } = string.Empty;

        /// <summary>
        /// Conversion event name
        /// </summary>
        [JsonPropertyName("event")]
        public string Event { get; set; } = string.Empty;

        /// <summary>
        /// Event ID for deduplication
        /// </summary>
        [JsonPropertyName("event_id")]
        public string? EventId { get; set; }

        /// <summary>
        /// Timestamp when the event took place
        /// </summary>
        [JsonPropertyName("timestamp")]
        public string? Timestamp { get; set; }

        /// <summary>
        /// Context information for attribution
        /// </summary>
        [JsonPropertyName("context")]
        public WebEventContext Context { get; set; } = new();

        /// <summary>
        /// Properties associated with the event
        /// </summary>
        [JsonPropertyName("properties")]
        public WebEventProperties? Properties { get; set; }
    }

    /// <summary>
    /// Request model for reporting web events in bulk
    /// </summary>
    public class WebEventBatchRequest
    {
        /// <summary>
        /// Pixel ID
        /// </summary>
        [JsonPropertyName("pixel_code")]
        public string PixelCode { get; set; } = string.Empty;

        /// <summary>
        /// List of web events
        /// </summary>
        [JsonPropertyName("batch")]
        public List<WebEventBatchItem> Batch { get; set; } = new();
    }

    /// <summary>
    /// Individual web event item in batch request
    /// </summary>
    public class WebEventBatchItem
    {
        /// <summary>
        /// Event type (currently only "track" is supported)
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = "track";

        /// <summary>
        /// Conversion event name
        /// </summary>
        [JsonPropertyName("event")]
        public string? Event { get; set; }

        /// <summary>
        /// Event ID for deduplication
        /// </summary>
        [JsonPropertyName("event_id")]
        public string? EventId { get; set; }

        /// <summary>
        /// Timestamp when the event took place
        /// </summary>
        [JsonPropertyName("timestamp")]
        public string? Timestamp { get; set; }

        /// <summary>
        /// Context information for attribution
        /// </summary>
        [JsonPropertyName("context")]
        public WebEventContext? Context { get; set; }

        /// <summary>
        /// Properties associated with the event
        /// </summary>
        [JsonPropertyName("properties")]
        public WebEventProperties? Properties { get; set; }
    }

    /// <summary>
    /// Context information for web events
    /// </summary>
    public class WebEventContext
    {
        /// <summary>
        /// Ad information
        /// </summary>
        [JsonPropertyName("ad")]
        public WebEventAdInfo? Ad { get; set; }

        /// <summary>
        /// Page information
        /// </summary>
        [JsonPropertyName("page")]
        public WebEventPageInfo? Page { get; set; }

        /// <summary>
        /// User information
        /// </summary>
        [JsonPropertyName("user")]
        public WebEventUserInfo? User { get; set; }

        /// <summary>
        /// User IP address
        /// </summary>
        [JsonPropertyName("ip")]
        public string? Ip { get; set; }

        /// <summary>
        /// User agent
        /// </summary>
        [JsonPropertyName("user_agent")]
        public string? UserAgent { get; set; }
    }

    /// <summary>
    /// Ad information for web events
    /// </summary>
    public class WebEventAdInfo
    {
        /// <summary>
        /// TikTok Click ID
        /// </summary>
        [JsonPropertyName("callback")]
        public string? Callback { get; set; }
    }

    /// <summary>
    /// Page information for web events
    /// </summary>
    public class WebEventPageInfo
    {
        /// <summary>
        /// Page URL when event happened
        /// </summary>
        [JsonPropertyName("url")]
        public string? Url { get; set; }

        /// <summary>
        /// Page referrer
        /// </summary>
        [JsonPropertyName("referrer")]
        public string? Referrer { get; set; }
    }

    /// <summary>
    /// User information for web events
    /// </summary>
    public class WebEventUserInfo
    {
        /// <summary>
        /// External user ID (hashed with SHA256)
        /// </summary>
        [JsonPropertyName("external_id")]
        public string? ExternalId { get; set; }

        /// <summary>
        /// User email (hashed with SHA256)
        /// </summary>
        [JsonPropertyName("email")]
        public string? Email { get; set; }

        /// <summary>
        /// User phone number (hashed with SHA256)
        /// </summary>
        [JsonPropertyName("phone_number")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// TikTok pixel cookie ID
        /// </summary>
        [JsonPropertyName("ttp")]
        public string? Ttp { get; set; }
    }

    /// <summary>
    /// Properties associated with web events
    /// </summary>
    public class WebEventProperties
    {
        /// <summary>
        /// Content type (product or product_group)
        /// </summary>
        [JsonPropertyName("content_type")]
        public string? ContentType { get; set; }

        /// <summary>
        /// List of content objects with product information
        /// </summary>
        [JsonPropertyName("contents")]
        public List<WebEventContentItem>? Contents { get; set; }

        /// <summary>
        /// Currency code (ISO 4217)
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// Total value of the order or items sold
        /// </summary>
        [JsonPropertyName("value")]
        public double? Value { get; set; }

        /// <summary>
        /// Search query text
        /// </summary>
        [JsonPropertyName("query")]
        public string? Query { get; set; }

        /// <summary>
        /// Description of the item or page
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Status of an order, item, or service
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }
    }

    /// <summary>
    /// Content item for web events
    /// </summary>
    public class WebEventContentItem
    {
        /// <summary>
        /// Price of the item
        /// </summary>
        [JsonPropertyName("price")]
        public double? Price { get; set; }

        /// <summary>
        /// Quantity of the item
        /// </summary>
        [JsonPropertyName("quantity")]
        public int? Quantity { get; set; }

        /// <summary>
        /// Unique ID of the product or content
        /// </summary>
        [JsonPropertyName("content_id")]
        public string? ContentId { get; set; }

        /// <summary>
        /// Category of the page/product
        /// </summary>
        [JsonPropertyName("content_category")]
        public string? ContentCategory { get; set; }

        /// <summary>
        /// Name of the page/product
        /// </summary>
        [JsonPropertyName("content_name")]
        public string? ContentName { get; set; }

        /// <summary>
        /// Brand name of the product item
        /// </summary>
        [JsonPropertyName("brand")]
        public string? Brand { get; set; }
    }

    #endregion

    #region Offline Events Models

    /// <summary>
    /// Request model for creating an offline event set
    /// </summary>
    public class CreateOfflineEventSetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Event set name
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Event set description
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Whether to enable auto tracking
        /// </summary>
        [JsonPropertyName("auto_tracking")]
        public bool? AutoTracking { get; set; }
    }

    /// <summary>
    /// Response model for creating an offline event set
    /// </summary>
    public class CreateOfflineEventSetResponse
    {
        /// <summary>
        /// The event set ID that was created
        /// </summary>
        [JsonPropertyName("event_set_id")]
        public string? EventSetId { get; set; }
    }

    /// <summary>
    /// Request model for updating an offline event set
    /// </summary>
    public class UpdateOfflineEventSetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Event set ID
        /// </summary>
        [JsonPropertyName("event_set_id")]
        public string EventSetId { get; set; } = string.Empty;

        /// <summary>
        /// Event set name
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Event set description
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Whether to enable auto tracking
        /// </summary>
        [JsonPropertyName("auto_tracking")]
        public bool? AutoTracking { get; set; }
    }

    /// <summary>
    /// Request model for getting offline event sets
    /// </summary>
    public class GetOfflineEventSetsRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Event set IDs to filter by
        /// </summary>
        [JsonPropertyName("event_set_ids")]
        public List<string>? EventSetIds { get; set; }
    }

    /// <summary>
    /// Response model for getting offline event sets
    /// </summary>
    public class GetOfflineEventSetsResponse
    {
        /// <summary>
        /// List of offline event sets
        /// </summary>
        [JsonPropertyName("event_sets")]
        public List<OfflineEventSetInfo>? EventSets { get; set; }
    }

    /// <summary>
    /// Offline event set information
    /// </summary>
    public class OfflineEventSetInfo
    {
        /// <summary>
        /// Event set ID
        /// </summary>
        [JsonPropertyName("event_set_id")]
        public string? EventSetId { get; set; }

        /// <summary>
        /// Event set name
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Event set description
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Whether auto tracking is enabled
        /// </summary>
        [JsonPropertyName("auto_tracking")]
        public bool? AutoTracking { get; set; }

        /// <summary>
        /// Creation time
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Last update time
        /// </summary>
        [JsonPropertyName("update_time")]
        public string? UpdateTime { get; set; }
    }

    /// <summary>
    /// Request model for deleting an offline event set
    /// </summary>
    public class DeleteOfflineEventSetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Event set ID
        /// </summary>
        [JsonPropertyName("event_set_id")]
        public string EventSetId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request model for reporting offline events
    /// </summary>
    public class ReportOfflineEventsRequest
    {
        /// <summary>
        /// Event set ID
        /// </summary>
        [JsonPropertyName("event_set_id")]
        public string EventSetId { get; set; } = string.Empty;

        /// <summary>
        /// Event name
        /// </summary>
        [JsonPropertyName("event")]
        public string Event { get; set; } = string.Empty;

        /// <summary>
        /// Event ID for deduplication
        /// </summary>
        [JsonPropertyName("event_id")]
        public string? EventId { get; set; }

        /// <summary>
        /// Timestamp when the event occurred
        /// </summary>
        [JsonPropertyName("timestamp")]
        public string? Timestamp { get; set; }

        /// <summary>
        /// User information
        /// </summary>
        [JsonPropertyName("user")]
        public OfflineEventUserInfo? User { get; set; }

        /// <summary>
        /// Properties associated with the event
        /// </summary>
        [JsonPropertyName("properties")]
        public OfflineEventProperties? Properties { get; set; }
    }

    /// <summary>
    /// Request model for reporting offline events in bulk
    /// </summary>
    public class ReportOfflineEventsBatchRequest
    {
        /// <summary>
        /// Event set ID
        /// </summary>
        [JsonPropertyName("event_set_id")]
        public string EventSetId { get; set; } = string.Empty;

        /// <summary>
        /// List of offline events
        /// </summary>
        [JsonPropertyName("batch")]
        public List<OfflineEventBatchItem> Batch { get; set; } = new();
    }

    /// <summary>
    /// Individual offline event item in batch request
    /// </summary>
    public class OfflineEventBatchItem
    {
        /// <summary>
        /// Event type (currently only "track" is supported)
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = "track";

        /// <summary>
        /// Event name
        /// </summary>
        [JsonPropertyName("event")]
        public string? Event { get; set; }

        /// <summary>
        /// Event ID for deduplication
        /// </summary>
        [JsonPropertyName("event_id")]
        public string? EventId { get; set; }

        /// <summary>
        /// Timestamp when the event occurred
        /// </summary>
        [JsonPropertyName("timestamp")]
        public string? Timestamp { get; set; }

        /// <summary>
        /// User information
        /// </summary>
        [JsonPropertyName("user")]
        public OfflineEventUserInfo? User { get; set; }

        /// <summary>
        /// Properties associated with the event
        /// </summary>
        [JsonPropertyName("properties")]
        public OfflineEventProperties? Properties { get; set; }
    }

    /// <summary>
    /// User information for offline events
    /// </summary>
    public class OfflineEventUserInfo
    {
        /// <summary>
        /// External user ID (hashed with SHA256)
        /// </summary>
        [JsonPropertyName("external_id")]
        public string? ExternalId { get; set; }

        /// <summary>
        /// User email (hashed with SHA256)
        /// </summary>
        [JsonPropertyName("email")]
        public string? Email { get; set; }

        /// <summary>
        /// User phone number (hashed with SHA256)
        /// </summary>
        [JsonPropertyName("phone_number")]
        public string? PhoneNumber { get; set; }
    }

    /// <summary>
    /// Properties associated with offline events
    /// </summary>
    public class OfflineEventProperties
    {
        /// <summary>
        /// Currency code (ISO 4217)
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// Total value of the order or items sold
        /// </summary>
        [JsonPropertyName("value")]
        public double? Value { get; set; }

        /// <summary>
        /// Description of the event
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Order ID or transaction ID
        /// </summary>
        [JsonPropertyName("order_id")]
        public string? OrderId { get; set; }

        /// <summary>
        /// Store location or identifier
        /// </summary>
        [JsonPropertyName("store_location")]
        public string? StoreLocation { get; set; }
    }

    #endregion

    #region Common Response Models

    /// <summary>
    /// Response model for batch operations with partial failure support
    /// </summary>
    public class EventsBatchResponse
    {
        /// <summary>
        /// Whether the event reporting failed partially
        /// </summary>
        [JsonPropertyName("partial_failure")]
        public bool? PartialFailure { get; set; }

        /// <summary>
        /// List of errors in case of partial failure
        /// </summary>
        [JsonPropertyName("failed_events")]
        public List<EventsBatchFailedEvent>? FailedEvents { get; set; }
    }

    /// <summary>
    /// Failed event information in batch operations
    /// </summary>
    public class EventsBatchFailedEvent
    {
        /// <summary>
        /// Order in batch request
        /// </summary>
        [JsonPropertyName("order_in_batch")]
        public int? OrderInBatch { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("error")]
        public string? Error { get; set; }
    }

    #endregion
}
