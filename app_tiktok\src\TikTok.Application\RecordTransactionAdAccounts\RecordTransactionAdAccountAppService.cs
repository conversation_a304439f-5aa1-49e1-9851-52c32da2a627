using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using TikTok.RecordTransactionAdAccounts;
using TikTok.Localization;
using TikTok.Permissions;
using TikTok.RecordTransactionAdAccounts;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using TikTok.Entities;
using TikTok.Repositories;

namespace TikTok.RecordTransactionAdAccounts
{
    /// <summary>
    /// App Service cho RecordTransactionAdAccount
    /// </summary>
    public class RecordTransactionAdAccountAppService :
        CrudAppService<
            RawRecordTransactionAdAccountEntity,
            RecordTransactionAdAccountDto,
            Guid,
            GetRecordTransactionAdAccountListDto,
            CreateRecordTransactionAdAccountDto,
            UpdateRecordTransactionAdAccountDto>,
        IRecordTransactionAdAccountAppService
    {
        private readonly IRecordTransactionAdAccountRepository _adAccountTransactionRepository;

        public RecordTransactionAdAccountAppService(
            IRepository<RawRecordTransactionAdAccountEntity, Guid> repository,
            IRecordTransactionAdAccountRepository adAccountTransactionRepository)
            : base(repository)
        {
            _adAccountTransactionRepository = adAccountTransactionRepository;

            // Định nghĩa quyền
            GetPolicyName = TikTokPermissions.RecordTransactionAdAccounts.Default;
            GetListPolicyName = TikTokPermissions.RecordTransactionAdAccounts.Default;
            CreatePolicyName = TikTokPermissions.RecordTransactionAdAccounts.Create;
            UpdatePolicyName = TikTokPermissions.RecordTransactionAdAccounts.Edit;
            DeletePolicyName = TikTokPermissions.RecordTransactionAdAccounts.Delete;
        }

        /// <summary>
        /// Lấy bản ghi giao dịch theo Advertiser ID và Date
        /// </summary>
        /// <param name="advertiserId">ID Tài khoản Quảng cáo</param>
        /// <param name="date">Thời gian giao dịch</param>
        /// <returns>Bản ghi giao dịch</returns>
        [Authorize(TikTokPermissions.RecordTransactionAdAccounts.Default)]
        public async Task<RecordTransactionAdAccountDto> GetByAdvertiserIdAndDateAsync(string advertiserId, DateTime date)
        {
            var entity = await _adAccountTransactionRepository.GetByAdvertiserIdAndDateAsync(advertiserId, date);
            return ObjectMapper.Map<RawRecordTransactionAdAccountEntity, RecordTransactionAdAccountDto>(entity);
        }

        /// <summary>
        /// Lấy danh sách bản ghi giao dịch theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID Tài khoản Quảng cáo</param>
        /// <returns>Danh sách bản ghi giao dịch</returns>
        [Authorize(TikTokPermissions.RecordTransactionAdAccounts.Default)]
        public async Task<PagedResultDto<RecordTransactionAdAccountDto>> GetByAdvertiserIdAsync(string advertiserId)
        {
            var entities = await _adAccountTransactionRepository.GetByAdvertiserIdAsync(advertiserId);
            var dtos = ObjectMapper.Map<RawRecordTransactionAdAccountEntity[], RecordTransactionAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<RecordTransactionAdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        /// <summary>
        /// Ghi đè phương thức GetListAsync để sử dụng custom repository
        /// </summary>
        /// <param name="input">Input để lấy danh sách</param>
        /// <returns>Danh sách bản ghi giao dịch</returns>
        [Authorize(TikTokPermissions.RecordTransactionAdAccounts.Default)]
        public override async Task<PagedResultDto<RecordTransactionAdAccountDto>> GetListAsync(GetRecordTransactionAdAccountListDto input)
        {
            var entities = await _adAccountTransactionRepository.GetListAsync(
                input.Sorting,
                input.MaxResultCount,
                input.SkipCount,
                input.Filter
            );

            var totalCount = await _adAccountTransactionRepository.GetCountAsync(input.Filter);

            var dtos = ObjectMapper.Map<RawRecordTransactionAdAccountEntity[], RecordTransactionAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<RecordTransactionAdAccountDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }

        /// <summary>
        /// Ghi đè phương thức CreateAsync để thêm validation
        /// </summary>
        /// <param name="input">Input để tạo mới</param>
        /// <returns>Bản ghi giao dịch đã tạo</returns>
        [Authorize(TikTokPermissions.RecordTransactionAdAccounts.Create)]
        public override async Task<RecordTransactionAdAccountDto> CreateAsync(CreateRecordTransactionAdAccountDto input)
        {
            // Kiểm tra xem đã tồn tại bản ghi với AdvertiserId và Date chưa
            var exists = await _adAccountTransactionRepository.IsExistsAsync(input.AdvertiserId, input.Date);
            if (exists)
            {
                throw new BusinessException(L["AdAccountTransactionAlreadyExists"]);
            }

            return await base.CreateAsync(input);
        }

        /// <summary>
        /// Ghi đè phương thức UpdateAsync để thêm validation
        /// </summary>
        /// <param name="id">ID của bản ghi</param>
        /// <param name="input">Input để cập nhật</param>
        /// <returns>Bản ghi giao dịch đã cập nhật</returns>
        [Authorize(TikTokPermissions.RecordTransactionAdAccounts.Edit)]
        public override async Task<RecordTransactionAdAccountDto> UpdateAsync(Guid id, UpdateRecordTransactionAdAccountDto input)
        {
            // Kiểm tra xem đã tồn tại bản ghi khác với AdvertiserId và Date chưa (trừ bản ghi hiện tại)
            var exists = await _adAccountTransactionRepository.IsExistsAsync(input.AdvertiserId, input.Date, id);
            if (exists)
            {
                throw new BusinessException(L["AdAccountTransactionAlreadyExists"]);
            }

            return await base.UpdateAsync(id, input);
        }

        /// <summary>
        /// Ghi đè phương thức DeleteAsync để thêm authorization
        /// </summary>
        /// <param name="id">ID của bản ghi</param>
        /// <returns>Task</returns>
        [Authorize(TikTokPermissions.RecordTransactionAdAccounts.Delete)]
        public override async Task DeleteAsync(Guid id)
        {
            await base.DeleteAsync(id);
        }
    }
}