/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Terms endpoints
    /// </summary>
    public static class TermsEndpoints
    {
        /// <summary>
        /// Get terms endpoint (v1.3)
        /// </summary>
        public const string GetTerms = "/term/get/";

        /// <summary>
        /// Sign terms endpoint (v1.3)
        /// </summary>
        public const string SignTerms = "/term/confirm/";

        /// <summary>
        /// Check terms status endpoint (v1.3)
        /// </summary>
        public const string CheckTermsStatus = "/term/check/";
    }
}
