/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Spark Ads Recommendation endpoints
    /// </summary>
    public static class SparkAdsRecommendationEndpoints
    {
        /// <summary>
        /// Get Spark Ads video recommendations for a Business Account endpoint (v1.3)
        /// </summary>
        public const string GetBusinessVideoRecommendations = "/business/video/recommend/";

        /// <summary>
        /// Get Spark Ads video recommendations for a TTO account endpoint (v1.3)
        /// </summary>
        public const string GetTTOVideoRecommendations = "/spark_ad/recommend/";

        /// <summary>
        /// Create a campaign, an ad group, and a Spark Ad in one step endpoint (v1.3)
        /// </summary>
        public const string CreateAllInOneSparkAd = "/business/spark_ad/create/";
    }
}
