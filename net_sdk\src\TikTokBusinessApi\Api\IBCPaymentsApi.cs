/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API BC Payments operations
    /// </summary>
    public interface IBCPaymentsApi
    {
        /// <summary>
        /// Process a payment (recharge money to or deduct money from an ad account in a Business Center, or increase/decrease the credit balance of a Business Center)
        /// </summary>
        /// <param name="request">Payment processing request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing transaction information</returns>
        //Task<ProcessPaymentResponse> ProcessPaymentAsync(
        //    ProcessPaymentRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the balance and budget of ad accounts in the Business Center
        /// </summary>
        /// <param name="request">Advertiser balance request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing advertiser account balances</returns>
        Task<GetAdvertiserBalanceResponse> GetAdvertiserBalanceAsync(
            GetAdvertiserBalanceRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the balance of a Business Center
        /// </summary>
        /// <param name="request">BC balance request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing BC balance information</returns>
        Task<GetBcBalanceResponse> GetBcBalanceAsync(
            GetBcBalanceRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the transaction records of a BC or ad accounts within the Business Center
        /// </summary>
        /// <param name="request">Account transactions request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing transaction records</returns>
        Task<GetBcAccountTransactionResponse> GetBcAccountTransactionAsync(
            GetBcAccountTransactionRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the transaction records of ad accounts in the Business Center
        /// </summary>
        /// <param name="request">Advertiser transactions request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing advertiser transaction records</returns>
        Task<GetAdvertiserTransactionResponse> GetAdvertiserTransactionAsync(
            GetAdvertiserTransactionRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the transaction records of a Business Center
        /// </summary>
        /// <param name="request">BC transactions request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing BC transaction records</returns>
        Task<GetBcTransactionResponse> GetBcTransactionAsync(
            GetBcTransactionRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the budget change history of an ad account within a Business Center
        /// </summary>
        /// <param name="request">Budget change history request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing budget change history</returns>
        Task<GetBudgetChangelogResponse> GetBudgetChangelogAsync(
            GetBudgetChangelogRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the cost records at the ad account and Business Center levels
        /// </summary>
        /// <param name="request">Cost records request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing cost records</returns>
        Task<GetCostRecordsResponse> GetCostRecordsAsync(
            GetCostRecordsRequest request,
            CancellationToken cancellationToken = default);
    }
}
