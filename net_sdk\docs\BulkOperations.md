# TikTok Business API Bulk Operations

The TikTok Business API SDK provides powerful bulk operation utilities to help you efficiently process large datasets and perform batch operations with built-in error handling, retry logic, and parallel processing capabilities.

## Overview

The `BulkOperations` static class provides methods to:
- Split collections into manageable batches
- Execute operations with automatic retry logic
- Process batches in parallel with configurable concurrency
- Track success/failure rates and detailed error information
- Handle transient failures gracefully

## Quick Start

### Basic Batching

```csharp
using TikTokBusinessApi.Utilities;

// Split a large collection into batches
var items = Enumerable.Range(1, 100).ToList();
var batches = items.Batch(10); // Creates batches of 10 items each

foreach (var batch in batches)
{
    Console.WriteLine($"Processing batch: {string.Join(", ", batch)}");
    // Process each batch...
}
```

### Sequential Bulk Operations

```csharp
// Define your operation
var operation = async (IEnumerable<string> batch, CancellationToken ct) =>
{
    // Your API call here
    var request = new CampaignStatusUpdateRequest
    {
        AdvertiserId = advertiserId,
        CampaignIds = batch.ToList(),
        OperationStatus = "ENABLE"
    };
    
    return await client.Campaign.UpdateCampaignStatusAsync(accessToken, request, ct);
};

// Execute with retry logic
var result = await BulkOperations.ExecuteBulkOperationAsync(
    campaignIds, 
    operation, 
    batchSize: 20,
    maxRetries: 3,
    retryDelay: 1000
);

// Check results
Console.WriteLine($"Success rate: {result.SuccessRate:F1}%");
```

### Parallel Bulk Operations

```csharp
// Execute batches in parallel for better performance
var result = await BulkOperations.ExecuteParallelBulkOperationAsync(
    items,
    operation,
    batchSize: 10,
    maxConcurrency: 3 // Process up to 3 batches simultaneously
);
```

## Key Features

### Automatic Retry Logic

The bulk operations utility includes built-in retry logic for handling transient failures:

```csharp
var result = await BulkOperations.ExecuteBulkOperationAsync(
    items,
    operation,
    maxRetries: 3,        // Retry up to 3 times
    retryDelay: 1000      // Wait 1 second between retries
);
```

### Comprehensive Error Tracking

Failed operations are tracked with detailed information:

```csharp
foreach (var error in result.FailedOperations)
{
    Console.WriteLine($"Error: {error.Error}");
    Console.WriteLine($"Retry attempts: {error.RetryCount}");
    Console.WriteLine($"Timestamp: {error.Timestamp}");
    Console.WriteLine($"Exception: {error.Exception?.Message}");
}
```

### Result Analysis

The `BulkOperationResult<T>` provides comprehensive metrics:

```csharp
var result = await BulkOperations.ExecuteBulkOperationAsync(items, operation);

Console.WriteLine($"Total operations: {result.TotalOperations}");
Console.WriteLine($"Successful: {result.SuccessfulOperations.Count}");
Console.WriteLine($"Failed: {result.FailedOperations.Count}");
Console.WriteLine($"Success rate: {result.SuccessRate:F1}%");
Console.WriteLine($"Fully successful: {result.IsFullySuccessful}");
Console.WriteLine($"Has any success: {result.HasAnySuccess}");
Console.WriteLine($"Has any failure: {result.HasAnyFailure}");
```

## Real-World Examples

### Campaign Status Updates

```csharp
public async Task UpdateCampaignStatuses(string accessToken, string advertiserId, List<string> campaignIds)
{
    var operation = async (IEnumerable<string> batch, CancellationToken ct) =>
    {
        var request = new CampaignStatusUpdateRequest
        {
            AdvertiserId = advertiserId,
            CampaignIds = batch.ToList(),
            OperationStatus = "ENABLE"
        };
        
        return await _client.Campaign.UpdateCampaignStatusAsync(accessToken, request, ct);
    };

    var result = await BulkOperations.ExecuteBulkOperationAsync(
        campaignIds, operation, batchSize: 20);

    if (result.IsFullySuccessful)
    {
        Console.WriteLine("All campaigns updated successfully!");
    }
    else
    {
        Console.WriteLine($"Updated {result.SuccessfulOperations.Count} batches, {result.FailedOperations.Count} failed");
    }
}
```

### Data Retrieval Across Multiple Advertisers

```csharp
public async Task GetCampaignsForMultipleAdvertisers(string accessToken, List<string> advertiserIds)
{
    var operation = async (IEnumerable<string> batch, CancellationToken ct) =>
    {
        var tasks = batch.Select(advertiserId => 
            _client.Campaign.GetCampaignsAsync(advertiserId, cancellationToken: ct));
        
        var responses = await Task.WhenAll(tasks);
        return responses.ToList();
    };

    var result = await BulkOperations.ExecuteParallelBulkOperationAsync(
        advertiserIds, operation, batchSize: 5, maxConcurrency: 3);

    var totalCampaigns = result.SuccessfulOperations
        .SelectMany(responses => responses)
        .SelectMany(response => response.Data ?? new List<CampaignInfo>())
        .Count();

    Console.WriteLine($"Retrieved {totalCampaigns} campaigns from {advertiserIds.Count} advertisers");
}
```

## Best Practices

### Batch Size Selection

Choose appropriate batch sizes based on:
- **API rate limits**: Stay within TikTok's rate limiting constraints
- **Memory usage**: Larger batches use more memory
- **Error isolation**: Smaller batches isolate failures better
- **Performance**: Balance between overhead and throughput

```csharp
// Recommended batch sizes for different operations
var batchSize = operationType switch
{
    "campaign_update" => 20,
    "ad_creation" => 10,
    "data_retrieval" => 50,
    _ => 25
};
```

### Error Handling Strategy

```csharp
var result = await BulkOperations.ExecuteBulkOperationAsync(items, operation);

if (result.HasAnyFailure)
{
    // Log failures for investigation
    foreach (var error in result.FailedOperations)
    {
        _logger.LogError(error.Exception, "Batch operation failed: {Error}", error.Error);
    }

    // Optionally retry failed items
    if (result.SuccessRate < 90) // Less than 90% success
    {
        // Implement retry logic for failed items
        await RetryFailedOperations(result.FailedOperations);
    }
}
```

### Parallel Processing Guidelines

```csharp
// Configure concurrency based on your needs
var maxConcurrency = Environment.ProcessorCount; // Use CPU cores as baseline
maxConcurrency = Math.Min(maxConcurrency, 5);    // Limit to avoid overwhelming APIs

var result = await BulkOperations.ExecuteParallelBulkOperationAsync(
    items, operation, maxConcurrency: maxConcurrency);
```

### Logging Integration

```csharp
// Pass logger for detailed operation tracking
var result = await BulkOperations.ExecuteBulkOperationAsync(
    items, operation, logger: _logger);

// The utility will automatically log:
// - Operation start/completion
// - Batch processing progress
// - Retry attempts
// - Final success/failure statistics
```

## Performance Considerations

1. **Memory Usage**: Large batches consume more memory. Monitor memory usage with large datasets.

2. **API Rate Limits**: Respect TikTok's rate limits by choosing appropriate batch sizes and concurrency levels.

3. **Network Latency**: Parallel processing can help overcome network latency for independent operations.

4. **Error Recovery**: Implement proper error handling and consider retry strategies for transient failures.

5. **Monitoring**: Use logging to monitor operation performance and identify bottlenecks.

## Integration with Dependency Injection

```csharp
// In Program.cs or Startup.cs
services.AddScoped<BulkOperationService>();

public class BulkOperationService
{
    private readonly TikTokBusinessApiClient _client;
    private readonly ILogger<BulkOperationService> _logger;

    public BulkOperationService(TikTokBusinessApiClient client, ILogger<BulkOperationService> logger)
    {
        _client = client;
        _logger = logger;
    }

    public async Task<BulkOperationResult<T>> ProcessItemsAsync<T>(
        IEnumerable<string> items,
        Func<IEnumerable<string>, CancellationToken, Task<T>> operation)
    {
        return await BulkOperations.ExecuteBulkOperationAsync(
            items, operation, logger: _logger);
    }
}
```

## Testing

The SDK includes comprehensive tests for bulk operations. Run them with:

```bash
dotnet test --filter "FullyQualifiedName~BulkOperationsTests"
```

## Complete Example

See `BulkOperationsExample.cs` in the examples folder for a complete implementation showing various bulk operation patterns and best practices.
