using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using TikTok.AdAccounts;
using TikTok.Permissions;
using TikTok.ResourcePermissions;
using TikTok.Users;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.ObjectMapping;

namespace TikTok.UserAccessManagement
{
    /// <summary>
    /// Application Service cho User Access Management
    /// </summary>
    [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
    [RemoteService(IsEnabled = false)]
    public class UserAccessManagementAppService : ApplicationService, IUserAccessManagementAppService
    {
        private readonly IUserManagementService _userManagementService;
        private readonly IAdAccountAppService _adAccountAppService;
        private readonly IResourcePermissionAppService _resourcePermissionAppService;
        private readonly IIdentityUserRepository _identityUserRepository;
        private readonly IdentityUserManager _userManager;
        private readonly ILogger<UserAccessManagementAppService> _logger;

        public UserAccessManagementAppService(
            IUserManagementService userManagementService,
            IAdAccountAppService adAccountAppService,
            IResourcePermissionAppService resourcePermissionAppService,
            IIdentityUserRepository identityUserRepository,
            IdentityUserManager userManager,
            ILogger<UserAccessManagementAppService> logger)
        {
            _userManagementService = userManagementService;
            _adAccountAppService = adAccountAppService;
            _resourcePermissionAppService = resourcePermissionAppService;
            _identityUserRepository = identityUserRepository;
            _userManager = userManager;
            _logger = logger;
        }

        /// <summary>
        /// Lấy danh sách người dùng với thông tin truy cập
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<PagedResultDto<UserAccessDto>> GetUsersAsync(GetUserAccessListDto input)
        {
            try
            {
                _logger.LogDebug("Getting users list with access information. Filter: {Filter}", input.Filter);

                // Lấy danh sách người dùng từ UserManagementService
                var userInput = new GetUserListDto
                {
                    Filter = input.Filter,
                    UserName = input.UserName,
                    Email = input.Email,
                    Name = input.Name,
                    MaxResultCount = input.MaxResultCount,
                    SkipCount = input.SkipCount,
                    Sorting = input.Sorting
                };

                var usersResult = await _userManagementService.GetUserListAsync(userInput);
                var userAccessList = new List<UserAccessDto>();

                foreach (var user in usersResult.Items)
                {
                    var userAccess = await MapToUserAccessDto(user);
                    
                    // Áp dụng các bộ lọc bổ sung
                    if (ShouldIncludeUser(userAccess, input))
                    {
                        userAccessList.Add(userAccess);
                    }
                }

                // Áp dụng lọc và sắp xếp
                var filteredUsers = ApplyAdditionalFilters(userAccessList, input);
                var totalCount = filteredUsers.Count;

                // Phân trang
                var pagedUsers = filteredUsers
                    .Skip(input.SkipCount)
                    .Take(input.MaxResultCount)
                    .ToList();

                _logger.LogDebug("Retrieved {Count} users with access information", pagedUsers.Count);

                return new PagedResultDto<UserAccessDto>
                {
                    TotalCount = totalCount,
                    Items = pagedUsers
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting users list");
                throw;
            }
        }

        /// <summary>
        /// Lấy thông tin truy cập của một người dùng cụ thể
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<UserAccessDto> GetUserAccessAsync(Guid userId)
        {
            try
            {
                _logger.LogDebug("Getting user access information for user: {UserId}", userId);

                var user = await _userManagementService.GetUserAsync(userId);
                if (user == null)
                {
                    throw new Volo.Abp.UserFriendlyException($"User with ID {userId} not found");
                }

                var userAccess = await MapToUserAccessDto(user);
                
                _logger.LogDebug("Retrieved access information for user: {UserName}", user.UserName);
                return userAccess;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user access information for user: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// Lấy danh sách tài khoản quảng cáo có thể phân quyền cho người dùng
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<List<AdAccountDto>> GetAvailableAdAccountsAsync(GetAvailableAdAccountsDto input)
        {
            try
            {
                _logger.LogDebug("Getting available ad accounts for user: {UserId}", input.UserId);

                // Lấy tất cả tài khoản quảng cáo
                var adAccountsInput = new GetAdAccountListDto
                {
                    FilterText = input.Filter,
                    OwnerBcId = input.BcId,
                    AdvertiserId = input.AdvertiserId,
                    MaxResultCount = input.MaxResultCount
                };

                var allAdAccounts = await _adAccountAppService.GetListAsync(adAccountsInput);

                // Lấy danh sách tài khoản đã được phân quyền cho user này
                var assignedAdAccounts = await GetAssignedAdAccountsAsync(input.UserId);
                var assignedIds = assignedAdAccounts.Select(a => a.AdvertiserId).ToHashSet();

                // Lọc ra những tài khoản chưa được phân quyền
                var availableAdAccounts = allAdAccounts.Items
                    .Where(a => !assignedIds.Contains(a.AdvertiserId))
                    .ToList();

                _logger.LogDebug("Found {Count} available ad accounts for user: {UserId}", availableAdAccounts.Count, input.UserId);
                return availableAdAccounts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting available ad accounts for user: {UserId}", input.UserId);
                throw;
            }
        }

        /// <summary>
        /// Lấy danh sách tài khoản quảng cáo đã được phân quyền cho người dùng
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<List<AdAccountDto>> GetAssignedAdAccountsAsync(Guid userId)
        {
            try
            {
                _logger.LogDebug("Getting assigned ad accounts for user: {UserId}", userId);

                // Lấy danh sách quyền của user đối với tài khoản quảng cáo
                var userPermissionsPaged = await _resourcePermissionAppService.GetByUserIdAsync(userId);
                var adAccountPermissions = userPermissionsPaged.Items
                    .Where(p => p.ResourceType == "AdAccount")
                    .Select(p => p.ResourceId)
                    .Distinct()
                    .ToList();

                if (!adAccountPermissions.Any())
                {
                    return new List<AdAccountDto>();
                }

                // Lấy thông tin chi tiết của các tài khoản quảng cáo
                var assignedAdAccounts = new List<AdAccountDto>();
                foreach (var advertiserId in adAccountPermissions)
                {
                    try
                    {
                        var adAccountsInput = new GetAdAccountListDto
                        {
                            AdvertiserId = advertiserId,
                            MaxResultCount = 1
                        };

                        var adAccountResult = await _adAccountAppService.GetListAsync(adAccountsInput);
                        if (adAccountResult.Items.Any())
                        {
                            assignedAdAccounts.Add(adAccountResult.Items.First());
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Could not retrieve ad account details for AdvertiserId: {AdvertiserId}", advertiserId);
                    }
                }

                _logger.LogDebug("Found {Count} assigned ad accounts for user: {UserId}", assignedAdAccounts.Count, userId);
                return assignedAdAccounts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting assigned ad accounts for user: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// Phân quyền tài khoản quảng cáo cho người dùng
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task AssignAdAccountsAsync(AssignAdAccountsDto input)
        {
            try
            {
                _logger.LogDebug("Assigning {Count} ad accounts to user: {UserId}", input.AdAccountIds.Count, input.UserId);

                // Kiểm tra user tồn tại
                var user = await _userManagementService.GetUserAsync(input.UserId);
                if (user == null)
                {
                    throw new Volo.Abp.UserFriendlyException($"User with ID {input.UserId} not found");
                }

                // Tạo danh sách quyền cần phân cho từng tài khoản quảng cáo
                var permissionsToAssign = new List<ResourcePermissions.AssignPermissionForResourceDto>();

                foreach (var adAccountId in input.AdAccountIds)
                {
                    permissionsToAssign.Add(new ResourcePermissions.AssignPermissionForResourceDto
                    {
                        UserId = input.UserId,
                        ResourceId = adAccountId,
                        ResourceType = "AdAccount",
                        Permissions = new List<string> { "View", "Access" } // Quyền cơ bản
                    });
                }

                // Thực hiện phân quyền
                await _resourcePermissionAppService.AssignPermissionsAsync(permissionsToAssign);

                _logger.LogInformation("Successfully assigned {Count} ad accounts to user: {UserId}", input.AdAccountIds.Count, input.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while assigning ad accounts to user: {UserId}", input.UserId);
                throw;
            }
        }

        /// <summary>
        /// Thu hồi quyền truy cập tài khoản quảng cáo của người dùng
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task RevokeAdAccountsAsync(RevokeAdAccountsDto input)
        {
            try
            {
                _logger.LogDebug("Revoking {Count} ad accounts from user: {UserId}", input.AdAccountIds.Count, input.UserId);

                // Kiểm tra user tồn tại
                var user = await _userManagementService.GetUserAsync(input.UserId);
                if (user == null)
                {
                    throw new Volo.Abp.UserFriendlyException($"User with ID {input.UserId} not found");
                }

                // Thu hồi quyền cho từng tài khoản quảng cáo
                foreach (var adAccountId in input.AdAccountIds)
                {
                    await _resourcePermissionAppService.RevokePermissionsAsync(input.UserId, adAccountId, "AdAccount");
                }

                _logger.LogInformation("Successfully revoked {Count} ad accounts from user: {UserId}", input.AdAccountIds.Count, input.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while revoking ad accounts from user: {UserId}", input.UserId);
                throw;
            }
        }

        /// <summary>
        /// Lấy thống kê tổng quan về quyền truy cập
        /// </summary>
        [Authorize(TikTokPermissions.AdAccountPermissionManagement.Default)]
        public async Task<UserAccessStatisticsDto> GetStatisticsAsync()
        {
            try
            {
                _logger.LogDebug("Getting user access statistics");

                var statistics = new UserAccessStatisticsDto();

                // Lấy thống kê người dùng
                var allUsers = await _userManagementService.GetAllUsersAsync();
                statistics.TotalUsers = allUsers.Count;
                statistics.ActiveUsers = allUsers.Count(u => u.IsActive);

                // Lấy thống kê tài khoản quảng cáo
                var allAdAccounts = await _adAccountAppService.GetListAsync(new GetAdAccountListDto { MaxResultCount = int.MaxValue });
                statistics.TotalAdAccounts = (int)allAdAccounts.TotalCount;

                // Đếm số người dùng có quyền truy cập tài khoản quảng cáo
                var usersWithAccess = 0;
                var assignedAdAccountsSet = new HashSet<string>();

                foreach (var user in allUsers)
                {
                    var userPermissionsPaged = await _resourcePermissionAppService.GetByUserIdAsync(user.Id);
                    var adAccountPermissions = userPermissionsPaged.Items.Where(p => p.ResourceType == "AdAccount").ToList();

                    if (adAccountPermissions.Any())
                    {
                        usersWithAccess++;
                        foreach (var permission in adAccountPermissions)
                        {
                            assignedAdAccountsSet.Add(permission.ResourceId);
                        }
                    }
                }

                statistics.UsersWithAdAccountAccess = usersWithAccess;
                statistics.AssignedAdAccounts = assignedAdAccountsSet.Count;
                statistics.UnassignedAdAccounts = statistics.TotalAdAccounts - statistics.AssignedAdAccounts;

                // Thống kê theo vai trò
                var usersByRole = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
                foreach (var user in allUsers)
                {
                    try
                    {
                        var identityUser = await _identityUserRepository.FindAsync(user.Id);
                        if (identityUser != null)
                        {
                            var roles = await _userManager.GetRolesAsync(identityUser);
                            var primaryRole = roles.FirstOrDefault() ?? "No Role";
                            if (!usersByRole.ContainsKey(primaryRole))
                            {
                                usersByRole[primaryRole] = 0;
                            }
                            usersByRole[primaryRole]++;
                        }
                        else
                        {
                            if (!usersByRole.ContainsKey("No Role"))
                            {
                                usersByRole["No Role"] = 0;
                            }
                            usersByRole["No Role"]++;
                        }
                    }
                    catch
                    {
                        if (!usersByRole.ContainsKey("Unknown"))
                        {
                            usersByRole["Unknown"] = 0;
                        }
                        usersByRole["Unknown"]++;
                    }
                }
                statistics.UsersByRole = usersByRole;

                _logger.LogDebug("Retrieved user access statistics");
                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user access statistics");
                throw;
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Chuyển đổi UserDto thành UserAccessDto
        /// </summary>
        private async Task<UserAccessDto> MapToUserAccessDto(UserDto user)
        {
            var userAccess = ObjectMapper.Map<UserDto, UserAccessDto>(user);
            userAccess.UserId = user.Id;

            // Lấy thông tin quyền truy cập tài khoản quảng cáo
            try
            {
                var assignedAdAccounts = await GetAssignedAdAccountsAsync(user.Id);
                userAccess.AdAccountsCount = assignedAdAccounts.Count;
                userAccess.AssignedAdAccountIds = assignedAdAccounts.Select(a => a.AdvertiserId).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not retrieve ad account access for user: {UserId}", user.Id);
                userAccess.AdAccountsCount = 0;
                userAccess.AssignedAdAccountIds = new List<string>();
            }

            // Lấy thông tin vai trò
            try
            {
                var identityUser = await _identityUserRepository.FindAsync(user.Id);
                if (identityUser != null)
                {
                    var roles = await _userManager.GetRolesAsync(identityUser);
                    userAccess.RoleNames = roles.ToList();
                    userAccess.RoleName = roles.FirstOrDefault() ?? "No Role";
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not retrieve roles for user: {UserId}", user.Id);
                userAccess.RoleName = "Unknown";
                userAccess.RoleNames = new List<string>();
            }

            return userAccess;
        }

        /// <summary>
        /// Kiểm tra xem có nên bao gồm user trong kết quả hay không
        /// </summary>
        private bool ShouldIncludeUser(UserAccessDto userAccess, GetUserAccessListDto input)
        {
            // Lọc theo vai trò
            if (!string.IsNullOrEmpty(input.RoleName) &&
                !userAccess.RoleNames.Any(r => r.Contains(input.RoleName, StringComparison.OrdinalIgnoreCase)))
            {
                return false;
            }

            // Lọc theo trạng thái hoạt động
            if (input.IsActive.HasValue && userAccess.IsActive != input.IsActive.Value)
            {
                return false;
            }

            // Lọc theo việc có quyền truy cập tài khoản quảng cáo
            if (input.HasAdAccountAccess.HasValue)
            {
                var hasAccess = userAccess.AdAccountsCount > 0;
                if (hasAccess != input.HasAdAccountAccess.Value)
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Áp dụng các bộ lọc bổ sung và sắp xếp
        /// </summary>
        private List<UserAccessDto> ApplyAdditionalFilters(List<UserAccessDto> users, GetUserAccessListDto input)
        {
            var query = users.AsQueryable();

            // Text search across common fields (UserName, Email, Name)
            if (!string.IsNullOrWhiteSpace(input.Filter))
            {
                var keyword = input.Filter.Trim();
                query = query.Where(u =>
                    (!string.IsNullOrEmpty(u.UserName) && u.UserName.Contains(keyword, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(u.Email) && u.Email.Contains(keyword, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(u.Name) && u.Name.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                );
            }

            // Specific field filters (contains for flexibility)
            if (!string.IsNullOrWhiteSpace(input.UserName))
            {
                var userNameKey = input.UserName.Trim();
                query = query.Where(u => !string.IsNullOrEmpty(u.UserName) && u.UserName.Contains(userNameKey, StringComparison.OrdinalIgnoreCase));
            }
            if (!string.IsNullOrWhiteSpace(input.Email))
            {
                var emailKey = input.Email.Trim();
                query = query.Where(u => !string.IsNullOrEmpty(u.Email) && u.Email.Contains(emailKey, StringComparison.OrdinalIgnoreCase));
            }
            if (!string.IsNullOrWhiteSpace(input.Name))
            {
                var nameKey = input.Name.Trim();
                query = query.Where(u => !string.IsNullOrEmpty(u.Name) && u.Name.Contains(nameKey, StringComparison.OrdinalIgnoreCase));
            }

            // Sắp xếp
            if (!string.IsNullOrEmpty(input.Sorting))
            {
                // Implement sorting logic based on input.Sorting
                if (input.Sorting.Contains("userName"))
                {
                    query = input.Sorting.Contains("desc")
                        ? query.OrderByDescending(u => u.UserName)
                        : query.OrderBy(u => u.UserName);
                }
                else if (input.Sorting.Contains("email"))
                {
                    query = input.Sorting.Contains("desc")
                        ? query.OrderByDescending(u => u.Email)
                        : query.OrderBy(u => u.Email);
                }
                else if (input.Sorting.Contains("adAccountsCount"))
                {
                    query = input.Sorting.Contains("desc")
                        ? query.OrderByDescending(u => u.AdAccountsCount)
                        : query.OrderBy(u => u.AdAccountsCount);
                }
                else
                {
                    query = query.OrderBy(u => u.UserName);
                }
            }
            else
            {
                query = query.OrderBy(u => u.UserName);
            }

            return query.ToList();
        }

        #endregion
    }
}
