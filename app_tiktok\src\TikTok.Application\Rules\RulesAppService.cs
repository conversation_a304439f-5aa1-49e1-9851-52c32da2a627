using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using TikTok.AdAccounts;
using TikTok.Application.Contracts.Rules;
using TikTok.Consts;
using TikTok.Domain.Entities.Rules;
using TikTok.Domain.Shared.Rules;
using TikTok.Entities;
using TikTok.Entities.AdAccounts;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTok.ResourceProviders;
using TikTok.ResourceProviders.AdAccounts;
using TikTok.Rules;
using Tsp.Zalo.Entities;
using Tsp.Zalo.IService;
using Tsp.Zalo.Models;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Users;

namespace TikTok.Application.Rules
{
    [RemoteService(false)]
    public class RulesAppService : CrudAppService<RuleEntity, RuleDto, Guid, PagedAndSortedResultRequestDto, CreateOrUpdateRuleDto>, IRulesAppService
    {
        private readonly ICurrentUser _currentUser;
        private readonly IPermissionChecker _permissionChecker;
        private readonly IRepository<RuleAdAccountEntity, Guid> _ruleAdAccountRepository;
        private readonly IRepository<RawAdAccountEntity, Guid> _rawAdAccountRepository;
        private readonly IRepository<RawBusinessCenterEntity, Guid> _rawBusinessCenterRepository;
        private readonly IRuleRepository _ruleRepository;
        private readonly IRepository<ZaloCookie, Guid> _zaloCookieRepository;
        private readonly IIdentityUserRepository _identityUserRepository;
        private readonly IAccountUserAppService _accountUserAppService;
        private readonly IAdAccountAppService _adAccountAppService;
        private readonly IAdAccountResourceProvider _resourceProvider;
        private readonly ICustomerRepository _customerRepository;
        private readonly ICustomerAdAccountRepository _customerAdAccountRepository;
        public RulesAppService(
            IRepository<RuleEntity, Guid> repository,
            ICurrentUser currentUser,
            IRepository<RuleAdAccountEntity, Guid> ruleAdAccountRepository,
            IRepository<RawAdAccountEntity, Guid> rawAdAccountRepository,
            IRepository<RawBusinessCenterEntity, Guid> rawBusinessCenterRepository,
            IRuleRepository ruleRepository,
            IPermissionChecker permissionChecker,
            IRepository<ZaloCookie, Guid> zaloCookieRepository,
            IAccountUserAppService accountUserAppService,
            IAdAccountAppService adAccountAppService,
            IIdentityUserRepository identityUserRepository,
            IAdAccountResourceProvider resourceProvider
,
            ICustomerRepository customerRepository,
            ICustomerAdAccountRepository customerAdAccountRepository) : base(repository)
        {
            _currentUser = currentUser;
            _ruleAdAccountRepository = ruleAdAccountRepository;
            _rawAdAccountRepository = rawAdAccountRepository;
            _rawBusinessCenterRepository = rawBusinessCenterRepository;
            _ruleRepository = ruleRepository;
            _permissionChecker = permissionChecker;
            _zaloCookieRepository = zaloCookieRepository;
            _accountUserAppService = accountUserAppService;
            _adAccountAppService = adAccountAppService;
            _identityUserRepository = identityUserRepository;
            _resourceProvider = resourceProvider;
            _customerRepository = customerRepository;
            _customerAdAccountRepository = customerAdAccountRepository;
        }

        public async Task<PagedResultDto<RuleDto>> GetOwnerRules(RuleGetListInput input)
        {
            var canViewAll = await _permissionChecker.IsGrantedAsync(TikTokPermissions.NotificationRules.ViewAll);

            var entities = await _ruleRepository.GetListAsync(
                   sorting: input.Sorting,
                   maxResultCount: input.MaxResultCount,
                   skipCount: input.SkipCount,
                   ownerId: canViewAll ? null : _currentUser.GetId(),
                   ruleName: input.RuleName,
                   targetEntity: input.TargetEntity.HasValue ? (RuleTarget)input.TargetEntity.Value : null,
                   notificationFrequency: input.NotificationFrequency,
                   isDefault: input.IsDefault);

            // var customerIds = new List<Guid>();
            
            // if (!string.IsNullOrWhiteSpace(input.CustomerName) && !string.IsNullOrWhiteSpace(input.CustomerId))
            // {
            //     var customers = await _customerRepository
            //         .GetListAsync(x => x.CustomerName.ToLower().Contains(input.CustomerName.ToLower())
            //         || x.CustomerId.ToLower()
            //         .Contains(input.CustomerId.ToLower()));
            //     customerIds = customers.Select(x => x.Id).ToList();
            // }
            // else if (!string.IsNullOrWhiteSpace(input.CustomerName))
            // {
            //     var customers = await _customerRepository
            //         .GetListAsync(x => x.CustomerName.ToLower().Contains(input.CustomerName.ToLower()));
            //     customerIds = customers.Select(x => x.Id).ToList();
            // }
            // else if (!string.IsNullOrWhiteSpace(input.CustomerId))
            // {
            //     var customers = await _customerRepository
            //         .GetListAsync(x => x.CustomerId.ToLower().Contains(input.CustomerId.ToLower()));
            //     customerIds = customers.Select(x => x.Id).ToList();
            // }
            // var customerAdAccounts = await _customerAdAccountRepository.GetListAsync(x => customerIds.Contains(x.CustomerId));
            // var customerAdAccountIds = customerAdAccounts.Select(x => x.AdvertiserId).ToList();

            var totalCount = await _ruleRepository.GetCountAsync(
                sorting: input.Sorting,
                maxResultCount: input.MaxResultCount,
                skipCount: input.SkipCount,
                ownerId: canViewAll ? null : _currentUser.GetId(),
                ruleName: input.RuleName,
                targetEntity: input.TargetEntity.HasValue ? (RuleTarget)input.TargetEntity.Value : null,
                notificationFrequency: input.NotificationFrequency,
                isDefault: input.IsDefault);

            // Get owner name
            var ownerNames = await _identityUserRepository
                .GetListByIdsAsync(
                    entities.Select(x => x.OwnerId)
                    .Where(x => x.HasValue)
                    .Select(x => x!.Value).ToList());

            var dtos = ObjectMapper.Map<RuleEntity[], RuleDto[]>(entities.ToArray());
            foreach (var dto in dtos)
            {
                dto.OwnerName = ownerNames.FirstOrDefault(x => x.Id == dto.OwnerId)?.Name;
            }

            return new PagedResultDto<RuleDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }

        public async Task<RuleConsumersResponse> GetRuleConsumers(Guid ruleId)
        {
            var ruleAdAccounts = await _ruleAdAccountRepository.GetListAsync(x => x.RuleId == ruleId && x.CreatorId == CurrentUser.Id);
            var adAccountOfRuleIds = ruleAdAccounts?.Where(x => x.AdAccountId.HasValue).Select(x => x.AdAccountId!.Value).ToList() ?? [];
            var bcIds = ruleAdAccounts?.Where(x => x.BcId.HasValue).Select(x => x.BcId!.Value).ToList() ?? [];

            // Get and map ad accounts and business centers
            var adAccountResponses = new List<RuleConsumersAdAccountResponse>();
            var businessCenters = new List<RuleConsumersBCResponse>();

            // Check if the currentUser has manage rule permission
            var canManageRule = await _permissionChecker.IsGrantedAsync(TikTokPermissions.NotificationRules.ManageRule);
            var isAdAccountSupervisor = await _permissionChecker.IsGrantedAsync(TikTokPermissions.AdAccounts.Supervise);

            var currentUserId = _currentUser.GetId();
            var resourcePermissions = await _resourceProvider.GetListAsync(
                new GetResourcePermissionAssignedUserDto
                {
                    UserId = currentUserId,
                    Permissions = new List<string>
                    {
                        TikTokPermissions.AdAccounts.Default,
                        TikTokPermissions.AdAccounts.Edit
                    }
                });
            // Get all adAccounts, that is supported by the currentUser
            var adAccountUserIds = !isAdAccountSupervisor ? resourcePermissions.Select(x => x.ResourceId).Distinct().ToList() : [];

            // Get ad accounts
            if (adAccountOfRuleIds.Any())
            {
                var rawAdAccounts = await _rawAdAccountRepository.GetListAsync(x => adAccountOfRuleIds.Contains(x.Id));
                adAccountResponses = rawAdAccounts.Select(adAccount => new RuleConsumersAdAccountResponse
                {
                    Id = adAccount.Id,
                    AdvertiserId = adAccount.AdvertiserId,
                    OwnerBcId = adAccount.OwnerBcId,
                    Name = adAccount.Name,
                    IsOwner = isAdAccountSupervisor ? true : adAccountUserIds.Contains(adAccount.AdvertiserId)
                }).ToList();
            }

            // Get business centers
            if (bcIds.Any())
            {
                var rawBusinessCenters = await _rawBusinessCenterRepository.GetListAsync(x => bcIds.Contains(x.Id));
                businessCenters = rawBusinessCenters.Select(bc => new RuleConsumersBCResponse
                {
                    Id = bc.Id,
                    BcId = bc.BcId,
                    Name = bc.Name,
                    Company = bc.Company,
                    Currency = bc.Currency,
                    RegisteredArea = bc.RegisteredArea
                }).ToList();
            }

            return new RuleConsumersResponse
            {
                RuleId = ruleId,
                AdAccounts = adAccountResponses,
                BusinessCenters = businessCenters,
            };
        }

        public async Task<List<ZaloCookieDto>> GetNotificationAccountsForRule()
        {
            var accountUsers = await _accountUserAppService.GetAccountUsersByUserIdAsync(_currentUser.GetId());
            var zaloAccountIds = accountUsers.Select(x => x.ZaloAccountId).ToList();
            var zaloCookies = await _zaloCookieRepository.GetListAsync(x => zaloAccountIds.Contains(x.Id));
            return ObjectMapper.Map<List<ZaloCookie>, List<ZaloCookieDto>>(zaloCookies).ToList();
        }

        /// <summary>
        /// Ghi đè phương thức UpdateAsync để thêm authorization
        /// </summary>
        /// <param name="id">ID của rule</param>
        /// <param name="input">Thông tin cập nhật</param>
        /// <returns>Rule đã cập nhật</returns>
        public override async Task<RuleDto> UpdateAsync(Guid id, CreateOrUpdateRuleDto input)
        {
            var canManageRule = await _permissionChecker.IsGrantedAsync(TikTokPermissions.NotificationRules.ManageRule);
            if (canManageRule)
            {
                return await base.UpdateAsync(id, input);
            }
            else
            {
                var currentRule = await Repository.GetAsync(id);
                var isOwner = currentRule.OwnerId == _currentUser.GetId();
                if (!isOwner)
                {
                    throw new BusinessException(L["NoPermissionToDeleteRule"]);
                }
                return await base.UpdateAsync(id, input);
            }
        }

        /// <summary>
        /// Ghi đè phương thức DeleteAsync để thêm authorization
        /// </summary>
        /// <param name="id">ID của rule</param>
        /// <returns>Task</returns>
        public override async Task DeleteAsync(Guid id)
        {
            var canManageRule = await _permissionChecker.IsGrantedAsync(TikTokPermissions.NotificationRules.ManageRule);
            if (canManageRule)
            {
                await base.DeleteAsync(id);
            }
            else
            {
                var currentRule = await Repository.GetAsync(id);
                var isOwner = currentRule.OwnerId == _currentUser.GetId();
                if (!isOwner)
                {
                    throw new BusinessException(L["NoPermissionToDeleteRule"]);
                }
                await base.DeleteAsync(id);
            }
        }
    }
}