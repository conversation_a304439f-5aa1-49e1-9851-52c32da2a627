using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using Newtonsoft.Json;
using System.Data;
using System.Linq.Expressions;
using TikTok.Application.Contracts.LatestNotifications;
using TikTok.Application.Contracts.Rules;
using TikTok.BalanceAdAccounts;
using TikTok.Domain.Shared.Rules;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.Rules;
using TikTok.Transactions;
using Volo.Abp.ObjectMapping;

namespace TikTok.Activities.Rules
{
    [Activity(Category = "Quy tắc", DisplayName = "Đánh giá số dư theo Ad Account", Outcomes = new[] { OutcomeNames.Done })]
    public class EvaluateBalanceRulesActivity : UnitOfWorkActivity
    {
        private readonly ILatestBalanceAdAccountRepository _latestBalanceAdAccountRepository;
        private readonly ITransactionRepository _transactionRepository;
        private readonly ILatestNotificationRepository _latestNotificationRepository;
        private readonly IRuleCache _ruleCache;
        private readonly IRuleAdAccountService _ruleAdAccountService;
        private readonly IObjectMapper _objectMapper;
        private readonly ILogger<EvaluateBalanceRulesActivity> _logger;
        private readonly Func<RuleDto, bool> IS_BALANCE_TARGET = (RuleDto r) => r.TargetEntity == RuleTarget.Balance;


        public EvaluateBalanceRulesActivity(
            ILatestBalanceAdAccountRepository latestBalanceAdAccountRepository,
            ITransactionRepository transactionRepository,
            ILatestNotificationRepository latestNotificationRepository,
            IRuleCache ruleCache,
            IRuleAdAccountService ruleAdAccountService,
            IObjectMapper objectMapper,
            ILogger<EvaluateBalanceRulesActivity> logger)
        {
            _latestBalanceAdAccountRepository = latestBalanceAdAccountRepository;
            _transactionRepository = transactionRepository;
            _latestNotificationRepository = latestNotificationRepository;
            _ruleCache = ruleCache;
            _ruleAdAccountService = ruleAdAccountService;
            _objectMapper = objectMapper;
            _logger = logger;
        }

        [ActivityInput(Hint = "Business Center ID", SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public string BcId { get; set; }

        [ActivityInput(Hint = "Danh sách Advertiser IDs", SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public List<string> AdvertiserIds { get; set; } = new List<string>();

        [ActivityOutput(Hint = "Danh sách quyết định thông báo")]
        public List<BalanceNotificationDecisionDto> Decisions { get; set; } = new List<BalanceNotificationDecisionDto>();

        [ActivityOutput(Hint = "Danh sách thông báo cần tạo/cập nhật")]
        public List<LatestNotificationDto> NotificationsToUpsert { get; set; } = new List<LatestNotificationDto>();

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            _logger.LogDebug("Starting EvaluateBalanceRulesActivity for BcId: {BcId}, AdvertiserIds: {AdvertiserIds}",
                BcId, string.Join(",", AdvertiserIds ?? new List<string>()));

            if (string.IsNullOrEmpty(BcId))
            {
                _logger.LogError("BcId is required but was null or empty");
                throw new InvalidOperationException("BcId is required.");
            }

            // 1. Lấy danh sách quy tắc từ cache
            var rulesList = await _ruleCache.GetByTargetEntityAsync(RuleTarget.Balance);

            // 2. Lấy danh sách quy tắc gắn theo tài khoản quảng cáo
            var ruleAdAccountList = await _ruleAdAccountService.GetRulesByBcIdAndAdvertiserIdsAsync(BcId, AdvertiserIds);

            // 3. Lấy danh sách số dư mới nhất
            var latestBalances = await GetLatestBalancesAsync();

            // 4. Lấy danh sách giao dịch mới nhất
            var latestTransactions = await GetLatestTransactionsAsync();

            // 5. Lấy danh sách thông báo mới nhất
            var latestNotifications = await GetLatestNotificationsAsync();

            var result = ProcessAll(new BalanceEvaluationInput
            {
                LatestBalanceAdAccounts = latestBalances,
                Notifications = latestNotifications,
                Transactions = latestTransactions,
                Rules = rulesList,
                RuleAdAccountMap = ruleAdAccountList
            });

            Decisions = result.Decisions;
            NotificationsToUpsert = result.NotificationsToUpsert;

            _logger.LogDebug("Completed EvaluateBalanceRulesActivity. Decisions: {DecisionCount}, NotificationsToUpsert: {NotificationCount}",
                Decisions.Count, NotificationsToUpsert.Count);

            return Done();
        }

        private async Task<List<BalanceAdAccountDto>> GetLatestBalancesAsync()
        {
            _logger.LogDebug("Getting latest balances for BcId: {BcId}", BcId);

            var queryable = (await _latestBalanceAdAccountRepository.GetQueryableAsync())
                .Where(x => x.BcId == BcId);

            if (AdvertiserIds != null && AdvertiserIds.Any())
            {
                queryable = queryable.Where(x => AdvertiserIds.Contains(x.AdvertiserId));
            }

            var result = _objectMapper.Map<List<RawLatestBalanceAdAccountEntity>, List<BalanceAdAccountDto>>(queryable.ToList());
            _logger.LogDebug("Retrieved {Count} latest balances", result.Count);

            return result;
        }

        private async Task<List<TransactionDto>> GetLatestTransactionsAsync()
        {
            _logger.LogDebug("Getting latest transactions for BcId: {BcId}", BcId);

            var queryable = (await _transactionRepository.GetQueryableAsync())
                .Where(x => x.BcId == BcId);

            if (AdvertiserIds != null && AdvertiserIds.Any())
            {
                queryable = queryable.Where(x => x.AccountId != null && AdvertiserIds.Contains(x.AccountId));
            }

            // Lấy giao dịch cuối cùng cho mỗi tài khoản quảng cáo
            var latestTransactions = queryable
                .GroupBy(x => x.AccountId)
                .Select(g => g.OrderByDescending(x => x.CreateTime).First())
                .ToList();

            var result = _objectMapper.Map<List<RawTransactionEntity>, List<TransactionDto>>(latestTransactions);
            _logger.LogDebug("Retrieved {Count} latest transactions", result.Count);

            return result;
        }

        private async Task<List<LatestNotificationDto>> GetLatestNotificationsAsync()
        {
            _logger.LogDebug("Getting latest notifications for AdvertiserIds: {AdvertiserIds}",
                string.Join(",", AdvertiserIds ?? new List<string>()));

            if (AdvertiserIds == null || AdvertiserIds.Count == 0)
            {
                _logger.LogDebug("No AdvertiserIds provided, returning empty notifications list");
                return new List<LatestNotificationDto>();
            }

            // Lấy thông báo theo Type và danh sách AdvertiserIds
            var notifications = await _latestNotificationRepository.GetByTypeAndObjectIdsAsync(NotificationType.Balance, AdvertiserIds);

            // Map entities to DTOs using AutoMapper
            var result = _objectMapper.Map<List<LatestNotificationEntity>, List<LatestNotificationDto>>(notifications) ?? new List<LatestNotificationDto>();
            _logger.LogDebug("Retrieved {Count} latest notifications", result.Count);

            return result;
        }

        private BalanceEvaluationResult ProcessAll(BalanceEvaluationInput input)
        {
            var decisions = new List<BalanceNotificationDecisionDto>();
            var notificationsToUpsert = new List<LatestNotificationDto>();
            var ruleDefault = input.Rules.FirstOrDefault(x => x.IsDefault);
            foreach (var latestAccountBalances in input.LatestBalanceAdAccounts ?? new List<BalanceAdAccountDto>())
            {
                _logger.LogDebug("Processing advertiser {AdvertiserId} with balance {Balance}",
                    latestAccountBalances.AdvertiserId, latestAccountBalances.ValidCashBalance);

                var applicableRules = GetRulesWithPrecedence(latestAccountBalances, input.Rules, input.RuleAdAccountMap);
                if (!applicableRules.Any())
                {
                    _logger.LogDebug("No applicable rules found for advertiser {AdvertiserId}", latestAccountBalances.AdvertiserId);
                    continue;
                }

                _logger.LogDebug("Found {Count} applicable rules for advertiser {AdvertiserId}",
                    applicableRules.Count, latestAccountBalances.AdvertiserId);
                var rules = new List<RuleDto>();

                // Process each rule individually
                foreach (var rule in applicableRules)
                {
                    var latestNoti = FindLatestNotificationFor(latestAccountBalances.AdvertiserId, input.Notifications, rule.Id);
                    var verdict = ShouldNotifyWithSingleRule(latestAccountBalances, latestNoti, input.Transactions, rule);

                    if (verdict.ShouldSend)
                    {
                        _logger.LogDebug("Decision to send notification for advertiser {AdvertiserId} with rule {RuleId}. Reason: {Reason}",
                            latestAccountBalances.AdvertiserId, rule.Id, verdict.Reason);

                        rules.Add(rule);

                        notificationsToUpsert.Add(
                            CreateNotificationDtoForSingleRule(latestAccountBalances, verdict.LastTransaction, rule)
                        );
                    }
                    else
                    {
                        _logger.LogDebug("No notification needed for advertiser {AdvertiserId} with rule {RuleId}. Reason: {Reason}",
                            latestAccountBalances.AdvertiserId, rule.Id, verdict.Reason);
                    }
                }

                if (rules.Any())
                {

                    decisions.Add(new BalanceNotificationDecisionDto
                    {
                        Id = Guid.NewGuid(),
                        BalanceAdAccount = latestAccountBalances,
                        Rules = rules
                    });
                }
            }

            return new BalanceEvaluationResult
            {
                Decisions = decisions,
                NotificationsToUpsert = notificationsToUpsert,
                RuleDefault = ruleDefault
            };
        }

        private bool CanSend(DateTime? lastNotifiedTime, NotificationFrequency frequency)
        {
            if (!lastNotifiedTime.HasValue) return true;

            var now = DateTime.Now;
            var diffMs = (now - lastNotifiedTime.Value).TotalMilliseconds;

            return frequency switch
            {
                NotificationFrequency.Once => false,
                NotificationFrequency.Daily => diffMs >= 24 * 60 * 60 * 1000,
                NotificationFrequency.Hourly => diffMs >= 60 * 60 * 1000,
                NotificationFrequency.TwoHour => diffMs >= 2 * 60 * 60 * 1000,
                NotificationFrequency.ThirtyMinute => diffMs >= 30 * 60 * 1000,
                NotificationFrequency.TenMinute => diffMs >= 10 * 60 * 1000,
                NotificationFrequency.FiveMinute => diffMs >= 5 * 60 * 1000,
                _ => false
            };
        }

        private string GetStatusText(AdvertiserAccountStatus advertiserStatus)
        {
            return advertiserStatus switch
            {
                AdvertiserAccountStatus.SHOW_ACCOUNT_STATUS_NOT_APPROVED => "NOT_APPROVED",
                AdvertiserAccountStatus.SHOW_ACCOUNT_STATUS_APPROVED => "APPROVED",
                AdvertiserAccountStatus.SHOW_ACCOUNT_STATUS_IN_REVIEW => "IN_REVIEW",
                AdvertiserAccountStatus.SHOW_ACCOUNT_STATUS_PUNISHED => "PUNISHED",
                _ => "UNKNOWN"
            };
        }

        private object GetFieldValue(string field, BalanceAdAccountDto balanceAdAccount)
        {
            return field?.ToLower() switch
            {
                // Advertiser Account Information
                RuleFields.AdvertiserId => balanceAdAccount.AdvertiserId,
                RuleFields.AdvertiserName => balanceAdAccount.AdvertiserName,
                RuleFields.AdvertiserStatus => balanceAdAccount.AdvertiserStatus,
                RuleFields.AdvertiserType => balanceAdAccount.AdvertiserType,
                RuleFields.Timezone => balanceAdAccount.Timezone,
                RuleFields.Currency => balanceAdAccount.Currency,
                RuleFields.AccountOpenDays => balanceAdAccount.AccountOpenDays,
                RuleFields.BalanceReminder => balanceAdAccount.BalanceReminder,
                RuleFields.Company => balanceAdAccount.Company,
                RuleFields.ContactName => balanceAdAccount.ContactName,
                RuleFields.ContactEmail => balanceAdAccount.ContactEmail,
                RuleFields.CreateTime => balanceAdAccount.CreateTime,

                // Balance Information
                RuleFields.AccountBalance => balanceAdAccount.AccountBalance,
                RuleFields.ValidAccountBalance => balanceAdAccount.ValidAccountBalance,
                RuleFields.FrozenBalance => balanceAdAccount.FrozenBalance,
                RuleFields.Tax => balanceAdAccount.Tax,
                RuleFields.CashBalance => balanceAdAccount.CashBalance,
                RuleFields.ValidCashBalance => balanceAdAccount.ValidCashBalance,
                RuleFields.GrantBalance => balanceAdAccount.GrantBalance,
                RuleFields.ValidGrantBalance => balanceAdAccount.ValidGrantBalance,
                RuleFields.TransferableAmount => balanceAdAccount.TransferableAmount,

                // Budget Information
                RuleFields.BudgetMode => balanceAdAccount.BudgetMode,
                RuleFields.Budget => balanceAdAccount.Budget,
                RuleFields.BudgetCost => balanceAdAccount.BudgetCost,
                RuleFields.BudgetRemaining => balanceAdAccount.BudgetRemaining,

                // Business Center
                RuleFields.BcId => balanceAdAccount.BcId,

                // Custom Calculated Fields
                RuleFields.BalanceBudgetRatio => CalculateBalanceBudgetRatio(balanceAdAccount),

                // Legacy fields (keeping for backward compatibility)
                RuleFields.Balance => balanceAdAccount.ValidCashBalance,
                RuleFields.Status => balanceAdAccount.AdvertiserStatus,
                RuleFields.Name => balanceAdAccount.AdvertiserName,
                RuleFields.AccountId => balanceAdAccount.AdvertiserId,

                // Date fields
                RuleFields.Date => balanceAdAccount.Date,

                // Default case - try to get property by name
                _ => balanceAdAccount.GetType().GetProperty(field)?.GetValue(balanceAdAccount)
            };
        }

        /// <summary>
        /// Tính toán tỷ lệ phần trăm của số dư/ngân sách
        /// </summary>
        private decimal CalculateBalanceBudgetRatio(BalanceAdAccountDto balanceAdAccount)
        {
            // Sử dụng ValidCashBalance làm số dư
            var balance = balanceAdAccount.ValidCashBalance;

            // Sử dụng Budget làm ngân sách
            var budget = balanceAdAccount.Budget;

            // Nếu không có ngân sách hoặc ngân sách = 0, trả về 0
            if (budget <= 0)
            {
                return 0;
            }

            // Tính tỷ lệ phần trăm: (số dư / ngân sách) * 100
            var ratio = (balance / budget) * 100;

            // Làm tròn đến 2 chữ số thập phân
            return Math.Round(ratio, 2);
        }

        private bool EvalSimple(string op, object left, object right, string type)
        {
            // Handle null values
            if (left == null && right == null) return true;
            if (left == null || right == null) return false;

            // Type conversion based on specified type
            if (type == RuleConditionTypes.Number)
            {
                if (left.ToString() == "" || right.ToString() == "") return false;

                if (double.TryParse(left.ToString(), out double leftNum) &&
                    double.TryParse(right.ToString(), out double rightNum))
                {
                    left = leftNum;
                    right = rightNum;
                }
                else
                {
                    return false; // Invalid number conversion
                }
            }
            else if (type == RuleConditionTypes.Boolean)
            {
                if (bool.TryParse(left.ToString(), out bool leftBool) &&
                    bool.TryParse(right.ToString(), out bool rightBool))
                {
                    left = leftBool;
                    right = rightBool;
                }
                else
                {
                    return false; // Invalid boolean conversion
                }
            }
            else
            {
                // String type (default)
                left = left.ToString();
                right = right.ToString();
            }

            return op?.ToLower() switch
            {
                RuleOperators.Equal => Equals(left, right),
                RuleOperators.NotEqual => !Equals(left, right),
                RuleOperators.GreaterThan => left is IComparable l && right is IComparable r && l.CompareTo(r) > 0,
                RuleOperators.GreaterThanOrEqual => left is IComparable l1 && right is IComparable r1 && l1.CompareTo(r1) >= 0,
                RuleOperators.LessThan => left is IComparable l2 && right is IComparable r2 && l2.CompareTo(r2) < 0,
                RuleOperators.LessThanOrEqual => left is IComparable l3 && right is IComparable r3 && l3.CompareTo(r3) <= 0,
                RuleOperators.In => right is IEnumerable<object> arr1 && arr1.Contains(left),
                RuleOperators.NotIn => right is IEnumerable<object> arr2 && !arr2.Contains(left),
                RuleOperators.Between => right is IEnumerable<object> arr3 && arr3.Count() >= 2 &&
                             left is IComparable l4 &&
                             l4.CompareTo(Convert.ToDouble(arr3.First())) >= 0 &&
                             l4.CompareTo(Convert.ToDouble(arr3.Skip(1).First())) <= 0,
                RuleOperators.StartsWith => left?.ToString().StartsWith(right?.ToString() ?? "", StringComparison.OrdinalIgnoreCase) == true,
                RuleOperators.EndsWith => left?.ToString().EndsWith(right?.ToString() ?? "", StringComparison.OrdinalIgnoreCase) == true,
                RuleOperators.Contains => left?.ToString().Contains(right?.ToString() ?? "", StringComparison.OrdinalIgnoreCase) == true,
                _ => false
            };
        }

        private bool EvalRuleNode(RuleConditionNode node, BalanceAdAccountDto balanceAdAccount)
        {
            if (node == null) return true;

            // Handle leaf node (has field and operator)
            if (!string.IsNullOrEmpty(node.Field) && !string.IsNullOrEmpty(node.Operator))
            {
                var left = GetFieldValue(node.Field, balanceAdAccount);
                var ok = EvalSimple(node.Operator, left, node.Value, node.Type?.ToLower());
                return node.Not ? !ok : ok;
            }

            // Handle group node (has rules array)
            var cond = (node.Condition ?? LogicalConditions.And).ToLower();
            var rules = node.Rules ?? new List<RuleConditionNode>();

            if (!rules.Any()) return true; // Empty group returns true

            var res = cond == LogicalConditions.Or
                ? rules.Any(r => EvalRuleNode(r, balanceAdAccount))
                : rules.All(r => EvalRuleNode(r, balanceAdAccount));

            return node.Not ? !res : res;
        }

        private List<RuleDto> GetRulesWithPrecedence(BalanceAdAccountDto balanceAdAccount, List<RuleDto> rules, List<RuleAdAccountFlatDto> ruleAdAccountList)
        {
            var bcId = balanceAdAccount.BcId;
            var advertiserId = balanceAdAccount.AdvertiserId;
            var isBalanceTarget = (RuleDto r) => r.TargetEntity == RuleTarget.Balance;
            var ruleOutput = new Dictionary<Guid, RuleDto>();

            var defaultRule = rules.FirstOrDefault(x => x.IsDefault);
            if (defaultRule != null)
            {
                ruleOutput.Add(defaultRule.Id, defaultRule);
            }

            var mapsByRule = new Dictionary<Guid, List<RuleAdAccountMapping>>();
            foreach (var m in ruleAdAccountList ?? new List<RuleAdAccountFlatDto>())
            {
                var rid = m.RuleId;
                if (!mapsByRule.ContainsKey(rid))
                    mapsByRule[rid] = new List<RuleAdAccountMapping>();

                mapsByRule[rid].Add(new RuleAdAccountMapping
                {
                    BcId = m.BcId,
                    AdvertiserId = m.AdvertiserId
                });
            }

            // Direct rules for specific advertiser
            var directs = (rules ?? new List<RuleDto>())
                .Where(IS_BALANCE_TARGET)
                .Where(r => mapsByRule.ContainsKey(r.Id) &&
                           mapsByRule[r.Id].Any(x => !string.IsNullOrEmpty(x.AdvertiserId) && x.AdvertiserId == advertiserId))
                .ToList();
            if (directs.Any())
            {
                foreach (var item in directs)
                {
                    if (!ruleOutput.ContainsKey(item.Id))
                    {
                        ruleOutput.Add(item.Id, item);
                    }
                }
            }

            // BC-level rules
            //var bcRules = (rules ?? new List<RuleDto>())
            //    .Where(IS_BALANCE_TARGET)
            //    .Where(r => mapsByRule.ContainsKey(r.Id) &&
            //               mapsByRule[r.Id].Any(x => string.IsNullOrEmpty(x.AdvertiserId) && x.BcId == bcId))
            //    .ToList();
            //if (bcRules.Any())
            //{
            //    _logger.LogDebug("Found {Count} BC-level rules for BcId {BcId}", bcRules.Count, bcId);
            //    return bcRules;
            //}

            _logger.LogDebug("No applicable rules found for advertiser {AdvertiserId} in BcId {BcId}", advertiserId, bcId);
            return ruleOutput.Values.ToList();
        }

        private LatestNotificationDto FindLatestNotificationFor(string advertiserId, List<LatestNotificationDto> notifications, Guid ruleId)
        {
            // Tìm thông báo theo cả ObjectId và RuleId (trường Rule trong entity)
            var latest = (notifications ?? new List<LatestNotificationDto>())
                .Where(n => n.ObjectId == advertiserId && n.Rule == ruleId.ToString())
                .OrderByDescending(n => n.LastNotifiedTime)
                .FirstOrDefault();

            return latest;
        }

        private NotificationVerdict ShouldNotifyWithSingleRule(BalanceAdAccountDto balanceAdAccountDto, LatestNotificationDto latestNotification,
            List<TransactionDto> transactions, RuleDto rule)
        {
            // Evaluate single rule condition
            RuleConditionNode conditionJson = null;
            try
            {
                if (!string.IsNullOrEmpty(rule.Condition))
                {
                    conditionJson = JsonConvert.DeserializeObject<RuleConditionNode>(rule.Condition);
                    _logger.LogDebug("Deserialized condition for rule {RuleId}: {Condition}", rule.Id, rule.Condition);

                    // Validate rule condition structure
                    if (!RuleValidationHelper.IsValidRuleCondition(conditionJson))
                    {
                        _logger.LogWarning("Invalid rule condition structure for rule {RuleId}", rule.Id);
                        return new NotificationVerdict { ShouldSend = false, Reason = $"Invalid condition structure for rule {rule.RuleName}" };
                    }
                }
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Failed to deserialize condition for rule {RuleId}: {Condition}", rule.Id, rule.Condition);
                return new NotificationVerdict { ShouldSend = false, Reason = $"Invalid condition format for rule {rule.RuleName}" };
            }

            if (conditionJson != null)
            {
                var ruleResult = EvalRuleNode(conditionJson, balanceAdAccountDto);
                _logger.LogDebug("Rule {RuleId} evaluation result: {Result} for advertiser {AdvertiserId}",
                    rule.Id, ruleResult, balanceAdAccountDto.AdvertiserId);

                if (!ruleResult)
                {
                    return new NotificationVerdict { ShouldSend = false, Reason = $"Condition not satisfied for rule {rule.RuleName}" };
                }
            }

            // Giao dịch mới nhất cho account
            var accTx = (transactions ?? new List<TransactionDto>())
                .Where(t => t.AccountId == balanceAdAccountDto.AdvertiserId)
                .OrderByDescending(t => t.CreateTime)
                .FirstOrDefault();

            var isApproved = balanceAdAccountDto.AdvertiserStatus == AdvertiserAccountStatus.SHOW_ACCOUNT_STATUS_APPROVED;
            var statusText = GetStatusText(balanceAdAccountDto.AdvertiserStatus);

            // Check frequency for single rule
            var frequency = rule.NotificationFrequency;
            var hasOnce = frequency == NotificationFrequency.Once;

            if (latestNotification == null)
            {
                return new NotificationVerdict
                {
                    ShouldSend = true,
                    LastTransaction = accTx,
                    Reason = isApproved ? $"First (Rule {rule.RuleName})" : $"First for {statusText} (Rule {rule.RuleName})"
                };
            }

            var lastTime = latestNotification.LastNotifiedTime;
            var canSend = CanSend(lastTime, frequency);
            if (!canSend)
            {
                return new NotificationVerdict { ShouldSend = false, Reason = $"Too frequent for rule {rule.RuleName}" };
            }

            if (!isApproved)
            {
                if (!string.IsNullOrEmpty(latestNotification.Payload))
                {
                    try
                    {
                        var p = DeserializePayload(latestNotification.Payload);
                        if (p?.BalanceAdAccount?.AdvertiserStatus == balanceAdAccountDto.AdvertiserStatus)
                        {
                            return new NotificationVerdict { ShouldSend = false, Reason = $"Already notified for {statusText} (Rule {rule.RuleName})" };
                        }
                        return new NotificationVerdict
                        {
                            ShouldSend = true,
                            LastTransaction = accTx,
                            Reason = $"Status changed to {statusText} (Rule {rule.RuleName})"
                        };
                    }
                    catch
                    {
                        return new NotificationVerdict
                        {
                            ShouldSend = true,
                            LastTransaction = accTx,
                            Reason = $"Invalid payload (Rule {rule.RuleName})"
                        };
                    }
                }
                return new NotificationVerdict
                {
                    ShouldSend = true,
                    LastTransaction = accTx,
                    Reason = $"No payload (Rule {rule.RuleName})"
                };
            }

            // Once-mode: cần giao dịch mới
            if (hasOnce)
            {
                if (!string.IsNullOrEmpty(latestNotification.Payload))
                {
                    try
                    {
                        var p = DeserializePayload(latestNotification.Payload);
                        if (accTx == null || accTx.TransactionId == p?.LastTransactionId)
                        {
                            return new NotificationVerdict { ShouldSend = false, Reason = $"No new transaction (Once-mode, Rule {rule.RuleName})" };
                        }
                        return new NotificationVerdict
                        {
                            ShouldSend = true,
                            LastTransaction = accTx,
                            Reason = $"New transaction (Once-mode, Rule {rule.RuleName})"
                        };
                    }
                    catch
                    {
                        return new NotificationVerdict
                        {
                            ShouldSend = true,
                            LastTransaction = accTx,
                            Reason = $"Invalid payload (Once-mode, Rule {rule.RuleName})"
                        };
                    }
                }
                return new NotificationVerdict
                {
                    ShouldSend = true,
                    LastTransaction = accTx,
                    Reason = $"No payload (Once-mode, Rule {rule.RuleName})"
                };
            }

            // Định kỳ
            if (!string.IsNullOrEmpty(latestNotification.Payload))
            {
                try
                {
                    var p = DeserializePayload(latestNotification.Payload);
                    var lastTxId = p?.LastTransactionId;
                    var hasNewTx = accTx != null && accTx.TransactionId != lastTxId;
                    return new NotificationVerdict
                    {
                        ShouldSend = true,
                        LastTransaction = accTx,
                        Reason = hasNewTx ? $"New transaction (Periodic, Rule {rule.RuleName})" : $"Periodic (Rule {rule.RuleName})"
                    };
                }
                catch
                {
                    return new NotificationVerdict
                    {
                        ShouldSend = true,
                        LastTransaction = accTx,
                        Reason = $"Invalid payload (Periodic, Rule {rule.RuleName})"
                    };
                }
            }
            return new NotificationVerdict
            {
                ShouldSend = true,
                LastTransaction = accTx,
                Reason = $"Periodic(no payload, Rule {rule.RuleName})"
            };
        }

        private LatestNotificationDto CreateNotificationDtoForSingleRule(BalanceAdAccountDto balanceAdAccount, TransactionDto lastTransaction, RuleDto rule)
        {
            var payload = new NotificationPayload
            {
                Rule = rule, // Single rule
                BalanceAdAccount = balanceAdAccount,
                LastTransactionId = lastTransaction?.TransactionId,
                NotificationTime = DateTime.Now
            };

            // Create rule summary for backward compatibility
            var ruleSummary = rule.NotificationFrequency.ToString() ?? "";

            return new LatestNotificationDto
            {
                Id = Guid.NewGuid(),
                ObjectId = balanceAdAccount.AdvertiserId,
                Type = NotificationType.Balance,
                Payload = JsonConvert.SerializeObject(payload),
                Rule = rule.Id.ToString(), // Store RuleId as string
                LastNotifiedTime = DateTime.Now,
                CreationTime = DateTime.Now,
            };
        }

        /// <summary>
        /// Deserialize notification payload with backward compatibility
        /// </summary>
        private NotificationPayload DeserializePayload(string payloadJson)
        {
            if (string.IsNullOrEmpty(payloadJson))
                return null;

            try
            {
                // Try to deserialize as new format first (single rule)
                return JsonConvert.DeserializeObject<NotificationPayload>(payloadJson);
            }
            catch (JsonException)
            {

            }

            return null;
        }
    }

    // Supporting classes
    public class BalanceEvaluationInput
    {
        public List<BalanceAdAccountDto> LatestBalanceAdAccounts { get; set; } = new List<BalanceAdAccountDto>();
        public List<LatestNotificationDto> Notifications { get; set; } = new List<LatestNotificationDto>();
        public List<TransactionDto> Transactions { get; set; } = new List<TransactionDto>();
        public List<RuleDto> Rules { get; set; } = new List<RuleDto>();
        public List<RuleAdAccountFlatDto> RuleAdAccountMap { get; set; } = new List<RuleAdAccountFlatDto>();
    }

    public class BalanceEvaluationResult
    {
        public List<BalanceNotificationDecisionDto> Decisions { get; set; } = new List<BalanceNotificationDecisionDto>();
        public List<LatestNotificationDto> NotificationsToUpsert { get; set; } = new List<LatestNotificationDto>();

        public RuleDto? RuleDefault { get; set; }
    }

    public class NotificationVerdict
    {
        public bool ShouldSend { get; set; }
        public string Reason { get; set; }
        public TransactionDto LastTransaction { get; set; }
    }

    public class RuleAdAccountMapping
    {
        public string BcId { get; set; }
        public string AdvertiserId { get; set; }
    }

    public class RuleConditionNode
    {
        public string Field { get; set; }
        public string Operator { get; set; }
        public object Value { get; set; }
        public string Type { get; set; }
        public bool Not { get; set; }
        public string Condition { get; set; } // "and" or "or"
        public List<RuleConditionNode> Rules { get; set; } = new List<RuleConditionNode>();
        public string Label { get; set; } // For display purposes
        public bool IsLocked { get; set; } // Whether rule is locked
    }

    public class NotificationPayload
    {
        public RuleDto Rule { get; set; }
        public BalanceAdAccountDto BalanceAdAccount { get; set; }
        public string LastTransactionId { get; set; }
        public DateTime NotificationTime { get; set; }
    }

    public static class RuleValidationHelper
    {
        public static bool IsValidRuleCondition(RuleConditionNode node)
        {
            if (node == null) return false;

            // Leaf node validation
            if (!string.IsNullOrEmpty(node.Field) && !string.IsNullOrEmpty(node.Operator))
            {
                return IsValidOperator(node.Operator) && IsValidField(node.Field);
            }

            // Group node validation
            if (!string.IsNullOrEmpty(node.Condition) && (node.Rules?.Any() == true))
            {
                return IsValidCondition(node.Condition) &&
                       node.Rules.All(IsValidRuleCondition);
            }

            return false;
        }

        private static bool IsValidOperator(string op)
        {
            var validOperators = new[]
            {
                RuleOperators.Equal, RuleOperators.NotEqual, RuleOperators.GreaterThan, RuleOperators.GreaterThanOrEqual,
                RuleOperators.LessThan, RuleOperators.LessThanOrEqual, RuleOperators.In, RuleOperators.NotIn, RuleOperators.Between,
                RuleOperators.StartsWith, RuleOperators.EndsWith, RuleOperators.Contains
            };
            return validOperators.Contains(op?.ToLower());
        }

        private static bool IsValidField(string field)
        {
            var validFields = new[]
            {
                // Advertiser Account Information
                RuleFields.AdvertiserId, RuleFields.AdvertiserName, RuleFields.AdvertiserStatus, RuleFields.AdvertiserType,
                RuleFields.Timezone, RuleFields.Currency, RuleFields.AccountOpenDays, RuleFields.BalanceReminder,
                RuleFields.Company, RuleFields.ContactName, RuleFields.ContactEmail, RuleFields.CreateTime,
                
                // Balance Information
                RuleFields.AccountBalance, RuleFields.ValidAccountBalance, RuleFields.FrozenBalance, RuleFields.Tax,
                RuleFields.CashBalance, RuleFields.ValidCashBalance, RuleFields.GrantBalance, RuleFields.ValidGrantBalance,
                RuleFields.TransferableAmount,
                
                // Budget Information
                RuleFields.BudgetMode, RuleFields.Budget, RuleFields.BudgetCost, RuleFields.BudgetRemaining,
                
                // Business Center
                RuleFields.BcId,
                
                // Custom Calculated Fields
                RuleFields.BalanceBudgetRatio,
                
                // Legacy fields (keeping for backward compatibility)
                RuleFields.Balance, RuleFields.Status, RuleFields.Name, RuleFields.AccountId,
                
                // Date fields
                RuleFields.Date
            };
            return validFields.Contains(field?.ToLower());
        }

        private static bool IsValidCondition(string condition)
        {
            return condition?.ToLower() == LogicalConditions.And || condition?.ToLower() == LogicalConditions.Or;
        }
    }
}