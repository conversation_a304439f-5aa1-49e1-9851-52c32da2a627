using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.JobManagement
{
    /// <summary>
    /// DTO cho cấu hình job
    /// </summary>
    public class JobConfigurationDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// Tên cấu hình
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// <PERSON>ô tả cấu hình
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Trạng thái hoạt động (active/inactive)
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Cron expression cho Manager Job
        /// </summary>
        public string ManagerJobCron { get; set; }

        /// <summary>
        /// Cron expression cho Register Job
        /// </summary>
        public string RegisterJobCron { get; set; }

        /// <summary>
        /// Thời gian timeout của worker (phút)
        /// </summary>
        public int WorkerTimeoutMinutes { get; set; }

        /// <summary>
        /// Số lượng worker tối đa
        /// </summary>
        public int MaxWorkers { get; set; }


    }
}