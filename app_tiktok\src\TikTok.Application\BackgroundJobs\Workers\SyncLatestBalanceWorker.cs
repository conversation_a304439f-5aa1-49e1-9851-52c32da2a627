using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Application.Contracts.MessageProviders;
using TikTok.Repositories;
using System.Collections.Generic;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Worker để đồng bộ ngân sách mới nhất (Latest Balance)
    /// </summary>
    public class SyncLatestBalanceWorker : WorkerJobBase
    {
        private readonly ILatestBalanceSyncService _latestBalanceSyncService;
        private readonly INotificationService _notificationService;

        public SyncLatestBalanceWorker(
            ILogger<SyncLatestBalanceWorker> logger,
            IServiceProvider serviceProvider,
            ILatestBalanceSyncService latestBalanceSyncService,
            INotificationService notificationService)
            : base(logger, serviceProvider)
        {
            _latestBalanceSyncService = latestBalanceSyncService;
            _notificationService = notificationService;
        }

        public override CommandType CommandType => CommandType.SyncLatestBalance;

        protected override async Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job)
        {
            // Parse parameters
            var parameters = ParseParameters(args.Parameters);

            // Sync Latest Balance based on parameters
            LatestBalanceSyncResult responseSync;

            if (string.IsNullOrEmpty(parameters.BcId))
            {
                // Sync all Business Centers
                responseSync = await _latestBalanceSyncService.SyncAllLatestBalancesForAllBcsAsync();
            }
            else
            {
                // Sync specific Business Center
                responseSync = await _latestBalanceSyncService.SyncAllLatestBalancesAsync(parameters.BcId);
            }

            if (responseSync != null)
            {
                if (responseSync.IsSuccess)
                {
                    var addvertiserIds = responseSync.AdvertiserIds;
                    if (addvertiserIds != null && addvertiserIds.Any())
                    {
                        // paging send notification with page size 500
                        int pageSize = MAX_PAGE_SIZE;
                        int totalPages = (int)Math.Ceiling((double)addvertiserIds.Count / pageSize);
                        for (int page = 0; page < totalPages; page++)
                        {
                            try
                            {
                                var addvertiserIdsChunk = addvertiserIds.Skip(page * pageSize).Take(pageSize).ToList();
                                var payload = JsonConvert.SerializeObject(new
                                {
                                    Name = "Notification Balance",
                                    PageSize = pageSize,
                                    PageNumber = page + 1,
                                    TotalPages = totalPages,
                                    TotalItems = addvertiserIds.Count,
                                    Items = addvertiserIdsChunk
                                });

                                var notificationDto = SendNotificationDto.CreateBalanceNotification(
                                    parameters.BcId,
                                    addvertiserIdsChunk,
                                    payload
                                );

                                await _notificationService.SendNotificationAsync(notificationDto);
                                _logger.LogDebug("Đã gửi thông báo số dư cho {AdvertiserIdsCount} AdvertiserIds", addvertiserIdsChunk.Count);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Lỗi khi gửi thông báo số dư cho trang {PageNumber} của {TotalPages} trang", page + 1, totalPages);
                            }
                        }
                    }

                    var resultMessage = $"Đồng bộ thành công: {responseSync.BcCount} BC, {responseSync.AdAccountCount} AdAccount";
                    return JobResult.Success(JsonConvert.SerializeObject(new
                    {
                        message = resultMessage,
                        data = responseSync
                    }));
                }
                else
                {
                    return JobResult.Error(responseSync.ErrorMessage ?? "Lỗi đồng bộ ngân sách mới nhất",
                        JsonConvert.SerializeObject(responseSync));
                }
            }
            else
            {
                return JobResult.Error("Lỗi đồng bộ ngân sách mới nhất");
            }
        }
    }
}