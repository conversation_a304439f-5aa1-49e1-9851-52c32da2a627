using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.FactGmvMaxCampaigns.Dtos
{
    public class FactGmvMaxCampaignDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// Foreign Key đến Dim_Date
        /// </summary>
        public int DimDateId { get; set; }

        /// <summary>
        /// Foreign Key đến Dim_BusinessCenter
        /// </summary>
        public Guid DimBusinessCenterId { get; set; }

        /// <summary>
        /// Foreign Key đến Dim_AdAccount
        /// </summary>
        public Guid DimAdAccountId { get; set; }

        /// <summary>
        /// Foreign Key đến Dim_Campaign
        /// </summary>
        public Guid DimCampaignId { get; set; }

        /// <summary>
        /// Foreign Key đến Dim_Store (TikTok Shop)
        /// </summary>
        public Guid DimStoreId { get; set; }

        /// <summary>
        /// Business Key - Campaign ID từ TikTok
        /// </summary>
        public string CampaignId { get; set; }

        /// <summary>
        /// Business Key - Store ID từ TikTok Shop
        /// </summary>
        public string StoreId { get; set; }

        /// <summary>
        /// Business Key - Business Center ID từ TikTok
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// Business Key - Advertiser ID từ TikTok
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// Tên chiến dịch
        /// </summary>
        public string? CampaignName { get; set; }

        /// <summary>
        /// Loại GMV Max Campaign (PRODUCT, LIVE)
        /// </summary>
        public string ShoppingAdsType { get; set; }

        /// <summary>
        /// Trạng thái hoạt động (ENABLE, DISABLE)
        /// </summary>
        public string? OperationStatus { get; set; }

        /// <summary>
        /// Loại đấu giá (CUSTOM: Target ROI, NO_BID: Maximum delivery)
        /// </summary>
        public string? BidType { get; set; }

        /// <summary>
        /// Mục tiêu ROAS
        /// </summary>
        public decimal? RoasBid { get; set; }

        /// <summary>
        /// Mục tiêu ROAS (VND)
        /// </summary>
        public decimal? RoasBidVND { get; set; }

        /// <summary>
        /// Mục tiêu ROAS (USD)
        /// </summary>
        public decimal? RoasBidUSD { get; set; }

        /// <summary>
        /// Ngân sách Target ROI
        /// </summary>
        public decimal? TargetRoiBudget { get; set; }

        /// <summary>
        /// Ngân sách Target ROI (VND)
        /// </summary>
        public decimal? TargetRoiBudgetVND { get; set; }

        /// <summary>
        /// Ngân sách Target ROI (USD)
        /// </summary>
        public decimal? TargetRoiBudgetUSD { get; set; }

        /// <summary>
        /// Ngân sách Maximum delivery
        /// </summary>
        public decimal? MaxDeliveryBudget { get; set; }

        /// <summary>
        /// Ngân sách Maximum delivery (VND)
        /// </summary>
        public decimal? MaxDeliveryBudgetVND { get; set; }

        /// <summary>
        /// Ngân sách Maximum delivery (USD)
        /// </summary>
        public decimal? MaxDeliveryBudgetUSD { get; set; }

        /// <summary>
        /// Chi phí quảng cáo (Cost)
        /// </summary>
        public decimal Cost { get; set; }

        /// <summary>
        /// Chi phí quảng cáo (Cost) (VND)
        /// </summary>
        public decimal? CostVND { get; set; }

        /// <summary>
        /// Chi phí quảng cáo (Cost) (USD)
        /// </summary>
        public decimal? CostUSD { get; set; }

        /// <summary>
        /// Chi phí thực tế (Net Cost)
        /// </summary>
        public decimal? NetCost { get; set; }

        /// <summary>
        /// Chi phí thực tế (Net Cost) (VND)
        /// </summary>
        public decimal? NetCostVND { get; set; }

        /// <summary>
        /// Chi phí thực tế (Net Cost) (USD)
        /// </summary>
        public decimal? NetCostUSD { get; set; }

        /// <summary>
        /// Số lượng đơn hàng (Orders)
        /// </summary>
        public int Orders { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi đơn hàng (CPO)
        /// </summary>
        public decimal? CostPerOrder { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi đơn hàng (CPO) (VND)
        /// </summary>
        public decimal? CostPerOrderVND { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi đơn hàng (CPO) (USD)
        /// </summary>
        public decimal? CostPerOrderUSD { get; set; }

        /// <summary>
        /// Tổng doanh thu (Gross Revenue)
        /// </summary>
        public decimal GrossRevenue { get; set; }

        /// <summary>
        /// Tổng doanh thu (Gross Revenue) (VND)
        /// </summary>
        public decimal? GrossRevenueVND { get; set; }

        /// <summary>
        /// Tổng doanh thu (Gross Revenue) (USD)
        /// </summary>
        public decimal? GrossRevenueUSD { get; set; }

        /// <summary>
        /// ROAS (Return on Ad Spend) - Hiệu quả quảng cáo
        /// </summary>
        public decimal? ROAS { get; set; }

        /// <summary>
        /// TACOS (True ACOS) - Hiệu quả quảng cáo so với tổng doanh thu
        /// </summary>
        public decimal? TACOS { get; set; }

        // === LIVE CAMPAIGN SPECIFIC FIELDS ===

        /// <summary>
        /// Tên TikTok account (chỉ có cho LIVE campaigns)
        /// </summary>
        public string? TtAccountName { get; set; }

        /// <summary>
        /// URL hình đại diện TikTok account (chỉ có cho LIVE campaigns)
        /// </summary>
        public string? TtAccountProfileImageUrl { get; set; }

        /// <summary>
        /// Identity ID (TikTok account ID) (chỉ có cho LIVE campaigns)
        /// </summary>
        public string? IdentityId { get; set; }

        /// <summary>
        /// Số lượt xem LIVE (chỉ có cho LIVE campaigns)
        /// </summary>
        public long? LiveViews { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi lượt xem LIVE (chỉ có cho LIVE campaigns)
        /// </summary>
        public decimal? CostPerLiveView { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi lượt xem LIVE (VND) (chỉ có cho LIVE campaigns)
        /// </summary>
        public decimal? CostPerLiveViewVND { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi lượt xem LIVE (USD) (chỉ có cho LIVE campaigns)
        /// </summary>
        public decimal? CostPerLiveViewUSD { get; set; }

        /// <summary>
        /// Số lượt xem LIVE ít nhất 10 giây (chỉ có cho LIVE campaigns)
        /// </summary>
        public long? TenSecondLiveViews { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi lượt xem LIVE 10 giây (chỉ có cho LIVE campaigns)
        /// </summary>
        public decimal? CostPerTenSecondLiveView { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi lượt xem LIVE 10 giây (VND) (chỉ có cho LIVE campaigns)
        /// </summary>
        public decimal? CostPerTenSecondLiveViewVND { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi lượt xem LIVE 10 giây (USD) (chỉ có cho LIVE campaigns)
        /// </summary>
        public decimal? CostPerTenSecondLiveViewUSD { get; set; }

        /// <summary>
        /// Số lượt follow profile trong quá trình LIVE (chỉ có cho LIVE campaigns)
        /// </summary>
        public int? LiveFollows { get; set; }

        // === CAMPAIGN CONFIGURATION FIELDS ===

        /// <summary>
        /// Loại lịch trình (Continuously)
        /// </summary>
        public string? ScheduleType { get; set; }

        /// <summary>
        /// Thời gian bắt đầu chiến dịch (UTC)
        /// </summary>
        public DateTime? ScheduleStartTime { get; set; }

        /// <summary>
        /// Thời gian kết thúc chiến dịch (UTC)
        /// </summary>
        public DateTime? ScheduleEndTime { get; set; }

        /// <summary>
        /// Tiền tệ
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Ngày báo cáo UTC format yyyy-MM-dd 00:00:00 (theo ngày)
        /// </summary>
        public DateTime Date { get; set; }
    }
}
