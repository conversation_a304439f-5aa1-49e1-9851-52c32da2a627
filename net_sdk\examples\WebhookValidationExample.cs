/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Models;
using TikTokBusinessApi.Utilities;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to validate TikTok webhook signatures and parse webhook events
    /// </summary>
    public class WebhookValidationExample
    {
        private readonly WebhookValidator _webhookValidator;
        private readonly ILogger<WebhookValidationExample> _logger;

        public WebhookValidationExample(ILogger<WebhookValidationExample> logger)
        {
            _logger = logger;
            _webhookValidator = new WebhookValidator();
        }

        /// <summary>
        /// Example of validating a webhook signature and parsing the event
        /// </summary>
        /// <param name="payload">The raw webhook payload</param>
        /// <param name="signature">The X-TikTok-Signature header value</param>
        /// <param name="timestamp">The X-TikTok-Timestamp header value</param>
        /// <param name="webhookSecret">Your webhook secret</param>
        /// <returns>Task representing the async operation</returns>
        public async Task HandleWebhookAsync(string payload, string signature, string timestamp, string webhookSecret)
        {
            try
            {
                Console.WriteLine("=== TikTok Webhook Validation Example ===");

                // Step 1: Validate the webhook signature
                var isValid = _webhookValidator.ValidateSignature(payload, signature, webhookSecret, timestamp);

                if (!isValid)
                {
                    _logger.LogWarning("Invalid webhook signature received");
                    Console.WriteLine("❌ Webhook signature validation failed");
                    return;
                }

                Console.WriteLine("✅ Webhook signature validated successfully");

                // Step 2: Parse the webhook event to determine the event type
                // First, try to parse as a generic webhook event to get the event type
                var baseEvent = _webhookValidator.ParseWebhookEvent<WebhookEventBase>(payload);
                Console.WriteLine($"📧 Received webhook event: {baseEvent.EventType}");

                // Step 3: Parse the specific event type based on the event_type field
                await ProcessWebhookEventAsync(payload, baseEvent.EventType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing webhook");
                Console.WriteLine($"❌ Error processing webhook: {ex.Message}");
            }
        }

        /// <summary>
        /// Process different types of webhook events
        /// </summary>
        /// <param name="payload">The webhook payload</param>
        /// <param name="eventType">The event type</param>
        /// <returns>Task representing the async operation</returns>
        private async Task ProcessWebhookEventAsync(string payload, string eventType)
        {
            switch (eventType)
            {
                case WebhookEventTypes.CampaignCreation:
                case WebhookEventTypes.CampaignUpdate:
                case WebhookEventTypes.CampaignDeletion:
                    await ProcessCampaignEventAsync(payload, eventType);
                    break;

                case WebhookEventTypes.AdGroupCreation:
                case WebhookEventTypes.AdGroupUpdate:
                case WebhookEventTypes.AdGroupDeletion:
                    await ProcessAdGroupEventAsync(payload, eventType);
                    break;

                case WebhookEventTypes.AdCreation:
                case WebhookEventTypes.AdUpdate:
                case WebhookEventTypes.AdDeletion:
                    await ProcessAdEventAsync(payload, eventType);
                    break;

                case WebhookEventTypes.CommentCreation:
                case WebhookEventTypes.CommentUpdate:
                case WebhookEventTypes.CommentDeletion:
                    await ProcessCommentEventAsync(payload, eventType);
                    break;

                case WebhookEventTypes.AdvertiserAccountCreation:
                case WebhookEventTypes.AdvertiserAccountUpdate:
                case WebhookEventTypes.AdvertiserAccountDeletion:
                    await ProcessAdvertiserAccountEventAsync(payload, eventType);
                    break;

                default:
                    _logger.LogWarning("Unknown webhook event type: {EventType}", eventType);
                    Console.WriteLine($"⚠️ Unknown event type: {eventType}");
                    break;
            }
        }

        /// <summary>
        /// Process campaign-related webhook events
        /// </summary>
        private async Task ProcessCampaignEventAsync(string payload, string eventType)
        {
            var campaignEvent = _webhookValidator.ParseWebhookEvent<CampaignWebhookEvent>(payload);
            
            Console.WriteLine($"🎯 Campaign Event: {eventType}");
            Console.WriteLine($"   Campaign ID: {campaignEvent.Data.CampaignId}");
            Console.WriteLine($"   Campaign Name: {campaignEvent.Data.CampaignName}");
            Console.WriteLine($"   Advertiser ID: {campaignEvent.Data.AdvertiserId}");
            Console.WriteLine($"   Status: {campaignEvent.Data.Status}");
            Console.WriteLine($"   Objective: {campaignEvent.Data.ObjectiveType}");

            // Add your business logic here
            switch (eventType)
            {
                case WebhookEventTypes.CampaignCreation:
                    await OnCampaignCreatedAsync(campaignEvent);
                    break;
                case WebhookEventTypes.CampaignUpdate:
                    await OnCampaignUpdatedAsync(campaignEvent);
                    break;
                case WebhookEventTypes.CampaignDeletion:
                    await OnCampaignDeletedAsync(campaignEvent);
                    break;
            }
        }

        /// <summary>
        /// Process ad group-related webhook events
        /// </summary>
        private async Task ProcessAdGroupEventAsync(string payload, string eventType)
        {
            var adGroupEvent = _webhookValidator.ParseWebhookEvent<AdGroupWebhookEvent>(payload);
            
            Console.WriteLine($"📊 Ad Group Event: {eventType}");
            Console.WriteLine($"   Ad Group ID: {adGroupEvent.Data.AdGroupId}");
            Console.WriteLine($"   Ad Group Name: {adGroupEvent.Data.AdGroupName}");
            Console.WriteLine($"   Campaign ID: {adGroupEvent.Data.CampaignId}");
            Console.WriteLine($"   Status: {adGroupEvent.Data.Status}");
            Console.WriteLine($"   Optimization Event: {adGroupEvent.Data.OptimizationEvent}");

            // Add your business logic here
            await Task.CompletedTask; // Placeholder for actual implementation
        }

        /// <summary>
        /// Process ad-related webhook events
        /// </summary>
        private async Task ProcessAdEventAsync(string payload, string eventType)
        {
            var adEvent = _webhookValidator.ParseWebhookEvent<AdWebhookEvent>(payload);
            
            Console.WriteLine($"📢 Ad Event: {eventType}");
            Console.WriteLine($"   Ad ID: {adEvent.Data.AdId}");
            Console.WriteLine($"   Ad Name: {adEvent.Data.AdName}");
            Console.WriteLine($"   Ad Group ID: {adEvent.Data.AdGroupId}");
            Console.WriteLine($"   Status: {adEvent.Data.Status}");

            // Add your business logic here
            await Task.CompletedTask; // Placeholder for actual implementation
        }

        /// <summary>
        /// Process comment-related webhook events
        /// </summary>
        private async Task ProcessCommentEventAsync(string payload, string eventType)
        {
            var commentEvent = _webhookValidator.ParseWebhookEvent<CommentWebhookEvent>(payload);
            
            Console.WriteLine($"💬 Comment Event: {eventType}");
            Console.WriteLine($"   Comment ID: {commentEvent.Data.CommentId}");
            Console.WriteLine($"   Video ID: {commentEvent.Data.VideoId}");
            Console.WriteLine($"   User: {commentEvent.Data.Username}");
            Console.WriteLine($"   Text: {commentEvent.Data.Text}");

            // Add your business logic here
            await Task.CompletedTask; // Placeholder for actual implementation
        }

        /// <summary>
        /// Process advertiser account-related webhook events
        /// </summary>
        private async Task ProcessAdvertiserAccountEventAsync(string payload, string eventType)
        {
            var accountEvent = _webhookValidator.ParseWebhookEvent<AdvertiserAccountWebhookEvent>(payload);
            
            Console.WriteLine($"🏢 Advertiser Account Event: {eventType}");
            Console.WriteLine($"   Advertiser ID: {accountEvent.Data.AdvertiserId}");
            Console.WriteLine($"   Advertiser Name: {accountEvent.Data.AdvertiserName}");
            Console.WriteLine($"   Status: {accountEvent.Data.Status}");
            Console.WriteLine($"   Currency: {accountEvent.Data.Currency}");

            // Add your business logic here
            await Task.CompletedTask; // Placeholder for actual implementation
        }

        /// <summary>
        /// Handle campaign creation events
        /// </summary>
        private async Task OnCampaignCreatedAsync(CampaignWebhookEvent campaignEvent)
        {
            _logger.LogInformation("Campaign created: {CampaignId}", campaignEvent.Data.CampaignId);
            
            // Example: Send notification, update database, trigger workflows, etc.
            Console.WriteLine("   ✅ Campaign creation processed");
            
            await Task.CompletedTask; // Placeholder for actual implementation
        }

        /// <summary>
        /// Handle campaign update events
        /// </summary>
        private async Task OnCampaignUpdatedAsync(CampaignWebhookEvent campaignEvent)
        {
            _logger.LogInformation("Campaign updated: {CampaignId}", campaignEvent.Data.CampaignId);
            
            // Example: Update local cache, sync with external systems, etc.
            Console.WriteLine("   🔄 Campaign update processed");
            
            await Task.CompletedTask; // Placeholder for actual implementation
        }

        /// <summary>
        /// Handle campaign deletion events
        /// </summary>
        private async Task OnCampaignDeletedAsync(CampaignWebhookEvent campaignEvent)
        {
            _logger.LogInformation("Campaign deleted: {CampaignId}", campaignEvent.Data.CampaignId);
            
            // Example: Clean up related data, archive records, etc.
            Console.WriteLine("   🗑️ Campaign deletion processed");
            
            await Task.CompletedTask; // Placeholder for actual implementation
        }

        /// <summary>
        /// Example of how to use the webhook validator in an ASP.NET Core controller
        /// </summary>
        public void AspNetCoreControllerExample()
        {
            Console.WriteLine("\n=== ASP.NET Core Controller Example ===");
            Console.WriteLine(@"
[ApiController]
[Route(""api/[controller]"")]
public class WebhookController : ControllerBase
{
    private readonly WebhookValidator _webhookValidator;
    private readonly IConfiguration _configuration;

    public WebhookController(WebhookValidator webhookValidator, IConfiguration configuration)
    {
        _webhookValidator = webhookValidator;
        _configuration = configuration;
    }

    [HttpPost(""tiktok"")]
    public async Task<IActionResult> HandleTikTokWebhook()
    {
        // Read the raw request body
        using var reader = new StreamReader(Request.Body);
        var payload = await reader.ReadToEndAsync();

        // Get headers
        var signature = Request.Headers[""X-TikTok-Signature""].FirstOrDefault();
        var timestamp = Request.Headers[""X-TikTok-Timestamp""].FirstOrDefault();
        var webhookSecret = _configuration[""TikTok:WebhookSecret""];

        // Validate the webhook
        if (!_webhookValidator.ValidateSignature(payload, signature, webhookSecret, timestamp))
        {
            return Unauthorized(""Invalid signature"");
        }

        // Process the webhook event
        await ProcessWebhookEventAsync(payload);

        return Ok();
    }
}");
        }
    }
}
