/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Brand Safety operations
    /// </summary>
    public interface IBrandSafetyApi
    {
        /// <summary>
        /// Get the Brand Safety Hub settings of an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing brand safety settings</returns>
        Task<Response<BrandSafetyResponse>> GetBrandSafetySettingsAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Set or update the Brand Safety Hub settings of an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing brand safety settings to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated brand safety settings</returns>
        //Task<Response<BrandSafetyResponse>> UpdateBrandSafetySettingsAsync(
        //    string accessToken,
        //    BrandSafetyUpdateRequest body,
        //    CancellationToken cancellationToken = default);
    }
}
