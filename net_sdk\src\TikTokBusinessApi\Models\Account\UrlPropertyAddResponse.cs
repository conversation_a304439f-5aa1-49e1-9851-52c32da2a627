/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for URL property addition
    /// </summary>
    public class UrlPropertyAddResponse
    {
        /// <summary>
        /// Information about the URL property that is added and pending for ownership verification
        /// </summary>
        [JsonPropertyName("url_property_info")]
        public UrlPropertyInfo UrlPropertyInfo { get; set; } = new UrlPropertyInfo();
    }
}
