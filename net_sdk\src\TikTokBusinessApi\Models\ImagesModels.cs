/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Upload Models

    /// <summary>
    /// Request model for uploading an image
    /// </summary>
    public class ImageUploadRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Image name. Length limit: 1-100 characters.
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }

        /// <summary>
        /// Image upload method. Default value: UPLOAD_BY_FILE.
        /// Enum values: UPLOAD_BY_FILE, UPLOAD_BY_URL, UPLOAD_BY_FILE_ID
        /// </summary>
        [Required]
        [JsonPropertyName("upload_type")]
        public string UploadType { get; set; } = "UPLOAD_BY_FILE";

        /// <summary>
        /// MD5 of the image (used for server verification). Required when upload_type is UPLOAD_BY_FILE.
        /// </summary>
        [JsonPropertyName("image_signature")]
        public string? ImageSignature { get; set; }

        /// <summary>
        /// Image url address. Required when upload_type is UPLOAD_BY_URL.
        /// </summary>
        [JsonPropertyName("image_url")]
        public string? ImageUrl { get; set; }

        /// <summary>
        /// The file_id of the image that you want to upload. Required when upload_type is UPLOAD_BY_FILE_ID.
        /// </summary>
        [JsonPropertyName("file_id")]
        public string? FileId { get; set; }
    }

    /// <summary>
    /// Response model for image upload
    /// </summary>
    public class ImageUploadResponse
    {
        /// <summary>
        /// Image ID that is used to create ads
        /// </summary>
        [JsonPropertyName("image_id")]
        public string ImageId { get; set; } = string.Empty;

        /// <summary>
        /// Material ID
        /// </summary>
        [JsonPropertyName("material_id")]
        public string MaterialId { get; set; } = string.Empty;

        /// <summary>
        /// Whether the source of the image is valid for Carousel Ads
        /// </summary>
        [JsonPropertyName("is_carousel_usable")]
        public bool? IsCarouselUsable { get; set; }

        /// <summary>
        /// Whether it can be displayed on the platform
        /// </summary>
        [JsonPropertyName("displayable")]
        public bool Displayable { get; set; }

        /// <summary>
        /// Image height
        /// </summary>
        [JsonPropertyName("height")]
        public int Height { get; set; }

        /// <summary>
        /// Image width
        /// </summary>
        [JsonPropertyName("width")]
        public int Width { get; set; }

        /// <summary>
        /// Image Format
        /// </summary>
        [JsonPropertyName("format")]
        public string Format { get; set; } = string.Empty;

        /// <summary>
        /// Image URL, valid for an hour and needs to be re-acquired after expiration
        /// </summary>
        [JsonPropertyName("image_url")]
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// MD5 of picture
        /// </summary>
        [JsonPropertyName("signature")]
        public string Signature { get; set; } = string.Empty;

        /// <summary>
        /// Image size in bytes
        /// </summary>
        [JsonPropertyName("size")]
        public long Size { get; set; }

        /// <summary>
        /// Image name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Creation time. UTC time. Format: 2020-06-10T07:39:14Z
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Modification time. UTC time. Format: 2020-06-10T07:39:14Z
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string? ModifyTime { get; set; }
    }

    #endregion

    #region Update Models

    /// <summary>
    /// Request model for updating an image name
    /// </summary>
    public class ImageUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Image name. Length limit: 1 - 100 characters.
        /// </summary>
        [Required]
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Image ID
        /// </summary>
        [Required]
        [JsonPropertyName("image_id")]
        public string ImageId { get; set; } = string.Empty;
    }

    #endregion

    #region Info Models

    /// <summary>
    /// Request model for getting image information
    /// </summary>
    public class ImageInfoRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Image ID list. Up to 100 IDs per request.
        /// </summary>
        [Required]
        [JsonPropertyName("image_ids")]
        public List<string> ImageIds { get; set; } = new List<string>();
    }

    /// <summary>
    /// Response model for image information
    /// </summary>
    public class ImageInfoResponse
    {
        /// <summary>
        /// Information about the images
        /// </summary>
        [JsonPropertyName("list")]
        public List<ImagesImageInfo> List { get; set; } = new List<ImagesImageInfo>();
    }

    #endregion

    #region Search Models

    /// <summary>
    /// Request model for searching images
    /// </summary>
    public class ImageSearchRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Filters on the data
        /// </summary>
        [JsonPropertyName("filtering")]
        public ImageSearchFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number. Default value: 1. Value range: ≥ 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 20. Value range: 1-100
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering options for image search
    /// </summary>
    public class ImageSearchFiltering
    {
        /// <summary>
        /// Image height
        /// </summary>
        [JsonPropertyName("height")]
        public int? Height { get; set; }

        /// <summary>
        /// Image IDs. At most 100 IDs can be included in the list
        /// </summary>
        [JsonPropertyName("image_ids")]
        public List<string>? ImageIds { get; set; }

        /// <summary>
        /// A list of material IDs. At most 100 IDs can be included in the list
        /// </summary>
        [JsonPropertyName("material_ids")]
        public List<string>? MaterialIds { get; set; }

        /// <summary>
        /// Image aspect ratio, e.g.: [1.7, 2.5]. Use 1.7 to search for Images with aspect ratio between 1.65-1.75
        /// </summary>
        [JsonPropertyName("ratio")]
        public List<float>? Ratio { get; set; }

        /// <summary>
        /// Image width
        /// </summary>
        [JsonPropertyName("width")]
        public int? Width { get; set; }

        /// <summary>
        /// Whether to search in displayable materials only
        /// </summary>
        [JsonPropertyName("displayable")]
        public bool? Displayable { get; set; }
    }

    /// <summary>
    /// Response model for image search
    /// </summary>
    public class ImageSearchResponse
    {
        /// <summary>
        /// A list of image information
        /// </summary>
        [JsonPropertyName("list")]
        public List<ImagesImageInfo> List { get; set; } = new List<ImagesImageInfo>();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new PageInfo();
    }

    #endregion

    #region Common Models

    /// <summary>
    /// Image information model for Images API
    /// </summary>
    public class ImagesImageInfo
    {
        /// <summary>
        /// Image ID, used to create ads
        /// </summary>
        [JsonPropertyName("image_id")]
        public string ImageId { get; set; } = string.Empty;

        /// <summary>
        /// Material ID
        /// </summary>
        [JsonPropertyName("material_id")]
        public string MaterialId { get; set; } = string.Empty;

        /// <summary>
        /// Whether the source of the image is valid for Carousel Ads
        /// </summary>
        [JsonPropertyName("is_carousel_usable")]
        public bool? IsCarouselUsable { get; set; }

        /// <summary>
        /// Image width
        /// </summary>
        [JsonPropertyName("width")]
        public int Width { get; set; }

        /// <summary>
        /// Image format
        /// </summary>
        [JsonPropertyName("format")]
        public string Format { get; set; } = string.Empty;

        /// <summary>
        /// Image URL, valid for an hour and needs to be re-acquired after expiration
        /// </summary>
        [JsonPropertyName("image_url")]
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// Image height
        /// </summary>
        [JsonPropertyName("height")]
        public int Height { get; set; }

        /// <summary>
        /// MD5 of picture
        /// </summary>
        [JsonPropertyName("signature")]
        public string Signature { get; set; } = string.Empty;

        /// <summary>
        /// Image size in bytes
        /// </summary>
        [JsonPropertyName("size")]
        public long Size { get; set; }

        /// <summary>
        /// Image name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Creation time. UTC time. Format: 2020-06-10T07:39:14Z
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Modification time. UTC time. Format: 2020-06-10T07:39:14Z
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string? ModifyTime { get; set; }

        /// <summary>
        /// Whether it can be displayed on the platform
        /// </summary>
        [JsonPropertyName("displayable")]
        public bool Displayable { get; set; }
    }

    #endregion
}
