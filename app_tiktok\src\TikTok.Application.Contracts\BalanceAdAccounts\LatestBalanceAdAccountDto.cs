using System;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// DTO cho tài khoản quảng cáo với thông tin số dư và ngân sách mới nhất
    /// </summary>
    public class LatestBalanceAdAccountDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID tài khoản nhà quảng cáo
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// Tên tài khoản nhà quảng cáo
        /// </summary>
        public string AdvertiserName { get; set; }

        /// <summary>
        /// Trạng thái hiển thị của tài khoản nhà quảng cáo
        /// </summary>
        public AdvertiserAccountStatus AdvertiserStatus { get; set; }

        /// <summary>
        /// Loại tài khoản nhà quảng cáo
        /// </summary>
        public AdAccountType AdvertiserType { get; set; }

        /// <summary>
        /// Múi giờ của tài khoản nhà quảng cáo
        /// </summary>
        public string Timezone { get; set; }

        /// <summary>
        /// Tiền tệ của tài khoản nhà quảng cáo
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Số ngày tài khoản nhà quảng cáo đã được mở
        /// </summary>
        public int AccountOpenDays { get; set; }

        /// <summary>
        /// Cảnh báo khi số dư đạt ngưỡng
        /// </summary>
        public bool BalanceReminder { get; set; }

        /// <summary>
        /// Tên công ty của tài khoản nhà quảng cáo
        /// </summary>
        public string? Company { get; set; }

        /// <summary>
        /// Tên liên hệ của tài khoản nhà quảng cáo
        /// </summary>
        public string? ContactName { get; set; }

        /// <summary>
        /// Email liên hệ của tài khoản nhà quảng cáo
        /// </summary>
        public string? ContactEmail { get; set; }

        /// <summary>
        /// Thời gian mở tài khoản nhà quảng cáo (UTC+0)
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// Tổng số dư tài khoản nhà quảng cáo
        /// </summary>
        public decimal AccountBalance { get; set; }

        /// <summary>
        /// Số dư tài khoản hợp lệ
        /// </summary>
        public decimal ValidAccountBalance { get; set; }

        /// <summary>
        /// Số dư bị đóng băng
        /// </summary>
        public decimal FrozenBalance { get; set; }

        /// <summary>
        /// Thuế của tài khoản nhà quảng cáo
        /// </summary>
        public decimal Tax { get; set; }

        /// <summary>
        /// Số dư tiền mặt
        /// </summary>
        public decimal CashBalance { get; set; }

        /// <summary>
        /// Số dư tiền mặt hợp lệ
        /// </summary>
        public decimal ValidCashBalance { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher
        /// </summary>
        public decimal GrantBalance { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher hợp lệ
        /// </summary>
        public decimal ValidGrantBalance { get; set; }

        /// <summary>
        /// Số tiền có thể chuyển từ tài khoản nhà quảng cáo
        /// </summary>
        public decimal? TransferableAmount { get; set; }

        /// <summary>
        /// Chế độ ngân sách của tài khoản quảng cáo
        /// </summary>
        public BudgetMode BudgetMode { get; set; }

        /// <summary>
        /// Ngân sách (tùy theo budget_mode)
        /// </summary>
        public decimal Budget { get; set; }

        /// <summary>
        /// Chi phí ngân sách đã sử dụng
        /// </summary>
        public decimal BudgetCost { get; set; }

        /// <summary>
        /// Ngân sách còn lại
        /// </summary>
        public decimal BudgetRemaining { get; set; }

        /// <summary>
        /// Thông tin hạn chế tần suất thay đổi ngân sách
        /// </summary>
        public BudgetFrequencyRestrictionDto BudgetFrequencyRestriction { get; set; }

        /// <summary>
        /// Thông tin hạn chế số tiền thay đổi ngân sách
        /// </summary>
        public BudgetAmountRestrictionDto BudgetAmountRestriction { get; set; }

        /// <summary>
        /// Số tiền tối thiểu có thể chuyển
        /// </summary>
        public MinTransferableAmountDto MinTransferableAmount { get; set; }

        /// <summary>
        /// ID của Business Center
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// Thời gian đồng bộ dữ liệu mới nhất (UTC)
        /// </summary>
        public DateTime? Date { get; set; }
    }
}
