# Implementation Guide: Syncfusion Pivot Table for Fact Balance

## Overview

This guide provides step-by-step instructions to implement a Syncfusion Pivot Table for TikTok balance and budget analysis using the Fact_SoDu (Balance Facts) data model.

## Prerequisites

-   ASP.NET Core application with ABP Framework
-   Syncfusion ES5 license (Essential Studio)
-   TikTok Business API data synchronization in place
-   Fact_SoDu table implemented following Star schema design

## Step 1: Setup Syncfusion Dependencies

Syncfusion is already installed in Project. Don not need to config

## Step 2: Data Structure Preparation

### 2.1 Define Fact Balance Data Model

Create JavaScript object structure matching your Fact_SoDu table:

```javascript
const factBalanceData = [
    {
        DateKey: ********,
        Date: '2025-07-01',
        Type: 'BusinessCenter|AdAccount',
        EntityId: 'unique_entity_id',
        EntityName: 'display_name',
        BusinessCenter: 'bc_name',
        AdAccount: 'account_name',
        AccountBalance: *********,
        ValidAccountBalance: *********,
        FrozenBalance: ********,
        Tax: ********,
        CashBalance: *********,
        ValidCashBalance: *********,
        GrantBalance: *********,
        ValidGrantBalance: *********,
        TransferableAmount: 0,
        Budget: *********,
        BudgetCost: ********,
        BudgetRemaining: *********,
        Currency: 'VND',
        Timezone: 'Asia/Ho_Chi_Minh',
    },
    // ... more data rows
];
```

### 2.2 Data Enhancement

Add calculated fields and formatting:

```javascript
factBalanceData.forEach((item) => {
    // Calculate KPIs
    item.AvailableBalance = item.ValidAccountBalance - item.FrozenBalance;
    item.CashRatio =
        item.AccountBalance > 0
            ? (item.CashBalance / item.AccountBalance) * 100
            : 0;
    item.GrantRatio =
        item.AccountBalance > 0
            ? (item.GrantBalance / item.AccountBalance) * 100
            : 0;
    item.FrozenRatio =
        item.AccountBalance > 0
            ? (item.FrozenBalance / item.AccountBalance) * 100
            : 0;
    item.BudgetUtilization =
        item.Budget > 0 ? (item.BudgetCost / item.Budget) * 100 : 0;
    item.BudgetRemainingRatio =
        item.Budget > 0 ? (item.BudgetRemaining / item.Budget) * 100 : 0;

    // Add date components
    const dateObj = new Date(item.Date);
    item.Year = dateObj.getFullYear();
    item.Month = dateObj.getMonth() + 1;
    item.WeekDay = dateObj.toLocaleDateString('vi-VN', { weekday: 'long' });
    item.IsWeekend = dateObj.getDay() === 0 || dateObj.getDay() === 6;

    // Add balance status indicators
    item.BalanceStatus = getBalanceStatus(item.ValidAccountBalance, item.Type);
    item.BudgetStatus = getBudgetStatus(item.BudgetUtilization);
});
```

## Step 3: Create Pivot Table Component

### 3.1 HTML Container

```html
<div id="FactBalancePivotTable"></div>
```

### 3.2 Initialize Pivot Table

```javascript
const pivotTableObj = new ej.pivotview.PivotView({
    dataSourceSettings: {
        // Data source
        dataSource: factBalanceData,

        // Row hierarchy - TikTok balance structure
        rows: [
            { name: 'Type', caption: 'Cấp độ' },
            { name: 'BusinessCenter', caption: 'Trung tâm kinh doanh' },
            { name: 'AdAccount', caption: 'Tài khoản quảng cáo' },
        ],

        // Column grouping - Time analysis
        columns: [
            { name: 'Date', caption: 'Ngày' },
            { name: 'WeekDay', caption: 'Thứ' },
        ],

        // Metrics - Key balance indicators
        values: [
            {
                name: 'AccountBalance',
                caption: 'Tổng số dư (VND)',
                type: 'Sum',
            },
            {
                name: 'ValidAccountBalance',
                caption: 'Số dư khả dụng (VND)',
                type: 'Sum',
            },
            {
                name: 'FrozenBalance',
                caption: 'Số dư đóng băng (VND)',
                type: 'Sum',
            },
            {
                name: 'CashBalance',
                caption: 'Số dư tiền mặt (VND)',
                type: 'Sum',
            },
            {
                name: 'GrantBalance',
                caption: 'Số dư voucher (VND)',
                type: 'Sum',
            },
            { name: 'Budget', caption: 'Ngân sách (VND)', type: 'Sum' },
            {
                name: 'BudgetCost',
                caption: 'Chi phí ngân sách (VND)',
                type: 'Sum',
            },
            {
                name: 'BudgetRemaining',
                caption: 'Ngân sách còn lại (VND)',
                type: 'Sum',
            },
            {
                name: 'BudgetUtilization',
                caption: 'Tỷ lệ sử dụng ngân sách (%)',
                type: 'Avg',
            },
            { name: 'CashRatio', caption: 'Tỷ lệ tiền mặt (%)', type: 'Avg' },
            { name: 'GrantRatio', caption: 'Tỷ lệ voucher (%)', type: 'Avg' },
        ],

        // Filters
        filters: [
            { name: 'Currency', caption: 'Tiền tệ' },
            { name: 'BalanceStatus', caption: 'Trạng thái số dư' },
            { name: 'BudgetStatus', caption: 'Trạng thái ngân sách' },
            { name: 'IsWeekend', caption: 'Cuối tuần' },
        ],

        // Formatting
        formatSettings: [
            { name: 'AccountBalance', format: 'N0', currency: 'VND' },
            { name: 'ValidAccountBalance', format: 'N0', currency: 'VND' },
            { name: 'FrozenBalance', format: 'N0', currency: 'VND' },
            { name: 'CashBalance', format: 'N0', currency: 'VND' },
            { name: 'GrantBalance', format: 'N0', currency: 'VND' },
            { name: 'Budget', format: 'N0', currency: 'VND' },
            { name: 'BudgetCost', format: 'N0', currency: 'VND' },
            { name: 'BudgetRemaining', format: 'N0', currency: 'VND' },
            { name: 'BudgetUtilization', format: 'N2', suffix: '%' },
            { name: 'CashRatio', format: 'N2', suffix: '%' },
            { name: 'GrantRatio', format: 'N2', suffix: '%' },
        ],

        // Enable features
        allowLabelFilter: true,
        allowValueFilter: true,
        enableSorting: true,
        expandAll: false,
    },

    // Pivot table settings
    height: 700,
    width: '100%',
    showGroupingBar: true,
    showFieldList: true,
    allowExcelExport: true,
    allowPdfExport: true,
    showToolbar: true,

    // Toolbar options
    toolbar: [
        'New',
        'Save',
        'Grid',
        'Chart',
        'Export',
        'SubTotal',
        'GrandTotal',
        'FieldList',
    ],
});

// Render to container
pivotTableObj.appendTo('#FactBalancePivotTable');
```

## Step 4: Add Performance Features

### 4.1 Conditional Formatting for Balance Status

```javascript
conditionalFormatSettings: [
    {
        measure: 'ValidAccountBalance',
        value1: *********,
        conditions: 'GreaterThan',
        style: {
            backgroundColor: '#d4edda',
            color: '#155724',
        },
    },
    {
        measure: 'ValidAccountBalance',
        value1: ********,
        value2: *********,
        conditions: 'Between',
        style: {
            backgroundColor: '#fff3cd',
            color: '#856404',
        },
    },
    {
        measure: 'ValidAccountBalance',
        value1: ********,
        conditions: 'LessThan',
        style: {
            backgroundColor: '#f8d7da',
            color: '#721c24',
        },
    },
    {
        measure: 'BudgetUtilization',
        value1: 80,
        conditions: 'GreaterThan',
        style: {
            backgroundColor: '#f8d7da',
            color: '#721c24',
        },
    },
    {
        measure: 'BudgetUtilization',
        value1: 60,
        value2: 80,
        conditions: 'Between',
        style: {
            backgroundColor: '#fff3cd',
            color: '#856404',
        },
    },
];
```

### 4.2 Export Functionality

```javascript
// Excel Export
function exportToExcel() {
    if (pivotTableObj) {
        pivotTableObj.excelExport();
    }
}

// PDF Export
function exportToPdf() {
    if (pivotTableObj) {
        pivotTableObj.pdfExport();
    }
}

// Chart View
function showChart() {
    if (pivotTableObj) {
        pivotTableObj.displayOption.view = 'Chart';
    }
}
```

## Step 5: Add Data Integration

### 5.1 API Integration (ASP.NET Core)

```csharp
[HttpGet]
public async Task<IActionResult> GetBalanceData(DateTime? fromDate, DateTime? toDate, string type = null)
{
    var data = await _factBalanceService.GetDataAsync(fromDate, toDate, type);
    return Json(data);
}
```

### 5.2 Dynamic Data Loading

```javascript
async function loadBalanceData(filters = {}) {
    try {
        showLoading();
        const response = await fetch('/api/fact-balance/data', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(filters),
        });

        const data = await response.json();

        // Update pivot table data
        pivotTableObj.dataSourceSettings.dataSource = data;
        pivotTableObj.refresh();

        hideLoading();
    } catch (error) {
        console.error('Error loading data:', error);
        hideLoading();
    }
}
```

## Step 6: Implement UI Controls

### 6.1 Filter Controls

```html
<div class="controls-section">
    <button onclick="exportToExcel()" class="btn btn-success">
        <i class="fas fa-file-excel"></i> Xuất Excel
    </button>
    <button onclick="exportToPdf()" class="btn btn-danger">
        <i class="fas fa-file-pdf"></i> Xuất PDF
    </button>
    <button onclick="refreshData()" class="btn btn-primary">
        <i class="fas fa-sync-alt"></i> Làm mới
    </button>
    <button onclick="showAlerts()" class="btn btn-warning">
        <i class="fas fa-bell"></i> Xem cảnh báo
    </button>
</div>
```

### 6.2 Level Filtering

```javascript
// Filter by balance level
function filterByLevel(level) {
    const filterItem = {
        name: 'Type',
        type: 'Include',
        items: [level],
    };

    pivotTableObj.dataSourceSettings.filterSettings = [filterItem];
    pivotTableObj.refresh();
}

// Filter by balance status
function filterByBalanceStatus(status) {
    const filterItem = {
        name: 'BalanceStatus',
        type: 'Include',
        items: [status],
    };

    pivotTableObj.dataSourceSettings.filterSettings = [filterItem];
    pivotTableObj.refresh();
}
```

## Step 7: Alert System Implementation

### 7.1 Low Balance Alerts

```javascript
function updateAlerts() {
    const alertsContainer = document.getElementById('alerts-container');
    const lowBalanceAccounts = factBalanceData.filter(
        (item) =>
            item.Type === 'AdAccount' && item.ValidAccountBalance < ********
    );
    const highBudgetUtilization = factBalanceData.filter(
        (item) => item.Type === 'AdAccount' && item.BudgetUtilization > 80
    );

    let alertsHtml = '';

    if (lowBalanceAccounts.length > 0) {
        alertsHtml += `
            <div class="alert-card alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> Cảnh báo số dư thấp</h6>
                <p>${lowBalanceAccounts.length} tài khoản có số dư khả dụng dưới 20 triệu VND</p>
            </div>
        `;
    }

    if (highBudgetUtilization.length > 0) {
        alertsHtml += `
            <div class="alert-card alert-danger">
                <h6><i class="fas fa-exclamation-circle"></i> Cảnh báo ngân sách</h6>
                <p>${highBudgetUtilization.length} tài khoản sử dụng hơn 80% ngân sách</p>
            </div>
        `;
    }

    alertsContainer.innerHTML = alertsHtml;
}
```

### 7.2 Summary Cards Update

```javascript
function updateSummaryCards() {
    const totals = factBalanceData.reduce(
        (acc, item) => {
            acc.totalBalance += item.AccountBalance || 0;
            acc.validBalance += item.ValidAccountBalance || 0;
            acc.frozenBalance += item.FrozenBalance || 0;
            acc.totalBudget += item.Budget || 0;
            acc.budgetCost += item.BudgetCost || 0;
            acc.count++;
            return acc;
        },
        {
            totalBalance: 0,
            validBalance: 0,
            frozenBalance: 0,
            totalBudget: 0,
            budgetCost: 0,
            count: 0,
        }
    );

    const budgetUtilization =
        totals.totalBudget > 0
            ? (totals.budgetCost / totals.totalBudget) * 100
            : 0;

    document.getElementById('total-balance').textContent = formatCurrency(
        totals.totalBalance
    );
    document.getElementById('valid-balance').textContent = formatCurrency(
        totals.validBalance
    );
    document.getElementById('frozen-balance').textContent = formatCurrency(
        totals.frozenBalance
    );
    document.getElementById('total-budget').textContent = formatCurrency(
        totals.totalBudget
    );
    document.getElementById('budget-utilization').textContent =
        budgetUtilization.toFixed(1) + '%';
    document.getElementById('active-accounts').textContent = totals.count;

    // Update card colors based on values
    updateCardColors();
}
```

## Step 8: Performance Optimization

### 8.1 Virtual Scrolling (for large datasets)

```javascript
virtualScrollSettings: {
    allowVirtualScrolling: true;
}
```

### 8.2 Data Compression

```javascript
// Enable data compression for large datasets
dataSourceSettings: {
    enableSorting: true,
    allowMemberFilter: true,
    compress: true
}
```

## Step 9: Testing and Validation

### 9.1 Data Validation

-   Verify all balance metrics calculate correctly
-   Test drill-down functionality through balance hierarchy
-   Validate export formats (Excel/PDF)
-   Check responsive behavior on different screen sizes
-   Test alert system with various threshold values

### 9.2 Performance Testing

-   Test with large datasets (10K+ rows)
-   Verify loading times under 3 seconds
-   Test concurrent user access
-   Validate memory usage

## Step 10: Deployment Considerations

### 10.1 Production Settings

```javascript
// Disable development features in production
const isProduction = window.location.hostname !== 'localhost';

const pivotConfig = {
    showFieldList: !isProduction,
    allowConditionalFormatting: true,
    locale: 'vi-VN',
};
```

### 10.2 Security

-   Implement proper authentication for data access
-   Validate user permissions for different balance levels
-   Sanitize all input parameters
-   Use HTTPS for all API calls

## Business Questions Supported

### 1. Tài khoản sắp hết tiền (cảnh báo)

```sql
SELECT EntityId, EntityName, ValidAccountBalance
FROM Fact_SoDu
WHERE Type = 'AdAccount' AND ValidAccountBalance < 1000000
AND DateKey = (SELECT MAX(DateKey) FROM Fact_SoDu)
```

### 2. Xu hướng số dư theo thời gian

```sql
SELECT DateKey, SUM(ValidAccountBalance) as TotalBalance
FROM Fact_SoDu WHERE Type = 'AdAccount'
GROUP BY DateKey ORDER BY DateKey
```

### 3. Tỷ lệ sử dụng ngân sách

```sql
SELECT EntityName,
       Budget, BudgetCost,
       BudgetCost/Budget as Utilization
FROM Fact_SoDu
WHERE Type = 'AdAccount' AND Budget > 0
```

### 4. Số dư theo Business Center

```sql
SELECT bc.Name as BCName,
       SUM(f.ValidAccountBalance) as TotalBalance
FROM Fact_SoDu f
JOIN Dim_BusinessCenter bc ON f.BusinessCenterKey = bc.BusinessCenterKey
GROUP BY bc.Name
```

## Alerting Rules

### Cảnh báo số dư thấp

-   **Cảnh báo vàng**: ValidAccountBalance < 30% của mức trung bình 30 ngày qua
-   **Cảnh báo đỏ**: ValidAccountBalance < 7 ngày chi tiêu trung bình

### Cảnh báo ngân sách

-   **Cảnh báo vàng**: BudgetUtilization > 60%
-   **Cảnh báo đỏ**: BudgetUtilization > 80%

## Conclusion

This implementation provides a comprehensive TikTok balance and budget management dashboard using Syncfusion Pivot Table with proper data hierarchy, real-time filtering, export capabilities, alert system, and performance optimization for production use.
