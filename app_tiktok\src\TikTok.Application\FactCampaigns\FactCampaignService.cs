using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Application.Services;
using Volo.Abp.Application.Dtos;
using TikTok.FactCampaigns;
using TikTok.FactCampaigns.Dtos;
using TikTok.Entities;
using TikTok.DimDates;
using TikTok.DimAdAccounts;
using TikTok.DimBusinessCenters;
using TikTok.DimCampaigns;
using System.ComponentModel.DataAnnotations;
using TikTok.Domain.Repositories;

namespace TikTok.Application.FactCampaigns
{
    public class FactCampaignService : ApplicationService, IFactCampaignService
    {
        private readonly IRepository<FactCampaignEntity, Guid> _factCampaignRepository;
        private readonly IRepository<DimDateEntity, int> _dimDateRepository;
        private readonly IRepository<DimAdAccountEntity, Guid> _dimAdAccountRepository;
        private readonly IRepository<DimBusinessCenterEntity, Guid> _dimBusinessCenterRepository;
        private readonly IRepository<DimCampaignEntity, Guid> _dimCampaignRepository;
        private readonly IFactCampaignDapperRepository _factCampaignDapperRepository;

        public FactCampaignService(
            IRepository<FactCampaignEntity, Guid> factCampaignRepository,
            IRepository<DimDateEntity, int> dimDateRepository,
            IRepository<DimAdAccountEntity, Guid> dimAdAccountRepository,
            IRepository<DimBusinessCenterEntity, Guid> dimBusinessCenterRepository,
            IRepository<DimCampaignEntity, Guid> dimCampaignRepository,
            IFactCampaignDapperRepository factCampaignDapperRepository)
        {
            _factCampaignRepository = factCampaignRepository;
            _dimDateRepository = dimDateRepository;
            _dimAdAccountRepository = dimAdAccountRepository;
            _dimBusinessCenterRepository = dimBusinessCenterRepository;
            _dimCampaignRepository = dimCampaignRepository;
            _factCampaignDapperRepository = factCampaignDapperRepository;
        }

        public async Task<GetFactCampaignDataResponse> GetListAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var result = new GetFactCampaignDataResponse()
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    FactCampaigns = [],
                    DimDates = [],
                    DimAdAccounts = [],
                    DimBusinessCenters = [],
                    DimCampaigns = []
                };

                // Get all fact campaigns and filter in memory for the specified date range
                var factCampaigns = await _factCampaignRepository.GetListAsync(fc => fc.Date >= fromDate.Date && fc.Date <= toDate.Date);

                if (!factCampaigns.Any())
                {
                    // Return empty DTO if no data found
                    return result;
                }

                result.FactCampaigns = ObjectMapper.Map<List<FactCampaignEntity>, List<FactCampaignDto>>(factCampaigns);

                // Get all unique DimDateId values from factCampaigns
                var uniqueDimDateIds = factCampaigns.Select(fc => fc.DimDateId).Distinct().ToList();

                // Get all dim-dates that are included in factCampaigns data
                if (uniqueDimDateIds.Any())
                {
                    var dimDates = await _dimDateRepository.GetListAsync(dd => uniqueDimDateIds.Contains(dd.Id));
                    result.DimDates = ObjectMapper.Map<List<DimDateEntity>, List<DimDateDto>>(dimDates);
                }

                // Get all unique DimAdAccountId values from factCampaigns
                var uniqueDimAdAccountIds = factCampaigns.Select(fc => fc.DimAdAccountId).Distinct().ToList();

                // Get all dim-adaccounts that are included in factCampaigns data
                if (uniqueDimAdAccountIds.Any())
                {
                    var dimAdAccounts = await _dimAdAccountRepository.GetListAsync(daa => uniqueDimAdAccountIds.Contains(daa.Id));
                    result.DimAdAccounts = ObjectMapper.Map<List<DimAdAccountEntity>, List<DimAdAccountDto>>(dimAdAccounts);
                }

                // Get all unique DimBusinessCenterId values from factCampaigns
                var uniqueDimBusinessCenterIds = factCampaigns.Select(fc => fc.DimBusinessCenterId).Distinct().ToList();

                // Get all dim-businesscenters that are included in factCampaigns data
                if (uniqueDimBusinessCenterIds.Any())
                {
                    var dimBusinessCenters = await _dimBusinessCenterRepository.GetListAsync(dbc => uniqueDimBusinessCenterIds.Contains(dbc.Id));
                    result.DimBusinessCenters = ObjectMapper.Map<List<DimBusinessCenterEntity>, List<DimBusinessCenterDto>>(dimBusinessCenters);
                }

                // Get all unique DimCampaignId values from factCampaigns
                var uniqueDimCampaignIds = factCampaigns.Select(fc => fc.DimCampaignId).Distinct().ToList();

                // Get all dim-campaigns that are included in factCampaigns data
                if (uniqueDimCampaignIds.Any())
                {
                    var dimCampaigns = await _dimCampaignRepository.GetListAsync(dc => uniqueDimCampaignIds.Contains(dc.Id));
                    result.DimCampaigns = ObjectMapper.Map<List<DimCampaignEntity>, List<DimCampaignDto>>(dimCampaigns);
                }

                return result;
            }
            catch (Exception ex)
            {
                // Log the exception (you might want to inject ILogger here)
                throw new ValidationException($"Error retrieving fact campaign data: {ex.Message}");
            }
        }

        public async Task<IEnumerable<FactCampaignDto>> GetListFactCampaignByAdvertiserIdAsync(string advertiserId)
        {
            var factCampaigns = await _factCampaignDapperRepository.GetListFactCampaignByAdvertiserIdAsync(advertiserId);
            return ObjectMapper.Map<IEnumerable<FactCampaignEntity>, IEnumerable<FactCampaignDto>>(factCampaigns);
        }

        public async Task<FactCampaignSummary> GetSummaryAsync()
        {
            var summary = await _factCampaignDapperRepository.GetSummaryAsync();
            return summary;
        }
    }
}