/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Business Messaging endpoints
    /// </summary>
    public static class BusinessMessagingEndpoints
    {
        /// <summary>
        /// Send a message to a conversation endpoint (v1.3)
        /// </summary>
        public const string SendMessage = "/business/message/send/";

        /// <summary>
        /// Get a list of conversations endpoint (v1.3)
        /// </summary>
        public const string GetConversations = "/business/message/conversation/list/";

        /// <summary>
        /// Get a list of messages in a conversation endpoint (v1.3)
        /// </summary>
        public const string GetMessages = "/business/message/content/list/";

        /// <summary>
        /// Upload an image endpoint (v1.3)
        /// </summary>
        public const string UploadMedia = "/business/message/media/upload/";

        /// <summary>
        /// Download an image from a message endpoint (v1.3)
        /// </summary>
        public const string DownloadMedia = "/business/message/media/download/";

        /// <summary>
        /// Create a Business Messaging Webhook configuration endpoint (v1.3)
        /// </summary>
        public const string CreateWebhook = "/business/webhook/update/";

        /// <summary>
        /// Get a Business Messaging Webhook configuration endpoint (v1.3)
        /// </summary>
        public const string GetWebhook = "/business/webhook/list/";

        /// <summary>
        /// Delete a Business Messaging Webhook configuration endpoint (v1.3)
        /// </summary>
        public const string DeleteWebhook = "/business/webhook/delete/";

        /// <summary>
        /// Create an automatic message for a Business Account endpoint (v1.3)
        /// </summary>
        public const string CreateAutoMessage = "/business/message/auto_message/create/";

        /// <summary>
        /// Update the automatic message for a Business Account endpoint (v1.3)
        /// </summary>
        public const string UpdateAutoMessage = "/business/message/auto_message/update/";

        /// <summary>
        /// Turn on or turn off an automatic message for a Business Account endpoint (v1.3)
        /// </summary>
        public const string UpdateAutoMessageStatus = "/business/message/auto_message/status/update/";

        /// <summary>
        /// Get the automatic messages for a Business Account endpoint (v1.3)
        /// </summary>
        public const string GetAutoMessages = "/business/message/auto_message/get/";

        /// <summary>
        /// Delete the automatic message for a Business Account endpoint (v1.3)
        /// </summary>
        public const string DeleteAutoMessage = "/business/message/auto_message/delete/";

        /// <summary>
        /// Sort the automatic message for a Business Account endpoint (v1.3)
        /// </summary>
        public const string SortAutoMessages = "/business/message/auto_message/sort/";
    }
}
