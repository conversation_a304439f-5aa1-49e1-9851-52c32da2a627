/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request parameters for getting ad group review information
    /// </summary>
    public class AdGroupReviewInfoRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The list of ad group IDs. Currently, we support a maximum of 20 ad group IDs in the request.
        /// </summary>
        [JsonPropertyName("adgroup_ids")]
        [Required]
        public List<string> AdGroupIds { get; set; } = new List<string>();

        /// <summary>
        /// Language
        /// </summary>
        [JsonPropertyName("lang")]
        public string? Language { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupReviewInfoRequest(AdvertiserId={AdvertiserId}, AdGroupIds={AdGroupIds?.Count}, Language={Language})";
        }
    }

    /// <summary>
    /// Request parameters for getting ad review information
    /// </summary>
    public class AdReviewInfoRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The list of ad IDs. Quantity: 1-100.
        /// </summary>
        [JsonPropertyName("ad_ids")]
        [Required]
        public List<string> AdIds { get; set; } = new List<string>();

        /// <summary>
        /// Language
        /// </summary>
        [JsonPropertyName("lang")]
        public string? Language { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdReviewInfoRequest(AdvertiserId={AdvertiserId}, AdIds={AdIds?.Count}, Language={Language})";
        }
    }

    /// <summary>
    /// Request parameters for appealing ad group rejection
    /// </summary>
    public class AdGroupAppealRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        [Required]
        public string AdGroupId { get; set; } = string.Empty;

        /// <summary>
        /// Ad ID. Optional.
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// The reason for the appeal
        /// </summary>
        [JsonPropertyName("appeal_reason")]
        public string? AppealReason { get; set; }

        /// <summary>
        /// List of attachment links
        /// </summary>
        [JsonPropertyName("attachment_list")]
        public List<string>? AttachmentList { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupAppealRequest(AdvertiserId={AdvertiserId}, AdGroupId={AdGroupId}, AdId={AdId}, AppealReason={AppealReason})";
        }
    }

    /// <summary>
    /// Response data for ad group review information
    /// </summary>
    public class AdGroupReviewInfoResponse
    {
        /// <summary>
        /// The bi-dimensional structure for the review data at the ad level
        /// </summary>
        [JsonPropertyName("ad_review_map")]
        public Dictionary<string, Dictionary<string, AdReviewInfo>>? AdReviewMap { get; set; }

        /// <summary>
        /// The review data at the ad group level, organized by adgroup_id
        /// </summary>
        [JsonPropertyName("ad_group_review_map")]
        public Dictionary<string, AdGroupReviewInfo>? AdGroupReviewMap { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupReviewInfoResponse(AdReviewMap={AdReviewMap?.Count}, AdGroupReviewMap={AdGroupReviewMap?.Count})";
        }
    }

    /// <summary>
    /// Response data for ad review information
    /// </summary>
    public class AdReviewInfoResponse
    {
        /// <summary>
        /// Review data at the ad level
        /// </summary>
        [JsonPropertyName("ad_review_map")]
        public Dictionary<string, AdReviewInfo>? AdReviewMap { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdReviewInfoResponse(AdReviewMap={AdReviewMap?.Count})";
        }
    }

    /// <summary>
    /// Response data for ad group appeal requests
    /// </summary>
    public class AdGroupAppealResponse
    {
        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return "AdGroupAppealResponse()";
        }
    }

    /// <summary>
    /// Ad review information
    /// </summary>
    public class AdReviewInfo
    {
        /// <summary>
        /// The ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// Whether the ad has been approved or not
        /// </summary>
        [JsonPropertyName("is_approved")]
        public bool? IsApproved { get; set; }

        /// <summary>
        /// The ad review status
        /// </summary>
        [JsonPropertyName("review_status")]
        public string? ReviewStatus { get; set; }

        /// <summary>
        /// Ad appeal status (deprecated)
        /// </summary>
        [JsonPropertyName("appeal_status")]
        public string? AppealStatus { get; set; }

        /// <summary>
        /// The placements that failed the review
        /// </summary>
        [JsonPropertyName("forbidden_placements")]
        public List<string>? ForbiddenPlacements { get; set; }

        /// <summary>
        /// Age groups that failed the review
        /// </summary>
        [JsonPropertyName("forbidden_ages")]
        public List<string>? ForbiddenAges { get; set; }

        /// <summary>
        /// The targeted regions that failed the review
        /// </summary>
        [JsonPropertyName("forbidden_locations")]
        public List<string>? ForbiddenLocations { get; set; }

        /// <summary>
        /// The audience operating systems that failed the review
        /// </summary>
        [JsonPropertyName("forbidden_operation_systems")]
        public List<string>? ForbiddenOperationSystems { get; set; }

        /// <summary>
        /// The last time when the ad was reviewed
        /// </summary>
        [JsonPropertyName("last_audit_time")]
        public string? LastAuditTime { get; set; }

        /// <summary>
        /// Details about the rejection
        /// </summary>
        [JsonPropertyName("reject_info")]
        public List<RejectInfo>? RejectInfo { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdReviewInfo(AdId={AdId}, IsApproved={IsApproved}, ReviewStatus={ReviewStatus})";
        }
    }

    /// <summary>
    /// Ad group review information
    /// </summary>
    public class AdGroupReviewInfo
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdGroupId { get; set; }

        /// <summary>
        /// Whether the ad group has been approved
        /// </summary>
        [JsonPropertyName("is_approved")]
        public bool? IsApproved { get; set; }

        /// <summary>
        /// The ad group review status
        /// </summary>
        [JsonPropertyName("review_status")]
        public string? ReviewStatus { get; set; }

        /// <summary>
        /// Ad group appeal status
        /// </summary>
        [JsonPropertyName("appeal_status")]
        public string? AppealStatus { get; set; }

        /// <summary>
        /// The placements that failed the review
        /// </summary>
        [JsonPropertyName("forbidden_placements")]
        public List<string>? ForbiddenPlacements { get; set; }

        /// <summary>
        /// The audience age ranges that failed the review
        /// </summary>
        [JsonPropertyName("forbidden_ages")]
        public List<string>? ForbiddenAges { get; set; }

        /// <summary>
        /// The targeted locations that failed the review
        /// </summary>
        [JsonPropertyName("forbidden_locations")]
        public List<string>? ForbiddenLocations { get; set; }

        /// <summary>
        /// The audience operating systems that failed the review
        /// </summary>
        [JsonPropertyName("forbidden_operation_systems")]
        public List<string>? ForbiddenOperationSystems { get; set; }

        /// <summary>
        /// The last time when the ad group was reviewed
        /// </summary>
        [JsonPropertyName("last_audit_time")]
        public string? LastAuditTime { get; set; }

        /// <summary>
        /// Whether ads in this ad group have been rejected
        /// </summary>
        [JsonPropertyName("contains_rejected_ads")]
        public bool? ContainsRejectedAds { get; set; }

        /// <summary>
        /// Details about the rejection
        /// </summary>
        [JsonPropertyName("reject_info")]
        public List<RejectInfo>? RejectInfo { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupReviewInfo(AdGroupId={AdGroupId}, IsApproved={IsApproved}, ReviewStatus={ReviewStatus})";
        }
    }

    /// <summary>
    /// Details about the rejection
    /// </summary>
    public class RejectInfo
    {
        /// <summary>
        /// The review suggestion
        /// </summary>
        [JsonPropertyName("suggestion")]
        public string? Suggestion { get; set; }

        /// <summary>
        /// List of rejection reasons
        /// </summary>
        [JsonPropertyName("reasons")]
        public List<string>? Reasons { get; set; }

        /// <summary>
        /// Age groups that failed the review
        /// </summary>
        [JsonPropertyName("forbidden_ages")]
        public List<string>? ForbiddenAges { get; set; }

        /// <summary>
        /// The targeted regions that failed the review
        /// </summary>
        [JsonPropertyName("forbidden_locations")]
        public List<string>? ForbiddenLocations { get; set; }

        /// <summary>
        /// The placements that failed the review
        /// </summary>
        [JsonPropertyName("forbidden_placements")]
        public List<string>? ForbiddenPlacements { get; set; }

        /// <summary>
        /// The content of the ad that has been reviewed
        /// </summary>
        [JsonPropertyName("content_info")]
        public ContentInfo? ContentInfo { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"RejectInfo(Suggestion={Suggestion}, Reasons={Reasons?.Count})";
        }
    }

    /// <summary>
    /// The content of the ad that has been reviewed
    /// </summary>
    public class ContentInfo
    {
        /// <summary>
        /// The image content
        /// </summary>
        [JsonPropertyName("image_content")]
        public ImageContent? ImageContent { get; set; }

        /// <summary>
        /// The video content that has been reviewed
        /// </summary>
        [JsonPropertyName("video_content")]
        public VideoContent? VideoContent { get; set; }

        /// <summary>
        /// The text
        /// </summary>
        [JsonPropertyName("text_content")]
        public string? TextContent { get; set; }

        /// <summary>
        /// The type of the content that has been reviewed
        /// </summary>
        [JsonPropertyName("content_type")]
        public string? ContentType { get; set; }

        /// <summary>
        /// The content of the music in the Carousel Ads that has been reviewed
        /// </summary>
        [JsonPropertyName("carousel_music_content")]
        public CarouselMusicContent? CarouselMusicContent { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ContentInfo(ContentType={ContentType}, TextContent={TextContent})";
        }
    }

    /// <summary>
    /// The image content
    /// </summary>
    public class ImageContent
    {
        /// <summary>
        /// The ID of the image that has been reviewed
        /// </summary>
        [JsonPropertyName("image_id")]
        public string? ImageId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ImageContent(ImageId={ImageId})";
        }
    }

    /// <summary>
    /// The video content that has been reviewed
    /// </summary>
    public class VideoContent
    {
        /// <summary>
        /// The ID of the video that is being reviewed
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"VideoContent(VideoId={VideoId})";
        }
    }

    /// <summary>
    /// The content of the music in the Carousel Ads that has been reviewed
    /// </summary>
    public class CarouselMusicContent
    {
        /// <summary>
        /// The ID of the piece of music used in the Carousel Ads
        /// </summary>
        [JsonPropertyName("music_id")]
        public string? MusicId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CarouselMusicContent(MusicId={MusicId})";
        }
    }
}
