# GMV Max Sync Service - Tối ưu hóa đồng bộ dữ liệu

## Tổng quan

Service đồng bộ GMV Max tối ưu được thiết kế để đồng bộ tất cả dữ liệu GMV Max cho một Business Center trong một lần gọi duy nhất. Service này tự động:

1. <PERSON><PERSON><PERSON> sách `AdvertiserIds` từ `bcId`
2. <PERSON><PERSON><PERSON><PERSON> từng `AdvertiserId` để đồng bộ lần lượt:
    - **Chiến dịch** (Campaign)
    - **Báo cáo chiến dịch** (Product Campaign Report)
    - **Báo cáo sản phẩm** (Product Detail Product Report)
    - **B<PERSON>o cáo sáng tạo** (Product Creative Report)

## Cấu trúc Service

### Interface

-   `IGmvMaxSyncService`: Interface chính cho service đồng bộ
-   `IGmvMaxSyncApplicationAppService`: Interface cho Application Service

### Implementation

-   `GmvMaxSyncService`: Implementation chính
-   `GmvMaxSyncApplicationAppService`: Application Service implementation
-   `GmvMaxSyncController`: API Controller

### DTO và Parameters

-   `GmvMaxAdvertiserDto`: DTO cho thông tin Advertiser

    -   `AdvertiserId`: ID của Advertiser
    -   `Name`: Tên của Advertiser
    -   `Timezone`: Múi giờ của Advertiser
    -   `Currency`: Tiền tệ của Advertiser

-   `GmvMaxSyncParameters`: Parameters cho việc đồng bộ
    -   `BcId`: ID của Business Center
    -   `TikTokClient`: TikTok Client đã được khởi tạo
    -   `Advertisers`: Danh sách Advertiser cần đồng bộ

### Kết quả đồng bộ

-   `GmvMaxSyncResult`: Kết quả tổng hợp bao gồm:
    -   Thông tin tổng quan (BC count, Advertiser count)
    -   Kết quả chi tiết từng loại đồng bộ
    -   Thời gian thực hiện

## Cách sử dụng

### 1. Qua API

#### Đồng bộ cho một Business Center

```http
POST /api/gmv-max-sync/sync/{bcId}
```

#### Đồng bộ với danh sách Advertiser tùy chỉnh

```http
POST /api/gmv-max-sync/sync-with-advertisers/{bcId}
Content-Type: application/json

[
  {
    "advertiserId": "ADV001",
    "name": "Advertiser 1",
    "timezone": "Asia/Ho_Chi_Minh",
    "currency": "VND"
  },
  {
    "advertiserId": "ADV002",
    "name": "Advertiser 2",
    "timezone": "Asia/Ho_Chi_Minh",
    "currency": "VND"
  }
]
```

#### Đồng bộ cho tất cả Business Centers

```http
POST /api/gmv-max-sync/sync-all
```

### 2. Qua Background Job

Service được tích hợp vào `SyncBusinessCenterWorker` và sẽ được gọi tự động khi chạy background job đồng bộ Business Center.

### 3. Qua Dependency Injection

#### Sử dụng method cơ bản

```csharp
public class MyService
{
    private readonly IGmvMaxSyncService _gmvMaxSyncService;

    public MyService(IGmvMaxSyncService gmvMaxSyncService)
    {
        _gmvMaxSyncService = gmvMaxSyncService;
    }

    public async Task SyncGmvMax(string bcId)
    {
        var result = await _gmvMaxSyncService.SyncGmvMaxAsync(bcId);

        if (result.IsSuccess)
        {
            Console.WriteLine($"Đồng bộ thành công: {result.TotalSynced} bản ghi");
            Console.WriteLine($"Thời gian: {result.Duration}");
        }
    }
}
```

#### Sử dụng với parameters tùy chỉnh

```csharp
public async Task SyncGmvMaxWithCustomParameters(string bcId)
{
    // Tạo danh sách Advertiser tùy chỉnh
    var advertisers = new List<GmvMaxAdvertiserDto>
    {
        new GmvMaxAdvertiserDto
        {
            AdvertiserId = "ADV001",
            Name = "Advertiser 1",
            Timezone = "Asia/Ho_Chi_Minh",
            Currency = "VND"
        },
        new GmvMaxAdvertiserDto
        {
            AdvertiserId = "ADV002",
            Name = "Advertiser 2",
            Timezone = "Asia/Ho_Chi_Minh",
            Currency = "VND"
        }
    };

    // Tạo parameters
    var parameters = new GmvMaxSyncParameters
    {
        BcId = bcId,
        TikTokClient = null, // Sẽ được tạo trong service
        Advertisers = advertisers
    };

    var result = await _gmvMaxSyncService.SyncGmvMaxWithParametersAsync(parameters);

    if (result.IsSuccess)
    {
        Console.WriteLine($"Đồng bộ thành công: {result.TotalSynced} bản ghi");
        Console.WriteLine($"Thời gian: {result.Duration}");
        Console.WriteLine($"Số Advertiser: {result.AdvertiserCount}");
    }
}
```

## Luồng xử lý

### Method cơ bản (SyncGmvMaxAsync)

1. **Lấy AdvertiserIds**: Từ `bcId`, service lấy danh sách tất cả Advertiser IDs
2. **Đồng bộ Campaign**: Gọi `SyncManyGmvMaxCampaignsAsync` với danh sách AdvertiserIds
3. **Đồng bộ Product Campaign Report**: Gọi `SyncGmvMaxProductCampaignAsync`
4. **Đồng bộ Product Detail Product Report**: Gọi `SyncGmvMaxProductDetailProductAsync`
5. **Đồng bộ Product Creative Report**: Gọi `SyncGmvMaxProductCreativeAsync`
6. **Tổng hợp kết quả**: Kết hợp tất cả kết quả thành một response duy nhất

### Method với parameters tùy chỉnh (SyncGmvMaxWithParametersAsync)

1. **Validate Parameters**: Kiểm tra BcId, TikTokClient và danh sách Advertisers
2. **Đồng bộ Campaign**: Gọi `SyncCampaignsWithParametersAsync` với từng Advertiser
3. **Đồng bộ Product Campaign Report**: Gọi `SyncProductCampaignReportsWithParametersAsync`
4. **Đồng bộ Product Detail Product Report**: Gọi `SyncProductDetailProductReportsWithParametersAsync`
5. **Đồng bộ Product Creative Report**: Gọi `SyncProductCreativeReportsWithParametersAsync`
6. **Tổng hợp kết quả**: Kết hợp tất cả kết quả thành một response duy nhất

## Lợi ích

### 1. Tối ưu hiệu suất

-   Giảm số lần gọi API
-   Xử lý tuần tự các bước đồng bộ
-   Tổng hợp kết quả trong một response

### 2. Dễ sử dụng

-   Một API call duy nhất cho toàn bộ quá trình
-   Kết quả tổng hợp rõ ràng
-   Error handling tập trung

### 3. Khả năng mở rộng

-   Dễ dàng thêm các loại đồng bộ mới
-   Cấu trúc modular
-   Tương thích với hệ thống hiện tại

## Logging

Service cung cấp logging chi tiết cho từng bước:

-   Bắt đầu/kết thúc mỗi bước đồng bộ
-   Số lượng Advertiser được xử lý
-   Thời gian thực hiện
-   Lỗi chi tiết nếu có

## Error Handling

-   Xử lý lỗi riêng cho từng bước đồng bộ
-   Tiếp tục xử lý các bước còn lại nếu một bước bị lỗi
-   Tổng hợp thông tin lỗi trong kết quả cuối cùng

## Monitoring

Kết quả đồng bộ bao gồm:

-   `StartTime`: Thời gian bắt đầu
-   `EndTime`: Thời gian kết thúc
-   `Duration`: Tổng thời gian thực hiện
-   `TotalSynced`: Tổng số bản ghi đã đồng bộ
-   Chi tiết từng loại đồng bộ (Campaign, Report, etc.)
