using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.BusinessCenters;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu hồ sơ chi phí
    /// </summary>
    public class CostProfileSyncService : BaseSyncService, ICostProfileSyncService
    {
        private readonly ICostProfileRepository _costProfileRepository;
        private readonly IRepository<RawCostProfileEntity, Guid> _costProfileEntityRepository;
        private readonly IBusinessCenterCache _businessCenterCache;

        private const int MAX_PAGE_SIZE = 50;

        public CostProfileSyncService(
            IServiceProvider serviceProvider,
            ICostProfileRepository costProfileRepository,
            IRepository<RawCostProfileEntity, Guid> costProfileEntityRepository,
            ILogger<CostProfileSyncService> logger,
            IBusinessCenterCache businessCenterCache) : base(serviceProvider, logger)
        {
            _costProfileRepository = costProfileRepository;
            _costProfileEntityRepository = costProfileEntityRepository;
            _businessCenterCache = businessCenterCache;
        }

        /// <summary>
        /// Đồng bộ hồ sơ chi phí theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<CostProfileSyncResult> SyncCostProfilesAsync(string bcId)
        {
            var result = new CostProfileSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ hồ sơ chi phí cho BC: {BcId}", bcId);

                string? timezone = null;
                var businessCenter = await _businessCenterCache.GetByBcIdAsync(bcId);
                if (businessCenter != null)
                {
                    timezone = businessCenter.Timezone;
                }

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // 3. Đồng bộ hồ sơ chi phí
                var costProfileResult = await SyncCostProfilesFromApiAsync(tikTokClient, bcId, timezone);
                result.TotalSynced = costProfileResult.TotalSynced;
                result.NewRecords = costProfileResult.NewRecords;
                result.UpdatedRecords = costProfileResult.UpdatedRecords;
                result.AdAccountCount = costProfileResult.AdAccountCount;

                _logger.LogDebug("Hoàn thành đồng bộ hồ sơ chi phí cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ hồ sơ chi phí cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ hồ sơ chi phí: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ hồ sơ chi phí cho BC: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ hồ sơ chi phí cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<CostProfileSyncResult> SyncAllCostProfilesAsync()
        {
            var result = new CostProfileSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ hồ sơ chi phí cho tất cả BC");

                // Lấy tất cả Business Applications active
                var allBusinessApplications = await _businessApplicationCache.GetAllActiveAsync();
                var bcGroups = allBusinessApplications.GroupBy(x => x.BcId);

                if (!bcGroups.Any())
                {
                    result.ErrorMessage = "Không tìm thấy ứng dụng Business active nào";
                    _logger.LogWarning(result.ErrorMessage);
                    return result;
                }

                // Đồng bộ từng BC
                foreach (var bcGroup in bcGroups)
                {
                    var bcId = bcGroup.Key;
                    try
                    {
                        var bcResult = await SyncCostProfilesAsync(bcId);
                        result.TotalSynced += bcResult.TotalSynced;
                        result.NewRecords += bcResult.NewRecords;
                        result.UpdatedRecords += bcResult.UpdatedRecords;
                        result.AdAccountCount += bcResult.AdAccountCount;
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ hồ sơ chi phí cho BC: {BcId}", bcId);
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        result.ErrorMessage = ex.Message + Environment.NewLine;
                        // Tiếp tục với BC tiếp theo
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ hồ sơ chi phí cho BC: {BcId}", bcId);
                        // Tiếp tục với BC tiếp theo
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ hồ sơ chi phí cho tất cả BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, AdAccount: {AdAccountCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.AdAccountCount);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ hồ sơ chi phí: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ hồ sơ chi phí cho tất cả BC");
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ hồ sơ chi phí từ TikTok API
        /// </summary>
        private async Task<CostProfileSyncResult> SyncCostProfilesFromApiAsync(TikTokBusinessApiClient client, string bcId, string? timezone = null)
        {
            var result = new CostProfileSyncResult();

            if (timezone == null)
            {
                timezone = DateTimeService.UTC_TIMEZONE;
            }

            int page = 1;
            bool hasMoreData = true;

            // Lấy ra ngày hiện tại
            var currentDate = _dateTimeService.GetDateTimeNow(timezone);
            // filter theo ngày hiện tại

            var filter = new CostRecordsFiltering
            {
                StartDate = currentDate.ToString("yyyy-MM-dd"),
                EndDate = currentDate.ToString("yyyy-MM-dd"),
            };

            while (hasMoreData)
            {
                var request = new GetCostRecordsRequest
                {
                    BcId = bcId,
                    Page = page,
                    PageSize = MAX_PAGE_SIZE,
                    Filtering = filter,
                };

                var response = await client.BcPayments.GetCostRecordsAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy hồ sơ chi phí: {response.Message}");
                }

                if (response?.Data?.CostList == null || !response.Data.CostList.Any())
                {
                    hasMoreData = false;
                    break;
                }

                // Handle cost profile sync for page
                _logger.LogDebug("Đồng bộ hồ sơ chi phí cho BC: {BcId}, Trang: {Page}", bcId, page);
                await SyncCostProfilesForPage(bcId, page, result, response);
                _logger.LogDebug("Đồng bộ hồ sơ chi phí cho BC: {BcId}, Trang: {Page}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    bcId, page, result.TotalSynced, result.NewRecords, result.UpdatedRecords);

                // Kiểm tra xem còn dữ liệu không
                if (response.Data.CostList.Count < MAX_PAGE_SIZE)
                {
                    hasMoreData = false;
                }
                else
                {
                    page++;
                }
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ hồ sơ chi phí cho một trang
        /// </summary>
        private async Task SyncCostProfilesForPage(string bcId, int page, CostProfileSyncResult result, GetCostRecordsResponse response)
        {
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                // Tạo danh sách thêm mới
                var newCostProfiles = new List<RawCostProfileEntity>();

                // Lấy thời gian hiện tại theo UTC
                var dateTimeUtcNow = _dateTimeService.GetDateTimeUtcNow();

                // Xử lý từng hồ sơ chi phí
                foreach (var costRecord in response.Data.CostList)
                {
                    try
                    {
                        // Lấy bản ghi mới nhất
                        var latestCostProfile = await _costProfileRepository.GetLatestByAdvertiserIdAsync(costRecord.AdvertiserId ?? "");
                        var newCostProfile = MapApiCostRecordToEntity(costRecord, bcId);
                        // Gán thời gian hiện tại cho bản ghi mới
                        newCostProfile.Date = dateTimeUtcNow;

                        if (latestCostProfile == null)
                        {
                            // Tạo mới nếu chưa có bản ghi nào
                            newCostProfiles.Add(newCostProfile);
                            result.NewRecords++;
                        }
                        else
                        {
                            // So sánh dữ liệu để xem có thay đổi không
                            if (latestCostProfile.HasChanged(newCostProfile))
                            {
                                // Tạo bản ghi mới nếu có thay đổi
                                newCostProfiles.Add(newCostProfile);
                                result.NewRecords++;
                            }
                        }

                        result.TotalSynced++;
                        result.AdAccountCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý hồ sơ chi phí: {AdvertiserId}", costRecord.AdvertiserId);
                        // Tiếp tục với hồ sơ chi phí tiếp theo
                    }
                }

                // Thêm mới
                if (newCostProfiles.Any())
                {
                    await _costProfileEntityRepository.InsertManyAsync(newCostProfiles);
                }
                await uow.CompleteAsync();
            }
        }

        /// <summary>
        /// Map dữ liệu hồ sơ chi phí từ API sang Entity
        /// </summary>
        private RawCostProfileEntity MapApiCostRecordToEntity(CostRecord apiCostRecord, string bcId)
        {
            return new RawCostProfileEntity(Guid.NewGuid())
            {
                AdvertiserId = apiCostRecord.AdvertiserId ?? "",
                AdvertiserName = apiCostRecord.AdvertiserName ?? "",
                Amount = apiCostRecord.Amount ?? 0,
                CashAmount = apiCostRecord.CashAmount ?? 0,
                GrantAmount = apiCostRecord.GrantAmount ?? 0,
                TaxAmount = apiCostRecord.TaxAmount ?? 0,
                Currency = apiCostRecord.Currency ?? "USD",
                BcId = bcId,
                Date = DateTime.UtcNow // Sẽ được gán lại trong SyncCostProfilesForPage
            };
        }
    }
}