/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example class demonstrating BC Reporting API usage
    /// </summary>
    public class BcReportingApiExample : IDisposable
    {
        private readonly TikTokBusinessApiClient _client;

        public BcReportingApiExample()
        {
            _client = new TikTokBusinessApiClient();
        }

        /// <summary>
        /// Example of getting advertiser attributes (currencies and registration areas)
        /// </summary>
        public async Task GetAdvertiserAttributeExample()
        {
            try
            {
                Console.WriteLine("Getting advertiser attributes...");

                var request = new GetAdvertiserAttributeRequest
                {
                    BcId = "your-business-center-id"
                };

                var response = await _client.BcReporting.GetAdvertiserAttributeAsync(
                    "your-access-token", 
                    request);

                Console.WriteLine($"Request ID: {response.RequestId}");
                
                if (response.Data != null)
                {
                    Console.WriteLine("Currencies:");
                    if (response.Data.Currencies != null)
                    {
                        foreach (var currency in response.Data.Currencies)
                        {
                            Console.WriteLine($"  - {currency}");
                        }
                    }

                    Console.WriteLine("Region Codes:");
                    if (response.Data.RegionCodes != null)
                    {
                        foreach (var regionCode in response.Data.RegionCodes)
                        {
                            Console.WriteLine($"  - {regionCode}");
                        }
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.Code}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Comprehensive example showing the complete workflow
        /// </summary>
        public async Task ComprehensiveWorkflowExample()
        {
            Console.WriteLine("=== BC Reporting API Comprehensive Example ===");

            // 1. Get advertiser attributes
            Console.WriteLine("\n1. Getting advertiser attributes...");
            await GetAdvertiserAttributeExample();

            Console.WriteLine("\n=== Example completed ===");
        }

        public void Dispose()
        {
            _client?.Dispose();
        }
    }
}
