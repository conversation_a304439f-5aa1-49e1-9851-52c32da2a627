using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.ReportIntegratedAdAccounts
{
    /// <summary>
    /// DTO cho việc lấy danh sách báo cáo tích hợp AdAccount
    /// </summary>
    public class GetReportIntegratedAdAccountListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// ID tài khoản nhà quảng cáo
        /// </summary>
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Tên tài khoản nhà quảng cáo
        /// </summary>
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// ID Business Center
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// Ngày bắt đầu
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Ngày kết thúc
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Bộ lọc tìm kiếm
        /// </summary>
        public string? Filter { get; set; }
    }
} 