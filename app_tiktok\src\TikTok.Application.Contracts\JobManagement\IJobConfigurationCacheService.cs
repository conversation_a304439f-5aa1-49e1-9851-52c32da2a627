using System;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.JobManagement
{
    /// <summary>
    /// Interface cho service cache cấu hình job
    /// </summary>
    public interface IJobConfigurationCacheService: IScopedDependency
    {
        /// <summary>
        /// L<PERSON>y cấu hình hiện tại với cache
        /// </summary>
        /// <returns>Cấu hình hiện tại</returns>
        Task<JobConfigurationDto> GetCurrentConfigurationWithCacheAsync();

        /// <summary>
        /// Clear cache của cấu hình job
        /// </summary>
        Task ClearConfigurationCacheAsync();
    }
}