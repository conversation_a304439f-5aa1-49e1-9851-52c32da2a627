using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.ReportIntegratedBcs
{
    /// <summary>
    /// DTO cho việc lấy danh sách báo cáo tích hợp Business Center
    /// </summary>
    public class GetReportIntegratedBcListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// ID Business Center
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// <PERSON><PERSON>y bắt đầu
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Ng<PERSON>y kết thúc
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Bộ lọc
        /// </summary>
        public string? Filter { get; set; }
    }
}