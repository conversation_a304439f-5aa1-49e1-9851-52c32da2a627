/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Creative Tools endpoints
    /// </summary>
    public static class CreativeToolsEndpoints
    {
        /// <summary>
        /// Get the status of a task endpoint (v1.3)
        /// </summary>
        public const string GetTaskStatus = "/creative/status/get/";

        /// <summary>
        /// Edit an image endpoint (v1.3)
        /// </summary>
        public const string EditImage = "/creative/image/edit/";

        /// <summary>
        /// Preview an ad endpoint (v1.3)
        /// </summary>
        public const string PreviewAd = "/creative/ads_preview/create/";

        /// <summary>
        /// Create a Smart Video Soundtrack task endpoint (v1.3) - Deprecated
        /// </summary>
        public const string CreateVideoSoundtrackTask = "/creative/video_soundtrack/create/";

        /// <summary>
        /// Create a Quick Optimization task endpoint (v1.3) - Deprecated
        /// </summary>
        public const string CreateQuickOptimizationTask = "/creative/quick_optimization/create/";

        /// <summary>
        /// Create a Smart Video task endpoint (v1.3) - Deprecated
        /// </summary>
        public const string CreateSmartVideoTask = "/creative/smart_video/create/";

        /// <summary>
        /// Delete creative assets endpoint (v1.3)
        /// </summary>
        public const string DeleteCreativeAssets = "/creative/asset/delete/";

        /// <summary>
        /// Get Smart Text recommendations endpoint (v1.3)
        /// </summary>
        public const string GetSmartTextRecommendations = "/creative/smart_text/generate/";

        /// <summary>
        /// Send Smart Text feedback endpoint (v1.3)
        /// </summary>
        public const string SendSmartTextFeedback = "/creative/smart_text/feedback/";

        /// <summary>
        /// Get recommended CTAs endpoint (v1.3)
        /// </summary>
        public const string GetRecommendedCTAs = "/creative/cta/recommend/";

        /// <summary>
        /// Create a Smart Fix task endpoint (v1.3)
        /// </summary>
        public const string CreateSmartFixTask = "/video/fix/task/create/";

        /// <summary>
        /// Get the results of a Smart Fix task endpoint (v1.3)
        /// </summary>
        public const string GetSmartFixTaskResults = "/video/fix/task/get/";

        /// <summary>
        /// Get Creative Fatigue Detection results endpoint (v1.3)
        /// </summary>
        public const string GetCreativeFatigueResults = "/creative_fatigue/get/";
    }
}
