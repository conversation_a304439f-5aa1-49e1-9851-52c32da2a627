param (
    [string]$modulePath = '',
    [string]$webAppFolder = '',
    [string]$outputFolder = '',
    [string]$artifactName = '',
    [string]$version = 'latest'
)

$currentFolder = $PSScriptRoot
$slnFolder = Join-Path $currentFolder "../../"
Set-Location $slnFolder

$moduleFolder = Join-Path $slnFolder $modulePath
$webFolder = Join-Path $moduleFolder $webAppFolder
$outputPath = Join-Path $slnFolder $outputFolder

Write-Host "🔄 Begin restore NuGet packages"
Set-Location $moduleFolder
dotnet restore  "$moduleFolder\src\TikTok.Web\TikTok.Web.csproj"  --configfile NuGet.Config
Write-Host "🔄 End restore NuGet packages"

Write-Host "********* BUILDING Web Application *********" -ForegroundColor Green
Write-Host "Begin install ABP libs" -ForegroundColor Green
$installLibsResult = & abp install-libs
if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ abp install-libs failed"
    exit 1
}
Write-Host "End install ABP libs" -ForegroundColor Green

# 🔧 Build WebApp
Set-Location $slnFolder
$webOutputPath = Join-Path $outputPath "web"
Write-Host "🔄 Publishing WebApp to $webOutputPath"
dotnet publish $webFolder -c Release -o $webOutputPath

# ⚙️ Build DbMigrator
$migratorFolder = Join-Path $moduleFolder "src/TikTok.DbMigrator"
$migratorOutputPath = Join-Path $outputPath "migrator"
Write-Host "🔄 Publishing DbMigrator to $migratorOutputPath"
dotnet publish $migratorFolder -c Release -o $migratorOutputPath

Write-Host "✅ Publish completed"
