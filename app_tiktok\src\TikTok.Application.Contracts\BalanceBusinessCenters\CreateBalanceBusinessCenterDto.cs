using System;
using System.ComponentModel.DataAnnotations;

namespace TikTok.BalanceBusinessCenters
{
    /// <summary>
    /// DTO cho việc tạo mới ngân sách Business Center
    /// </summary>
    public class CreateBalanceBusinessCenterDto
    {
        /// <summary>
        /// ID của Business Center
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// Tên của Business Center
        /// </summary>
        [StringLength(512)]
        public string? BcName { get; set; }

        /// <summary>
        /// Tiền tệ của Business Center
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Currency { get; set; }

        /// <summary>
        /// Tổng số dư của Business Center, làm tròn đến hai chữ số thập phân
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal? AccountBalance { get; set; }

        /// <summary>
        /// Số dư tài khoản hợp lệ của Business Center, làm tròn đến hai chữ số thập phân
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal? ValidAccountBalance { get; set; }

        /// <summary>
        /// Số dư bị đóng băng của Business Center, làm tròn đến hai chữ số thập phân
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal? FrozenBalance { get; set; }

        /// <summary>
        /// Thuế của Business Center, làm tròn đến hai chữ số thập phân
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal? Tax { get; set; }

        /// <summary>
        /// Số dư tiền mặt của Business Center, làm tròn đến hai chữ số thập phân
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal? CashBalance { get; set; }

        /// <summary>
        /// Số dư tiền mặt hợp lệ của Business Center, làm tròn đến hai chữ số thập phân
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal? ValidCashBalance { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher của Business Center, làm tròn đến hai chữ số thập phân
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal? GrantBalance { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher hợp lệ của Business Center, làm tròn đến hai chữ số thập phân
        /// </summary>
        [Range(0, double.MaxValue)]
        public decimal? ValidGrantBalance { get; set; }

        /// <summary>
        /// Thời gian nhận ngân sách
        /// </summary>
        public DateTime? Date { get; set; }

        /// <summary>
        /// Múi giờ của ngân sách
        /// </summary>
        [StringLength(50)]
        public string? Timezone { get; set; }
    }
}