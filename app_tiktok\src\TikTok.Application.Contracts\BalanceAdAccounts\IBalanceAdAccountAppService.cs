using System;
using System.Threading.Tasks;
using TikTok.BalanceAdAccounts;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// Interface cho BalanceAdAccount App Service
    /// </summary>
    public interface IBalanceAdAccountAppService :
        ICrudAppService<
            BalanceAdAccountDto,
            Guid,
            GetBalanceAdAccountListDto,
            CreateBalanceAdAccountDto,
            UpdateBalanceAdAccountDto>
    {
        /// <summary>
        /// Lấy tài khoản nhà quảng cáo theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của nhà quảng cáo</param>
        /// <returns>Tài khoản nhà quảng cáo</returns>
        Task<BalanceAdAccountDto> GetByAdvertiserIdAsync(string advertiserId);

        /// <summary>
        /// Lấy tài khoản nhà quảng cáo theo Advertiser ID và ngày
        /// </summary>
        /// <param name="advertiserId">ID của nhà quảng cáo</param>
        /// <param name="date">Ngày cần lấy dữ liệu</param>
        /// <returns>Tài khoản nhà quảng cáo</returns>
        Task<BalanceAdAccountDto> GetByAdvertiserIdAndDateAsync(string advertiserId, DateTime date);

        /// <summary>
        /// Lấy bản ghi mới nhất theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của nhà quảng cáo</param>
        /// <returns>Bản ghi mới nhất</returns>
        Task<BalanceAdAccountDto> GetLatestByAdvertiserIdAsync(string advertiserId);

        /// <summary>
        /// Lấy danh sách tài khoản nhà quảng cáo theo Business Center ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo</returns>
        Task<PagedResultDto<BalanceAdAccountDto>> GetByBcIdAsync(string bcId);

        /// <summary>
        /// Lấy danh sách tài khoản nhà quảng cáo theo Advertiser Status
        /// </summary>
        /// <param name="advertiserStatus">Trạng thái nhà quảng cáo</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo</returns>
        Task<PagedResultDto<BalanceAdAccountDto>> GetByAdvertiserStatusAsync(AdvertiserAccountStatus advertiserStatus);

        /// <summary>
        /// Lấy danh sách tài khoản nhà quảng cáo theo Advertiser Type
        /// </summary>
        /// <param name="advertiserType">Loại nhà quảng cáo</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo</returns>
        Task<PagedResultDto<BalanceAdAccountDto>> GetByAdvertiserTypeAsync(AdAccountType advertiserType);

        /// <summary>
        /// Lấy danh sách tài khoản nhà quảng cáo theo Currency
        /// </summary>
        /// <param name="currency">Tiền tệ</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo</returns>
        Task<PagedResultDto<BalanceAdAccountDto>> GetByCurrencyAsync(string currency);

        /// <summary>
        /// Lấy danh sách tài khoản nhà quảng cáo có số dư thấp
        /// </summary>
        /// <param name="minBalance">Số dư tối thiểu</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo</returns>
        Task<PagedResultDto<BalanceAdAccountDto>> GetLowBalanceAccountsAsync(decimal minBalance);

        /// <summary>
        /// Lấy danh sách tài khoản nhà quảng cáo theo khoảng thời gian tạo
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Danh sách tài khoản nhà quảng cáo</returns>
        Task<PagedResultDto<BalanceAdAccountDto>> GetByCreateTimeRangeAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Kiểm tra Advertiser ID đã tồn tại chưa
        /// </summary>
        /// <param name="advertiserId">Advertiser ID cần kiểm tra</param>
        /// <param name="excludeId">ID cần loại trừ (cho update)</param>
        /// <returns>True nếu đã tồn tại</returns>
        Task<bool> ExistsByAdvertiserIdAsync(string advertiserId, Guid? excludeId = null);

        /// <summary>
        /// Cập nhật trạng thái tài khoản nhà quảng cáo
        /// </summary>
        /// <param name="id">ID của tài khoản</param>
        /// <param name="status">Trạng thái mới</param>
        /// <returns>Tài khoản đã cập nhật</returns>
        Task<BalanceAdAccountDto> UpdateStatusAsync(Guid id, AdvertiserAccountStatus status);

        /// <summary>
        /// Cập nhật số dư tài khoản nhà quảng cáo
        /// </summary>
        /// <param name="id">ID của tài khoản</param>
        /// <param name="newBalance">Số dư mới</param>
        /// <returns>Tài khoản đã cập nhật</returns>
        Task<BalanceAdAccountDto> UpdateBalanceAsync(Guid id, decimal newBalance);

        /// <summary>
        /// Cập nhật ngân sách tài khoản nhà quảng cáo
        /// </summary>
        /// <param name="id">ID của tài khoản</param>
        /// <param name="budget">Ngân sách mới</param>
        /// <returns>Tài khoản đã cập nhật</returns>
        Task<BalanceAdAccountDto> UpdateBudgetAsync(Guid id, decimal budget);

        /// <summary>
        /// Lấy thống kê tổng quan về tài khoản nhà quảng cáo
        /// </summary>
        /// <returns>Thống kê tổng quan</returns>
        Task<BalanceAdAccountStatisticsDto> GetStatisticsAsync();
    }
}