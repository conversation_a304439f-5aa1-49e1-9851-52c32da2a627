# RawGmvMaxProductCampaignReportEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawGmvMaxProductCampaignReports`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawGmvMaxProductCampaignReports`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho báo cáo cấp chiến dịch của Product GMV Max Campaign. Lưu trữ thông tin tổng quan về hiệu suất chiến dịch Product GMV Max bao gồm cấu hình chiến dịch, ng<PERSON> s<PERSON>ch, mục tiêu tối ưu và các metrics hiệu suất theo thời gian.

## Bảng dữ liệu: RawGmvMaxProductCampaignReports

| Tên Field                       | Kiểu dữ liệu     | Bắ<PERSON> buộc | <PERSON><PERSON> dài | <PERSON><PERSON> tả                                                                    |
| ------------------------------- | ---------------- | -------- | ------ | ------------------------------------------------------------------------ |
| Id                              | Guid             | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                   |
| BcId                            | string           | ✅       | 100    | ID Business Center                                                       |
| AdvertiserId                    | string           | ✅       | 100    | ID nhà quảng cáo                                                         |
| StoreId                         | string           | ✅       | 100    | ID TikTok Shop                                                           |
| CampaignId                      | string           | ✅       | 100    | ID chiến dịch GMV Max                                                    |
| CampaignName                    | string           | ❌       | 500    | Tên chiến dịch                                                           |
| OperationStatus                 | string           | ❌       | 20     | Trạng thái ON/OFF chiến dịch (ENABLE, DISABLE)                          |
| ScheduleType                    | string           | ❌       | 50     | Loại lịch trình (Continuously)                                          |
| ScheduleStartTime               | DateTime?        | ❌       | -      | Thời gian bắt đầu chiến dịch (UTC)                                       |
| ScheduleEndTime                 | DateTime?        | ❌       | -      | Thời gian kết thúc chiến dịch (UTC)                                      |
| BidType                         | string           | ❌       | 20     | Chế độ tối ưu (CUSTOM: Target ROI, NO_BID: Maximum delivery)             |
| TargetRoiBudget                 | decimal?         | ❌       | -      | Ngân sách Target ROI                                                     |
| MaxDeliveryBudget               | decimal?         | ❌       | -      | Ngân sách Maximum delivery                                               |
| RoasBid                         | decimal?         | ❌       | -      | Mục tiêu ROI                                                             |
| Cost                            | decimal?         | ❌       | -      | Chi phí quảng cáo (theo đơn vị tiền tệ của ad account)                   |
| NetCost                         | decimal?         | ❌       | -      | Chi phí thực tế (trừ ad credit hoặc coupon)                              |
| Orders                          | int?             | ❌       | -      | Số lượng đơn hàng SKU cá nhân                                            |
| CostPerOrder                    | decimal?         | ❌       | -      | Chi phí trung bình mỗi đơn hàng                                          |
| GrossRevenue                    | decimal?         | ❌       | -      | Tổng doanh thu gộp từ TikTok Shop                                        |
| ROI                             | decimal?         | ❌       | -      | Tỷ suất lợi nhuận (Return on Investment)                                |
| Currency                        | string           | ✅       | 10     | Tiền tệ - Mã tiền tệ, ví dụ: USD                                         |
| Date                            | DateTime         | ✅       | -      | Ngày giờ tổng hợp báo cáo, UTC format yyyy-MM-dd HH:00:00 (theo giờ)    |
| CreationTime                    | DateTime         | ✅       | -      | Thời gian tạo bản ghi (Audit)                                           |
| CreatorId                       | Guid?            | ❌       | -      | ID người tạo (Audit)                                                     |
| LastModificationTime            | DateTime?        | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                          |
| LastModifierId                  | Guid?            | ❌       | -      | ID người sửa đổi cuối (Audit)                                           |

## Cấu trúc dữ liệu

### Thông tin nhận dạng
-   **BcId**: ID Business Center quản lý
-   **AdvertiserId**: ID tài khoản nhà quảng cáo
-   **StoreId**: ID TikTok Shop liên kết
-   **CampaignId**: ID duy nhất của chiến dịch GMV Max

### Thông tin chiến dịch
-   **CampaignName**: Tên hiển thị của chiến dịch
-   **OperationStatus**: Trạng thái hoạt động (ENABLE/DISABLE)
-   **ScheduleType**: Loại lịch trình chạy
-   **ScheduleStartTime/ScheduleEndTime**: Thời gian bắt đầu và kết thúc

### Cấu hình tối ưu hóa
-   **BidType**: Chế độ tối ưu (CUSTOM/NO_BID)
-   **TargetRoiBudget**: Ngân sách cho Target ROI
-   **MaxDeliveryBudget**: Ngân sách cho Maximum delivery
-   **RoasBid**: Mục tiêu ROI

### Metrics hiệu suất tài chính
-   **Cost**: Chi phí quảng cáo tổng
-   **NetCost**: Chi phí thực tế sau chiết khấu
-   **Orders**: Số lượng đơn hàng SKU cá nhân
-   **CostPerOrder**: Chi phí trung bình mỗi đơn hàng
-   **GrossRevenue**: Tổng doanh thu gộp
-   **ROI**: Tỷ suất lợi nhuận

### Thông tin báo cáo
-   **Currency**: Mã tiền tệ
-   **Date**: Thời điểm tổng hợp báo cáo

## Phương thức đặc biệt

### HasChanged()

```csharp
public bool HasChanged(RawGmvMaxProductCampaignReportEntity other)
```

So sánh entity hiện tại với entity khác để phát hiện thay đổi. Bao gồm tất cả các trường thông tin campaign, cấu hình tối ưu hóa và metrics hiệu suất.

### UpdateFrom()

```csharp
public void UpdateFrom(RawGmvMaxProductCampaignReportEntity other)
```

Cập nhật tất cả các trường của entity hiện tại từ entity khác. Được sử dụng để đồng bộ dữ liệu khi có thay đổi.

## Đặc điểm chính của Product GMV Max Campaign Report

### Campaign-Level Overview
-   **Aggregated Metrics**: Tổng hợp hiệu suất toàn campaign
-   **Budget Management**: Quản lý ngân sách và chi phí
-   **ROI Tracking**: Theo dõi tỷ suất lợi nhuận

### Product Campaign Optimization
-   **Target ROI**: Tối ưu hóa theo mục tiêu ROI
-   **Budget Allocation**: Phân bổ ngân sách thông minh
-   **Performance Monitoring**: Theo dõi hiệu suất real-time

### TikTok Shop Integration
-   **Shop Revenue**: Doanh thu từ TikTok Shop
-   **Order Tracking**: Theo dõi đơn hàng chi tiết
-   **Product Sales**: Bán hàng qua GMV Max

## Mục đích sử dụng

-   Báo cáo hiệu suất tổng quan chiến dịch Product GMV Max
-   Theo dõi ROI và hiệu quả tài chính campaign-level
-   Quản lý ngân sách và tối ưu hóa chi phí
-   Phân tích hiệu suất sản phẩm tổng thể
-   Tích hợp với TikTok Shop ecosystem
-   Hỗ trợ ra quyết định tối ưu hóa campaign

## Quan hệ với các entity khác

-   **RawGmvMaxCampaignsEntity**: Many-to-One, thuộc về chiến dịch GMV Max
-   **RawGmvMaxProductDetailProductReportEntity**: One-to-Many, chi tiết từng sản phẩm
-   **RawGmvMaxProductCreativeReportEntity**: One-to-Many, chi tiết từng creative

## Use Cases thực tế

### Campaign Performance Overview
```
Tổng quan hiệu suất chiến dịch Product:
- Theo dõi Cost và NetCost để đánh giá hiệu quả chi phí
- Phân tích ROI để tối ưu hóa lợi nhuận campaign
- So sánh Orders và GrossRevenue để đánh giá conversion
```

### Budget Optimization
```
Tối ưu hóa ngân sách campaign:
- So sánh TargetRoiBudget với MaxDeliveryBudget
- Điều chỉnh RoasBid dựa trên ROI thực tế
- Tối ưu hóa CostPerOrder để cải thiện efficiency
```

### Campaign Lifecycle Management
```
Quản lý vòng đời chiến dịch:
- Theo dõi OperationStatus để quản lý trạng thái
- Phân tích ScheduleStartTime và ScheduleEndTime
- Tối ưu hóa BidType dựa trên performance data
```

### ROI Analysis
```
Phân tích tỷ suất lợi nhuận:
- Tính toán ROI = (GrossRevenue - Cost) / Cost
- So sánh với RoasBid để đánh giá hiệu quả
- Optimization strategy dựa trên ROI trends
```