/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using TikTokBusinessApi.Utilities;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use bulk operations for efficient batch processing
    /// </summary>
    public class BulkOperationsExample
    {
        private readonly TikTokBusinessApiClient _client;
        private readonly ILogger<BulkOperationsExample> _logger;

        public BulkOperationsExample(TikTokBusinessApiClient client, ILogger<BulkOperationsExample> logger)
        {
            _client = client;
            _logger = logger;
        }

        /// <summary>
        /// Example of bulk processing campaign IDs with batch operations
        /// </summary>
        /// <param name="accessToken">Access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task BulkUpdateCampaignStatusExample(string accessToken, string advertiserId)
        {
            try
            {
                Console.WriteLine("=== Bulk Campaign Status Update Example ===");

                // Define campaign IDs to update
                var campaignIds = new List<string>
                {
                    "campaign_001", "campaign_002", "campaign_003",
                    "campaign_004", "campaign_005", "campaign_006"
                };

                Console.WriteLine($"Updating status for {campaignIds.Count} campaigns...");

                // Create operation to update campaign status
                var updateOperation = async (IEnumerable<string> batch, CancellationToken ct) =>
                {
                    var request = new CampaignStatusUpdateRequest
                    {
                        AdvertiserId = advertiserId,
                        CampaignIds = batch.ToList(),
                        OperationStatus = "ENABLE"
                    };

                    return await _client.Campaign.UpdateCampaignStatusAsync(accessToken, request, ct);
                };

                // Perform bulk update with batch size of 3
                var result = await BulkOperations.ExecuteBulkOperationAsync(
                    campaignIds, updateOperation, batchSize: 3, logger: _logger);

                // Display results
                Console.WriteLine($"✅ Successful operations: {result.SuccessfulOperations.Count}");
                Console.WriteLine($"❌ Failed operations: {result.FailedOperations.Count}");
                Console.WriteLine($"📊 Success rate: {result.SuccessRate:F1}%");

                if (result.FailedOperations.Any())
                {
                    Console.WriteLine("\nFailed operations:");
                    foreach (var failure in result.FailedOperations)
                    {
                        Console.WriteLine($"  - Error: {failure.Error}");
                        Console.WriteLine($"  - Retry count: {failure.RetryCount}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in bulk campaign status update example");
                Console.WriteLine($"❌ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of simple batching operations
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        public async Task SimpleBatchingExample()
        {
            try
            {
                Console.WriteLine("=== Simple Batching Example ===");

                // Example: Process a large list of items in batches
                var items = Enumerable.Range(1, 25).Select(i => $"item_{i:D3}").ToList();
                Console.WriteLine($"Processing {items.Count} items in batches...");

                // Split into batches of 5
                var batches = items.Batch(5);

                var batchNumber = 1;
                foreach (var batch in batches)
                {
                    Console.WriteLine($"Batch {batchNumber}: {string.Join(", ", batch)}");

                    // Simulate processing time
                    await Task.Delay(100);
                    batchNumber++;
                }

                Console.WriteLine("✅ Batching completed!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in simple batching example");
                Console.WriteLine($"❌ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of parallel bulk operations
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        public async Task ParallelBulkOperationExample()
        {
            try
            {
                Console.WriteLine("=== Parallel Bulk Operation Example ===");

                var items = Enumerable.Range(1, 20).Select(i => $"task_{i:D2}").ToList();
                Console.WriteLine($"Processing {items.Count} items in parallel batches...");

                // Create a mock operation that simulates work
                var operation = async (IEnumerable<string> batch, CancellationToken ct) =>
                {
                    var batchList = batch.ToList();
                    Console.WriteLine($"  Processing batch: {string.Join(", ", batchList)}");

                    // Simulate async work
                    await Task.Delay(200, ct);

                    return $"Processed {batchList.Count} items";
                };

                // Execute with parallel processing
                var result = await BulkOperations.ExecuteParallelBulkOperationAsync(
                    items, operation, batchSize: 4, maxConcurrency: 3, logger: _logger);

                Console.WriteLine($"✅ Successful operations: {result.SuccessfulOperations.Count}");
                Console.WriteLine($"❌ Failed operations: {result.FailedOperations.Count}");
                Console.WriteLine($"📊 Success rate: {result.SuccessRate:F1}%");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in parallel bulk operation example");
                Console.WriteLine($"❌ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of bulk retrieving campaigns across multiple advertisers
        /// </summary>
        /// <param name="accessToken">Access token</param>
        /// <param name="advertiserIds">List of advertiser IDs</param>
        /// <returns>Task representing the async operation</returns>
        public async Task BulkGetCampaignsExample(string accessToken, List<string> advertiserIds)
        {
            try
            {
                Console.WriteLine("=== Bulk Get Campaigns Example ===");
                Console.WriteLine($"Retrieving campaigns for {advertiserIds.Count} advertisers...");

                // Define fields to retrieve
                var fields = new List<string>
                {
                    "campaign_id",
                    "campaign_name",
                    "operation_status",
                    "objective_type",
                    "budget",
                    "create_time"
                };

                // Define filtering criteria
                var filtering = new CampaignFiltering
                {
                    PrimaryStatus = "ENABLE" // Only get active campaigns
                };

                var result = await _bulkOperations.BulkGetCampaignsAsync(
                    accessToken, advertiserIds, fields, filtering);

                Console.WriteLine($"✅ Successfully retrieved campaigns from {result.SuccessfulOperations.Count} advertisers");
                Console.WriteLine($"❌ Failed to retrieve campaigns from {result.FailedOperations.Count} advertisers");

                // Display campaign summary
                var totalCampaigns = result.SuccessfulOperations.Sum(r => r.Data?.Count ?? 0);
                Console.WriteLine($"📊 Total campaigns retrieved: {totalCampaigns}");

                // Display campaigns by advertiser
                foreach (var response in result.SuccessfulOperations)
                {
                    if (response.Data?.Any() == true)
                    {
                        Console.WriteLine($"\nCampaigns for advertiser:");
                        foreach (var campaign in response.Data.Take(3)) // Show first 3 campaigns
                        {
                            Console.WriteLine($"  - {campaign.CampaignName} ({campaign.CampaignId})");
                            Console.WriteLine($"    Status: {campaign.OperationStatus}, Objective: {campaign.ObjectiveType}");
                        }

                        if (response.Data.Count > 3)
                        {
                            Console.WriteLine($"    ... and {response.Data.Count - 3} more campaigns");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in bulk get campaigns example");
                Console.WriteLine($"❌ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of bulk updating ad group statuses
        /// </summary>
        /// <param name="accessToken">Access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task BulkUpdateAdGroupStatusExample(string accessToken, string advertiserId)
        {
            try
            {
                Console.WriteLine("=== Bulk Ad Group Status Update Example ===");

                var statusUpdates = new List<AdGroupStatusUpdate>
                {
                    new() { AdgroupId = "adgroup_001", OperationStatus = "ENABLE" },
                    new() { AdgroupId = "adgroup_002", OperationStatus = "DISABLE" },
                    new() { AdgroupId = "adgroup_003", OperationStatus = "ENABLE" }
                };

                Console.WriteLine($"Updating status for {statusUpdates.Count} ad groups...");

                var result = await _bulkOperations.BulkUpdateAdGroupStatusAsync(
                    accessToken, advertiserId, statusUpdates);

                Console.WriteLine($"✅ Successful operations: {result.SuccessfulOperations.Count}");
                Console.WriteLine($"❌ Failed operations: {result.FailedOperations.Count}");
                Console.WriteLine($"📊 Success rate: {result.SuccessRate:F1}%");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in bulk ad group status update example");
                Console.WriteLine($"❌ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of bulk updating ad statuses
        /// </summary>
        /// <param name="accessToken">Access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task BulkUpdateAdStatusExample(string accessToken, string advertiserId)
        {
            try
            {
                Console.WriteLine("=== Bulk Ad Status Update Example ===");

                var statusUpdates = new List<AdStatusUpdate>
                {
                    new() { AdId = "ad_001", OperationStatus = "ENABLE" },
                    new() { AdId = "ad_002", OperationStatus = "DISABLE" },
                    new() { AdId = "ad_003", OperationStatus = "ENABLE" },
                    new() { AdId = "ad_004", OperationStatus = "DISABLE" }
                };

                Console.WriteLine($"Updating status for {statusUpdates.Count} ads...");

                var result = await _bulkOperations.BulkUpdateAdStatusAsync(
                    accessToken, advertiserId, statusUpdates);

                Console.WriteLine($"✅ Successful operations: {result.SuccessfulOperations.Count}");
                Console.WriteLine($"❌ Failed operations: {result.FailedOperations.Count}");
                Console.WriteLine($"📊 Success rate: {result.SuccessRate:F1}%");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in bulk ad status update example");
                Console.WriteLine($"❌ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Complete workflow example demonstrating multiple bulk operations
        /// </summary>
        /// <param name="accessToken">Access token</param>
        /// <param name="advertiserIds">List of advertiser IDs</param>
        /// <returns>Task representing the async operation</returns>
        public async Task CompleteWorkflowExample(string accessToken, List<string> advertiserIds)
        {
            Console.WriteLine("=== Complete Bulk Operations Workflow ===");

            // 1. Bulk retrieve campaigns
            await BulkGetCampaignsExample(accessToken, advertiserIds);

            Console.WriteLine("\n" + new string('-', 50) + "\n");

            // 2. Bulk update campaign statuses for first advertiser
            if (advertiserIds.Any())
            {
                await BulkUpdateCampaignStatusExample(accessToken, advertiserIds.First());
            }

            Console.WriteLine("\n" + new string('-', 50) + "\n");

            // 3. Bulk update ad group statuses for first advertiser
            if (advertiserIds.Any())
            {
                await BulkUpdateAdGroupStatusExample(accessToken, advertiserIds.First());
            }

            Console.WriteLine("\n" + new string('-', 50) + "\n");

            // 4. Bulk update ad statuses for first advertiser
            if (advertiserIds.Any())
            {
                await BulkUpdateAdStatusExample(accessToken, advertiserIds.First());
            }

            Console.WriteLine("\n🎉 Bulk operations workflow completed!");
        }
    }
}
