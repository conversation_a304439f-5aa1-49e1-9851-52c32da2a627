using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TikTok.Customers;
using TikTok.Entities;
using TikTok.Entities.Raws;
using TikTok.Repositories;
using TikTok.Enums;
using TikTok.Permissions;
using TikTok.Repositories;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using System.IO;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.HSSF.UserModel;
using Volo.Abp.Uow;
using Microsoft.Extensions.Logging;

namespace TikTok.Customers
{
    /// <summary>
    /// App Service implementation cho Customer
    /// </summary>
    [Authorize(TikTokPermissions.Customers.Default)]
    public class CustomerAppService :
        CrudAppService<
            CustomerEntity,                    // The Customer entity
            CustomerDto,                       // Used to show customers
            Guid,                             // Primary key of the customer entity
            GetCustomerListDto,               // Used for paging/sorting
            CreateCustomerDto,                // Used to create a new customer
            UpdateCustomerDto>,               // Used to update a customer
        ICustomerAppService
    {
        private readonly ICustomerRepository _customerRepository;
        private readonly ICustomerAdAccountRepository _customerAdAccountRepository;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly ILogger<CustomerAppService> _logger;

        public CustomerAppService(ICustomerRepository customerRepository, ICustomerAdAccountRepository customerAdAccountRepository, IUnitOfWorkManager unitOfWorkManager, ILogger<CustomerAppService> logger)
            : base(customerRepository)
        {
            _customerRepository = customerRepository;
            _customerAdAccountRepository = customerAdAccountRepository;

            GetPolicyName = TikTokPermissions.Customers.Default;
            GetListPolicyName = TikTokPermissions.Customers.Default;
            CreatePolicyName = TikTokPermissions.Customers.Create;
            UpdatePolicyName = TikTokPermissions.Customers.Edit;
            DeletePolicyName = TikTokPermissions.Customers.Delete;
            _unitOfWorkManager = unitOfWorkManager;
            _logger = logger;
        }

        public override async Task<PagedResultDto<CustomerDto>> GetListAsync(GetCustomerListDto input)
        {
            // Get the total count
            var totalCount = await _customerRepository.GetCountAsync(
                input.Filter,
                input.CustomerId,
                input.CustomerName,
                input.AccountName,
                input.PhoneNumber,
                input.CustomerType,
                input.Website,
                input.CreationTimeFrom,
                input.CreationTimeTo
            );

            // Get the customers
            var customers = await _customerRepository.GetListAsync(
                input.Sorting,
                input.MaxResultCount,
                input.SkipCount,
                input.Filter,
                input.CustomerId,
                input.CustomerName,
                input.AccountName,
                input.PhoneNumber,
                input.CustomerType,
                input.Website,
                input.CreationTimeFrom,
                input.CreationTimeTo
            );

            // Map to DTOs directly (AutoMapper will handle the One-to-Many relationship)
            var customerDtos = ObjectMapper.Map<List<CustomerEntity>, List<CustomerDto>>(customers);

            return new PagedResultDto<CustomerDto>
            {
                TotalCount = totalCount,
                Items = customerDtos
            };
        }

        public override async Task<CustomerDto> CreateAsync(CreateCustomerDto input)
        {
            // Check if CustomerId already exists
            var exists = await _customerRepository.IsCustomerIdExistsAsync(input.CustomerId);
            if (exists)
            {
                throw new UserFriendlyException(L["CustomerIdAlreadyExists", input.CustomerId]);
            }

            // Create the customer
            var customer = ObjectMapper.Map<CreateCustomerDto, CustomerEntity>(input);
            customer.AdAccounts = new List<CustomerAdAccountEntity>(); // Initialize empty collection

            await Repository.InsertAsync(customer);

            // Get customer with AdAccounts for return
            var customerWithAdAccounts = await _customerRepository.GetWithAdAccountsAsync(customer.Id);
            return ObjectMapper.Map<CustomerEntity, CustomerDto>(customerWithAdAccounts);
        }

        public override async Task<CustomerDto> UpdateAsync(Guid id, UpdateCustomerDto input)
        {
            // Check if CustomerId already exists (excluding current record)
            var exists = await _customerRepository.IsCustomerIdExistsAsync(input.CustomerId, id);
            if (exists)
            {
                throw new UserFriendlyException(L["CustomerIdAlreadyExists", input.CustomerId]);
            }

            // Get the existing customer
            var customer = await Repository.GetAsync(id);

            // Update customer properties
            ObjectMapper.Map(input, customer);

            await Repository.UpdateAsync(customer);

            // Get customer with AdAccounts for return
            var customerWithAdAccounts = await _customerRepository.GetWithAdAccountsAsync(customer.Id);
            return ObjectMapper.Map<CustomerEntity, CustomerDto>(customerWithAdAccounts);
        }

        public override async Task<CustomerDto> GetAsync(Guid id)
        {
            var customer = await _customerRepository.GetWithAdAccountsAsync(id);
            return ObjectMapper.Map<CustomerEntity, CustomerDto>(customer);
        }

        public override async Task DeleteAsync(Guid id)
        {
            // Delete AdAccounts first (cascade delete should handle this, but explicit for safety)
            var existingAdAccounts = await _customerAdAccountRepository.GetByCustomerIdAsync(id);
            if (existingAdAccounts.Any())
            {
                await _customerAdAccountRepository.DeleteManyAsync(existingAdAccounts);
            }

            await base.DeleteAsync(id);
        }

        [HttpGet]
        public async Task<AdAccountSearchResultListDto> SearchAdAccountsAsync(SearchAdAccountDto input)
        {
            // Note: This method searches RawAdAccountEntity (global ad accounts)
            // not the Customer's OwnsMany AdAccount collection
            // This functionality needs to be moved to AdAccountAppService
            // For now, returning empty result

            return new AdAccountSearchResultListDto
            {
                TotalCount = 0,
                Items = new List<AdAccountSearchResultDto>()
            };
        }

        /// <summary>
        /// Import customers from Excel bytes. Controller is responsible for receiving multipart file and passing bytes here.
        /// </summary>
        [Authorize(TikTokPermissions.Customers.Import)]
        [RemoteService(false)]
        public async Task<ImportCustomersResultDto> ImportExcelAsync(byte[] fileBytes, string fileName)
        {
            if (fileBytes == null || fileBytes.Length == 0)
            {
                throw new UserFriendlyException("File rỗng");
            }

            // 1) Parse rows
            var parsedRows = ParseRows(fileBytes, fileName);

            var result = new ImportCustomersResultDto
            {
                Total = parsedRows.Count
            };

            if (parsedRows.Count == 0)
            {
                result.Message = "File không có dữ liệu";
                return result;
            }

            // 2) Group rows by CustomerId
            var groups = parsedRows
                .Where(r => !string.IsNullOrWhiteSpace(r.CustomerId))
                .GroupBy(r => r.CustomerId?.Trim())
                .ToList();

            // Count rows that were ignored due to missing CustomerId
            var rowsWithIdCount = groups.Sum(g => g.Count());
            result.Skipped += parsedRows.Count - rowsWithIdCount;

            // 3) save to database with batch = 200
            var batchSize = 200;
            var totalBatches = (int)Math.Ceiling((double)groups.Count / batchSize);
            
            for (int i = 0; i < totalBatches; i++)
            {
                var batchGroups = groups.Skip(i * batchSize).Take(batchSize).ToList();
                await SaveBatchAsync(batchGroups, result);
            }

            result.Message = $"Imported {result.Created} new customers. Skipped rows: {result.Skipped}";
            return result;
        }

        private async Task SaveBatchAsync(List<IGrouping<string?, ParsedRow>> batchGroups, ImportCustomersResultDto result)
        {
            
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var listInsert = new List<CustomerEntity>();
                var listUpdate = new List<CustomerEntity>();
                
                var customerIds = batchGroups.Select(x => x.Key).ToList();
                var allCustomers = await _customerRepository.GetListAsync(x => customerIds.Contains(x.CustomerId));

                foreach (var group in batchGroups)
                {
                    var customerId = group.Key;
                    if (string.IsNullOrEmpty(customerId))
                        continue;

                    var first = group.First();
                    var normalizedCustomerId = customerId.Trim();

                    var existing = allCustomers.FirstOrDefault(x => x.CustomerId.ToLower().Trim().Equals(normalizedCustomerId.ToLower().Trim(), StringComparison.InvariantCultureIgnoreCase));
                    if (existing != null)
                    {
                        // Update existing customer
                        existing.CustomerName = string.IsNullOrWhiteSpace(first.CustomerName) ? first.CustomerId! : first.CustomerName!;
                        existing.PhoneNumber = string.IsNullOrWhiteSpace(first.PhoneNumber) ? null : first.PhoneNumber;
                        existing.CustomerType = NormalizeCustomerType(first.CustomerTypeText);
                        
                        // Create new ad accounts for existing customer
                        var adAccounts = group.Select(x => new CustomerAdAccountEntity(Guid.NewGuid())
                        {
                            CustomerId = existing.Id,
                            AdvertiserId = string.IsNullOrWhiteSpace(x.AdvertiserId) ? null : x.AdvertiserId,
                            AdvertiserName = string.IsNullOrWhiteSpace(x.AdvertiserName) ? null : x.AdvertiserName,
                            ShopId = string.IsNullOrWhiteSpace(x.ShopId) ? null : x.ShopId,
                            ShopName = string.IsNullOrWhiteSpace(x.ShopName) ? null : x.ShopName
                        }).ToList();
                        
                        await _customerAdAccountRepository.InsertManyAsync(adAccounts);
                        listUpdate.Add(existing);
                    }
                    else
                    {
                        var customer = new CustomerEntity(Guid.NewGuid())
                        {
                            CustomerId = first.CustomerId!,
                            CustomerName = string.IsNullOrWhiteSpace(first.CustomerName) ? first.CustomerId! : first.CustomerName!,
                            AccountName = null, // Not provided by file (Tên tài khoản quảng cáo is for ad account)
                            PhoneNumber = string.IsNullOrWhiteSpace(first.PhoneNumber) ? null : first.PhoneNumber,
                            CustomerType = NormalizeCustomerType(first.CustomerTypeText),
                            Website = null
                        };

                        listInsert.Add(customer);
                        result.Created++;

                        // Create AdAccounts for new customer
                        var adAccounts = group.Select(x => new CustomerAdAccountEntity(Guid.NewGuid())
                        {
                            CustomerId = customer.Id,
                            AdvertiserId = string.IsNullOrWhiteSpace(x.AdvertiserId) ? null : x.AdvertiserId,
                            AdvertiserName = string.IsNullOrWhiteSpace(x.AdvertiserName) ? null : x.AdvertiserName,
                            ShopId = string.IsNullOrWhiteSpace(x.ShopId) ? null : x.ShopId,
                            ShopName = string.IsNullOrWhiteSpace(x.ShopName) ? null : x.ShopName
                        }).ToList();

                        await _customerAdAccountRepository.InsertManyAsync(adAccounts);
                    }
                }

                if (listInsert.Count > 0)
                {
                    await _customerRepository.InsertManyAsync(listInsert);
                }
                if (listUpdate.Count > 0)
                {
                    await _customerRepository.UpdateManyAsync(listUpdate);
                }

                await uow.CompleteAsync();
            }
        }

        private sealed class ParsedRow
        {
            public string? CustomerId { get; set; }
            public string? CustomerName { get; set; }
            public string? AdvertiserName { get; set; }
            public string? AdvertiserId { get; set; }
            public string? ShopName { get; set; }
            public string? ShopId { get; set; }
            public string? PhoneNumber { get; set; }
            public string? CustomerTypeText { get; set; }
        }

        private List<ParsedRow> ParseRows(byte[] fileBytes, string fileName)
        {
            if (fileName.EndsWith(".csv", StringComparison.OrdinalIgnoreCase))
            {
                return ParseCsv(fileBytes);
            }

            using var stream = new MemoryStream(fileBytes);
            IWorkbook workbook;
            if (fileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
            {
                workbook = new XSSFWorkbook(stream);
            }
            else if (fileName.EndsWith(".xls", StringComparison.OrdinalIgnoreCase))
            {
                workbook = new HSSFWorkbook(stream);
            }
            else
            {
                throw new UserFriendlyException("Định dạng file không được hỗ trợ. Hãy dùng .xlsx, .xls hoặc .csv");
            }

            var rows = new List<ParsedRow>();
            var sheet = workbook.GetSheetAt(0);
            if (sheet == null)
            {
                return rows;
            }

            for (int r = 1; r <= sheet.LastRowNum; r++)
            {
                var row = sheet.GetRow(r);
                if (row == null) continue;

                rows.Add(new ParsedRow
                {
                    CustomerId = row.GetCell(1)?.ToString()?.Trim(),
                    CustomerName = row.GetCell(2)?.ToString()?.Trim(),
                    AdvertiserName = row.GetCell(3)?.ToString()?.Trim(),
                    AdvertiserId = row.GetCell(4)?.ToString()?.Trim(),
                    ShopName = row.GetCell(5)?.ToString()?.Trim(),
                    ShopId = row.GetCell(6)?.ToString()?.Trim(),
                    PhoneNumber = row.GetCell(9)?.ToString()?.Trim(),
                    CustomerTypeText = row.GetCell(10)?.ToString()?.Trim(),
                });
            }

            // Count skipped rows missing essential fields
            return rows;
        }

        private List<ParsedRow> ParseCsv(byte[] fileBytes)
        {
            var rows = new List<ParsedRow>();
            using var reader = new StreamReader(new MemoryStream(fileBytes));
            // Skip header
            string? line = reader.ReadLine();
            while ((line = reader.ReadLine()) != null)
            {
                var cells = line.Split(',');
                if (cells.Length < 11)
                {
                    continue;
                }
                rows.Add(new ParsedRow
                {
                    CustomerId = cells[1]?.Trim(),
                    CustomerName = cells[2]?.Trim(),
                    AdvertiserName = cells[3]?.Trim(),
                    AdvertiserId = cells[4]?.Trim(),
                    ShopName = cells[5]?.Trim(),
                    ShopId = cells[6]?.Trim(),
                    PhoneNumber = cells[9]?.Trim(),
                    CustomerTypeText = cells[10]?.Trim(),
                });
            }
            return rows;
        }

        private CustomerType NormalizeCustomerType(string? typeText)
        {
            if (string.IsNullOrWhiteSpace(typeText))
                return CustomerType.RETAIL;

            var normalized = typeText.ToLowerInvariant();
            if (normalized.Contains("đại lý") || normalized.Contains("dai ly") || normalized.Contains("agency"))
            {
                return CustomerType.AGENCY;
            }
            if (normalized.Contains("khách lẻ") || normalized.Contains("khach le") || normalized.Contains("retail"))
            {
                return CustomerType.RETAIL;
            }
            return CustomerType.RETAIL;
        }
    }
}
