using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using TikTok.Enums;
using TikTok.Entities;
using TikTok.Repositories;
using TikTok.Application.Contracts.LatestNotifications;
using Custom.BaseActivities;
using Volo.Abp.ObjectMapping;

namespace TikTok.Activities.SendMessages
{
    [Activity(Category = "Thông báo", DisplayName = "Lấy thông báo mới nhất", Outcomes = new[] { OutcomeNames.Done })]
    public class GetLatestNotificationsActivity : UnitOfWorkActivity
    {
        private readonly ILatestNotificationRepository _latestNotificationRepository;
        private readonly ILogger<GetLatestNotificationsActivity> _logger;
        private readonly IObjectMapper _objectMapper;

        public GetLatestNotificationsActivity(
            ILatestNotificationRepository latestNotificationRepository,
            ILogger<GetLatestNotificationsActivity> logger,
            IObjectMapper objectMapper)
        {
            _latestNotificationRepository = latestNotificationRepository;
            _logger = logger;
            _objectMapper = objectMapper;
        }

        [ActivityInput(Hint = "Loại thông báo", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public NotificationType NotificationType { get; set; }

        [ActivityInput(Hint = "Danh sách ID của đối tượng", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public List<string> ObjectIds { get; set; } = new List<string>();

        [ActivityOutput(Hint = "Danh sách thông báo mới nhất")]
        public List<LatestNotificationDto> LatestNotifications { get; set; } = new List<LatestNotificationDto>();

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            if (ObjectIds == null || ObjectIds.Count == 0)
            {
                LatestNotifications = new List<LatestNotificationDto>();
                return Done(LatestNotifications);
            }

            // Lấy thông báo theo Type và danh sách objectIds
            var notifications = await _latestNotificationRepository.GetByTypeAndObjectIdsAsync(NotificationType, ObjectIds);

            // Map entities to DTOs using AutoMapper
            LatestNotifications = _objectMapper.Map<List<LatestNotificationEntity>, List<LatestNotificationDto>>(notifications) ?? new List<LatestNotificationDto>();

            return Done(LatestNotifications);
        }
    }
}