using System.ComponentModel.DataAnnotations;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// DTO cho số tiền tối thiểu bạn có thể chuyển vào tài khoản quảng cáo
    /// </summary>
    public class MinTransferableAmountDto
    {
        /// <summary>
        /// Số tiền tối thiểu có thể chuyển
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Amount phải lớn hơn hoặc bằng 0")]
        public decimal Amount { get; set; }

        /// <summary>
        /// Đơn vị tiền tệ
        /// </summary>
        [StringLength(10, ErrorMessage = "Currency không được vượt quá 10 ký tự")]
        public string Currency { get; set; }

        /// <summary>
        /// Mô tả về hạn chế
        /// </summary>
        [StringLength(500, ErrorMessage = "Description không được vượt quá 500 ký tự")]
        public string Description { get; set; }
    }
}