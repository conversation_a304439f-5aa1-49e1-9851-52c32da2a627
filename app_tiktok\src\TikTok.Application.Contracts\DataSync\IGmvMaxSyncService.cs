using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ tối ưu dữ liệu GMV Max
    /// </summary>
    public interface IGmvMaxSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ tất cả dữ liệu GMV Max cho một Business Center
        /// Bao gồm: Chiến dịch, báo cáo chiến dịch, báo cáo sản phẩm, báo cáo sáng tạo
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ tổng hợp</returns>
        Task<GmvMaxSyncResult> SyncGmvMaxAsync(string bcId,DateTime? startDate=null,DateTime? endDate = null);

        /// <summary>
        /// Đồng bộ tất cả dữ liệu GMV Max cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ tổng hợp</returns>
        Task<GmvMaxSyncResult> SyncAllGmvMaxAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ tổng hợp dữ liệu GMV Max
    /// </summary>
    public class GmvMaxSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số Advertiser đã đồng bộ
        /// </summary>
        public int AdvertiserCount { get; set; }

        /// <summary>
        /// Kết quả đồng bộ chiến dịch
        /// </summary>
        public GmvMaxCampaignSyncResult? CampaignSyncResult { get; set; }

        /// <summary>
        /// Kết quả đồng bộ báo cáo chiến dịch
        /// </summary>
        public GmvMaxProductCampaignSyncResult? ProductCampaignSyncResult { get; set; }

        /// <summary>
        /// Kết quả đồng bộ báo cáo sản phẩm
        /// </summary>
        public GmvMaxProductDetailProductSyncResult? ProductDetailProductSyncResult { get; set; }

        /// <summary>
        /// Kết quả đồng bộ báo cáo sáng tạo
        /// </summary>
        public GmvMaxProductCreativeSyncResult? ProductCreativeSyncResult { get; set; }

        /// <summary>
        /// Kết quả đồng bộ báo cáo live campaign
        /// </summary>
        public GmvMaxLiveCampaignSyncResult? LiveCampaignSyncResult { get; set; }

        /// <summary>
        /// Kết quả đồng bộ báo cáo live livestream
        /// </summary>
        public GmvMaxLiveLivestreamSyncResult? LiveLivestreamSyncResult { get; set; }

        /// <summary>
        /// Thời gian bắt đầu
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Thời gian kết thúc
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Tổng thời gian thực hiện
        /// </summary>
        public TimeSpan Duration => EndTime - StartTime;

        public override int TotalSynced => 
            (CampaignSyncResult?.TotalSynced ?? 0) +
            (ProductCampaignSyncResult?.TotalSynced ?? 0) +
            (ProductDetailProductSyncResult?.TotalSynced ?? 0) +
            (ProductCreativeSyncResult?.TotalSynced ?? 0) +
            (LiveCampaignSyncResult?.TotalSynced ?? 0) +
            (LiveLivestreamSyncResult?.TotalSynced ?? 0);
    }
} 