using System;
using TikTok.Enums;

namespace TikTok.JobManagement
{
    /// <summary>
    /// DTO cho thống kê hệ thống job
    /// </summary>
    public class JobSystemStatisticsDto
    {
        /// <summary>
        /// Tổng số worker
        /// </summary>
        public int TotalWorkers { get; set; }

        /// <summary>
        /// Số worker đang làm việc
        /// </summary>
        public int ActiveWorkers { get; set; }

        /// <summary>
        /// Số worker rảnh
        /// </summary>
        public int IdleWorkers { get; set; }

        /// <summary>
        /// Số worker có lỗi
        /// </summary>
        public int ErrorWorkers { get; set; }

        /// <summary>
        /// Số worker timeout
        /// </summary>
        public int TimeoutWorkers { get; set; }

        /// <summary>
        /// Số job đang chờ xử lý
        /// </summary>
        public int PendingJobs { get; set; }

        /// <summary>
        /// Số job hoàn thành hôm nay
        /// </summary>
        public int CompletedJobsToday { get; set; }

        /// <summary>
        /// Tổng số job
        /// </summary>
        public int TotalJobs { get; set; }

        /// <summary>
        /// Số job đã hoàn thành
        /// </summary>
        public int CompletedJobs { get; set; }

        /// <summary>
        /// Số job lỗi
        /// </summary>
        public int FailedJobs { get; set; }

        /// <summary>
        /// Tỷ lệ thành công (%)
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// Thời gian xử lý trung bình (giây)
        /// </summary>
        public double AvgProcessingTime { get; set; }

        /// <summary>
        /// Thời gian hoạt động cuối cùng của Manager Job
        /// </summary>
        public DateTime? LastManagerJobRun { get; set; }

        /// <summary>
        /// Thời gian hoạt động cuối cùng của Register Job
        /// </summary>
        public DateTime? LastRegisterJobRun { get; set; }

        /// <summary>
        /// Trạng thái hoạt động của hệ thống
        /// </summary>
        public bool IsSystemActive { get; set; }
    }
}