/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Catalog Event Sources endpoints
    /// </summary>
    public static class CatalogEventSourcesEndpoints
    {
        /// <summary>
        /// Bind an event source to a catalog endpoint (v1.3)
        /// </summary>
        public const string BindEventSource = "/catalog/eventsource/bind/";

        /// <summary>
        /// Unbind an event source from a catalog endpoint (v1.3)
        /// </summary>
        public const string UnbindEventSource = "/catalog/eventsource/unbind/";

        /// <summary>
        /// Get event source binding info of a catalog endpoint (v1.3)
        /// </summary>
        public const string GetEventSourceBindingInfo = "/catalog/eventsource_bind/get/";
    }
}
