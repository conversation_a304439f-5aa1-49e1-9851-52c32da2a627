﻿using AutoMapper;
using TikTok.AdAccounts;
using TikTok.Assets;
using TikTok.BusinessApplications;
using TikTok.BusinessCenters;
using TikTok.Entities;
using TikTok.Transactions;
using TikTok.BalanceBusinessCenters;
using TikTok.BalanceAdAccounts;
using TikTok.RecordTransactionAdAccounts;
using TikTok.RecordTransactionBcs;
using TikTok.JobManagement;
using TikTok.Campaigns;
using TikTok.CostProfiles;
using TikTok.ReportIntegratedBcs;
using TikTok.ReportIntegratedCampaigns;
using TikTok.ReportIntegratedAdAccounts;
using TikTok.ReportIntegratedAdGroups;
using TikTok.ReportIntegratedAds;
using TikTok.Entities.Raws;
using TikTok.FactBalances;
using TikTok.DimBusinessCenters;
using TikTok.DimAdAccounts;
using TikTok.DimDates;
using TikTok.FactCampaigns.Dtos;
using TikTok.DimCampaigns;
using TikTok.Customers;
using TikTok.Application.Contracts.Rules;
using TikTok.Domain.Entities.Rules;

namespace TikTok;

public class TikTokActivitieAutoMapperProfile : Profile
{
    public TikTokActivitieAutoMapperProfile()
    {
        // Customer AdAccount mappings
        CreateMap<RawLatestBalanceAdAccountEntity, BalanceAdAccountDto>();
        
        // Rules mappings
        CreateMap<RuleEntity, RuleDto>();
        CreateMap<RuleAdAccountEntity, RuleAdAccountFlatDto>();
    }
}
