using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;

namespace TikTok.Campaigns
{
    /// <summary>
    /// DTO cho việc cập nhật chiến dịch quảng cáo
    /// </summary>
    public class UpdateCampaignDto
    {
        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID chiến dịch
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CampaignId { get; set; }

        /// <summary>
        /// Nguồn gốc của chiến dịch
        /// </summary>
        public CampaignSystemOrigin? CampaignSystemOrigin { get; set; }

        /// <summary>
        /// Thời gian tạo chiến dịch, định dạng UTC
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// Thời gian sửa đổi chiến dịch, định dạng UTC
        /// </summary>
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// Mục tiêu quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ObjectiveType { get; set; }

        /// <summary>
        /// Loại quảng bá ứng dụng
        /// </summary>
        public AppPromotionType? AppPromotionType { get; set; }

        /// <summary>
        /// Loại mục tiêu mới
        /// </summary>
        public VirtualObjectiveType? VirtualObjectiveType { get; set; }

        /// <summary>
        /// Điểm đến bán hàng
        /// </summary>
        public SalesDestination? SalesDestination { get; set; }

        /// <summary>
        /// Cho biết chiến dịch có phải là Chiến dịch Tìm kiếm hay không
        /// </summary>
        public bool IsSearchCampaign { get; set; }

        /// <summary>
        /// Cho biết chiến dịch có phải là loại chiến dịch tự động hay không
        /// </summary>
        public bool IsSmartPerformanceCampaign { get; set; }

        /// <summary>
        /// Loại chiến dịch
        /// </summary>
        [Required]
        public CampaignType CampaignType { get; set; }

        /// <summary>
        /// ID ứng dụng được quảng bá
        /// </summary>
        [StringLength(100)]
        public string? AppId { get; set; }

        /// <summary>
        /// Cho biết chiến dịch có phải là Chiến dịch chuyên biệt nâng cao hay không
        /// </summary>
        public bool IsAdvancedDedicatedCampaign { get; set; }

        /// <summary>
        /// Cho biết có tắt tính năng phân bổ SKAN hay không
        /// </summary>
        public bool DisableSkanCampaign { get; set; }

        /// <summary>
        /// Loại phân bổ cho Chiến dịch chuyên biệt
        /// </summary>
        public BidAlignType? BidAlignType { get; set; }

        /// <summary>
        /// Trạng thái của Trang hồ sơ ứng dụng
        /// </summary>
        public CampaignAppProfilePageState? CampaignAppProfilePageState { get; set; }

        /// <summary>
        /// Loại chiến dịch Đặt chỗ cụ thể
        /// </summary>
        public RfCampaignType? RfCampaignType { get; set; }

        /// <summary>
        /// Nguồn sản phẩm của chiến dịch
        /// </summary>
        public CampaignProductSource? CampaignProductSource { get; set; }

        /// <summary>
        /// Cho biết có sử dụng danh mục để tự động quảng cáo hay không
        /// </summary>
        public bool CatalogEnabled { get; set; }

        /// <summary>
        /// Tên chiến dịch
        /// </summary>
        [Required]
        [StringLength(255)]
        public string CampaignName { get; set; }

        /// <summary>
        /// Các danh mục quảng cáo đặc biệt
        /// </summary>
        public List<SpecialIndustries>? SpecialIndustries { get; set; }

        /// <summary>
        /// Cho biết CBO có được bật hay không
        /// </summary>
        public bool BudgetOptimizeOn { get; set; }

        /// <summary>
        /// Chiến lược đặt giá thầu ở cấp chiến dịch
        /// </summary>
        [StringLength(50)]
        public string? BidType { get; set; }

        /// <summary>
        /// Chiến lược đặt giá thầu cho các sự kiện trong ứng dụng
        /// </summary>
        [StringLength(50)]
        public string? DeepBidType { get; set; }

        /// <summary>
        /// Mục tiêu ROAS cho Tối ưu hóa giá trị
        /// </summary>
        public decimal RoasBid { get; set; }

        /// <summary>
        /// Mục tiêu tối ưu hóa
        /// </summary>
        [StringLength(100)]
        public string? OptimizationGoal { get; set; }

        /// <summary>
        /// Chế độ ngân sách
        /// </summary>
        [Required]
        [StringLength(50)]
        public string BudgetMode { get; set; }

        /// <summary>
        /// Ngân sách chiến dịch
        /// </summary>
        public decimal Budget { get; set; }

        /// <summary>
        /// ID API thời gian thực (RTA)
        /// </summary>
        [StringLength(100)]
        public string? RtaId { get; set; }

        /// <summary>
        /// Cho biết có sử dụng đặt giá thầu RTA hay không
        /// </summary>
        public bool RtaBidEnabled { get; set; }

        /// <summary>
        /// Cho biết có sử dụng RTA để tự động chọn sản phẩm hay không
        /// </summary>
        public bool RtaProductSelectionEnabled { get; set; }

        /// <summary>
        /// Trạng thái hoạt động
        /// </summary>
        [Required]
        [StringLength(20)]
        public string OperationStatus { get; set; }

        /// <summary>
        /// Trạng thái chiến dịch (Trạng thái thứ cấp)
        /// </summary>
        [StringLength(100)]
        public string? SecondaryStatus { get; set; }

        /// <summary>
        /// Chế độ xác định postback SKAN 4.0
        /// </summary>
        public PostbackWindowMode? PostbackWindowMode { get; set; }

        /// <summary>
        /// Cho biết chiến dịch có cấu trúc mới hay không
        /// </summary>
        public bool IsNewStructure { get; set; }

        /// <summary>
        /// Loại chiến dịch (ứng dụng hoặc trang đích)
        /// </summary>
        [Required]
        public Objective Objective { get; set; }
    }
}