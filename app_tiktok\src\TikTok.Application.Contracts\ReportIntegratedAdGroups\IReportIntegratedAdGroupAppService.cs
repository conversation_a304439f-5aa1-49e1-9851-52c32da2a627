using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.ReportIntegratedAdGroups;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.ReportIntegratedAdGroups
{
    /// <summary>
    /// Interface service cho báo cáo tích hợp AdGroup
    /// </summary>
    public interface IReportIntegratedAdGroupAppService : 
        ICrudAppService<
            ReportIntegratedAdGroupDto,
            Guid,
            GetReportIntegratedAdGroupListDto,
            CreateReportIntegratedAdGroupDto,
            UpdateReportIntegratedAdGroupDto>
    {
        /// <summary>
        /// L<PERSON>y danh sách theo Advertiser IDs và khoảng thời gian
        /// </summary>
        /// <param name="advertiserIds">Danh sách ID tài khoản nhà quảng cáo</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu</param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kết thúc</param>
        /// <returns>Danh sách báo cáo tích hợp AdGroup</returns>
        Task<List<ReportIntegratedAdGroupDto>> GetByAdvertiserIdsAndDateRangeAsync(
            List<string> advertiserIds,
            DateTime startDate,
            DateTime endDate);

        /// <summary>
        /// Lấy báo cáo theo AdGroup ID và ngày
        /// </summary>
        /// <param name="adGroupId">ID nhóm quảng cáo</param>
        /// <param name="date">Ngày báo cáo</param>
        /// <returns>Báo cáo tích hợp AdGroup</returns>
        Task<ReportIntegratedAdGroupDto?> GetByAdGroupIdAndDateAsync(
            string adGroupId,
            DateTime date);
    }
} 