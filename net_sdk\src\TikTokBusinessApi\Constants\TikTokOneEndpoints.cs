/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok One (TTO) Business API endpoints
    /// </summary>
    public static class TikTokOneEndpoints
    {
        /// <summary>
        /// Get Creator access token endpoint (v1.3)
        /// </summary>
        public const string GetCreatorAccessToken = "/tt_user/oauth2/token/";

        /// <summary>
        /// Renew Creator access token endpoint (v1.3)
        /// </summary>
        public const string RenewCreatorAccessToken = "/tt_user/oauth2/refresh_token/";

        /// <summary>
        /// Revoke Creator access token endpoint (v1.3)
        /// </summary>
        public const string RevokeCreatorAccessToken = "/tt_user/oauth2/revoke/";

        /// <summary>
        /// Get authorized Creator permissions endpoint (v1.3)
        /// </summary>
        public const string GetCreatorTokenInfo = "/tt_user/token_info/get/";

        /// <summary>
        /// Get authorized TTO Creator Marketplace accounts endpoint (v1.3)
        /// </summary>
        public const string GetTtoCreatorMarketplaceAccounts = "/tto/oauth2/tcm/";

        /// <summary>
        /// Get TTO Creator Marketplace account details endpoint (v1.3)
        /// </summary>
        public const string GetTtoAccountInfo = "/tto/oauth2/info/";

        /// <summary>
        /// Get authorized TTO Creator insights endpoint (v1.3)
        /// </summary>
        public const string GetAuthorizedTtoCreatorInsights = "/tto/creator/authorized/";

        /// <summary>
        /// Get authorized TTO Media insights endpoint (v1.3)
        /// </summary>
        public const string GetAuthorizedTtoMediaInsights = "/tto/creator/authorized/video/list/";

        /// <summary>
        /// Get TTO Public Account insights endpoint (v1.3)
        /// </summary>
        public const string GetTtoPublicAccountInsights = "/tto/tcm/creator/public/";

        /// <summary>
        /// Get TTO creator ranking or search labels endpoint (v1.3)
        /// </summary>
        public const string GetTtoCreatorLabels = "/tto/tcm/category/label/";

        /// <summary>
        /// Get top TTO creator rankings endpoint (v1.3)
        /// </summary>
        public const string GetTtoCreatorRankings = "/tto/tcm/rank/";

        /// <summary>
        /// Discover TTO creators endpoint (v1.3)
        /// </summary>
        public const string DiscoverTtoCreators = "/tto/tcm/creator/discover/";
    }
}
