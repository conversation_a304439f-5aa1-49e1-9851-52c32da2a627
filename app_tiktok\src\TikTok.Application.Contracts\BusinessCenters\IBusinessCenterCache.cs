using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.BusinessCenters
{
    public interface IBusinessCenterCache : IScopedDependency
    {
        public Task<BusinessCenterDto?> GetById(Guid id);

        public Task<BusinessCenterDto?> GetByBcIdAsync(string bcId);

        public Task<List<BusinessCenterDto>> GetAllAsync();

        public Task CleanCache();
    }
}