/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Creative Insights operations
    /// </summary>
    public class CreativeInsightsApi : ICreativeInsightsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<CreativeInsightsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the CreativeInsightsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public CreativeInsightsApi(IApiClient apiClient, ILogger<CreativeInsightsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get ad benchmarks - Use this endpoint to get the performance data of ads against benchmarks
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting ad benchmarks</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing ad benchmark data</returns>
        public async Task<Response<AdBenchmarkResponse>> GetAdBenchmarksAsync(
            string accessToken,
            AdBenchmarkRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting ad benchmarks for advertiser {AdvertiserId}", request.AdvertiserId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["dimensions"] = JsonSerializer.Serialize(request.Dimensions)
            };

            if (!string.IsNullOrEmpty(request.CompareTimeWindow))
                queryParams["compare_time_window"] = request.CompareTimeWindow;

            if (request.MetricsFields != null && request.MetricsFields.Count > 0)
                queryParams["metrics_fields"] = JsonSerializer.Serialize(request.MetricsFields);

            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering);

            if (!string.IsNullOrEmpty(request.SortField))
                queryParams["sort_field"] = request.SortField;

            if (!string.IsNullOrEmpty(request.SortType))
                queryParams["sort_type"] = request.SortType;

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();

            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            var response = await _apiClient.CallApiAsync<AdBenchmarkResponse>(
                path: $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeInsightsEndpoints.GetAdBenchmarks}",
                method: HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: new Dictionary<string, string>
                {
                    ["Access-Token"] = accessToken
                },
                authNames: null,
                cancellationToken: cancellationToken);

            _logger?.LogDebug("Successfully retrieved ad benchmarks for advertiser {AdvertiserId}", request.AdvertiserId);

            return new Response<AdBenchmarkResponse>
            {
                Data = response,
                RequestId = null // Will be set by the API client
            };
        }

        /// <summary>
        /// Get in-second performance - Use this endpoint to get in-second performance data about ads, or Video Insights data about a video
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting video performance data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing video performance data</returns>
        public async Task<Response<VideoPerformanceResponse>> GetVideoPerformanceAsync(
            string accessToken,
            VideoPerformanceRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting video performance for advertiser {AdvertiserId}", request.AdvertiserId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["filtering"] = JsonSerializer.Serialize(request.Filtering)
            };

            if (!string.IsNullOrEmpty(request.ReportType))
                queryParams["report_type"] = request.ReportType;

            if (request.MetricsFields != null && request.MetricsFields.Count > 0)
                queryParams["metrics_fields"] = JsonSerializer.Serialize(request.MetricsFields);

            if (!string.IsNullOrEmpty(request.SortField))
                queryParams["sort_field"] = request.SortField;

            if (!string.IsNullOrEmpty(request.SortType))
                queryParams["sort_type"] = request.SortType;

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();

            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            var response = await _apiClient.CallApiAsync<VideoPerformanceResponse>(
                path: $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeInsightsEndpoints.GetVideoPerformance}",
                method: HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: new Dictionary<string, string>
                {
                    ["Access-Token"] = accessToken
                },
                authNames: null,
                cancellationToken: cancellationToken);

            _logger?.LogDebug("Successfully retrieved video performance for advertiser {AdvertiserId}", request.AdvertiserId);

            return new Response<VideoPerformanceResponse>
            {
                Data = response,
                RequestId = null // Will be set by the API client
            };
        }
    }
}
