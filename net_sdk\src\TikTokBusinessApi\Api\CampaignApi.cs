/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;
using TikTokBusinessApi.Serialization;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Campaign operations
    /// </summary>
    public class CampaignApi : ICampaignApi
    {
        private readonly IApiClient _apiClient;
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// Initializes a new instance of the CampaignApi class
        /// </summary>
        /// <param name="apiClient">API client for making requests</param>
        public CampaignApi(IApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new System.ArgumentNullException(nameof(apiClient));
            _jsonOptions = JsonSerializerOptionsExtensions.CreateDefault();

        }

        /// <inheritdoc />
        public async Task<TikTokApiResponse<CampaignGetResponse>> GetCampaignsAsync(
            string advertiserId,
            List<string>? fields = null,
            CampaignFiltering? filtering = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            if (page < 1)
                throw new System.ArgumentException("Page must be greater than 0", nameof(page));

            if (pageSize < 1 || pageSize > 1000)
                throw new System.ArgumentException("Page size must be between 1 and 1000", nameof(pageSize));

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["page"] = page.ToString(),
                ["page_size"] = pageSize.ToString()
            };

            if (fields != null)
            {
                queryParams["fields"] = string.Join(",", fields);
            }

            if (filtering != null)
            {
                queryParams["filtering"] = JsonSerializer.Serialize(filtering, _jsonOptions);
            }

            return await _apiClient.CallApiAsync<TikTokApiResponse<CampaignGetResponse>>(
                $"{CampaignEndpoints.Get}",
                System.Net.Http.HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <inheritdoc />
        // public async Task<CampaignCreateResponse> CreateCampaignsAsync(
        //     CampaignCreateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     System.ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.Campaigns == null || request.Campaigns.Count == 0)
        //         throw new System.ArgumentException("At least one campaign must be provided", nameof(request));

        //     // Validate each campaign
        //     foreach (var campaign in request.Campaigns)
        //     {
        //         if (string.IsNullOrWhiteSpace(campaign.CampaignName))
        //             throw new System.ArgumentException("Campaign name cannot be null or empty", nameof(request));

        //         if (string.IsNullOrWhiteSpace(campaign.ObjectiveType))
        //             throw new System.ArgumentException("Objective type cannot be null or empty", nameof(request));
        //     }

        //     return await _apiClient.CallApiAsync<CampaignCreateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CampaignEndpoints.Create}",
        //         System.Net.Http.HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <inheritdoc />
        // public async Task<CampaignUpdateResponse> UpdateCampaignsAsync(
        //     CampaignUpdateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     System.ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.Campaigns == null || request.Campaigns.Count == 0)
        //         throw new System.ArgumentException("At least one campaign must be provided", nameof(request));

        //     // Validate each campaign
        //     foreach (var campaign in request.Campaigns)
        //     {
        //         if (string.IsNullOrWhiteSpace(campaign.CampaignId))
        //             throw new System.ArgumentException("Campaign ID cannot be null or empty", nameof(request));
        //     }

        //     return await _apiClient.CallApiAsync<CampaignUpdateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CampaignEndpoints.Update}",
        //         System.Net.Http.HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <inheritdoc />
        // public async Task<CampaignStatusUpdateResponse> UpdateCampaignStatusAsync(
        //     CampaignStatusUpdateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     System.ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.CampaignIds == null || request.CampaignIds.Count == 0)
        //         throw new System.ArgumentException("At least one campaign ID must be provided", nameof(request));

        //     if (request.CampaignIds.Count > 20)
        //         throw new System.ArgumentException("Maximum 20 campaign IDs can be updated at once", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.OperationStatus))
        //         throw new System.ArgumentException("Operation status cannot be null or empty", nameof(request));

        //     return await _apiClient.CallApiAsync<CampaignStatusUpdateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CampaignEndpoints.UpdateStatus}",
        //         System.Net.Http.HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        [System.Obsolete("This method is deprecated. Use GetCampaignQuotaInfoAsync instead.")]
        public async Task<CampaignQuotaGetResponse> GetCampaignQuotaAsync(
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            var request = new CampaignQuotaGetRequest
            {
                AdvertiserId = advertiserId
            };

            return await _apiClient.CallApiAsync<CampaignQuotaGetResponse>(
                $"{CampaignEndpoints.GetQuota}",
                System.Net.Http.HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<CampaignQuotaInfoGetResponse> GetCampaignQuotaInfoAsync(
            CampaignQuotaInfoGetRequest request,
            CancellationToken cancellationToken = default)
        {
            System.ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (request.AdNetworkIds?.Count > 100)
                throw new System.ArgumentException("Maximum 100 ad network IDs can be provided", nameof(request));

            return await _apiClient.CallApiAsync<CampaignQuotaInfoGetResponse>(
                $"{CampaignEndpoints.GetQuotaInfo}",
                System.Net.Http.HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <inheritdoc />
        // public async Task<CampaignCopyTaskCreateResponse> CreateCampaignCopyTaskAsync(
        //     CampaignCopyTaskCreateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     System.ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.SourceCampaignId))
        //         throw new System.ArgumentException("Source campaign ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.TargetAdvertiserId))
        //         throw new System.ArgumentException("Target advertiser ID cannot be null or empty", nameof(request));

        //     return await _apiClient.CallApiAsync<CampaignCopyTaskCreateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CampaignEndpoints.CreateCopyTask}",
        //         System.Net.Http.HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        //public async Task<CampaignCopyTaskCheckResponse> CheckCampaignCopyTaskAsync(
        //    CampaignCopyTaskCheckRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    System.ArgumentNullException.ThrowIfNull(request);

        //    if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //        throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //    if (string.IsNullOrWhiteSpace(request.TaskId))
        //        throw new System.ArgumentException("Task ID cannot be null or empty", nameof(request));

        //    return await _apiClient.CallApiAsync<CampaignCopyTaskCheckResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CampaignEndpoints.CheckCopyTask}",
        //        System.Net.Http.HttpMethod.Get,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}
    }
}
