/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;

namespace TikTokBusinessApi.Exceptions
{
    /// <summary>
    /// Exception thrown when an API call fails
    /// </summary>
    public class ApiException : Exception
    {
        /// <summary>
        /// HTTP status code of the response
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// Response content
        /// </summary>
        public string? ResponseContent { get; set; }

        /// <summary>
        /// Response headers
        /// </summary>
        public Dictionary<string, string>? ResponseHeaders { get; set; }

        /// <summary>
        /// Initializes a new instance of the ApiException class
        /// </summary>
        public ApiException()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ApiException class with a message
        /// </summary>
        /// <param name="message">Exception message</param>
        public ApiException(string message) : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the ApiException class with a message and inner exception
        /// </summary>
        /// <param name="message">Exception message</param>
        /// <param name="innerException">Inner exception</param>
        public ApiException(string message, Exception innerException) : base(message, innerException)
        {
        }

        /// <summary>
        /// Initializes a new instance of the ApiException class with status code and message
        /// </summary>
        /// <param name="statusCode">HTTP status code</param>
        /// <param name="message">Exception message</param>
        public ApiException(int statusCode, string message) : base(message)
        {
            StatusCode = statusCode;
        }

        /// <summary>
        /// Initializes a new instance of the ApiException class with status code, message, and response content
        /// </summary>
        /// <param name="statusCode">HTTP status code</param>
        /// <param name="message">Exception message</param>
        /// <param name="responseContent">Response content</param>
        public ApiException(int statusCode, string message, string responseContent) : base(message)
        {
            StatusCode = statusCode;
            ResponseContent = responseContent;
        }

        /// <summary>
        /// Initializes a new instance of the ApiException class with full details
        /// </summary>
        /// <param name="statusCode">HTTP status code</param>
        /// <param name="message">Exception message</param>
        /// <param name="responseContent">Response content</param>
        /// <param name="responseHeaders">Response headers</param>
        public ApiException(int statusCode, string message, string responseContent, Dictionary<string, string> responseHeaders) 
            : base(message)
        {
            StatusCode = statusCode;
            ResponseContent = responseContent;
            ResponseHeaders = responseHeaders;
        }

        /// <summary>
        /// Returns a string representation of the exception
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ApiException: {Message} (Status: {StatusCode})\nResponse: {ResponseContent}";
        }
    }
}
