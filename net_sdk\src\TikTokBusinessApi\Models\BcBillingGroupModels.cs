/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Create Billing Group Models

    /// <summary>
    /// Request model for creating a billing group via /bc/billing_group/create/
    /// </summary>
    public class CreateBillingGroupRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Billing group name
        /// </summary>
        [JsonPropertyName("billing_group_name")]
        public string BillingGroupName { get; set; } = string.Empty;

        /// <summary>
        /// Ad account IDs that you want to include in the billing group
        /// </summary>
        [JsonPropertyName("advertiser_ids")]
        public List<string> AdvertiserIds { get; set; } = new List<string>();

        /// <summary>
        /// Billing group emails
        /// </summary>
        [JsonPropertyName("billing_group_emails")]
        public List<string>? BillingGroupEmails { get; set; }

        /// <summary>
        /// Whether to set this billing group as the default billing group for the Business Center
        /// </summary>
        [JsonPropertyName("is_primary")]
        public bool? IsPrimary { get; set; }

        /// <summary>
        /// The advertising type of all ad accounts in the billing group. This setting can not be edited.
        /// Enum values: AUCTION, RESERVATION (to be deprecated)
        /// </summary>
        [JsonPropertyName("billing_group_type")]
        public string? BillingGroupType { get; set; }
    }

    /// <summary>
    /// Data model for create billing group response
    /// </summary>
    public class CreateBillingGroupData
    {
        /// <summary>
        /// ID of the billing group that was just created
        /// </summary>
        [JsonPropertyName("billing_group_id")]
        public string? BillingGroupId { get; set; }
    }

    /// <summary>
    /// Response for create billing group
    /// </summary>
    public class CreateBillingGroupResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public CreateBillingGroupData? Data { get; set; }
    }

    #endregion

    #region Update Billing Group Models

    /// <summary>
    /// Request model for updating a billing group via /bc/billing_group/update/
    /// </summary>
    public class UpdateBillingGroupRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the billing group you'd like to edit
        /// </summary>
        [JsonPropertyName("billing_group_id")]
        public string BillingGroupId { get; set; } = string.Empty;

        /// <summary>
        /// New billing group name
        /// </summary>
        [JsonPropertyName("new_billing_group_name")]
        public string? NewBillingGroupName { get; set; }

        /// <summary>
        /// List of emails for the billing group. If this field is provided, this new list of emails will overwrite the billing group's existing emails
        /// </summary>
        [JsonPropertyName("new_billing_group_emails")]
        public List<string>? NewBillingGroupEmails { get; set; }

        /// <summary>
        /// List of ad account IDs you want to add to this billing group
        /// </summary>
        [JsonPropertyName("add_advertiser_ids")]
        public List<string>? AddAdvertiserIds { get; set; }

        /// <summary>
        /// List of ad account IDs you want to remove from this billing group
        /// </summary>
        [JsonPropertyName("delete_advertiser_ids")]
        public List<string>? DeleteAdvertiserIds { get; set; }

        /// <summary>
        /// Whether to set this billing group as the default billing group for the Business Center
        /// </summary>
        [JsonPropertyName("is_primary")]
        public bool? IsPrimary { get; set; }
    }

    /// <summary>
    /// Response for update billing group
    /// </summary>
    public class UpdateBillingGroupResponse
    {
        /// <summary>
        /// Response data (empty object)
        /// </summary>
        [JsonPropertyName("data")]
        public object? Data { get; set; }
    }

    #endregion

    #region Get Billing Groups Models

    /// <summary>
    /// Filtering conditions for getting billing groups
    /// </summary>
    public class GetBillingGroupsFiltering
    {
        /// <summary>
        /// The Billing Group status that you want to filter by. Enum values: VALID (valid), INVALID(invalid)
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// How the invoices in the billing groups are handled.
        /// Enum values: ACCOUNT: Invoices in this billing groups are combined. ADVERTISER: Invoices in this billing group are handled separately.
        /// </summary>
        [JsonPropertyName("invoice_group_by")]
        public string? InvoiceGroupBy { get; set; }

        /// <summary>
        /// Billed-to parties shown on invoices.
        /// Enum values: ACCOUNT: Invoices are billed to agencies. ADVERTISER: Invoices are billed to advertisers.
        /// </summary>
        [JsonPropertyName("billed_to_type")]
        public string? BilledToType { get; set; }

        /// <summary>
        /// Billing Group ID
        /// </summary>
        [JsonPropertyName("billing_group_id")]
        public string? BillingGroupId { get; set; }

        /// <summary>
        /// Billing group name
        /// </summary>
        [JsonPropertyName("billing_group_name")]
        public string? BillingGroupName { get; set; }

        /// <summary>
        /// The advertising type of all ad accounts in the billing group. This setting cannot be edited.
        /// Enum values: AUCTION, RESERVATION
        /// </summary>
        [JsonPropertyName("billing_group_type")]
        public string? BillingGroupType { get; set; }
    }

    /// <summary>
    /// Request model for getting billing groups via /bc/billing_group/get/
    /// </summary>
    public class GetBillingGroupsRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public GetBillingGroupsFiltering? Filtering { get; set; }

        /// <summary>
        /// Page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size, in the range of 1-50. The default value is 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Advertiser information in billing group
    /// </summary>
    public class BillingGroupAdvertiser
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Advertiser name
        /// </summary>
        [JsonPropertyName("advertiser_name")]
        public string? AdvertiserName { get; set; }
    }

    /// <summary>
    /// Billing group information for billing group management
    /// </summary>
    public class BillingGroupDetails
    {
        /// <summary>
        /// The Billing Group status. Enum values: VALID (valid), INVALID(invalid)
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Billing group ID
        /// </summary>
        [JsonPropertyName("billing_group_id")]
        public string? BillingGroupId { get; set; }

        /// <summary>
        /// Billing group name
        /// </summary>
        [JsonPropertyName("billing_group_name")]
        public string? BillingGroupName { get; set; }

        /// <summary>
        /// Invoice object name
        /// </summary>
        [JsonPropertyName("invoice_object_name")]
        public string? InvoiceObjectName { get; set; }

        /// <summary>
        /// Whether it is the default billing group of the Business Center
        /// </summary>
        [JsonPropertyName("is_primary")]
        public bool? IsPrimary { get; set; }

        /// <summary>
        /// List of advertiser accounts in this billing group
        /// </summary>
        [JsonPropertyName("advertisers")]
        public List<BillingGroupAdvertiser>? Advertisers { get; set; }

        /// <summary>
        /// Emails of the billing group
        /// </summary>
        [JsonPropertyName("billing_group_emails")]
        public List<string>? BillingGroupEmails { get; set; }

        /// <summary>
        /// How the invoices in the billing groups are handled.
        /// Enum values: ACCOUNT: Invoices in this billing groups are combined. ADVERTISER: Invoices in this billing group are handled separately.
        /// </summary>
        [JsonPropertyName("invoice_group_by")]
        public string? InvoiceGroupBy { get; set; }

        /// <summary>
        /// Billed-to parties shown on invoices.
        /// Enum values: ACCOUNT: Invoices are billed to agencies. ADVERTISER: Invoices are billed to advertisers.
        /// </summary>
        [JsonPropertyName("billed_to_type")]
        public string? BilledToType { get; set; }

        /// <summary>
        /// The advertising type of all ad accounts in the billing group. This setting cannot be edited.
        /// Enum values: AUCTION, RESERVATION
        /// </summary>
        [JsonPropertyName("billing_group_type")]
        public string? BillingGroupType { get; set; }
    }

    /// <summary>
    /// Pagination information
    /// </summary>
    public class BillingGroupPageInfo
    {
        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page Size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Total number of results
        /// </summary>
        [JsonPropertyName("total_number")]
        public int? TotalNumber { get; set; }

        /// <summary>
        /// Total pages of results
        /// </summary>
        [JsonPropertyName("total_page")]
        public int? TotalPage { get; set; }
    }

    /// <summary>
    /// Data model for get billing groups response
    /// </summary>
    public class GetBillingGroupsData
    {
        /// <summary>
        /// List of billing groups
        /// </summary>
        [JsonPropertyName("list")]
        public List<BillingGroupDetails>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public BillingGroupPageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Response for get billing groups
    /// </summary>
    public class GetBillingGroupsResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public GetBillingGroupsData? Data { get; set; }
    }

    #endregion

    #region Get Billing Group Advertisers Models

    /// <summary>
    /// Request model for getting billing group advertisers via /bc/billing_group/advertiser/list/
    /// </summary>
    public class GetBillingGroupAdvertisersRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Billing Group ID
        /// </summary>
        [JsonPropertyName("billing_group_id")]
        public string BillingGroupId { get; set; } = string.Empty;

        /// <summary>
        /// Page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size, in the range of 1-50. The default value is 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Advertiser information in billing group advertiser list
    /// </summary>
    public class BillingGroupAdvertiserInfo
    {
        /// <summary>
        /// Ad Account ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }
    }

    /// <summary>
    /// Data model for get billing group advertisers response
    /// </summary>
    public class GetBillingGroupAdvertisersData
    {
        /// <summary>
        /// List of advertisers
        /// </summary>
        [JsonPropertyName("list")]
        public List<BillingGroupAdvertiserInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public BillingGroupPageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Response for get billing group advertisers
    /// </summary>
    public class GetBillingGroupAdvertisersResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public GetBillingGroupAdvertisersData? Data { get; set; }
    }

    #endregion
}
