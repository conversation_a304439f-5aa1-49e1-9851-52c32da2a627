using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic; // Added for List
using System.Linq;
using System.Threading.Tasks;
using TikTok.BusinessApplications;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.JobManagement;
using TikTok.Repositories;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Background job để quản lý và phân phối công việc cho workers
    /// </summary>
    public class ManagerJob : ITransientDependency
    {
        private readonly ILogger<ManagerJob> _logger;
        private readonly IJobConfigurationCacheService _configurationCacheService;
        private readonly IJobActivityService _jobActivityService;
        private readonly IWorkerInfoRepository _workerInfoRepository;
        private readonly IJobRepository _jobRepository;
        private readonly IBackgroundJobManager _backgroundJobManager;
        private readonly IJobTypeConfigurationCache _jobTypeConfigurationCache;

        public ManagerJob(
            ILogger<ManagerJob> logger,
            IJobConfigurationCacheService configurationCacheService,
            IJobActivityService jobActivityService,
            IWorkerInfoRepository workerInfoRepository,
            IJobRepository jobRepository,
            IBackgroundJobManager backgroundJobManager,
            IJobTypeConfigurationCache jobTypeConfigurationCache)
        {
            _logger = logger;
            _configurationCacheService = configurationCacheService;
            _jobActivityService = jobActivityService;
            _workerInfoRepository = workerInfoRepository;
            _jobRepository = jobRepository;
            _backgroundJobManager = backgroundJobManager;
            _jobTypeConfigurationCache = jobTypeConfigurationCache;
        }

        public async Task ExecuteAsync(ManagerJobArgs args)
        {
            _logger.LogDebug("Starting ManagerJob");

            try
            {
                // Lấy cấu hình hiện tại từ cache
                var configuration = await _configurationCacheService.GetCurrentConfigurationWithCacheAsync();
                if (configuration == null || !configuration.IsActive)
                {
                    _logger.LogDebug("ManagerJob skipped - no active configuration");
                    return;
                }

                // Kiểm tra và xử lý timeout cho workers đang làm việc
                await CheckAndHandleWorkerTimeoutsAsync();

                // Lấy danh sách worker đang hoạt động
                var availableWorkers = await _workerInfoRepository.GetByStatusAsync(WorkerStatus.Idle);
                if (!availableWorkers.Any())
                {
                    _logger.LogDebug("No available workers");
                    return;
                }

                // Lấy danh sách công việc chờ xử lý
                var pendingJobs = await _jobRepository.GetPendingJobsAsync(availableWorkers.Count);
                if (!pendingJobs.Any())
                {
                    _logger.LogDebug("No pending jobs");
                    return;
                }

                // Phân phối công việc cho workers
                await DistributeJobsToWorkersAsync(pendingJobs, availableWorkers);

                // Cập nhật thời gian chạy cuối cùng
                await _jobActivityService.UpdateLastManagerJobRunAsync(DateTime.UtcNow);

                _logger.LogDebug("ManagerJob completed successfully. Distributed {JobCount} jobs to {WorkerCount} workers",
                    pendingJobs.Count, availableWorkers.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ManagerJob failed");
            }
        }

        /// <summary>
        /// Kiểm tra và xử lý timeout cho workers đang làm việc
        /// </summary>
        private async Task CheckAndHandleWorkerTimeoutsAsync()
        {
            try
            {
                // Lấy cấu hình chung để có timeout mặc định
                var configuration = await _configurationCacheService.GetCurrentConfigurationWithCacheAsync();
                var defaultTimeoutMinutes = configuration?.WorkerTimeoutMinutes ?? 30;

                // Lấy danh sách workers đang làm việc
                var workingWorkers = await _workerInfoRepository.GetByStatusAsync(WorkerStatus.Working);
                if (!workingWorkers.Any())
                {
                    return;
                }

                var timeoutWorkers = new List<WorkerInfoEntity>();

                foreach (var worker in workingWorkers)
                {
                    // Lấy timeout riêng cho loại công việc nếu có
                    var jobTimeoutMinutes = defaultTimeoutMinutes;
                    if (worker.BusinessApplicationId.HasValue)
                    {
                        var jobs = await _jobRepository.GetJobsByWorkerAsync(worker.WorkerId);
                        var job = jobs.FirstOrDefault();
                        if (job != null)
                        {
                            var jobTypeConfig = await _jobTypeConfigurationCache.GetByCommandTypeAsync(job.CommandType);
                            if (jobTypeConfig?.TimeoutMinutes.HasValue == true)
                            {
                                jobTimeoutMinutes = jobTypeConfig.TimeoutMinutes.Value;
                            }
                        }
                    }

                    var timeoutThreshold = DateTime.UtcNow.AddMinutes(-jobTimeoutMinutes);

                    // Kiểm tra nếu worker đã bắt đầu làm việc và vượt quá thời gian timeout
                    if (worker.StartedAt.HasValue && worker.StartedAt.Value < timeoutThreshold)
                    {
                        timeoutWorkers.Add(worker);
                    }
                }

                if (timeoutWorkers.Any())
                {
                    _logger.LogWarning("Found {TimeoutCount} workers that have exceeded their timeout threshold",
                        timeoutWorkers.Count);

                    foreach (var worker in timeoutWorkers)
                    {
                        try
                        {
                            // Cập nhật trạng thái worker thành Timeout
                            worker.Status = WorkerStatus.Timeout;
                            worker.TimeoutAt = DateTime.UtcNow;
                            worker.LastHeartbeat = DateTime.UtcNow;
                            worker.ErrorMessage = $"Worker timed out after processing";
                            await _workerInfoRepository.UpdateAsync(worker);

                            _logger.LogDebug("Marked worker {WorkerId} as timeout. Started at: {StartedAt}, Timeout at: {TimeoutAt}",
                                worker.WorkerId, worker.StartedAt, worker.TimeoutAt);

                            // Nếu worker đang xử lý job, cần cập nhật trạng thái job
                            if (worker.BusinessApplicationId.HasValue)
                            {
                                await HandleJobTimeoutAsync(worker);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Failed to handle timeout for worker {WorkerId}", worker.WorkerId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while checking worker timeouts");
            }
        }

        /// <summary>
        /// Xử lý timeout cho job đang được worker xử lý
        /// </summary>
        /// <param name="worker">Worker bị timeout</param>
        private async Task HandleJobTimeoutAsync(WorkerInfoEntity worker)
        {
            try
            {
                // Tìm job đang được worker xử lý
                var jobs = await _jobRepository.GetJobsByWorkerAsync(worker.WorkerId);
                var job = jobs.FirstOrDefault(); // Lấy job đầu tiên vì worker chỉ xử lý 1 job tại một thời điểm

                if (job != null)
                {
                    // Cập nhật trạng thái job thành Error với thông báo timeout
                    var processingTime = worker.TimeoutAt.HasValue && worker.StartedAt.HasValue
                        ? worker.TimeoutAt.Value - worker.StartedAt.Value
                        : TimeSpan.Zero;

                    job.MarkAsError($"Job timed out after worker {worker.WorkerId} exceeded {processingTime.TotalMinutes:F1} minutes of processing");
                    await _jobRepository.UpdateAsync(job);

                    _logger.LogDebug("Updated job {JobId} status to Error due to worker timeout", job.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to handle job timeout for worker {WorkerId}", worker.WorkerId);
            }
        }

        /// <summary>
        /// Phân phối công việc cho workers
        /// </summary>
        /// <param name="pendingJobs">Danh sách công việc chờ xử lý</param>
        /// <param name="availableWorkers">Danh sách worker có sẵn</param>
        private async Task DistributeJobsToWorkersAsync(
            System.Collections.Generic.List<JobEntity> pendingJobs,
            System.Collections.Generic.List<WorkerInfoEntity> availableWorkers)
        {
            var workerIndex = 0;

            foreach (var job in pendingJobs)
            {
                if (workerIndex >= availableWorkers.Count)
                {
                    _logger.LogWarning("No more available workers for remaining jobs");
                    break;
                }

                var worker = availableWorkers[workerIndex];

                try
                {
                    // Cập nhật trạng thái công việc thành InProcess
                    job.StartProcessing(worker.WorkerId);
                    await _jobRepository.UpdateAsync(job);

                    // Cập nhật trạng thái worker thành Working
                    worker.Status = WorkerStatus.Working;
                    worker.StartedAt = DateTime.UtcNow;
                    worker.BusinessApplicationId = job.BusinessApplicationId;
                    await _workerInfoRepository.UpdateAsync(worker);

                    // Tạo WorkerJobArgs
                    var workerJobArgs = new WorkerJobArgs(job.Id, worker.WorkerId, job.CommandType, job.Parameters);

                    // Đăng ký background job để xử lý
                    await _backgroundJobManager.EnqueueAsync<WorkerJobArgs>(workerJobArgs);

                    _logger.LogDebug("Assigned job {JobId} to worker {WorkerId} and queued background job", job.Id, worker.WorkerId);

                    workerIndex++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to assign job {JobId} to worker {WorkerId}", job.Id, worker.WorkerId);

                    // Đánh dấu lỗi cho công việc
                    job.MarkAsError($"Failed to assign to worker: {ex.Message}");
                    await _jobRepository.UpdateAsync(job);
                }
            }
        }
    }
}