using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using TikTok.Users;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Uow;

namespace TikTok.Users
{
    /// <summary>
    /// App Service implementation cho User Management
    /// </summary>
    [Authorize] // Có thể thêm permission cụ thể sau
    public class UserManagementService : ApplicationService, IUserManagementService
    {
        private readonly IIdentityUserRepository _identityUserRepository;
        private readonly ILogger<UserManagementService> _logger;

        public UserManagementService(
            IIdentityUserRepository identityUserRepository,
            IdentityUserManager identityUserManager,
            ILogger<UserManagementService> logger)
        {
            _identityUserRepository = identityUserRepository;
            _logger = logger;
        }

        /// <summary>
        /// <PERSON><PERSON><PERSON> danh sách tất cả người dùng hiện tại trong hệ thống
        /// </summary>
        /// <returns>Danh sách tất cả người dùng</returns>
        public async Task<List<UserDto>> GetAllUsersAsync()
        {
            try
            {
                _logger.LogDebug("Getting all users from the system");
                var users = await _identityUserRepository.GetListAsync();
                var userDtos = ObjectMapper.Map<List<IdentityUser>, List<UserDto>>(users);
                _logger.LogDebug($"Retrieved {userDtos.Count} users from the system");
                return userDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting all users");
                throw;
            }
        }

        /// <summary>
        /// Lấy danh sách người dùng với tìm kiếm và phân trang
        /// </summary>
        /// <param name="input">Thông tin tìm kiếm và phân trang</param>
        /// <returns>Danh sách người dùng</returns>
        public async Task<PagedResultDto<UserDto>> GetUserListAsync(GetUserListDto input)
        {
            try
            {
                _logger.LogDebug("Getting user list with filtering and paging");

                // Build filter conditions
                var filter = input.UserName ?? input.Email ?? input.Name ?? input.PhoneNumber;

                // Get total count
                var totalCount = await _identityUserRepository.GetCountAsync(filter);

                // Get users with paging
                var users = await _identityUserRepository.GetListAsync(
                    sorting: input.Sorting ?? nameof(IdentityUser.CreationTime),
                    maxResultCount: input.MaxResultCount,
                    skipCount: input.SkipCount,
                    filter: filter
                );

                // Apply additional filters
                var filteredUsers = users.AsQueryable();

                if (!string.IsNullOrEmpty(input.UserName))
                {
                    filteredUsers = filteredUsers.Where(u => u.UserName.Contains(input.UserName));
                }

                if (!string.IsNullOrEmpty(input.Email))
                {
                    filteredUsers = filteredUsers.Where(u => u.Email.Contains(input.Email));
                }

                if (!string.IsNullOrEmpty(input.Name))
                {
                    filteredUsers = filteredUsers.Where(u =>
                        (u.Name != null && u.Name.Contains(input.Name)) ||
                        (u.Surname != null && u.Surname.Contains(input.Name)));
                }

                if (!string.IsNullOrEmpty(input.PhoneNumber))
                {
                    filteredUsers = filteredUsers.Where(u =>
                        u.PhoneNumber != null && u.PhoneNumber.Contains(input.PhoneNumber));
                }

                if (input.IsActive.HasValue)
                {
                    filteredUsers = filteredUsers.Where(u => u.IsActive == input.IsActive.Value);
                }

                if (input.EmailConfirmed.HasValue)
                {
                    filteredUsers = filteredUsers.Where(u => u.EmailConfirmed == input.EmailConfirmed.Value);
                }

                if (input.PhoneNumberConfirmed.HasValue)
                {
                    filteredUsers = filteredUsers.Where(u => u.PhoneNumberConfirmed == input.PhoneNumberConfirmed.Value);
                }

                if (input.TwoFactorEnabled.HasValue)
                {
                    filteredUsers = filteredUsers.Where(u => u.TwoFactorEnabled == input.TwoFactorEnabled.Value);
                }

                if (input.LockoutEnabled.HasValue)
                {
                    filteredUsers = filteredUsers.Where(u => u.LockoutEnabled == input.LockoutEnabled.Value);
                }

                if (input.CreationTimeFrom.HasValue)
                {
                    filteredUsers = filteredUsers.Where(u => u.CreationTime >= input.CreationTimeFrom.Value);
                }

                if (input.CreationTimeTo.HasValue)
                {
                    filteredUsers = filteredUsers.Where(u => u.CreationTime <= input.CreationTimeTo.Value);
                }

                // Apply paging again after filtering
                var finalUsers = filteredUsers
                    .Skip(input.SkipCount)
                    .Take(input.MaxResultCount)
                    .ToList();

                var userDtos = ObjectMapper.Map<List<IdentityUser>, List<UserDto>>(finalUsers);

                _logger.LogDebug($"Retrieved {userDtos.Count} users out of {totalCount} total users");

                return new PagedResultDto<UserDto>
                {
                    TotalCount = totalCount,
                    Items = userDtos
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user list");
                throw;
            }
        }

        /// <summary>
        /// Lấy thông tin người dùng theo ID
        /// </summary>
        /// <param name="id">ID người dùng</param>
        /// <returns>Thông tin người dùng</returns>
        public async Task<UserDto> GetUserAsync(Guid id)
        {
            try
            {
                _logger.LogDebug($"Getting user by ID: {id}");

                var user = await _identityUserRepository.FindAsync(id);
                if (user == null)
                {
                    _logger.LogWarning($"User with ID {id} not found");
                    return null;
                }

                var userDto = ObjectMapper.Map<IdentityUser, UserDto>(user);

                _logger.LogDebug($"Successfully retrieved user: {user.UserName}");
                return userDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occurred while getting user with ID: {id}");
                throw;
            }
        }

        /// <summary>
        /// Lấy thông tin người dùng theo UserName
        /// </summary>
        /// <param name="userName">Tên đăng nhập</param>
        /// <returns>Thông tin người dùng</returns>
        public async Task<UserDto> GetUserByUserNameAsync(string userName)
        {
            try
            {
                _logger.LogDebug($"Getting user by UserName: {userName}");

                var user = await _identityUserRepository.FindByNormalizedUserNameAsync(userName.ToUpperInvariant());
                if (user == null)
                {
                    _logger.LogWarning($"User with UserName {userName} not found");
                    return null;
                }

                var userDto = ObjectMapper.Map<IdentityUser, UserDto>(user);

                _logger.LogDebug($"Successfully retrieved user: {user.UserName}");
                return userDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occurred while getting user with UserName: {userName}");
                throw;
            }
        }

        /// <summary>
        /// Lấy thông tin người dùng theo Email
        /// </summary>
        /// <param name="email">Email</param>
        /// <returns>Thông tin người dùng</returns>
        public async Task<UserDto> GetUserByEmailAsync(string email)
        {
            try
            {
                _logger.LogDebug($"Getting user by Email: {email}");

                var user = await _identityUserRepository.FindByNormalizedEmailAsync(email.ToUpperInvariant());
                if (user == null)
                {
                    _logger.LogWarning($"User with Email {email} not found");
                    return null;
                }

                var userDto = ObjectMapper.Map<IdentityUser, UserDto>(user);

                _logger.LogDebug($"Successfully retrieved user: {user.UserName}");
                return userDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occurred while getting user with Email: {email}");
                throw;
            }
        }

        /// <summary>
        /// Lấy số lượng người dùng hiện tại trong hệ thống
        /// </summary>
        /// <returns>Số lượng người dùng</returns>
        public async Task<long> GetUserCountAsync()
        {
            try
            {
                _logger.LogDebug("Getting total user count");

                var count = await _identityUserRepository.GetCountAsync();

                _logger.LogDebug($"Total user count: {count}");
                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting user count");
                throw;
            }
        }

        /// <summary>
        /// Lấy số lượng người dùng đang hoạt động
        /// </summary>
        /// <returns>Số lượng người dùng đang hoạt động</returns>
        public async Task<long> GetActiveUserCountAsync()
        {
            try
            {
                _logger.LogDebug("Getting active user count");

                var users = await _identityUserRepository.GetListAsync();
                var activeCount = users.Count(u => u.IsActive);

                _logger.LogDebug($"Active user count: {activeCount}");
                return activeCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting active user count");
                throw;
            }
        }
    }
}