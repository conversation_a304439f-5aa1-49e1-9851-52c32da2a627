/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TikTokBusinessApi.Auth
{
    /// <summary>
    /// Dummy authentication resolver for testing and development
    /// Maps user IDs to predefined credentials
    /// </summary>
    public class DummyUserIdResolver : IAuthenticationResolver
    {
        private readonly Dictionary<string, AuthenticationCredentials> _userCredentials;
        private readonly AuthenticationCredentials? _defaultCredentials;
        private readonly ILogger<DummyUserIdResolver>? _logger;

        /// <summary>
        /// Initializes a new instance of the DummyUserIdResolver class
        /// </summary>
        /// <param name="defaultAppId">Default application ID</param>
        /// <param name="defaultAccessToken">Default access token</param>
        /// <param name="logger">Logger instance</param>
        public DummyUserIdResolver(string? defaultAppId = null, string? defaultAccessToken = null, ILogger<DummyUserIdResolver>? logger = null)
        {
            _userCredentials = new Dictionary<string, AuthenticationCredentials>();
            _logger = logger;

            if (!string.IsNullOrEmpty(defaultAppId) && !string.IsNullOrEmpty(defaultAccessToken))
            {
                _defaultCredentials = AuthenticationCredentials.ForTikTok(defaultAppId, defaultAccessToken);
            }
        }

        /// <summary>
        /// Adds credentials for a specific user
        /// </summary>
        /// <param name="userId">User identifier</param>
        /// <param name="credentials">Authentication credentials</param>
        public void AddUserCredentials(string userId, AuthenticationCredentials credentials)
        {
            _userCredentials[userId] = credentials;
            _logger?.LogDebug("Added credentials for user: {UserId}", userId);
        }

        /// <summary>
        /// Adds TikTok credentials for a specific user
        /// </summary>
        /// <param name="userId">User identifier</param>
        /// <param name="appId">Application ID</param>
        /// <param name="accessToken">Access token</param>
        public void AddUserTikTokCredentials(string userId, string appId, string accessToken)
        {
            AddUserCredentials(userId, AuthenticationCredentials.ForTikTok(appId, accessToken));
        }

        /// <summary>
        /// Resolves authentication credentials for the given context
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>Authentication credentials or null if not found</returns>
        public Task<AuthenticationCredentials?> ResolveCredentialsAsync(AuthenticationContext context)
        {
            AuthenticationCredentials? credentials = null;

            // Try to resolve by user ID first
            if (!string.IsNullOrEmpty(context.UserId) && _userCredentials.TryGetValue(context.UserId, out credentials))
            {
                _logger?.LogDebug("Resolved credentials for user: {UserId}", context.UserId);
                return Task.FromResult<AuthenticationCredentials?>(credentials);
            }

            // Fall back to default credentials
            if (_defaultCredentials != null)
            {
                _logger?.LogDebug("Using default credentials for context: {UserId}", context.UserId ?? "anonymous");
                return Task.FromResult<AuthenticationCredentials?>(_defaultCredentials);
            }

            _logger?.LogWarning("No credentials found for user: {UserId}", context.UserId ?? "anonymous");
            return Task.FromResult<AuthenticationCredentials?>(null);
        }

        /// <summary>
        /// Checks if credentials can be resolved for the given context
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>True if credentials can be resolved</returns>
        public Task<bool> CanResolveAsync(AuthenticationContext context)
        {
            var canResolve = (!string.IsNullOrEmpty(context.UserId) && _userCredentials.ContainsKey(context.UserId)) ||
                           _defaultCredentials != null;

            return Task.FromResult(canResolve);
        }

        /// <summary>
        /// Gets all registered user IDs
        /// </summary>
        /// <returns>List of registered user IDs</returns>
        public IEnumerable<string> GetRegisteredUsers()
        {
            return _userCredentials.Keys;
        }

        /// <summary>
        /// Removes credentials for a specific user
        /// </summary>
        /// <param name="userId">User identifier</param>
        /// <returns>True if credentials were removed</returns>
        public bool RemoveUserCredentials(string userId)
        {
            var removed = _userCredentials.Remove(userId);
            if (removed)
            {
                _logger?.LogDebug("Removed credentials for user: {UserId}", userId);
            }
            return removed;
        }

        /// <summary>
        /// Clears all user credentials
        /// </summary>
        public void ClearAllCredentials()
        {
            _userCredentials.Clear();
            _logger?.LogDebug("Cleared all user credentials");
        }
    }

    /// <summary>
    /// Configuration-based authentication resolver
    /// Resolves credentials from configuration settings
    /// </summary>
    public class ConfigurationAuthenticationResolver : IAuthenticationResolver
    {
        private readonly string? _appId;
        private readonly string? _accessToken;
        private readonly ILogger<ConfigurationAuthenticationResolver>? _logger;

        /// <summary>
        /// Initializes a new instance of the ConfigurationAuthenticationResolver class
        /// </summary>
        /// <param name="appId">Application ID from configuration</param>
        /// <param name="accessToken">Access token from configuration</param>
        /// <param name="logger">Logger instance</param>
        public ConfigurationAuthenticationResolver(string? appId, string? accessToken, ILogger<ConfigurationAuthenticationResolver>? logger = null)
        {
            _appId = appId;
            _accessToken = accessToken;
            _logger = logger;
        }

        /// <summary>
        /// Resolves authentication credentials for the given context
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>Authentication credentials or null if not found</returns>
        public Task<AuthenticationCredentials?> ResolveCredentialsAsync(AuthenticationContext context)
        {
            if (string.IsNullOrEmpty(_appId) || string.IsNullOrEmpty(_accessToken))
            {
                _logger?.LogWarning("Configuration credentials not available");
                return Task.FromResult<AuthenticationCredentials?>(null);
            }

            var credentials = AuthenticationCredentials.ForTikTok(_appId, _accessToken);
            _logger?.LogDebug("Resolved configuration credentials for context: {UserId}", context.UserId ?? "anonymous");
            
            return Task.FromResult<AuthenticationCredentials?>(credentials);
        }

        /// <summary>
        /// Checks if credentials can be resolved for the given context
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>True if credentials can be resolved</returns>
        public Task<bool> CanResolveAsync(AuthenticationContext context)
        {
            var canResolve = !string.IsNullOrEmpty(_appId) && !string.IsNullOrEmpty(_accessToken);
            return Task.FromResult(canResolve);
        }
    }

    /// <summary>
    /// Composite authentication resolver that tries multiple resolvers in order
    /// </summary>
    public class CompositeAuthenticationResolver : IAuthenticationResolver
    {
        private readonly List<IAuthenticationResolver> _resolvers;
        private readonly ILogger<CompositeAuthenticationResolver>? _logger;

        /// <summary>
        /// Initializes a new instance of the CompositeAuthenticationResolver class
        /// </summary>
        /// <param name="resolvers">List of resolvers to try in order</param>
        /// <param name="logger">Logger instance</param>
        public CompositeAuthenticationResolver(IEnumerable<IAuthenticationResolver> resolvers, ILogger<CompositeAuthenticationResolver>? logger = null)
        {
            _resolvers = new List<IAuthenticationResolver>(resolvers);
            _logger = logger;
        }

        /// <summary>
        /// Resolves authentication credentials for the given context
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>Authentication credentials or null if not found</returns>
        public async Task<AuthenticationCredentials?> ResolveCredentialsAsync(AuthenticationContext context)
        {
            foreach (var resolver in _resolvers)
            {
                try
                {
                    if (await resolver.CanResolveAsync(context))
                    {
                        var credentials = await resolver.ResolveCredentialsAsync(context);
                        if (credentials != null)
                        {
                            _logger?.LogDebug("Resolved credentials using resolver: {ResolverType}", resolver.GetType().Name);
                            return credentials;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Resolver {ResolverType} failed to resolve credentials", resolver.GetType().Name);
                }
            }

            _logger?.LogWarning("No resolver could provide credentials for context: {UserId}", context.UserId ?? "anonymous");
            return null;
        }

        /// <summary>
        /// Checks if credentials can be resolved for the given context
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>True if credentials can be resolved</returns>
        public async Task<bool> CanResolveAsync(AuthenticationContext context)
        {
            foreach (var resolver in _resolvers)
            {
                try
                {
                    if (await resolver.CanResolveAsync(context))
                    {
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Resolver {ResolverType} failed to check if it can resolve", resolver.GetType().Name);
                }
            }

            return false;
        }

        /// <summary>
        /// Adds a resolver to the composite
        /// </summary>
        /// <param name="resolver">Resolver to add</param>
        public void AddResolver(IAuthenticationResolver resolver)
        {
            _resolvers.Add(resolver);
        }
    }
}
