using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTok.ResourceProviders;
using TikTok.ResourceProviders.AdAccounts;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Users;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// Application Service cho LatestBalanceAdAccount
    /// Chỉ cung cấp các thao tác đọc dữ liệu mới nhất
    /// </summary>
    public class LatestBalanceAdAccountAppService :
        ReadOnlyAppService<
            RawLatestBalanceAdAccountEntity,
            LatestBalanceAdAccountDto,
            Guid,
            GetLatestBalanceAdAccountListDto>,
        ILatestBalanceAdAccountAppService
    {
        private readonly ILatestBalanceAdAccountRepository _latestBalanceAdAccountRepository;
        private readonly IPermissionChecker _permissionChecker;
        private readonly IAdAccountResourceProvider _resourceProvider;

        public LatestBalanceAdAccountAppService(
            ILatestBalanceAdAccountRepository latestBalanceAdAccountRepository,
            IPermissionChecker permissionChecker,
            IAdAccountResourceProvider resourceProvider)
            : base(latestBalanceAdAccountRepository)
        {
            _latestBalanceAdAccountRepository = latestBalanceAdAccountRepository;

            // Cấu hình quyền truy cập
            GetPolicyName = TikTokPermissions.BalanceAdAccounts.Default;
            GetListPolicyName = TikTokPermissions.BalanceAdAccounts.Default;
            _permissionChecker = permissionChecker;
            _resourceProvider = resourceProvider;
        }

        public override async Task<PagedResultDto<LatestBalanceAdAccountDto>> GetListAsync(GetLatestBalanceAdAccountListDto input)
        {
            var isSupervisor = await _permissionChecker.IsGrantedAsync(TikTokPermissions.AdAccounts.Supervise);

            var resources = await _resourceProvider.GetListAsync(new GetResourcePermissionAssignedUserDto()
            {
                UserId = CurrentUser.GetId()
            });

            var advertiserIds = isSupervisor ? null : resources.Select(x => x.ResourceId).Distinct().ToList();

            var entities = await _latestBalanceAdAccountRepository.GetListAsync(
                sorting: input.Sorting,
                maxResultCount: input.MaxResultCount,
                skipCount: input.SkipCount,
                filter: input.Filter,
                advertiserId: input.AdvertiserId,
                advertiserName: input.AdvertiserName,
                bcId: input.BcId,
                advertiserStatus: input.AdvertiserStatus,
                advertiserType: input.AdvertiserType,
                budgetMode: input.BudgetMode,
                company: input.Company,
                currency: input.Currency,
                timezone: input.Timezone,
                createTimeFrom: input.CreateTimeFrom,
                createTimeTo: input.CreateTimeTo,
                dateFrom: input.DateFrom,
                dateTo: input.DateTo,
                accountBalanceFrom: input.AccountBalanceFrom,
                accountBalanceTo: input.AccountBalanceTo,
                budgetFrom: input.BudgetFrom,
                budgetTo: input.BudgetTo,
                accountOpenDaysFrom: input.AccountOpenDaysFrom,
                accountOpenDaysTo: input.AccountOpenDaysTo,
                advertiserIds: advertiserIds);

            var totalCount = await _latestBalanceAdAccountRepository.GetCountAsync(
                filter: input.Filter,
                advertiserId: input.AdvertiserId,
                advertiserName: input.AdvertiserName,
                bcId: input.BcId,
                advertiserStatus: input.AdvertiserStatus,
                advertiserType: input.AdvertiserType,
                budgetMode: input.BudgetMode,
                company: input.Company,
                currency: input.Currency,
                timezone: input.Timezone,
                createTimeFrom: input.CreateTimeFrom,
                createTimeTo: input.CreateTimeTo,
                dateFrom: input.DateFrom,
                dateTo: input.DateTo,
                accountBalanceFrom: input.AccountBalanceFrom,
                accountBalanceTo: input.AccountBalanceTo,
                budgetFrom: input.BudgetFrom,
                budgetTo: input.BudgetTo,
                accountOpenDaysFrom: input.AccountOpenDaysFrom,
                accountOpenDaysTo: input.AccountOpenDaysTo,
                advertiserIds);

            var dtos = ObjectMapper.Map<RawLatestBalanceAdAccountEntity[], LatestBalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<LatestBalanceAdAccountDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }

        public async Task<LatestBalanceAdAccountDto> GetByAdvertiserIdAsync(string advertiserId)
        {
            var entity = await _latestBalanceAdAccountRepository.GetByAdvertiserIdAsync(advertiserId);
            if (entity == null)
            {
                throw new UserFriendlyException("Không tìm thấy tài khoản với AdvertiserId: " + advertiserId);
            }

            return ObjectMapper.Map<RawLatestBalanceAdAccountEntity, LatestBalanceAdAccountDto>(entity);
        }

        public async Task<PagedResultDto<LatestBalanceAdAccountDto>> GetByBcIdAsync(string bcId)
        {
            var entities = await _latestBalanceAdAccountRepository.GetByBcIdAsync(bcId);
            var dtos = ObjectMapper.Map<RawLatestBalanceAdAccountEntity[], LatestBalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<LatestBalanceAdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<PagedResultDto<LatestBalanceAdAccountDto>> GetByStatusAsync(AdvertiserAccountStatus status)
        {
            var entities = await _latestBalanceAdAccountRepository.GetByStatusAsync(status);
            var dtos = ObjectMapper.Map<RawLatestBalanceAdAccountEntity[], LatestBalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<LatestBalanceAdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<PagedResultDto<LatestBalanceAdAccountDto>> GetByAccountTypeAsync(AdAccountType accountType)
        {
            var entities = await _latestBalanceAdAccountRepository.GetByAccountTypeAsync(accountType);
            var dtos = ObjectMapper.Map<RawLatestBalanceAdAccountEntity[], LatestBalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<LatestBalanceAdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<PagedResultDto<LatestBalanceAdAccountDto>> GetByBudgetModeAsync(BudgetMode budgetMode)
        {
            var entities = await _latestBalanceAdAccountRepository.GetByBudgetModeAsync(budgetMode);
            var dtos = ObjectMapper.Map<RawLatestBalanceAdAccountEntity[], LatestBalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<LatestBalanceAdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<PagedResultDto<LatestBalanceAdAccountDto>> GetBySyncDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            var entities = await _latestBalanceAdAccountRepository.GetBySyncDateRangeAsync(fromDate, toDate);
            var dtos = ObjectMapper.Map<RawLatestBalanceAdAccountEntity[], LatestBalanceAdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<LatestBalanceAdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        public async Task<LatestBalanceAdAccountStatisticsDto> GetStatisticsAsync()
        {
            var allEntities = await _latestBalanceAdAccountRepository.GetListAsync();
            var statistics = new LatestBalanceAdAccountStatisticsDto();

            if (!allEntities.Any())
            {
                return statistics;
            }

            // Tính toán thống kê tổng quan
            statistics.TotalAccounts = allEntities.Count;
            statistics.TotalAccountBalance = allEntities.Sum(x => x.AccountBalance);
            statistics.TotalCashBalance = allEntities.Sum(x => x.CashBalance);
            statistics.TotalGrantBalance = allEntities.Sum(x => x.GrantBalance);
            statistics.TotalBudget = allEntities.Sum(x => x.Budget);
            statistics.TotalBudgetCost = allEntities.Sum(x => x.BudgetCost);
            statistics.TotalBudgetRemaining = allEntities.Sum(x => x.BudgetRemaining);
            statistics.LastSyncTime = allEntities.Max(x => x.Date);
            statistics.AccountsWithBalanceReminder = allEntities.Count(x => x.BalanceReminder);
            statistics.AccountsWithLowBalance = allEntities.Count(x => x.AccountBalance < x.Budget * 0.1m);

            // Thống kê theo trạng thái
            statistics.AccountsByStatus = allEntities
                .GroupBy(x => x.AdvertiserStatus)
                .ToDictionary(g => g.Key, g => g.Count());

            // Thống kê theo loại tài khoản
            statistics.AccountsByType = allEntities
                .GroupBy(x => x.AdvertiserType)
                .ToDictionary(g => g.Key, g => g.Count());

            // Thống kê theo chế độ ngân sách
            statistics.AccountsByBudgetMode = allEntities
                .GroupBy(x => x.BudgetMode)
                .ToDictionary(g => g.Key, g => g.Count());

            // Thống kê theo tiền tệ
            statistics.AccountsByCurrency = allEntities
                .GroupBy(x => x.Currency)
                .ToDictionary(g => g.Key, g => g.Count());

            return statistics;
        }

        public async Task<List<LatestBalanceAdAccountDto>> GetByManyAdvertiserIdsAsync(IEnumerable<string> advertiserIds)
        {
            var entities = await _latestBalanceAdAccountRepository.GetByManyAdvertiserIdsAsync(advertiserIds);
            return ObjectMapper.Map<List<RawLatestBalanceAdAccountEntity>, List<LatestBalanceAdAccountDto>>(entities);
        }
    }
}
