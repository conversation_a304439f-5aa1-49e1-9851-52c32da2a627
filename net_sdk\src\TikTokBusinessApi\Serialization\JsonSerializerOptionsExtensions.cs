/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Serialization
{
    /// <summary>
    /// Extensions for JsonSerializerOptions to configure TikTok API serialization
    /// </summary>
    public static class JsonSerializerOptionsExtensions
    {
        /// <summary>
        /// Configures JsonSerializerOptions for TikTok Business API
        /// </summary>
        /// <param name="options">JsonSerializerOptions to configure</param>
        /// <returns>Configured JsonSerializerOptions</returns>
        public static JsonSerializerOptions ConfigureForTikTokApi(this JsonSerializerOptions options)
        {
            options.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            options.PropertyNameCaseInsensitive = true;
            options.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
            options.WriteIndented = false;
            
            // Add custom converters
            options.Converters.Add(new DateTimeConverter());
            options.Converters.Add(new DateTimeOffsetConverter());
            options.Converters.Add(new JsonStringEnumConverter());
            
            return options;
        }

        /// <summary>
        /// Creates default JsonSerializerOptions for TikTok Business API
        /// </summary>
        /// <returns>Configured JsonSerializerOptions</returns>
        public static JsonSerializerOptions CreateDefault()
        {
            return new JsonSerializerOptions().ConfigureForTikTokApi();
        }
    }

    /// <summary>
    /// Custom DateTime converter for RFC3339 format
    /// </summary>
    public class DateTimeConverter : JsonConverter<DateTime>
    {
        private const string DateTimeFormat = "yyyy-MM-dd'T'HH:mm:ss.fffK";

        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var value = reader.GetString();
            if (string.IsNullOrEmpty(value))
                return default;

            if (DateTime.TryParse(value, out var dateTime))
                return dateTime;

            throw new JsonException($"Unable to parse '{value}' as DateTime");
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString(DateTimeFormat));
        }
    }

    /// <summary>
    /// Custom DateTimeOffset converter for RFC3339 format
    /// </summary>
    public class DateTimeOffsetConverter : JsonConverter<DateTimeOffset>
    {
        private const string DateTimeFormat = "yyyy-MM-dd'T'HH:mm:ss.fffK";

        public override DateTimeOffset Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var value = reader.GetString();
            if (string.IsNullOrEmpty(value))
                return default;

            if (DateTimeOffset.TryParse(value, out var dateTimeOffset))
                return dateTimeOffset;

            throw new JsonException($"Unable to parse '{value}' as DateTimeOffset");
        }

        public override void Write(Utf8JsonWriter writer, DateTimeOffset value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString(DateTimeFormat));
        }
    }

    /// <summary>
    /// Custom nullable DateTime converter
    /// </summary>
    public class NullableDateTimeConverter : JsonConverter<DateTime?>
    {
        private readonly DateTimeConverter _converter = new();

        public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Null)
                return null;

            return _converter.Read(ref reader, typeof(DateTime), options);
        }

        public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
        {
            if (value.HasValue)
                _converter.Write(writer, value.Value, options);
            else
                writer.WriteNullValue();
        }
    }

    /// <summary>
    /// Custom nullable DateTimeOffset converter
    /// </summary>
    public class NullableDateTimeOffsetConverter : JsonConverter<DateTimeOffset?>
    {
        private readonly DateTimeOffsetConverter _converter = new();

        public override DateTimeOffset? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Null)
                return null;

            return _converter.Read(ref reader, typeof(DateTimeOffset), options);
        }

        public override void Write(Utf8JsonWriter writer, DateTimeOffset? value, JsonSerializerOptions options)
        {
            if (value.HasValue)
                _converter.Write(writer, value.Value, options);
            else
                writer.WriteNullValue();
        }
    }
}
