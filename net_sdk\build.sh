#!/bin/bash

# Build script for TikTok Business API .NET SDK

set -e

CONFIGURATION="Release"
OUTPUT_PATH="./artifacts"
SKIP_TESTS=false
PACK=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --configuration)
            CONFIGURATION="$2"
            shift 2
            ;;
        --output)
            OUTPUT_PATH="$2"
            shift 2
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --pack)
            PACK=true
            shift
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

echo "Building TikTok Business API .NET SDK..."

# Clean previous builds
if [ -d "$OUTPUT_PATH" ]; then
    rm -rf "$OUTPUT_PATH"
fi

# Restore packages
echo "Restoring NuGet packages..."
dotnet restore

# Build solution
echo "Building solution..."
dotnet build --configuration "$CONFIGURATION" --no-restore

# Run tests
if [ "$SKIP_TESTS" = false ]; then
    echo "Running tests..."
    dotnet test --configuration "$CONFIGURATION" --no-build --verbosity normal
fi

# Pack NuGet package
if [ "$PACK" = true ]; then
    echo "Creating NuGet package..."
    dotnet pack src/TikTokBusinessApi/TikTokBusinessApi.csproj --configuration "$CONFIGURATION" --no-build --output "$OUTPUT_PATH"
    echo "Package created successfully in $OUTPUT_PATH"
fi

echo "Build completed successfully!"
