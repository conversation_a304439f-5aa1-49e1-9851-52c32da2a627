using System;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;

namespace TikTok.RecordTransactionBcs
{
    /// <summary>
    /// DTO cập nhật RecordTransactionBc
    /// </summary>
    public class UpdateRecordTransactionBcDto
    {
        [Required]
        public Guid Id { get; set; }
        /// <summary>
        /// Ngày giao dịch
        /// </summary>
        [Required]
        public DateTime Date { get; set; }

        /// <summary>
        /// Số tiền giao dịch
        /// </summary>
        [Required]
        public decimal Amount { get; set; }

        /// <summary>
        /// Múi giờ
        /// </summary>
        [Required]
        public string Timezone { get; set; }

        /// <summary>
        /// Tiền tệ được sử dụng trong giao dịch
        /// </summary>
        [Required]
        public string Currency { get; set; }

        /// <summary>
        /// Loại quỹ
        /// </summary>
        [Required]
        public int FundsType { get; set; }

        /// <summary>
        /// ID hóa đơn
        /// </summary>
        public string InvoiceId { get; set; }

        /// <summary>
        /// Số sê-ri hóa đơn
        /// </summary>
        public string InvoiceSerialNumber { get; set; }
    }
}