/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for getting token information
    /// </summary>
    public class TokenInfoRequest
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// Access token authorized by TikTok accounts
        /// </summary>
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;
    }
}
