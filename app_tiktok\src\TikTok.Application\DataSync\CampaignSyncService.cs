using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu chiến dịch (Campaigns)
    /// </summary>
    public class CampaignSyncService : BaseSyncService, ICampaignSyncService
    {
        private readonly ICampaignRepository _campaignRepository;
        private readonly IRepository<RawCampaignEntity, Guid> _campaignEntityRepository;
        private readonly IAdAccountRepository _adAccountRepository;
        private readonly IGmvMaxCampaignSyncService _gmvMaxCampaignSyncService;
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="serviceProvider">Service provider</param>
        /// <param name="campaignRepository">Campaign repository</param>
        /// <param name="campaignEntityRepository">Campaign entity repository</param>
        /// <param name="logger">Logger</param>
        /// <param name="adAccountRepository">Ad Account repository</param>
        public CampaignSyncService(
            IServiceProvider serviceProvider,
            ICampaignRepository campaignRepository,
            IRepository<RawCampaignEntity, Guid> campaignEntityRepository,
            IGmvMaxCampaignSyncService gmvMaxCampaignSyncService,
            ILogger<CampaignSyncService> logger,
            IAdAccountRepository adAccountRepository) : base(serviceProvider, logger)
        {
            _campaignRepository = campaignRepository;
            _campaignEntityRepository = campaignEntityRepository;
            _adAccountRepository = adAccountRepository;
            _gmvMaxCampaignSyncService = gmvMaxCampaignSyncService;
        }

        /// <summary>
        /// Đồng bộ chiến dịch theo Advertiser ID và BC ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<CampaignSyncResult> SyncCampaignsAsync(string advertiserId, string bcId)
        {
            return await SyncCampaignsAsync(advertiserId, bcId, null, null);
        }

        /// <summary>
        /// Đồng bộ chiến dịch theo Advertiser ID và BC ID với filtering
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="objectiveType">Loại mục tiêu</param>
        /// <param name="operationStatus">Trạng thái hoạt động</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<CampaignSyncResult> SyncCampaignsAsync(string advertiserId, string bcId, string? objectiveType = null, string? operationStatus = null)
        {
            var result = new CampaignSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ chiến dịch cho Advertiser: {AdvertiserId}", advertiserId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // 3. Tạo filtering cho API
                var filtering = new CampaignFiltering();
                if (!string.IsNullOrEmpty(objectiveType))
                {
                    filtering.ObjectiveType = objectiveType;
                }
                if (!string.IsNullOrEmpty(operationStatus))
                {
                    filtering.PrimaryStatus = operationStatus;
                }

                // 4. Đồng bộ từng trang
                var page = 1;
                var pageSize = 50;
                var hasMorePages = true;

                while (hasMorePages)
                {
                    try
                    {
                        // 5. Gọi API lấy danh sách chiến dịch
                        var response = await tikTokClient.Campaign.GetCampaignsAsync(
                            advertiserId,
                            fields: null,
                            filtering: filtering,
                            page: page,
                            pageSize: pageSize);

                        if (!TikTokApiCodes.IsSuccess(response.Code))
                        {
                            throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu chiến dịch cho trang {page}: {response.Message}");
                        }

                        if (response?.Data?.List == null || !response.Data.List.Any())
                        {
                            _logger.LogDebug("Không có dữ liệu chiến dịch cho trang {Page}", page);
                            break;
                        }

                        // 6. Xử lý từng chiến dịch trong trang
                        _logger.LogDebug("Đồng bộ chiến dịch cho trang dữ liệu Advertiser: {AdvertiserId}, Trang: {Page}, Tổng: {Total}", advertiserId, page, response.Data.List.Count);
                        await SyncCampaignsForPage(advertiserId, response.Data.List, result, page);
                        _logger.LogDebug("Đã đồng bộ chiến dịch cho trang dữ liệu Advertiser: {AdvertiserId}, Trang: {Page}, Tổng: {Total}", advertiserId, page, response.Data.List.Count);

                        // 7. Kiểm tra có trang tiếp theo không
                        hasMorePages = response.Data.PageInfo?.TotalPage > page;
                        page++;
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ chiến dịch cho trang {Page}, Advertiser: {AdvertiserId}", page, advertiserId);
                        result.ErrorRecords++;
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        result.ErrorMessage = ex.Message + Environment.NewLine;
                        break;
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ chiến dịch cho Advertiser: {AdvertiserId}, Tổng: {Total}", advertiserId, result.TotalRecords);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ chiến dịch cho Advertiser: {AdvertiserId}", advertiserId);
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ chiến dịch cho một trang dữ liệu
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="campaigns">Danh sách chiến dịch từ API</param>
        /// <param name="result">Kết quả đồng bộ</param>
        /// <param name="page">Số trang</param>
        private async Task SyncCampaignsForPage(string advertiserId, List<CampaignInfo> campaigns, CampaignSyncResult result, int page)
        {
            _logger.LogDebug("Đồng bộ chiến dịch cho trang dữ liệu Advertiser: {AdvertiserId}, Trang: {Page}, Tổng: {Total}", advertiserId, page, campaigns.Count);
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);

            try
            {
                var newCampaigns = new List<RawCampaignEntity>();
                var updatedCampaigns = new List<RawCampaignEntity>();

                foreach (var apiCampaign in campaigns)
                {
                    try
                    {
                        // 1. Kiểm tra chiến dịch đã tồn tại chưa
                        var existingCampaign = await _campaignRepository.FindByCampaignIdAsync(apiCampaign.CampaignId);
                        if (existingCampaign == null)
                        {
                            // 2. Tạo mới nếu chưa tồn tại
                            var newCampaign = MapApiCampaignToEntity(apiCampaign, advertiserId);
                            newCampaigns.Add(newCampaign);
                            result.NewRecords++;
                        }
                        else
                        {
                            // 3. Cập nhật nếu đã tồn tại
                            UpdateCampaignFromApi(existingCampaign, apiCampaign);
                            updatedCampaigns.Add(existingCampaign);
                            result.UpdatedRecords++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý chiến dịch {CampaignId}", apiCampaign.CampaignId);
                        result.ErrorRecords++;
                    }
                }

                await _campaignEntityRepository.InsertManyAsync(newCampaigns);
                await _campaignEntityRepository.UpdateManyAsync(updatedCampaigns);
                await uow.CompleteAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ chiến dịch cho trang dữ liệu Advertiser: {AdvertiserId}, Trang: {Page}", advertiserId, page);
                result.ErrorRecords += campaigns.Count;
                throw;
            }
        }

        /// <summary>
        /// Map CampaignInfo từ API sang CampaignEntity
        /// </summary>
        /// <param name="apiCampaign">Campaign từ API</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <returns>CampaignEntity</returns>
        private RawCampaignEntity MapApiCampaignToEntity(CampaignInfo apiCampaign, string advertiserId)
        {
            return new RawCampaignEntity
            {
                AdvertiserId = advertiserId,
                CampaignId = apiCampaign.CampaignId ?? string.Empty,
                CampaignSystemOrigin = MapCampaignSystemOrigin(apiCampaign.CampaignSystemOrigin),
                CreateTime = ParseDateTime(apiCampaign.CreateTime),
                ModifyTime = ParseDateTime(apiCampaign.ModifyTime),
                ObjectiveType = apiCampaign.ObjectiveType ?? string.Empty,
                AppPromotionType = MapAppPromotionType(apiCampaign.AppPromotionType),
                VirtualObjectiveType = MapVirtualObjectiveType(apiCampaign.VirtualObjectiveType),
                SalesDestination = MapSalesDestination(apiCampaign.SalesDestination),
                IsSearchCampaign = apiCampaign.IsSearchCampaign ?? false,
                IsSmartPerformanceCampaign = apiCampaign.IsSmartPerformanceCampaign ?? false,
                CampaignType = MapCampaignType(apiCampaign.CampaignType),
                AppId = apiCampaign.AppId,
                IsAdvancedDedicatedCampaign = apiCampaign.IsAdvancedDedicatedCampaign ?? false,
                DisableSkanCampaign = apiCampaign.DisableSkanCampaign ?? false,
                BidAlignType = MapBidAlignType(apiCampaign.BidAlignType),
                CampaignAppProfilePageState = MapCampaignAppProfilePageState(apiCampaign.CampaignAppProfilePageState),
                RfCampaignType = MapRfCampaignType(apiCampaign.RfCampaignType),
                CampaignProductSource = MapCampaignProductSource(apiCampaign.CampaignProductSource),
                CatalogEnabled = apiCampaign.CatalogEnabled ?? false,
                CampaignName = apiCampaign.CampaignName ?? string.Empty,
                SpecialIndustries = MapSpecialIndustries(apiCampaign.SpecialIndustries),
                BudgetOptimizeOn = apiCampaign.BudgetOptimizeOn ?? false,
                BidType = apiCampaign.BidType,
                DeepBidType = apiCampaign.DeepBidType,
                RoasBid = (decimal)(apiCampaign.RoasBid ?? 0),
                OptimizationGoal = apiCampaign.OptimizationGoal,
                BudgetMode = apiCampaign.BudgetMode ?? string.Empty,
                Budget = (decimal)(apiCampaign.Budget ?? 0),
                RtaId = apiCampaign.RtaId,
                RtaBidEnabled = apiCampaign.RtaBidEnabled ?? false,
                RtaProductSelectionEnabled = apiCampaign.RtaProductSelectionEnabled ?? false,
                OperationStatus = apiCampaign.OperationStatus ?? string.Empty,
                SecondaryStatus = apiCampaign.SecondaryStatus,
                PostbackWindowMode = MapPostbackWindowMode(apiCampaign.PostbackWindowMode),
                IsNewStructure = apiCampaign.IsNewStructure ?? false,
                Objective = MapObjective(apiCampaign.Objective)
            };
        }

        /// <summary>
        /// Cập nhật CampaignEntity từ API data
        /// </summary>
        /// <param name="existingCampaign">Campaign hiện tại</param>
        /// <param name="apiCampaign">Campaign từ API</param>
        private void UpdateCampaignFromApi(RawCampaignEntity existingCampaign, CampaignInfo apiCampaign)
        {
            existingCampaign.CampaignSystemOrigin = MapCampaignSystemOrigin(apiCampaign.CampaignSystemOrigin);
            existingCampaign.ModifyTime = ParseDateTime(apiCampaign.ModifyTime);
            existingCampaign.ObjectiveType = apiCampaign.ObjectiveType ?? existingCampaign.ObjectiveType;
            existingCampaign.AppPromotionType = MapAppPromotionType(apiCampaign.AppPromotionType);
            existingCampaign.VirtualObjectiveType = MapVirtualObjectiveType(apiCampaign.VirtualObjectiveType);
            existingCampaign.SalesDestination = MapSalesDestination(apiCampaign.SalesDestination);
            existingCampaign.IsSearchCampaign = apiCampaign.IsSearchCampaign ?? existingCampaign.IsSearchCampaign;
            existingCampaign.IsSmartPerformanceCampaign = apiCampaign.IsSmartPerformanceCampaign ?? existingCampaign.IsSmartPerformanceCampaign;
            existingCampaign.CampaignType = MapCampaignType(apiCampaign.CampaignType);
            existingCampaign.AppId = apiCampaign.AppId;
            existingCampaign.IsAdvancedDedicatedCampaign = apiCampaign.IsAdvancedDedicatedCampaign ?? existingCampaign.IsAdvancedDedicatedCampaign;
            existingCampaign.DisableSkanCampaign = apiCampaign.DisableSkanCampaign ?? existingCampaign.DisableSkanCampaign;
            existingCampaign.BidAlignType = MapBidAlignType(apiCampaign.BidAlignType);
            existingCampaign.CampaignAppProfilePageState = MapCampaignAppProfilePageState(apiCampaign.CampaignAppProfilePageState);
            existingCampaign.RfCampaignType = MapRfCampaignType(apiCampaign.RfCampaignType);
            existingCampaign.CampaignProductSource = MapCampaignProductSource(apiCampaign.CampaignProductSource);
            existingCampaign.CatalogEnabled = apiCampaign.CatalogEnabled ?? existingCampaign.CatalogEnabled;
            existingCampaign.CampaignName = apiCampaign.CampaignName ?? existingCampaign.CampaignName;
            existingCampaign.SpecialIndustries = MapSpecialIndustries(apiCampaign.SpecialIndustries);
            existingCampaign.BudgetOptimizeOn = apiCampaign.BudgetOptimizeOn ?? existingCampaign.BudgetOptimizeOn;
            existingCampaign.BidType = apiCampaign.BidType;
            existingCampaign.DeepBidType = apiCampaign.DeepBidType;
            existingCampaign.RoasBid = apiCampaign.RoasBid ?? 0;
            existingCampaign.OptimizationGoal = apiCampaign.OptimizationGoal;
            existingCampaign.BudgetMode = apiCampaign.BudgetMode ?? existingCampaign.BudgetMode;
            existingCampaign.Budget = apiCampaign.Budget ?? 0;
            existingCampaign.RtaId = apiCampaign.RtaId;
            existingCampaign.RtaBidEnabled = apiCampaign.RtaBidEnabled ?? existingCampaign.RtaBidEnabled;
            existingCampaign.RtaProductSelectionEnabled = apiCampaign.RtaProductSelectionEnabled ?? existingCampaign.RtaProductSelectionEnabled;
            existingCampaign.OperationStatus = apiCampaign.OperationStatus ?? existingCampaign.OperationStatus;
            existingCampaign.SecondaryStatus = apiCampaign.SecondaryStatus;
            existingCampaign.PostbackWindowMode = MapPostbackWindowMode(apiCampaign.PostbackWindowMode);
            existingCampaign.IsNewStructure = apiCampaign.IsNewStructure ?? existingCampaign.IsNewStructure;
            existingCampaign.Objective = MapObjective(apiCampaign.Objective);
        }

        /// <summary>
        /// Parse DateTime từ string
        /// </summary>
        /// <param name="dateTimeString">DateTime string</param>
        /// <returns>DateTime</returns>
        private DateTime ParseDateTime(string? dateTimeString)
        {
            if (string.IsNullOrEmpty(dateTimeString))
                return DateTime.UtcNow;

            return DateTime.TryParse(dateTimeString, out var result) ? result : DateTime.UtcNow;
        }

        /// <summary>
        /// Map Campaign System Origin từ API sang enum
        /// </summary>
        /// <param name="campaignSystemOrigin">Campaign system origin từ API</param>
        /// <returns>CampaignSystemOrigin enum</returns>
        private CampaignSystemOrigin? MapCampaignSystemOrigin(string? campaignSystemOrigin)
        {
            return Enum.TryParse(campaignSystemOrigin, true, out CampaignSystemOrigin result) ? result : null;
        }

        /// <summary>
        /// Map App Promotion Type từ API sang enum
        /// </summary>
        /// <param name="appPromotionType">App promotion type từ API</param>
        /// <returns>AppPromotionType enum</returns>
        private AppPromotionType? MapAppPromotionType(string? appPromotionType)
        {
            return Enum.TryParse(appPromotionType, true, out AppPromotionType result) ? result : null;
        }

        /// <summary>
        /// Map Virtual Objective Type từ API sang enum
        /// </summary>
        /// <param name="virtualObjectiveType">Virtual objective type từ API</param>
        /// <returns>VirtualObjectiveType enum</returns>
        private VirtualObjectiveType? MapVirtualObjectiveType(string? virtualObjectiveType)
        {
            return Enum.TryParse(virtualObjectiveType, true, out VirtualObjectiveType result) ? result : null;
        }

        /// <summary>
        /// Map Sales Destination từ API sang enum
        /// </summary>
        /// <param name="salesDestination">Sales destination từ API</param>
        /// <returns>SalesDestination enum</returns>
        private SalesDestination? MapSalesDestination(string? salesDestination)
        {
            return Enum.TryParse(salesDestination, true, out SalesDestination result) ? result : null;
        }

        /// <summary>
        /// Map Campaign Type từ API sang enum
        /// </summary>
        /// <param name="campaignType">Campaign type từ API</param>
        /// <returns>CampaignType enum</returns>
        private CampaignType MapCampaignType(string? campaignType)
        {
            return Enum.TryParse(campaignType, true, out CampaignType result) ? result : CampaignType.REGULAR_CAMPAIGN;
        }

        /// <summary>
        /// Map Bid Align Type từ API sang enum
        /// </summary>
        /// <param name="bidAlignType">Bid align type từ API</param>
        /// <returns>BidAlignType enum</returns>
        private BidAlignType? MapBidAlignType(string? bidAlignType)
        {
            return Enum.TryParse(bidAlignType, true, out BidAlignType result) ? result : null;
        }

        /// <summary>
        /// Map Campaign App Profile Page State từ API sang enum
        /// </summary>
        /// <param name="campaignAppProfilePageState">Campaign app profile page state từ API</param>
        /// <returns>CampaignAppProfilePageState enum</returns>
        private CampaignAppProfilePageState? MapCampaignAppProfilePageState(string? campaignAppProfilePageState)
        {
            return Enum.TryParse(campaignAppProfilePageState, true, out CampaignAppProfilePageState result) ? result : null;
        }

        /// <summary>
        /// Map RF Campaign Type từ API sang enum
        /// </summary>
        /// <param name="rfCampaignType">RF campaign type từ API</param>
        /// <returns>RfCampaignType enum</returns>
        private RfCampaignType? MapRfCampaignType(string? rfCampaignType)
        {
            return Enum.TryParse(rfCampaignType, true, out RfCampaignType result) ? result : null;
        }

        /// <summary>
        /// Map Campaign Product Source từ API sang enum
        /// </summary>
        /// <param name="campaignProductSource">Campaign product source từ API</param>
        /// <returns>CampaignProductSource enum</returns>
        private CampaignProductSource? MapCampaignProductSource(string? campaignProductSource)
        {
            return Enum.TryParse(campaignProductSource, true, out CampaignProductSource result) ? result : null;
        }

        /// <summary>
        /// Map Special Industries từ API sang enum list
        /// </summary>
        /// <param name="specialIndustries">Special industries từ API</param>
        /// <returns>List SpecialIndustries enum</returns>
        private List<SpecialIndustries> MapSpecialIndustries(List<string>? specialIndustries)
        {
            if (specialIndustries == null || !specialIndustries.Any())
                return new List<SpecialIndustries>();

            var result = new List<SpecialIndustries>();
            foreach (var industry in specialIndustries)
            {
                if (Enum.TryParse(industry, true, out SpecialIndustries mappedIndustry))
                {
                    result.Add(mappedIndustry);
                }
            }
            return result;
        }

        /// <summary>
        /// Map Postback Window Mode từ API sang enum
        /// </summary>
        /// <param name="postbackWindowMode">Postback window mode từ API</param>
        /// <returns>PostbackWindowMode enum</returns>
        private PostbackWindowMode? MapPostbackWindowMode(string? postbackWindowMode)
        {
            return Enum.TryParse(postbackWindowMode, true, out PostbackWindowMode result) ? result : null;
        }

        /// <summary>
        /// Map Objective từ API sang enum
        /// </summary>
        /// <param name="objective">Objective từ API</param>
        /// <returns>Objective enum</returns>
        private Objective MapObjective(string? objective)
        {
            return Enum.TryParse(objective, true, out Objective result) ? result : Objective.APP;
        }

        /// <summary>
        /// Đồng bộ nhiều chiến dịch cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds">Danh sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<CampaignSyncResult> SyncManyCampaignsAsync(string bcId, List<string>? advertiserIds = null)
        {
            var result = new CampaignSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Nếu advertiserIds là null, lấy tất cả advertiser IDs từ ad account repository
                if (advertiserIds == null)
                {
                    var adAccounts = await _adAccountRepository.GetByOwnerBcIdAsync(bcId);
                    advertiserIds = adAccounts.Select(x => x.AdvertiserId).Distinct().ToList();
                    _logger.LogDebug("Lấy danh sách Advertiser IDs từ Ad Account Repository cho BC: {BcId}, Số Advertiser: {Count}", bcId, advertiserIds.Count);
                }

                if (!advertiserIds.Any())
                {
                    _logger.LogWarning("Không tìm thấy Advertiser nào cho BC: {BcId}", bcId);
                    result.ErrorMessage = $"Không tìm thấy Advertiser nào cho BC: {bcId}";
                    return result;
                }

                _logger.LogDebug("Bắt đầu đồng bộ nhiều chiến dịch cho BC: {BcId}, Số Advertiser: {Count}", bcId, advertiserIds.Count);
                var unauthorized = new List<string>();
                foreach (var advertiserId in advertiserIds)
                {
                    try
                    {
                        // 1. Sync ordinary campaigns
                        //var singleResult = await SyncCampaignsAsync(advertiserId, bcId);
                        //result.NewRecords += singleResult.NewRecords;
                        //result.UpdatedRecords += singleResult.UpdatedRecords;
                        //result.ErrorRecords += singleResult.ErrorRecords;

                        // 2. Sync gmvMax campaigns
                        var gmvMaxResult = await _gmvMaxCampaignSyncService.SyncGmvMaxCampaignsAsync(advertiserId, bcId, unauthorized);
                        result.NewRecords += gmvMaxResult.NewRecords;
                        result.UpdatedRecords += gmvMaxResult.UpdatedRecords;
                        result.ErrorRecords += gmvMaxResult.ErrorRecords;

                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ chiến dịch cho Advertiser: {AdvertiserId}", advertiserId);
                        result.ErrorRecords++;
                    }
                }
                await SendToTelegram(unauthorized);
                _logger.LogDebug("Hoàn thành đồng bộ nhiều chiến dịch cho BC: {BcId}, Tổng: {Total}", bcId, result.TotalRecords);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ nhiều chiến dịch cho BC: {BcId}", bcId);
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả chiến dịch cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<CampaignSyncResult> SyncAllCampaignsAsync()
        {
            var result = new CampaignSyncResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả chiến dịch cho tất cả Business Centers");

                // Lấy tất cả Business Applications active
                var allBusinessApplications = await _businessApplicationCache.GetAllActiveAsync();
                var bcGroups = allBusinessApplications.GroupBy(x => x.BcId);

                if (!bcGroups.Any())
                {
                    result.ErrorMessage = "Không tìm thấy ứng dụng Business active nào";
                    _logger.LogWarning(result.ErrorMessage);
                    return result;
                }

                // Đồng bộ từng BC
                foreach (var bcGroup in bcGroups)
                {
                    var bcId = bcGroup.Key;
                    try
                    {
                        var bcResult = await SyncManyCampaignsAsync(bcId);
                        result.NewRecords += bcResult.NewRecords;
                        result.UpdatedRecords += bcResult.UpdatedRecords;
                        result.ErrorRecords += bcResult.ErrorRecords;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ chiến dịch cho BC: {BcId}", bcId);
                        // Tiếp tục với BC tiếp theo
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ tất cả chiến dịch cho tất cả Business Centers. Tổng: {Total}", result.TotalRecords);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả chiến dịch");
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }
    }
}