using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using TikTok.Application.Contracts.Rules;
using TikTok.Rules;
using Volo.Abp.ObjectMapping;
using Volo.Abp.Users;
using System.Linq;

namespace TikTok.Activities.Rules
{
    [Activity(Category = "Quy tắc", DisplayName = "Lấy danh sách quy tắc từ cache", Outcomes = new[] { OutcomeNames.Done })]
    public class GetRulesListActivity : UnitOfWorkActivity
    {
        private readonly IRuleCache _ruleCache;
        private readonly ILogger<GetRulesListActivity> _logger;

        public GetRulesListActivity(
            IRuleCache ruleCache,
            ILogger<GetRulesListActivity> logger)
        {
            _ruleCache = ruleCache;
            _logger = logger;
        }

        [ActivityOutput(Hint = "Danh sách quy tắc từ cache")]
        public List<RuleDto> Output { get; set; } = new();

        [ActivityOutput(Hint = "Tổng số quy tắc")]
        public long TotalCount { get; set; } = 0;

        [ActivityOutput(Hint = "Thông báo lỗi nếu có")]
        public string? ErrorMessage { get; set; }

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                _logger.LogDebug("Bắt đầu lấy danh sách quy tắc từ cache");

                // Lấy tất cả quy tắc từ cache
                var allRules = await _ruleCache.GetAllAsync();
                
                Output = allRules;
                TotalCount = allRules.Count;

                _logger.LogDebug("Hoàn thành lấy danh sách quy tắc từ cache. Tổng số: {TotalCount}", TotalCount);

                ErrorMessage = null;
                return Done();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi lấy danh sách quy tắc từ cache");
                
                // Trả về danh sách rỗng và thông báo lỗi
                Output = new List<RuleDto>();
                TotalCount = 0;
                ErrorMessage = $"Lỗi khi lấy danh sách quy tắc từ cache: {ex.Message}";
                
                return Done();
            }
        }
    }
}
