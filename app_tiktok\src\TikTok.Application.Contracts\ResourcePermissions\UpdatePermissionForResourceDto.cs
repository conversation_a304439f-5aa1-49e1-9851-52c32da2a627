using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TikTok.ResourcePermissions
{
    /// <summary>
    /// DTO để cập nhật quyền cho nhiều người dùng đối với tài nguyên
    /// </summary>
    public class UpdatePermissionForResourceDto
    {
        /// <summary>
        /// ID của người dùng
        /// </summary>
        [Required]
        public Guid UserId { get; set; }

        /// <summary>
        /// ID của tài nguyên
        /// </summary>
        [Required]
        [StringLength(255)]
        public string ResourceId { get; set; }

        /// <summary>
        /// Loại tài nguyên TikTok
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ResourceType { get; set; }

        /// <summary>
        /// Danh sách quyền mới được cập nhật
        /// </summary>
        [Required]
        public List<string> Permissions { get; set; } = new List<string>();
    }
}
