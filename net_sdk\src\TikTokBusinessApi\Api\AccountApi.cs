/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models.Account;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Account operations
    /// </summary>
    public class AccountApi : IAccountApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<AccountApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the AccountApi class
        /// </summary>
        /// <param name="apiClient">API client instance</param>
        /// <param name="logger">Logger instance</param>
        public AccountApi(IApiClient apiClient, ILogger<AccountApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <inheritdoc />
        public async Task<TokenInfoResponse> GetTokenInfoAsync(TokenInfoRequest request, CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting token info for app_id: {AppId}", request.AppId);

            var response = await _apiClient.CallApiAsync<TokenInfoResponse>(
                ApiEndpoints.Account.TokenInfo,
                HttpMethod.Post,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        public async Task<BusinessProfileResponse> GetBusinessProfileAsync(
            string businessId,
            string? startDate = null,
            string? endDate = null,
            List<string>? fields = null,
            CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting business profile for business_id: {BusinessId}", businessId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = businessId
            };

            if (!string.IsNullOrEmpty(startDate))
                queryParams["start_date"] = startDate;

            if (!string.IsNullOrEmpty(endDate))
                queryParams["end_date"] = endDate;

            if (fields != null && fields.Count > 0)
                queryParams["fields"] = string.Join(",", fields);

            var response = await _apiClient.CallApiAsync<BusinessProfileResponse>(
                ApiEndpoints.Account.BusinessAnalytics,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        public async Task<VideoListResponse> GetVideoListAsync(
            string businessId,
            List<string>? fields = null,
            VideoListFilters? filters = null,
            long? cursor = null,
            int? maxCount = null,
            CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting video list for business_id: {BusinessId}", businessId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = businessId
            };

            if (fields != null && fields.Count > 0)
                queryParams["fields"] = string.Join(",", fields);

            if (filters?.VideoIds != null && filters.VideoIds.Count > 0)
                queryParams["filters[video_ids]"] = string.Join(",", filters.VideoIds);

            if (filters?.AdPostOnly.HasValue == true)
                queryParams["filters[ad_post_only]"] = filters.AdPostOnly.Value.ToString().ToLower();

            if (cursor.HasValue)
                queryParams["cursor"] = cursor.Value.ToString();

            if (maxCount.HasValue)
                queryParams["max_count"] = maxCount.Value.ToString();

            var response = await _apiClient.CallApiAsync<VideoListResponse>(
                ApiEndpoints.Account.BusinessVideoList,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        public async Task<BenchmarkResponse> GetBenchmarkAsync(
            string businessId,
            string businessCategory,
            CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting benchmark for business_id: {BusinessId}, category: {Category}", businessId, businessCategory);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = businessId,
                ["business_category"] = businessCategory
            };

            var response = await _apiClient.CallApiAsync<BenchmarkResponse>(
                ApiEndpoints.Account.BusinessBenchmark,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        public async Task<VideoSettingsResponse> GetVideoSettingsAsync(
            string businessId,
            CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting video settings for business_id: {BusinessId}", businessId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = businessId
            };

            var response = await _apiClient.CallApiAsync<VideoSettingsResponse>(
                ApiEndpoints.Account.VideoSettings,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        public async Task<CommentListResponse> GetCommentsAsync(
            string businessId,
            string videoId,
            List<string>? commentIds = null,
            bool? includeReplies = null,
            string? status = null,
            string? sortField = null,
            string? sortOrder = null,
            int? cursor = null,
            int? maxCount = null,
            CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting comments for business_id: {BusinessId}, video_id: {VideoId}", businessId, videoId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = businessId,
                ["video_id"] = videoId
            };

            if (commentIds != null && commentIds.Count > 0)
                queryParams["comment_ids"] = string.Join(",", commentIds);

            if (includeReplies.HasValue)
                queryParams["include_replies"] = includeReplies.Value.ToString().ToLower();

            if (!string.IsNullOrEmpty(status))
                queryParams["status"] = status;

            if (!string.IsNullOrEmpty(sortField))
                queryParams["sort_field"] = sortField;

            if (!string.IsNullOrEmpty(sortOrder))
                queryParams["sort_order"] = sortOrder;

            if (cursor.HasValue)
                queryParams["cursor"] = cursor.Value.ToString();

            if (maxCount.HasValue)
                queryParams["max_count"] = maxCount.Value.ToString();

            var response = await _apiClient.CallApiAsync<CommentListResponse>(
                ApiEndpoints.Account.CommentList,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        public async Task<CommentListResponse> GetCommentRepliesAsync(
            string businessId,
            string videoId,
            string commentId,
            string? status = null,
            string? sortField = null,
            string? sortOrder = null,
            int? cursor = null,
            int? maxCount = null,
            CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting comment replies for business_id: {BusinessId}, video_id: {VideoId}, comment_id: {CommentId}",
                businessId, videoId, commentId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = businessId,
                ["video_id"] = videoId,
                ["comment_id"] = commentId
            };

            if (!string.IsNullOrEmpty(status))
                queryParams["status"] = status;

            if (!string.IsNullOrEmpty(sortField))
                queryParams["sort_field"] = sortField;

            if (!string.IsNullOrEmpty(sortOrder))
                queryParams["sort_order"] = sortOrder;

            if (cursor.HasValue)
                queryParams["cursor"] = cursor.Value.ToString();

            if (maxCount.HasValue)
                queryParams["max_count"] = maxCount.Value.ToString();

            var response = await _apiClient.CallApiAsync<CommentListResponse>(
                ApiEndpoints.Account.CommentReplyList,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        //public async Task<CommentCreateResponse> CreateCommentAsync(CommentCreateRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Creating comment for business_id: {BusinessId}, video_id: {VideoId}",
        //        request.BusinessId, request.VideoId);

        //    var response = await _apiClient.CallApiAsync<CommentCreateResponse>(
        //        ApiEndpoints.Account.CommentCreate,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response;
        //}

        /// <inheritdoc />
        //public async Task<CommentReplyCreateResponse> CreateCommentReplyAsync(CommentReplyCreateRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Creating comment reply for business_id: {BusinessId}, video_id: {VideoId}, comment_id: {CommentId}",
        //        request.BusinessId, request.VideoId, request.CommentId);

        //    var response = await _apiClient.CallApiAsync<CommentReplyCreateResponse>(
        //        ApiEndpoints.Account.CommentReplyCreate,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response;
        //}

        /// <inheritdoc />
        //public async Task LikeCommentAsync(CommentLikeRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Liking/unliking comment for business_id: {BusinessId}, comment_id: {CommentId}, action: {Action}",
        //        request.BusinessId, request.CommentId, request.Action);

        //    await _apiClient.CallApiAsync<object>(
        //        ApiEndpoints.Account.CommentLike,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <inheritdoc />
        //public async Task HideCommentAsync(CommentHideRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Hiding/unhiding comment for business_id: {BusinessId}, comment_id: {CommentId}, action: {Action}",
        //        request.BusinessId, request.CommentId, request.Action);

        //    await _apiClient.CallApiAsync<object>(
        //        ApiEndpoints.Account.BusinessCommentHide,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <inheritdoc />
        //public async Task DeleteCommentAsync(CommentDeleteRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Deleting comment for business_id: {BusinessId}, comment_id: {CommentId}",
        //        request.BusinessId, request.CommentId);

        //    await _apiClient.CallApiAsync<object>(
        //        ApiEndpoints.Account.BusinessCommentDelete,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <inheritdoc />
        //public async Task<VideoPublishResponse> PublishVideoAsync(VideoPublishRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Publishing video for business_id: {BusinessId}", request.BusinessId);

        //    var response = await _apiClient.CallApiAsync<VideoPublishResponse>(
        //        ApiEndpoints.Account.BusinessVideoPublish,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response;
        //}

        /// <inheritdoc />
        //public async Task<PhotoPublishResponse> PublishPhotoAsync(PhotoPublishRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Publishing photo for business_id: {BusinessId}", request.BusinessId);

        //    var response = await _apiClient.CallApiAsync<PhotoPublishResponse>(
        //        ApiEndpoints.Account.BusinessPhotoPublish,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response;
        //}

        /// <inheritdoc />
        public async Task<PublishStatusResponse> GetPublishStatusAsync(string businessId, string publishId, CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting publish status for business_id: {BusinessId}, publish_id: {PublishId}", businessId, publishId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = businessId,
                ["publish_id"] = publishId
            };

            var response = await _apiClient.CallApiAsync<PublishStatusResponse>(
                ApiEndpoints.Account.BusinessPublishStatus,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        public async Task<HashtagSuggestionResponse> GetHashtagSuggestionsAsync(string businessId, string keyword, string? language = null, CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting hashtag suggestions for business_id: {BusinessId}, keyword: {Keyword}", businessId, keyword);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = businessId,
                ["keyword"] = keyword
            };

            if (!string.IsNullOrEmpty(language))
                queryParams["language"] = language;

            var response = await _apiClient.CallApiAsync<HashtagSuggestionResponse>(
                ApiEndpoints.Account.BusinessHashtagSuggestion,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        //public async Task<PostAuthorizationSettingResponse> SetPostAuthorizationAsync(PostAuthorizationSettingRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Setting post authorization for business_id: {BusinessId}, item_id: {ItemId}, is_ad_promotable: {IsAdPromotable}",
        //        request.BusinessId, request.ItemId, request.IsAdPromotable);

        //    var response = await _apiClient.CallApiAsync<PostAuthorizationSettingResponse>(
        //        ApiEndpoints.Account.BusinessPostAuthorizeSetting,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response;
        //}

        /// <inheritdoc />
        //public async Task<PostAuthorizationExtensionResponse> ExtendPostAuthorizationAsync(PostAuthorizationExtensionRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Extending post authorization for business_id: {BusinessId}, item_id: {ItemId}",
        //        request.BusinessId, request.ItemId);

        //    var response = await _apiClient.CallApiAsync<PostAuthorizationExtensionResponse>(
        //        ApiEndpoints.Account.BusinessPostAuthorize,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response;
        //}

        /// <inheritdoc />
        public async Task<PostAuthorizationStatusResponse> GetPostAuthorizationStatusAsync(string businessId, string itemId, CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting post authorization status for business_id: {BusinessId}, item_id: {ItemId}", businessId, itemId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = businessId,
                ["item_id"] = itemId
            };

            var response = await _apiClient.CallApiAsync<PostAuthorizationStatusResponse>(
                ApiEndpoints.Account.BusinessPostAuthorizeStatus,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        //public async Task<PostAuthorizationDeleteResponse> DeletePostAuthorizationAsync(PostAuthorizationDeleteRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Deleting post authorization for business_id: {BusinessId}, item_id: {ItemId}",
        //        request.BusinessId, request.ItemId);

        //    var response = await _apiClient.CallApiAsync<PostAuthorizationDeleteResponse>(
        //        ApiEndpoints.Account.BusinessPostAuthorizeDelete,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response;
        //}

        /// <inheritdoc />
        //public async Task<UrlPropertyAddResponse> AddUrlPropertyAsync(UrlPropertyAddRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Adding URL property for app_id: {AppId}, url: {Url}",
        //        request.AppId, request.UrlPropertyMeta.Url);

        //    var response = await _apiClient.CallApiAsync<UrlPropertyAddResponse>(
        //        ApiEndpoints.Account.UrlPropertyAdd,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response;
        //}

        /// <inheritdoc />
        //public async Task<UrlPropertyVerifyResponse> VerifyUrlPropertyAsync(UrlPropertyVerifyRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Verifying URL property for app_id: {AppId}, url: {Url}",
        //        request.AppId, request.UrlPropertyMeta.Url);

        //    var response = await _apiClient.CallApiAsync<UrlPropertyVerifyResponse>(
        //        ApiEndpoints.Account.UrlPropertyVerify,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response;
        //}

        /// <inheritdoc />
        //public async Task DeleteUrlPropertyAsync(UrlPropertyDeleteRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Deleting URL property for app_id: {AppId}, url: {Url}",
        //        request.AppId, request.UrlPropertyMeta.Url);

        //    await _apiClient.CallApiAsync<object>(
        //        ApiEndpoints.Account.UrlPropertyDelete,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <inheritdoc />
        public async Task<UrlPropertyListResponse> GetUrlPropertiesAsync(string appId, CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting URL properties for app_id: {AppId}", appId);

            var queryParams = new Dictionary<string, string>
            {
                ["app_id"] = appId
            };

            var response = await _apiClient.CallApiAsync<UrlPropertyListResponse>(
                ApiEndpoints.Account.UrlPropertyList,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        //public async Task<WebhookUpdateResponse> UpdateWebhookAsync(WebhookUpdateRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Updating webhook for app_id: {AppId}, webhook_url: {WebhookUrl}",
        //        request.AppId, request.WebhookUrl);

        //    var response = await _apiClient.CallApiAsync<WebhookUpdateResponse>(
        //        ApiEndpoints.Account.WebhookUpdate,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response;
        //}

        /// <inheritdoc />
        public async Task<WebhookListResponse> GetWebhookConfigurationsAsync(string appId, CancellationToken cancellationToken = default)
        {
            _logger?.LogDebug("Getting webhook configurations for app_id: {AppId}", appId);

            var queryParams = new Dictionary<string, string>
            {
                ["app_id"] = appId
            };

            var response = await _apiClient.CallApiAsync<WebhookListResponse>(
                ApiEndpoints.Account.WebhookList,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        //public async Task<WebhookDeleteResponse> DeleteWebhookAsync(WebhookDeleteRequest request, CancellationToken cancellationToken = default)
        //{
        //    _logger?.LogDebug("Deleting webhook for app_id: {AppId}", request.AppId);

        //    var response = await _apiClient.CallApiAsync<WebhookDeleteResponse>(
        //        ApiEndpoints.Account.WebhookDelete,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response;
        //}
    }
}
