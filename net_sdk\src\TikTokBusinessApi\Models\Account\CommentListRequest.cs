/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for getting comments on a video
    /// </summary>
    public class CommentListRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for owned TikTok video to list comments on
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// A list of IDs for comments or comment replies that you want to filter the results by
        /// </summary>
        [JsonPropertyName("comment_ids")]
        public List<string>? CommentIds { get; set; }

        /// <summary>
        /// Whether to include replies to the top-level comments in the results
        /// </summary>
        [JsonPropertyName("include_replies")]
        public bool? IncludeReplies { get; set; }

        /// <summary>
        /// The visibility status of comments that you want to filter the results by
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Specific field to sort comments by
        /// </summary>
        [JsonPropertyName("sort_field")]
        public string? SortField { get; set; }

        /// <summary>
        /// Specific order to sort comments by
        /// </summary>
        [JsonPropertyName("sort_order")]
        public string? SortOrder { get; set; }

        /// <summary>
        /// Cursor for pagination
        /// </summary>
        [JsonPropertyName("cursor")]
        public int? Cursor { get; set; }

        /// <summary>
        /// The maximum number of comments that will be returned for each page
        /// </summary>
        [JsonPropertyName("max_count")]
        public int? MaxCount { get; set; }
    }
}
