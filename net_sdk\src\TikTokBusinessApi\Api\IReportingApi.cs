/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Reporting operations
    /// </summary>
    public interface IReportingApi
    {
        /// <summary>
        /// Run a synchronous report
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Synchronous report request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing report data</returns>
        Task<TikTokApiResponse<SynchronousReportResponse>> GetSynchronousReportAsync(
            SynchronousReportRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create an asynchronous report task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Asynchronous report task creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task ID</returns>
        // Task<Response<AsynchronousReportTaskCreateResponse>> CreateAsynchronousReportTaskAsync(
        //     string accessToken,
        //     AsynchronousReportTaskCreateRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the status of an asynchronous report task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="taskId">Task ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task status</returns>
        Task<Response<AsynchronousReportTaskStatusResponse>> GetAsynchronousReportTaskStatusAsync(
            string accessToken,
            string advertiserId,
            string taskId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Download the output of an asynchronous report task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="taskId">Task ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing download information</returns>
        Task<Response<AsynchronousReportTaskDownloadResponse>> DownloadAsynchronousReportTaskAsync(
            string accessToken,
            string advertiserId,
            string taskId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Cancel an asynchronous report task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Asynchronous report task cancellation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing cancellation status</returns>
        // Task<Response<AsynchronousReportTaskCancelResponse>> CancelAsynchronousReportTaskAsync(
        //     string accessToken,
        //     AsynchronousReportTaskCancelRequest request,
        //     CancellationToken cancellationToken = default);
    }
}
