/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API BC Members operations
    /// </summary>
    public interface IBCMembersApi
    {
        /// <summary>
        /// Get members in a Business Center. You need to be an Admin of the Business Center.
        /// </summary>
        /// <param name="request">Request parameters for getting members</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the list of members</returns>
        Task<GetMembersResponse> GetMembersAsync(
            GetMembersRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Invite members to a Business Center and assign roles and assets to members. You need to be an Admin of the Business Center.
        /// For each Business Center, the maximum number of members is 4000, and the maximum number of admins online is 20.
        /// </summary>
        /// <param name="request">Request parameters for inviting members</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response confirming the members were invited</returns>
        //Task<InviteMembersResponse> InviteMembersAsync(
        //    InviteMembersRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update a member's user name and their role in the Business Center. You need to be an Admin of the Business Center.
        /// </summary>
        /// <param name="request">Request parameters for updating a member</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response confirming the member was updated</returns>
        //Task<UpdateMemberResponse> UpdateMemberAsync(
        //    UpdateMemberRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a member in a Business Center. You need to be an Admin of the Business Center.
        /// </summary>
        /// <param name="request">Request parameters for deleting a member</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response confirming the member was deleted</returns>
        //Task<DeleteMemberResponse> DeleteMemberAsync(
        //    DeleteMemberRequest request,
        //    CancellationToken cancellationToken = default);
    }
}
