# RawTransactionEntity

## Thông tin bảng dữ liệu
- **Table**: `RawTransactions`
- **TablePrefix**: `Raw_`
- **TableName**: `Raw_RawTransactions`
- **DbSchema**: `null`

## Mô tả
Entity đại diện cho giao dịch (Transaction). Lưu trữ thông tin chi tiết về các giao dịch tài chính trong hệ thống TikTok Business, bao gồm thông tin thanh toán, thuế, và các chi tiết liên quan đến tài khoản quảng cáo và Business Center.

## Bảng dữ liệu: RawTransaction

| Tên Field | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả |
|-----------|--------------|----------|--------|-------|
| Id | Guid | ✓ | - | ID duy nhất của bản ghi (Primary Key) |
| TransactionId | string | ✓ | 100 | ID duy nhất của giao dịch |
| PaymentPortfolioId | string | ✗ | 100 | ID của danh mục thanh toán liên quan đến giao dịch |
| PaymentPortfolioName | string | ✗ | 255 | Tên của danh mục thanh toán liên quan đến giao dịch |
| AccountId | string | ✗ | 100 | ID của tài khoản quảng cáo liên quan đến giao dịch |
| AccountName | string | ✗ | 255 | Tên của tài khoản quảng cáo liên quan đến giao dịch |
| BcId | string | ✓ | 100 | ID của Business Center |
| BcName | string | ✓ | 255 | Tên của Business Center |
| Amount | decimal | ✓ | - | Tổng số tiền của giao dịch, bao gồm tổng phụ và số tiền thuế |
| Subtotal | decimal | ✓ | - | Số tiền tổng phụ (số tiền không bao gồm thuế) của giao dịch |
| TaxAmount | decimal | ✓ | - | Số tiền thuế có trong giao dịch |
| Currency | string | ✓ | 10 | Mã tiền tệ mà các số tiền được tính toán |
| AmountType | AmountType | ✓ | - | Loại số tiền |
| TransactionType | TransactionType | ✓ | - | Loại giao dịch |
| BillingType | BillingType | ✓ | - | Loại thanh toán |
| Timezone | string | ✓ | 20 | Múi giờ gốc nơi giao dịch xảy ra, định dạng UTC±HH:MM (để tham chiếu khi convert từ UTC) |
| CreateTime | DateTime | ✓ | - | Thời gian bản ghi giao dịch được tạo (lưu trữ dưới dạng UTC) |
| InvoiceId | string | ✗ | 100 | ID hóa đơn |
| SerialNumber | string | ✗ | 100 | Số sê-ri hóa đơn |
| TransactionLevel | TransactionLevel | ✓ | - | Cấp độ giao dịch |
| CreationTime | DateTime | ✓ | - | Thời gian tạo bản ghi (Audit) |
| CreatorId | Guid? | ✗ | - | ID người tạo (Audit) |
| LastModificationTime | DateTime? | ✗ | - | Thời gian sửa đổi cuối (Audit) |
| LastModifierId | Guid? | ✗ | - | ID người sửa đổi cuối (Audit) |

## Enums liên quan

### AmountType
- Định nghĩa loại số tiền trong giao dịch

### TransactionType
- Định nghĩa loại giao dịch (nạp tiền, chi tiêu, hoàn tiền, etc.)

### BillingType
- Định nghĩa loại thanh toán (thẻ tín dụng, chuyển khoản, etc.)

### TransactionLevel
- Định nghĩa cấp độ giao dịch (tài khoản, chiến dịch, nhóm quảng cáo, etc.)

## Cấu trúc dữ liệu

### Thông tin định danh
- **TransactionId**: ID duy nhất của giao dịch từ TikTok API
- **PaymentPortfolioId/Name**: Thông tin danh mục thanh toán
- **AccountId/Name**: Thông tin tài khoản quảng cáo liên quan
- **BcId/Name**: Thông tin Business Center

### Thông tin tài chính
- **Amount**: Tổng số tiền giao dịch (bao gồm thuế)
- **Subtotal**: Số tiền trước thuế
- **TaxAmount**: Số tiền thuế
- **Currency**: Đơn vị tiền tệ

### Phân loại giao dịch
- **AmountType**: Loại số tiền (credit, debit, etc.)
- **TransactionType**: Loại giao dịch (recharge, spend, refund, etc.)
- **BillingType**: Phương thức thanh toán
- **TransactionLevel**: Cấp độ giao dịch

### Thông tin bổ sung
- **InvoiceId/SerialNumber**: Thông tin hóa đơn
- **Timezone**: Múi giờ gốc của giao dịch (để tham chiếu khi convert từ UTC)
- **CreateTime**: Thời gian tạo giao dịch (lưu trữ dưới dạng UTC)

## Mục đích sử dụng
- Lưu trữ lịch sử giao dịch tài chính
- Theo dõi thu chi của tài khoản quảng cáo
- Quản lý hóa đơn và thanh toán
- Hỗ trợ báo cáo tài chính
- Tích hợp với hệ thống kế toán
- Theo dõi luồng tiền trong Business Center

## Lưu ý
- Tất cả các trường số tiền đều được lưu trữ với độ chính xác decimal
- Entity này kế thừa từ FullAuditedAggregateRoot để có đầy đủ thông tin audit
- Các trường liên quan đến tài khoản và danh mục thanh toán có thể null để đảm bảo tính linh hoạt
- **CreateTime được lưu trữ dưới dạng UTC** để đảm bảo tính nhất quán về thời gian
- **Timezone lưu trữ múi giờ gốc** để có thể convert ngược lại từ UTC khi cần thiết
- Khi đồng bộ dữ liệu, CreateTime từ API sẽ được convert sang UTC trước khi lưu vào database 