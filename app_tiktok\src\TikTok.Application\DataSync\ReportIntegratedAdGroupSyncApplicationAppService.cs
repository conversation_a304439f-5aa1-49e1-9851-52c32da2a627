using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu báo cáo tích hợp AdGroup
    /// </summary>
    public class ReportIntegratedAdGroupSyncApplicationAppService : ApplicationService, IReportIntegratedAdGroupSyncApplicationAppService
    {
        private readonly IReportIntegratedAdGroupSyncService _reportIntegratedAdGroupSyncService;

        public ReportIntegratedAdGroupSyncApplicationAppService(IReportIntegratedAdGroupSyncService reportIntegratedAdGroupSyncService)
        {
            _reportIntegratedAdGroupSyncService = reportIntegratedAdGroupSyncService;
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdGroup cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedAdGroupSyncResult> SyncAllReportIntegratedAdGroupForAllBcsAsync()
        {
            return await _reportIntegratedAdGroupSyncService.SyncAllReportIntegratedAdGroupForAllBcsAsync();
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdGroup theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate">Ngày bắt đầu (tùy chọn)</param>
        /// <param name="endDate">Ngày kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedAdGroupSyncResult> SyncReportIntegratedAdGroupAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _reportIntegratedAdGroupSyncService.SyncReportIntegratedAdGroupAsync(bcId, startDate, endDate);
        }
    }
}