/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for video settings
    /// </summary>
    public class VideoSettingsResponse
    {
        /// <summary>
        /// The available privacy level options for the TikTok account's posts
        /// </summary>
        [JsonPropertyName("privacy_level_options")]
        public List<string> PrivacyLevelOptions { get; set; } = new();

        /// <summary>
        /// Whether the "Allow comments" setting is available for the TikTok account's posts
        /// </summary>
        [Json<PERSON>ropertyName("comment_disabled")]
        public bool CommentDisabled { get; set; }

        /// <summary>
        /// Whether the "Allow Duets" setting is available for the TikTok account's posts
        /// </summary>
        [JsonPropertyName("duet_disabled")]
        public bool DuetDisabled { get; set; }

        /// <summary>
        /// Whether the "Allow Stitch" setting is available for the TikTok account's posts
        /// </summary>
        [JsonPropertyName("stitch_disabled")]
        public bool StitchDisabled { get; set; }

        /// <summary>
        /// The maximum duration for video posts that the TikTok account can publish in seconds
        /// </summary>
        [JsonPropertyName("max_video_post_duration_sec")]
        public int MaxVideoPostDurationSec { get; set; }
    }
}
