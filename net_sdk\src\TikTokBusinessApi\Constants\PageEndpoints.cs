/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Page endpoints
    /// </summary>
    public static class PageEndpoints
    {
        /// <summary>
        /// Get the Page ID endpoint (v1.3)
        /// </summary>
        public const string GetPages = "/page/get/";

        /// <summary>
        /// Create a TIP Editor SDK access token endpoint (v1.3)
        /// </summary>
        public const string CreateTipSdkAccessToken = "/oauth2/access_token/tip_sdk/create/";

        /// <summary>
        /// Validate a TIP Editor SDK access token endpoint (v1.3)
        /// </summary>
        public const string ValidateTipSdkAccessToken = "/oauth2/access_token/tip_sdk/validate/";

        /// <summary>
        /// Renew a TIP Editor SDK access token endpoint (v1.3)
        /// </summary>
        public const string RenewTipSdkAccessToken = "/oauth2/access_token/tip_sdk/renew/";
    }
}
