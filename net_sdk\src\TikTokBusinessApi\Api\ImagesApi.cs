/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Images operations
    /// </summary>
    public class ImagesApi : IImagesApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<ImagesApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the ImagesApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public ImagesApi(IApiClient apiClient, ILogger<ImagesApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Upload an image to the Asset Library and use the obtained image ID for creating ads
        /// </summary>
        /// <param name="request">Image upload request parameters</param>
        /// <param name="fileStream">Image file stream (required when upload_type is UPLOAD_BY_FILE)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing image upload information</returns>
        //public async Task<ImageUploadResponse> UploadImageAsync(
        //    ImageUploadRequest request,
        //    Stream? fileStream = null,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.UploadType))
        //        throw new ArgumentException("Upload type cannot be null or empty", nameof(request));

        //    // Validate required parameters based on upload type
        //    switch (request.UploadType)
        //    {
        //        case "UPLOAD_BY_FILE":
        //            if (fileStream == null)
        //                throw new ArgumentNullException(nameof(fileStream), "File stream is required when upload_type is UPLOAD_BY_FILE");
        //            if (string.IsNullOrWhiteSpace(request.ImageSignature))
        //                throw new ArgumentException("Image signature is required when upload_type is UPLOAD_BY_FILE", nameof(request));
        //            break;
        //        case "UPLOAD_BY_URL":
        //            if (string.IsNullOrWhiteSpace(request.ImageUrl))
        //                throw new ArgumentException("Image URL is required when upload_type is UPLOAD_BY_URL", nameof(request));
        //            break;
        //        case "UPLOAD_BY_FILE_ID":
        //            if (string.IsNullOrWhiteSpace(request.FileId))
        //                throw new ArgumentException("File ID is required when upload_type is UPLOAD_BY_FILE_ID", nameof(request));
        //            break;
        //        default:
        //            throw new ArgumentException($"Invalid upload type: {request.UploadType}", nameof(request));
        //    }

        //    _logger?.LogInformation("Uploading image for advertiser {AdvertiserId} using {UploadType}", 
        //        request.AdvertiserId, request.UploadType);

        //    object body;
        //    if (request.UploadType == "UPLOAD_BY_FILE" && fileStream != null)
        //    {
        //        // Create multipart form data content for file upload
        //        var content = new MultipartFormDataContent();
        //        content.Add(new StringContent(request.AdvertiserId), "advertiser_id");
        //        content.Add(new StringContent(request.UploadType), "upload_type");
                
        //        if (!string.IsNullOrWhiteSpace(request.FileName))
        //            content.Add(new StringContent(request.FileName), "file_name");
        //        if (!string.IsNullOrWhiteSpace(request.ImageSignature))
        //            content.Add(new StringContent(request.ImageSignature), "image_signature");
                
        //        content.Add(new StreamContent(fileStream), "image_file", request.FileName ?? "image");
        //        body = content;
        //    }
        //    else
        //    {
        //        // Use JSON body for URL or FILE_ID uploads
        //        body = request;
        //    }

        //    return await _apiClient.CallApiAsync<ImageUploadResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ImagesEndpoints.UploadImage}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Update the name of an image
        /// </summary>
        /// <param name="request">Image update request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the asynchronous operation</returns>
        //public async Task UpdateImageAsync(
        //    ImageUpdateRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.ImageId))
        //        throw new ArgumentException("Image ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.FileName))
        //        throw new ArgumentException("File name cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Updating image {ImageId} for advertiser {AdvertiserId}", 
        //        request.ImageId, request.AdvertiserId);

        //    await _apiClient.CallApiAsync<object>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ImagesEndpoints.UpdateImage}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get information about images from the Asset Library
        /// </summary>
        /// <param name="request">Image info request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing image information</returns>
        public async Task<ImageInfoResponse> GetImageInfoAsync(
            ImageInfoRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));
            if (request.ImageIds == null || request.ImageIds.Count == 0)
                throw new ArgumentException("Image IDs list cannot be null or empty", nameof(request));
            if (request.ImageIds.Count > 100)
                throw new ArgumentException("Maximum 100 image IDs allowed per request", nameof(request));

            _logger?.LogInformation("Getting info for {Count} images for advertiser {AdvertiserId}", 
                request.ImageIds.Count, request.AdvertiserId);

            var queryParams = new Dictionary<string, string>
            {
                { "advertiser_id", request.AdvertiserId },
                { "image_ids", System.Text.Json.JsonSerializer.Serialize(request.ImageIds) }
            };

            return await _apiClient.CallApiAsync<ImageInfoResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ImagesEndpoints.GetImageInfo}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Search for image creatives in an advertising account's Asset Library
        /// </summary>
        /// <param name="request">Image search request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing search results</returns>
        public async Task<ImageSearchResponse> SearchImagesAsync(
            ImageSearchRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Searching images for advertiser {AdvertiserId}", request.AdvertiserId);

            var queryParams = new Dictionary<string, string>
            {
                { "advertiser_id", request.AdvertiserId }
            };

            if (request.Filtering != null)
                queryParams.Add("filtering", System.Text.Json.JsonSerializer.Serialize(request.Filtering));
            if (request.Page.HasValue)
                queryParams.Add("page", request.Page.Value.ToString());
            if (request.PageSize.HasValue)
                queryParams.Add("page_size", request.PageSize.Value.ToString());

            return await _apiClient.CallApiAsync<ImageSearchResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ImagesEndpoints.SearchImages}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
