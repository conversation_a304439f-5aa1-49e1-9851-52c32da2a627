/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Api;
using TikTokBusinessApi.Core;

namespace TikTokBusinessApi.Utilities
{
    /// <summary>
    /// Health check utility to verify SDK and API connectivity
    /// </summary>
    public class HealthChecker
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<HealthChecker>? _logger;
        private readonly HealthCheckOptions _options;

        /// <summary>
        /// Initializes a new instance of the HealthChecker class
        /// </summary>
        /// <param name="apiClient">API client for making requests</param>
        /// <param name="options">Health check configuration options</param>
        /// <param name="logger">Logger instance (optional)</param>
        public HealthChecker(IApiClient apiClient, HealthCheckOptions? options = null, ILogger<HealthChecker>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _options = options ?? new HealthCheckOptions();
            _logger = logger;
        }

        /// <summary>
        /// Perform a comprehensive health check
        /// </summary>
        /// <param name="accessToken">Access token for API calls (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Health check result</returns>
        public async Task<HealthCheckResult> CheckHealthAsync(string? accessToken = null, CancellationToken cancellationToken = default)
        {
            var result = new HealthCheckResult
            {
                StartTime = DateTimeOffset.UtcNow
            };

            _logger?.LogInformation("Starting comprehensive health check");

            var checks = new List<Task<HealthCheckItem>>
            {
                CheckApiConnectivityAsync(cancellationToken),
                CheckConfigurationAsync(cancellationToken)
            };

            if (!string.IsNullOrEmpty(accessToken))
            {
                checks.Add(CheckAuthenticationAsync(accessToken, cancellationToken));
                checks.Add(CheckBasicApiCallAsync(accessToken, cancellationToken));
            }

            if (_options.CheckDependencies)
            {
                checks.Add(CheckDependenciesAsync(cancellationToken));
            }

            if (_options.CheckPerformance)
            {
                checks.Add(CheckPerformanceAsync(cancellationToken));
            }

            // Execute all checks
            var checkResults = await Task.WhenAll(checks);
            result.Checks.AddRange(checkResults);

            result.EndTime = DateTimeOffset.UtcNow;
            result.Duration = result.EndTime - result.StartTime;

            // Determine overall health status
            result.Status = DetermineOverallStatus(result.Checks);

            _logger?.LogInformation("Health check completed in {Duration}ms with status: {Status}", 
                result.Duration.TotalMilliseconds, result.Status);

            return result;
        }

        /// <summary>
        /// Perform a quick health check (connectivity only)
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Health check result</returns>
        public async Task<HealthCheckResult> QuickHealthCheckAsync(CancellationToken cancellationToken = default)
        {
            var result = new HealthCheckResult
            {
                StartTime = DateTimeOffset.UtcNow
            };

            _logger?.LogDebug("Starting quick health check");

            var connectivityCheck = await CheckApiConnectivityAsync(cancellationToken);
            result.Checks.Add(connectivityCheck);

            result.EndTime = DateTimeOffset.UtcNow;
            result.Duration = result.EndTime - result.StartTime;
            result.Status = connectivityCheck.Status;

            return result;
        }

        private async Task<HealthCheckItem> CheckApiConnectivityAsync(CancellationToken cancellationToken)
        {
            var check = new HealthCheckItem
            {
                Name = "API Connectivity",
                StartTime = DateTimeOffset.UtcNow
            };

            try
            {
                var stopwatch = Stopwatch.StartNew();
                
                // Try to make a simple HTTP request to the API base URL
                using var httpClient = new HttpClient { Timeout = _options.ConnectivityTimeout };
                var response = await httpClient.GetAsync($"{_apiClient.ApiPrefix}/health", cancellationToken);
                
                stopwatch.Stop();
                check.Duration = stopwatch.Elapsed;
                check.EndTime = DateTimeOffset.UtcNow;

                if (response.IsSuccessStatusCode)
                {
                    check.Status = HealthStatus.Healthy;
                    check.Message = $"API is reachable (HTTP {(int)response.StatusCode})";
                }
                else
                {
                    check.Status = HealthStatus.Degraded;
                    check.Message = $"API returned HTTP {(int)response.StatusCode}";
                }

                check.Data["ResponseTime"] = check.Duration.TotalMilliseconds;
                check.Data["StatusCode"] = (int)response.StatusCode;
            }
            catch (TaskCanceledException)
            {
                check.Status = HealthStatus.Unhealthy;
                check.Message = "API connectivity check timed out";
                check.EndTime = DateTimeOffset.UtcNow;
                check.Duration = check.EndTime - check.StartTime;
            }
            catch (Exception ex)
            {
                check.Status = HealthStatus.Unhealthy;
                check.Message = $"API connectivity failed: {ex.Message}";
                check.EndTime = DateTimeOffset.UtcNow;
                check.Duration = check.EndTime - check.StartTime;
                check.Exception = ex;
            }

            return check;
        }

        private async Task<HealthCheckItem> CheckConfigurationAsync(CancellationToken cancellationToken)
        {
            var check = new HealthCheckItem
            {
                Name = "Configuration",
                StartTime = DateTimeOffset.UtcNow
            };

            try
            {
                // Create a Configuration object from ApiClient properties
                var configuration = new Configuration
                {
                    BaseUrl = _apiClient.BasePath,
                    Timeout = _apiClient.Timeout,
                    MaxRetries = _apiClient.MaxRetryAttempts,
                    RetryDelay = TimeSpan.FromMilliseconds(_apiClient.RetryDelayMs)
                };

                // Use ConfigurationValidator if available
                var validator = new ConfigurationValidator(null);
                var validationResult = validator.ValidateConfiguration(configuration);

                check.EndTime = DateTimeOffset.UtcNow;
                check.Duration = check.EndTime - check.StartTime;

                if (validationResult.IsValid)
                {
                    check.Status = validationResult.WarningCount > 0 ? HealthStatus.Degraded : HealthStatus.Healthy;
                    check.Message = validationResult.GetSummary();
                }
                else
                {
                    check.Status = HealthStatus.Unhealthy;
                    check.Message = validationResult.GetSummary();
                }

                check.Data["ErrorCount"] = validationResult.ErrorCount;
                check.Data["WarningCount"] = validationResult.WarningCount;
                check.Data["InfoCount"] = validationResult.InfoCount;
            }
            catch (Exception ex)
            {
                check.Status = HealthStatus.Unhealthy;
                check.Message = $"Configuration validation failed: {ex.Message}";
                check.EndTime = DateTimeOffset.UtcNow;
                check.Duration = check.EndTime - check.StartTime;
                check.Exception = ex;
            }

            return check;
        }

        private async Task<HealthCheckItem> CheckAuthenticationAsync(string accessToken, CancellationToken cancellationToken)
        {
            var check = new HealthCheckItem
            {
                Name = "Authentication",
                StartTime = DateTimeOffset.UtcNow
            };

            try
            {
                // Try to validate the access token by making a simple API call
                var accountApi = new AccountApi(_apiClient, null);
                var tokenRequest = new Models.Account.TokenInfoRequest
                {
                    AppId = "test-app-id", // This would need to be configured properly
                    AccessToken = accessToken
                };
                var tokenInfo = await accountApi.GetTokenInfoAsync(tokenRequest, cancellationToken);

                check.EndTime = DateTimeOffset.UtcNow;
                check.Duration = check.EndTime - check.StartTime;

                if (tokenInfo != null)
                {
                    check.Status = HealthStatus.Healthy;
                    check.Message = "Access token is valid";
                    check.Data["TokenValid"] = true;
                    check.Data["AppId"] = tokenInfo.AppId ?? "unknown";
                }
                else
                {
                    check.Status = HealthStatus.Unhealthy;
                    check.Message = "Access token validation failed";
                    check.Data["TokenValid"] = false;
                }
            }
            catch (Exception ex)
            {
                check.Status = HealthStatus.Unhealthy;
                check.Message = $"Authentication check failed: {ex.Message}";
                check.EndTime = DateTimeOffset.UtcNow;
                check.Duration = check.EndTime - check.StartTime;
                check.Exception = ex;
                check.Data["TokenValid"] = false;
            }

            return check;
        }

        private async Task<HealthCheckItem> CheckBasicApiCallAsync(string accessToken, CancellationToken cancellationToken)
        {
            var check = new HealthCheckItem
            {
                Name = "Basic API Call",
                StartTime = DateTimeOffset.UtcNow
            };

            try
            {
                // Make a simple API call to test basic functionality
                var accountApi = new AccountApi(_apiClient, null);
                var tokenRequest = new Models.Account.TokenInfoRequest
                {
                    AppId = "test-app-id", // This would need to be configured properly
                    AccessToken = accessToken
                };
                var tokenInfo = await accountApi.GetTokenInfoAsync(tokenRequest, cancellationToken);

                check.EndTime = DateTimeOffset.UtcNow;
                check.Duration = check.EndTime - check.StartTime;

                if (tokenInfo != null)
                {
                    check.Status = HealthStatus.Healthy;
                    check.Message = "API connectivity verified";
                    check.Data["AppId"] = tokenInfo.AppId ?? "unknown";
                }
                else
                {
                    check.Status = HealthStatus.Degraded;
                    check.Message = "Basic API call returned no data";
                }

                check.Data["ResponseTime"] = check.Duration.TotalMilliseconds;
            }
            catch (Exception ex)
            {
                check.Status = HealthStatus.Unhealthy;
                check.Message = $"Basic API call failed: {ex.Message}";
                check.EndTime = DateTimeOffset.UtcNow;
                check.Duration = check.EndTime - check.StartTime;
                check.Exception = ex;
            }

            return check;
        }

        private async Task<HealthCheckItem> CheckDependenciesAsync(CancellationToken cancellationToken)
        {
            var check = new HealthCheckItem
            {
                Name = "Dependencies",
                StartTime = DateTimeOffset.UtcNow
            };

            try
            {
                // Check critical dependencies
                var dependencies = new List<string>();

                // Check if HttpClient is available
                using var testClient = new HttpClient();
                dependencies.Add("HttpClient");

                // Check JSON serialization
                var testObject = new { test = "value" };
                var json = System.Text.Json.JsonSerializer.Serialize(testObject);
                System.Text.Json.JsonSerializer.Deserialize<object>(json);
                dependencies.Add("System.Text.Json");

                check.EndTime = DateTimeOffset.UtcNow;
                check.Duration = check.EndTime - check.StartTime;
                check.Status = HealthStatus.Healthy;
                check.Message = $"All {dependencies.Count} dependencies are available";
                check.Data["DependencyCount"] = dependencies.Count;
                check.Data["Dependencies"] = dependencies;
            }
            catch (Exception ex)
            {
                check.Status = HealthStatus.Unhealthy;
                check.Message = $"Dependency check failed: {ex.Message}";
                check.EndTime = DateTimeOffset.UtcNow;
                check.Duration = check.EndTime - check.StartTime;
                check.Exception = ex;
            }

            return check;
        }

        private async Task<HealthCheckItem> CheckPerformanceAsync(CancellationToken cancellationToken)
        {
            var check = new HealthCheckItem
            {
                Name = "Performance",
                StartTime = DateTimeOffset.UtcNow
            };

            try
            {
                // Perform basic performance checks
                var stopwatch = Stopwatch.StartNew();
                
                // Test JSON serialization performance
                var testData = new Dictionary<string, object>
                {
                    ["timestamp"] = DateTimeOffset.UtcNow,
                    ["data"] = Enumerable.Range(1, 1000).ToArray()
                };

                var json = System.Text.Json.JsonSerializer.Serialize(testData);
                var deserialized = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(json);

                stopwatch.Stop();

                check.EndTime = DateTimeOffset.UtcNow;
                check.Duration = check.EndTime - check.StartTime;

                var serializationTime = stopwatch.Elapsed.TotalMilliseconds;
                
                if (serializationTime < 100)
                {
                    check.Status = HealthStatus.Healthy;
                    check.Message = "Performance is good";
                }
                else if (serializationTime < 500)
                {
                    check.Status = HealthStatus.Degraded;
                    check.Message = "Performance is acceptable but could be better";
                }
                else
                {
                    check.Status = HealthStatus.Unhealthy;
                    check.Message = "Performance is poor";
                }

                check.Data["SerializationTime"] = serializationTime;
                check.Data["MemoryUsage"] = GC.GetTotalMemory(false);
            }
            catch (Exception ex)
            {
                check.Status = HealthStatus.Unhealthy;
                check.Message = $"Performance check failed: {ex.Message}";
                check.EndTime = DateTimeOffset.UtcNow;
                check.Duration = check.EndTime - check.StartTime;
                check.Exception = ex;
            }

            return check;
        }

        private HealthStatus DetermineOverallStatus(List<HealthCheckItem> checks)
        {
            var hasUnhealthy = false;
            var hasDegraded = false;

            foreach (var check in checks)
            {
                switch (check.Status)
                {
                    case HealthStatus.Unhealthy:
                        hasUnhealthy = true;
                        break;
                    case HealthStatus.Degraded:
                        hasDegraded = true;
                        break;
                }
            }

            if (hasUnhealthy)
                return HealthStatus.Unhealthy;
            if (hasDegraded)
                return HealthStatus.Degraded;
            
            return HealthStatus.Healthy;
        }
    }

    /// <summary>
    /// Configuration options for health checks
    /// </summary>
    public class HealthCheckOptions
    {
        /// <summary>
        /// Timeout for connectivity checks
        /// </summary>
        public TimeSpan ConnectivityTimeout { get; set; } = TimeSpan.FromSeconds(10);

        /// <summary>
        /// Whether to check dependencies
        /// </summary>
        public bool CheckDependencies { get; set; } = true;

        /// <summary>
        /// Whether to check performance
        /// </summary>
        public bool CheckPerformance { get; set; } = true;

        /// <summary>
        /// Whether to include detailed error information
        /// </summary>
        public bool IncludeExceptionDetails { get; set; } = false;
    }

    /// <summary>
    /// Overall health check result
    /// </summary>
    public class HealthCheckResult
    {
        /// <summary>
        /// Overall health status
        /// </summary>
        public HealthStatus Status { get; set; } = HealthStatus.Unknown;

        /// <summary>
        /// When the health check started
        /// </summary>
        public DateTimeOffset StartTime { get; set; }

        /// <summary>
        /// When the health check completed
        /// </summary>
        public DateTimeOffset EndTime { get; set; }

        /// <summary>
        /// Total duration of the health check
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Individual health check items
        /// </summary>
        public List<HealthCheckItem> Checks { get; set; } = new();

        /// <summary>
        /// Get a summary of the health check results
        /// </summary>
        /// <returns>Summary string</returns>
        public string GetSummary()
        {
            var healthy = Checks.Count(c => c.Status == HealthStatus.Healthy);
            var degraded = Checks.Count(c => c.Status == HealthStatus.Degraded);
            var unhealthy = Checks.Count(c => c.Status == HealthStatus.Unhealthy);

            return $"Health Check: {Status} ({healthy} healthy, {degraded} degraded, {unhealthy} unhealthy) in {Duration.TotalMilliseconds:F0}ms";
        }
    }

    /// <summary>
    /// Individual health check item
    /// </summary>
    public class HealthCheckItem
    {
        /// <summary>
        /// Name of the health check
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Health status
        /// </summary>
        public HealthStatus Status { get; set; } = HealthStatus.Unknown;

        /// <summary>
        /// Descriptive message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// When the check started
        /// </summary>
        public DateTimeOffset StartTime { get; set; }

        /// <summary>
        /// When the check completed
        /// </summary>
        public DateTimeOffset EndTime { get; set; }

        /// <summary>
        /// Duration of the check
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Additional data from the check
        /// </summary>
        public Dictionary<string, object> Data { get; set; } = new();

        /// <summary>
        /// Exception that occurred during the check (if any)
        /// </summary>
        public Exception? Exception { get; set; }
    }

    /// <summary>
    /// Health status enumeration
    /// </summary>
    public enum HealthStatus
    {
        /// <summary>
        /// Health status is unknown
        /// </summary>
        Unknown,

        /// <summary>
        /// System is healthy and functioning normally
        /// </summary>
        Healthy,

        /// <summary>
        /// System is functioning but with some issues
        /// </summary>
        Degraded,

        /// <summary>
        /// System is not functioning properly
        /// </summary>
        Unhealthy
    }
}
