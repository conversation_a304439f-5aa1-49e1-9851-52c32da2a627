/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for creating Smart Creative ads
    /// </summary>
    public class SmartCreativeCreateBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string AdGroupId { get; set; } = string.Empty;

        /// <summary>
        /// List of media information
        /// </summary>
        [JsonPropertyName("media_info_list")]
        public List<SmartCreativeMediaInfoWrapper> MediaInfoList { get; set; } = new List<SmartCreativeMediaInfoWrapper>();

        /// <summary>
        /// List of ad titles (also called ad texts)
        /// </summary>
        [JsonPropertyName("title_list")]
        public List<SmartCreativeTitle>? TitleList { get; set; }

        /// <summary>
        /// Call-to-action list
        /// </summary>
        [JsonPropertyName("call_to_action_list")]
        public List<SmartCreativeCallToAction>? CallToActionList { get; set; }

        /// <summary>
        /// List of deeplinks
        /// </summary>
        [JsonPropertyName("deeplink_list")]
        public List<SmartCreativeDeeplink>? DeeplinkList { get; set; }

        /// <summary>
        /// Display names
        /// </summary>
        [JsonPropertyName("display_name_list")]
        public List<SmartCreativeDisplayName>? DisplayNameList { get; set; }

        /// <summary>
        /// Page ID list
        /// </summary>
        [JsonPropertyName("page_list")]
        public List<SmartCreativePage>? PageList { get; set; }

        /// <summary>
        /// Card ID list
        /// </summary>
        [JsonPropertyName("card_list")]
        public List<SmartCreativeCard>? CardList { get; set; }

        /// <summary>
        /// Multiple landing page URLs
        /// </summary>
        [JsonPropertyName("landing_page_urls")]
        public List<SmartCreativeLandingPageUrl>? LandingPageUrls { get; set; }

        /// <summary>
        /// Common material
        /// </summary>
        [JsonPropertyName("common_material")]
        public SmartCreativeCommonMaterial? CommonMaterial { get; set; }
    }

    /// <summary>
    /// Request body for updating Smart Creative materials
    /// </summary>
    public class SmartCreativeUpdateBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string AdGroupId { get; set; } = string.Empty;

        /// <summary>
        /// Whether to use incremental update mode
        /// </summary>
        [JsonPropertyName("patch_update")]
        public bool? PatchUpdate { get; set; }

        /// <summary>
        /// List of media information
        /// </summary>
        [JsonPropertyName("media_info_list")]
        public List<SmartCreativeMediaInfoWrapper>? MediaInfoList { get; set; }

        /// <summary>
        /// List of ad titles (also called ad texts)
        /// </summary>
        [JsonPropertyName("title_list")]
        public List<SmartCreativeTitle>? TitleList { get; set; }

        /// <summary>
        /// Call-to-action list
        /// </summary>
        [JsonPropertyName("call_to_action_list")]
        public List<SmartCreativeCallToAction>? CallToActionList { get; set; }

        /// <summary>
        /// List of deeplinks
        /// </summary>
        [JsonPropertyName("deeplink_list")]
        public List<SmartCreativeDeeplink>? DeeplinkList { get; set; }

        /// <summary>
        /// Display names
        /// </summary>
        [JsonPropertyName("display_name_list")]
        public List<SmartCreativeDisplayName>? DisplayNameList { get; set; }

        /// <summary>
        /// Avatar image list
        /// </summary>
        [JsonPropertyName("avatar_icon_list")]
        public List<SmartCreativeAvatarIcon>? AvatarIconList { get; set; }

        /// <summary>
        /// Page ID list
        /// </summary>
        [JsonPropertyName("page_list")]
        public List<SmartCreativePage>? PageList { get; set; }

        /// <summary>
        /// Card ID list
        /// </summary>
        [JsonPropertyName("card_list")]
        public List<SmartCreativeCard>? CardList { get; set; }

        /// <summary>
        /// Common material
        /// </summary>
        [JsonPropertyName("common_material")]
        public SmartCreativeCommonMaterial? CommonMaterial { get; set; }
    }

    /// <summary>
    /// Request body for updating Smart Creative material statuses
    /// </summary>
    public class SmartCreativeMaterialStatusUpdateBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("ad_group_id")]
        public string AdGroupId { get; set; } = string.Empty;

        /// <summary>
        /// Material IDs
        /// </summary>
        [JsonPropertyName("material_ids")]
        public List<string> MaterialIds { get; set; } = new List<string>();

        /// <summary>
        /// Status of the material
        /// </summary>
        [JsonPropertyName("material_status")]
        public string MaterialStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// Wrapper for media information
    /// </summary>
    public class SmartCreativeMediaInfoWrapper
    {
        /// <summary>
        /// Material information
        /// </summary>
        [JsonPropertyName("media_info")]
        public SmartCreativeMediaInfo MediaInfo { get; set; } = new SmartCreativeMediaInfo();
    }

    /// <summary>
    /// Media information
    /// </summary>
    public class SmartCreativeMediaInfo
    {
        /// <summary>
        /// Video information
        /// </summary>
        [JsonPropertyName("video_info")]
        public SmartCreativeVideoInfo? VideoInfo { get; set; }

        /// <summary>
        /// Image information
        /// </summary>
        [JsonPropertyName("image_info")]
        public List<SmartCreativeImageInfo>? ImageInfo { get; set; }

        /// <summary>
        /// AIGC disclosure type
        /// </summary>
        [JsonPropertyName("aigc_disclosure_type")]
        public string? AigcDisclosureType { get; set; }

        /// <summary>
        /// TikTok item ID for Spark Ads
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TikTokItemId { get; set; }

        /// <summary>
        /// Identity ID for Spark Ads
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Identity type for Spark Ads
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }
    }

    /// <summary>
    /// Video information
    /// </summary>
    public class SmartCreativeVideoInfo
    {
        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Video name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }
    }

    /// <summary>
    /// Image information
    /// </summary>
    public class SmartCreativeImageInfo
    {
        /// <summary>
        /// Image ID
        /// </summary>
        [JsonPropertyName("web_uri")]
        public string WebUri { get; set; } = string.Empty;

        /// <summary>
        /// Image name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }
    }

    /// <summary>
    /// Ad title information
    /// </summary>
    public class SmartCreativeTitle
    {
        /// <summary>
        /// Ad title (ad text)
        /// </summary>
        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;
    }

    /// <summary>
    /// Call-to-action information
    /// </summary>
    public class SmartCreativeCallToAction
    {
        /// <summary>
        /// Call-to-action text
        /// </summary>
        [JsonPropertyName("call_to_action")]
        public string CallToAction { get; set; } = string.Empty;
    }

    /// <summary>
    /// Deeplink information
    /// </summary>
    public class SmartCreativeDeeplink
    {
        /// <summary>
        /// The specific location where you want your audience to go if they have your app installed
        /// </summary>
        [JsonPropertyName("deeplink")]
        public string Deeplink { get; set; } = string.Empty;

        /// <summary>
        /// The deeplink type
        /// </summary>
        [JsonPropertyName("deeplink_type")]
        public string DeeplinkType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Display name information
    /// </summary>
    public class SmartCreativeDisplayName
    {
        /// <summary>
        /// App name that is displayed in the ad
        /// </summary>
        [JsonPropertyName("app_name")]
        public string? AppName { get; set; }

        /// <summary>
        /// The display name of the landing page
        /// </summary>
        [JsonPropertyName("landing_page_name")]
        public string? LandingPageName { get; set; }
    }

    /// <summary>
    /// Avatar icon information
    /// </summary>
    public class SmartCreativeAvatarIcon
    {
        /// <summary>
        /// Avatar image
        /// </summary>
        [JsonPropertyName("avatar_icon")]
        public SmartCreativeAvatarIconInfo AvatarIcon { get; set; } = new SmartCreativeAvatarIconInfo();
    }

    /// <summary>
    /// Avatar icon details
    /// </summary>
    public class SmartCreativeAvatarIconInfo
    {
        /// <summary>
        /// ID of the avatar image
        /// </summary>
        [JsonPropertyName("web_uri")]
        public string WebUri { get; set; } = string.Empty;
    }

    /// <summary>
    /// Page information
    /// </summary>
    public class SmartCreativePage
    {
        /// <summary>
        /// Page ID
        /// </summary>
        [JsonPropertyName("page_id")]
        public string PageId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Card information
    /// </summary>
    public class SmartCreativeCard
    {
        /// <summary>
        /// Display Card ID, Gift Code Sticker ID, Countdown Sticker ID, or Download Card ID
        /// </summary>
        [JsonPropertyName("card_id")]
        public string CardId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Landing page URL information
    /// </summary>
    public class SmartCreativeLandingPageUrl
    {
        /// <summary>
        /// Landing page URL
        /// </summary>
        [JsonPropertyName("landing_page_url")]
        public string LandingPageUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// Common material information
    /// </summary>
    public class SmartCreativeCommonMaterial
    {
        /// <summary>
        /// Ad name
        /// </summary>
        [JsonPropertyName("ad_name")]
        public string? AdName { get; set; }

        /// <summary>
        /// The ID of the CTA portfolio that you want to use in your ads
        /// </summary>
        [JsonPropertyName("call_to_action_id")]
        public string? CallToActionId { get; set; }

        /// <summary>
        /// Whether you grant displaying some of your ads in our TikTok for Business Creative Center
        /// </summary>
        [JsonPropertyName("creative_authorized")]
        public bool? CreativeAuthorized { get; set; }

        /// <summary>
        /// Playable material url
        /// </summary>
        [JsonPropertyName("playable_url")]
        public string? PlayableUrl { get; set; }

        /// <summary>
        /// Fallback Type
        /// </summary>
        [JsonPropertyName("fallback_type")]
        public string? FallbackType { get; set; }

        /// <summary>
        /// Tracking information
        /// </summary>
        [JsonPropertyName("tracking_info")]
        public SmartCreativeTrackingInfo? TrackingInfo { get; set; }

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Identity type
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// Whether to enable Smart Creative for the ad group
        /// </summary>
        [JsonPropertyName("is_smart_creative")]
        public bool? IsSmartCreative { get; set; }
    }

    /// <summary>
    /// Tracking information
    /// </summary>
    public class SmartCreativeTrackingInfo
    {
        /// <summary>
        /// Default Impression Tracking URL
        /// </summary>
        [JsonPropertyName("impression_tracking_urls")]
        public List<string>? ImpressionTrackingUrls { get; set; }

        /// <summary>
        /// Click Tracking URL
        /// </summary>
        [JsonPropertyName("click_tracking_urls")]
        public List<string>? ClickTrackingUrls { get; set; }

        /// <summary>
        /// The pixel ID that you'd like to track
        /// </summary>
        [JsonPropertyName("tracking_pixel_id")]
        public string? TrackingPixelId { get; set; }

        /// <summary>
        /// The ID of the application that you want to track
        /// </summary>
        [JsonPropertyName("tracking_app_id")]
        public string? TrackingAppId { get; set; }

        /// <summary>
        /// A list of Offline Event set IDs that you want to track
        /// </summary>
        [JsonPropertyName("tracking_offline_event_set_ids")]
        public List<string>? TrackingOfflineEventSetIds { get; set; }
    }

    /// <summary>
    /// Response for Smart Creative create operation
    /// </summary>
    public class SmartCreativeCreateResponse
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdGroupId { get; set; }

        /// <summary>
        /// List of media information
        /// </summary>
        [JsonPropertyName("media_info_list")]
        public List<SmartCreativeMediaInfoResponseWrapper>? MediaInfoList { get; set; }

        /// <summary>
        /// List of ad titles
        /// </summary>
        [JsonPropertyName("title_list")]
        public List<SmartCreativeTitle>? TitleList { get; set; }

        /// <summary>
        /// Call-to-action list
        /// </summary>
        [JsonPropertyName("call_to_action_list")]
        public List<SmartCreativeCallToAction>? CallToActionList { get; set; }

        /// <summary>
        /// List of deeplinks
        /// </summary>
        [JsonPropertyName("deeplink_list")]
        public List<SmartCreativeDeeplink>? DeeplinkList { get; set; }

        /// <summary>
        /// Display names
        /// </summary>
        [JsonPropertyName("display_name_list")]
        public List<SmartCreativeDisplayName>? DisplayNameList { get; set; }

        /// <summary>
        /// Page ID list
        /// </summary>
        [JsonPropertyName("page_list")]
        public List<SmartCreativePage>? PageList { get; set; }

        /// <summary>
        /// Card ID list
        /// </summary>
        [JsonPropertyName("card_list")]
        public List<SmartCreativeCard>? CardList { get; set; }

        /// <summary>
        /// Landing page URLs
        /// </summary>
        [JsonPropertyName("landing_page_urls")]
        public List<SmartCreativeLandingPageUrl>? LandingPageUrls { get; set; }

        /// <summary>
        /// Common material
        /// </summary>
        [JsonPropertyName("common_material")]
        public SmartCreativeCommonMaterial? CommonMaterial { get; set; }
    }

    /// <summary>
    /// Response for Smart Creative get operation
    /// </summary>
    public class SmartCreativeGetResponse
    {
        /// <summary>
        /// Data list
        /// </summary>
        [JsonPropertyName("list")]
        public List<SmartCreativeGetData>? List { get; set; }
    }

    /// <summary>
    /// Smart Creative get data
    /// </summary>
    public class SmartCreativeGetData
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdGroupId { get; set; }

        /// <summary>
        /// List of media information
        /// </summary>
        [JsonPropertyName("media_info_list")]
        public List<SmartCreativeMediaInfoResponseWrapper>? MediaInfoList { get; set; }

        /// <summary>
        /// List of ad titles
        /// </summary>
        [JsonPropertyName("title_list")]
        public List<SmartCreativeTitleResponse>? TitleList { get; set; }

        /// <summary>
        /// Call-to-action list
        /// </summary>
        [JsonPropertyName("call_to_action_list")]
        public List<SmartCreativeCallToAction>? CallToActionList { get; set; }

        /// <summary>
        /// List of deeplinks
        /// </summary>
        [JsonPropertyName("deeplink_list")]
        public List<SmartCreativeDeeplink>? DeeplinkList { get; set; }

        /// <summary>
        /// Display names
        /// </summary>
        [JsonPropertyName("display_name_list")]
        public List<SmartCreativeDisplayName>? DisplayNameList { get; set; }

        /// <summary>
        /// Avatar icon list
        /// </summary>
        [JsonPropertyName("avatar_icon_list")]
        public List<SmartCreativeAvatarIcon>? AvatarIconList { get; set; }

        /// <summary>
        /// Page ID list
        /// </summary>
        [JsonPropertyName("page_list")]
        public List<SmartCreativePage>? PageList { get; set; }

        /// <summary>
        /// Card ID list
        /// </summary>
        [JsonPropertyName("card_list")]
        public List<SmartCreativeCard>? CardList { get; set; }

        /// <summary>
        /// Landing page URLs
        /// </summary>
        [JsonPropertyName("landing_page_urls")]
        public List<string>? LandingPageUrls { get; set; }

        /// <summary>
        /// Common material
        /// </summary>
        [JsonPropertyName("common_material")]
        public SmartCreativeCommonMaterial? CommonMaterial { get; set; }
    }

    /// <summary>
    /// Response wrapper for media information
    /// </summary>
    public class SmartCreativeMediaInfoResponseWrapper
    {
        /// <summary>
        /// Material ID
        /// </summary>
        [JsonPropertyName("material_id")]
        public string? MaterialId { get; set; }

        /// <summary>
        /// The status of the material
        /// </summary>
        [JsonPropertyName("material_operation_status")]
        public string? MaterialOperationStatus { get; set; }

        /// <summary>
        /// Media Information
        /// </summary>
        [JsonPropertyName("media_info")]
        public SmartCreativeMediaInfoResponse? MediaInfo { get; set; }
    }

    /// <summary>
    /// Media information response
    /// </summary>
    public class SmartCreativeMediaInfoResponse
    {
        /// <summary>
        /// Image information
        /// </summary>
        [JsonPropertyName("image_info")]
        public List<SmartCreativeImageInfo>? ImageInfo { get; set; }

        /// <summary>
        /// Video information
        /// </summary>
        [JsonPropertyName("video_info")]
        public SmartCreativeVideoInfo? VideoInfo { get; set; }

        /// <summary>
        /// AIGC disclosure type
        /// </summary>
        [JsonPropertyName("aigc_disclosure_type")]
        public string? AigcDisclosureType { get; set; }

        /// <summary>
        /// TikTok item ID for Spark Ads
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TikTokItemId { get; set; }

        /// <summary>
        /// Name of the material
        /// </summary>
        [JsonPropertyName("material_name")]
        public string? MaterialName { get; set; }

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Identity type
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }
    }

    /// <summary>
    /// Title response with material information
    /// </summary>
    public class SmartCreativeTitleResponse
    {
        /// <summary>
        /// Ad title (ad text)
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// Material ID
        /// </summary>
        [JsonPropertyName("material_id")]
        public string? MaterialId { get; set; }

        /// <summary>
        /// The status of the material
        /// </summary>
        [JsonPropertyName("material_operation_status")]
        public string? MaterialOperationStatus { get; set; }
    }

    /// <summary>
    /// Response for Smart Creative material status update operation
    /// </summary>
    public class SmartCreativeMaterialStatusUpdateResponse
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("ad_group_id")]
        public string? AdGroupId { get; set; }

        /// <summary>
        /// Material IDs
        /// </summary>
        [JsonPropertyName("material_ids")]
        public List<string>? MaterialIds { get; set; }

        /// <summary>
        /// Status of the material
        /// </summary>
        [JsonPropertyName("material_status")]
        public string? MaterialStatus { get; set; }
    }
}
