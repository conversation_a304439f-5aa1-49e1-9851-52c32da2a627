using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu chiến dịch (Campaigns)
    /// </summary>
    public class CampaignSyncApplicationAppService : ApplicationService, ICampaignSyncApplicationAppService
    {
        private readonly ICampaignSyncService _campaignSyncService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="campaignSyncService">Campaign sync service</param>
        public CampaignSyncApplicationAppService(ICampaignSyncService campaignSyncService)
        {
            _campaignSyncService = campaignSyncService;
        }

        /// <summary>
        /// Đồng bộ chiến dịch theo Advertiser ID và BC ID với filtering
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="objectiveType"><PERSON><PERSON><PERSON> mục tiêu</param>
        /// <param name="operationStatus">Trạng thái hoạt động</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<CampaignSyncResult> SyncCampaignsAsync(string advertiserId, string bcId, string? objectiveType = null, string? operationStatus = null)
        {
            return await _campaignSyncService.SyncCampaignsAsync(advertiserId, bcId, objectiveType, operationStatus);
        }

        /// <summary>
        /// Đồng bộ nhiều chiến dịch cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds">Danh sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<CampaignSyncResult> SyncManyCampaignsAsync(string bcId, List<string>? advertiserIds = null)
        {
            return await _campaignSyncService.SyncManyCampaignsAsync(bcId, advertiserIds);
        }

        /// <summary>
        /// Đồng bộ tất cả chiến dịch cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<CampaignSyncResult> SyncAllCampaignsAsync()
        {
            return await _campaignSyncService.SyncAllCampaignsAsync();
        }
    }
}