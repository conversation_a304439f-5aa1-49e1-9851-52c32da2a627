/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Change Log operations
    /// </summary>
    public class ChangeLogApi : IChangeLogApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<ChangeLogApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the ChangeLogApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public ChangeLogApi(IApiClient apiClient, ILogger<ChangeLogApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Initiate a change log download task for an ad account based on the parameters that you specify
        /// </summary>
        /// <param name="request">Create change log task request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task ID</returns>
        //public async Task<CreateChangeLogTaskResponse> CreateTaskAsync(
        //    CreateChangeLogTaskRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Creating change log task for advertiser {AdvertiserId}", request.AdvertiserId);

        //    return await _apiClient.CallApiAsync<CreateChangeLogTaskResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ChangeLogEndpoints.CreateTask}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Check whether a change log download task has completed or not
        /// </summary>
        /// <param name="request">Check change log task status request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task status</returns>
        //public async Task<CheckChangeLogTaskResponse> CheckTaskStatusAsync(
        //    CheckChangeLogTaskRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.TaskId))
        //        throw new ArgumentException("Task ID cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Checking status for change log task {TaskId} for advertiser {AdvertiserId}", 
        //        request.TaskId, request.AdvertiserId);

        //    var queryParams = new Dictionary<string, string>
        //    {
        //        ["advertiser_id"] = request.AdvertiserId,
        //        ["task_id"] = request.TaskId
        //    };

        //    return await _apiClient.CallApiAsync<CheckChangeLogTaskResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ChangeLogEndpoints.CheckTaskStatus}",
        //        HttpMethod.Get,
        //        queryParams: queryParams,
        //        body: null,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Download the change log file
        /// </summary>
        /// <param name="request">Download change log task request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing change log file data</returns>
        public async Task<DownloadChangeLogTaskResponse> DownloadTaskAsync(
            DownloadChangeLogTaskRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));
            if (string.IsNullOrWhiteSpace(request.TaskId))
                throw new ArgumentException("Task ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Downloading change log for task {TaskId} for advertiser {AdvertiserId}", 
                request.TaskId, request.AdvertiserId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["task_id"] = request.TaskId
            };

            return await _apiClient.CallApiAsync<DownloadChangeLogTaskResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ChangeLogEndpoints.DownloadTask}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
