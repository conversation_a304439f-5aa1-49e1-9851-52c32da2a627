/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request parameters for getting advertiser attributes (currencies and registration areas)
    /// </summary>
    public class GetAdvertiserAttributeRequest
    {
        /// <summary>
        /// Business Center ID. Required parameter.
        /// To get a list of Business Centers that you have access to, use /bc/get/
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Data model for get advertiser attributes response
    /// </summary>
    public class GetAdvertiserAttributeData
    {
        /// <summary>
        /// Currency codes of ad accounts in the Business Center
        /// Example: ["USD"]
        /// </summary>
        [JsonPropertyName("currencies")]
        public List<string>? Currencies { get; set; }

        /// <summary>
        /// The location codes for the places of registration for ad accounts in the Business Center
        /// Example: ["US", "BR"]
        /// </summary>
        [JsonPropertyName("region_codes")]
        public List<string>? RegionCodes { get; set; }
    }

    /// <summary>
    /// Response for getting advertiser attributes
    /// </summary>
    public class GetAdvertiserAttributeResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public GetAdvertiserAttributeData? Data { get; set; }
    }
}
