using System;
using TikTok.Enums;
using System.Collections.Generic;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// DTO thống kê tổng quan về tài khoản nhà quảng cáo mới nhất
    /// </summary>
    public class LatestBalanceAdAccountStatisticsDto
    {
        /// <summary>
        /// Tổng số tài khoản nhà quảng cáo
        /// </summary>
        public int TotalAccounts { get; set; }

        /// <summary>
        /// Tổng số dư tài khoản
        /// </summary>
        public decimal TotalAccountBalance { get; set; }

        /// <summary>
        /// Tổng số dư tiền mặt
        /// </summary>
        public decimal TotalCashBalance { get; set; }

        /// <summary>
        /// Tổng số dư phiếu giảm giá/voucher
        /// </summary>
        public decimal TotalGrantBalance { get; set; }

        /// <summary>
        /// Tổng ngân sách
        /// </summary>
        public decimal TotalBudget { get; set; }

        /// <summary>
        /// Tổng chi phí ngân sách đã sử dụng
        /// </summary>
        public decimal TotalBudgetCost { get; set; }

        /// <summary>
        /// Tổng ngân sách còn lại
        /// </summary>
        public decimal TotalBudgetRemaining { get; set; }

        /// <summary>
        /// Số tài khoản theo trạng thái
        /// </summary>
        public Dictionary<AdvertiserAccountStatus, int> AccountsByStatus { get; set; }

        /// <summary>
        /// Số tài khoản theo loại
        /// </summary>
        public Dictionary<AdAccountType, int> AccountsByType { get; set; }

        /// <summary>
        /// Số tài khoản theo chế độ ngân sách
        /// </summary>
        public Dictionary<BudgetMode, int> AccountsByBudgetMode { get; set; }

        /// <summary>
        /// Số tài khoản theo tiền tệ
        /// </summary>
        public Dictionary<string, int> AccountsByCurrency { get; set; }

        /// <summary>
        /// Thời gian đồng bộ dữ liệu mới nhất
        /// </summary>
        public DateTime? LastSyncTime { get; set; }

        /// <summary>
        /// Số tài khoản có cảnh báo số dư
        /// </summary>
        public int AccountsWithBalanceReminder { get; set; }

        /// <summary>
        /// Số tài khoản có số dư thấp (dưới 10% ngân sách)
        /// </summary>
        public int AccountsWithLowBalance { get; set; }

        public LatestBalanceAdAccountStatisticsDto()
        {
            AccountsByStatus = new Dictionary<AdvertiserAccountStatus, int>();
            AccountsByType = new Dictionary<AdAccountType, int>();
            AccountsByBudgetMode = new Dictionary<BudgetMode, int>();
            AccountsByCurrency = new Dictionary<string, int>();
        }
    }
}
