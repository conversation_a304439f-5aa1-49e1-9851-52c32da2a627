/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TikTokBusinessApi.Auth
{
    /// <summary>
    /// Factory for creating TikTok authentication instances based on context
    /// </summary>
    public class TikTokAuthenticationFactory : IAuthenticationFactory
    {
        private readonly IAuthenticationResolver _resolver;
        private readonly ILogger<TikTokAuthenticationFactory>? _logger;

        /// <summary>
        /// Initializes a new instance of the TikTokAuthenticationFactory class
        /// </summary>
        /// <param name="resolver">Authentication resolver</param>
        /// <param name="logger">Logger instance</param>
        public TikTokAuthenticationFactory(IAuthenticationResolver resolver, ILogger<TikTokAuthenticationFactory>? logger = null)
        {
            _resolver = resolver ?? throw new ArgumentNullException(nameof(resolver));
            _logger = logger;
        }

        /// <summary>
        /// Creates an authentication instance for the given context
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>Authentication instance for the context</returns>
        public async Task<IAuthentication?> CreateAuthenticationAsync(AuthenticationContext context)
        {
            try
            {
                var credentials = await _resolver.ResolveCredentialsAsync(context);
                if (credentials == null)
                {
                    _logger?.LogWarning("No credentials resolved for context: {UserId}, {AuthType}", 
                        context.UserId ?? "anonymous", context.AuthenticationType);
                    return null;
                }

                if (credentials.IsExpired)
                {
                    _logger?.LogWarning("Credentials expired for context: {UserId}, expires at: {ExpiresAt}", 
                        context.UserId ?? "anonymous", credentials.ExpiresAt);
                    return null;
                }

                var tikTokAuth = new TikTokAuthentication(
                    credentials.AppId,
                    credentials.AccessToken,
                    null); // TODO: Pass proper logger instance

                _logger?.LogDebug("Created TikTok authentication for context: {UserId}", context.UserId ?? "anonymous");
                return tikTokAuth;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to create TikTok authentication for context: {UserId}", context.UserId ?? "anonymous");
                return null;
            }
        }

        /// <summary>
        /// Gets the supported authentication types
        /// </summary>
        /// <returns>List of supported authentication type names</returns>
        public IEnumerable<string> GetSupportedTypes()
        {
            return new[] { "tiktok" };
        }

        /// <summary>
        /// Validates if the factory can create authentication for the given context
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>True if authentication can be created</returns>
        public async Task<bool> CanCreateAuthenticationAsync(AuthenticationContext context)
        {
            if (context.AuthenticationType != "tiktok")
            {
                return false;
            }

            return await _resolver.CanResolveAsync(context);
        }
    }

    /// <summary>
    /// Registry for managing multiple authentication factories
    /// </summary>
    public class AuthenticationFactoryRegistry : IAuthenticationFactoryRegistry
    {
        private readonly Dictionary<string, IAuthenticationFactory> _factories;
        private readonly ILogger<AuthenticationFactoryRegistry>? _logger;

        /// <summary>
        /// Initializes a new instance of the AuthenticationFactoryRegistry class
        /// </summary>
        /// <param name="logger">Logger instance</param>
        public AuthenticationFactoryRegistry(ILogger<AuthenticationFactoryRegistry>? logger = null)
        {
            _factories = new Dictionary<string, IAuthenticationFactory>(StringComparer.OrdinalIgnoreCase);
            _logger = logger;
        }

        /// <summary>
        /// Registers an authentication factory for a specific type
        /// </summary>
        /// <param name="authenticationType">Authentication type name</param>
        /// <param name="factory">Authentication factory</param>
        public void RegisterFactory(string authenticationType, IAuthenticationFactory factory)
        {
            if (string.IsNullOrEmpty(authenticationType))
                throw new ArgumentException("Authentication type cannot be null or empty", nameof(authenticationType));
            
            if (factory == null)
                throw new ArgumentNullException(nameof(factory));

            _factories[authenticationType] = factory;
            _logger?.LogDebug("Registered authentication factory for type: {AuthenticationType}", authenticationType);
        }

        /// <summary>
        /// Gets a factory for the specified authentication type
        /// </summary>
        /// <param name="authenticationType">Authentication type name</param>
        /// <returns>Authentication factory or null if not found</returns>
        public IAuthenticationFactory? GetFactory(string authenticationType)
        {
            if (string.IsNullOrEmpty(authenticationType))
                return null;

            _factories.TryGetValue(authenticationType, out var factory);
            return factory;
        }

        /// <summary>
        /// Gets all registered authentication types
        /// </summary>
        /// <returns>List of registered authentication types</returns>
        public IEnumerable<string> GetRegisteredTypes()
        {
            return _factories.Keys.ToList();
        }

        /// <summary>
        /// Unregisters a factory for the specified authentication type
        /// </summary>
        /// <param name="authenticationType">Authentication type name</param>
        /// <returns>True if factory was removed</returns>
        public bool UnregisterFactory(string authenticationType)
        {
            if (string.IsNullOrEmpty(authenticationType))
                return false;

            var removed = _factories.Remove(authenticationType);
            if (removed)
            {
                _logger?.LogDebug("Unregistered authentication factory for type: {AuthenticationType}", authenticationType);
            }
            return removed;
        }

        /// <summary>
        /// Checks if authentication can be created for the given context
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>True if authentication can be created</returns>
        public async Task<bool> CanCreateAuthenticationAsync(AuthenticationContext context)
        {
            var factory = GetFactory(context.AuthenticationType);
            if (factory == null)
            {
                return false;
            }

            return await factory.CanCreateAuthenticationAsync(context);
        }

        /// <summary>
        /// Creates an authentication instance using the appropriate factory
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>Authentication instance or null if not found</returns>
        public async Task<IAuthentication?> CreateAuthenticationAsync(AuthenticationContext context)
        {
            var factory = GetFactory(context.AuthenticationType);
            if (factory == null)
            {
                _logger?.LogWarning("No factory registered for authentication type: {AuthenticationType}", context.AuthenticationType);
                return null;
            }

            try
            {
                return await factory.CreateAuthenticationAsync(context);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to create authentication for context: {UserId}, {AuthType}",
                    context.UserId ?? "anonymous", context.AuthenticationType);
                return null;
            }
        }
    }

    /// <summary>
    /// Builder for creating TikTok authentication factory with different resolvers
    /// </summary>
    public class TikTokAuthenticationFactoryBuilder
    {
        private IAuthenticationResolver? _resolver;
        private ILogger<TikTokAuthenticationFactory>? _logger;

        /// <summary>
        /// Sets the authentication resolver
        /// </summary>
        /// <param name="resolver">Authentication resolver</param>
        /// <returns>Builder instance</returns>
        public TikTokAuthenticationFactoryBuilder WithResolver(IAuthenticationResolver resolver)
        {
            _resolver = resolver;
            return this;
        }

        /// <summary>
        /// Sets up a dummy resolver with default credentials
        /// </summary>
        /// <param name="defaultAppId">Default application ID</param>
        /// <param name="defaultAccessToken">Default access token</param>
        /// <returns>Builder instance</returns>
        public TikTokAuthenticationFactoryBuilder WithDummyResolver(string defaultAppId, string defaultAccessToken)
        {
            _resolver = new DummyUserIdResolver(defaultAppId, defaultAccessToken);
            return this;
        }

        /// <summary>
        /// Sets up a configuration-based resolver
        /// </summary>
        /// <param name="appId">Application ID from configuration</param>
        /// <param name="accessToken">Access token from configuration</param>
        /// <returns>Builder instance</returns>
        public TikTokAuthenticationFactoryBuilder WithConfigurationResolver(string appId, string accessToken)
        {
            _resolver = new ConfigurationAuthenticationResolver(appId, accessToken);
            return this;
        }

        /// <summary>
        /// Sets the logger
        /// </summary>
        /// <param name="logger">Logger instance</param>
        /// <returns>Builder instance</returns>
        public TikTokAuthenticationFactoryBuilder WithLogger(ILogger<TikTokAuthenticationFactory> logger)
        {
            _logger = logger;
            return this;
        }

        /// <summary>
        /// Builds the TikTok authentication factory
        /// </summary>
        /// <returns>TikTok authentication factory</returns>
        public TikTokAuthenticationFactory Build()
        {
            if (_resolver == null)
            {
                throw new InvalidOperationException("Authentication resolver must be configured");
            }

            return new TikTokAuthenticationFactory(_resolver, _logger);
        }
    }
}
