using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using TikTok.Enums;
using Volo.Abp.DependencyInjection;

namespace TikTok.Customers
{
    public interface ICustomerCache : IScopedDependency
    {
        public Task<CustomerDto?> GetById(Guid id);

        public Task<CustomerDto?> GetByCustomerIdAsync(string customerId);

        public Task<CustomerDto?> GetByAccountNameAsync(string accountName);

        public Task<List<CustomerDto>> GetAllAsync();

        public Task<List<CustomerDto>> GetByCustomerTypeAsync(CustomerType customerType);

        public Task CleanCache();
    }
}
