using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// Application Service interface cho việc đồng bộ dữ liệu GMV Max Campaign
    /// </summary>
    public interface IGmvMaxCampaignSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ GMV Max Campaign theo Advertiser ID và BC ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxCampaignSyncResult> SyncGmvMaxCampaignsAsync(string advertiserId, string bcId);

        /// <summary>
        /// Đồng bộ nhiều GMV Max Campaign cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds"><PERSON><PERSON> sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxCampaignSyncResult> SyncManyGmvMaxCampaignsAsync(string bcId, List<string>? advertiserIds = null);

        /// <summary>
        /// Đồng bộ tất cả GMV Max Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxCampaignSyncResult> SyncAllGmvMaxCampaignsAsync();
    }
} 