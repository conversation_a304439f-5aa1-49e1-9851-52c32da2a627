/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;
using TikTokBusinessApi.Serialization;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Reporting operations
    /// </summary>
    public class ReportingApi : IReportingApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<ReportingApi>? _logger;
        private readonly JsonSerializerOptions _jsonOptions;
        /// <summary>
        /// Initializes a new instance of the ReportingApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public ReportingApi(IApiClient apiClient, ILogger<ReportingApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
            _jsonOptions = JsonSerializerOptionsExtensions.CreateDefault();
        }

        /// <summary>
        /// Run a synchronous report
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Synchronous report request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing report data</returns>
        public async Task<TikTokApiResponse<SynchronousReportResponse>> GetSynchronousReportAsync(
            SynchronousReportRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting synchronous report");

            // For synchronous reports, we use query parameters since it's a GET request
            var queryParams = new Dictionary<string, string>();

            // Add conditional parameters
            if (!string.IsNullOrEmpty(request.AdvertiserId))
                queryParams["advertiser_id"] = request.AdvertiserId;

            if (request.AdvertiserIds?.Count > 0)
                queryParams["advertiser_ids"] = JsonSerializer.Serialize(request.AdvertiserIds, _jsonOptions);

            if (!string.IsNullOrEmpty(request.BcId))
                queryParams["bc_id"] = request.BcId;

            if (!string.IsNullOrEmpty(request.ServiceType))
                queryParams["service_type"] = request.ServiceType;

            // Required parameters
            queryParams["report_type"] = request.ReportType;

            if (!string.IsNullOrEmpty(request.DataLevel))
                queryParams["data_level"] = request.DataLevel;

            if (request.Dimensions?.Count > 0)
                queryParams["dimensions"] = System.Text.Json.JsonSerializer.Serialize(request.Dimensions, _jsonOptions);

            if (request.Metrics?.Count > 0)
                queryParams["metrics"] = System.Text.Json.JsonSerializer.Serialize(request.Metrics, _jsonOptions);

            if (request.EnableTotalMetrics.HasValue)
                queryParams["enable_total_metrics"] = request.EnableTotalMetrics.Value.ToString().ToLower();

            if (!string.IsNullOrEmpty(request.StartDate))
                queryParams["start_date"] = request.StartDate;

            if (!string.IsNullOrEmpty(request.EndDate))
                queryParams["end_date"] = request.EndDate;

            if (request.QueryLifetime.HasValue)
                queryParams["query_lifetime"] = request.QueryLifetime.Value.ToString().ToLower();

            if (request.MultiAdvReportInUtcTime.HasValue)
                queryParams["multi_adv_report_in_utc_time"] = request.MultiAdvReportInUtcTime.Value.ToString().ToLower();

            if (!string.IsNullOrEmpty(request.OrderField))
                queryParams["order_field"] = request.OrderField;

            if (!string.IsNullOrEmpty(request.OrderType))
                queryParams["order_type"] = request.OrderType;

            if (request.Filtering?.Count > 0)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering, _jsonOptions);

            if (!string.IsNullOrEmpty(request.QueryMode))
                queryParams["query_mode"] = request.QueryMode;

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();

            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<TikTokApiResponse<SynchronousReportResponse>>(
                ReportingEndpoints.GetSynchronousReport,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Create an asynchronous report task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Asynchronous report task creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task ID</returns>
        // public async Task<Response<AsynchronousReportTaskCreateResponse>> CreateAsynchronousReportTaskAsync(
        //     string accessToken,
        //     AsynchronousReportTaskCreateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrEmpty(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogDebug("Creating asynchronous report task");

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<AsynchronousReportTaskCreateResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ReportingEndpoints.CreateAsynchronousReportTask}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get the status of an asynchronous report task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="taskId">Task ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task status</returns>
        public async Task<Response<AsynchronousReportTaskStatusResponse>> GetAsynchronousReportTaskStatusAsync(
            string accessToken,
            string advertiserId,
            string taskId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            if (string.IsNullOrEmpty(taskId))
                throw new ArgumentException("Task ID cannot be null or empty", nameof(taskId));

            _logger?.LogDebug("Getting asynchronous report task status for task {TaskId}", taskId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["task_id"] = taskId
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<AsynchronousReportTaskStatusResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ReportingEndpoints.GetAsynchronousReportTaskStatus}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Download the output of an asynchronous report task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="taskId">Task ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing download information</returns>
        public async Task<Response<AsynchronousReportTaskDownloadResponse>> DownloadAsynchronousReportTaskAsync(
            string accessToken,
            string advertiserId,
            string taskId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            if (string.IsNullOrEmpty(taskId))
                throw new ArgumentException("Task ID cannot be null or empty", nameof(taskId));

            _logger?.LogDebug("Downloading asynchronous report task output for task {TaskId}", taskId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["task_id"] = taskId
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<AsynchronousReportTaskDownloadResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ReportingEndpoints.DownloadAsynchronousReportTask}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Cancel an asynchronous report task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Asynchronous report task cancellation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing cancellation status</returns>
        // public async Task<Response<AsynchronousReportTaskCancelResponse>> CancelAsynchronousReportTaskAsync(
        //     string accessToken,
        //     AsynchronousReportTaskCancelRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrEmpty(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogDebug("Canceling asynchronous report task {TaskId}", request.TaskId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<AsynchronousReportTaskCancelResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ReportingEndpoints.CancelAsynchronousReportTask}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }
    }
}
