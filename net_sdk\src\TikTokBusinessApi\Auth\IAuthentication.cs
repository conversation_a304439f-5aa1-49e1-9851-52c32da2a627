/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Net.Http;
using System.Threading.Tasks;

namespace TikTokBusinessApi.Auth
{
    /// <summary>
    /// Interface for authentication mechanisms
    /// </summary>
    public interface IAuthentication
    {
        /// <summary>
        /// Applies authentication to the HTTP request
        /// </summary>
        /// <param name="request">HTTP request message</param>
        void ApplyToRequest(HttpRequestMessage request);
    }

    /// <summary>
    /// Enhanced interface for authentication mechanisms that can modify request body
    /// </summary>
    public interface IBodyAuthentication : IAuthentication
    {
        /// <summary>
        /// Applies authentication by modifying the request body
        /// </summary>
        /// <param name="request">HTTP request message</param>
        /// <param name="requestBody">Current request body object (can be null)</param>
        /// <returns>Modified request body object with authentication parameters</returns>
        Task<object?> ApplyToRequestBodyAsync(HttpRequestMessage request, object? requestBody);

        /// <summary>
        /// Gets authentication parameters that should be added to the request body
        /// </summary>
        /// <returns>Dictionary of authentication parameters</returns>
        Task<System.Collections.Generic.Dictionary<string, object>> GetAuthenticationParametersAsync();
    }

    /// <summary>
    /// Interface for authentication context that provides access tokens
    /// </summary>
    public interface IAuthenticationContext
    {
        /// <summary>
        /// Gets the current access token
        /// </summary>
        /// <returns>Current access token or null if not available</returns>
        Task<string?> GetAccessTokenAsync();

        /// <summary>
        /// Sets the access token for subsequent requests
        /// </summary>
        /// <param name="accessToken">Access token to set</param>
        Task SetAccessTokenAsync(string? accessToken);

        /// <summary>
        /// Gets the application ID
        /// </summary>
        /// <returns>Application ID</returns>
        string? GetAppId();
    }
}
