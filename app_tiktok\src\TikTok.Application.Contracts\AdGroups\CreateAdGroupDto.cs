﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TikTok.AdGroups
{
    /// <summary>
    /// DTO để tạo mới nhóm quảng cáo
    /// </summary>
    public class CreateAdGroupDto
    {
        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID chiến dịch
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CampaignId { get; set; }

        /// <summary>
        /// Tên chiến dịch
        /// </summary>
        [StringLength(255)]
        public string CampaignName { get; set; }

        /// <summary>
        /// Nguồn gốc của chiến dịch mà nhóm quảng cáo thuộc về
        /// </summary>
        [StringLength(50)]
        public string CampaignSystemOrigin { get; set; }

        /// <summary>
        /// <PERSON><PERSON> phải nhóm quảng cáo trong chiến dịch tự động hay không
        /// </summary>
        public bool IsSmartPerformanceCampaign { get; set; }

        /// <summary>
        /// ID nhóm quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdgroupId { get; set; }

        /// <summary>
        /// Tên nhóm quảng cáo
        /// </summary>
        [Required]
        [StringLength(255)]
        public string AdgroupName { get; set; }

        /// <summary>
        /// Thời gian tạo nhóm quảng cáo
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// Thời gian sửa đổi nhóm quảng cáo
        /// </summary>
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// Loại quảng cáo mua sắm
        /// </summary>
        [StringLength(50)]
        public string ShoppingAdsType { get; set; }

        /// <summary>
        /// ID danh tính
        /// </summary>
        [StringLength(100)]
        public string IdentityId { get; set; }

        /// <summary>
        /// Loại danh tính
        /// </summary>
        [StringLength(50)]
        public string IdentityType { get; set; }

        /// <summary>
        /// ID BC được ủy quyền cho danh tính
        /// </summary>
        [StringLength(100)]
        public string IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// Nguồn sản phẩm để lấy sản phẩm quảng bá
        /// </summary>
        [StringLength(50)]
        public string ProductSource { get; set; }

        /// <summary>
        /// ID catalog
        /// </summary>
        [StringLength(100)]
        public string CatalogId { get; set; }

        /// <summary>
        /// ID BC mà catalog thuộc về
        /// </summary>
        [StringLength(100)]
        public string CatalogAuthorizedBcId { get; set; }

        /// <summary>
        /// ID của TikTok Shop
        /// </summary>
        [StringLength(100)]
        public string StoreId { get; set; }

        /// <summary>
        /// ID BC được ủy quyền truy cập store
        /// </summary>
        [StringLength(100)]
        public string StoreAuthorizedBcId { get; set; }

        /// <summary>
        /// Loại khuyến mãi (Vị trí tối ưu hóa)
        /// </summary>
        [StringLength(50)]
        public string PromotionType { get; set; }

        /// <summary>
        /// Loại mục tiêu khuyến mãi cho mục tiêu Lead Generation
        /// </summary>
        [StringLength(50)]
        public string PromotionTargetType { get; set; }

        /// <summary>
        /// Loại ứng dụng nhắn tin tức thời hoặc URL tùy chỉnh
        /// </summary>
        [StringLength(50)]
        public string MessagingAppType { get; set; }

        /// <summary>
        /// ID tài khoản ứng dụng nhắn tin tức thời
        /// </summary>
        [StringLength(100)]
        public string MessagingAppAccountId { get; set; }

        /// <summary>
        /// Mã vùng cho số điện thoại WhatsApp hoặc Zalo
        /// </summary>
        [StringLength(10)]
        public string PhoneRegionCode { get; set; }

        /// <summary>
        /// Mã gọi vùng cho số điện thoại WhatsApp hoặc Zalo
        /// </summary>
        [StringLength(10)]
        public string PhoneRegionCallingCode { get; set; }

        /// <summary>
        /// Số điện thoại WhatsApp hoặc Zalo
        /// </summary>
        [StringLength(20)]
        public string PhoneNumber { get; set; }

        /// <summary>
        /// Loại TikTok Instant Page trong nhóm quảng cáo
        /// </summary>
        [StringLength(50)]
        public string PromotionWebsiteType { get; set; }

        /// <summary>
        /// ID ứng dụng được quảng bá
        /// </summary>
        [StringLength(100)]
        public string AppId { get; set; }

        /// <summary>
        /// Loại ứng dụng được quảng bá
        /// </summary>
        [StringLength(50)]
        public string AppType { get; set; }

        /// <summary>
        /// Link tải ứng dụng
        /// </summary>
        [StringLength(500)]
        public string AppDownloadUrl { get; set; }

        /// <summary>
        /// ID Pixel (chỉ áp dụng cho landing page)
        /// </summary>
        [StringLength(100)]
        public string PixelId { get; set; }

        /// <summary>
        /// Sự kiện chuyển đổi cho nhóm quảng cáo
        /// </summary>
        [StringLength(100)]
        public string OptimizationEvent { get; set; }

        /// <summary>
        /// Trạng thái tối ưu hóa deep funnel
        /// </summary>
        [StringLength(50)]
        public string DeepFunnelOptimizationStatus { get; set; }

        /// <summary>
        /// Loại nguồn sự kiện
        /// </summary>
        [StringLength(50)]
        public string DeepFunnelEventSource { get; set; }

        /// <summary>
        /// ID nguồn sự kiện
        /// </summary>
        [StringLength(100)]
        public string DeepFunnelEventSourceId { get; set; }

        /// <summary>
        /// Sự kiện tối ưu hóa deep funnel
        /// </summary>
        [StringLength(100)]
        public string DeepFunnelOptimizationEvent { get; set; }

        /// <summary>
        /// Chiến lược vị trí quyết định nơi hiển thị quảng cáo
        /// </summary>
        [StringLength(50)]
        public string PlacementType { get; set; }

        /// <summary>
        /// Các ứng dụng nơi bạn muốn phân phối quảng cáo
        /// </summary>
        public List<string> Placements { get; set; }

        /// <summary>
        /// Các vị trí phụ trong TikTok cho quảng cáo
        /// </summary>
        public List<string> TiktokSubplacements { get; set; }

        /// <summary>
        /// Có bao gồm quảng cáo trong Search Ads hay không
        /// </summary>
        public bool SearchResultEnabled { get; set; }

        /// <summary>
        /// Có bật từ khóa tự động hay không
        /// </summary>
        public bool AutomatedKeywordsEnabled { get; set; }

        /// <summary>
        /// Có cho phép bình luận trên quảng cáo TikTok hay không
        /// </summary>
        public bool CommentDisabled { get; set; }

        /// <summary>
        /// Người dùng có thể tải video quảng cáo trên TikTok hay không
        /// </summary>
        public bool VideoDownloadDisabled { get; set; }

        /// <summary>
        /// Có tắt chia sẻ lên nền tảng bên thứ ba hay không
        /// </summary>
        public bool ShareDisabled { get; set; }

        /// <summary>
        /// Danh sách ID ứng dụng Pangle bị chặn
        /// </summary>
        public List<string> BlockedPangleAppIds { get; set; }

        /// <summary>
        /// Loại đối tượng retargeting ứng dụng
        /// </summary>
        [StringLength(50)]
        public string AudienceType { get; set; }

        /// <summary>
        /// Có bật tự động targeting hay không (deprecated)
        /// </summary>
        public bool AutoTargetingEnabled { get; set; }

        /// <summary>
        /// Loại retargeting của quảng cáo mua sắm
        /// </summary>
        [StringLength(50)]
        public string ShoppingAdsRetargetingType { get; set; }

        /// <summary>
        /// Thời gian hiệu lực cho hành động đối tượng chỉ định
        /// </summary>
        public int? ShoppingAdsRetargetingActionsDays { get; set; }

        /// <summary>
        /// Mối quan hệ logic giữa đối tượng retargeting VSA và đối tượng tùy chỉnh
        /// </summary>
        [StringLength(50)]
        public string ShoppingAdsRetargetingCustomAudienceRelation { get; set; }

        /// <summary>
        /// ID các vị trí được nhắm mục tiêu
        /// </summary>
        public List<string> LocationIds { get; set; }

        /// <summary>
        /// ID mã ZIP hoặc mã bưu điện của các vị trí được nhắm mục tiêu
        /// </summary>
        public List<string> ZipcodeIds { get; set; }

        /// <summary>
        /// Mã ngôn ngữ bạn muốn nhắm mục tiêu
        /// </summary>
        public List<string> Languages { get; set; }

        /// <summary>
        /// Giới tính bạn muốn nhắm mục tiêu
        /// </summary>
        [StringLength(20)]
        public string Gender { get; set; }

        /// <summary>
        /// Nhóm tuổi bạn muốn nhắm mục tiêu
        /// </summary>
        public List<string> AgeGroups { get; set; }

        /// <summary>
        /// Sức mua bạn muốn nhắm mục tiêu
        /// </summary>
        [StringLength(50)]
        public string SpendingPower { get; set; }

        /// <summary>
        /// Thu nhập hộ gia đình bạn muốn nhắm mục tiêu
        /// </summary>
        public List<string> HouseholdIncome { get; set; }

        /// <summary>
        /// Danh sách ID đối tượng
        /// </summary>
        public List<string> AudienceIds { get; set; }

        /// <summary>
        /// Có bật Smart audience hay không
        /// </summary>
        public bool SmartAudienceEnabled { get; set; }

        /// <summary>
        /// Danh sách ID đối tượng loại trừ
        /// </summary>
        public List<string> ExcludedAudienceIds { get; set; }

        /// <summary>
        /// ID từ khóa sở thích chung để nhắm mục tiêu đối tượng
        /// </summary>
        public List<string> InterestCategoryIds { get; set; }

        /// <summary>
        /// ID từ khóa sở thích bổ sung để nhắm mục tiêu đối tượng
        /// </summary>
        public List<string> InterestKeywordIds { get; set; }

        /// <summary>
        /// ID danh mục ý định mua hàng
        /// </summary>
        public List<string> PurchaseIntentionKeywordIds { get; set; }

        /// <summary>
        /// Có bật Smart interests & behaviors hay không
        /// </summary>
        public bool SmartInterestBehaviorEnabled { get; set; }

        /// <summary>
        /// Chế độ ngân sách
        /// </summary>
        [Required]
        [StringLength(50)]
        public string BudgetMode { get; set; }

        /// <summary>
        /// Ngân sách nhóm quảng cáo
        /// </summary>
        public decimal Budget { get; set; }

        /// <summary>
        /// Ngân sách quảng cáo được lên lịch cho ngày hôm sau
        /// </summary>
        public decimal? ScheduledBudget { get; set; }

        /// <summary>
        /// Loại lịch trình
        /// </summary>
        [StringLength(50)]
        public string ScheduleType { get; set; }

        /// <summary>
        /// Thời gian bắt đầu phân phối quảng cáo
        /// </summary>
        public DateTime? ScheduleStartTime { get; set; }

        /// <summary>
        /// Thời gian kết thúc phân phối quảng cáo
        /// </summary>
        public DateTime? ScheduleEndTime { get; set; }

        /// <summary>
        /// Loại phân phối cho ngày
        /// </summary>
        [StringLength(1000)]
        public string Dayparting { get; set; }

        /// <summary>
        /// Mục tiêu tối ưu hóa
        /// </summary>
        [StringLength(100)]
        public string OptimizationGoal { get; set; }

        /// <summary>
        /// Mục tiêu thứ cấp khi optimization_goal là INSTALL hoặc VALUE
        /// </summary>
        [StringLength(100)]
        public string SecondaryOptimizationEvent { get; set; }

        /// <summary>
        /// Tần suất
        /// </summary>
        public int? Frequency { get; set; }

        /// <summary>
        /// Lịch trình tần suất
        /// </summary>
        public int? FrequencySchedule { get; set; }

        /// <summary>
        /// Chiến lược đặt giá thầu
        /// </summary>
        [StringLength(50)]
        public string BidType { get; set; }

        /// <summary>
        /// Giá thầu
        /// </summary>
        public decimal? BidPrice { get; set; }

        /// <summary>
        /// Chi phí mục tiêu cho mỗi chuyển đổi cho oCPM
        /// </summary>
        public decimal? ConversionBidPrice { get; set; }

        /// <summary>
        /// Chiến lược đặt giá thầu cho sự kiện trong ứng dụng
        /// </summary>
        [StringLength(50)]
        public string DeepBidType { get; set; }

        /// <summary>
        /// Mục tiêu ROAS cho Tối ưu hóa giá trị
        /// </summary>
        public decimal? RoasBid { get; set; }

        /// <summary>
        /// Trạng thái hoạt động
        /// </summary>
        [Required]
        [StringLength(20)]
        public string OperationStatus { get; set; }

        /// <summary>
        /// Trạng thái nhóm quảng cáo (trạng thái thứ cấp)
        /// </summary>
        [StringLength(100)]
        public string SecondaryStatus { get; set; }

        /// <summary>
        /// Liệu chiến dịch có cấu trúc mới hay không
        /// </summary>
        public bool IsNewStructure { get; set; }

        /// <summary>
        /// Có bỏ qua giai đoạn học hay không
        /// </summary>
        public bool SkipLearningPhase { get; set; }

        /// <summary>
        /// Constructor mặc định
        /// </summary>
        public CreateAdGroupDto()
        {
            Placements = new List<string>();
            TiktokSubplacements = new List<string>();
            BlockedPangleAppIds = new List<string>();
            LocationIds = new List<string>();
            ZipcodeIds = new List<string>();
            Languages = new List<string>();
            AgeGroups = new List<string>();
            HouseholdIncome = new List<string>();
            AudienceIds = new List<string>();
            ExcludedAudienceIds = new List<string>();
            InterestCategoryIds = new List<string>();
            InterestKeywordIds = new List<string>();
            PurchaseIntentionKeywordIds = new List<string>();
        }
    }
}