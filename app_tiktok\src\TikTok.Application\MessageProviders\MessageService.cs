using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using TikTok.Application.Contracts.MessageProviders;

namespace TikTok.Application.MessageProviders
{
    public class MessageService : IMessageService
    {
        private readonly IEnumerable<IMessageProvider> _providers;
        private readonly ILogger<MessageService> _logger;
        private readonly IHostEnvironment _environment;
        private readonly MessageProviderOptions _options;

        public MessageService(
            IEnumerable<IMessageProvider> providers,
            ILogger<MessageService> logger,
            IHostEnvironment environment,
            IOptions<MessageProviderOptions> options)
        {
            _providers = providers;
            _logger = logger;
            _environment = environment;
            _options = options.Value;
        }

        public async Task SendMessageAsync(string message)
        {
            // Gửi đến tất cả các provider được enable
            var enabledProviders = _providers.Where(p => p.IsEnabled).ToList();
            if (!enabledProviders.Any())
            {
                _logger.LogWarning("No enabled message providers found");
                return;
            }

            var tasks = enabledProviders.Select(provider =>
                SendMessageAsync(message, provider.Name));

            await Task.WhenAll(tasks);

        }

        public async Task SendMessageAsync(string message, string providerName)
        {
            var provider = _providers.FirstOrDefault(p =>
                p.Name.Equals(providerName, StringComparison.OrdinalIgnoreCase));

            if (provider == null)
            {
                _logger.LogError("Message provider '{ProviderName}' not found", providerName);
                return;
            }

            if (!provider.IsEnabled)
            {
                _logger.LogWarning("Message provider '{ProviderName}' is disabled", providerName);
                return;
            }

            try
            {
                await provider.SendMessageAsync(message);
                _logger.LogDebug("Message sent successfully via {ProviderName}", providerName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send message via {ProviderName}", providerName);
            }
        }

        public Task SendMessageWithTemplateAsync(string message)
        {
            // Get environment information
            string environmentName = _environment.EnvironmentName;
            string applicationName = _options.ApplicationName;
            string hostName = Environment.MachineName;
            string ipAddress = GetServerIpAddress();

            var messageTemplate = $"🚨 MESSAGE ALERT 🚨\n\n" +
                             $"🌐 Environment: {environmentName}\n" +
                             $"📱 Application: {applicationName}\n" +
                             $"🖥️ Server: {hostName} ({ipAddress})\n\n" +
                             $"💬 Message: {message}\n" +
                             $"⏰ Time: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC";
            return SendMessageAsync(messageTemplate);
        }

        // Helper method to get server IP address
        private string GetServerIpAddress()
        {
            try
            {
                string hostName = System.Net.Dns.GetHostName();
                var hostEntry = System.Net.Dns.GetHostEntry(hostName);

                // Try to get IPv4 address first
                foreach (var ip in hostEntry.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return ip.ToString();
                    }
                }

                // If no IPv4 is found, return the first available IP
                if (hostEntry.AddressList.Length > 0)
                {
                    return hostEntry.AddressList[0].ToString();
                }

                return "Unknown";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get server IP address");
                return "Unknown";
            }
        }
    }
}