using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu giao dịch
    ///
    /// Lưu ý về xử lý thời gian:
    /// - CreateTime từ API sẽ được convert sang UTC trước khi lưu vào database
    /// - Timezone lưu trữ múi giờ gốc để có thể convert ngược lại từ UTC khi cần thiết
    /// - <PERSON>hi lọc dữ liệu, CreateTime UTC sẽ được convert về timezone gốc để so sánh
    /// </summary>
    public class TransactionSyncService : BaseSyncService, ITransactionSyncService
    {
        private readonly ITransactionRepository _transactionRepository;
        private readonly IRepository<RawTransactionEntity, Guid> _transactionEntityRepository;

        private const int MAX_PAGE_SIZE = 50;

        public TransactionSyncService(
            IServiceProvider serviceProvider,
            ITransactionRepository transactionRepository,
            IRepository<RawTransactionEntity, Guid> transactionEntityRepository,
            ILogger<TransactionSyncService> logger) : base(serviceProvider, logger)
        {
            _transactionRepository = transactionRepository;
            _transactionEntityRepository = transactionEntityRepository;
        }

        /// <summary>
        /// Đồng bộ dữ liệu giao dịch theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<TransactionSyncResult> SyncTransactionsAsync(string bcId)
        {
            var result = new TransactionSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ dữ liệu giao dịch cho BC: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // 3. Đồng bộ từng loại giao dịch
                var bcResult = await SyncBcTransactionsAsync(tikTokClient, bcId);
                var adAccountResult = await SyncAdAccountTransactionsAsync(tikTokClient, bcId);
                var accountResult = await SyncPaymentPortfolioTransactionsAsync(tikTokClient, bcId);

                // 4. Tổng hợp kết quả
                result.TotalSynced = bcResult.TotalSynced + adAccountResult.TotalSynced + accountResult.TotalSynced;
                result.NewRecords = bcResult.NewRecords + adAccountResult.NewRecords + accountResult.NewRecords;
                result.UpdatedRecords = bcResult.UpdatedRecords + adAccountResult.UpdatedRecords + accountResult.UpdatedRecords;
                
                // Tổng hợp danh sách transaction IDs mới
                result.NewTransactionIds.AddRange(bcResult.NewTransactionIds);
                result.NewTransactionIds.AddRange(adAccountResult.NewTransactionIds);
                result.NewTransactionIds.AddRange(accountResult.NewTransactionIds);

                _logger.LogDebug("Hoàn thành đồng bộ dữ liệu giao dịch cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, NewTransactionIds: {NewTransactionIdsCount}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.NewTransactionIds.Count);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ dữ liệu giao dịch cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ dữ liệu giao dịch: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ dữ liệu giao dịch cho BC: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ giao dịch BC (Business Center)
        /// </summary>
        private async Task<TransactionSyncResult> SyncBcTransactionsAsync(TikTokBusinessApiClient client, string bcId)
        {
            var result = new TransactionSyncResult();

            // Lấy ngày tạo giao dịch mới nhất theo loại BC
            var latestBcTransaction = await GetLatestTransactionByLevelAsync(bcId, TransactionLevel.BC);
            int page = 1;
            bool hasMoreData = true;
            TransactionFiltering? filtering = BuildFiltering(latestBcTransaction);

            _logger.LogDebug("Đồng bộ giao dịch BC cho BC: {BcId}, từ ngày: {StartDate}", bcId, latestBcTransaction?.CreateTime);

            while (hasMoreData)
            {
                var request = new GetBcAccountTransactionRequest()
                {
                    BcId = bcId,
                    Filtering = filtering,
                    TransactionLevel = TransactionLevel.BC.ToString(),
                    Page = page,
                    PageSize = MAX_PAGE_SIZE
                };

                var response = await client.BcPayments.GetBcAccountTransactionAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    result.ErrorMessage = $"Lỗi khi lấy giao dịch BC: {response.Message}";
                    _logger.LogError("Lỗi khi lấy giao dịch BC cho BC: {BcId}, Mã lỗi: {Code}, Thông báo: {Message}",
                        bcId, response.Code, response.Message);
                    throw new BusinessException(response.Code.ToString(), response.Message);
                }
                if (response?.Data?.TransactionList == null || !response.Data.TransactionList.Any())
                {
                    hasMoreData = false;
                    break;
                }

                var syncResult = await ProcessTransactionListAsync(response.Data.TransactionList, TransactionLevel.BC, bcId, page);
                result.TotalSynced += syncResult.TotalSynced;
                result.NewRecords += syncResult.NewRecords;
                result.UpdatedRecords += syncResult.UpdatedRecords;
                result.NewTransactionIds.AddRange(syncResult.NewTransactionIds);

                // Kiểm tra xem còn dữ liệu không
                if (response.Data.TransactionList.Count < MAX_PAGE_SIZE)
                {
                    hasMoreData = false;
                }
                else
                {
                    page++;
                }
            }

            _logger.LogDebug("Hoàn thành đồng bộ giao dịch BC cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, NewTransactionIds: {NewTransactionIdsCount}",
                bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.NewTransactionIds.Count);
            return result;
        }

        private TransactionFiltering? BuildFiltering(RawTransactionEntity? latestBcTransaction)
        {
            TransactionFiltering? filtering = null;
            if (latestBcTransaction != null)
            {
                var currentDate = _dateTimeService.GetDateTimeNow();
                var startDate = latestBcTransaction.CreateTime.AddDays(-2);
                var endDate = currentDate.AddDays(2);
                // check if range is valid (valid range is 365 days), if over 365 days, set startDate to 365 days ago from endDate
                if (endDate - startDate > TimeSpan.FromDays(365))
                {
                    startDate = endDate.AddDays(-365);
                }
                filtering = new TransactionFiltering
                {
                    StartTime = startDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    EndTime = endDate.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }

            return filtering;
        }

        /// <summary>
        /// Đồng bộ giao dịch Ad Account (Advertiser)
        /// </summary>
        private async Task<TransactionSyncResult> SyncAdAccountTransactionsAsync(TikTokBusinessApiClient client, string bcId)
        {
            var result = new TransactionSyncResult();
            // Lấy ngày tạo giao dịch mới nhất theo loại AD_ACCOUNT
            var latestAdAccountTransaction = await GetLatestTransactionByLevelAsync(bcId, TransactionLevel.ADVERTISER);

            _logger.LogDebug("Đồng bộ giao dịch Ad Account cho BC: {BcId}, từ ngày: {StartDate}", bcId, latestAdAccountTransaction?.CreateTime);

            int page = 1;
            bool hasMoreData = true;

            TransactionFiltering? filtering = BuildFiltering(latestAdAccountTransaction);

            while (hasMoreData)
            {
                var request = new GetBcAccountTransactionRequest()
                {
                    BcId = bcId,
                    Filtering = filtering,
                    TransactionLevel = TransactionLevel.ADVERTISER.ToString(),
                    Page = page,
                    PageSize = MAX_PAGE_SIZE
                };

                var response = await client.BcPayments.GetBcAccountTransactionAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    result.ErrorMessage = $"Lỗi khi lấy giao dịch Ad Account: {response.Message}";
                    _logger.LogError("Lỗi khi lấy giao dịch Ad Account cho BC: {BcId}, Mã lỗi: {Code}, Thông báo: {Message}",
                        bcId, response.Code, response.Message);
                    throw new BusinessException(response.Code.ToString(), response.Message);
                }

                if (response?.Data?.TransactionList == null || !response.Data.TransactionList.Any())
                {
                    hasMoreData = false;
                    break;
                }

                var syncResult = await ProcessTransactionListAsync(response.Data.TransactionList, TransactionLevel.ADVERTISER, bcId, page);
                result.TotalSynced += syncResult.TotalSynced;
                result.NewRecords += syncResult.NewRecords;
                result.UpdatedRecords += syncResult.UpdatedRecords;
                result.NewTransactionIds.AddRange(syncResult.NewTransactionIds);

                // Kiểm tra xem còn dữ liệu không
                if (response.Data.TransactionList.Count < MAX_PAGE_SIZE)
                {
                    hasMoreData = false;
                }
                else
                {
                    page++;
                }
            }

            _logger.LogDebug("Hoàn thành đồng bộ giao dịch Ad Account cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, NewTransactionIds: {NewTransactionIdsCount}",
                bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.NewTransactionIds.Count);
            return result;
        }

        /// <summary>
        /// Đồng bộ giao dịch Account (Payment Portfolio)
        /// </summary>
        private async Task<TransactionSyncResult> SyncPaymentPortfolioTransactionsAsync(TikTokBusinessApiClient client, string bcId)
        {
            var result = new TransactionSyncResult();

            // Lấy ngày tạo giao dịch mới nhất theo loại PAYMENT_PORTFOLIO
            var latestAccountTransaction = await GetLatestTransactionByLevelAsync(bcId, TransactionLevel.PAYMENT_PORTFOLIO);
            _logger.LogDebug("Đồng bộ giao dịch Payment Portfolio cho BC: {BcId}, từ ngày: {StartDate}", bcId, latestAccountTransaction?.CreateTime);
            int page = 1;
            bool hasMoreData = true;

            TransactionFiltering? filtering = BuildFiltering(latestAccountTransaction);

            while (hasMoreData)
            {
                var request = new GetBcAccountTransactionRequest()
                {
                    BcId = bcId,
                    TransactionLevel = TransactionLevel.PAYMENT_PORTFOLIO.ToString(),
                    Filtering = filtering,
                    Page = page,
                    PageSize = MAX_PAGE_SIZE
                };

                var response = await client.BcPayments.GetBcAccountTransactionAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy giao dịch Payment Portfolio: {response.Message}");
                }

                if (response?.Data?.TransactionList == null || !response.Data.TransactionList.Any())
                {
                    hasMoreData = false;
                    break;
                }

                var syncResult = await ProcessTransactionListAsync(response.Data.TransactionList, TransactionLevel.PAYMENT_PORTFOLIO, bcId, page);
                result.TotalSynced += syncResult.TotalSynced;
                result.NewRecords += syncResult.NewRecords;
                result.UpdatedRecords += syncResult.UpdatedRecords;
                result.NewTransactionIds.AddRange(syncResult.NewTransactionIds);

                // Kiểm tra xem còn dữ liệu không
                if (response.Data.TransactionList.Count < MAX_PAGE_SIZE)
                {
                    hasMoreData = false;
                }
                else
                {
                    page++;
                }
            }

            _logger.LogDebug("Hoàn thành đồng bộ giao dịch Payment Portfolio cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, NewTransactionIds: {NewTransactionIdsCount}",
                bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.NewTransactionIds.Count);
            return result;
        }

        /// <summary>
        /// Xử lý danh sách giao dịch từ API
        /// </summary>
        private async Task<TransactionSyncResult> ProcessTransactionListAsync(
            List<TransactionRecord> transactionList,
            TransactionLevel transactionLevel,
            string bcId,
            int page)
        {
            // log input
            _logger.LogDebug("Xử lý danh sách giao dịch từ API: {TransactionLevel}, BC: {BcId}, Trang: {Page}, Số lượng: {Count}",
                transactionLevel, bcId, page, transactionList.Count);
            var result = new TransactionSyncResult();

            // use unit of work
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var newTransactions = new List<RawTransactionEntity>();
                var updatedTransactions = new List<RawTransactionEntity>();

                var newTransactionEntities = MapApiTransactionToEntities(transactionList, transactionLevel, bcId);
                var transactionIds = newTransactionEntities.Select(x => x.TransactionId).ToList();
                var existingTransactions = await _transactionRepository.GetByManyTransactionIdsAsync(transactionIds);

                foreach (var newTransaction in newTransactionEntities)
                {
                    try
                    {
                        // Kiểm tra xem giao dịch đã tồn tại chưa
                        var existingTransaction = existingTransactions.FirstOrDefault(x => x.TransactionId == newTransaction.TransactionId);

                        if (existingTransaction == null)
                        {
                            // Tạo mới giao dịch
                            newTransactions.Add(newTransaction);
                            result.NewRecords++;
                            result.NewTransactionIds.Add(newTransaction.TransactionId);
                        }
                        else
                        {
                            // Cập nhật giao dịch hiện có
                            if (existingTransaction.HasIsChange(newTransaction))
                            {
                                UpdateTransactionEntity(existingTransaction, newTransaction);
                                updatedTransactions.Add(existingTransaction);
                                result.UpdatedRecords++;
                            }
                        }

                        result.TotalSynced++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý giao dịch {TransactionId}", newTransaction.TransactionId);
                    }
                }

                // Thêm mới và cập nhật
                if (newTransactions.Any())
                {
                    await _transactionEntityRepository.InsertManyAsync(newTransactions);
                }
                if (updatedTransactions.Any())
                {
                    await _transactionEntityRepository.UpdateManyAsync(updatedTransactions);
                }
                await uow.CompleteAsync();
            }

            // log result
            _logger.LogDebug("Hoàn thành xử lý danh sách giao dịch từ API: {TransactionLevel}, BC: {BcId}, Trang: {Page}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, NewTransactionIds: {NewTransactionIdsCount}",
                transactionLevel, bcId, page, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.NewTransactionIds.Count);

            return result;
        }

        /// <summary>
        /// Lấy giao dịch mới nhất theo cấp độ
        /// </summary>
        private async Task<RawTransactionEntity?> GetLatestTransactionByLevelAsync(string bcId, TransactionLevel level)
        {
            var transactions = await _transactionRepository.GetListAsync(
                bcId: bcId,
                transactionLevel: level,
                sorting: "CreateTime desc",
                maxResultCount: 1);

            return transactions.FirstOrDefault();
        }

        /// <summary>
        /// Xác định ngày bắt đầu để đồng bộ
        /// </summary>
        private DateTime GetStartDateForSync(DateTime? latestCreateTime)
        {
            if (!latestCreateTime.HasValue)
            {
                // Nếu chưa có giao dịch nào, lấy từ 30 ngày trước
                return DateTime.UtcNow.AddDays(-30);
            }

            var today = DateTime.UtcNow.Date;
            var latestDate = latestCreateTime.Value.Date; // CreateTime đã là UTC

            if (latestDate == today)
            {
                // Nếu giao dịch mới nhất là hôm nay, chỉ lấy giao dịch trong ngày hôm nay
                return today;
            }
            else
            {
                // Nếu giao dịch mới nhất khác ngày hôm nay, lấy từ ngày đó đến hiện tại
                return latestDate;
            }
        }

        /// <summary>
        /// Map dữ liệu từ API sang Entity
        /// </summary>
        private RawTransactionEntity MapApiTransactionToEntity(TransactionRecord apiTransaction, TransactionLevel level, string bcId)
        {
            // Parse và convert CreateTime sang UTC
            var originalTimezone = apiTransaction.Timezone ?? DateTimeService.UTC_TIMEZONE;
            var originalCreateTime = ParseDateTime(apiTransaction.CreateTime);
            var utcCreateTime = _dateTimeService.ConvertToUtc(originalCreateTime, originalTimezone);

            return new RawTransactionEntity(Guid.NewGuid())
            {
                TransactionId = apiTransaction.TransactionId,
                PaymentPortfolioId = apiTransaction.PaymentPortfolioId,
                PaymentPortfolioName = apiTransaction.PaymentPortfolioName,
                AccountId = apiTransaction.AccountId,
                AccountName = apiTransaction.AccountName,
                BcId = bcId,
                BcName = apiTransaction.BcName ?? "Unknown BC",
                Amount = apiTransaction.Amount ?? 0,
                Subtotal = apiTransaction.Subtotal ?? 0,
                TaxAmount = apiTransaction.TaxAmount ?? 0,
                Currency = apiTransaction.Currency,
                AmountType = Enum.TryParse(apiTransaction.AmountType, out AmountType amountType) ? amountType : AmountType.OTHER,
                TransactionType = Enum.TryParse(apiTransaction.TransactionType, out TransactionType transactionType) ? transactionType : TransactionType.BILL_PAYMENT,
                BillingType = Enum.TryParse(apiTransaction.BillingType, out BillingType billingType) ? billingType : BillingType.CASH,
                Timezone = originalTimezone, // Lưu timezone gốc để tham chiếu
                CreateTime = utcCreateTime, // Lưu thời gian đã convert sang UTC
                InvoiceId = apiTransaction.InvoiceId,
                SerialNumber = apiTransaction.SerialNumber,
                TransactionLevel = level
            };
        }

        private List<RawTransactionEntity> MapApiTransactionToEntities(List<TransactionRecord> apiTransactions, TransactionLevel level, string bcId)
        {
            return apiTransactions.Select(x => MapApiTransactionToEntity(x, level, bcId)).ToList();
        }

        /// <summary>
        /// Cập nhật entity từ dữ liệu API
        /// </summary>
        private void UpdateTransactionEntity(RawTransactionEntity entity, RawTransactionEntity newTransaction)
        {
            entity.PaymentPortfolioId = newTransaction.PaymentPortfolioId;
            entity.PaymentPortfolioName = newTransaction.PaymentPortfolioName;
            entity.AccountId = newTransaction.AccountId;
            entity.AccountName = newTransaction.AccountName;
            entity.BcName = newTransaction.BcName;
            entity.Amount = newTransaction.Amount;
            entity.Subtotal = newTransaction.Subtotal;
            entity.TaxAmount = newTransaction.TaxAmount;
            entity.Currency = newTransaction.Currency;
            entity.AmountType = newTransaction.AmountType;
            entity.TransactionType = newTransaction.TransactionType;
            entity.BillingType = newTransaction.BillingType;
            entity.Timezone = newTransaction.Timezone;
            entity.CreateTime = newTransaction.CreateTime;
            entity.InvoiceId = newTransaction.InvoiceId;
            entity.SerialNumber = newTransaction.SerialNumber;
            entity.TransactionLevel = newTransaction.TransactionLevel;
        }

        /// <summary>
        /// Parse DateTime từ string
        /// </summary>
        private DateTime ParseDateTime(string? dateTimeString)
        {
            if (string.IsNullOrEmpty(dateTimeString))
                return DateTime.UtcNow;

            if (DateTime.TryParse(dateTimeString, out var result))
                return result;

            return DateTime.UtcNow;
        }

        /// <summary>
        /// Đồng bộ dữ liệu giao dịch cho tất cả Business Applications
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<TransactionSyncResult> SyncAllTransactionsAsync()
        {
            var result = new TransactionSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ dữ liệu giao dịch cho tất cả Business Applications");

                // Lấy tất cả Business Applications active
                var allBusinessApplications = await _businessApplicationCache.GetAllActiveAsync();
                var bcGroups = allBusinessApplications.GroupBy(x => x.BcId);

                if (!bcGroups.Any())
                {
                    result.ErrorMessage = "Không tìm thấy Business Application nào đang active";
                    _logger.LogWarning(result.ErrorMessage);
                    return result;
                }

                // Đồng bộ từng Business Center
                foreach (var bcGroup in bcGroups)
                {
                    var bcId = bcGroup.Key;
                    var activeApplication = bcGroup.FirstOrDefault(x => x.IsActive);

                    if (activeApplication == null || string.IsNullOrEmpty(activeApplication.AccessToken))
                    {
                        _logger.LogWarning("Bỏ qua BC {BcId} - không có ứng dụng active hoặc access token", bcId);
                        continue;
                    }

                    try
                    {
                        _logger.LogDebug("Đồng bộ dữ liệu cho BC: {BcId}", bcId);

                        var bcResult = await SyncTransactionsAsync(bcId);

                        result.TotalSynced += bcResult.TotalSynced;
                        result.NewRecords += bcResult.NewRecords;
                        result.UpdatedRecords += bcResult.UpdatedRecords;
                        result.NewTransactionIds.AddRange(bcResult.NewTransactionIds);

                        if (!bcResult.IsSuccess)
                        {
                            _logger.LogWarning("Lỗi khi đồng bộ BC {BcId}: {ErrorMessage}",
                                bcId, bcResult.ErrorMessage);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ BC {BcId}", bcId);
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ dữ liệu giao dịch cho tất cả Business Applications. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, NewTransactionIds: {NewTransactionIdsCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.NewTransactionIds.Count);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ dữ liệu giao dịch cho tất cả Business Applications: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ dữ liệu giao dịch cho tất cả Business Applications");
            }

            return result;
        }
    }
}