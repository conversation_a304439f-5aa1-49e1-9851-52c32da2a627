using System;
using System.ComponentModel.DataAnnotations;

namespace TikTok.ResourcePermissions
{
    /// <summary>
    /// DTO để cập nhật quyền tài nguyên
    /// </summary>
    public class UpdateResourcePermissionDto
    {
        /// <summary>
        /// ID của người dùng
        /// </summary>
        [Required]
        public Guid UserId { get; set; }

        /// <summary>
        /// ID của tài nguyên
        /// </summary>
        [Required]
        [StringLength(255)]
        public string ResourceId { get; set; }

        /// <summary>
        /// Quyền được cấp
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Permission { get; set; }

        /// <summary>
        /// Loại tài nguyên TikTok
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ResourceType { get; set; }
    }
}
