using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu từ Raw sang Fact
    /// </summary>
    public class SyncRawToFactBusinessCenterApplicationAppService : ApplicationService, ISyncRawToFactBusinessCenterApplicationAppService
    {
        private readonly ISyncRawToFactBusinessCenterService _syncRawToFactBusinessCenterService;

        public SyncRawToFactBusinessCenterApplicationAppService(ISyncRawToFactBusinessCenterService syncRawToFactBusinessCenterService)
        {
            _syncRawToFactBusinessCenterService = syncRawToFactBusinessCenterService;
        }

        /// <summary>
        /// Đồng bộ Business Centers từ Raw sang Fact
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BusinessCenterSyncResult> SyncBusinessCentersAsync()
        {
            return await _syncRawToFactBusinessCenterService.SyncBusinessCentersAsync();
        }

        /// <summary>
        /// Đồng bộ Ad Accounts từ Raw sang Fact
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BusinessCenterSyncResult> SyncAdAccountsAsync()
        {
            return await _syncRawToFactBusinessCenterService.SyncAdAccountsAsync();
        }

        /// <summary>
        /// Đồng bộ tất cả dữ liệu từ Raw sang Fact
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BusinessCenterSyncResult> SyncAllAsync()
        {
            return await _syncRawToFactBusinessCenterService.SyncAllAsync();
        }

        /// <summary>
        /// Đồng bộ Business Centers từ Raw sang Fact theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BusinessCenterSyncResult> SyncBusinessCentersAsync(string bcId)
        {
            return await _syncRawToFactBusinessCenterService.SyncBusinessCentersAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ Ad Accounts từ Raw sang Fact theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BusinessCenterSyncResult> SyncAdAccountsAsync(string bcId)
        {
            return await _syncRawToFactBusinessCenterService.SyncAdAccountsAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ tất cả dữ liệu từ Raw sang Fact theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BusinessCenterSyncResult> SyncAllAsync(string bcId)
        {
            return await _syncRawToFactBusinessCenterService.SyncAllAsync(bcId);
        }
    }
}
