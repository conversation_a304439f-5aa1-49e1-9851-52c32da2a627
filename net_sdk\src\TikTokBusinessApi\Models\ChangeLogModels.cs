/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Create Change Log Task Models

    /// <summary>
    /// Request model for creating a change log download task
    /// </summary>
    public class CreateChangeLogTaskRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Query start time, format: %Y-%m-%d %H:%M:%S
        /// </summary>
        [JsonPropertyName("start_time")]
        public string? StartTime { get; set; }

        /// <summary>
        /// Query end time, format: %Y-%m-%d %H:%M:%S
        /// Note: The largest time gap allowed is 30 days
        /// </summary>
        [JsonPropertyName("end_time")]
        public string? EndTime { get; set; }

        /// <summary>
        /// Timezone of returned value, refer to Appendix-Time Zone for optional values
        /// </summary>
        [JsonPropertyName("timezone")]
        public string? Timezone { get; set; }

        /// <summary>
        /// Module to be queried
        /// Optional values: BIDDING_AND_OPTIMIZATION, BUDGET, STATUS, TARGETING
        /// </summary>
        [JsonPropertyName("module")]
        public string? Module { get; set; }

        /// <summary>
        /// Query object ID
        /// </summary>
        [JsonPropertyName("object_ids")]
        public List<string>? ObjectIds { get; set; }

        /// <summary>
        /// Query object type
        /// Optional values: AD, ADGROUP, ADVERTISER, CAMPAIGN, INSTANT_FORM
        /// </summary>
        [JsonPropertyName("object_type")]
        public string? ObjectType { get; set; }

        /// <summary>
        /// Operation type
        /// Optional values: CREATE, AUDIT, STATUS, UPDATE, DELETE, DOWNLOAD_LEADS, SUBSCRIBE_FORM, UNSUBSCRIBE_FORM
        /// </summary>
        [JsonPropertyName("operation_types")]
        public List<string>? OperationTypes { get; set; }

        /// <summary>
        /// Order by fields
        /// </summary>
        [JsonPropertyName("order_fields")]
        public List<string>? OrderFields { get; set; }
    }

    /// <summary>
    /// Data model for create change log task response
    /// </summary>
    public class CreateChangeLogTaskData
    {
        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }
    }

    /// <summary>
    /// Response model for creating a change log download task
    /// </summary>
    public class CreateChangeLogTaskResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public CreateChangeLogTaskData? Data { get; set; }
    }

    #endregion

    #region Check Change Log Task Status Models

    /// <summary>
    /// Request model for checking change log task status
    /// </summary>
    public class CheckChangeLogTaskRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Task ID (Required)
        /// </summary>
        [JsonPropertyName("task_id")]
        public string TaskId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Data model for check change log task status response
    /// </summary>
    public class CheckChangeLogTaskData
    {
        /// <summary>
        /// Task status values: PROCESSING, SUCCESS, FAILED
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }
    }

    /// <summary>
    /// Response model for checking change log task status
    /// </summary>
    public class CheckChangeLogTaskResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public CheckChangeLogTaskData? Data { get; set; }
    }

    #endregion

    #region Download Change Log Task Models

    /// <summary>
    /// Request model for downloading change log task
    /// </summary>
    public class DownloadChangeLogTaskRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Task ID (Required)
        /// </summary>
        [JsonPropertyName("task_id")]
        public string TaskId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Data model for download change log task response
    /// </summary>
    public class DownloadChangeLogTaskData
    {
        /// <summary>
        /// Task status. Enum values: PROCESSING, SUCCESS, FAILED
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// CSV file data of the log
        /// </summary>
        [JsonPropertyName("changelog")]
        public string? Changelog { get; set; }
    }

    /// <summary>
    /// Response model for downloading change log task
    /// </summary>
    public class DownloadChangeLogTaskResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public DownloadChangeLogTaskData? Data { get; set; }
    }

    #endregion
}
