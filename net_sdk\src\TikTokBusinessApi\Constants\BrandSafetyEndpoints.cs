/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Brand Safety endpoints
    /// </summary>
    public static class BrandSafetyEndpoints
    {
        /// <summary>
        /// Get brand safety settings endpoint (v1.3)
        /// </summary>
        public const string GetBrandSafetySettings = "/v1.3/tiktok_inventory_filters/get/";

        /// <summary>
        /// Update brand safety settings endpoint (v1.3)
        /// </summary>
        public const string UpdateBrandSafetySettings = "/v1.3/tiktok_inventory_filters/update/";
    }
}
