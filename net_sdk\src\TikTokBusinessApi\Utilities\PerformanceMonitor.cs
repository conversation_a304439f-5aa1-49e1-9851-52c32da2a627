/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TikTokBusinessApi.Utilities
{
    /// <summary>
    /// Performance monitoring and metrics collection utility
    /// </summary>
    public class PerformanceMonitor : IDisposable
    {
        private readonly ConcurrentDictionary<string, OperationMetrics> _operationMetrics;
        private readonly ConcurrentQueue<PerformanceEvent> _events;
        private readonly ILogger<PerformanceMonitor>? _logger;
        private readonly PerformanceMonitorOptions _options;
        private readonly Timer _reportingTimer;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the PerformanceMonitor class
        /// </summary>
        /// <param name="options">Performance monitoring configuration options</param>
        /// <param name="logger">Logger instance (optional)</param>
        public PerformanceMonitor(PerformanceMonitorOptions? options = null, ILogger<PerformanceMonitor>? logger = null)
        {
            _operationMetrics = new ConcurrentDictionary<string, OperationMetrics>();
            _events = new ConcurrentQueue<PerformanceEvent>();
            _logger = logger;
            _options = options ?? new PerformanceMonitorOptions();

            // Set up periodic reporting
            if (_options.EnablePeriodicReporting)
            {
                _reportingTimer = new Timer(GeneratePeriodicReport, null, 
                    _options.ReportingInterval, _options.ReportingInterval);
            }
        }

        /// <summary>
        /// Start monitoring an operation
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="metadata">Additional metadata for the operation</param>
        /// <returns>Performance tracking context</returns>
        public PerformanceTracker StartOperation(string operationName, Dictionary<string, object>? metadata = null)
        {
            if (string.IsNullOrEmpty(operationName))
                throw new ArgumentException("Operation name cannot be null or empty", nameof(operationName));

            var tracker = new PerformanceTracker(operationName, metadata, this);
            
            _logger?.LogDebug("Started monitoring operation: {OperationName}", operationName);
            
            return tracker;
        }

        /// <summary>
        /// Record the completion of an operation
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="duration">Duration of the operation</param>
        /// <param name="success">Whether the operation was successful</param>
        /// <param name="metadata">Additional metadata</param>
        internal void RecordOperation(string operationName, TimeSpan duration, bool success, Dictionary<string, object>? metadata = null)
        {
            // Update operation metrics
            var metrics = _operationMetrics.GetOrAdd(operationName, _ => new OperationMetrics(operationName));
            metrics.RecordExecution(duration, success);

            // Add to events queue
            var perfEvent = new PerformanceEvent
            {
                OperationName = operationName,
                Timestamp = DateTimeOffset.UtcNow,
                Duration = duration,
                Success = success,
                Metadata = metadata ?? new Dictionary<string, object>()
            };

            _events.Enqueue(perfEvent);

            // Trim events queue if it gets too large
            while (_events.Count > _options.MaxEventHistory)
            {
                _events.TryDequeue(out _);
            }

            _logger?.LogDebug("Recorded operation: {OperationName}, Duration: {Duration}ms, Success: {Success}", 
                operationName, duration.TotalMilliseconds, success);
        }

        /// <summary>
        /// Get performance metrics for a specific operation
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <returns>Performance metrics or null if operation not found</returns>
        public OperationMetrics? GetOperationMetrics(string operationName)
        {
            if (string.IsNullOrEmpty(operationName))
                throw new ArgumentException("Operation name cannot be null or empty", nameof(operationName));

            return _operationMetrics.TryGetValue(operationName, out var metrics) ? metrics : null;
        }

        /// <summary>
        /// Get performance metrics for all operations
        /// </summary>
        /// <returns>Dictionary of operation metrics</returns>
        public Dictionary<string, OperationMetrics> GetAllMetrics()
        {
            return _operationMetrics.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// Get recent performance events
        /// </summary>
        /// <param name="count">Number of recent events to retrieve (default: 100)</param>
        /// <returns>List of recent performance events</returns>
        public List<PerformanceEvent> GetRecentEvents(int count = 100)
        {
            return _events.TakeLast(Math.Min(count, _events.Count)).ToList();
        }

        /// <summary>
        /// Get performance events for a specific operation
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="count">Number of recent events to retrieve (default: 100)</param>
        /// <returns>List of performance events for the operation</returns>
        public List<PerformanceEvent> GetOperationEvents(string operationName, int count = 100)
        {
            if (string.IsNullOrEmpty(operationName))
                throw new ArgumentException("Operation name cannot be null or empty", nameof(operationName));

            return _events
                .Where(e => e.OperationName == operationName)
                .TakeLast(Math.Min(count, _events.Count))
                .ToList();
        }

        /// <summary>
        /// Generate a comprehensive performance report
        /// </summary>
        /// <returns>Performance report</returns>
        public PerformanceReport GenerateReport()
        {
            var report = new PerformanceReport
            {
                GeneratedAt = DateTimeOffset.UtcNow,
                TotalOperations = _operationMetrics.Values.Sum(m => m.TotalExecutions),
                TotalSuccessfulOperations = _operationMetrics.Values.Sum(m => m.SuccessfulExecutions),
                TotalFailedOperations = _operationMetrics.Values.Sum(m => m.FailedExecutions),
                OperationMetrics = GetAllMetrics(),
                RecentEvents = GetRecentEvents(_options.ReportEventCount)
            };

            if (report.TotalOperations > 0)
            {
                report.OverallSuccessRate = (double)report.TotalSuccessfulOperations / report.TotalOperations * 100;
                report.AverageExecutionTime = TimeSpan.FromMilliseconds(
                    _operationMetrics.Values.Average(m => m.AverageExecutionTime.TotalMilliseconds));
            }

            return report;
        }

        /// <summary>
        /// Reset all performance metrics and events
        /// </summary>
        public void Reset()
        {
            _operationMetrics.Clear();
            
            while (_events.TryDequeue(out _))
            {
                // Clear all events
            }

            _logger?.LogInformation("Performance monitor reset - all metrics and events cleared");
        }

        /// <summary>
        /// Reset metrics for a specific operation
        /// </summary>
        /// <param name="operationName">Name of the operation to reset</param>
        public void ResetOperation(string operationName)
        {
            if (string.IsNullOrEmpty(operationName))
                throw new ArgumentException("Operation name cannot be null or empty", nameof(operationName));

            if (_operationMetrics.TryRemove(operationName, out _))
            {
                _logger?.LogDebug("Reset metrics for operation: {OperationName}", operationName);
            }
        }

        private void GeneratePeriodicReport(object? state)
        {
            try
            {
                var report = GenerateReport();
                
                _logger?.LogInformation("Performance Report - Operations: {TotalOps}, Success Rate: {SuccessRate:F1}%, Avg Time: {AvgTime}ms",
                    report.TotalOperations, report.OverallSuccessRate, report.AverageExecutionTime.TotalMilliseconds);

                // Log top slowest operations
                var slowestOps = report.OperationMetrics
                    .OrderByDescending(kvp => kvp.Value.AverageExecutionTime)
                    .Take(3)
                    .ToList();

                if (slowestOps.Any())
                {
                    _logger?.LogInformation("Slowest Operations: {SlowOps}", 
                        string.Join(", ", slowestOps.Select(op => $"{op.Key} ({op.Value.AverageExecutionTime.TotalMilliseconds:F1}ms)")));
                }

                // Log operations with high failure rates
                var failingOps = report.OperationMetrics
                    .Where(kvp => kvp.Value.FailureRate > 10) // More than 10% failure rate
                    .OrderByDescending(kvp => kvp.Value.FailureRate)
                    .Take(3)
                    .ToList();

                if (failingOps.Any())
                {
                    _logger?.LogWarning("Operations with High Failure Rates: {FailingOps}",
                        string.Join(", ", failingOps.Select(op => $"{op.Key} ({op.Value.FailureRate:F1}%)")));
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error generating periodic performance report");
            }
        }

        /// <summary>
        /// Dispose of the performance monitor and clean up resources
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _reportingTimer?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Performance tracking context for individual operations
    /// </summary>
    public class PerformanceTracker : IDisposable
    {
        private readonly string _operationName;
        private readonly Dictionary<string, object>? _metadata;
        private readonly PerformanceMonitor _monitor;
        private readonly Stopwatch _stopwatch;
        private bool _disposed;
        private bool _success = true;

        internal PerformanceTracker(string operationName, Dictionary<string, object>? metadata, PerformanceMonitor monitor)
        {
            _operationName = operationName;
            _metadata = metadata;
            _monitor = monitor;
            _stopwatch = Stopwatch.StartNew();
        }

        /// <summary>
        /// Mark the operation as failed
        /// </summary>
        public void MarkAsFailed()
        {
            _success = false;
        }

        /// <summary>
        /// Mark the operation as successful (default state)
        /// </summary>
        public void MarkAsSuccessful()
        {
            _success = true;
        }

        /// <summary>
        /// Add metadata to the operation
        /// </summary>
        /// <param name="key">Metadata key</param>
        /// <param name="value">Metadata value</param>
        public void AddMetadata(string key, object value)
        {
            if (_metadata != null)
            {
                _metadata[key] = value;
            }
        }

        /// <summary>
        /// Complete the operation tracking
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _stopwatch.Stop();
                _monitor.RecordOperation(_operationName, _stopwatch.Elapsed, _success, _metadata);
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Configuration options for performance monitoring
    /// </summary>
    public class PerformanceMonitorOptions
    {
        /// <summary>
        /// Whether to enable periodic reporting
        /// </summary>
        public bool EnablePeriodicReporting { get; set; } = true;

        /// <summary>
        /// Interval for generating periodic reports
        /// </summary>
        public TimeSpan ReportingInterval { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// Maximum number of events to keep in history
        /// </summary>
        public int MaxEventHistory { get; set; } = 1000;

        /// <summary>
        /// Number of recent events to include in reports
        /// </summary>
        public int ReportEventCount { get; set; } = 50;
    }

    /// <summary>
    /// Metrics for a specific operation
    /// </summary>
    public class OperationMetrics
    {
        private readonly object _lock = new();
        private readonly List<TimeSpan> _executionTimes = new();

        /// <summary>
        /// Name of the operation
        /// </summary>
        public string OperationName { get; }

        /// <summary>
        /// Total number of executions
        /// </summary>
        public long TotalExecutions { get; private set; }

        /// <summary>
        /// Number of successful executions
        /// </summary>
        public long SuccessfulExecutions { get; private set; }

        /// <summary>
        /// Number of failed executions
        /// </summary>
        public long FailedExecutions { get; private set; }

        /// <summary>
        /// Success rate as a percentage
        /// </summary>
        public double SuccessRate => TotalExecutions > 0 ? (double)SuccessfulExecutions / TotalExecutions * 100 : 0;

        /// <summary>
        /// Failure rate as a percentage
        /// </summary>
        public double FailureRate => TotalExecutions > 0 ? (double)FailedExecutions / TotalExecutions * 100 : 0;

        /// <summary>
        /// Average execution time
        /// </summary>
        public TimeSpan AverageExecutionTime { get; private set; }

        /// <summary>
        /// Minimum execution time
        /// </summary>
        public TimeSpan MinExecutionTime { get; private set; } = TimeSpan.MaxValue;

        /// <summary>
        /// Maximum execution time
        /// </summary>
        public TimeSpan MaxExecutionTime { get; private set; }

        /// <summary>
        /// First execution timestamp
        /// </summary>
        public DateTimeOffset FirstExecution { get; private set; }

        /// <summary>
        /// Last execution timestamp
        /// </summary>
        public DateTimeOffset LastExecution { get; private set; }

        internal OperationMetrics(string operationName)
        {
            OperationName = operationName;
            FirstExecution = DateTimeOffset.UtcNow;
        }

        internal void RecordExecution(TimeSpan duration, bool success)
        {
            lock (_lock)
            {
                TotalExecutions++;
                LastExecution = DateTimeOffset.UtcNow;

                if (success)
                    SuccessfulExecutions++;
                else
                    FailedExecutions++;

                _executionTimes.Add(duration);

                // Update min/max
                if (duration < MinExecutionTime)
                    MinExecutionTime = duration;
                if (duration > MaxExecutionTime)
                    MaxExecutionTime = duration;

                // Calculate average
                AverageExecutionTime = TimeSpan.FromTicks(_executionTimes.Sum(t => t.Ticks) / _executionTimes.Count);

                // Keep only recent execution times to prevent memory growth
                if (_executionTimes.Count > 1000)
                {
                    _executionTimes.RemoveRange(0, 500); // Remove oldest half
                }
            }
        }

        /// <summary>
        /// Get percentile execution time
        /// </summary>
        /// <param name="percentile">Percentile (0-100)</param>
        /// <returns>Execution time at the specified percentile</returns>
        public TimeSpan GetPercentile(double percentile)
        {
            if (percentile < 0 || percentile > 100)
                throw new ArgumentOutOfRangeException(nameof(percentile), "Percentile must be between 0 and 100");

            lock (_lock)
            {
                if (_executionTimes.Count == 0)
                    return TimeSpan.Zero;

                var sortedTimes = _executionTimes.OrderBy(t => t).ToList();
                var index = (int)Math.Ceiling(percentile / 100.0 * sortedTimes.Count) - 1;
                index = Math.Max(0, Math.Min(index, sortedTimes.Count - 1));

                return sortedTimes[index];
            }
        }
    }

    /// <summary>
    /// Individual performance event
    /// </summary>
    public class PerformanceEvent
    {
        /// <summary>
        /// Name of the operation
        /// </summary>
        public string OperationName { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when the operation completed
        /// </summary>
        public DateTimeOffset Timestamp { get; set; }

        /// <summary>
        /// Duration of the operation
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Additional metadata for the operation
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Comprehensive performance report
    /// </summary>
    public class PerformanceReport
    {
        /// <summary>
        /// When the report was generated
        /// </summary>
        public DateTimeOffset GeneratedAt { get; set; }

        /// <summary>
        /// Total number of operations across all types
        /// </summary>
        public long TotalOperations { get; set; }

        /// <summary>
        /// Total number of successful operations
        /// </summary>
        public long TotalSuccessfulOperations { get; set; }

        /// <summary>
        /// Total number of failed operations
        /// </summary>
        public long TotalFailedOperations { get; set; }

        /// <summary>
        /// Overall success rate as a percentage
        /// </summary>
        public double OverallSuccessRate { get; set; }

        /// <summary>
        /// Average execution time across all operations
        /// </summary>
        public TimeSpan AverageExecutionTime { get; set; }

        /// <summary>
        /// Metrics for each operation type
        /// </summary>
        public Dictionary<string, OperationMetrics> OperationMetrics { get; set; } = new();

        /// <summary>
        /// Recent performance events
        /// </summary>
        public List<PerformanceEvent> RecentEvents { get; set; } = new();
    }
}
