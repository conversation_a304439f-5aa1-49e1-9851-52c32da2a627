/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Authentication operations
    /// </summary>
    public interface IAuthenticationApi
    {
        /// <summary>
        /// Obtain a long-term access token via /oauth2/access_token/
        /// </summary>
        /// <param name="request">Long-term access token request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Long-term access token response</returns>
        Task<LongTermAccessTokenResponse> GetLongTermAccessTokenAsync(
            LongTermAccessTokenRequest request, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Obtain an access token via /oauth/token/ (supports both JSON and form-encoded)
        /// </summary>
        /// <param name="request">Access token request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Access token response</returns>
        Task<AccessTokenResponse> GetAccessTokenAsync(
            AccessTokenRequest request, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Revoke a long-term access token via /oauth2/revoke_token/
        /// </summary>
        /// <param name="request">Revoke long-term token request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Revoke long-term token response</returns>
        //Task<RevokeLongTermTokenResponse> RevokeLongTermAccessTokenAsync(
        //    RevokeLongTermTokenRequest request, 
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Obtain a short-term access token via /tt_user/oauth2/token/
        /// </summary>
        /// <param name="request">Short-term access token request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Short-term access token response</returns>
        Task<ShortTermAccessTokenResponse> GetShortTermAccessTokenAsync(
            ShortTermAccessTokenRequest request, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Refresh a short-term access token via /tt_user/oauth2/refresh_token/
        /// </summary>
        /// <param name="request">Refresh short-term token request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Refresh short-term token response</returns>
        //Task<RefreshShortTermTokenResponse> RefreshShortTermAccessTokenAsync(
        //    RefreshShortTermTokenRequest request, 
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Revoke a short-term access token via /tt_user/oauth2/revoke/
        /// </summary>
        /// <param name="request">Revoke short-term token request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Revoke short-term token response</returns>
        //Task<RevokeShortTermTokenResponse> RevokeShortTermAccessTokenAsync(
        //    RevokeShortTermTokenRequest request, 
        //    CancellationToken cancellationToken = default);
    }
}
