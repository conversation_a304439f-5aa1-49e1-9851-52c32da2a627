/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for updating an ad group
    /// </summary>
    public class AdGroupUpdateBody
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        [Required]
        public string AdgroupId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group name
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdgroupName { get; set; }

        /// <summary>
        /// Optimization event
        /// </summary>
        [JsonPropertyName("optimization_event")]
        public string? OptimizationEvent { get; set; }

        /// <summary>
        /// Optimization goal
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string? OptimizationGoal { get; set; }

        /// <summary>
        /// Bid type
        /// </summary>
        [JsonPropertyName("bid_type")]
        public string? BidType { get; set; }

        /// <summary>
        /// Bid price in micros
        /// </summary>
        [JsonPropertyName("bid_price")]
        public long? BidPrice { get; set; }

        /// <summary>
        /// Budget in micros
        /// </summary>
        [JsonPropertyName("budget")]
        public long? Budget { get; set; }

        /// <summary>
        /// Budget mode
        /// </summary>
        [JsonPropertyName("budget_mode")]
        public string? BudgetMode { get; set; }

        /// <summary>
        /// Schedule type
        /// </summary>
        [JsonPropertyName("schedule_type")]
        public string? ScheduleType { get; set; }

        /// <summary>
        /// Schedule start time
        /// </summary>
        [JsonPropertyName("schedule_start_time")]
        public string? ScheduleStartTime { get; set; }

        /// <summary>
        /// Schedule end time
        /// </summary>
        [JsonPropertyName("schedule_end_time")]
        public string? ScheduleEndTime { get; set; }

        /// <summary>
        /// Day parting schedule
        /// </summary>
        [JsonPropertyName("dayparting")]
        public string? Dayparting { get; set; }

        /// <summary>
        /// Audience type
        /// </summary>
        [JsonPropertyName("audience_type")]
        public string? AudienceType { get; set; }

        /// <summary>
        /// Placements
        /// </summary>
        [JsonPropertyName("placements")]
        public List<string>? Placements { get; set; }

        /// <summary>
        /// Location IDs
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string>? LocationIds { get; set; }

        /// <summary>
        /// Age groups
        /// </summary>
        [JsonPropertyName("age_groups")]
        public List<string>? AgeGroups { get; set; }

        /// <summary>
        /// Genders
        /// </summary>
        [JsonPropertyName("genders")]
        public List<string>? Genders { get; set; }

        /// <summary>
        /// Languages
        /// </summary>
        [JsonPropertyName("languages")]
        public List<string>? Languages { get; set; }

        /// <summary>
        /// Interest category IDs
        /// </summary>
        [JsonPropertyName("interest_category_ids")]
        public List<string>? InterestCategoryIds { get; set; }

        /// <summary>
        /// Interest keyword IDs
        /// </summary>
        [JsonPropertyName("interest_keyword_ids")]
        public List<string>? InterestKeywordIds { get; set; }

        /// <summary>
        /// Behavior category IDs
        /// </summary>
        [JsonPropertyName("behavior_category_ids")]
        public List<string>? BehaviorCategoryIds { get; set; }

        /// <summary>
        /// Household income
        /// </summary>
        [JsonPropertyName("household_income")]
        public List<string>? HouseholdIncome { get; set; }

        /// <summary>
        /// Operating systems
        /// </summary>
        [JsonPropertyName("operating_systems")]
        public List<string>? OperatingSystems { get; set; }

        /// <summary>
        /// Device models
        /// </summary>
        [JsonPropertyName("device_models")]
        public List<string>? DeviceModels { get; set; }

        /// <summary>
        /// Network types
        /// </summary>
        [JsonPropertyName("network_types")]
        public List<string>? NetworkTypes { get; set; }

        /// <summary>
        /// Device price buckets
        /// </summary>
        [JsonPropertyName("device_price_buckets")]
        public List<string>? DevicePriceBuckets { get; set; }

        /// <summary>
        /// Carrier IDs
        /// </summary>
        [JsonPropertyName("carrier_ids")]
        public List<string>? CarrierIds { get; set; }

        /// <summary>
        /// Custom audiences
        /// </summary>
        [JsonPropertyName("custom_audiences")]
        public List<string>? CustomAudiences { get; set; }

        /// <summary>
        /// Excluded custom audiences
        /// </summary>
        [JsonPropertyName("excluded_custom_audiences")]
        public List<string>? ExcludedCustomAudiences { get; set; }

        /// <summary>
        /// Lookalike audiences
        /// </summary>
        [JsonPropertyName("lookalike_audiences")]
        public List<string>? LookalikeAudiences { get; set; }

        /// <summary>
        /// Excluded lookalike audiences
        /// </summary>
        [JsonPropertyName("excluded_lookalike_audiences")]
        public List<string>? ExcludedLookalikeAudiences { get; set; }

        /// <summary>
        /// Conversion window
        /// </summary>
        [JsonPropertyName("conversion_window")]
        public string? ConversionWindow { get; set; }

        /// <summary>
        /// Attribution window
        /// </summary>
        [JsonPropertyName("attribution_window")]
        public string? AttributionWindow { get; set; }

        /// <summary>
        /// Deep bid type
        /// </summary>
        [JsonPropertyName("deep_bid_type")]
        public string? DeepBidType { get; set; }

        /// <summary>
        /// Deep CPA bid
        /// </summary>
        [JsonPropertyName("deep_cpa_bid")]
        public long? DeepCpaBid { get; set; }

        /// <summary>
        /// Pacing
        /// </summary>
        [JsonPropertyName("pacing")]
        public string? Pacing { get; set; }

        /// <summary>
        /// Billing event
        /// </summary>
        [JsonPropertyName("billing_event")]
        public string? BillingEvent { get; set; }

        /// <summary>
        /// Bid display mode
        /// </summary>
        [JsonPropertyName("bid_display_mode")]
        public string? BidDisplayMode { get; set; }

        /// <summary>
        /// Frequency
        /// </summary>
        [JsonPropertyName("frequency")]
        public int? Frequency { get; set; }

        /// <summary>
        /// Frequency schedule
        /// </summary>
        [JsonPropertyName("frequency_schedule")]
        public int? FrequencySchedule { get; set; }

        /// <summary>
        /// Pixel ID
        /// </summary>
        [JsonPropertyName("pixel_id")]
        public string? PixelId { get; set; }

        /// <summary>
        /// App ID
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupUpdateBody class
        /// </summary>
        public AdGroupUpdateBody()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupUpdateBody(AdgroupId={AdgroupId}, AdgroupName={AdgroupName})";
        }
    }

    /// <summary>
    /// Request body for updating ad group budget
    /// </summary>
    public class AdGroupBudgetUpdateBody
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        [Required]
        public string AdgroupId { get; set; } = string.Empty;

        /// <summary>
        /// Budget in micros
        /// </summary>
        [JsonPropertyName("budget")]
        [Required]
        public long Budget { get; set; }

        /// <summary>
        /// Budget mode
        /// </summary>
        [JsonPropertyName("budget_mode")]
        public string? BudgetMode { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupBudgetUpdateBody class
        /// </summary>
        public AdGroupBudgetUpdateBody()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupBudgetUpdateBody(AdgroupId={AdgroupId}, Budget={Budget})";
        }
    }

    /// <summary>
    /// Audience targeting information for ad groups
    /// </summary>
    public class AudienceTargeting
    {
        /// <summary>
        /// Audience type
        /// </summary>
        [JsonPropertyName("audience_type")]
        public string? AudienceType { get; set; }

        /// <summary>
        /// Placements
        /// </summary>
        [JsonPropertyName("placements")]
        public List<string>? Placements { get; set; }

        /// <summary>
        /// Location IDs
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string>? LocationIds { get; set; }

        /// <summary>
        /// Age groups
        /// </summary>
        [JsonPropertyName("age_groups")]
        public List<string>? AgeGroups { get; set; }

        /// <summary>
        /// Genders
        /// </summary>
        [JsonPropertyName("genders")]
        public List<string>? Genders { get; set; }

        /// <summary>
        /// Languages
        /// </summary>
        [JsonPropertyName("languages")]
        public List<string>? Languages { get; set; }

        /// <summary>
        /// Interest category IDs
        /// </summary>
        [JsonPropertyName("interest_category_ids")]
        public List<string>? InterestCategoryIds { get; set; }

        /// <summary>
        /// Interest keyword IDs
        /// </summary>
        [JsonPropertyName("interest_keyword_ids")]
        public List<string>? InterestKeywordIds { get; set; }

        /// <summary>
        /// Behavior category IDs
        /// </summary>
        [JsonPropertyName("behavior_category_ids")]
        public List<string>? BehaviorCategoryIds { get; set; }

        /// <summary>
        /// Household income
        /// </summary>
        [JsonPropertyName("household_income")]
        public List<string>? HouseholdIncome { get; set; }

        /// <summary>
        /// Operating systems
        /// </summary>
        [JsonPropertyName("operating_systems")]
        public List<string>? OperatingSystems { get; set; }

        /// <summary>
        /// Device models
        /// </summary>
        [JsonPropertyName("device_models")]
        public List<string>? DeviceModels { get; set; }

        /// <summary>
        /// Network types
        /// </summary>
        [JsonPropertyName("network_types")]
        public List<string>? NetworkTypes { get; set; }

        /// <summary>
        /// Device price buckets
        /// </summary>
        [JsonPropertyName("device_price_buckets")]
        public List<string>? DevicePriceBuckets { get; set; }

        /// <summary>
        /// Carrier IDs
        /// </summary>
        [JsonPropertyName("carrier_ids")]
        public List<string>? CarrierIds { get; set; }

        /// <summary>
        /// Custom audiences
        /// </summary>
        [JsonPropertyName("custom_audiences")]
        public List<string>? CustomAudiences { get; set; }

        /// <summary>
        /// Excluded custom audiences
        /// </summary>
        [JsonPropertyName("excluded_custom_audiences")]
        public List<string>? ExcludedCustomAudiences { get; set; }

        /// <summary>
        /// Lookalike audiences
        /// </summary>
        [JsonPropertyName("lookalike_audiences")]
        public List<string>? LookalikeAudiences { get; set; }

        /// <summary>
        /// Excluded lookalike audiences
        /// </summary>
        [JsonPropertyName("excluded_lookalike_audiences")]
        public List<string>? ExcludedLookalikeAudiences { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceTargeting class
        /// </summary>
        public AudienceTargeting()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AudienceTargeting(AudienceType={AudienceType}, Placements={Placements?.Count})";
        }
    }
}
