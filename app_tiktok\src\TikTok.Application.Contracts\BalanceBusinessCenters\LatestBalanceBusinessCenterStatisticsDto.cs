using System;
using System.Collections.Generic;

namespace TikTok.BalanceBusinessCenters
{
    /// <summary>
    /// DTO thống kê tổng quan về Business Center mới nhất
    /// </summary>
    public class LatestBalanceBusinessCenterStatisticsDto
    {
        /// <summary>
        /// Tổng số Business Center
        /// </summary>
        public int TotalBusinessCenters { get; set; }

        /// <summary>
        /// Tổng số dư tài khoản
        /// </summary>
        public decimal TotalAccountBalance { get; set; }

        /// <summary>
        /// Tổng số dư tài khoản hợp lệ
        /// </summary>
        public decimal TotalValidAccountBalance { get; set; }

        /// <summary>
        /// Tổng số dư bị đóng băng
        /// </summary>
        public decimal TotalFrozenBalance { get; set; }

        /// <summary>
        /// Tổng thuế
        /// </summary>
        public decimal TotalTax { get; set; }

        /// <summary>
        /// Tổng số dư tiền mặt
        /// </summary>
        public decimal TotalCashBalance { get; set; }

        /// <summary>
        /// Tổng số dư tiền mặt hợp lệ
        /// </summary>
        public decimal TotalValidCashBalance { get; set; }

        /// <summary>
        /// Tổng số dư phiếu giảm giá/voucher
        /// </summary>
        public decimal TotalGrantBalance { get; set; }

        /// <summary>
        /// Tổng số dư phiếu giảm giá/voucher hợp lệ
        /// </summary>
        public decimal TotalValidGrantBalance { get; set; }

        /// <summary>
        /// Thời gian đồng bộ mới nhất
        /// </summary>
        public DateTime? LastSyncTime { get; set; }

        /// <summary>
        /// Số Business Center theo tiền tệ
        /// </summary>
        public Dictionary<string, int> BusinessCentersByCurrency { get; set; }

        /// <summary>
        /// Số Business Center theo múi giờ
        /// </summary>
        public Dictionary<string, int> BusinessCentersByTimezone { get; set; }

        /// <summary>
        /// Số Business Center có số dư thấp (dưới 1000)
        /// </summary>
        public int BusinessCentersWithLowBalance { get; set; }

        /// <summary>
        /// Số Business Center có số dư cao (trên 10000)
        /// </summary>
        public int BusinessCentersWithHighBalance { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public LatestBalanceBusinessCenterStatisticsDto()
        {
            BusinessCentersByCurrency = new Dictionary<string, int>();
            BusinessCentersByTimezone = new Dictionary<string, int>();
        }
    }
}
