using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu giao dịch
    /// </summary>
    public interface ITransactionSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ dữ liệu giao dịch theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<TransactionSyncResult> SyncTransactionsAsync(string bcId);

        /// <summary>
        /// Đồng bộ dữ liệu giao dịch cho tất cả Business Applications
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<TransactionSyncResult> SyncAllTransactionsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu giao dịch
    /// </summary>
    public class TransactionSyncResult : SyncResultBase
    {
        /// <summary>
        /// Danh sách ID của các giao dịch mới được thêm
        /// </summary>
        public List<string> NewTransactionIds { get; set; } = new List<string>();
    }
}