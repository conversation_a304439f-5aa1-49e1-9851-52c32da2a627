/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for post authorization status
    /// </summary>
    public class PostAuthorizationStatusResponse
    {
        /// <summary>
        /// The ID of the TikTok post
        /// </summary>
        [JsonPropertyName("item_id")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// The authorization code for the TikTok post
        /// </summary>
        [JsonPropertyName("auth_code")]
        public string AuthCode { get; set; } = string.Empty;

        /// <summary>
        /// The time when the authorization code becomes valid
        /// </summary>
        [JsonPropertyName("auth_code_start_time")]
        public string AuthCodeStartTime { get; set; } = string.Empty;

        /// <summary>
        /// The time when the authorization code expires
        /// </summary>
        [JsonPropertyName("auth_code_end_time")]
        public string AuthCodeEndTime { get; set; } = string.Empty;

        /// <summary>
        /// The authorization validity period in days
        /// </summary>
        [JsonPropertyName("authorization_days")]
        public int AuthorizationDays { get; set; }

        /// <summary>
        /// The status of the authorization code
        /// Enum values: NOT_USED, IN_USE, EXPIRED
        /// </summary>
        [JsonPropertyName("auth_code_status")]
        public string AuthCodeStatus { get; set; } = string.Empty;
    }
}
