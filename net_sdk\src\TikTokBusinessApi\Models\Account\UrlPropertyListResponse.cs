/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for URL property list
    /// </summary>
    public class UrlPropertyListResponse
    {
        /// <summary>
        /// List of URL properties
        /// </summary>
        [JsonPropertyName("url_property_list")]
        public List<UrlPropertyInfo> UrlPropertyList { get; set; } = new List<UrlPropertyInfo>();
    }
}
