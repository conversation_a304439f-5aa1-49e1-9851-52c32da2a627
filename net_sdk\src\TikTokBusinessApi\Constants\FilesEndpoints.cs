/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Files endpoints
    /// </summary>
    public static class FilesEndpoints
    {
        /// <summary>
        /// Upload a file endpoint (v1.3)
        /// </summary>
        public const string UploadFile = "/file/temporarily/upload/";

        /// <summary>
        /// Start a file chunk upload task endpoint (v1.3)
        /// </summary>
        public const string StartChunkUpload = "/file/start/upload/";

        /// <summary>
        /// Transfer a file chunk endpoint (v1.3)
        /// </summary>
        public const string TransferChunk = "/file/transfer/upload/";

        /// <summary>
        /// Finish a chunk upload task endpoint (v1.3)
        /// </summary>
        public const string FinishChunkUpload = "/file/finish/upload/";

        /// <summary>
        /// Check the names of files endpoint (v1.3)
        /// </summary>
        public const string CheckFileNames = "/file/name/check/";
    }
}
