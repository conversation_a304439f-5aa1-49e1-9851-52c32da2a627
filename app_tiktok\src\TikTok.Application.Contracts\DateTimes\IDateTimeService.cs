using System;
using Volo.Abp.DependencyInjection;

namespace TikTok.DateTimes;

public interface IDateTimeService : ITransientDependency
{
    /// <summary>
    /// Lấy thời gian hiện tại theo timezone được chỉ định
    /// </summary>
    /// <param name="timezone">Timezone (ví dụ: "UTC+07:00"). <PERSON><PERSON><PERSON> null thì mặc định là "UTC+07:00"</param>
    /// <returns>Thời gian hiện tại theo timezone</returns>
    System.DateTime GetDateTimeNow(string? timezone = null);

    /// <summary>
    /// Lấy thời gian hiện tại theo timezone được chỉ định với thông tin timezone
    /// </summary>
    /// <param name="timezone">Timezone (ví dụ: "UTC+07:00"). Nếu null thì mặc định là "UTC+07:00"</param>
    /// <returns>DateTimeWithTimezoneDto chứa thời gian hiện tại và thông tin timezone</returns>
    DateTimeWithTimezoneDto GetDateTimeNowWithTimezone(string? timezone = null);

    /// <summary>
    /// Lấy ngày hiện tại theo timezone (chỉ ngày tháng năm, không có giờ phút giây)
    /// </summary>
    /// <param name="timezone">Timezone (ví dụ: "Asia/Ho_Chi_Minh" hoặc "UTC+07:00"). Nếu null thì mặc định là "UTC+07:00"</param>
    /// <returns>Ngày hiện tại theo timezone</returns>
    System.DateTime GetDateNow(string? timezone = null);

    /// <summary>
    /// Lấy ngày hiện tại theo timezone với thông tin timezone
    /// </summary>
    /// <param name="timezone">Timezone (ví dụ: "Asia/Ho_Chi_Minh" hoặc "UTC+07:00"). Nếu null thì mặc định là "UTC+07:00"</param>
    /// <returns>DateTimeWithTimezoneDto chứa ngày hiện tại và thông tin timezone</returns>
    DateTimeWithTimezoneDto GetDateNowWithTimezone(string? timezone = null);

    /// <summary>
    /// Lấy khoảng ngày hiện tại theo timezone (23:59 ngày hôm qua - 23:59 ngày hôm nay)
    /// </summary>
    /// <param name="timezone">Timezone (ví dụ: "UTC+07:00"). Nếu null thì mặc định là "UTC+07:00"</param>
    /// <returns>DateRangeDto chứa thời gian bắt đầu và kết thúc của khoảng ngày</returns>
    DateRangeDto GetDateRangeNow(string? timezone = null);

    /// <summary>
    /// Lấy khoảng ngày hiện tại theo timezone với thông tin timezone
    /// </summary>
    /// <param name="timezone">Timezone (ví dụ: "UTC+07:00"). Nếu null thì mặc định là "UTC+07:00"</param>
    /// <returns>DateRangeWithTimezoneDto chứa khoảng ngày và thông tin timezone</returns>
    DateRangeWithTimezoneDto GetDateRangeNowWithTimezone(string? timezone = null);

    /// <summary>
    /// Lấy thời gian hiện tại UTC
    /// </summary>
    /// <returns>Thời gian hiện tại UTC</returns>
    System.DateTime GetDateTimeUtcNow();

    /// <summary>
    /// Lấy thời gian hiện tại UTC với thông tin timezone UTC
    /// </summary>
    /// <returns>DateTimeWithTimezoneDto chứa thời gian hiện tại UTC và thông tin timezone UTC</returns>
    DateTimeWithTimezoneDto GetDateTimeUtcNowWithTimezone();

    /// <summary>
    /// Lấy ngày hiện tại UTC (chỉ ngày tháng năm, không có giờ phút giây)
    /// </summary>
    /// <returns>Ngày hiện tại UTC</returns>
    System.DateTime GetDateUtcNow();

    /// <summary>
    /// Lấy ngày hiện tại UTC với thông tin timezone UTC
    /// </summary>
    /// <returns>DateTimeWithTimezoneDto chứa ngày hiện tại UTC và thông tin timezone UTC</returns>
    DateTimeWithTimezoneDto GetDateUtcNowWithTimezone();

    /// <summary>
    /// Lấy khoảng ngày hiện tại UTC (23:59 ngày hôm qua - 23:59 ngày hôm nay)
    /// </summary>
    /// <returns>DateRangeDto chứa thời gian bắt đầu và kết thúc của khoảng ngày UTC</returns>
    DateRangeDto GetDateRangeUtcNow();

    /// <summary>
    /// Lấy khoảng ngày hiện tại UTC với thông tin timezone UTC
    /// </summary>
    /// <returns>DateRangeWithTimezoneDto chứa khoảng ngày UTC và thông tin timezone UTC</returns>
    DateRangeWithTimezoneDto GetDateRangeUtcNowWithTimezone();

    /// <summary>
    /// Tạo khoảng ngày từ ngày được chỉ định đến ngày hiện tại theo timezone
    /// </summary>
    /// <param name="startDate">Ngày bắt đầu (sẽ được chuyển về 00:00:00)</param>
    /// <param name="timezone">Timezone (ví dụ: "UTC+07:00"). Nếu null thì mặc định là "UTC+07:00"</param>
    /// <returns>DateRangeDto chứa thời gian bắt đầu và kết thúc của khoảng ngày</returns>
    DateRangeDto GetDateRangeFromDateToNow(System.DateTime startDate, string? timezone = null);

    /// <summary>
    /// Chuyển đổi DateTime từ UTC sang timezone được chỉ định
    /// </summary>
    /// <param name="utcDateTime">DateTime UTC</param>
    /// <param name="timezone">Timezone (ví dụ: "Asia/Ho_Chi_Minh" hoặc "UTC+07:00")</param>
    /// <returns>DateTime theo timezone</returns>
    System.DateTime ConvertFromUtc(System.DateTime utcDateTime, string timezone);

    /// <summary>
    /// Chuyển đổi DateTime sang UTC từ timezone được chỉ định
    /// </summary>
    /// <param name="localDateTime">DateTime theo timezone</param>
    /// <param name="timezone">Timezone (ví dụ: "Asia/Ho_Chi_Minh" hoặc "UTC+07:00")</param>
    /// <returns>DateTime UTC</returns>
    System.DateTime ConvertToUtc(System.DateTime localDateTime, string timezone);

    /// <summary>
    /// Kiểm tra timezone có hợp lệ không
    /// </summary>
    /// <param name="timezone">Timezone cần kiểm tra</param>
    /// <returns>True nếu timezone hợp lệ</returns>
    bool IsValidTimezone(string timezone);

    /// <summary>
    /// Lấy offset của timezone so với UTC
    /// </summary>
    /// <param name="timezone">Timezone (ví dụ: "Asia/Ho_Chi_Minh" hoặc "UTC+07:00")</param>
    /// <returns>TimeSpan offset</returns>
    TimeSpan GetTimezoneOffset(string timezone);

    /// <summary>
    /// Chuyển đổi DateTime từ timezone được chỉ định sang UTC time và timezone UTC
    /// </summary>
    /// <param name="localDateTime">DateTime theo timezone</param>
    /// <param name="timezone">Timezone nguồn (ví dụ: "Asia/Ho_Chi_Minh" hoặc "UTC+07:00")</param>
    /// <returns>UtcDateTimeDto chứa DateTime UTC và thông tin timezone UTC</returns>
    UtcDateTimeDto ConvertToUtcWithTimezone(System.DateTime localDateTime, string timezone);

}