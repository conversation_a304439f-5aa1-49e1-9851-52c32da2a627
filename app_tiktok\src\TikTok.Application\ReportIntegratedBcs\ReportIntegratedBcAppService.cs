using System;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTok.ReportIntegratedBcs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.ReportIntegratedBcs
{
    /// <summary>
    /// Service implementation cho báo cáo tích hợp Business Center
    /// </summary>
    public class ReportIntegratedBcAppService :
        CrudAppService<
            RawReportIntegratedBcEntity,
            ReportIntegratedBcDto,
            Guid,
            GetReportIntegratedBcListDto,
            CreateReportIntegratedBcDto,
            UpdateReportIntegratedBcDto>,
        IReportIntegratedBcAppService
    {
        private readonly IRawReportIntegratedBcRepository _reportIntegratedBcRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="repository">Repository</param>
        /// <param name="reportIntegratedBcRepository">Report Integrated BC Repository</param>
        public ReportIntegratedBcAppService(
            IRepository<RawReportIntegratedBcEntity, Guid> repository,
            IRawReportIntegratedBcRepository reportIntegratedBcRepository) : base(repository)
        {
            _reportIntegratedBcRepository = reportIntegratedBcRepository;

            // Cấu hình permission
            GetPolicyName = TikTokPermissions.ReportIntegratedBcs.Default;
            GetListPolicyName = TikTokPermissions.ReportIntegratedBcs.Default;
            CreatePolicyName = TikTokPermissions.ReportIntegratedBcs.Create;
            UpdatePolicyName = TikTokPermissions.ReportIntegratedBcs.Edit;
            DeletePolicyName = TikTokPermissions.ReportIntegratedBcs.Delete;
        }

        /// <summary>
        /// Ghi đè phương thức GetListAsync để sử dụng repository
        /// </summary>
        /// <param name="input">Input để lấy danh sách</param>
        /// <returns>Danh sách báo cáo tích hợp BC</returns>
        public override async Task<PagedResultDto<ReportIntegratedBcDto>> GetListAsync(GetReportIntegratedBcListDto input)
        {
            var totalCount = await _reportIntegratedBcRepository.GetCountAsync(
                input.BcId,
                input.StartDate,
                input.EndDate,
                input.Filter);

            var items = await _reportIntegratedBcRepository.GetListAsync(
                input.BcId,
                input.StartDate,
                input.EndDate,
                input.Sorting,
                input.MaxResultCount,
                input.SkipCount,
                input.Filter);

            return new PagedResultDto<ReportIntegratedBcDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<RawReportIntegratedBcEntity[], ReportIntegratedBcDto[]>(items.ToArray())
            };
        }

        /// <summary>
        /// Lấy báo cáo theo BC ID và ngày
        /// </summary>
        /// <param name="bcId">ID Business Center</param>
        /// <param name="date">Ngày báo cáo</param>
        /// <returns>Báo cáo tích hợp BC</returns>
        public async Task<ReportIntegratedBcDto?> GetByBcIdAndDateAsync(string bcId, DateTime date)
        {
            var entity = await _reportIntegratedBcRepository.GetByBcIdAndDateAsync(bcId, date);
            return entity != null ? ObjectMapper.Map<RawReportIntegratedBcEntity, ReportIntegratedBcDto>(entity) : null;
        }

        /// <summary>
        /// Lấy danh sách báo cáo theo BC ID
        /// </summary>
        /// <param name="bcId">ID Business Center</param>
        /// <returns>Danh sách báo cáo tích hợp BC</returns>
        public async Task<PagedResultDto<ReportIntegratedBcDto>> GetByBcIdAsync(string bcId)
        {
            var items = await _reportIntegratedBcRepository.GetByBcIdAsync(bcId);
            var totalCount = items.Count;

            return new PagedResultDto<ReportIntegratedBcDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<RawReportIntegratedBcEntity[], ReportIntegratedBcDto[]>(items.ToArray())
            };
        }
    }
}