/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Main Request/Response Models

    /// <summary>
    /// Request model for Events API 2.0 - unified endpoint for all event types
    /// </summary>
    public class Events2TrackRequest
    {
        /// <summary>
        /// Type of events being uploaded through Events API
        /// Enum values: web, app, offline, crm
        /// </summary>
        [JsonPropertyName("event_source")]
        public string EventSource { get; set; } = string.Empty;

        /// <summary>
        /// Event Source ID used to measure events
        /// - For web: Pixel Code
        /// - For app: TikTok App ID  
        /// - For offline: Offline Event Set ID
        /// - For crm: CRM Event Set ID
        /// </summary>
        [JsonPropertyName("event_source_id")]
        public string EventSourceId { get; set; } = string.Empty;

        /// <summary>
        /// Array of events to report (up to 1,000 events per request)
        /// </summary>
        [JsonPropertyName("data")]
        public List<Events2EventData> Data { get; set; } = new();
    }

    /// <summary>
    /// Individual event data for Events API 2.0
    /// </summary>
    public class Events2EventData
    {
        /// <summary>
        /// Conversion event name (Standard Event or Custom Event)
        /// </summary>
        [JsonPropertyName("event")]
        public string Event { get; set; } = string.Empty;

        /// <summary>
        /// Unix timestamp when the event occurred (in seconds, UTC+0)
        /// </summary>
        [JsonPropertyName("event_time")]
        public long EventTime { get; set; }

        /// <summary>
        /// Event ID for deduplication (required for web, offline, and CRM events in certain scenarios)
        /// </summary>
        [JsonPropertyName("event_id")]
        public string? EventId { get; set; }

        /// <summary>
        /// Customer information
        /// </summary>
        [JsonPropertyName("user")]
        public Events2UserInfo? User { get; set; }

        /// <summary>
        /// Product, order and additional information
        /// </summary>
        [JsonPropertyName("properties")]
        public Events2Properties? Properties { get; set; }

        /// <summary>
        /// Web page information (required for web events)
        /// </summary>
        [JsonPropertyName("page")]
        public Events2PageInfo? Page { get; set; }

        /// <summary>
        /// App information (required for app events)
        /// </summary>
        [JsonPropertyName("app")]
        public Events2AppInfo? App { get; set; }

        /// <summary>
        /// Ad information (only for app events)
        /// </summary>
        [JsonPropertyName("ad")]
        public Events2AdInfo? Ad { get; set; }

        /// <summary>
        /// Limited Data Use flag (for web and app events)
        /// </summary>
        [JsonPropertyName("limited_data_use")]
        public bool? LimitedDataUse { get; set; }

        /// <summary>
        /// Lead information (required for CRM events)
        /// </summary>
        [JsonPropertyName("lead")]
        public Events2LeadInfo? Lead { get; set; }
    }

    /// <summary>
    /// Response model for Events API 2.0
    /// </summary>
    public class Events2TrackResponse
    {
        /// <summary>
        /// Response code (0 indicates success)
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Request ID for logging
        /// </summary>
        [JsonPropertyName("request_id")]
        public string? RequestId { get; set; }

        /// <summary>
        /// Response data (empty object for successful requests)
        /// </summary>
        [JsonPropertyName("data")]
        public object? Data { get; set; }
    }

    #endregion

    #region User Information Models

    /// <summary>
    /// Customer information for Events API 2.0
    /// </summary>
    public class Events2UserInfo
    {
        /// <summary>
        /// TikTok Click ID (for web events only)
        /// </summary>
        [JsonPropertyName("ttclid")]
        public string? TtClid { get; set; }

        /// <summary>
        /// Email address(es) - SHA-256 hashing required
        /// </summary>
        [JsonPropertyName("email")]
        public object? Email { get; set; } // Can be string or string[]

        /// <summary>
        /// Phone number(s) - SHA-256 hashing required, E.164 format
        /// </summary>
        [JsonPropertyName("phone")]
        public object? Phone { get; set; } // Can be string or string[]

        /// <summary>
        /// External ID(s) - SHA-256 hashing required (for web and CRM events)
        /// </summary>
        [JsonPropertyName("external_id")]
        public object? ExternalId { get; set; } // Can be string or string[]

        /// <summary>
        /// TikTok Cookie ID (for web events only)
        /// </summary>
        [JsonPropertyName("ttp")]
        public string? Ttp { get; set; }

        /// <summary>
        /// Public IP address (for web and app events)
        /// </summary>
        [JsonPropertyName("ip")]
        public string? Ip { get; set; }

        /// <summary>
        /// User agent (for web and app events)
        /// </summary>
        [JsonPropertyName("user_agent")]
        public string? UserAgent { get; set; }

        /// <summary>
        /// iOS IDFA (for app events only)
        /// </summary>
        [JsonPropertyName("idfa")]
        public string? Idfa { get; set; }

        /// <summary>
        /// iOS IDFV (for app events only)
        /// </summary>
        [JsonPropertyName("idfv")]
        public string? Idfv { get; set; }

        /// <summary>
        /// Google Advertising ID (for app events only)
        /// </summary>
        [JsonPropertyName("gaid")]
        public string? Gaid { get; set; }

        /// <summary>
        /// BCP 47 language identifier (for web and app events)
        /// </summary>
        [JsonPropertyName("locale")]
        public string? Locale { get; set; }

        /// <summary>
        /// App tracking transparency status (for app events only, iOS)
        /// Enum: AUTHORIZED, DENIED, NOT_DETERMINED, RESTRICTED, NOT_APPLICABLE
        /// </summary>
        [JsonPropertyName("att_status")]
        public string? AttStatus { get; set; }
    }

    #endregion

    #region Properties Models

    /// <summary>
    /// Properties information for Events API 2.0
    /// </summary>
    public class Events2Properties
    {
        /// <summary>
        /// Unique ID or array of multiple IDs of products or content
        /// </summary>
        [JsonPropertyName("content_ids")]
        public List<string>? ContentIds { get; set; }

        /// <summary>
        /// Relevant products in an event with product information
        /// </summary>
        [JsonPropertyName("contents")]
        public List<Events2ContentItem>? Contents { get; set; }

        /// <summary>
        /// Type of content in the event (product, product_group)
        /// </summary>
        [JsonPropertyName("content_type")]
        public string? ContentType { get; set; }

        /// <summary>
        /// ISO 4217 currency code
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// Value of the order or items sold
        /// </summary>
        [JsonPropertyName("value")]
        public double? Value { get; set; }

        /// <summary>
        /// Quantity of items
        /// </summary>
        [JsonPropertyName("num_items")]
        public int? NumItems { get; set; }

        /// <summary>
        /// User-entered string for search
        /// </summary>
        [JsonPropertyName("search_string")]
        public string? SearchString { get; set; }

        /// <summary>
        /// Description of the item or page
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Order ID
        /// </summary>
        [JsonPropertyName("order_id")]
        public string? OrderId { get; set; }

        /// <summary>
        /// Shop ID
        /// </summary>
        [JsonPropertyName("shop_id")]
        public string? ShopId { get; set; }
    }

    /// <summary>
    /// Content item for Events API 2.0
    /// </summary>
    public class Events2ContentItem
    {
        /// <summary>
        /// Price of the item
        /// </summary>
        [JsonPropertyName("price")]
        public double? Price { get; set; }

        /// <summary>
        /// Unique ID of the product or content
        /// </summary>
        [JsonPropertyName("content_id")]
        public string? ContentId { get; set; }

        /// <summary>
        /// Category of the page or product
        /// </summary>
        [JsonPropertyName("content_category")]
        public string? ContentCategory { get; set; }

        /// <summary>
        /// Name of the page or product
        /// </summary>
        [JsonPropertyName("content_name")]
        public string? ContentName { get; set; }

        /// <summary>
        /// Brand name of the product item
        /// </summary>
        [JsonPropertyName("brand")]
        public string? Brand { get; set; }
    }

    #endregion

    #region Page Information Models

    /// <summary>
    /// Page information for web events
    /// </summary>
    public class Events2PageInfo
    {
        /// <summary>
        /// Browser URL where the event happened (required for web events)
        /// </summary>
        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// Referrer URL
        /// </summary>
        [JsonPropertyName("referrer")]
        public string? Referrer { get; set; }
    }

    #endregion

    #region App Information Models

    /// <summary>
    /// App information for app events
    /// </summary>
    public class Events2AppInfo
    {
        /// <summary>
        /// Mobile App ID (required for app events)
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// Application name
        /// </summary>
        [JsonPropertyName("app_name")]
        public string? AppName { get; set; }

        /// <summary>
        /// App version number
        /// </summary>
        [JsonPropertyName("app_version")]
        public string? AppVersion { get; set; }
    }

    #endregion

    #region Ad Information Models

    /// <summary>
    /// Ad information for app events
    /// </summary>
    public class Events2AdInfo
    {
        /// <summary>
        /// Callback information to help attribute events
        /// </summary>
        [JsonPropertyName("callback")]
        public string? Callback { get; set; }

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// Ad ID (Creative ID)
        /// </summary>
        [JsonPropertyName("creative_id")]
        public string? CreativeId { get; set; }

        /// <summary>
        /// Whether the user is a retargeting user
        /// </summary>
        [JsonPropertyName("is_retargeting")]
        public bool? IsRetargeting { get; set; }

        /// <summary>
        /// Whether the event is attributed
        /// </summary>
        [JsonPropertyName("attributed")]
        public bool? Attributed { get; set; }

        /// <summary>
        /// Attribution type
        /// </summary>
        [JsonPropertyName("attribution_type")]
        public string? AttributionType { get; set; }

        /// <summary>
        /// Attribution provider
        /// </summary>
        [JsonPropertyName("attribution_provider")]
        public string? AttributionProvider { get; set; }
    }

    #endregion

    #region Lead Information Models

    /// <summary>
    /// Lead information for CRM events
    /// </summary>
    public class Events2LeadInfo
    {
        /// <summary>
        /// ID of TikTok leads (required for CRM events)
        /// </summary>
        [JsonPropertyName("lead_id")]
        public string LeadId { get; set; } = string.Empty;

        /// <summary>
        /// Lead event source (e.g., HubSpot, Salesforce)
        /// </summary>
        [JsonPropertyName("lead_event_source")]
        public string? LeadEventSource { get; set; }
    }

    #endregion

    #region Enums and Constants

    /// <summary>
    /// Event source types for Events API 2.0
    /// </summary>
    public static class Events2EventSource
    {
        /// <summary>
        /// Web events measured by Pixel Code
        /// </summary>
        public const string Web = "web";

        /// <summary>
        /// App events measured by TikTok App ID
        /// </summary>
        public const string App = "app";

        /// <summary>
        /// Offline events measured by Offline Event Set ID
        /// </summary>
        public const string Offline = "offline";

        /// <summary>
        /// CRM events measured by CRM Event Set ID
        /// </summary>
        public const string Crm = "crm";
    }

    /// <summary>
    /// App Tracking Transparency status values
    /// </summary>
    public static class Events2AttStatus
    {
        /// <summary>
        /// User has authorized access to app-related data
        /// </summary>
        public const string Authorized = "AUTHORIZED";

        /// <summary>
        /// User has not agreed to authorize access
        /// </summary>
        public const string Denied = "DENIED";

        /// <summary>
        /// User has not yet received an authorization request
        /// </summary>
        public const string NotDetermined = "NOT_DETERMINED";

        /// <summary>
        /// Authorization is restricted
        /// </summary>
        public const string Restricted = "RESTRICTED";

        /// <summary>
        /// iOS version is below 14 or device is running Android
        /// </summary>
        public const string NotApplicable = "NOT_APPLICABLE";
    }

    /// <summary>
    /// Content types for Events API 2.0
    /// </summary>
    public static class Events2ContentType
    {
        /// <summary>
        /// Individual product
        /// </summary>
        public const string Product = "product";

        /// <summary>
        /// Product group
        /// </summary>
        public const string ProductGroup = "product_group";
    }

    #endregion
}
