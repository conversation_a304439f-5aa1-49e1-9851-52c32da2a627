/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Exceptions;

namespace TikTokBusinessApi.Utilities
{
    /// <summary>
    /// Configurable retry policy utility for failed requests
    /// </summary>
    public class RetryPolicy
    {
        private readonly RetryPolicyOptions _options;
        private readonly ILogger<RetryPolicy>? _logger;

        /// <summary>
        /// Initializes a new instance of the RetryPolicy class
        /// </summary>
        /// <param name="options">Retry policy configuration options</param>
        /// <param name="logger">Logger instance (optional)</param>
        public RetryPolicy(RetryPolicyOptions? options = null, ILogger<RetryPolicy>? logger = null)
        {
            _options = options ?? new RetryPolicyOptions();
            _logger = logger;
        }

        /// <summary>
        /// Execute an operation with retry logic
        /// </summary>
        /// <typeparam name="T">Return type of the operation</typeparam>
        /// <param name="operation">Operation to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        public async Task<T> ExecuteAsync<T>(
            Func<CancellationToken, Task<T>> operation,
            CancellationToken cancellationToken = default)
        {
            if (operation == null)
                throw new ArgumentNullException(nameof(operation));

            var attempt = 0;
            var exceptions = new List<Exception>();

            while (attempt <= _options.MaxRetries)
            {
                try
                {
                    if (attempt > 0)
                    {
                        _logger?.LogDebug("Retry attempt {Attempt} of {MaxRetries}", attempt, _options.MaxRetries);
                    }

                    var result = await operation(cancellationToken);
                    
                    if (attempt > 0)
                    {
                        _logger?.LogInformation("Operation succeeded on retry attempt {Attempt}", attempt);
                    }

                    return result;
                }
                catch (Exception ex) when (ShouldRetry(ex, attempt))
                {
                    exceptions.Add(ex);
                    attempt++;

                    if (attempt <= _options.MaxRetries)
                    {
                        var delay = CalculateDelay(attempt);
                        _logger?.LogWarning(ex, "Operation failed on attempt {Attempt}, retrying in {Delay}ms. Error: {Error}", 
                            attempt, delay.TotalMilliseconds, ex.Message);

                        await Task.Delay(delay, cancellationToken);
                    }
                    else
                    {
                        _logger?.LogError(ex, "Operation failed after {MaxRetries} retries", _options.MaxRetries);
                    }
                }
            }

            // All retries exhausted, throw aggregate exception
            throw new AggregateException($"Operation failed after {_options.MaxRetries} retries", exceptions);
        }

        /// <summary>
        /// Execute an operation with retry logic (void return)
        /// </summary>
        /// <param name="operation">Operation to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the operation</returns>
        public async Task ExecuteAsync(
            Func<CancellationToken, Task> operation,
            CancellationToken cancellationToken = default)
        {
            await ExecuteAsync(async ct =>
            {
                await operation(ct);
                return true; // Dummy return value
            }, cancellationToken);
        }

        /// <summary>
        /// Determine if an exception should trigger a retry
        /// </summary>
        /// <param name="exception">Exception that occurred</param>
        /// <param name="attempt">Current attempt number</param>
        /// <returns>True if should retry, false otherwise</returns>
        private bool ShouldRetry(Exception exception, int attempt)
        {
            if (attempt >= _options.MaxRetries)
                return false;

            // Check if exception type is retryable
            if (!IsRetryableException(exception))
            {
                _logger?.LogDebug("Exception type {ExceptionType} is not retryable", exception.GetType().Name);
                return false;
            }

            // Check custom retry condition if provided
            if (_options.RetryCondition != null)
            {
                var shouldRetry = _options.RetryCondition(exception, attempt);
                _logger?.LogDebug("Custom retry condition returned {ShouldRetry} for attempt {Attempt}", shouldRetry, attempt);
                return shouldRetry;
            }

            return true;
        }

        /// <summary>
        /// Check if an exception type is retryable
        /// </summary>
        /// <param name="exception">Exception to check</param>
        /// <returns>True if retryable, false otherwise</returns>
        private bool IsRetryableException(Exception exception)
        {
            return exception switch
            {
                // Network-related exceptions
                HttpRequestException => true,
                TaskCanceledException when !exception.Message.Contains("timeout") => false, // User cancellation
                TaskCanceledException => true, // Timeout
                SocketException => true,
                
                // TikTok API specific exceptions
                ApiException apiEx => IsRetryableApiException(apiEx),
                SdkException tikTokEx => IsRetryableTikTokException(tikTokEx),
                
                // Circuit breaker
                CircuitBreakerOpenException => false, // Don't retry when circuit is open
                
                // Generic exceptions that might be transient
                InvalidOperationException => false,
                ArgumentException => false,
                
                _ => _options.RetryableExceptionTypes.Contains(exception.GetType())
            };
        }

        /// <summary>
        /// Check if an API exception is retryable based on status code
        /// </summary>
        /// <param name="apiException">API exception to check</param>
        /// <returns>True if retryable, false otherwise</returns>
        private bool IsRetryableApiException(ApiException apiException)
        {
            if (apiException.StatusCode == 0)
                return true; // Network error, likely retryable

            var statusCode = (HttpStatusCode)apiException.StatusCode;
            
            return statusCode switch
            {
                // Server errors are generally retryable
                HttpStatusCode.InternalServerError => true,
                HttpStatusCode.BadGateway => true,
                HttpStatusCode.ServiceUnavailable => true,
                HttpStatusCode.GatewayTimeout => true,
                
                // Rate limiting
                HttpStatusCode.TooManyRequests => true,
                
                // Client errors are generally not retryable
                HttpStatusCode.BadRequest => false,
                HttpStatusCode.Unauthorized => false,
                HttpStatusCode.Forbidden => false,
                HttpStatusCode.NotFound => false,
                HttpStatusCode.Conflict => false,
                HttpStatusCode.UnprocessableEntity => false,
                
                _ => false
            };
        }

        /// <summary>
        /// Check if a TikTok-specific exception is retryable
        /// </summary>
        /// <param name="tikTokException">TikTok exception to check</param>
        /// <returns>True if retryable, false otherwise</returns>
        private bool IsRetryableTikTokException(SdkException tikTokException)
        {
            // Check TikTok-specific error codes
            return tikTokException.ErrorCode switch
            {
                // Rate limiting errors
                40100 => true, // Rate limit exceeded
                40101 => true, // Quota exceeded

                // Server errors
                50000 => true, // Internal server error
                50001 => true, // Service unavailable

                // Authentication errors (not retryable)
                40001 => false, // Invalid access token
                40002 => false, // Access token expired
                40003 => false, // Invalid app ID

                // Permission errors (not retryable)
                40301 => false, // No permission
                40302 => false, // Insufficient permissions

                // Validation errors (not retryable)
                40000 => false, // Invalid request
                40004 => false, // Invalid parameters

                _ => false // Unknown error codes are not retried by default
            };
        }

        /// <summary>
        /// Calculate delay for the next retry attempt
        /// </summary>
        /// <param name="attempt">Current attempt number</param>
        /// <returns>Delay duration</returns>
        private TimeSpan CalculateDelay(int attempt)
        {
            return _options.DelayStrategy switch
            {
                RetryDelayStrategy.Fixed => _options.BaseDelay,
                RetryDelayStrategy.Linear => TimeSpan.FromMilliseconds(_options.BaseDelay.TotalMilliseconds * attempt),
                RetryDelayStrategy.Exponential => TimeSpan.FromMilliseconds(_options.BaseDelay.TotalMilliseconds * Math.Pow(2, attempt - 1)),
                RetryDelayStrategy.ExponentialWithJitter => CalculateExponentialWithJitter(attempt),
                _ => _options.BaseDelay
            };
        }

        /// <summary>
        /// Calculate exponential delay with jitter to avoid thundering herd
        /// </summary>
        /// <param name="attempt">Current attempt number</param>
        /// <returns>Delay duration with jitter</returns>
        private TimeSpan CalculateExponentialWithJitter(int attempt)
        {
            var exponentialDelay = _options.BaseDelay.TotalMilliseconds * Math.Pow(2, attempt - 1);
            var jitter = Random.Shared.NextDouble() * 0.1 * exponentialDelay; // 10% jitter
            var totalDelay = exponentialDelay + jitter;
            
            // Cap at maximum delay
            var maxDelay = _options.MaxDelay?.TotalMilliseconds ?? double.MaxValue;
            totalDelay = Math.Min(totalDelay, maxDelay);
            
            return TimeSpan.FromMilliseconds(totalDelay);
        }
    }

    /// <summary>
    /// Configuration options for retry policy
    /// </summary>
    public class RetryPolicyOptions
    {
        /// <summary>
        /// Maximum number of retry attempts
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// Base delay between retry attempts
        /// </summary>
        public TimeSpan BaseDelay { get; set; } = TimeSpan.FromSeconds(1);

        /// <summary>
        /// Maximum delay between retry attempts (for exponential strategies)
        /// </summary>
        public TimeSpan? MaxDelay { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// Delay calculation strategy
        /// </summary>
        public RetryDelayStrategy DelayStrategy { get; set; } = RetryDelayStrategy.ExponentialWithJitter;

        /// <summary>
        /// Custom retry condition function
        /// </summary>
        public Func<Exception, int, bool>? RetryCondition { get; set; }

        /// <summary>
        /// Additional exception types that should trigger retries
        /// </summary>
        public HashSet<Type> RetryableExceptionTypes { get; set; } = new();
    }

    /// <summary>
    /// Retry delay calculation strategies
    /// </summary>
    public enum RetryDelayStrategy
    {
        /// <summary>
        /// Fixed delay between retries
        /// </summary>
        Fixed,

        /// <summary>
        /// Linear increase in delay (delay * attempt)
        /// </summary>
        Linear,

        /// <summary>
        /// Exponential backoff (delay * 2^attempt)
        /// </summary>
        Exponential,

        /// <summary>
        /// Exponential backoff with random jitter to avoid thundering herd
        /// </summary>
        ExponentialWithJitter
    }
}
