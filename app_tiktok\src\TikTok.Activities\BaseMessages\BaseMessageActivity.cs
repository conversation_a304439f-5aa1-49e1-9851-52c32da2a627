﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Custom.BaseActivities;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using TikTok.BaseMessages;

namespace TikTok.Activities.BaseMessages
{
    public abstract class BaseMessageActivity : UnitOfWorkActivity
    {
        [ActivityInput(Hint = "Message Input", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public BaseMessageActivityInput Input { get; set; }
    }
}
