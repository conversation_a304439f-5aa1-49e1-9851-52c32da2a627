/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Smart Plus operations
    /// </summary>
    public class SmartPlusApi : ISmartPlusApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<SmartPlusApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the SmartPlusApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public SmartPlusApi(IApiClient apiClient, ILogger<SmartPlusApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get the dynamic quota on the number of active Smart+ Campaigns within an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing Smart+ Campaign quota information</returns>
        public async Task<Response<SmartPlusQuotaResponse>> GetSmartPlusQuotaAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogInformation("Getting Smart+ Campaign quota for advertiser {AdvertiserId}", advertiserId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId
            };

            return await _apiClient.CallApiAsync<Response<SmartPlusQuotaResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SmartPlusEndpoints.GetSmartPlusQuota}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <summary>
        // /// Create a Smart+ Campaign
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing Smart+ Campaign information</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing created Smart+ Campaign information</returns>
        // public async Task<Response<SmartPlusResponse>> CreateSmartPlusCampaignAsync(
        //     string accessToken,
        //     SmartPlusCreateBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Creating Smart+ Campaign for advertiser {AdvertiserId}", body.AdvertiserId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<SmartPlusResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SmartPlusEndpoints.CreateSmartPlusCampaign}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <summary>
        // /// Update a Smart+ Campaign
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing Smart+ Campaign information to update</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing updated Smart+ Campaign information</returns>
        // public async Task<Response<SmartPlusResponse>> UpdateSmartPlusCampaignAsync(
        //     string accessToken,
        //     SmartPlusUpdateBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Updating Smart+ Campaign {CampaignId} for advertiser {AdvertiserId}", 
        //         body.CampaignId, body.AdvertiserId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<SmartPlusResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SmartPlusEndpoints.UpdateSmartPlusCampaign}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get detailed information of Smart+ Campaigns and Smart Performance Web Campaigns
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="campaignIds">A list of campaign IDs (max size: 100)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing Smart+ Campaign information</returns>
        public async Task<Response<SmartPlusListResponse>> GetSmartPlusCampaignsAsync(
            string accessToken,
            string advertiserId,
            List<string> campaignIds,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (campaignIds == null || campaignIds.Count == 0)
                throw new ArgumentException("Campaign IDs cannot be null or empty", nameof(campaignIds));
            if (campaignIds.Count > 100)
                throw new ArgumentException("Campaign IDs list cannot exceed 100 items", nameof(campaignIds));

            _logger?.LogInformation("Getting Smart+ Campaigns for advertiser {AdvertiserId}", advertiserId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["campaign_ids"] = $"[\"{string.Join("\",\"", campaignIds)}\"]"
            };

            return await _apiClient.CallApiAsync<Response<SmartPlusListResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SmartPlusEndpoints.GetSmartPlusCampaigns}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Run a report on Smart+ Campaigns and Smart Performance Web Campaigns at the creative level
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="campaignIds">List of campaign IDs (max size: 1)</param>
        /// <param name="materialType">Creative material type</param>
        /// <param name="isIncludeRemovedMaterial">Whether to query data of deleted videos, texts, or posts</param>
        /// <param name="metrics">Metrics to query</param>
        /// <param name="queryLifetime">Whether to request the lifetime metrics</param>
        /// <param name="startDate">Query start date (required when queryLifetime is false)</param>
        /// <param name="endDate">Query end date (required when queryLifetime is false)</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size (1-100)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing Smart+ Campaign report data</returns>
        public async Task<Response<SmartPlusReportResponse>> GetSmartPlusReportAsync(
            string accessToken,
            string advertiserId,
            List<string> campaignIds,
            string materialType,
            bool? isIncludeRemovedMaterial = null,
            List<string>? metrics = null,
            bool? queryLifetime = null,
            string? startDate = null,
            string? endDate = null,
            int? page = null,
            int? pageSize = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (campaignIds == null || campaignIds.Count == 0)
                throw new ArgumentException("Campaign IDs cannot be null or empty", nameof(campaignIds));
            if (campaignIds.Count > 1)
                throw new ArgumentException("Campaign IDs list cannot exceed 1 item for reports", nameof(campaignIds));
            if (string.IsNullOrWhiteSpace(materialType))
                throw new ArgumentException("Material type cannot be null or empty", nameof(materialType));

            _logger?.LogInformation("Getting Smart+ Campaign report for advertiser {AdvertiserId} and material type {MaterialType}",
                advertiserId, materialType);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["campaign_ids"] = $"[\"{string.Join("\",\"", campaignIds)}\"]",
                ["material_type"] = materialType
            };

            if (isIncludeRemovedMaterial.HasValue)
                queryParams["is_include_removed_material"] = isIncludeRemovedMaterial.Value.ToString().ToLower();

            if (metrics != null && metrics.Count > 0)
                queryParams["metrics"] = $"[\"{string.Join("\",\"", metrics)}\"]";

            if (queryLifetime.HasValue)
                queryParams["query_lifetime"] = queryLifetime.Value.ToString().ToLower();

            if (!string.IsNullOrWhiteSpace(startDate))
                queryParams["start_date"] = startDate;

            if (!string.IsNullOrWhiteSpace(endDate))
                queryParams["end_date"] = endDate;

            if (page.HasValue)
                queryParams["page"] = page.Value.ToString();

            if (pageSize.HasValue)
                queryParams["page_size"] = pageSize.Value.ToString();

            return await _apiClient.CallApiAsync<Response<SmartPlusReportResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SmartPlusEndpoints.GetSmartPlusReport}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
