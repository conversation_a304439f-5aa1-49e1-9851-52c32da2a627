using TikTok.Enums;
using TikTokBusinessApi.Models;

namespace TikTok.DataSync
{
    /// <summary>
    /// DTO chứa dữ liệu báo cáo chi tiết cấp creative GMV Max Product Campaign
    /// </summary>
    public class GmvMaxProductCreativeReportData
    {
        /// <summary>
        /// Dữ liệu báo cáo từ TikTok API
        /// </summary>
        public GMVMaxReportItem ReportItem { get; set; }

        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID TikTok Shop
        /// </summary>
        public string StoreId { get; set; }

        /// <summary>
        /// ID chiến dịch GMV Max
        /// </summary>
        public string CampaignId { get; set; }

        /// <summary>
        /// ID nhóm sản phẩm
        /// </summary>
        public string ItemGroupId { get; set; }

        public CreativeType CreativeType { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public GmvMaxProductCreativeReportData()
        {
            ReportItem = new GMVMaxReportItem();
            AdvertiserId = string.Empty;
            StoreId = string.Empty;
            CampaignId = string.Empty;
            ItemGroupId = string.Empty;
        }

        /// <summary>
        /// Constructor với tham số
        /// </summary>
        /// <param name="reportItem">Dữ liệu báo cáo từ TikTok API</param>
        /// <param name="advertiserId">ID nhà quảng cáo</param>
        /// <param name="storeId">ID TikTok Shop</param>
        /// <param name="campaignId">ID chiến dịch GMV Max</param>
        /// <param name="itemGroupId">ID nhóm sản phẩm</param>
        public GmvMaxProductCreativeReportData(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId, string itemGroupId)
        {
            ReportItem = reportItem;
            AdvertiserId = advertiserId;
            StoreId = storeId;
            CampaignId = campaignId;
            ItemGroupId = itemGroupId;
        }
    }
} 