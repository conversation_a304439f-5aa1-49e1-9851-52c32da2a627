/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Images endpoints
    /// </summary>
    public static class ImagesEndpoints
    {
        /// <summary>
        /// Upload an image endpoint (v1.3)
        /// </summary>
        public const string UploadImage = "/file/image/ad/upload/";

        /// <summary>
        /// Update the name of an image endpoint (v1.3)
        /// </summary>
        public const string UpdateImage = "/file/image/ad/update/";

        /// <summary>
        /// Get info about images endpoint (v1.3)
        /// </summary>
        public const string GetImageInfo = "/file/image/ad/info/";

        /// <summary>
        /// Search for images endpoint (v1.3)
        /// </summary>
        public const string SearchImages = "/file/image/ad/search/";
    }
}
