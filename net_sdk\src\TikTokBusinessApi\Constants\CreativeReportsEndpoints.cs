/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Creative Reports endpoints
    /// </summary>
    public static class CreativeReportsEndpoints
    {
        /// <summary>
        /// Get creative basic reports endpoint (v1.3)
        /// </summary>
        public const string GetCreativeReports = "/creative/report/get/";
    }
}
