using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.Customers
{
    /// <summary>
    /// DTO cho việc hiển thị tài khoản khách hàng dạng bảng dàn phẳng
    /// </summary>
    public class CustomerAdAccountFlatDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID của khách hàng
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// Mã khách hàng
        /// </summary>
        public string CustomerIdCode { get; set; }

        /// <summary>
        /// Tên khách hàng
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// ID của advertiser
        /// </summary>
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Tên của advertiser
        /// </summary>
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// ID của shop
        /// </summary>
        public string? ShopId { get; set; }

        /// <summary>
        /// Tên của shop
        /// </summary>
        public string? ShopName { get; set; }
    }

    /// <summary>
    /// DTO cho việc tìm kiếm tài khoản khách hàng
    /// </summary>
    public class GetCustomerAdAccountListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Từ khóa tìm kiếm
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// ID của khách hàng
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// ID của advertiser
        /// </summary>
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// ID của shop
        /// </summary>
        public string? ShopId { get; set; }
    }
}
