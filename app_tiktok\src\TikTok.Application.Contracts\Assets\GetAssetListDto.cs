using System;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.Assets
{
    /// <summary>
    /// DTO để lấy danh sách tài sản
    /// </summary>
    public class GetAssetListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Asset ID
        /// </summary>
        public string? AssetId { get; set; }

        /// <summary>
        /// Tên tài sản
        /// </summary>
        public string? AssetName { get; set; }

        /// <summary>
        /// Loại tài sản
        /// </summary>
        public AssetType? AssetType { get; set; }

        /// <summary>
        /// Business Center ID
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// Loại tài khoản quảng cáo
        /// </summary>
        public AdAccountType? AdvertiserAccountType { get; set; }

        /// <summary>
        /// Vai trò quảng cáo
        /// </summary>
        public AdvertiserRole? AdvertiserRole { get; set; }

        /// <summary>
        /// Vai trò danh mục
        /// </summary>
        public CatalogRole? CatalogRole { get; set; }

        /// <summary>
        /// Có thể tạo quảng cáo
        /// </summary>
        public AdCreationEligible? AdCreationEligible { get; set; }

        /// <summary>
        /// Vai trò cửa hàng
        /// </summary>
        public StoreRole? StoreRole { get; set; }

        /// <summary>
        /// Tên BC sở hữu
        /// </summary>
        public string? OwnerBcName { get; set; }

        /// <summary>
        /// Cờ đánh dấu đã bị xóa khỏi Business Center
        /// </summary>
        public bool? IsRemoved { get; set; }

        /// <summary>
        /// Thời gian xóa khỏi Business Center (UTC)
        /// </summary>
        public DateTime? RemovedAt { get; set; }

        /// <summary>
        /// Loại quan hệ giữa Business Center và tài sản
        /// </summary>
        public RelationType? RelationType { get; set; }

        /// <summary>
        /// Trạng thái quan hệ giữa tài khoản Business Center và tài sản
        /// </summary>
        public RelationStatus? RelationStatus { get; set; }

        /// <summary>
        /// Trạng thái tài khoản quảng cáo
        /// </summary>
        public AdAccountStatus? AdvertiserStatus { get; set; }
    }
}