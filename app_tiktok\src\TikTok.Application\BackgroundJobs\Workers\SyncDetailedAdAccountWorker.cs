using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Worker để đồng bộ chi tiết tài khoản quảng cáo
    /// </summary>
    public class SyncDetailedAdAccountWorker : WorkerJobBase
    {
        private readonly IAdAccountSyncService _adAccountSyncService;

        public SyncDetailedAdAccountWorker(
            ILogger<SyncDetailedAdAccountWorker> logger,
            IServiceProvider serviceProvider,
            IAdAccountSyncService adAccountSyncService)
            : base(logger, serviceProvider)
        {
            _adAccountSyncService = adAccountSyncService;
        }

        public override CommandType CommandType => CommandType.SyncDetailedAdAccount;

        protected override async Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job)
        {
            // Parse parameters
            var parameters = ParseParameters(args.Parameters);

            // Sync Detailed AdAccount
            AdAccountSyncResult responseSync = null;

            if (!string.IsNullOrEmpty(parameters.BcId))
            {
                // Đồng bộ theo BC ID
                responseSync = await _adAccountSyncService.SyncAdAccountsAsync(parameters.BcId);
            }

            if (responseSync != null)
            {
                if (responseSync.IsSuccess)
                {
                    return JobResult.Success(JsonConvert.SerializeObject(new
                    {
                        BcId = parameters.BcId,
                        SyncDate = parameters.SyncDate,
                        SyncResult = responseSync,
                        Message = "Đồng bộ chi tiết tài khoản quảng cáo thành công"
                    }));
                }
                else
                {
                    return JobResult.Error(responseSync.ErrorMessage ?? string.Empty, JsonConvert.SerializeObject(responseSync));
                }
            }
            else
            {
                return JobResult.Error("Lỗi đồng bộ chi tiết tài khoản quảng cáo");
            }
        }

        /// <summary>
        /// Parameters cho SyncDetailedAdAccount
        /// </summary>
        public class SyncDetailedAdAccountParameters:DefaultParameters
        {
            /// <summary>
            /// ID của Advertiser (optional)
            /// </summary>
            public string AdvertiserId { get; set; }

        }
    }
}
