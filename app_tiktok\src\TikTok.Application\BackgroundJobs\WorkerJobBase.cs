using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using static TikTok.BackgroundJobs.Workers.SyncBalanceWorker;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Abstract base class cho worker jobs
    /// </summary>
    public abstract class WorkerJobBase : IWorkerJob, ITransientDependency
    {
        protected readonly ILogger _logger;
        protected readonly IServiceProvider _serviceProvider;
        protected readonly IJobRepository _jobRepository;

        protected const int MAX_PAGE_SIZE = 500;

        protected WorkerJobBase(
            ILogger logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _jobRepository = serviceProvider.GetRequiredService<IJobRepository>();
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Loạ<PERSON> lệnh mà worker này xử lý
        /// </summary>
        public abstract CommandType CommandType { get; }

        /// <summary>
        /// Xử lý công việc với logic chung
        /// </summary>
        /// <param name="args">Arguments</param>
        /// <returns>Kết quả xử lý</returns>
        public async Task<JobResult> ExecuteAsync(WorkerJobArgs args)
        {
            var stopwatch = Stopwatch.StartNew();

            _logger.LogDebug("Starting {WorkerType} for job {JobId} with worker {WorkerId}",
                GetType().Name, args.JobId, args.WorkerId);

            // Tiền xử lý: Lấy ra JobEntity
            var job = await _jobRepository.GetAsync(args.JobId);
            if (job == null)
            {
                return JobResult.Error($"Job with ID {args.JobId} not found", stopwatch.ElapsedMilliseconds);
            }

            // Kiểm tra trạng thái job
            if (job.Status != JobStatus.InProcess)
            {
                return JobResult.Error($"Job {args.JobId} is not in InProcess status. Current status: {job.Status}",
                    stopwatch.ElapsedMilliseconds);
            }

            // Kiểm tra CommandType có khớp không
            if (job.CommandType != args.CommandType)
            {
                return JobResult.Error($"Job {args.JobId} CommandType mismatch. Expected: {args.CommandType}, Actual: {job.CommandType}",
                    stopwatch.ElapsedMilliseconds);
            }

            try
            {
                // Thực hiện xử lý cụ thể của worker
                var result = await WorkerExecuteAsync(args, job);

                // Hậu xử lý: Cập nhật trạng thái công việc
                await PostProcessJobAsync(job, result);
                stopwatch.Stop();
                _logger.LogDebug("{WorkerType} completed for job {JobId} in {ElapsedMs}ms",
                GetType().Name, args.JobId, stopwatch.ElapsedMilliseconds);
                return result;
            }
            catch (Exception ex)
            {
                // Cập nhật trạng thái công việc thành Error
                job.MarkAsError(ex.Message);
                await _jobRepository.UpdateAsync(job);
                stopwatch.Stop();
                _logger.LogDebug("{WorkerType} failed for job {JobId} in {ElapsedMs}ms", GetType().Name, args.JobId, stopwatch.ElapsedMilliseconds);
                return JobResult.Error(ex.Message, stopwatch.ElapsedMilliseconds);
            }
        }

        /// <summary>
        /// Xử lý công việc cụ thể của worker (implement bởi derived class)
        /// </summary>
        /// <param name="args">Arguments</param>
        /// <param name="job">Job entity</param>
        /// <returns>Kết quả xử lý</returns>
        protected abstract Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job);

        /// <summary>
        /// Hậu xử lý: Cập nhật trạng thái công việc
        /// </summary>
        /// <param name="job">Job entity</param>
        /// <param name="result">Kết quả xử lý</param>
        protected virtual async Task PostProcessJobAsync(JobEntity job, JobResult result)
        {
            try
            {
                if (result.IsSuccess)
                {
                    // Cập nhật trạng thái công việc thành Completed
                    job.Complete(result.ResultData);
                    await _jobRepository.UpdateAsync(job);

                    _logger.LogDebug("Job {JobId} completed successfully", job.Id);
                }
                else
                {
                    // Đánh dấu lỗi cho công việc
                    job.MarkAsError(result.ErrorMessage);
                    await _jobRepository.UpdateAsync(job);

                    _logger.LogError("Job {JobId} failed: {ErrorMessage}", job.Id, result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to post-process job {JobId}", job.Id);
            }
        }

        /// <summary>
        /// Parse parameters từ JSON
        /// </summary>
        /// <param name="parametersJson">JSON parameters</param>
        /// <returns>Parameters object</returns>
        protected T ParseParameters<T>(string parametersJson) where T : class, new()
        {
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<T>(parametersJson)
                    ?? new T();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse parameters, using defaults");
                return new T();
            }
        }

        protected DefaultParameters ParseParameters(string parametersJson)
        {
            return ParseParameters<DefaultParameters>(parametersJson);
        }
    }
}