/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Create MMM Data Request Models

    /// <summary>
    /// Request for creating a Media Mix Modeling (MMM) data request
    /// </summary>
    public class MmmCreateRequest
    {
        /// <summary>
        /// A list of advertiser IDs to request MMM data for.
        /// We recommend limiting the number of advertiser IDs to 5.
        /// </summary>
        [JsonPropertyName("advertiser_ids")]
        [Required]
        public List<string> AdvertiserIds { get; set; } = new();

        /// <summary>
        /// Details of the data request
        /// </summary>
        [JsonPropertyName("request_info")]
        [Required]
        public MmmRequestInfo RequestInfo { get; set; } = new();

        /// <summary>
        /// The email address to receive the notification when the data is ready for download
        /// </summary>
        [JsonPropertyName("email")]
        public string? Email { get; set; }
    }

    /// <summary>
    /// Details of the MMM data request
    /// </summary>
    public class MmmRequestInfo
    {
        /// <summary>
        /// Details of the ad accounts and campaigns
        /// </summary>
        [JsonPropertyName("targets")]
        [Required]
        public List<MmmTarget> Targets { get; set; } = new();

        /// <summary>
        /// Start date, in the format of YYYY-MM-DD (UTC+0 time).
        /// The start date cannot be earlier than four years ago.
        /// </summary>
        [JsonPropertyName("from_date")]
        [Required]
        public string FromDate { get; set; } = string.Empty;

        /// <summary>
        /// End date, in the format of YYYY-MM-DD (UTC+0 time)
        /// </summary>
        [JsonPropertyName("to_date")]
        [Required]
        public string ToDate { get; set; } = string.Empty;

        /// <summary>
        /// Two-letter code of the country or region.
        /// If this field is not specified, data for all countries and regions will be returned.
        /// </summary>
        [JsonPropertyName("country_code")]
        public List<string>? CountryCode { get; set; }

        /// <summary>
        /// Media type. Enum values: All (recommended), Paid, Earned.
        /// Default value: All
        /// </summary>
        [JsonPropertyName("media_type")]
        public string? MediaType { get; set; }
    }

    /// <summary>
    /// Details of the ad accounts and campaigns for MMM data request
    /// </summary>
    public class MmmTarget
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The ID list of campaigns within the ad account to filter the data by.
        /// If you don't specify campaign_ids, data for all campaigns within the ad account will be returned.
        /// </summary>
        [JsonPropertyName("campaign_ids")]
        public List<string>? CampaignIds { get; set; }

        /// <summary>
        /// Brand. You can use this field to distinguish between data requests for different brands.
        /// If you specify this field, the value will be filled in the "Brand" column of the data file.
        /// </summary>
        [JsonPropertyName("brand")]
        public string? Brand { get; set; }

        /// <summary>
        /// Sub-brand. You can use this field to distinguish between data requests for different sub-brands.
        /// If you specify this field, the value will be filled in the "Sub-brand" column of the data file.
        /// </summary>
        [JsonPropertyName("sub_brand")]
        public string? SubBrand { get; set; }
    }

    /// <summary>
    /// Response for creating a Media Mix Modeling (MMM) data request
    /// </summary>
    public class MmmCreateResponse
    {
        /// <summary>
        /// Unique identifier for the MMM data request.
        /// To check the data request status, wait for up to 24 hours for the data processing to complete,
        /// and then pass this value to mmm_request_id in /mmm/api/check/
        /// </summary>
        [JsonPropertyName("mmm_request_id")]
        public string MmmRequestId { get; set; } = string.Empty;
    }

    #endregion

    #region Check MMM Data Request Status Models

    /// <summary>
    /// Request for checking the status of a Media Mix Modeling (MMM) data request
    /// </summary>
    public class MmmCheckRequest
    {
        /// <summary>
        /// Unique identifier for the MMM data request
        /// </summary>
        [JsonPropertyName("mmm_request_id")]
        [Required]
        public string MmmRequestId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for checking the status of a Media Mix Modeling (MMM) data request
    /// </summary>
    public class MmmCheckResponse
    {
        /// <summary>
        /// Status of the data request.
        /// Enum values: "request received", "in progress", "completed - ready for download", "failed"
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Returned only when status is "failed".
        /// The reason why the data request failed.
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }
    }

    #endregion

    #region Get MMM Data Download URL Models

    /// <summary>
    /// Request for obtaining the download URL for Media Mix Modeling (MMM) data
    /// </summary>
    public class MmmDownloadRequest
    {
        /// <summary>
        /// Unique identifier for the MMM data request
        /// </summary>
        [JsonPropertyName("mmm_request_id")]
        [Required]
        public string MmmRequestId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for obtaining the download URL for Media Mix Modeling (MMM) data
    /// </summary>
    public class MmmDownloadResponse
    {
        /// <summary>
        /// A URL to download the compressed CSV file for MMM data.
        /// Validity period: seven days.
        /// Once the URL expires, you need to call /mmm/api/download/ to obtain a new URL.
        /// </summary>
        [JsonPropertyName("download_url")]
        public string DownloadUrl { get; set; } = string.Empty;
    }

    #endregion

    #region Get MMM Data Request History Models

    /// <summary>
    /// Request for retrieving the details of historical Media Mix Modeling (MMM) data requests
    /// </summary>
    public class MmmHistoryRequest
    {
        /// <summary>
        /// Start date, in the format of YYYY-MM-DD (UTC+0 time).
        /// The start date cannot be earlier than four years ago.
        /// </summary>
        [JsonPropertyName("from_date")]
        [Required]
        public string FromDate { get; set; } = string.Empty;

        /// <summary>
        /// End date, in the format of YYYY-MM-DD (UTC+0 time)
        /// </summary>
        [JsonPropertyName("end_date")]
        [Required]
        public string EndDate { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for retrieving the details of historical Media Mix Modeling (MMM) data requests
    /// </summary>
    public class MmmHistoryResponse
    {
        /// <summary>
        /// A JSON-formatted string array that displays historical requests, providing details for each request
        /// </summary>
        [JsonPropertyName("historical_requests")]
        public List<string> HistoricalRequests { get; set; } = new();
    }

    #endregion

    #region Enums and Constants

    /// <summary>
    /// Media type options for MMM data requests
    /// </summary>
    public static class MmmMediaType
    {
        /// <summary>
        /// Both paid and earned media (recommended)
        /// </summary>
        public const string All = "All";

        /// <summary>
        /// Paid media. Advertising you have paid for directly, such as TV commercials and social media ads.
        /// </summary>
        public const string Paid = "Paid";

        /// <summary>
        /// Earned media. Advertising gained through organic means, such as social media mentions.
        /// </summary>
        public const string Earned = "Earned";
    }

    /// <summary>
    /// Status options for MMM data requests
    /// </summary>
    public static class MmmRequestStatus
    {
        /// <summary>
        /// The request was received but data processing has not started
        /// </summary>
        public const string RequestReceived = "request received";

        /// <summary>
        /// Data processing is in progress
        /// </summary>
        public const string InProgress = "in progress";

        /// <summary>
        /// Data processing has completed and the data is ready for download
        /// </summary>
        public const string CompletedReadyForDownload = "completed - ready for download";

        /// <summary>
        /// The request failed
        /// </summary>
        public const string Failed = "failed";
    }

    #endregion
}
