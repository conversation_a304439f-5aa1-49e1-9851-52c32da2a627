/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Ad Account operations
    /// </summary>
    public class AdAccountApi : IAdAccountApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<AdAccountApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the AdAccountApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public AdAccountApi(IApiClient apiClient, ILogger<AdAccountApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get authorized ad accounts
        /// Use this endpoint to obtain a list of advertiser accounts that authorized an app.
        /// </summary>
        /// <param name="appId">The App id applied by the developer</param>
        /// <param name="secret">The private key of the developer's application</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of authorized advertiser accounts</returns>
        public async Task<TikTokApiResponse<AuthorizedAccountsResponse>> GetAuthorizedAccountsAsync(
            string appId,
            string secret,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(appId))
                throw new ArgumentException("App ID cannot be null or empty", nameof(appId));
            if (string.IsNullOrEmpty(secret))
                throw new ArgumentException("Secret cannot be null or empty", nameof(secret));

            var request = new GetAuthorizedAccountsRequest(appId, secret);
            return await GetAuthorizedAccountsAsync(request, cancellationToken);
        }

        /// <summary>
        /// Get authorized ad accounts
        /// Use this endpoint to obtain a list of advertiser accounts that authorized an app.
        /// </summary>
        /// <param name="request">Request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of authorized advertiser accounts</returns>
        public async Task<TikTokApiResponse<AuthorizedAccountsResponse>> GetAuthorizedAccountsAsync(
            GetAuthorizedAccountsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting authorized accounts for app ID: {AppId}", request.AppId);

            try
            {
                var queryParams = new Dictionary<string, string>
                {
                    ["app_id"] = request.AppId,
                    ["secret"] = request.Secret
                };

                var response = await _apiClient.CallApiAsync<TikTokApiResponse<AuthorizedAccountsResponse>>(
                    path: $"{ApiEndpoints.AdAccount.GetAuthorizedAccounts}",
                    method: HttpMethod.Get,
                    queryParams: queryParams,
                    body: null,
                    headerParams: null,
                    authNames: null,
                    cancellationToken: cancellationToken);

                _logger?.LogDebug("Successfully retrieved {Count} authorized accounts", response.Data?.List?.Count ?? 0);
                return response;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get authorized accounts for app ID: {AppId}", request.AppId);
                throw;
            }
        }

        /// <summary>
        /// Get ad account details
        /// Use this endpoint to obtain the details of an advertiser's ad account.
        /// </summary>
        /// <param name="advertiserIds">List of advertiser IDs to query</param>
        /// <param name="fields">A list of information to be returned (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing ad account details</returns>
        public async Task<TikTokApiResponse<AdAccountDetailsResponse>> GetAdAccountDetailsAsync(
            List<string> advertiserIds,
            List<string>? fields = null,
            CancellationToken cancellationToken = default)
        {
            if (advertiserIds == null || advertiserIds.Count == 0)
                throw new ArgumentException("Advertiser IDs cannot be null or empty", nameof(advertiserIds));

            var request = new GetAdAccountDetailsRequest(advertiserIds, fields);
            return await GetAdAccountDetailsAsync(request, cancellationToken);
        }

        /// <summary>
        /// Get ad account details
        /// Use this endpoint to obtain the details of an advertiser's ad account.
        /// </summary>
        /// <param name="request">Request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing ad account details</returns>
        public async Task<TikTokApiResponse<AdAccountDetailsResponse>> GetAdAccountDetailsAsync(
            GetAdAccountDetailsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (request.AdvertiserIds == null || request.AdvertiserIds.Count == 0)
                throw new ArgumentException("Advertiser IDs cannot be null or empty", nameof(request));

            _logger?.LogDebug("Getting ad account details for {Count} advertiser IDs", request.AdvertiserIds.Count);

            try
            {
                var queryParams = new Dictionary<string, string>
                {
                    ["advertiser_ids"] = System.Text.Json.JsonSerializer.Serialize(request.AdvertiserIds)
                };

                if (request.Fields != null && request.Fields.Count > 0)
                {
                    queryParams["fields"] = System.Text.Json.JsonSerializer.Serialize(request.Fields);
                }

                var response = await _apiClient.CallApiAsync<TikTokApiResponse<AdAccountDetailsResponse>>(
                    path: $"{ApiEndpoints.AdAccount.GetAccountInfo}",
                    method: HttpMethod.Get,
                    queryParams: queryParams,
                    body: null,
                    headerParams: null,
                    authNames: null,
                    cancellationToken: cancellationToken);

                _logger?.LogDebug("Successfully retrieved details for {Count} ad accounts", response.Data?.List?.Count ?? 0);
                return response;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get ad account details for advertiser IDs: {AdvertiserIds}", 
                    string.Join(", ", request.AdvertiserIds));
                throw;
            }
        }
    }
}
