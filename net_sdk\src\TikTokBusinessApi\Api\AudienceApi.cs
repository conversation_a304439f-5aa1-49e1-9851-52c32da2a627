/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Audience operations
    /// </summary>
    public class AudienceApi : IAudienceApi
    {
        private readonly IApiClient _apiClient;

        /// <summary>
        /// Initializes a new instance of the AudienceApi class
        /// </summary>
        /// <param name="apiClient">API client for making requests</param>
        public AudienceApi(IApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        }

        /// <inheritdoc />
        // public async Task<AudienceFileUploadResponse> UploadAudienceFileAsync(
        //     AudienceFileUploadRequest request,
        //     Stream fileStream,
        //     string fileName,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);
        //     ArgumentNullException.ThrowIfNull(fileStream);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.FileSignature))
        //         throw new ArgumentException("File signature cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.CalculateType))
        //         throw new ArgumentException("Calculate type cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(fileName))
        //         throw new ArgumentException("File name cannot be null or empty", nameof(fileName));

        //     // Create multipart form data content
        //     using var content = new MultipartFormDataContent();
        //     content.Add(new StringContent(request.AdvertiserId), "advertiser_id");
        //     content.Add(new StringContent(request.FileSignature), "file_signature");
        //     content.Add(new StringContent(request.CalculateType), "calculate_type");
        //     content.Add(new StreamContent(fileStream), "file", fileName);

        //     return await _apiClient.CallApiAsync<AudienceFileUploadResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.FileUpload}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: content,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task<AudienceCreateResponse> CreateAudienceAsync(
        //     AudienceCreateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.CustomAudienceName))
        //         throw new ArgumentException("Custom audience name cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.CalculateType))
        //         throw new ArgumentException("Calculate type cannot be null or empty", nameof(request));

        //     if (request.FilePaths == null || request.FilePaths.Count == 0)
        //         throw new ArgumentException("At least one file path must be provided", nameof(request));

        //     return await _apiClient.CallApiAsync<AudienceCreateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.Create}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task<AudienceRuleCreateResponse> CreateAudienceByRulesAsync(
        //     AudienceRuleCreateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.CustomAudienceName))
        //         throw new ArgumentException("Custom audience name cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.AudienceType))
        //         throw new ArgumentException("Audience type cannot be null or empty", nameof(request));

        //     ArgumentNullException.ThrowIfNull(request.RuleSpec);

        //     return await _apiClient.CallApiAsync<AudienceRuleCreateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.CreateByRules}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        public async Task<AudienceListResponse> GetAudienceListAsync(
            AudienceListRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (request.Page < 1)
                throw new ArgumentException("Page must be greater than 0", nameof(request));

            if (request.PageSize < 1 || request.PageSize > 1000)
                throw new ArgumentException("Page size must be between 1 and 1000", nameof(request));

            return await _apiClient.CallApiAsync<AudienceListResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.List}",
                HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<AudienceGetResponse> GetAudienceAsync(
            AudienceGetRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (request.CustomAudienceIds == null || request.CustomAudienceIds.Count == 0)
                throw new ArgumentException("At least one custom audience ID must be provided", nameof(request));

            return await _apiClient.CallApiAsync<AudienceGetResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.Get}",
                HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        // public async Task<AudienceUpdateResponse> UpdateAudienceAsync(
        //     AudienceUpdateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.CustomAudienceId))
        //         throw new ArgumentException("Custom audience ID cannot be null or empty", nameof(request));

        //     return await _apiClient.CallApiAsync<AudienceUpdateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.Update}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task<AudienceDeleteResponse> DeleteAudienceAsync(
        //     AudienceDeleteRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.CustomAudienceIds == null || request.CustomAudienceIds.Count == 0)
        //         throw new ArgumentException("At least one custom audience ID must be provided", nameof(request));

        //     return await _apiClient.CallApiAsync<AudienceDeleteResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.Delete}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task<AudienceShareResponse> ShareAudienceAsync(
        //     AudienceShareRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.CustomAudienceIds == null || request.CustomAudienceIds.Count == 0)
        //         throw new ArgumentException("At least one custom audience ID must be provided", nameof(request));

        //     if (request.SharedAdvertiserIds == null || request.SharedAdvertiserIds.Count == 0)
        //         throw new ArgumentException("At least one shared advertiser ID must be provided", nameof(request));

        //     return await _apiClient.CallApiAsync<AudienceShareResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.Share}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        public async Task<AudienceShareLogResponse> GetShareLogAsync(
            AudienceShareLogRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.CustomAudienceId))
                throw new ArgumentException("Custom audience ID cannot be null or empty", nameof(request));

            return await _apiClient.CallApiAsync<AudienceShareLogResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.ShareLog}",
                HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        // public async Task<AudienceApplyResponse> ApplyAudienceAsync(
        //     AudienceApplyRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.CustomAudienceId))
        //         throw new ArgumentException("Custom audience ID cannot be null or empty", nameof(request));

        //     if (request.AdgroupIds == null || request.AdgroupIds.Count == 0)
        //         throw new ArgumentException("At least one ad group ID must be provided", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.ActionMode))
        //         throw new ArgumentException("Action mode cannot be null or empty", nameof(request));

        //     return await _apiClient.CallApiAsync<AudienceApplyResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.Apply}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task<AudienceLookalikeCreateResponse> CreateLookalikeAudienceAsync(
        //     AudienceLookalikeCreateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.CustomAudienceName))
        //         throw new ArgumentException("Custom audience name cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.SeedId))
        //         throw new ArgumentException("Seed ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.LookalikeType))
        //         throw new ArgumentException("Lookalike type cannot be null or empty", nameof(request));

        //     return await _apiClient.CallApiAsync<AudienceLookalikeCreateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.CreateLookalike}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task<AudienceLookalikeUpdateResponse> UpdateLookalikeAudienceAsync(
        //     AudienceLookalikeUpdateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.CustomAudienceId))
        //         throw new ArgumentException("Custom audience ID cannot be null or empty", nameof(request));

        //     return await _apiClient.CallApiAsync<AudienceLookalikeUpdateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.UpdateLookalike}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task<AudienceShareCancelResponse> CancelShareAudienceAsync(
        //     AudienceShareCancelRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.CustomAudienceIds == null || request.CustomAudienceIds.Count == 0)
        //         throw new ArgumentException("At least one custom audience ID must be provided", nameof(request));

        //     if (request.SharedAdvertiserIds == null || request.SharedAdvertiserIds.Count == 0)
        //         throw new ArgumentException("At least one shared advertiser ID must be provided", nameof(request));

        //     return await _apiClient.CallApiAsync<AudienceShareCancelResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.ShareCancel}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        public async Task<AudienceApplyLogResponse> GetApplyLogAsync(
            AudienceApplyLogRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.CustomAudienceId))
                throw new ArgumentException("Custom audience ID cannot be null or empty", nameof(request));

            return await _apiClient.CallApiAsync<AudienceApplyLogResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.ApplyLog}",
                HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<EnhancedAudienceGetResponse> GetEnhancedAudienceAsync(
            AudienceGetRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (request.CustomAudienceIds == null || request.CustomAudienceIds.Count == 0)
                throw new ArgumentException("At least one custom audience ID must be provided", nameof(request));

            return await _apiClient.CallApiAsync<EnhancedAudienceGetResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Audience.Get}",
                HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // ===== STREAMING/SEGMENT API METHODS =====

        /// <inheritdoc />
        // public async Task<SegmentAudienceResponse> ManageSegmentAudienceAsync(
        //     SegmentAudienceRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.Action))
        //         throw new ArgumentException("Action cannot be null or empty", nameof(request));

        //     if (request.Action == "create" && string.IsNullOrWhiteSpace(request.CustomAudienceName))
        //         throw new ArgumentException("Custom audience name is required when action is create", nameof(request));

        //     if (request.Action == "delete" && string.IsNullOrWhiteSpace(request.DeleteAudienceId))
        //         throw new ArgumentException("Delete audience ID is required when action is delete", nameof(request));

        //     return await _apiClient.CallApiAsync<SegmentAudienceResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Segment.Audience}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task<SegmentMappingResponse> ManageSegmentMappingAsync(
        //     SegmentMappingRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (request.AdvertiserIds == null || request.AdvertiserIds.Count == 0)
        //         throw new ArgumentException("At least one advertiser ID must be provided", nameof(request));

        //     if (request.IdSchema == null || request.IdSchema.Count == 0)
        //         throw new ArgumentException("At least one ID schema must be provided", nameof(request));

        //     if (request.BatchData == null || request.BatchData.Count == 0)
        //         throw new ArgumentException("Batch data cannot be empty", nameof(request));

        //     return await _apiClient.CallApiAsync<SegmentMappingResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.Segment.Mapping}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // ===== SAVED AUDIENCE API METHODS =====

        /// <inheritdoc />
        // public async Task<SavedAudienceCreateResponse> CreateSavedAudienceAsync(
        //     SavedAudienceCreateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.SavedAudienceName))
        //         throw new ArgumentException("Saved audience name cannot be null or empty", nameof(request));

        //     ArgumentNullException.ThrowIfNull(request.Targeting);

        //     return await _apiClient.CallApiAsync<SavedAudienceCreateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.SavedAudience.Create}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task<AudienceSuccessResponse> DeleteSavedAudienceAsync(
        //     SavedAudienceDeleteRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (request.SavedAudienceIds == null || request.SavedAudienceIds.Count == 0)
        //         throw new ArgumentException("At least one saved audience ID must be provided", nameof(request));

        //     return await _apiClient.CallApiAsync<AudienceSuccessResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.SavedAudience.Delete}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        public async Task<SavedAudienceListResponse> GetSavedAudienceListAsync(
            SavedAudienceListRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            return await _apiClient.CallApiAsync<SavedAudienceListResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.SavedAudience.List}",
                HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // ===== PANGLE AUDIENCE API METHODS =====

        /// <inheritdoc />
        public async Task<PangleAudiencePackageResponse> GetPangleAudiencePackageAsync(
            PangleAudiencePackageRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            return await _apiClient.CallApiAsync<PangleAudiencePackageResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.PangleAudience.GetPackage}",
                HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
