/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API BC Reporting operations
    /// </summary>
    public class BcReportingApi : IBcReportingApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<BcReportingApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the BcReportingApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public BcReportingApi(IApiClient apiClient, ILogger<BcReportingApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get currencies and registration areas for ad accounts within a Business Center.
        /// You can pass the returned currencies and places of registration to the filter fields 
        /// registered_area and currency_of_account in a Business Center report.
        /// </summary>
        /// <param name="request">Request containing the Business Center ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing currencies and region codes</returns>
        public async Task<GetAdvertiserAttributeResponse> GetAdvertiserAttributeAsync(
            GetAdvertiserAttributeRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting advertiser attributes for BC {BcId}", request.BcId);

            // Create query parameters for GET request
            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            return await _apiClient.CallApiAsync<GetAdvertiserAttributeResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCReportingEndpoints.GetAdvertiserAttribute}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
