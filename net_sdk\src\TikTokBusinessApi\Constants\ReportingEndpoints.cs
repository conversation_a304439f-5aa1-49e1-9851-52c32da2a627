/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Reporting endpoints
    /// </summary>
    public static class ReportingEndpoints
    {
        /// <summary>
        /// Run a synchronous report endpoint (v1.3)
        /// </summary>
        public const string GetSynchronousReport = "/report/integrated/get/";

        /// <summary>
        /// Create an asynchronous report task endpoint (v1.3)
        /// </summary>
        public const string CreateAsynchronousReportTask = "/report/task/create/";

        /// <summary>
        /// Get the status of an async report task endpoint (v1.3)
        /// </summary>
        public const string GetAsynchronousReportTaskStatus = "/report/task/check/";

        /// <summary>
        /// Download the output of an async report task endpoint (v1.3)
        /// </summary>
        public const string DownloadAsynchronousReportTask = "/report/task/download/";

        /// <summary>
        /// Cancel an asynchronous report task endpoint (v1.3)
        /// </summary>
        public const string CancelAsynchronousReportTask = "/report/task/cancel/";
    }
}
