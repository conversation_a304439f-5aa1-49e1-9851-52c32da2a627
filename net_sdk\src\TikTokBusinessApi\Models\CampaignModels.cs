/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request parameters for getting campaigns
    /// </summary>
    public class CampaignGetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Fields that you want to get. When not specified, all fields are returned by default.
        /// </summary>
        [JsonPropertyName("fields")]
        public List<string>? Fields { get; set; }

        /// <summary>
        /// The type of fields that you want to remove from the response
        /// </summary>
        [JsonPropertyName("exclude_field_types_in_response")]
        public List<string>? ExcludeFieldTypesInResponse { get; set; }

        /// <summary>
        /// Filters on the data
        /// </summary>
        [JsonPropertyName("filtering")]
        public CampaignFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10. Range: 1-1000
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering conditions for campaigns
    /// </summary>
    public class CampaignFiltering
    {
        /// <summary>
        /// Filter by campaign IDs. Max size: 100
        /// </summary>
        [JsonPropertyName("campaign_ids")]
        public List<string>? CampaignIds { get; set; }

        /// <summary>
        /// Filter by campaign name. Fuzzy match is supported
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// Filter by campaign origins (campaign sources)
        /// </summary>
        [JsonPropertyName("campaign_system_origins")]
        public List<string>? CampaignSystemOrigins { get; set; }

        /// <summary>
        /// Primary status
        /// </summary>
        [JsonPropertyName("primary_status")]
        public string? PrimaryStatus { get; set; }

        /// <summary>
        /// Filter by campaign secondary status
        /// </summary>
        [JsonPropertyName("secondary_status")]
        public string? SecondaryStatus { get; set; }

        /// <summary>
        /// Filter by advertising objective
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string? ObjectiveType { get; set; }

        /// <summary>
        /// Filter by buying types
        /// </summary>
        [JsonPropertyName("buying_types")]
        public List<string>? BuyingTypes { get; set; }

        /// <summary>
        /// Filter by whether the campaign is an automated campaign type
        /// </summary>
        [JsonPropertyName("is_smart_performance_campaign")]
        public bool? IsSmartPerformanceCampaign { get; set; }

        /// <summary>
        /// Filter by whether split test has been enabled for the campaign or not
        /// </summary>
        [JsonPropertyName("split_test_enabled")]
        public bool? SplitTestEnabled { get; set; }

        /// <summary>
        /// Filter by product source of the campaign
        /// </summary>
        [JsonPropertyName("campaign_product_source")]
        public string? CampaignProductSource { get; set; }

        /// <summary>
        /// Optimization goal
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string? OptimizationGoal { get; set; }

        /// <summary>
        /// Use this field to filter regular campaigns or iOS 14 campaigns
        /// </summary>
        [JsonPropertyName("campaign_type")]
        public string? CampaignType { get; set; }

        /// <summary>
        /// Filter by lower range of campaign creation time, in the format of YYYY-MM-DD HH:MM:SS (UTC time zone)
        /// </summary>
        [JsonPropertyName("creation_filter_start_time")]
        public string? CreationFilterStartTime { get; set; }

        /// <summary>
        /// Filter by higher range of campaign creation time, in the format of YYYY-MM-DD HH:MM:SS (UTC time zone)
        /// </summary>
        [JsonPropertyName("creation_filter_end_time")]
        public string? CreationFilterEndTime { get; set; }

        /// <summary>
        /// GMV Max Campaign type (Deprecated)
        /// </summary>
        [JsonPropertyName("gmv_max_promotion_types")]
        public List<string>? GmvMaxPromotionTypes { get; set; }

        /// <summary>
        /// Valid only when the value of gmv_max_promotion_types contains LIVE_GMV_MAX or PRODUCT_GMV_MAX (Deprecated)
        /// </summary>
        [JsonPropertyName("store_ids")]
        public List<string>? StoreIds { get; set; }
    }

    /// <summary>
    /// Response data for campaign get
    /// </summary>
    public class CampaignGetResponse
    {
        /// <summary>
        /// List of campaigns
        /// </summary>
        [JsonPropertyName("list")]
        public List<CampaignInfo>? List { get; set; }

        /// <summary>
        /// Page information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Campaign information
    /// </summary>
    public class CampaignInfo
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// The origin (source) of the campaign
        /// </summary>
        [JsonPropertyName("campaign_system_origin")]
        public string? CampaignSystemOrigin { get; set; }

        /// <summary>
        /// Time at which the campaign was created
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Time at which the campaign was modified
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string? ModifyTime { get; set; }

        /// <summary>
        /// Advertising objective
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string? ObjectiveType { get; set; }

        /// <summary>
        /// App promotion type
        /// </summary>
        [JsonPropertyName("app_promotion_type")]
        public string? AppPromotionType { get; set; }

        /// <summary>
        /// The new objective type
        /// </summary>
        [JsonPropertyName("virtual_objective_type")]
        public string? VirtualObjectiveType { get; set; }

        /// <summary>
        /// Sales destination, the destination where you want to drive your sales
        /// </summary>
        [JsonPropertyName("sales_destination")]
        public string? SalesDestination { get; set; }

        /// <summary>
        /// Whether the campaign is a Search Campaign
        /// </summary>
        [JsonPropertyName("is_search_campaign")]
        public bool? IsSearchCampaign { get; set; }

        /// <summary>
        /// Whether the campaign is an automated campaign type
        /// </summary>
        [JsonPropertyName("is_smart_performance_campaign")]
        public bool? IsSmartPerformanceCampaign { get; set; }

        /// <summary>
        /// Campaign Type, indicates the campaign is a regular campaign or iOS 14 campaign
        /// </summary>
        [JsonPropertyName("campaign_type")]
        public string? CampaignType { get; set; }

        /// <summary>
        /// The Application ID of the promoted app
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Whether the campaign is an Advanced Dedicated Campaign
        /// </summary>
        [JsonPropertyName("is_advanced_dedicated_campaign")]
        public bool? IsAdvancedDedicatedCampaign { get; set; }

        /// <summary>
        /// Whether to disable SKAN (SKAdNetwork) attribution
        /// </summary>
        [JsonPropertyName("disable_skan_campaign")]
        public bool? DisableSkanCampaign { get; set; }

        /// <summary>
        /// The attribution type for the Dedicated Campaign
        /// </summary>
        [JsonPropertyName("bid_align_type")]
        public string? BidAlignType { get; set; }

        /// <summary>
        /// Indicates the status of App Profile Page
        /// </summary>
        [JsonPropertyName("campaign_app_profile_page_state")]
        public string? CampaignAppProfilePageState { get; set; }

        /// <summary>
        /// When objective_type is specified as RF_REACH, this field specifies the specific Reservation campaign type
        /// </summary>
        [JsonPropertyName("rf_campaign_type")]
        public string? RfCampaignType { get; set; }

        /// <summary>
        /// Product source of the campaign
        /// </summary>
        [JsonPropertyName("campaign_product_source")]
        public string? CampaignProductSource { get; set; }

        /// <summary>
        /// Whether to use your catalog to automatically advertise relevant products
        /// </summary>
        [JsonPropertyName("catalog_enabled")]
        public bool? CatalogEnabled { get; set; }

        /// <summary>
        /// Campaign name
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// Special ad categories
        /// </summary>
        [JsonPropertyName("special_industries")]
        public List<string>? SpecialIndustries { get; set; }

        /// <summary>
        /// Whether CBO is enabled
        /// </summary>
        [JsonPropertyName("budget_optimize_on")]
        public bool? BudgetOptimizeOn { get; set; }

        /// <summary>
        /// Bidding strategy on the campaign level
        /// </summary>
        [JsonPropertyName("bid_type")]
        public string? BidType { get; set; }

        /// <summary>
        /// Bidding strategy for in-app events
        /// </summary>
        [JsonPropertyName("deep_bid_type")]
        public string? DeepBidType { get; set; }

        /// <summary>
        /// ROAS goal for Value Optimization
        /// </summary>
        [JsonPropertyName("roas_bid")]
        public decimal? RoasBid { get; set; }

        /// <summary>
        /// Optimization goal
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string? OptimizationGoal { get; set; }

        /// <summary>
        /// Budget mode
        /// </summary>
        [JsonPropertyName("budget_mode")]
        public string? BudgetMode { get; set; }

        /// <summary>
        /// Campaign budget
        /// </summary>
        [JsonPropertyName("budget")]
        public decimal? Budget { get; set; }

        /// <summary>
        /// Realtime API (RTA) ID, the RTA strategy identifier
        /// </summary>
        [JsonPropertyName("rta_id")]
        public string? RtaId { get; set; }

        /// <summary>
        /// Whether to use RTA bid
        /// </summary>
        [JsonPropertyName("rta_bid_enabled")]
        public bool? RtaBidEnabled { get; set; }

        /// <summary>
        /// Whether to use RTA to automatically select products
        /// </summary>
        [JsonPropertyName("rta_product_selection_enabled")]
        public bool? RtaProductSelectionEnabled { get; set; }

        /// <summary>
        /// Operation status
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string? OperationStatus { get; set; }

        /// <summary>
        /// Campaign status (Secondary status)
        /// </summary>
        [JsonPropertyName("secondary_status")]
        public string? SecondaryStatus { get; set; }

        /// <summary>
        /// The mode that determines which SKAN 4.0 postback you want to secure
        /// </summary>
        [JsonPropertyName("postback_window_mode")]
        public string? PostbackWindowMode { get; set; }

        /// <summary>
        /// Whether the campaign is a new structure
        /// </summary>
        [JsonPropertyName("is_new_structure")]
        public bool? IsNewStructure { get; set; }

        /// <summary>
        /// Campaign type (application or landing page)
        /// </summary>
        [JsonPropertyName("objective")]
        public string? Objective { get; set; }
    }

    /// <summary>
    /// Request parameters for creating campaigns
    /// </summary>
    public class CampaignCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign data to be created
        /// </summary>
        [JsonPropertyName("campaigns")]
        [Required]
        public List<CampaignCreateData> Campaigns { get; set; } = new();
    }

    /// <summary>
    /// Campaign data for creation
    /// </summary>
    public class CampaignCreateData
    {
        /// <summary>
        /// Campaign name. Character limit: 512
        /// </summary>
        [JsonPropertyName("campaign_name")]
        [Required]
        public string CampaignName { get; set; } = string.Empty;

        /// <summary>
        /// Advertising objective
        /// </summary>
        [JsonPropertyName("objective_type")]
        [Required]
        public string ObjectiveType { get; set; } = string.Empty;

        /// <summary>
        /// Budget mode
        /// </summary>
        [JsonPropertyName("budget_mode")]
        public string? BudgetMode { get; set; }

        /// <summary>
        /// Campaign budget
        /// </summary>
        [JsonPropertyName("budget")]
        public float? Budget { get; set; }

        /// <summary>
        /// Whether CBO is enabled
        /// </summary>
        [JsonPropertyName("budget_optimize_on")]
        public bool? BudgetOptimizeOn { get; set; }

        /// <summary>
        /// Bidding strategy on the campaign level
        /// </summary>
        [JsonPropertyName("bid_type")]
        public string? BidType { get; set; }

        /// <summary>
        /// Bidding strategy for in-app events
        /// </summary>
        [JsonPropertyName("deep_bid_type")]
        public string? DeepBidType { get; set; }

        /// <summary>
        /// ROAS goal for Value Optimization
        /// </summary>
        [JsonPropertyName("roas_bid")]
        public float? RoasBid { get; set; }

        /// <summary>
        /// Optimization goal
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string? OptimizationGoal { get; set; }

        /// <summary>
        /// Special ad categories
        /// </summary>
        [JsonPropertyName("special_industries")]
        public List<string>? SpecialIndustries { get; set; }

        /// <summary>
        /// App promotion type
        /// </summary>
        [JsonPropertyName("app_promotion_type")]
        public string? AppPromotionType { get; set; }

        /// <summary>
        /// The Application ID of the promoted app
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Campaign Type, indicates the campaign is a regular campaign or iOS 14 campaign
        /// </summary>
        [JsonPropertyName("campaign_type")]
        public string? CampaignType { get; set; }

        /// <summary>
        /// Whether the campaign is an Advanced Dedicated Campaign
        /// </summary>
        [JsonPropertyName("is_advanced_dedicated_campaign")]
        public bool? IsAdvancedDedicatedCampaign { get; set; }

        /// <summary>
        /// Whether to disable SKAN (SKAdNetwork) attribution
        /// </summary>
        [JsonPropertyName("disable_skan_campaign")]
        public bool? DisableSkanCampaign { get; set; }

        /// <summary>
        /// The attribution type for the Dedicated Campaign
        /// </summary>
        [JsonPropertyName("bid_align_type")]
        public string? BidAlignType { get; set; }

        /// <summary>
        /// When objective_type is specified as RF_REACH, this field specifies the specific Reservation campaign type
        /// </summary>
        [JsonPropertyName("rf_campaign_type")]
        public string? RfCampaignType { get; set; }

        /// <summary>
        /// Product source of the campaign
        /// </summary>
        [JsonPropertyName("campaign_product_source")]
        public string? CampaignProductSource { get; set; }

        /// <summary>
        /// Whether to use your catalog to automatically advertise relevant products
        /// </summary>
        [JsonPropertyName("catalog_enabled")]
        public bool? CatalogEnabled { get; set; }

        /// <summary>
        /// Realtime API (RTA) ID, the RTA strategy identifier
        /// </summary>
        [JsonPropertyName("rta_id")]
        public string? RtaId { get; set; }

        /// <summary>
        /// Whether to use RTA bid
        /// </summary>
        [JsonPropertyName("rta_bid_enabled")]
        public bool? RtaBidEnabled { get; set; }

        /// <summary>
        /// Whether to use RTA to automatically select products
        /// </summary>
        [JsonPropertyName("rta_product_selection_enabled")]
        public bool? RtaProductSelectionEnabled { get; set; }

        /// <summary>
        /// The mode that determines which SKAN 4.0 postback you want to secure
        /// </summary>
        [JsonPropertyName("postback_window_mode")]
        public string? PostbackWindowMode { get; set; }

        /// <summary>
        /// Whether the campaign is an automated campaign type
        /// </summary>
        [JsonPropertyName("is_smart_performance_campaign")]
        public bool? IsSmartPerformanceCampaign { get; set; }

        /// <summary>
        /// Whether the campaign is a Search Campaign
        /// </summary>
        [JsonPropertyName("is_search_campaign")]
        public bool? IsSearchCampaign { get; set; }

        /// <summary>
        /// Sales destination, the destination where you want to drive your sales
        /// </summary>
        [JsonPropertyName("sales_destination")]
        public string? SalesDestination { get; set; }
    }

    /// <summary>
    /// Response data for campaign create
    /// </summary>
    public class CampaignCreateResponse
    {
        /// <summary>
        /// List of campaign creation results
        /// </summary>
        [JsonPropertyName("data")]
        public List<CampaignCreateResult>? Data { get; set; }
    }

    /// <summary>
    /// Campaign creation result
    /// </summary>
    public class CampaignCreateResult
    {
        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// Error code
        /// </summary>
        [JsonPropertyName("code")]
        public int? Code { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }
    }

    /// <summary>
    /// Request parameters for updating campaigns
    /// </summary>
    public class CampaignUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign data to be updated
        /// </summary>
        [JsonPropertyName("campaigns")]
        [Required]
        public List<CampaignUpdateData> Campaigns { get; set; } = new();
    }

    /// <summary>
    /// Campaign data for update
    /// </summary>
    public class CampaignUpdateData
    {
        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        [Required]
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign name. Character limit: 512
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// Budget mode
        /// </summary>
        [JsonPropertyName("budget_mode")]
        public string? BudgetMode { get; set; }

        /// <summary>
        /// Campaign budget
        /// </summary>
        [JsonPropertyName("budget")]
        public float? Budget { get; set; }

        /// <summary>
        /// Whether CBO is enabled
        /// </summary>
        [JsonPropertyName("budget_optimize_on")]
        public bool? BudgetOptimizeOn { get; set; }

        /// <summary>
        /// Bidding strategy on the campaign level
        /// </summary>
        [JsonPropertyName("bid_type")]
        public string? BidType { get; set; }

        /// <summary>
        /// Bidding strategy for in-app events
        /// </summary>
        [JsonPropertyName("deep_bid_type")]
        public string? DeepBidType { get; set; }

        /// <summary>
        /// ROAS goal for Value Optimization
        /// </summary>
        [JsonPropertyName("roas_bid")]
        public float? RoasBid { get; set; }

        /// <summary>
        /// Optimization goal
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string? OptimizationGoal { get; set; }

        /// <summary>
        /// Special ad categories
        /// </summary>
        [JsonPropertyName("special_industries")]
        public List<string>? SpecialIndustries { get; set; }

        /// <summary>
        /// Realtime API (RTA) ID, the RTA strategy identifier
        /// </summary>
        [JsonPropertyName("rta_id")]
        public string? RtaId { get; set; }

        /// <summary>
        /// Whether to use RTA bid
        /// </summary>
        [JsonPropertyName("rta_bid_enabled")]
        public bool? RtaBidEnabled { get; set; }

        /// <summary>
        /// Whether to use RTA to automatically select products
        /// </summary>
        [JsonPropertyName("rta_product_selection_enabled")]
        public bool? RtaProductSelectionEnabled { get; set; }
    }

    /// <summary>
    /// Response data for campaign update
    /// </summary>
    public class CampaignUpdateResponse
    {
        /// <summary>
        /// List of campaign update results
        /// </summary>
        [JsonPropertyName("data")]
        public List<CampaignUpdateResult>? Data { get; set; }
    }

    /// <summary>
    /// Campaign update result
    /// </summary>
    public class CampaignUpdateResult
    {
        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// Error code
        /// </summary>
        [JsonPropertyName("code")]
        public int? Code { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }
    }

    /// <summary>
    /// Request parameters for updating campaign status
    /// </summary>
    public class CampaignStatusUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign IDs to update status for. Max size: 20
        /// </summary>
        [JsonPropertyName("campaign_ids")]
        [Required]
        public List<string> CampaignIds { get; set; } = new();

        /// <summary>
        /// Operation status to set
        /// </summary>
        [JsonPropertyName("operation_status")]
        [Required]
        public string OperationStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response data for campaign status update
    /// </summary>
    public class CampaignStatusUpdateResponse
    {
        /// <summary>
        /// List of campaign status update results
        /// </summary>
        [JsonPropertyName("data")]
        public List<CampaignStatusUpdateResult>? Data { get; set; }
    }

    /// <summary>
    /// Campaign status update result
    /// </summary>
    public class CampaignStatusUpdateResult
    {
        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// Error code
        /// </summary>
        [JsonPropertyName("code")]
        public int? Code { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }
    }

    /// <summary>
    /// Request parameters for getting campaign quota (Deprecated)
    /// </summary>
    public class CampaignQuotaGetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response data for campaign quota get (Deprecated)
    /// </summary>
    public class CampaignQuotaGetResponse
    {
        /// <summary>
        /// Quota information
        /// </summary>
        [JsonPropertyName("quota")]
        public int? Quota { get; set; }

        /// <summary>
        /// Used quota
        /// </summary>
        [JsonPropertyName("used")]
        public int? Used { get; set; }
    }

    /// <summary>
    /// Request parameters for getting campaign quota info
    /// </summary>
    public class CampaignQuotaInfoGetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad network IDs. Max size: 100
        /// </summary>
        [JsonPropertyName("ad_network_ids")]
        public List<string>? AdNetworkIds { get; set; }
    }

    /// <summary>
    /// Response data for campaign quota info get
    /// </summary>
    public class CampaignQuotaInfoGetResponse
    {
        /// <summary>
        /// List of quota information by ad network
        /// </summary>
        [JsonPropertyName("list")]
        public List<CampaignQuotaInfo>? List { get; set; }
    }

    /// <summary>
    /// Campaign quota information
    /// </summary>
    public class CampaignQuotaInfo
    {
        /// <summary>
        /// Ad network ID
        /// </summary>
        [JsonPropertyName("ad_network_id")]
        public string? AdNetworkId { get; set; }

        /// <summary>
        /// Ad network name
        /// </summary>
        [JsonPropertyName("ad_network_name")]
        public string? AdNetworkName { get; set; }

        /// <summary>
        /// Quota information
        /// </summary>
        [JsonPropertyName("quota")]
        public int? Quota { get; set; }

        /// <summary>
        /// Used quota
        /// </summary>
        [JsonPropertyName("used")]
        public int? Used { get; set; }
    }

    /// <summary>
    /// Request parameters for creating campaign copy task
    /// </summary>
    public class CampaignCopyTaskCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Source campaign ID to copy from
        /// </summary>
        [JsonPropertyName("source_campaign_id")]
        [Required]
        public string SourceCampaignId { get; set; } = string.Empty;

        /// <summary>
        /// Target advertiser ID to copy to
        /// </summary>
        [JsonPropertyName("target_advertiser_id")]
        [Required]
        public string TargetAdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// New campaign name for the copied campaign
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// Whether to copy ad groups
        /// </summary>
        [JsonPropertyName("copy_adgroup")]
        public bool? CopyAdgroup { get; set; }

        /// <summary>
        /// Whether to copy ads
        /// </summary>
        [JsonPropertyName("copy_ad")]
        public bool? CopyAd { get; set; }
    }

    /// <summary>
    /// Response data for campaign copy task create
    /// </summary>
    public class CampaignCopyTaskCreateResponse
    {
        /// <summary>
        /// Task ID for the copy operation
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }
    }

    /// <summary>
    /// Request parameters for checking campaign copy task
    /// </summary>
    public class CampaignCopyTaskCheckRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Task ID to check
        /// </summary>
        [JsonPropertyName("task_id")]
        [Required]
        public string TaskId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response data for campaign copy task check
    /// </summary>
    public class CampaignCopyTaskCheckResponse
    {
        /// <summary>
        /// Task status
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Task result details
        /// </summary>
        [JsonPropertyName("result")]
        public CampaignCopyTaskResult? Result { get; set; }
    }

    /// <summary>
    /// Campaign copy task result
    /// </summary>
    public class CampaignCopyTaskResult
    {
        /// <summary>
        /// New campaign ID created from copy
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// List of copied ad group results
        /// </summary>
        [JsonPropertyName("adgroup_list")]
        public List<CampaignCopyAdGroupResult>? AdgroupList { get; set; }

        /// <summary>
        /// List of copied ad results
        /// </summary>
        [JsonPropertyName("ad_list")]
        public List<CampaignCopyAdResult>? AdList { get; set; }
    }

    /// <summary>
    /// Campaign copy ad group result
    /// </summary>
    public class CampaignCopyAdGroupResult
    {
        /// <summary>
        /// Source ad group ID
        /// </summary>
        [JsonPropertyName("source_adgroup_id")]
        public string? SourceAdgroupId { get; set; }

        /// <summary>
        /// New ad group ID created from copy
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdgroupId { get; set; }

        /// <summary>
        /// Error code
        /// </summary>
        [JsonPropertyName("code")]
        public int? Code { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }
    }

    /// <summary>
    /// Campaign copy ad result
    /// </summary>
    public class CampaignCopyAdResult
    {
        /// <summary>
        /// Source ad ID
        /// </summary>
        [JsonPropertyName("source_ad_id")]
        public string? SourceAdId { get; set; }

        /// <summary>
        /// New ad ID created from copy
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// Error code
        /// </summary>
        [JsonPropertyName("code")]
        public int? Code { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }
    }
}
