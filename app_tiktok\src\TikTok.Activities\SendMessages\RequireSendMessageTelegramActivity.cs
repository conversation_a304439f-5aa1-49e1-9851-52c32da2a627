﻿using AutoMapper;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using TikTok.Activities.BaseMessages;
using TikTok.AdAccounts;
using TikTok.BusinessCenters;
using TikTok.Entities;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using TikTok.BaseMessages;
using Elsa.Expressions;
using Tsp.Zalo.Models;

namespace TikTok.Activities.SendMessages
{
    [Activity(Category = "Messages", DisplayName = "Chuẩn bị thông báo Telegram", Outcomes = new[] { OutcomeNames.Done })]
    public class RequireSendMessageTelegramActivity : BaseMessageActivity
    {
        private readonly ILogger<RequireSendMessageTelegramActivity> _logger;
        private readonly IRepository<RawBusinessCenterEntity, Guid> _businessCenterRepository;
        private readonly IRepository<RawAdAccountEntity, Guid> _adAccountRepository;
        private readonly IMapper _mapper;
        public RequireSendMessageTelegramActivity(ILogger<RequireSendMessageTelegramActivity> logger
            , IRepository<RawBusinessCenterEntity, Guid> businessCenterRepository
            , IRepository<RawAdAccountEntity, Guid> adAccountRepository
            , IMapper mapper)
        {
            _logger = logger;
            _businessCenterRepository = businessCenterRepository;
            _adAccountRepository = adAccountRepository;
            _mapper = mapper;
        }

        [ActivityOutput(Hint = "Message Output")]
        public List<BaseMessageActivityOutput> Output { get; set; }

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            if (Input == null && context.Input == null)
            {
                _logger.LogError("Input is null in RequireSendMessageTelegramActivity");
                throw new InvalidOperationException("Input is null in RequireSendMessageTelegramActivity");
            }
            else
            {
                Input = context.Input as BaseMessageActivityInput;
            }

            var businessCenter = await _businessCenterRepository.GetAsync(b => b.BcId == Input.BcId);
            var adAccount = (await _adAccountRepository.GetQueryableAsync())
                .Where(acc => acc.AdvertiserId == Input.AdAccountId)
                .Select(acc => acc)
                .OrderByDescending(acc => acc.LastModificationTime)
                .FirstOrDefault();

            if (adAccount == null)
            {
                throw new EntityNotFoundException("Can not found AdAccount with Id " + Input.AdAccountId);
            }

            // Get message from context and send via Zalo
            var message = $"🚨 *Biến động số dư tài khoản quảng cáo*\n\n" +
                         $"📊 *Thông tin Business Center:*\n" +
                         $"• ID: `{businessCenter?.BcId}`\n" +
                         $"• Tên: {businessCenter?.Name}\n" +
                         $"• Công ty: {businessCenter?.Company}\n" +
                         $"• Trạng thái: {businessCenter?.Status}\n\n" +
                         $"💳 *Thông tin AdAccount:*\n" +
                         $"• ID: `{adAccount.AdvertiserId}`\n" +
                         $"• Tên: {adAccount.Name}\n" +
                         $"• Công ty: {adAccount.Company}\n" +
                         $"• Quốc gia: {adAccount.Country}\n" +
                         $"• Tiền tệ: {adAccount.Currency}\n" +
                         $"• Số dư: {adAccount.Balance:N2}\n" +
                         $"• Trạng thái: {adAccount.Status}\n\n" +
                         $"📝 *Nội dung:* \n\n" +
                         $"⏰ Thời gian: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";

            var output = new List<BaseMessageActivityOutput>();
            output.Add(new TikTok.BaseMessages.BaseMessageActivityOutput()
            {
                OwnerId = Input.OwnerId ?? string.Empty,
                PhoneNumber = Input.Phonenumber,
                Content = message,
                MessageType = "text",
                Attachments = []
            });

            Output = output;

            _logger.LogDebug("Prepare to Send message: " + JsonConvert.SerializeObject(Output));
            return Done(Output);
        }
    }
}
