# Ad Account Permission Management Documentation

## Overview

The Ad Account Permission Management feature provides a comprehensive interface for managing user permissions and access to advertising accounts within the TikTok API system. This feature allows administrators to control which users can view and access specific advertising accounts.

## Features

### Core Functionality

1. **User Management**
   - View all users in the system with their access information
   - Search and filter users by various criteria
   - Display user roles and status information

2. **Ad Account Access Control**
   - Assign advertising accounts to specific users
   - Revoke access to advertising accounts
   - View available and assigned ad accounts for each user

3. **Statistics and Monitoring**
   - Real-time statistics on user access
   - Overview of assigned vs unassigned ad accounts
   - User distribution by roles

4. **Permission-Based Access**
   - Role-based permission system
   - View-only mode for users without management permissions
   - Granular permission control

## Architecture

### Backend Components

#### Application Services
- **UserAccessManagementAppService**: Main service handling user access operations
- **IUserAccessManagementAppService**: Service interface defining contracts

#### DTOs (Data Transfer Objects)
- **UserAccessDto**: Contains user information with access details
- **GetUserAccessListDto**: Request DTO for filtering and pagination
- **AssignAdAccountsDto**: DTO for assigning ad accounts to users
- **RevokeAdAccountsDto**: DTO for revoking ad account access
- **UserAccessStatisticsDto**: DTO for system statistics
- **GetAvailableAdAccountsDto**: DTO for retrieving available ad accounts

#### Permissions
- **TikTokPermissions.AdAccountPermissionManagement.Default**: Single permission for all ad account permission management operations

### Frontend Components

#### Razor Pages
- **Index.cshtml**: Main page layout with user table and modals
- **Index.cshtml.cs**: Page model handling server-side logic

#### JavaScript
- **Index.js**: Main JavaScript functionality for user interactions
- **userAccessPermission.js**: Permission-based UI adjustments

#### CSS
- **userAccess.css**: Custom styling for the user access management interface

## API Endpoints

### User Management
```
GET /api/app/user-access-management
GET /api/app/user-access-management/{userId}
GET /api/app/user-access-management/statistics
```

### Ad Account Management
```
GET /api/app/user-access-management/available-ad-accounts
GET /api/app/user-access-management/{userId}/assigned-ad-accounts
POST /api/app/user-access-management/assign-ad-accounts
POST /api/app/user-access-management/revoke-ad-accounts
```

## Usage Guide

### Accessing the Feature

1. Navigate to the "Ad Account Permission Management" menu item
2. Ensure you have the required permission:
   - `TikTok.AdAccountPermissionManagement.Default` for all operations

### Managing User Access

#### Viewing Users
1. The main page displays all users with their access information
2. Use filters to search by:
   - User name, email, or full name
   - Role
   - Active/inactive status
   - Ad account access status

#### Assigning Ad Account Access
1. Click "Manage Access" button for a user
2. In the modal, view available ad accounts on the left
3. Click on ad accounts to assign them to the user
4. Assigned accounts appear on the right side
5. Click "Save" to apply changes

#### Revoking Access
1. In the access management modal
2. Click on assigned ad accounts (right side) to revoke access
3. Revoked accounts move back to available list
4. Click "Save" to apply changes

### Statistics Dashboard
- Click the "Statistics" button to view system overview
- Statistics include:
  - Total users and active users
  - Users with ad account access
  - Total and assigned ad accounts
  - User distribution by roles

## Security Considerations

### Permission System
- All operations are protected by ABP's authorization system
- Users without proper permissions see read-only interfaces
- API endpoints validate permissions before processing requests

### Data Access
- Users can only see data they have permission to access
- Resource-based permissions control ad account visibility
- Audit logging tracks all permission changes

## Technical Implementation Details

### Database Integration
- Uses existing `ResourcePermissionEntity` for storing access permissions
- Integrates with ABP Identity system for user management
- No additional database schema changes required

### Performance Considerations
- Efficient querying with pagination support
- Caching of user role information
- Optimized permission checks

### Error Handling
- Comprehensive error logging
- User-friendly error messages
- Graceful degradation for permission issues

## Configuration

### Menu Integration
The feature is automatically added to the main navigation menu with the following configuration:

```csharp
context.Menu.AddItem(
    new ApplicationMenuItem(
        TikTokMenus.UserAccessManagement,
        l["Menu:UserAccessManagement"],
        "~/UserAccessManagement",
        icon: "fa fa-user-shield",
        order: 21
    ).RequirePermissions(TikTokPermissions.AdAccountPermissionManagement.Default)
);
```

### Localization
The feature supports multiple languages with localization keys in:
- English: `en.json`
- Vietnamese: `vi.json`

## Future Enhancements

### Planned Features
1. **Balance Notification Management** (Phase 2)
   - Toggle balance notifications per user
   - Custom notification thresholds
   - Notification history tracking

2. **Advanced Filtering**
   - Date range filters
   - Custom role filters
   - Export functionality

3. **Bulk Operations**
   - Bulk user assignment
   - Template-based access management
   - Import/export user access configurations

### Technical Improvements
- Enhanced caching strategies
- Real-time updates using SignalR
- Advanced audit logging
- Performance monitoring

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Verify user has required permissions
   - Check role assignments
   - Review permission configuration

2. **Ad Accounts Not Loading**
   - Check AdAccountAppService availability
   - Verify database connectivity
   - Review error logs

3. **Statistics Not Updating**
   - Clear application cache
   - Check background service status
   - Verify data synchronization

### Debugging
- Enable debug logging for `TikTok.UserAccessManagement` namespace
- Check browser console for JavaScript errors
- Review server logs for API errors

## Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.
