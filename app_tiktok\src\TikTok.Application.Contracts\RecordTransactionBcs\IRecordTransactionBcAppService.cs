using System;
using System.Threading.Tasks;
using TikTok.RecordTransactionBcs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.RecordTransactionBcs
{
    /// <summary>
    /// Interface cho RecordTransactionBc App Service
    /// </summary>
    public interface IRecordTransactionBcAppService :
        ICrudAppService<
            RecordTransactionBcDto,
            Guid,
            GetRecordTransactionBcListDto,
            CreateRecordTransactionBcDto,
            UpdateRecordTransactionBcDto>
    {
    }
}