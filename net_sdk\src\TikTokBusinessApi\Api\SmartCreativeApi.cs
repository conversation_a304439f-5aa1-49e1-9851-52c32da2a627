/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Smart Creative operations
    /// </summary>
    public class SmartCreativeApi : ISmartCreativeApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<SmartCreativeApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the SmartCreativeApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public SmartCreativeApi(IApiClient apiClient, ILogger<SmartCreativeApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Create Smart Creative ads by uploading necessary ad creatives to the library
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing Smart Creative ad information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created Smart Creative ad information</returns>
        // public async Task<Response<SmartCreativeCreateResponse>> CreateSmartCreativeAdsAsync(
        //     string accessToken,
        //     SmartCreativeCreateBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Creating Smart Creative ads for advertiser {AdvertiserId} and ad group {AdGroupId}", 
        //         body.AdvertiserId, body.AdGroupId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<SmartCreativeCreateResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SmartCreativeEndpoints.CreateSmartCreativeAds}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get creative materials for Smart Creative ads, including call-to-actions, texts, ad names, images, or video materials
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="adGroupIds">A list of ad group IDs</param>
        /// <param name="excludeFieldTypesInResponse">The type of fields that you want to remove from the response</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing Smart Creative materials</returns>
        public async Task<Response<SmartCreativeGetResponse>> GetSmartCreativeMaterialsAsync(
            string accessToken,
            string advertiserId,
            List<string> adGroupIds,
            List<string>? excludeFieldTypesInResponse = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (adGroupIds == null || adGroupIds.Count == 0)
                throw new ArgumentException("Ad group IDs cannot be null or empty", nameof(adGroupIds));

            _logger?.LogInformation("Getting Smart Creative materials for advertiser {AdvertiserId}", advertiserId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["adgroup_ids"] = string.Join(",", adGroupIds)
            };

            if (excludeFieldTypesInResponse != null && excludeFieldTypesInResponse.Count > 0)
            {
                queryParams["exclude_field_types_in_response"] = string.Join(",", excludeFieldTypesInResponse);
            }

            return await _apiClient.CallApiAsync<Response<SmartCreativeGetResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SmartCreativeEndpoints.GetSmartCreativeMaterials}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Modify Smart Creative ad creatives. You can modify call-to-action, ad names, images, and video materials
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing Smart Creative ad information to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated Smart Creative ad information</returns>
        // public async Task<Response<SmartCreativeCreateResponse>> UpdateSmartCreativeMaterialsAsync(
        //     string accessToken,
        //     SmartCreativeUpdateBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Updating Smart Creative materials for advertiser {AdvertiserId} and ad group {AdGroupId}", 
        //         body.AdvertiserId, body.AdGroupId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<SmartCreativeCreateResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SmartCreativeEndpoints.UpdateSmartCreativeMaterials}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Update the status of creative materials for Smart Creative ads, including ad texts, images, and video materials
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing material status update information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated material status information</returns>
        // public async Task<Response<SmartCreativeMaterialStatusUpdateResponse>> UpdateSmartCreativeMaterialStatusesAsync(
        //     string accessToken,
        //     SmartCreativeMaterialStatusUpdateBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Updating Smart Creative material statuses for advertiser {AdvertiserId} and ad group {AdGroupId}", 
        //         body.AdvertiserId, body.AdGroupId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<SmartCreativeMaterialStatusUpdateResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SmartCreativeEndpoints.UpdateSmartCreativeMaterialStatuses}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }
    }
}
