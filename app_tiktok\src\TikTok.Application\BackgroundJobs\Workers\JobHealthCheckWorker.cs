using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TikTok.Enums;
using TikTok.JobManagement;
using TikTok.Repositories;
using TikTok.Application.Contracts.MessageProviders;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Threading;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Background worker để kiểm tra sức khỏe của hệ thống job
    /// </summary>
    public class JobHealthCheckWorker : AsyncPeriodicBackgroundWorkerBase, ITransientDependency
    {
        private readonly ILogger<JobHealthCheckWorker> _logger;
        private readonly IMessageService _messageService;
        private DateTime? _lastNotificationTime;

        public JobHealthCheckWorker(
            ILogger<JobHealthCheckWorker> logger,
            AbpAsyncTimer timer,
            IServiceScopeFactory serviceScopeFactory,
            IMessageService messageService) : base(timer, serviceScopeFactory)
        {
            _logger = logger;
            // set time 10 minutes
            Timer.Period = 600000;
            _messageService = messageService;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            _logger.LogDebug("Starting JobHealthCheckWorker");

            try
            {
                using (var scope = ServiceScopeFactory.CreateScope())
                {
                    var configurationCacheService = scope.ServiceProvider.GetRequiredService<IJobConfigurationCacheService>();
                    var jobActivityService = scope.ServiceProvider.GetRequiredService<IJobActivityService>();
                    var workerInfoRepository = scope.ServiceProvider.GetRequiredService<IWorkerInfoRepository>();
                    var jobRepository = scope.ServiceProvider.GetRequiredService<IJobRepository>();

                    // Lấy cấu hình hiện tại từ cache
                    var configuration = await configurationCacheService.GetCurrentConfigurationWithCacheAsync();
                    if (configuration == null || !configuration.IsActive)
                    {
                        _logger.LogDebug("JobHealthCheckWorker skipped - no active configuration");
                        return;
                    }

                    // Kiểm tra timeout workers
                    await CheckTimeoutWorkersAsync(configuration, workerInfoRepository);

                    // Kiểm tra và khôi phục công việc bị treo
                    await CheckStuckJobsAsync(jobRepository);

                    // Kiểm tra thời gian hoạt động cuối cùng của Manager và Register Job
                    await CheckJobActivityAsync(jobActivityService);

                    // Dọn dẹp công việc đã hết hạn
                    var cleanedCount = await jobRepository.CleanupExpiredJobsAsync();
                    if (cleanedCount > 0)
                    {
                        _logger.LogDebug("Cleaned up {Count} expired jobs", cleanedCount);
                    }

                    // Gửi thông báo với tần suất được kiểm soát (2-3 lần/ngày)
                    await SendHealthCheckNotificationAsync();

                    _logger.LogDebug("JobHealthCheckWorker completed successfully");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "JobHealthCheckWorker failed");
            }
        }

        public async override Task StopAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Stopping JobHealthCheckWorker ...");
            await _messageService.SendMessageWithTemplateAsync("JobHealthCheckWorker stopped");
            await base.StopAsync(cancellationToken);
        }

        /// <summary>
        /// Kiểm tra và xử lý worker timeout
        /// </summary>
        /// <param name="configuration">Cấu hình</param>
        /// <param name="workerInfoRepository">Worker repository</param>
        private async Task CheckTimeoutWorkersAsync(JobConfigurationDto configuration, IWorkerInfoRepository workerInfoRepository)
        {
            var timeoutThreshold = DateTime.UtcNow.AddMinutes(-configuration.WorkerTimeoutMinutes);
            await workerInfoRepository.CleanupTimeoutWorkersAsync(timeoutThreshold);

            var timeoutWorkers = await workerInfoRepository.GetByStatusAsync(WorkerStatus.Timeout);
            if (timeoutWorkers.Any())
            {
                _logger.LogWarning("Found {Count} timeout workers", timeoutWorkers.Count);

                foreach (var worker in timeoutWorkers)
                {
                    // Reset worker về trạng thái Idle
                    worker.Status = WorkerStatus.Idle;
                    worker.StartedAt = null;
                    worker.CompletedAt = null;
                    worker.TimeoutAt = null;
                    worker.BusinessApplicationId = null;
                    worker.ErrorMessage = "Worker timeout";
                    await workerInfoRepository.UpdateAsync(worker);

                    _logger.LogDebug("Reset timeout worker {WorkerId} to Idle", worker.WorkerId);
                }
            }
        }

        /// <summary>
        /// Kiểm tra và khôi phục công việc bị treo
        /// </summary>
        /// <param name="jobRepository">Job repository</param>
        private async Task CheckStuckJobsAsync(IJobRepository jobRepository)
        {
            // Lấy danh sách công việc đang xử lý quá lâu (hơn 30 phút)
            var stuckJobs = await jobRepository.GetByStatusAsync(JobStatus.InProcess);
            var timeoutThreshold = DateTime.UtcNow.AddMinutes(-30);

            var timeoutJobs = stuckJobs.Where(j => j.StartedAt.HasValue && j.StartedAt.Value < timeoutThreshold).ToList();

            if (timeoutJobs.Any())
            {
                _logger.LogWarning("Found {Count} stuck jobs", timeoutJobs.Count);

                foreach (var job in timeoutJobs)
                {
                    // Thử lại công việc nếu chưa quá số lần thử
                    if (job.RetryCount < job.MaxRetryCount)
                    {
                        job.Retry();
                        await jobRepository.UpdateAsync(job);
                        _logger.LogDebug("Retry stuck job {JobId}", job.Id);
                    }
                    else
                    {
                        // Đánh dấu lỗi nếu đã thử quá nhiều lần
                        job.MarkAsError("Job timeout after maximum retries");
                        await jobRepository.UpdateAsync(job);
                        _logger.LogWarning("Mark job {JobId} as error due to timeout", job.Id);
                    }
                }
            }
        }

        /// <summary>
        /// Kiểm tra thời gian hoạt động cuối cùng của Manager và Register Job
        /// </summary>
        /// <param name="jobActivityService">Job activity service</param>
        private async Task CheckJobActivityAsync(IJobActivityService jobActivityService)
        {
            var now = DateTime.UtcNow;
            var managerJobThreshold = now.AddMinutes(-10); // 10 phút
            var registerJobThreshold = now.AddMinutes(-10); // 10 phút

            var lastManagerJobRun = await jobActivityService.GetLastManagerJobRunAsync();
            if (lastManagerJobRun.HasValue && lastManagerJobRun.Value < managerJobThreshold)
            {
                _logger.LogWarning("Manager Job has not run recently. Last run: {LastRun}",
                    lastManagerJobRun.Value);
                await _messageService.SendMessageWithTemplateAsync("Manager Job has not run recently. Last run: " + lastManagerJobRun.Value);
            }

            var lastRegisterJobRun = await jobActivityService.GetLastRegisterJobRunAsync();
            if (lastRegisterJobRun.HasValue && lastRegisterJobRun.Value < registerJobThreshold)
            {
                _logger.LogWarning("Register Job has not run recently. Last run: {LastRun}",
                    lastRegisterJobRun.Value);
                await _messageService.SendMessageWithTemplateAsync("Register Job has not run recently. Last run: " + lastRegisterJobRun.Value);
            }
        }

        /// <summary>
        /// Gửi thông báo với tần suất được kiểm soát (2-3 lần/ngày)
        /// </summary>
        private async Task SendHealthCheckNotificationAsync()
        {
            var now = DateTime.UtcNow;

            // Các thời điểm cố định để gửi thông báo (UTC)
            // 9:00 AM, 2:00 PM, 6:00 PM (Vietnam time = UTC+7)
            var notificationHours = new[] { 2, 7, 11 }; // UTC time: 2:00, 7:00, 11:00

            // Kiểm tra xem hiện tại có phải là thời điểm thông báo không
            var shouldSendNotification = notificationHours.Contains(now.Hour) &&
                                       now.Minute < 12; // Chỉ gửi trong 12 phút đầu của giờ

            if (shouldSendNotification)
            {
                // Kiểm tra xem đã gửi thông báo cho giờ này chưa
                var today = now.Date;
                if (!_lastNotificationTime.HasValue ||
                    _lastNotificationTime.Value.Date != today ||
                    _lastNotificationTime.Value.Hour != now.Hour)
                {
                    await _messageService.SendMessageWithTemplateAsync("JobHealthCheckWorker completed successfully");
                    _lastNotificationTime = now;
                    _logger.LogDebug("Health check notification sent at {Time} (Vietnam time: {VietnamTime})",
                        now, now.AddHours(7));
                }
                else
                {
                    _logger.LogDebug("Notification already sent for hour {Hour} today", now.Hour);
                }
            }
            else
            {
                _logger.LogDebug("Not notification time - current time: {Time} (Vietnam time: {VietnamTime})",
                    now, now.AddHours(7));
            }
        }
    }
}