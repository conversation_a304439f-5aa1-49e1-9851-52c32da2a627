/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API BC Billing Group operations
    /// </summary>
    public class BcBillingGroupApi : IBcBillingGroupApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<BcBillingGroupApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the BcBillingGroupApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public BcBillingGroupApi(IApiClient apiClient, ILogger<BcBillingGroupApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        // /// <summary>
        // /// Create a Billing Group in a Business Center
        // /// </summary>
        // /// <param name="request">Create billing group request</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing the created billing group ID</returns>
        // public async Task<CreateBillingGroupResponse> CreateBillingGroupAsync(
        //     CreateBillingGroupRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BcId))
        //         throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BillingGroupName))
        //         throw new ArgumentException("Billing group name cannot be null or empty", nameof(request));
        //     if (request.AdvertiserIds == null || request.AdvertiserIds.Count == 0)
        //         throw new ArgumentException("Advertiser IDs cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Creating billing group '{BillingGroupName}' for BC {BcId}", 
        //         request.BillingGroupName, request.BcId);

        //     return await _apiClient.CallApiAsync<CreateBillingGroupResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCBillingGroupEndpoints.CreateBillingGroup}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <summary>
        // /// Update the settings of a Billing Group
        // /// </summary>
        // /// <param name="request">Update billing group request</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response indicating success or failure</returns>
        // public async Task<UpdateBillingGroupResponse> UpdateBillingGroupAsync(
        //     UpdateBillingGroupRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BcId))
        //         throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BillingGroupId))
        //         throw new ArgumentException("Billing group ID cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Updating billing group {BillingGroupId} for BC {BcId}", 
        //         request.BillingGroupId, request.BcId);

        //     return await _apiClient.CallApiAsync<UpdateBillingGroupResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCBillingGroupEndpoints.UpdateBillingGroup}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get all Billing Groups in a Business Center
        /// </summary>
        /// <param name="request">Get billing groups request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of billing groups with pagination</returns>
        public async Task<GetBillingGroupsResponse> GetBillingGroupsAsync(
            GetBillingGroupsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting billing groups for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering);
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetBillingGroupsResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCBillingGroupEndpoints.GetBillingGroups}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the list of advertisers that are bound to a Billing Group
        /// </summary>
        /// <param name="request">Get billing group advertisers request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of advertisers with pagination</returns>
        public async Task<GetBillingGroupAdvertisersResponse> GetBillingGroupAdvertisersAsync(
            GetBillingGroupAdvertisersRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));
            if (string.IsNullOrWhiteSpace(request.BillingGroupId))
                throw new ArgumentException("Billing group ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting advertisers for billing group {BillingGroupId} in BC {BcId}", 
                request.BillingGroupId, request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["billing_group_id"] = request.BillingGroupId
            };

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetBillingGroupAdvertisersResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCBillingGroupEndpoints.GetBillingGroupAdvertisers}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
