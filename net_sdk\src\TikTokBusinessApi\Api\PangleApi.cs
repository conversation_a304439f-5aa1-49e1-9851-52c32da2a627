/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Pangle operations
    /// </summary>
    public class PangleApi : IPangleApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<PangleApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the PangleApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public PangleApi(IApiClient apiClient, ILogger<PangleApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get the Pangle block list of an ad account.
        /// Use this endpoint to get the Pangle block list of an ad account.
        /// </summary>
        /// <param name="request">Request parameters for getting the Pangle block list</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the Pangle block list</returns>
        public async Task<GetPangleBlockListResponse> GetBlockListAsync(
            GetPangleBlockListRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.AdvertiserId))
                throw new ArgumentException("AdvertiserId is required", nameof(request));

            _logger?.LogDebug("Getting Pangle block list for advertiser: {AdvertiserId}", request.AdvertiserId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId
            };

            var endpoint = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{PangleEndpoints.GetBlockList}";

            return await _apiClient.CallApiAsync<GetPangleBlockListResponse>(
                endpoint,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Update the Pangle block list.
        /// Use this endpoint to update the Pangle block list.
        /// </summary>
        /// <param name="request">Request parameters for updating the Pangle block list</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the update result</returns>
        //public async Task<UpdatePangleBlockListResponse> UpdateBlockListAsync(
        //    UpdatePangleBlockListRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    if (string.IsNullOrEmpty(request.AdvertiserId))
        //        throw new ArgumentException("AdvertiserId is required", nameof(request));

        //    _logger?.LogDebug("Updating Pangle block list for advertiser: {AdvertiserId}", request.AdvertiserId);

        //    var endpoint = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{PangleEndpoints.UpdateBlockList}";

        //    return await _apiClient.CallApiAsync<UpdatePangleBlockListResponse>(
        //        endpoint,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get the Pangle audience packages that are available to an advertiser.
        /// This audience package is only available for Pangle ad placement, and will not affect traffic for other placements.
        /// The audience package will be applied alongside any other targeting settings you select.
        /// Audience packages can help you more effectively reach your target audience, while narrowing down the scope for delivery.
        /// </summary>
        /// <param name="request">Request parameters for getting the Pangle audience packages</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the Pangle audience packages</returns>
        public async Task<GetPangleAudiencePackagesResponse> GetAudiencePackagesAsync(
            GetPangleAudiencePackagesRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.AdvertiserId))
                throw new ArgumentException("AdvertiserId is required", nameof(request));

            _logger?.LogDebug("Getting Pangle audience packages for advertiser: {AdvertiserId}", request.AdvertiserId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId
            };

            var endpoint = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{PangleEndpoints.GetAudiencePackages}";

            return await _apiClient.CallApiAsync<GetPangleAudiencePackagesResponse>(
                endpoint,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
