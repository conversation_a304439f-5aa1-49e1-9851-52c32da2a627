/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;

namespace TikTokBusinessApi.Core
{
    /// <summary>
    /// Represents an API response
    /// </summary>
    /// <typeparam name="T">Response data type</typeparam>
    public class ApiResponse<T>
    {
        /// <summary>
        /// HTTP status code
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// Response headers
        /// </summary>
        public Dictionary<string, string> Headers { get; set; } = new();

        /// <summary>
        /// Response data
        /// </summary>
        public T? Data { get; set; }
    }

    /// <summary>
    /// Represents a TikTok API response wrapper
    /// </summary>
    /// <typeparam name="T">Data type</typeparam>
    public class TikTokApiResponse<T>
    {
        /// <summary>
        /// Response code (0 for success)
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// Request ID for tracking
        /// </summary>
        public string? RequestId { get; set; }

        /// <summary>
        /// Response data
        /// </summary>
        public T? Data { get; set; }
    }

    /// <summary>
    /// Represents a simplified response for SDK consumers
    /// </summary>
    /// <typeparam name="T">Data type</typeparam>
    public class Response<T>
    {
        /// <summary>
        /// Response data
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// Request ID for tracking
        /// </summary>
        public string? RequestId { get; set; }
    }

    /// <summary>
    /// Non-generic response for operations that don't return data
    /// </summary>
    public class Response : Response<object>
    {
    }
}
