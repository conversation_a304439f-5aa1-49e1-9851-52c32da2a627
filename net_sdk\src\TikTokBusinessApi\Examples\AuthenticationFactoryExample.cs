/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using TikTokBusinessApi.Auth;
using TikTokBusinessApi.Core;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Examples demonstrating the new authentication factory pattern
    /// </summary>
    public class AuthenticationFactoryExample
    {
        /// <summary>
        /// Example 1: Basic usage with configuration (simplest approach)
        /// </summary>
        public static async Task BasicConfigurationExample()
        {
            // Create configuration - authentication factory is automatically set up
            var config = Configuration.CreateWithCredentials(
                appId: "your_app_id_here",
                accessToken: "default_access_token"
            );

            var apiClient = config.CreateApiClient();

            // Make API calls with default authentication context
            var response = await apiClient.CallApiAsync<Dictionary<string, object>>(
                path: "/tt_user/token_info/get/",
                method: HttpMethod.Post,
                queryParams: null,
                body: new { /* your business parameters */ },
                headerParams: null,
                authNames: new string[] { "tiktok" }
            );
            // app_id and access_token are automatically injected

            Console.WriteLine("Basic configuration example completed");
        }

        /// <summary>
        /// Example 2: Multi-user scenario with DummyUserIdResolver
        /// </summary>
        public static async Task MultiUserExample()
        {
            // Set up dummy resolver with multiple users
            var dummyResolver = new DummyUserIdResolver("global_app_id", "default_token");
            
            // Add specific users with their tokens
            dummyResolver.AddUserTikTokCredentials("user123", "global_app_id", "user123_access_token");
            dummyResolver.AddUserTikTokCredentials("user456", "global_app_id", "user456_access_token");
            dummyResolver.AddUserTikTokCredentials("user789", "global_app_id", "user789_access_token");

            // Create factory and register it
            var tikTokFactory = new TikTokAuthenticationFactory(dummyResolver);
            var apiClient = new ApiClient();
            apiClient.RegisterAuthenticationFactory("tiktok", tikTokFactory);

            // Make API calls for different users
            await CallApiForUser(apiClient, "user123");
            await CallApiForUser(apiClient, "user456");
            await CallApiForUser(apiClient, "user789");
            await CallApiForUser(apiClient, "unknown_user"); // Will use default token

            Console.WriteLine("Multi-user example completed");
        }

        private static async Task CallApiForUser(IApiClient apiClient, string userId)
        {
            var authContext = AuthenticationContext.ForUser(userId);
            
            var response = await apiClient.CallApiAsync<Dictionary<string, object>>(
                path: "/tt_user/token_info/get/",
                method: HttpMethod.Post,
                authenticationContext: authContext,
                body: new { /* user-specific parameters */ }
            );

            Console.WriteLine($"API call completed for user: {userId}");
        }

        /// <summary>
        /// Example 3: Custom resolver implementation
        /// </summary>
        public static async Task CustomResolverExample()
        {
            // Create a custom resolver (e.g., database-backed)
            var customResolver = new DatabaseAuthenticationResolver();
            
            var tikTokFactory = new TikTokAuthenticationFactory(customResolver);
            var apiClient = new ApiClient();
            apiClient.RegisterAuthenticationFactory("tiktok", tikTokFactory);

            // Use with tenant and user context
            var authContext = AuthenticationContext.ForUserAndTenant("user123", "tenant_abc");
            
            var response = await apiClient.CallApiAsync<Dictionary<string, object>>(
                path: "/campaign/get/",
                method: HttpMethod.Post,
                authenticationContext: authContext,
                body: new { advertiser_id = "123456789" }
            );

            Console.WriteLine("Custom resolver example completed");
        }

        /// <summary>
        /// Example 4: Composite resolver with fallback
        /// </summary>
        public static async Task CompositeResolverExample()
        {
            // Primary resolver (e.g., cache or database)
            var primaryResolver = new DatabaseAuthenticationResolver();
            
            // Fallback resolver
            var fallbackResolver = new ConfigurationAuthenticationResolver("fallback_app_id", "fallback_token");
            
            // Composite resolver tries primary first, then fallback
            var compositeResolver = new CompositeAuthenticationResolver(new IAuthenticationResolver[] { primaryResolver, fallbackResolver });
            
            var tikTokFactory = new TikTokAuthenticationFactory(compositeResolver);
            var apiClient = new ApiClient();
            apiClient.RegisterAuthenticationFactory("tiktok", tikTokFactory);

            var authContext = AuthenticationContext.ForUser("user123");
            
            var response = await apiClient.CallApiAsync<Dictionary<string, object>>(
                path: "/tt_user/token_info/get/",
                method: HttpMethod.Post,
                authenticationContext: authContext
            );

            Console.WriteLine("Composite resolver example completed");
        }

        /// <summary>
        /// Example 5: Builder pattern for factory setup
        /// </summary>
        public static async Task BuilderPatternExample()
        {
            // Use builder pattern for clean factory setup
            var tikTokFactory = new TikTokAuthenticationFactoryBuilder()
                .WithDummyResolver("your_app_id", "default_token")
                .Build();

            var apiClient = new ApiClient();
            apiClient.RegisterAuthenticationFactory("tiktok", tikTokFactory);

            // Add users to the dummy resolver
            if (tikTokFactory.GetType().GetField("_resolver", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?.GetValue(tikTokFactory) is DummyUserIdResolver resolver)
            {
                resolver.AddUserTikTokCredentials("user1", "your_app_id", "user1_token");
                resolver.AddUserTikTokCredentials("user2", "your_app_id", "user2_token");
            }

            var authContext = AuthenticationContext.ForUser("user1");
            
            var response = await apiClient.CallApiAsync<Dictionary<string, object>>(
                path: "/tt_user/token_info/get/",
                method: HttpMethod.Post,
                authenticationContext: authContext
            );

            Console.WriteLine("Builder pattern example completed");
        }

        /// <summary>
        /// Example 6: Environment-specific multi-user setup
        /// </summary>
        public static async Task EnvironmentSpecificExample()
        {
            // Production setup
            var prodResolver = new DummyUserIdResolver("prod_app_id", "prod_default_token");
            prodResolver.AddUserTikTokCredentials("prod_user1", "prod_app_id", "prod_user1_token");
            
            var prodConfig = Configuration.CreateForProduction("prod_app_id", "prod_default_token");
            var prodClient = prodConfig.CreateApiClient();

            // Sandbox setup  
            var sandboxResolver = new DummyUserIdResolver("sandbox_app_id", "sandbox_default_token");
            sandboxResolver.AddUserTikTokCredentials("test_user1", "sandbox_app_id", "test_user1_token");
            
            var sandboxConfig = Configuration.CreateForSandbox("sandbox_app_id", "sandbox_default_token");
            var sandboxClient = sandboxConfig.CreateApiClient();

            // Use different clients for different environments
            await CallApiForUser(prodClient, "prod_user1");
            await CallApiForUser(sandboxClient, "test_user1");

            Console.WriteLine("Environment-specific example completed");
        }
    }

    /// <summary>
    /// Example custom authentication resolver that could connect to a database
    /// </summary>
    public class DatabaseAuthenticationResolver : IAuthenticationResolver
    {
        public async Task<AuthenticationCredentials?> ResolveCredentialsAsync(AuthenticationContext context)
        {
            // In a real implementation, this would query a database
            // For demo purposes, return mock credentials
            await Task.Delay(10); // Simulate database call

            if (context.UserId == "user123" && context.TenantId == "tenant_abc")
            {
                return AuthenticationCredentials.ForTikTok("tenant_abc_app_id", "user123_tenant_abc_token");
            }

            return null; // Not found
        }

        public async Task<bool> CanResolveAsync(AuthenticationContext context)
        {
            // In a real implementation, this would check if user exists in database
            await Task.Delay(5); // Simulate database call
            return !string.IsNullOrEmpty(context.UserId);
        }
    }
}
