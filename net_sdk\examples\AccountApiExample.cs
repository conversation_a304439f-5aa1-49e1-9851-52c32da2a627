/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models.Account;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the AccountApi
    /// </summary>
    public class AccountApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public AccountApiExample(string appId, string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(appId, accessToken);
        }

        /// <summary>
        /// Example: Get token information
        /// </summary>
        public async Task GetTokenInfoExample()
        {
            try
            {
                Console.WriteLine("=== Get Token Info Example ===");

                var request = new TokenInfoRequest
                {
                    AppId = "your-app-id",
                    AccessToken = "your-access-token"
                };

                var tokenInfo = await _client.Account.GetTokenInfoAsync(request);

                Console.WriteLine($"App ID: {tokenInfo.AppId}");
                Console.WriteLine($"Creator ID: {tokenInfo.CreatorId}");
                Console.WriteLine($"Scope: {tokenInfo.Scope}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get business profile data
        /// </summary>
        public async Task GetBusinessProfileExample()
        {
            try
            {
                Console.WriteLine("=== Get Business Profile Example ===");

                var businessId = "your-business-id";
                var startDate = "2023-01-01";
                var endDate = "2023-01-31";
                var fields = new List<string>
                {
                    "display_name",
                    "followers_count",
                    "username",
                    "is_business_account",
                    "video_views",
                    "likes",
                    "comments",
                    "shares"
                };

                var profile = await _client.Account.GetBusinessProfileAsync(
                    businessId, startDate, endDate, fields);

                Console.WriteLine($"Display Name: {profile.DisplayName}");
                Console.WriteLine($"Username: {profile.Username}");
                Console.WriteLine($"Followers: {profile.FollowersCount}");
                Console.WriteLine($"Is Business Account: {profile.IsBusinessAccount}");

                if (profile.Metrics != null && profile.Metrics.Count > 0)
                {
                    Console.WriteLine("\nDaily Metrics:");
                    foreach (var metric in profile.Metrics)
                    {
                        Console.WriteLine($"Date: {metric.Date}");
                        Console.WriteLine($"  Video Views: {metric.VideoViews}");
                        Console.WriteLine($"  Likes: {metric.Likes}");
                        Console.WriteLine($"  Comments: {metric.Comments}");
                        Console.WriteLine($"  Shares: {metric.Shares}");
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get video list
        /// </summary>
        public async Task GetVideoListExample()
        {
            try
            {
                Console.WriteLine("=== Get Video List Example ===");

                var businessId = "your-business-id";
                var fields = new List<string>
                {
                    "item_id",
                    "create_time",
                    "caption",
                    "video_views",
                    "likes",
                    "comments",
                    "shares",
                    "video_duration"
                };

                var videoList = await _client.Account.GetVideoListAsync(
                    businessId, fields, maxCount: 10);

                Console.WriteLine($"Total videos returned: {videoList.Videos.Count}");
                Console.WriteLine($"Has more: {videoList.HasMore}");

                foreach (var video in videoList.Videos)
                {
                    Console.WriteLine($"\nVideo ID: {video.ItemId}");
                    Console.WriteLine($"Caption: {video.Caption}");
                    Console.WriteLine($"Duration: {video.VideoDuration} seconds");
                    Console.WriteLine($"Views: {video.VideoViews}");
                    Console.WriteLine($"Likes: {video.Likes}");
                    Console.WriteLine($"Comments: {video.Comments}");
                    Console.WriteLine($"Shares: {video.Shares}");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get business benchmarks
        /// </summary>
        public async Task GetBenchmarkExample()
        {
            try
            {
                Console.WriteLine("=== Get Benchmark Example ===");

                var businessId = "your-business-id";
                var businessCategory = "BEAUTY"; // See documentation for available categories

                var benchmark = await _client.Account.GetBenchmarkAsync(businessId, businessCategory);

                Console.WriteLine($"Business Category: {benchmark.BusinessCategory}");
                Console.WriteLine($"Average Likes: {benchmark.AverageLikes:F2}");
                Console.WriteLine($"Average Comments: {benchmark.AverageComments:F2}");
                Console.WriteLine($"Average Shares: {benchmark.AverageShares:F2}");
                Console.WriteLine($"Average Video Count: {benchmark.AverageVideoCount:F2}");
                Console.WriteLine($"Average Follower Count: {benchmark.AverageFollowerCount:F2}");
                Console.WriteLine($"Average Follower Growth: {benchmark.AverageFollowerGrowth:F2}");
                Console.WriteLine($"Average Engagement Rate: {benchmark.AverageEngagementRate:F4}");
                Console.WriteLine($"Average Video Views: {benchmark.AverageVideoViews:F2}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get video settings
        /// </summary>
        public async Task GetVideoSettingsExample()
        {
            try
            {
                Console.WriteLine("=== Get Video Settings Example ===");

                var businessId = "your-business-id";

                var settings = await _client.Account.GetVideoSettingsAsync(businessId);

                Console.WriteLine($"Privacy Level Options: {string.Join(", ", settings.PrivacyLevelOptions)}");
                Console.WriteLine($"Comment Disabled: {settings.CommentDisabled}");
                Console.WriteLine($"Duet Disabled: {settings.DuetDisabled}");
                Console.WriteLine($"Stitch Disabled: {settings.StitchDisabled}");
                Console.WriteLine($"Max Video Duration: {settings.MaxVideoPostDurationSec} seconds");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get comments on a video
        /// </summary>
        public async Task GetCommentsExample()
        {
            try
            {
                Console.WriteLine("=== Get Comments Example ===");

                var businessId = "your-business-id";
                var videoId = "your-video-id";

                var comments = await _client.Account.GetCommentsAsync(
                    businessId, videoId, includeReplies: true, maxCount: 20);

                Console.WriteLine($"Total comments: {comments.Comments.Count}");
                Console.WriteLine($"Has more: {comments.HasMore}");

                foreach (var comment in comments.Comments)
                {
                    Console.WriteLine($"\nComment ID: {comment.CommentId}");
                    Console.WriteLine($"User: {comment.Username} ({comment.DisplayName})");
                    Console.WriteLine($"Text: {comment.Text}");
                    Console.WriteLine($"Likes: {comment.Likes}");
                    Console.WriteLine($"Replies: {comment.Replies}");
                    Console.WriteLine($"Status: {comment.Status}");
                    Console.WriteLine($"Owner: {comment.Owner}");

                    if (comment.ReplyList != null && comment.ReplyList.Count > 0)
                    {
                        Console.WriteLine("  Replies:");
                        foreach (var reply in comment.ReplyList)
                        {
                            Console.WriteLine($"    - {reply.Username}: {reply.Text} (Likes: {reply.Likes})");
                        }
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Create a comment
        /// </summary>
        public async Task CreateCommentExample()
        {
            try
            {
                Console.WriteLine("=== Create Comment Example ===");

                var request = new CommentCreateRequest
                {
                    BusinessId = "your-business-id",
                    VideoId = "your-video-id",
                    Text = "This is a great video! 👍"
                };

                var newComment = await _client.Account.CreateCommentAsync(request);

                Console.WriteLine($"Created comment ID: {newComment.CommentId}");
                Console.WriteLine($"Text: {newComment.Text}");
                Console.WriteLine($"Create Time: {newComment.CreateTime}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Create a comment reply
        /// </summary>
        public async Task CreateCommentReplyExample()
        {
            try
            {
                Console.WriteLine("=== Create Comment Reply Example ===");

                var request = new CommentReplyCreateRequest
                {
                    BusinessId = "your-business-id",
                    VideoId = "your-video-id",
                    CommentId = "parent-comment-id",
                    Text = "Thank you for your comment! 😊"
                };

                var newReply = await _client.Account.CreateCommentReplyAsync(request);

                Console.WriteLine($"Created reply ID: {newReply.CommentId}");
                Console.WriteLine($"Parent comment ID: {newReply.ParentCommentId}");
                Console.WriteLine($"Text: {newReply.Text}");
                Console.WriteLine($"Create Time: {newReply.CreateTime}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Like a comment
        /// </summary>
        public async Task LikeCommentExample()
        {
            try
            {
                Console.WriteLine("=== Like Comment Example ===");

                var request = new CommentLikeRequest
                {
                    BusinessId = "your-business-id",
                    CommentId = "comment-id-to-like",
                    Action = "LIKE" // or "UNLIKE"
                };

                await _client.Account.LikeCommentAsync(request);

                Console.WriteLine($"Successfully {request.Action.ToLower()}d comment {request.CommentId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _client?.Dispose();
        }
    }

    /// <summary>
    /// Program entry point for running the examples
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Replace with your actual app ID and access token
            const string appId = "your-app-id";
            const string accessToken = "your-access-token";
            
            var example = new AccountApiExample(appId, accessToken);
            
            try
            {
                Console.WriteLine("=== TikTok Business API - Account Examples ===\n");
                
                // Example 1: Get token info
                await example.GetTokenInfoExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 2: Get business profile
                await example.GetBusinessProfileExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 3: Get video list
                await example.GetVideoListExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 4: Get benchmarks
                await example.GetBenchmarkExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 5: Get video settings
                await example.GetVideoSettingsExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 6: Get comments
                await example.GetCommentsExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 7: Create comment
                await example.CreateCommentExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 8: Create comment reply
                await example.CreateCommentReplyExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 9: Like comment
                await example.LikeCommentExample();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
            finally
            {
                example.Dispose();
            }
        }
    }
}
