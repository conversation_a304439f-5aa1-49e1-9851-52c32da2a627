using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using TikTok.Campaigns;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Campaigns
{
    /// <summary>
    /// App Service cho chiến dịch quảng cáo
    /// </summary>
    [Authorize(TikTokPermissions.Campaigns.Default)]
    public class CampaignAppService :
        CrudAppService<
            RawCampaignEntity,
            CampaignDto,
            Guid,
            GetCampaignListDto,
            CreateCampaignDto,
            UpdateCampaignDto>,
        ICampaignAppService
    {
        private readonly ICampaignRepository _campaignRepository;

        public CampaignAppService(
            ICampaignRepository campaignRepository) : base(campaignRepository)
        {
            _campaignRepository = campaignRepository;

            GetPolicyName = TikTokPermissions.Campaigns.Default;
            GetListPolicyName = TikTokPermissions.Campaigns.Default;
            CreatePolicyName = TikTokPermissions.Campaigns.Create;
            UpdatePolicyName = TikTokPermissions.Campaigns.Edit;
            DeletePolicyName = TikTokPermissions.Campaigns.Delete;
        }

        public override async Task<PagedResultDto<CampaignDto>> GetListAsync(GetCampaignListDto input)
        {
            var totalCount = await _campaignRepository.GetCountAsync(
                input.Filter,
                input.AdvertiserId,
                input.CampaignName,
                input.ObjectiveType,
                input.CampaignType,
                input.OperationStatus,
                input.FromDate,
                input.ToDate);

            var campaigns = await _campaignRepository.GetListAsync(
                input.Sorting,
                input.MaxResultCount,
                input.SkipCount,
                input.Filter,
                input.AdvertiserId,
                input.CampaignName,
                input.ObjectiveType,
                input.CampaignType,
                input.OperationStatus,
                input.FromDate,
                input.ToDate);

            var campaignDtos = campaigns.Select(MapToGetListOutputDto).ToList();

            return new PagedResultDto<CampaignDto>
            {
                TotalCount = totalCount,
                Items = campaignDtos
            };
        }

        public override async Task<CampaignDto> CreateAsync(CreateCampaignDto input)
        {
            // Set creation and modification times
            var now = DateTime.UtcNow;
            input.CreateTime = now;
            input.ModifyTime = now;

            return await base.CreateAsync(input);
        }

        public override async Task<CampaignDto> UpdateAsync(Guid id, UpdateCampaignDto input)
        {
            // Update modification time
            input.ModifyTime = DateTime.UtcNow;

            return await base.UpdateAsync(id, input);
        }
    }
}