using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu báo cáo tích hợp Ad
    /// </summary>
    public class ReportIntegratedAdSyncService : BaseSyncService, IReportIntegratedAdSyncService
    {
        private readonly IRawReportIntegratedAdRepository _reportIntegratedAdRepository;
        private readonly IRepository<RawReportIntegratedAdEntity, Guid> _reportIntegratedAdEntityRepository;
        private readonly IAdAccountRepository _adAccountRepository;

        public ReportIntegratedAdSyncService(
            IServiceProvider serviceProvider,
            IRawReportIntegratedAdRepository reportIntegratedAdRepository,
            IRepository<RawReportIntegratedAdEntity, Guid> reportIntegratedAdEntityRepository,
            ILogger<ReportIntegratedAdSyncService> logger,
            IAdAccountRepository adAccountRepository) : base(serviceProvider, logger)
        {
            _reportIntegratedAdRepository = reportIntegratedAdRepository;
            _reportIntegratedAdEntityRepository = reportIntegratedAdEntityRepository;
            _adAccountRepository = adAccountRepository;
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp Ad cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedAdSyncResult> SyncAllReportIntegratedAdForAllBcsAsync()
        {
            var result = new ReportIntegratedAdSyncResult
            {
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp Ad cho tất cả BC");

                var allBcs = await _businessApplicationCache.GetAllActiveAsync();
                var bcIds = allBcs.Select(x => x.BcId).Distinct().ToList();

                var totalResult = new ReportIntegratedAdSyncResult
                {
                };

                foreach (var bcId in bcIds)
                {
                    var bcResult = await SyncReportIntegratedAdAsync(bcId);
                    totalResult.TotalSynced += bcResult.TotalSynced;
                    totalResult.NewRecords += bcResult.NewRecords;
                    totalResult.UpdatedRecords += bcResult.UpdatedRecords;
                    totalResult.DayCount += bcResult.DayCount;
                    totalResult.AdCount += bcResult.AdCount;
                    totalResult.BcCount++;
                }

                result = totalResult;

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo tích hợp Ad cho tất cả BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}, Ad: {AdCount}, Ngày: {DayCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount, result.AdCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp Ad cho tất cả BC");
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo tích hợp Ad: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp Ad cho tất cả BC");
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp Ad theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate">Ngày bắt đầu (tùy chọn)</param>
        /// <param name="endDate">Ngày kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedAdSyncResult> SyncReportIntegratedAdAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var result = new ReportIntegratedAdSyncResult
            {
                BcCount = 1
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp Ad cho BC: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                string bcTimezone = DateTimeService.UTC_TIMEZONE;

                var bc = await _businessCenterCache.GetByBcIdAsync(bcId);
                if (bc != null && !string.IsNullOrEmpty(bc.Timezone))
                {
                    bcTimezone = bc.Timezone;
                }

                (DateTime startDate, DateTime endDate) rangeDateFiltering;
                if (startDate.HasValue && endDate.HasValue)
                {
                    if(startDate.Value > endDate.Value)
                    {
                        throw new UserFriendlyException("Ngày bắt đầu phải nhỏ hơn ngày kết thúc");
                    }
                    rangeDateFiltering = (startDate.Value, endDate.Value);
                }
                else
                {
                    rangeDateFiltering = await GetRangeDateFiltering(bcId, bcTimezone);
                }

                var adAccountIds = await _adAccountRepository.GetByBcIdAsync(bcId);

                var currentDate = rangeDateFiltering.startDate.Date;
                var end = rangeDateFiltering.endDate.Date;

                while (currentDate <= end)
                {
                    var pageEnd = currentDate.AddDays(30);
                    var daysDifference = (end - currentDate).TotalDays;
                    if (daysDifference < 30)
                    {
                        pageEnd = end;
                    }
                    _logger.LogDebug("Đồng bộ dữ liệu báo cáo Ad cho ngày: {Date}", currentDate.ToString("yyyy-MM-dd"));

                    // Một lần chỉ xử lý tối đa 5 tài khoản quảng cáo
                    int pageSize = 5;
                    var totalPages = (int)Math.Ceiling((double)adAccountIds.Count / pageSize);
                    for (int page = 0; page < totalPages; page++)
                    {
                        var adAccountIdsChunk = adAccountIds.Skip(page * pageSize).Take(pageSize).ToList();
                        var apiResponse = await GetSyncReportIntegratedAdFromApiAsync(tikTokClient, bcId, adAccountIdsChunk, currentDate, pageEnd);
                        if (apiResponse != null && apiResponse.Any())
                        {
                            await ProcessReportDataAsync(bcId, adAccountIdsChunk, bcTimezone, apiResponse, result);
                        }
                        else
                        {
                            _logger.LogDebug("Không có dữ liệu báo cáo Ad cho BC: {BcId}, Ngày: {Date}", bcId, currentDate.ToString("yyyy-MM-dd"));
                        }
                    }

                    if (pageEnd == end)
                    {
                        break;
                    }
                    currentDate = currentDate.AddDays(29);
                }

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo tích hợp Ad cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Ad: {AdCount}, Ngày: {DayCount}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.AdCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp Ad cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo tích hợp Ad: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp Ad cho BC: {BcId}", bcId);
            }

            return result;
        }

        private async Task<(DateTime startDate, DateTime endDate)> GetRangeDateFiltering(string bcId, string timezone)
        {
            // Lấy ngày hiện tại theo timezone của BC
            DateTime currentDateInTimezone = _dateTimeService.GetDateNow(timezone);
            DateTime endDate = currentDateInTimezone;

            // Lấy dữ liệu báo cáo tích hợp ad mới nhất (theo giờ)
            var latestReport = await _reportIntegratedAdRepository.GetLatestByBcIdAsync(bcId);

            DateTime startDate;
            if (latestReport == null)
            {
                // Nếu chưa có dữ liệu trong DB thì lấy khoảng 1 tuần từ ngày hiện tại
                startDate = currentDateInTimezone.AddDays(-LAST_SYNC_DAYS);
                _logger.LogDebug("Chưa có dữ liệu báo cáo Ad trong DB cho BC: {BcId}. Lấy dữ liệu 1 tuần từ {StartDate} đến {EndDate}",
                    bcId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            }
            else
            {
                // Nếu có dữ liệu trong DB thì lấy từ ngày trong DB đến ngày hiện tại
                // Convert từ UTC (trong DB) sang timezone của BC để so sánh
                var latestReportDateInTimezone = _dateTimeService.ConvertFromUtc(latestReport.Date, timezone).Date;
                startDate = latestReportDateInTimezone;
                _logger.LogDebug("Có dữ liệu báo cáo Ad trong DB cho BC: {BcId}. Lấy từ {StartDate} đến {EndDate}",
                    bcId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            }

            return (startDate, endDate);
        }

        /// <summary>
        /// Lấy dữ liệu báo cáo tích hợp Ad từ TikTok API
        /// Lưu ý: Khi lấy báo cáo theo giờ, startDate và endDate phải cùng một ngày
        /// </summary>
        private async Task<List<ReportDataItem>> GetSyncReportIntegratedAdFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, List<string> adAccountIds, DateTime startDate, DateTime endDate)
        {

            var records = new List<ReportDataItem>();
            var page = 1;
            const int pageSize = PAGE_SIZE_SYNC_REPORT;

            while (true)
            {
                var request = new SynchronousReportRequest
                {
                    AdvertiserIds = adAccountIds,
                    ServiceType = "AUCTION",
                    ReportType = "BASIC",
                    DataLevel = "AUCTION_AD",
                    Dimensions = new List<string> { "ad_id", "stat_time_day" },
                    Metrics = new List<string>
                    {
                        "spend", "impressions", "clicks", "ctr", "cpm", "cpc", "conversion", "cost_per_conversion", "reach", "frequency",
                        "video_play_actions", "video_watched_2s", "video_watched_6s", "video_views_p25", "video_views_p50", "video_views_p75", "video_views_p100", "average_video_play", "engaged_view",
                        "onsite_shopping_roas", "total_onsite_shopping_value", "onsite_shopping", "cost_per_onsite_shopping", "value_per_onsite_shopping", "onsite_on_web_detail", "onsite_on_web_cart", "onsite_initiate_checkout_count",
                        "live_views", "live_unique_views", "live_effective_views", "live_product_clicks",
                        "ad_id", "ad_name", "ad_text", "call_to_action", "image_mode", "is_aco", "is_smart_creative",
                        "advertiser_name", "advertiser_id", "campaign_name", "campaign_id", "adgroup_id", "adgroup_name", "currency", "timezone"
                    },
                    StartDate = startDate.ToString("yyyy-MM-dd"),
                    EndDate = endDate.ToString("yyyy-MM-dd"),
                    Page = page,
                    PageSize = pageSize
                };

                var response = await tikTokClient.Reporting.GetSynchronousReportAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu báo cáo tích hợp Ad: {response.Message}");
                }

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break;
                }

                records.AddRange(response.Data.List);

                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            return records;
        }

        /// <summary>
        /// Xử lý dữ liệu báo cáo từ API
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="adAccountIds">Danh sách ID tài khoản quảng cáo</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportDataList">Danh sách dữ liệu báo cáo</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task ProcessReportDataAsync(string bcId, List<string> adAccountIds, string bcTimezone, List<ReportDataItem> reportDataList, ReportIntegratedAdSyncResult result)
        {
            // sử dụng UnitOfWork để đảm bảo tính toàn vẹn dữ liệu cho mỗi 1000 bản ghi
            if (reportDataList == null || !reportDataList.Any())
            {
                _logger.LogDebug("Không có dữ liệu báo cáo Ad để xử lý cho {Total} bản ghi", reportDataList.Count);
                return;
            }

            var pageSize = PAGE_SIZE_HANDLE_SAVE_TO_DATABASE;
            var totalPages = (int)Math.Ceiling((double)reportDataList.Count / pageSize);
            _logger.LogDebug("Bắt đầu xử lý {TotalRecords} bản ghi báo cáo Ad cho {TotalPages} trang", reportDataList.Count, totalPages);

            for (int page = 0; page < totalPages; page++)
            {
                var pageData = reportDataList.Skip(page * pageSize).Take(pageSize).ToList();
                if (pageData.Any())
                {
                    try
                    {
                        await ProcessPageDataAsync(bcId, bcTimezone, pageData, result);
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý dữ liệu báo cáo Ad cho {Total} bản ghi, Trang: {Page}", pageData.Count, page + 1);
                        result.ErrorMessage += ex.Message + Environment.NewLine;
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        continue; // Bỏ qua lỗi và tiếp tục với trang tiếp theo
                    }
                }
            }
        }

        private async Task ProcessPageDataAsync(string bcId, string bcTimezone, List<ReportDataItem> pageData, ReportIntegratedAdSyncResult result)
        {
            // use unit of work
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var mappedEntities = await MapListReportDataToEntitiesAsync(bcId, bcTimezone, pageData);

                var existingEntities = await _reportIntegratedAdRepository.GetByBcIdAndDateRangeAsync(bcId, mappedEntities.Min(x => x.Date), mappedEntities.Max(x => x.Date));

                var insertedEntities = new List<RawReportIntegratedAdEntity>();
                var updatedEntities = new List<RawReportIntegratedAdEntity>();

                foreach (var mappedEntity in mappedEntities)
                {
                    // So sánh theo AdId và Date với độ chính xác đến giờ
                    var currentEntity = existingEntities.FirstOrDefault(x => x.AdId == mappedEntity.AdId && x.Date == mappedEntity.Date);
                    if (currentEntity == null)
                    {
                        insertedEntities.Add(mappedEntity);
                        result.NewRecords++;
                        result.TotalSynced++;
                    }
                    else
                    {
                        // Cập nhật nếu có thay đổi
                        if (currentEntity.HasChanged(mappedEntity))
                        {
                            UpdateEntityFromNewData(currentEntity, mappedEntity);
                            updatedEntities.Add(currentEntity);
                            result.UpdatedRecords++;
                            result.TotalSynced++;
                        }
                    }
                }

                // Thêm các bản ghi mới vào kho dữ liệu
                if (insertedEntities.Any())
                {
                    await _reportIntegratedAdEntityRepository.InsertManyAsync(insertedEntities);
                }
                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _reportIntegratedAdEntityRepository.UpdateManyAsync(updatedEntities);
                }

                result.DayCount += pageData.Count;
                result.AdCount += pageData.Select(x => x.Dimensions?.GetValueOrDefault("ad_id")?.ToString()).Distinct().Count();

                await uow.CompleteAsync();
            }
        }

        private async Task<List<RawReportIntegratedAdEntity>> MapListReportDataToEntitiesAsync(string bcId, string bcTimezone, List<ReportDataItem> reportDataList)
        {
            var entities = new List<RawReportIntegratedAdEntity>();
            foreach (var reportData in reportDataList)
            {
                var entity = await MapReportDataToEntityAsync(bcId, bcTimezone, reportData);
                if (entity != null)
                {
                    entities.Add(entity);
                }
            }
            return entities;
        }

        /// <summary>
        /// Map dữ liệu báo cáo từ API sang entity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportData">Dữ liệu báo cáo</param>
        /// <returns>RawReportIntegratedAdEntity</returns>
        private async Task<RawReportIntegratedAdEntity?> MapReportDataToEntityAsync(string bcId, string bcTimezone, ReportDataItem reportData)
        {
            var dateTimeStr = reportData.Dimensions?.GetValueOrDefault("stat_time_day")?.ToString();

            if (string.IsNullOrEmpty(dateTimeStr) || !DateTime.TryParse(dateTimeStr, out var reportDateTime))
            {
                _logger.LogWarning("Ngày giờ báo cáo Ad không hợp lệ: {DateTime} cho BC: {BcId}", dateTimeStr, bcId);
                return null;
            }

            var adTimezone = GetStringValue(reportData.Metrics, "timezone") ?? bcTimezone;
            // Convert datetime từ timezone của ad sang UTC
            var reportDateTimeUtc = _dateTimeService.ConvertToUtc(reportDateTime, adTimezone);

            var adId = reportData.Dimensions?.GetValueOrDefault("ad_id")?.ToString();
            if (string.IsNullOrEmpty(adId))
            {
                _logger.LogWarning("Ad ID không hợp lệ cho BC: {BcId}", bcId);
                return null;
            }

            var entity = new RawReportIntegratedAdEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdId = adId,
                Date = reportDateTimeUtc  // Lưu datetime UTC với giờ
            };

            if (reportData.Metrics != null)
            {
                // Map các metrics từ API response
                entity.AdvertiserId = GetStringValue(reportData.Metrics, "advertiser_id") ?? string.Empty;
                entity.AdvertiserName = GetStringValue(reportData.Metrics, "advertiser_name");
                entity.CampaignId = GetStringValue(reportData.Metrics, "campaign_id") ?? string.Empty;
                entity.CampaignName = GetStringValue(reportData.Metrics, "campaign_name");
                entity.AdGroupId = GetStringValue(reportData.Metrics, "adgroup_id") ?? string.Empty;
                entity.AdName = GetStringValue(reportData.Metrics, "ad_name") ?? string.Empty;
                entity.AdText = GetStringValue(reportData.Metrics, "ad_text");
                entity.CallToAction = GetStringValue(reportData.Metrics, "call_to_action");
                entity.ImageMode = GetStringValue(reportData.Metrics, "image_mode");
                entity.IsAco = GetLongValue(reportData.Metrics, "is_aco") == 1;
                entity.IsSmartCreative = GetLongValue(reportData.Metrics, "is_smart_creative") == 1;
                entity.Spend = GetDecimalValue(reportData.Metrics, "spend");
                entity.Impressions = GetLongValue(reportData.Metrics, "impressions");
                entity.Clicks = GetLongValue(reportData.Metrics, "clicks");
                entity.Ctr = GetDecimalValue(reportData.Metrics, "ctr");
                entity.Cpm = GetDecimalValue(reportData.Metrics, "cpm");
                entity.Cpc = GetDecimalValue(reportData.Metrics, "cpc");
                entity.Conversion = GetLongValue(reportData.Metrics, "conversion");
                entity.CostPerConversion = GetDecimalValue(reportData.Metrics, "cost_per_conversion");
                entity.Reach = GetLongValue(reportData.Metrics, "reach");
                entity.Frequency = GetDecimalValue(reportData.Metrics, "frequency");
                entity.VideoPlayActions = GetLongValue(reportData.Metrics, "video_play_actions");
                entity.VideoWatched2s = GetLongValue(reportData.Metrics, "video_watched_2s");
                entity.VideoWatched6s = GetLongValue(reportData.Metrics, "video_watched_6s");
                entity.VideoViewsP25 = GetLongValue(reportData.Metrics, "video_views_p25");
                entity.VideoViewsP50 = GetLongValue(reportData.Metrics, "video_views_p50");
                entity.VideoViewsP75 = GetLongValue(reportData.Metrics, "video_views_p75");
                entity.VideoViewsP100 = GetLongValue(reportData.Metrics, "video_views_p100");
                entity.AverageVideoPlay = GetDecimalValue(reportData.Metrics, "average_video_play");
                entity.EngagedView = GetLongValue(reportData.Metrics, "engaged_view");
                entity.OnsiteShoppingRoas = GetDecimalValue(reportData.Metrics, "onsite_shopping_roas");
                entity.TotalOnsiteShoppingValue = GetDecimalValue(reportData.Metrics, "total_onsite_shopping_value");
                entity.OnsiteShopping = GetLongValue(reportData.Metrics, "onsite_shopping");
                entity.CostPerOnsiteShopping = GetDecimalValue(reportData.Metrics, "cost_per_onsite_shopping");
                entity.ValuePerOnsiteShopping = GetDecimalValue(reportData.Metrics, "value_per_onsite_shopping");
                entity.OnsiteOnWebDetail = GetLongValue(reportData.Metrics, "onsite_on_web_detail");
                entity.OnsiteOnWebCart = GetLongValue(reportData.Metrics, "onsite_on_web_cart");
                entity.OnsiteInitiateCheckoutCount = GetLongValue(reportData.Metrics, "onsite_initiate_checkout_count");
                entity.LiveViews = GetLongValue(reportData.Metrics, "live_views");
                entity.LiveUniqueViews = GetLongValue(reportData.Metrics, "live_unique_views");
                entity.LiveEffectiveViews = GetLongValue(reportData.Metrics, "live_effective_views");
                entity.LiveProductClicks = GetLongValue(reportData.Metrics, "live_product_clicks");
                entity.Currency = GetStringValue(reportData.Metrics, "currency") ?? "USD";
            }

            return entity;
        }

        /// <summary>
        /// Cập nhật entity từ dữ liệu mới
        /// </summary>
        /// <param name="existingEntity">Entity hiện tại</param>
        /// <param name="newEntity">Entity mới</param>
        private void UpdateEntityFromNewData(RawReportIntegratedAdEntity existingEntity, RawReportIntegratedAdEntity newEntity)
        {
            existingEntity.AdvertiserId = newEntity.AdvertiserId;
            existingEntity.AdvertiserName = newEntity.AdvertiserName;
            existingEntity.CampaignId = newEntity.CampaignId;
            existingEntity.CampaignName = newEntity.CampaignName;
            existingEntity.AdGroupId = newEntity.AdGroupId;
            existingEntity.AdName = newEntity.AdName;
            existingEntity.AdText = newEntity.AdText;
            existingEntity.CallToAction = newEntity.CallToAction;
            existingEntity.ImageMode = newEntity.ImageMode;
            existingEntity.IsAco = newEntity.IsAco;
            existingEntity.IsSmartCreative = newEntity.IsSmartCreative;
            existingEntity.Spend = newEntity.Spend;
            existingEntity.Impressions = newEntity.Impressions;
            existingEntity.Clicks = newEntity.Clicks;
            existingEntity.Ctr = newEntity.Ctr;
            existingEntity.Cpm = newEntity.Cpm;
            existingEntity.Cpc = newEntity.Cpc;
            existingEntity.Conversion = newEntity.Conversion;
            existingEntity.CostPerConversion = newEntity.CostPerConversion;
            existingEntity.Reach = newEntity.Reach;
            existingEntity.Frequency = newEntity.Frequency;
            existingEntity.VideoPlayActions = newEntity.VideoPlayActions;
            existingEntity.VideoWatched2s = newEntity.VideoWatched2s;
            existingEntity.VideoWatched6s = newEntity.VideoWatched6s;
            existingEntity.VideoViewsP25 = newEntity.VideoViewsP25;
            existingEntity.VideoViewsP50 = newEntity.VideoViewsP50;
            existingEntity.VideoViewsP75 = newEntity.VideoViewsP75;
            existingEntity.VideoViewsP100 = newEntity.VideoViewsP100;
            existingEntity.AverageVideoPlay = newEntity.AverageVideoPlay;
            existingEntity.EngagedView = newEntity.EngagedView;
            existingEntity.OnsiteShoppingRoas = newEntity.OnsiteShoppingRoas;
            existingEntity.TotalOnsiteShoppingValue = newEntity.TotalOnsiteShoppingValue;
            existingEntity.OnsiteShopping = newEntity.OnsiteShopping;
            existingEntity.CostPerOnsiteShopping = newEntity.CostPerOnsiteShopping;
            existingEntity.ValuePerOnsiteShopping = newEntity.ValuePerOnsiteShopping;
            existingEntity.OnsiteOnWebDetail = newEntity.OnsiteOnWebDetail;
            existingEntity.OnsiteOnWebCart = newEntity.OnsiteOnWebCart;
            existingEntity.OnsiteInitiateCheckoutCount = newEntity.OnsiteInitiateCheckoutCount;
            existingEntity.LiveViews = newEntity.LiveViews;
            existingEntity.LiveUniqueViews = newEntity.LiveUniqueViews;
            existingEntity.LiveEffectiveViews = newEntity.LiveEffectiveViews;
            existingEntity.LiveProductClicks = newEntity.LiveProductClicks;
            existingEntity.Currency = newEntity.Currency;
        }
    }
}