/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Enum for brand safety inventory filter types
    /// </summary>
    public enum BrandSafetyType
    {
        /// <summary>
        /// Expanded inventory. Your ads will not appear next to explicitly inappropriate content, but they may appear next to content that features mature themes.
        /// </summary>
        [JsonPropertyName("EXPANDED_INVENTORY")]
        ExpandedInventory,

        /// <summary>
        /// Standard inventory. Your ads will appear next to content that is appropriate for most brands.
        /// </summary>
        [JsonPropertyName("STANDARD_INVENTORY")]
        StandardInventory,

        /// <summary>
        /// Limited inventory. Your ads will not appear next to content that contains mature themes.
        /// </summary>
        [JsonPropertyName("LIMITED_INVENTORY")]
        LimitedInventory,

        /// <summary>
        /// Full inventory without using any brand safety solution, which means your ads may appear next to some content featuring mature themes.
        /// </summary>
        [JsonPropertyName("NO_BRAND_SAFETY")]
        NoBrandSafety
    }

    /// <summary>
    /// Brand safety settings for an ad account
    /// </summary>
    public class BrandSafetySettings
    {
        /// <summary>
        /// Whether to apply the brand safety settings to all advertising objectives
        /// </summary>
        [JsonPropertyName("cover_all_ad_objectives")]
        public bool CoverAllAdObjectives { get; set; }

        /// <summary>
        /// Inventory filter tier
        /// </summary>
        [JsonPropertyName("brand_safety_type")]
        public string? BrandSafetyType { get; set; }

        /// <summary>
        /// The ID list of content categories that you want to avoid showing your ads next to
        /// </summary>
        [JsonPropertyName("category_exclusion_ids")]
        public List<string>? CategoryExclusionIds { get; set; }

        /// <summary>
        /// The ID of the vertical category to exclude sensitive vertical-related content
        /// </summary>
        [JsonPropertyName("vertical_sensitivity_id")]
        public string? VerticalSensitivityId { get; set; }
    }

    /// <summary>
    /// Request body for getting brand safety settings
    /// </summary>
    public class BrandSafetyGetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request body for updating brand safety settings
    /// </summary>
    public class BrandSafetyUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Whether to apply the brand safety settings to all advertising objectives
        /// </summary>
        [JsonPropertyName("cover_all_ad_objectives")]
        public bool CoverAllAdObjectives { get; set; }

        /// <summary>
        /// Inventory filter tier
        /// </summary>
        [JsonPropertyName("brand_safety_type")]
        public string? BrandSafetyType { get; set; }

        /// <summary>
        /// The ID list of content categories that you want to avoid showing your ads next to
        /// </summary>
        [JsonPropertyName("category_exclusion_ids")]
        public List<string>? CategoryExclusionIds { get; set; }

        /// <summary>
        /// The ID of the vertical category to exclude sensitive vertical-related content
        /// </summary>
        [JsonPropertyName("vertical_sensitivity_id")]
        public string? VerticalSensitivityId { get; set; }
    }

    /// <summary>
    /// Response for brand safety settings operations
    /// </summary>
    public class BrandSafetyResponse
    {
        /// <summary>
        /// Response code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string? RequestId { get; set; }

        /// <summary>
        /// Brand safety settings data
        /// </summary>
        [JsonPropertyName("data")]
        public BrandSafetySettings? Data { get; set; }
    }
}
