/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Pangle endpoints
    /// </summary>
    public static class PangleEndpoints
    {
        /// <summary>
        /// Get the Pangle block list endpoint (v1.3)
        /// </summary>
        public const string GetBlockList = "/pangle_block_list/get/";

        /// <summary>
        /// Update the Pangle block list endpoint (v1.3)
        /// </summary>
        public const string UpdateBlockList = "/pangle_block_list/update/";

        /// <summary>
        /// Get the Pangle audience packages endpoint (v1.3)
        /// </summary>
        public const string GetAudiencePackages = "/pangle_audience_package/get/";
    }
}
