/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for getting GMV Max campaigns
    /// </summary>
    public class GMVMaxCampaignGetFiltering
    {
        /// <summary>
        /// GMV Max Campaign type
        /// </summary>
        [JsonPropertyName("gmv_max_promotion_types")]
        public List<string> GMVMaxPromotionTypes { get; set; } = new List<string>();

        /// <summary>
        /// A list of TikTok Shop IDs
        /// </summary>
        [JsonPropertyName("store_ids")]
        public List<string>? StoreIds { get; set; }

        /// <summary>
        /// A list of campaign IDs
        /// </summary>
        [JsonPropertyName("campaign_ids")]
        public List<string>? CampaignIds { get; set; }

        /// <summary>
        /// Campaign name
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// Primary status
        /// </summary>
        [JsonPropertyName("primary_status")]
        public string? PrimaryStatus { get; set; }

        /// <summary>
        /// The earliest campaign creation time
        /// </summary>
        [JsonPropertyName("creation_filter_start_time")]
        public string? CreationFilterStartTime { get; set; }

        /// <summary>
        /// The latest campaign creation time
        /// </summary>
        [JsonPropertyName("creation_filter_end_time")]
        public string? CreationFilterEndTime { get; set; }
    }

    /// <summary>
    /// GMV Max Campaign information
    /// </summary>
    public class GMVMaxCampaign
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign name
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string CampaignName { get; set; } = string.Empty;

        /// <summary>
        /// The ON/OFF status of campaign
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string OperationStatus { get; set; } = string.Empty;

        /// <summary>
        /// The time when the campaign was created
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;

        /// <summary>
        /// The time when the campaign was last modified
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string ModifyTime { get; set; } = string.Empty;

        /// <summary>
        /// Advertising objective
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string ObjectiveType { get; set; } = string.Empty;

        /// <summary>
        /// Secondary status
        /// </summary>
        [JsonPropertyName("secondary_status")]
        public string SecondaryStatus { get; set; } = string.Empty;

        /// <summary>
        /// The ROI protection compensation status of the campaign.
        /// </summary>
        [JsonPropertyName("roi_protection_compensation_status")]
        public string RoiProtectionCompensationStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting GMV Max campaigns
    /// </summary>
    public class GMVMaxCampaignGetResponse
    {
        /// <summary>
        /// A list of campaigns
        /// </summary>
        [JsonPropertyName("list")]
        public List<GMVMaxCampaign> List { get; set; } = new List<GMVMaxCampaign>();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new PageInfo();
    }

    /// <summary>
    /// Identity information for GMV Max campaigns
    /// </summary>
    public class GMVMaxIdentity
    {
        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string IdentityId { get; set; } = string.Empty;

        /// <summary>
        /// Identity type
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string IdentityType { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the Business Center that a TikTok Account User in Business Center identity is associated with
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// The ID of the TikTok Shop that the TikTok Account User in Business Center identity is associated with
        /// </summary>
        [JsonPropertyName("identity_authorized_shop_id")]
        public string? IdentityAuthorizedShopId { get; set; }

        /// <summary>
        /// The ID of the TikTok Shop that a TikTok Account User for TikTok Shop identity is associated with
        /// </summary>
        [JsonPropertyName("store_id")]
        public string? StoreId { get; set; }

        /// <summary>
        /// Temporary profile image URL for the TikTok account
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string? ProfileImage { get; set; }

        /// <summary>
        /// The display name of the TikTok account
        /// </summary>
        [JsonPropertyName("display_name")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// The username of the TikTok account
        /// </summary>
        [JsonPropertyName("user_name")]
        public string? UserName { get; set; }

        /// <summary>
        /// Whether the identity is being used in enabled Video Shopping Ads, Product Shopping Ads or Live Shopping Ads
        /// </summary>
        [JsonPropertyName("is_running_custom_shop_ads")]
        public bool? IsRunningCustomShopAds { get; set; }

        /// <summary>
        /// Whether the identity is available for use in Product GMV Max Campaigns
        /// </summary>
        [JsonPropertyName("product_gmv_max_available")]
        public bool? ProductGMVMaxAvailable { get; set; }

        /// <summary>
        /// Whether the identity is available for use in LIVE GMV Max Campaigns
        /// </summary>
        [JsonPropertyName("live_gmv_max_available")]
        public bool? LiveGMVMaxAvailable { get; set; }

        /// <summary>
        /// The reason why the identity is not available for LIVE GMV Max Campaigns
        /// </summary>
        [JsonPropertyName("unavailable_reason")]
        public string? UnavailableReason { get; set; }
    }

    /// <summary>
    /// Video information for GMV Max campaigns
    /// </summary>
    public class GMVMaxVideoInfo
    {
        /// <summary>
        /// The ID of the video
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Temporary URL for the video cover
        /// </summary>
        [JsonPropertyName("video_cover_url")]
        public string? VideoCoverUrl { get; set; }

        /// <summary>
        /// Temporary preview URL for the video
        /// </summary>
        [JsonPropertyName("preview_url")]
        public string? PreviewUrl { get; set; }

        /// <summary>
        /// The height of the video in pixels
        /// </summary>
        [JsonPropertyName("height")]
        public int? Height { get; set; }

        /// <summary>
        /// The width of the video in pixels
        /// </summary>
        [JsonPropertyName("width")]
        public int? Width { get; set; }

        /// <summary>
        /// The bit rate of the video in bps
        /// </summary>
        [JsonPropertyName("bit_rate")]
        public long? BitRate { get; set; }

        /// <summary>
        /// The duration of the video in seconds
        /// </summary>
        [JsonPropertyName("duration")]
        public double? Duration { get; set; }

        /// <summary>
        /// The size of the video in bytes
        /// </summary>
        [JsonPropertyName("size")]
        public long? Size { get; set; }

        /// <summary>
        /// The MD5 of the video
        /// </summary>
        [JsonPropertyName("signature")]
        public string? Signature { get; set; }

        /// <summary>
        /// The format of the video
        /// </summary>
        [JsonPropertyName("format")]
        public string? Format { get; set; }

        /// <summary>
        /// The definition of the video
        /// </summary>
        [JsonPropertyName("definition")]
        public string? Definition { get; set; }

        /// <summary>
        /// The frames per second (FPS) of the video
        /// </summary>
        [JsonPropertyName("fps")]
        public int? Fps { get; set; }
    }

    /// <summary>
    /// Item information for GMV Max campaigns
    /// </summary>
    public class GMVMaxItem
    {
        /// <summary>
        /// The ID of the TikTok post
        /// </summary>
        [JsonPropertyName("item_id")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// The caption of the TikTok post
        /// </summary>
        [JsonPropertyName("text")]
        public string? Text { get; set; }

        /// <summary>
        /// The list of Product SPU IDs that the TikTok post is associated with
        /// </summary>
        [JsonPropertyName("spu_id_list")]
        public List<string> SpuIdList { get; set; } = new List<string>();

        /// <summary>
        /// Information about the identity associated with the TikTok post
        /// </summary>
        [JsonPropertyName("identity_info")]
        public GMVMaxIdentity IdentityInfo { get; set; } = new GMVMaxIdentity();

        /// <summary>
        /// Details of the video in the post
        /// </summary>
        [JsonPropertyName("video_info")]
        public GMVMaxVideoInfo? VideoInfo { get; set; }
    }

    /// <summary>
    /// Customized TikTok posts associated for GMV Max campaigns
    /// </summary>
    public class GMVMaxCustomAnchorVideoItem
    {
        /// <summary>
        /// The ID of the TikTok post
        /// </summary>
        [JsonPropertyName("item_id")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// The list of Product SPU IDs that the TikTok post is associated with
        /// </summary>
        [JsonPropertyName("spu_id_list")]
        public List<string> SpuIdList { get; set; } = new List<string>();

        /// <summary>
        /// Information about the identity associated with the TikTok post
        /// </summary>
        [JsonPropertyName("identity_info")]
        public GMVMaxIdentity IdentityInfo { get; set; } = new GMVMaxIdentity();
    }

    /// <summary>
    /// Detailed GMV Max Campaign information
    /// </summary>
    public class GMVMaxCampaignInfo
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The status of the GMV Max Campaign
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string OperationStatus { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the GMV Max Campaign
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// The name of the GMV Max Campaign
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string CampaignName { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_id")]
        public string StoreId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the Business Center that is authorized to access the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_authorized_bc_id")]
        public string StoreAuthorizedBcId { get; set; } = string.Empty;

        /// <summary>
        /// The type of the GMV Max Campaign
        /// </summary>
        [JsonPropertyName("shopping_ads_type")]
        public string ShoppingAdsType { get; set; } = string.Empty;

        /// <summary>
        /// Different dimensions to choose products
        /// </summary>
        [JsonPropertyName("product_specific_type")]
        public string? ProductSpecificType { get; set; }

        /// <summary>
        /// Product SPU (standard product unit) IDs
        /// </summary>
        [JsonPropertyName("item_group_ids")]
        public List<string>? ItemGroupIds { get; set; }

        /// <summary>
        /// Optimization goal
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string OptimizationGoal { get; set; } = string.Empty;

        /// <summary>
        /// Whether the campaign is eligible for ROI protection
        /// </summary>
        [JsonPropertyName("roi_protection_enabled")]
        public bool? RoiProtectionEnabled { get; set; }

        /// <summary>
        /// Bid strategy
        /// </summary>
        [JsonPropertyName("deep_bid_type")]
        public string DeepBidType { get; set; } = string.Empty;

        /// <summary>
        /// ROI target
        /// </summary>
        [JsonPropertyName("roas_bid")]
        public double? RoasBid { get; set; }

        /// <summary>
        /// Daily budget
        /// </summary>
        [JsonPropertyName("budget")]
        public decimal Budget { get; set; }

        /// <summary>
        /// Schedule type
        /// </summary>
        [JsonPropertyName("schedule_type")]
        public string ScheduleType { get; set; } = string.Empty;

        /// <summary>
        /// Campaign delivery start time
        /// </summary>
        [JsonPropertyName("schedule_start_time")]
        public string ScheduleStartTime { get; set; } = string.Empty;

        /// <summary>
        /// Campaign delivery end time
        /// </summary>
        [JsonPropertyName("schedule_end_time")]
        public string? ScheduleEndTime { get; set; }

        /// <summary>
        /// Placements
        /// </summary>
        [JsonPropertyName("placements")]
        public List<string> Placements { get; set; } = new List<string>();

        /// <summary>
        /// The IDs of the targeted locations
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string> LocationIds { get; set; } = new List<string>();

        /// <summary>
        /// The targeted age groups
        /// </summary>
        [JsonPropertyName("age_groups")]
        public List<string> AgeGroups { get; set; } = new List<string>();

        /// <summary>
        /// The video selection mode
        /// </summary>
        [JsonPropertyName("product_video_specific_type")]
        public string? ProductVideoSpecificType { get; set; }

        /// <summary>
        /// The list of identities (TikTok accounts) associated with the GMV Max Campaign
        /// </summary>
        [JsonPropertyName("identity_list")]
        public List<GMVMaxIdentity> IdentityList { get; set; } = new List<GMVMaxIdentity>();

        /// <summary>
        /// Whether to enable affiliate posts for your Product GMV Max Campaign
        /// </summary>
        [JsonPropertyName("affiliate_posts_enabled")]
        public bool? AffiliatePostsEnabled { get; set; }

        /// <summary>
        /// The list of authorized TikTok posts or customized TikTok posts associated with the GMV Max campaign
        /// </summary>
        [JsonPropertyName("item_list")]
        public List<GMVMaxItem> ItemList { get; set; } = new List<GMVMaxItem>();

        /// <summary>
        /// The ID of the collection of customized posts created in the Product GMV Max Campaign
        /// </summary>
        [JsonPropertyName("campaign_custom_anchor_video_id")]
        public string? CampaignCustomAnchorVideoId { get; set; }

        /// <summary>
        /// The list of customized TikTok posts associated with the GMV Max campaign
        /// </summary>
        [JsonPropertyName("custom_anchor_video_list")]
        public List<GMVMaxCustomAnchorVideoItem> CustomAnchorVideoList { get; set; } = new List<GMVMaxCustomAnchorVideoItem>();
    }

    /// <summary>
    /// Request body for creating a GMV Max Campaign
    /// </summary>
    public class GMVMaxCampaignCreateBody
    {
        /// <summary>
        /// Request ID that supports idempotency
        /// </summary>
        [JsonPropertyName("request_id")]
        public string RequestId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_id")]
        public string StoreId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the Business Center that is authorized to access the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_authorized_bc_id")]
        public string StoreAuthorizedBcId { get; set; } = string.Empty;

        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The type of the GMV Max Campaign
        /// </summary>
        [JsonPropertyName("shopping_ads_type")]
        public string ShoppingAdsType { get; set; } = string.Empty;

        /// <summary>
        /// Different dimensions to choose products
        /// </summary>
        [JsonPropertyName("product_specific_type")]
        public string? ProductSpecificType { get; set; }

        /// <summary>
        /// Product SPU (standard product unit) IDs
        /// </summary>
        [JsonPropertyName("item_group_ids")]
        public List<string>? ItemGroupIds { get; set; }

        /// <summary>
        /// Optimization goal
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string OptimizationGoal { get; set; } = string.Empty;

        /// <summary>
        /// Bid strategy
        /// </summary>
        [JsonPropertyName("deep_bid_type")]
        public string DeepBidType { get; set; } = string.Empty;

        /// <summary>
        /// ROI target
        /// </summary>
        [JsonPropertyName("roas_bid")]
        public double? RoasBid { get; set; }

        /// <summary>
        /// Daily budget
        /// </summary>
        [JsonPropertyName("budget")]
        public double Budget { get; set; }

        /// <summary>
        /// Schedule type
        /// </summary>
        [JsonPropertyName("schedule_type")]
        public string ScheduleType { get; set; } = string.Empty;

        /// <summary>
        /// Campaign delivery start time
        /// </summary>
        [JsonPropertyName("schedule_start_time")]
        public string ScheduleStartTime { get; set; } = string.Empty;

        /// <summary>
        /// Campaign delivery end time
        /// </summary>
        [JsonPropertyName("schedule_end_time")]
        public string? ScheduleEndTime { get; set; }

        /// <summary>
        /// The video selection mode
        /// </summary>
        [JsonPropertyName("product_video_specific_type")]
        public string? ProductVideoSpecificType { get; set; }

        /// <summary>
        /// The list of identities (TikTok accounts) to associate with the GMV Max Campaign
        /// </summary>
        [JsonPropertyName("identity_list")]
        public List<GMVMaxIdentity>? IdentityList { get; set; }

        /// <summary>
        /// Whether to enable affiliate posts for your Product GMV Max Campaign
        /// </summary>
        [JsonPropertyName("affiliate_posts_enabled")]
        public bool? AffiliatePostsEnabled { get; set; }

        /// <summary>
        /// The list of authorized TikTok posts or customized TikTok posts to associate with the Product GMV Max campaign
        /// </summary>
        [JsonPropertyName("item_list")]
        public List<GMVMaxItem>? ItemList { get; set; }

        /// <summary>
        /// The list of customized TikTok posts to associate with the Product GMV Max campaign
        /// </summary>
        [JsonPropertyName("custom_anchor_video_list")]
        public List<GMVMaxItem>? CustomAnchorVideoList { get; set; }

        /// <summary>
        /// The name of the GMV Max Campaign
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string CampaignName { get; set; } = string.Empty;
    }

    /// <summary>
    /// TikTok Shop information for GMV Max campaigns
    /// </summary>
    public class GMVMaxStore
    {
        /// <summary>
        /// ID of the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_id")]
        public string StoreId { get; set; } = string.Empty;

        /// <summary>
        /// Whether the TikTok Shop is available for GMV Max Campaigns
        /// </summary>
        [JsonPropertyName("is_gmv_max_available")]
        public bool IsGMVMaxAvailable { get; set; }

        /// <summary>
        /// ID of the Business Center that is authorized to access the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_authorized_bc_id")]
        public string StoreAuthorizedBcId { get; set; } = string.Empty;

        /// <summary>
        /// Whether the Business Center owns the TikTok Shop
        /// </summary>
        [JsonPropertyName("is_owner_bc")]
        public bool IsOwnerBc { get; set; }

        /// <summary>
        /// Information about the Business Center that is authorized to access the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_authorized_bc_info")]
        public GMVMaxStoreAuthorizedBcInfo StoreAuthorizedBcInfo { get; set; } = new GMVMaxStoreAuthorizedBcInfo();

        /// <summary>
        /// The thumbnail URL of the TikTok Shop
        /// </summary>
        [JsonPropertyName("thumbnail_url")]
        public string? ThumbnailUrl { get; set; }

        /// <summary>
        /// The name of the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_name")]
        public string StoreName { get; set; } = string.Empty;

        /// <summary>
        /// The shop code of the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_code")]
        public string StoreCode { get; set; } = string.Empty;

        /// <summary>
        /// The codes of the regions that the TikTok Shop can target
        /// </summary>
        [JsonPropertyName("targeting_region_codes")]
        public List<string> TargetingRegionCodes { get; set; } = new List<string>();

        /// <summary>
        /// The status of the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_status")]
        public string StoreStatus { get; set; } = string.Empty;

        /// <summary>
        /// Business Center user's permission to the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_role")]
        public string StoreRole { get; set; } = string.Empty;

        /// <summary>
        /// Information about the GMV Max exclusive authorization of the TikTok Shop
        /// </summary>
        [JsonPropertyName("exclusive_authorized_advertiser_info")]
        public GMVMaxExclusiveAuthorizedAdvertiserInfo ExclusiveAuthorizedAdvertiserInfo { get; set; } = new GMVMaxExclusiveAuthorizedAdvertiserInfo();
    }

    /// <summary>
    /// Business Center information for GMV Max stores
    /// </summary>
    public class GMVMaxStoreAuthorizedBcInfo
    {
        /// <summary>
        /// The ID of the Business Center
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// The profile image URL of the Business Center
        /// </summary>
        [JsonPropertyName("bc_profile_image")]
        public string? BcProfileImage { get; set; }

        /// <summary>
        /// The name of the Business Center
        /// </summary>
        [JsonPropertyName("bc_name")]
        public string BcName { get; set; } = string.Empty;

        /// <summary>
        /// The role of the user (member) within the Business Center
        /// </summary>
        [JsonPropertyName("user_role")]
        public string UserRole { get; set; } = string.Empty;
    }

    /// <summary>
    /// Exclusive authorized advertiser information for GMV Max stores
    /// </summary>
    public class GMVMaxExclusiveAuthorizedAdvertiserInfo
    {
        /// <summary>
        /// The ID of the only ad account that is authorized to create GMV Max Campaigns for the TikTok Shop
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The name of the only ad account that is authorized to create GMV Max Campaigns for the TikTok Shop
        /// </summary>
        [JsonPropertyName("advertiser_name")]
        public string AdvertiserName { get; set; } = string.Empty;

        /// <summary>
        /// The status of the only ad account that is authorized to create GMV Max Campaign for the TikTok Shop
        /// </summary>
        [JsonPropertyName("advertiser_status")]
        public string AdvertiserStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting TikTok Shops for GMV Max campaigns
    /// </summary>
    public class GMVMaxStoreListResponse
    {
        /// <summary>
        /// The list of TikTok Shops that the ad account has access to
        /// </summary>
        [JsonPropertyName("store_list")]
        public List<GMVMaxStore> StoreList { get; set; } = new List<GMVMaxStore>();
    }

    /// <summary>
    /// Response for getting identities for GMV Max campaigns
    /// </summary>
    public class GMVMaxIdentityListResponse
    {
        /// <summary>
        /// The list of identities associated with the TikTok Shop
        /// </summary>
        [JsonPropertyName("identity_list")]
        public List<GMVMaxIdentity> IdentityList { get; set; } = new List<GMVMaxIdentity>();
    }

    /// <summary>
    /// Bid recommendation response for GMV Max campaigns
    /// </summary>
    public class GMVMaxBidRecommendationResponse
    {
        /// <summary>
        /// Recommended ROI target for the optimization goal Gross revenue
        /// </summary>
        [JsonPropertyName("roas_bid")]
        public double RoasBid { get; set; }

        /// <summary>
        /// Recommended daily budget for the optimization goal Gross revenue
        /// </summary>
        [JsonPropertyName("budget")]
        public double Budget { get; set; }
    }

    /// <summary>
    /// Request body for updating a GMV Max Campaign
    /// </summary>
    public class GMVMaxCampaignUpdateBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the GMV Max Campaign
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// The name of the GMV Max Campaign
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// Daily budget
        /// </summary>
        [JsonPropertyName("budget")]
        public double? Budget { get; set; }

        /// <summary>
        /// ROI target
        /// </summary>
        [JsonPropertyName("roas_bid")]
        public double? RoasBid { get; set; }

        /// <summary>
        /// Campaign delivery start time
        /// </summary>
        [JsonPropertyName("schedule_start_time")]
        public string? ScheduleStartTime { get; set; }

        /// <summary>
        /// Campaign delivery end time
        /// </summary>
        [JsonPropertyName("schedule_end_time")]
        public string? ScheduleEndTime { get; set; }

        /// <summary>
        /// Product SPU (standard product unit) IDs
        /// </summary>
        [JsonPropertyName("item_group_ids")]
        public List<string>? ItemGroupIds { get; set; }

        /// <summary>
        /// The list of customized TikTok posts to associate with the Product GMV Max campaign
        /// </summary>
        [JsonPropertyName("custom_anchor_video_list")]
        public List<GMVMaxItem>? CustomAnchorVideoList { get; set; }
    }

    /// <summary>
    /// Request body for updating GMV Max Campaign status
    /// </summary>
    public class GMVMaxCampaignStatusUpdateBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the GMV Max Campaign
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// The status of the GMV Max Campaign
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string OperationStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// Filtering conditions for GMV Max Campaign reports
    /// </summary>
    public class GMVMaxReportFiltering
    {
        /// <summary>
        /// Filter by the GMV Max Campaign type, Enum values: PRODUCT, LIVE
        /// </summary>
        [JsonPropertyName("gmv_max_promotion_types")]
        public List<string>? GMVMaxPromotionTypes { get; set; }

        /// <summary>
        /// Filter by a list of GMV Max Campaign IDs, max 100
        /// </summary>
        [JsonPropertyName("campaign_ids")]
        public List<string>? CampaignIds { get; set; }

        /// <summary>
        /// Filter by a GMV Max Campaign name
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// Filter by campaign statuses, Enum values: STATUS_DELIVERY_OK, STATUS_DISABLE, STATUS_DELETE
        /// </summary>
        [JsonPropertyName("campaign_statuses")]
        public List<string>? CampaignStatuses { get; set; }

        /// <summary>
        /// Filter by a list of product SPU IDs, max 100
        /// </summary>
        [JsonPropertyName("item_group_ids")]
        public List<string>? ItemGroupIds { get; set; }

        /// <summary>
        /// Filter by creative types
        /// </summary>
        [JsonPropertyName("creative_types")]
        public List<string>? CreativeTypes { get; set; }

        /// <summary>
        /// Filter by creative statuses, Enum values: IN_QUEUE, LEARNING, DELIVERING, NOT_DELIVERYING, AUTHORIZATION_NEEDED, EXCLUDED, UNAVAILABLE, REJECTED
        /// </summary>
        [JsonPropertyName("creative_delivery_statuses")]
        public List<string>? CreativeDeliveryStatuses { get; set; }

        /// <summary>
        /// Filter by a search keyword
        /// </summary>
        [JsonPropertyName("search_word")]
        public string? SearchWord { get; set; }

        /// <summary>
        /// Filter by a list of Livestream room IDs, max 100
        /// </summary>
        [JsonPropertyName("room_ids")]
        public List<string>? RoomIds { get; set; }
    }

    /// <summary>
    /// Individual item in GMV Max Campaign report
    /// </summary>
    public class GMVMaxReportItem
    {
        /// <summary>
        /// All requested dimension data
        /// </summary>
        [JsonPropertyName("dimensions")]
        public Dictionary<string, object>? Dimensions { get; set; }

        /// <summary>
        /// All requested metric data
        /// </summary>
        [JsonPropertyName("metrics")]
        public Dictionary<string, object>? Metrics { get; set; }
    }

    /// <summary>
    /// Request for GMV Max Campaign report
    /// </summary>
    public class GMVMaxReportRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// A list of TikTok Shop IDs (max size: 1)
        /// </summary>
        public List<string> StoreIds { get; set; } = new List<string>();

        /// <summary>
        /// Query start date (closed interval) in the format of YYYY-MM-DD
        /// </summary>
        public string StartDate { get; set; } = string.Empty;

        /// <summary>
        /// Query end date (closed interval) in the format of YYYY-MM-DD
        /// </summary>
        public string EndDate { get; set; } = string.Empty;

        /// <summary>
        /// Metrics to query
        /// </summary>
        public List<string> Metrics { get; set; } = new List<string>();

        /// <summary>
        /// Dimension groupings
        /// </summary>
        public List<string> Dimensions { get; set; } = new List<string>();

        /// <summary>
        /// Whether to enable the total added-up data for requested metrics
        /// Default: false
        /// </summary>
        public bool EnableTotalMetrics { get; set; } = false;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        public GMVMaxReportFiltering? Filtering { get; set; }

        /// <summary>
        /// Sorting field
        /// </summary>
        public string? SortField { get; set; }

        /// <summary>
        /// Sorting order (ASC, DESC)
        /// Default: DESC
        /// </summary>
        public string? SortType { get; set; }

        /// <summary>
        /// Current page number
        /// Value range: ≥1, Default: 1
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// Page size
        /// Value range: 1-1,000, Default: 10
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// Response for GMV Max Campaign report
    /// </summary>
    public class GMVMaxReportResponse
    {
        /// <summary>
        /// The total added-up data for requested metrics (returned only when enable_total_metrics is true)
        /// </summary>
        [JsonPropertyName("total_metrics")]
        public Dictionary<string, object>? TotalMetrics { get; set; }

        /// <summary>
        /// Data list
        /// </summary>
        [JsonPropertyName("list")]
        public List<GMVMaxReportItem> List { get; set; } = new List<GMVMaxReportItem>();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new PageInfo();
    }
}
