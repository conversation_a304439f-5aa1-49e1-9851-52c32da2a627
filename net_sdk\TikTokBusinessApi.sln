Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TikTokBusinessApi", "src\TikTokBusinessApi\TikTokBusinessApi.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TikTokBusinessApi.Tests", "tests\TikTokBusinessApi.Tests\TikTokBusinessApi.Tests.csproj", "{DC7BC617-8D5C-4EC3-A19D-87D8ACFD3329}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TikTokBusinessApi.ConsoleClient", "src\TikTokBusinessApi.ConsoleClient\TikTokBusinessApi.ConsoleClient.csproj", "{E79C35B4-8661-45E9-A2D7-F05465ED2E79}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC7BC617-8D5C-4EC3-A19D-87D8ACFD3329}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC7BC617-8D5C-4EC3-A19D-87D8ACFD3329}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC7BC617-8D5C-4EC3-A19D-87D8ACFD3329}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC7BC617-8D5C-4EC3-A19D-87D8ACFD3329}.Release|Any CPU.Build.0 = Release|Any CPU
		{E79C35B4-8661-45E9-A2D7-F05465ED2E79}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E79C35B4-8661-45E9-A2D7-F05465ED2E79}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E79C35B4-8661-45E9-A2D7-F05465ED2E79}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E79C35B4-8661-45E9-A2D7-F05465ED2E79}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-5678-9012-123456789012}
	EndGlobalSection
EndGlobal
