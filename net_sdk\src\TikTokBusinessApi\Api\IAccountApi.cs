/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models.Account;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Account operations
    /// </summary>
    public interface IAccountApi
    {
        /// <summary>
        /// Get the permission scopes of a TikTok Business Account or a TikTok Personal Account that are authorized by the TikTok account user
        /// </summary>
        /// <param name="request">Token info request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Token information response</returns>
        Task<TokenInfoResponse> GetTokenInfoAsync(TokenInfoRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Access detailed analytics and insights about a TikTok account's follower base and profile engagement
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok account</param>
        /// <param name="startDate">Query start date, closed interval, format such as: 2021-06-01</param>
        /// <param name="endDate">Query end date, closed interval, format such as: 2021-06-01</param>
        /// <param name="fields">Requested fields. If not set, returns the default fields only</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Business profile response</returns>
        Task<BusinessProfileResponse> GetBusinessProfileAsync(
            string businessId,
            string? startDate = null,
            string? endDate = null,
            List<string>? fields = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get reach and engagement data for all the public video posts of a TikTok account
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok account</param>
        /// <param name="fields">Requested fields. If not set, returns the default field only</param>
        /// <param name="filters">Filters to apply to the result set</param>
        /// <param name="cursor">Cursor for pagination</param>
        /// <param name="maxCount">The maximum number of videos that will be returned for each page</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Video list response</returns>
        Task<VideoListResponse> GetVideoListAsync(
            string businessId,
            List<string>? fields = null,
            VideoListFilters? filters = null,
            long? cursor = null,
            int? maxCount = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve the benchmarks specific to a business category
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok account</param>
        /// <param name="businessCategory">Business category</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Benchmark response</returns>
        Task<BenchmarkResponse> GetBenchmarkAsync(
            string businessId,
            string businessCategory,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Obtain the post privacy settings of a TikTok account
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok account</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Video settings response</returns>
        Task<VideoSettingsResponse> GetVideoSettingsAsync(
            string businessId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Access all the comments or only the specified comments (along with related information) - both public and hidden - that have been created against a specific organic video posted by an owned TikTok Account
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok account</param>
        /// <param name="videoId">Unique identifier for owned TikTok video to list comments on</param>
        /// <param name="commentIds">A list of IDs for comments or comment replies that you want to filter the results by</param>
        /// <param name="includeReplies">Whether to include replies to the top-level comments in the results</param>
        /// <param name="status">The visibility status of comments that you want to filter the results by</param>
        /// <param name="sortField">Specific field to sort comments by</param>
        /// <param name="sortOrder">Specific order to sort comments by</param>
        /// <param name="cursor">Cursor for pagination</param>
        /// <param name="maxCount">The maximum number of comments that will be returned for each page</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Comment list response</returns>
        Task<CommentListResponse> GetCommentsAsync(
            string businessId,
            string videoId,
            List<string>? commentIds = null,
            bool? includeReplies = null,
            string? status = null,
            string? sortField = null,
            string? sortOrder = null,
            int? cursor = null,
            int? maxCount = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Access all replies to a specific comment (along with related information) - both public and hidden - that have been created against a comment on an organic video posted by an owned TikTok Account
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok account</param>
        /// <param name="videoId">Unique identifier for owned TikTok video to list comments on</param>
        /// <param name="commentId">Unique identifier for comment on an owned TikTok video to list replies on</param>
        /// <param name="status">Enumerated status of comment reply visibility</param>
        /// <param name="sortField">Specific field to sort comment replies by</param>
        /// <param name="sortOrder">Specific order to sort comment replies by</param>
        /// <param name="cursor">Cursor for pagination</param>
        /// <param name="maxCount">The maximum number of comment replies that will be returned for each page</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Comment list response</returns>
        Task<CommentListResponse> GetCommentRepliesAsync(
            string businessId,
            string videoId,
            string commentId,
            string? status = null,
            string? sortField = null,
            string? sortOrder = null,
            int? cursor = null,
            int? maxCount = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the publishing status of a TikTok video post or photo post
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok account</param>
        /// <param name="publishId">Unique identifier for a post publishing task</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Publish status response</returns>
        Task<PublishStatusResponse> GetPublishStatusAsync(string businessId, string publishId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get recommended hashtags for TikTok accounts
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok account</param>
        /// <param name="keyword">The keyword that you want to get recommended hashtags for</param>
        /// <param name="language">Keyword language</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Hashtag suggestion response</returns>
        Task<HashtagSuggestionResponse> GetHashtagSuggestionsAsync(string businessId, string keyword, string? language = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the authorization status of a TikTok post
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok account</param>
        /// <param name="itemId">The ID of the TikTok post</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Post authorization status response</returns>
        Task<PostAuthorizationStatusResponse> GetPostAuthorizationStatusAsync(string businessId, string itemId, CancellationToken cancellationToken = default);
    }
}
