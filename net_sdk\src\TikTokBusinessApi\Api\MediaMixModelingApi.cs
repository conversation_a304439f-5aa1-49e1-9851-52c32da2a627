/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Media Mix Modeling operations
    /// </summary>
    public class MediaMixModelingApi : IMediaMixModelingApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<MediaMixModelingApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the MediaMixModelingApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public MediaMixModelingApi(IApiClient apiClient, ILogger<MediaMixModelingApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Create a Media Mix Modeling (MMM) data request.
        /// After you create the data request, wait for up to 24 hours for the data processing to complete.
        /// Then use CheckMmmDataRequestStatusAsync to check the data request status.
        /// </summary>
        /// <param name="request">MMM data request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the MMM request ID</returns>
        // public async Task<MmmCreateResponse> CreateMmmDataRequestAsync(
        //     MmmCreateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));
        //     if (request.AdvertiserIds == null || request.AdvertiserIds.Count == 0)
        //         throw new ArgumentException("Advertiser IDs cannot be null or empty", nameof(request));
        //     if (request.RequestInfo == null)
        //         throw new ArgumentException("Request info cannot be null", nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.RequestInfo.FromDate))
        //         throw new ArgumentException("From date cannot be null or empty", nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.RequestInfo.ToDate))
        //         throw new ArgumentException("To date cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Creating MMM data request for {AdvertiserCount} advertisers from {FromDate} to {ToDate}",
        //         request.AdvertiserIds.Count, request.RequestInfo.FromDate, request.RequestInfo.ToDate);

        //     return await _apiClient.CallApiAsync<MmmCreateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MediaMixModelingEndpoints.CreateMmmDataRequest}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Check the status of a Media Mix Modeling (MMM) data request.
        /// Once the status of the data request is "completed - ready for download",
        /// you can use GetMmmDataDownloadUrlAsync to obtain a download URL for the data.
        /// </summary>
        /// <param name="mmmRequestId">Unique identifier for the MMM data request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the request status</returns>
        //public async Task<MmmCheckResponse> CheckMmmDataRequestStatusAsync(
        //    string mmmRequestId,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(mmmRequestId))
        //        throw new ArgumentException("MMM request ID cannot be null or empty", nameof(mmmRequestId));

        //    _logger?.LogInformation("Checking status of MMM data request {MmmRequestId}", mmmRequestId);

        //    var queryParams = new Dictionary<string, string>
        //    {
        //        ["mmm_request_id"] = mmmRequestId
        //    };

        //    return await _apiClient.CallApiAsync<MmmCheckResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MediaMixModelingEndpoints.CheckMmmDataRequestStatus}",
        //        HttpMethod.Get,
        //        queryParams: queryParams,
        //        body: null,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Obtain the download URL for Media Mix Modeling (MMM) data.
        /// The URL is valid for seven days. Once the URL expires, you need to call this method again to obtain a new URL.
        /// </summary>
        /// <param name="mmmRequestId">Unique identifier for the MMM data request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the download URL</returns>
        public async Task<MmmDownloadResponse> GetMmmDataDownloadUrlAsync(
            string mmmRequestId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(mmmRequestId))
                throw new ArgumentException("MMM request ID cannot be null or empty", nameof(mmmRequestId));

            _logger?.LogInformation("Getting download URL for MMM data request {MmmRequestId}", mmmRequestId);

            var queryParams = new Dictionary<string, string>
            {
                ["mmm_request_id"] = mmmRequestId
            };

            return await _apiClient.CallApiAsync<MmmDownloadResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MediaMixModelingEndpoints.GetMmmDataDownloadUrl}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Retrieve the details of historical Media Mix Modeling (MMM) data requests.
        /// </summary>
        /// <param name="request">Request parameters including date range</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing historical request details</returns>
        public async Task<MmmHistoryResponse> GetMmmDataRequestHistoryAsync(
            MmmHistoryRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.FromDate))
                throw new ArgumentException("From date cannot be null or empty", nameof(request));
            if (string.IsNullOrWhiteSpace(request.EndDate))
                throw new ArgumentException("End date cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting MMM data request history from {FromDate} to {EndDate}",
                request.FromDate, request.EndDate);

            var queryParams = new Dictionary<string, string>
            {
                ["from_date"] = request.FromDate,
                ["end_date"] = request.EndDate
            };

            return await _apiClient.CallApiAsync<MmmHistoryResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MediaMixModelingEndpoints.GetMmmDataRequestHistory}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
