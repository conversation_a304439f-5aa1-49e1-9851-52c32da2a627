/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Base class for all TikTok webhook events
    /// </summary>
    public abstract class WebhookEventBase
    {
        /// <summary>
        /// Event type identifier
        /// </summary>
        [JsonPropertyName("event_type")]
        public string EventType { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when the event occurred
        /// </summary>
        [JsonPropertyName("timestamp")]
        public long Timestamp { get; set; }

        /// <summary>
        /// Unique identifier for the event
        /// </summary>
        [JsonPropertyName("event_id")]
        public string EventId { get; set; } = string.Empty;

        /// <summary>
        /// Version of the webhook event format
        /// </summary>
        [JsonPropertyName("version")]
        public string Version { get; set; } = string.Empty;
    }

    /// <summary>
    /// Webhook event for advertiser account operations
    /// </summary>
    public class AdvertiserAccountWebhookEvent : WebhookEventBase
    {
        /// <summary>
        /// Advertiser account data
        /// </summary>
        [JsonPropertyName("data")]
        public AdvertiserAccountEventData Data { get; set; } = new();
    }

    /// <summary>
    /// Data for advertiser account webhook events
    /// </summary>
    public class AdvertiserAccountEventData
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Advertiser name
        /// </summary>
        [JsonPropertyName("advertiser_name")]
        public string AdvertiserName { get; set; } = string.Empty;

        /// <summary>
        /// Account status
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Business center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Currency code
        /// </summary>
        [JsonPropertyName("currency")]
        public string Currency { get; set; } = string.Empty;

        /// <summary>
        /// Timezone
        /// </summary>
        [JsonPropertyName("timezone")]
        public string Timezone { get; set; } = string.Empty;
    }

    /// <summary>
    /// Webhook event for campaign operations
    /// </summary>
    public class CampaignWebhookEvent : WebhookEventBase
    {
        /// <summary>
        /// Campaign data
        /// </summary>
        [JsonPropertyName("data")]
        public CampaignEventData Data { get; set; } = new();
    }

    /// <summary>
    /// Data for campaign webhook events
    /// </summary>
    public class CampaignEventData
    {
        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign name
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string CampaignName { get; set; } = string.Empty;

        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign objective type
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string ObjectiveType { get; set; } = string.Empty;

        /// <summary>
        /// Campaign status
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Budget amount in micros
        /// </summary>
        [JsonPropertyName("budget")]
        public long Budget { get; set; }

        /// <summary>
        /// Budget mode
        /// </summary>
        [JsonPropertyName("budget_mode")]
        public string BudgetMode { get; set; } = string.Empty;
    }

    /// <summary>
    /// Webhook event for ad group operations
    /// </summary>
    public class AdGroupWebhookEvent : WebhookEventBase
    {
        /// <summary>
        /// Ad group data
        /// </summary>
        [JsonPropertyName("data")]
        public AdGroupEventData Data { get; set; } = new();
    }

    /// <summary>
    /// Data for ad group webhook events
    /// </summary>
    public class AdGroupEventData
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string AdGroupId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group name
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string AdGroupName { get; set; } = string.Empty;

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group status
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Optimization event
        /// </summary>
        [JsonPropertyName("optimization_event")]
        public string OptimizationEvent { get; set; } = string.Empty;

        /// <summary>
        /// Bid type
        /// </summary>
        [JsonPropertyName("bid_type")]
        public string BidType { get; set; } = string.Empty;

        /// <summary>
        /// Budget amount in micros
        /// </summary>
        [JsonPropertyName("budget")]
        public long Budget { get; set; }
    }

    /// <summary>
    /// Webhook event for ad operations
    /// </summary>
    public class AdWebhookEvent : WebhookEventBase
    {
        /// <summary>
        /// Ad data
        /// </summary>
        [JsonPropertyName("data")]
        public AdEventData Data { get; set; } = new();
    }

    /// <summary>
    /// Data for ad webhook events
    /// </summary>
    public class AdEventData
    {
        /// <summary>
        /// Ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string AdId { get; set; } = string.Empty;

        /// <summary>
        /// Ad name
        /// </summary>
        [JsonPropertyName("ad_name")]
        public string AdName { get; set; } = string.Empty;

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string AdGroupId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad status
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Creative IDs
        /// </summary>
        [JsonPropertyName("creative_ids")]
        public List<string> CreativeIds { get; set; } = new();
    }

    /// <summary>
    /// Webhook event for comment operations
    /// </summary>
    public class CommentWebhookEvent : WebhookEventBase
    {
        /// <summary>
        /// Comment data
        /// </summary>
        [JsonPropertyName("data")]
        public CommentEventData Data { get; set; } = new();
    }

    /// <summary>
    /// Data for comment webhook events
    /// </summary>
    public class CommentEventData
    {
        /// <summary>
        /// Comment ID
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string CommentId { get; set; } = string.Empty;

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Comment text
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; } = string.Empty;

        /// <summary>
        /// User ID who made the comment
        /// </summary>
        [JsonPropertyName("user_id")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// Username who made the comment
        /// </summary>
        [JsonPropertyName("username")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Comment creation time
        /// </summary>
        [JsonPropertyName("create_time")]
        public long CreateTime { get; set; }

        /// <summary>
        /// Parent comment ID (for replies)
        /// </summary>
        [JsonPropertyName("parent_comment_id")]
        public string? ParentCommentId { get; set; }
    }

    /// <summary>
    /// Constants for webhook event types
    /// </summary>
    public static class WebhookEventTypes
    {
        // Account events
        public const string AdvertiserAccountCreation = "ADVERTISER_ACCOUNT_CREATION";
        public const string AdvertiserAccountUpdate = "ADVERTISER_ACCOUNT_UPDATE";
        public const string AdvertiserAccountDeletion = "ADVERTISER_ACCOUNT_DELETION";

        // Campaign events
        public const string CampaignCreation = "CAMPAIGN_CREATION";
        public const string CampaignUpdate = "CAMPAIGN_UPDATE";
        public const string CampaignDeletion = "CAMPAIGN_DELETION";

        // Ad group events
        public const string AdGroupCreation = "ADGROUP_CREATION";
        public const string AdGroupUpdate = "ADGROUP_UPDATE";
        public const string AdGroupDeletion = "ADGROUP_DELETION";

        // Ad events
        public const string AdCreation = "AD_CREATION";
        public const string AdUpdate = "AD_UPDATE";
        public const string AdDeletion = "AD_DELETION";

        // Comment events
        public const string CommentCreation = "COMMENT_CREATION";
        public const string CommentUpdate = "COMMENT_UPDATE";
        public const string CommentDeletion = "COMMENT_DELETION";
    }
}
