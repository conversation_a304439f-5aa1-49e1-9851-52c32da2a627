/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the BC Management API
    /// </summary>
    public class BcManagementApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public BcManagementApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example of getting Business Centers list
        /// </summary>
        public async Task GetBusinessCentersExample()
        {
            try
            {
                Console.WriteLine("Getting Business Centers...");

                // Get all Business Centers for the user
                var allBcResponse = await _client.BcManagement.GetBusinessCentersAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"]);

                if (allBcResponse.Data?.List != null)
                {
                    Console.WriteLine($"Found {allBcResponse.Data.List.Count} Business Centers:");
                    foreach (var bcInfo in allBcResponse.Data.List)
                    {
                        Console.WriteLine($"- BC ID: {bcInfo.BcInfo?.BcId}");
                        Console.WriteLine($"  Name: {bcInfo.BcInfo?.Name}");
                        Console.WriteLine($"  Company: {bcInfo.BcInfo?.Company}");
                        Console.WriteLine($"  Status: {bcInfo.BcInfo?.Status}");
                        Console.WriteLine($"  User Role: {bcInfo.UserRole}");
                        Console.WriteLine();
                    }
                }

                // Get specific Business Center with pagination
                var specificBcRequest = new GetBusinessCentersRequest
                {
                    BcId = "your_bc_id_here",
                    Page = 1,
                    PageSize = 10
                };

                var specificBcResponse = await _client.BcManagement.GetBusinessCentersAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    specificBcRequest);

                Console.WriteLine($"Specific BC Response - Request ID: {specificBcResponse.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code}, Request ID: {ex.RequestId})");
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting Business Center changelog
        /// </summary>
        public async Task GetBusinessCenterChangelogExample()
        {
            try
            {
                Console.WriteLine("Getting Business Center changelog...");

                var changelogRequest = new GetBusinessCenterChangelogRequest
                {
                    BcId = "your_bc_id_here",
                    Filtering = new ChangelogFiltering
                    {
                        StartDate = "2024-01-01",
                        EndDate = "2024-01-31",
                        ActivityType = "ALL"
                    },
                    Lang = "en",
                    SortField = "operation_time",
                    SortType = "DESC",
                    Page = 1,
                    PageSize = 20
                };

                var changelogResponse = await _client.BcManagement.GetBusinessCenterChangelogAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    changelogRequest);

                if (changelogResponse.Data?.ChangelogList != null)
                {
                    Console.WriteLine($"Found {changelogResponse.Data.ChangelogList.Count} changelog entries:");
                    foreach (var entry in changelogResponse.Data.ChangelogList)
                    {
                        Console.WriteLine($"- Time: {entry.Time}");
                        Console.WriteLine($"  Activity Type: {entry.ActivityType}");
                        Console.WriteLine($"  Operator ID: {entry.OperatorId}");
                        Console.WriteLine($"  Activity Log: {entry.ActivityLog}");
                        Console.WriteLine();
                    }

                    if (changelogResponse.Data.PageInfo != null)
                    {
                        Console.WriteLine($"Page Info:");
                        Console.WriteLine($"  Current Page: {changelogResponse.Data.PageInfo.Page}");
                        Console.WriteLine($"  Page Size: {changelogResponse.Data.PageInfo.PageSize}");
                        Console.WriteLine($"  Total Number: {changelogResponse.Data.PageInfo.TotalNumber}");
                        Console.WriteLine($"  Total Pages: {changelogResponse.Data.PageInfo.TotalPage}");
                    }
                }

                Console.WriteLine($"Changelog Response - Request ID: {changelogResponse.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code}, Request ID: {ex.RequestId})");
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting changelog with different activity types
        /// </summary>
        public async Task GetChangelogByActivityTypeExample()
        {
            try
            {
                var activityTypes = new[] { "USER", "ACCOUNT", "ASSET", "BUSINESS" };

                foreach (var activityType in activityTypes)
                {
                    Console.WriteLine($"Getting changelog for activity type: {activityType}");

                    var request = new GetBusinessCenterChangelogRequest
                    {
                        BcId = "your_bc_id_here",
                        Filtering = new ChangelogFiltering
                        {
                            ActivityType = activityType
                        },
                        Page = 1,
                        PageSize = 10
                    };

                    var response = await _client.BcManagement.GetBusinessCenterChangelogAsync(
                        _client.ApiClient.DefaultHeaders["Access-Token"],
                        request);

                    var count = response.Data?.ChangelogList?.Count ?? 0;
                    Console.WriteLine($"Found {count} entries for {activityType} activities");
                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Run all examples
        /// </summary>
        public async Task RunAllExamples()
        {
            await GetBusinessCentersExample();
            Console.WriteLine(new string('-', 50));
            
            await GetBusinessCenterChangelogExample();
            Console.WriteLine(new string('-', 50));
            
            await GetChangelogByActivityTypeExample();
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _client?.Dispose();
        }
    }

    /// <summary>
    /// Program entry point for running BC Management API examples
    /// </summary>
    public class BcManagementProgram
    {
        public static async Task Main(string[] args)
        {
            if (args.Length == 0)
            {
                Console.WriteLine("Usage: BcManagementProgram <access_token>");
                return;
            }

            var accessToken = args[0];
            var example = new BcManagementApiExample(accessToken);

            try
            {
                await example.RunAllExamples();
            }
            finally
            {
                example.Dispose();
            }
        }
    }
}
