/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using TikTokBusinessApi.Core.Resilience;

namespace TikTokBusinessApi.Exceptions
{
    /// <summary>
    /// Exception thrown when a circuit breaker is open and blocks requests
    /// </summary>
    public class CircuitBreakerOpenException : Exception
    {
        /// <summary>
        /// Circuit breaker metrics at the time of the exception
        /// </summary>
        public CircuitBreakerMetrics? Metrics { get; set; }

        /// <summary>
        /// Time when the circuit breaker will attempt to transition to half-open
        /// </summary>
        public DateTimeOffset? RetryAfter { get; set; }

        /// <summary>
        /// Initializes a new instance of the CircuitBreakerOpenException class
        /// </summary>
        public CircuitBreakerOpenException() : base("Circuit breaker is open")
        {
        }

        /// <summary>
        /// Initializes a new instance of the CircuitBreakerOpenException class with a message
        /// </summary>
        /// <param name="message">Exception message</param>
        public CircuitBreakerOpenException(string message) : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the CircuitBreakerOpenException class with a message and inner exception
        /// </summary>
        /// <param name="message">Exception message</param>
        /// <param name="innerException">Inner exception</param>
        public CircuitBreakerOpenException(string message, Exception innerException) : base(message, innerException)
        {
        }

        /// <summary>
        /// Initializes a new instance of the CircuitBreakerOpenException class with full details
        /// </summary>
        /// <param name="message">Exception message</param>
        /// <param name="metrics">Circuit breaker metrics</param>
        /// <param name="retryAfter">Time when retry can be attempted</param>
        public CircuitBreakerOpenException(string message, CircuitBreakerMetrics? metrics, DateTimeOffset? retryAfter) 
            : base(message)
        {
            Metrics = metrics;
            RetryAfter = retryAfter;
        }
    }
}
