# DimTransactionTypeEntity

## Thông tin bảng dữ liệu

-   **Table**: `DimTransactionTypes`
-   **TablePrefix**: `Dim_`
-   **TableName**: `Dim_DimTransactionTypes`
-   **DbSchema**: `TikTok`

## Mô tả

Dimension table tĩnh lưu trữ phân loại giao dịch tài chính. **Đơn giản hóa chỉ những trường cần thiết**.

## Cấu trúc bảng

| Tên Field           | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                               |
| ------------------- | ------------ | -------- | ------ | ----------------------------------- |
| Id                  | Guid         | ✓        | -      | Kh<PERSON><PERSON> chính surrogate key (Identity) |
| TransactionTypeCode | string       | ✓        | 50     | Mã loại giao dịch (Business Key)    |
| TransactionTypeName | string       | ✓        | 100    | Tên loại giao dịch                  |
| FundsTypeCode       | string       | ✓        | 50     | Mã loại quỹ                         |
| FundsTypeName       | string       | ✓        | 100    | Tên loại quỹ                        |
| IsDebit             | bool         | ✓        | -      | Giao dịch nợ (tiền ra)              |
| IsCredit            | bool         | ✓        | -      | Giao dịch có (tiền vào)             |
| IsActive            | bool         | ✓        | -      | Đang hoạt động                      |
| CreationTime        | DateTime     | ✓        | -      | Thời gian tạo bản ghi               |

## Pre-defined Values

### Transaction Types

| Code          | Name               | FundsType | IsDebit | IsCredit |
| ------------- | ------------------ | --------- | ------- | -------- |
| RECHARGE      | Nạp tiền           | CASH      | 0       | 1        |
| SPEND         | Chi tiêu quảng cáo | CASH      | 1       | 0        |
| TRANSFER_IN   | Chuyển tiền vào    | CASH      | 0       | 1        |
| TRANSFER_OUT  | Chuyển tiền ra     | CASH      | 1       | 0        |
| REFUND        | Hoàn tiền          | CASH      | 0       | 1        |
| CREDIT_SPEND  | Chi tiêu tín dụng  | CREDIT    | 1       | 0        |
| VOUCHER_SPEND | Chi tiêu voucher   | VOUCHER   | 1       | 0        |

### Funds Types

-   **CASH**: Tiền mặt
-   **CREDIT**: Tín dụng quảng cáo
-   **VOUCHER**: Phiếu giảm giá

## Grain Definition

-   **Mỗi dòng đại diện cho**: 1 loại giao dịch duy nhất
-   **Unique Key**: Id
-   **Business Key**: TransactionTypeCode

## Business Questions

```sql
-- Phân loại giao dịch
SELECT FundsTypeName, COUNT(*) as TypeCount FROM DimTransactionTypes WHERE IsActive = 1 GROUP BY FundsTypeName

-- Giao dịch thu/chi
SELECT CASE WHEN IsDebit = 1 THEN 'Outflow' ELSE 'Inflow' END as Direction, COUNT(*)
FROM DimTransactionTypes WHERE IsActive = 1 GROUP BY IsDebit
```
