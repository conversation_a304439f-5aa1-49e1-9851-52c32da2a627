using System;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu báo cáo tích hợp AdGroup
    /// </summary>
    public interface IReportIntegratedAdGroupSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdGroup cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<ReportIntegratedAdGroupSyncResult> SyncAllReportIntegratedAdGroupForAllBcsAsync();

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdGroup theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu (tù<PERSON> chọn)</param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kế<PERSON> thú<PERSON> (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<ReportIntegratedAdGroupSyncResult> SyncReportIntegratedAdGroupAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null);
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu báo cáo tích hợp AdGroup
    /// </summary>
    public class ReportIntegratedAdGroupSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số AdGroup đã đồng bộ
        /// </summary>
        public int AdGroupCount { get; set; }

        /// <summary>
        /// Số ngày đã đồng bộ
        /// </summary>
        public int DayCount { get; set; }
    }
}