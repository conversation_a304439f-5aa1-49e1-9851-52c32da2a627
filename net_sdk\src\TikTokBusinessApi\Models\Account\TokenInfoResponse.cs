/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for token information
    /// </summary>
    public class TokenInfoResponse
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// Scope of permissions for the specified access token
        /// </summary>
        [JsonPropertyName("scope")]
        public string Scope { get; set; } = string.Empty;

        /// <summary>
        /// Application specific unique ID of the TikTok Account
        /// </summary>
        [JsonPropertyName("creator_id")]
        public string CreatorId { get; set; } = string.Empty;
    }
}
