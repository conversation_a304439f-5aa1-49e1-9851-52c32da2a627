using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu báo cáo GMV Max Live Detail Livestream
    /// </summary>
    public class GmvMaxLiveLivestreamSyncApplicationAppService : ApplicationService, IGmvMaxLiveLivestreamSyncApplicationAppService
    {
        private readonly IGmvMaxLiveLivestreamSyncService _gmvMaxLiveLivestreamSyncService;

        public GmvMaxLiveLivestreamSyncApplicationAppService(
            IGmvMaxLiveLivestreamSyncService gmvMaxLiveLivestreamSyncService)
        {
            _gmvMaxLiveLivestreamSyncService = gmvMaxLiveLivestreamSyncService;
        }

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Detail Livestream theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu (tùy chọn)</param>
        /// <param name="endDate">Ngày kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxLiveLivestreamSyncResult> SyncGmvMaxLiveLivestreamAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _gmvMaxLiveLivestreamSyncService.SyncGmvMaxLiveLivestreamAsync(bcId, startDate, endDate);
        }

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Detail Livestream cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxLiveLivestreamSyncResult> SyncAllGmvMaxLiveLivestreamForAllBcsAsync()
        {
            return await _gmvMaxLiveLivestreamSyncService.SyncAllGmvMaxLiveLivestreamForAllBcsAsync();
        }
    }
}
