/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Negative Keywords operations
    /// </summary>
    public interface INegativeKeywordsApi
    {
        /// <summary>
        /// Get the list of negative keywords for Search Ads
        /// </summary>
        /// <param name="request">Request parameters for getting negative keywords</param>
        /// <returns>Response containing the list of negative keywords</returns>
        Task<GetNegativeKeywordsResponse> GetNegativeKeywordsAsync(GetNegativeKeywordsRequest request);

        // /// <summary>
        // /// Create negative keywords for Search Ads
        // /// </summary>
        // /// <param name="request">Request parameters for creating negative keywords</param>
        // /// <returns>Response for the create operation</returns>
        // Task<CreateNegativeKeywordsResponse> CreateNegativeKeywordsAsync(CreateNegativeKeywordsRequest request);

        // /// <summary>
        // /// Update a negative keyword for Search Ads
        // /// </summary>
        // /// <param name="request">Request parameters for updating a negative keyword</param>
        // /// <returns>Response containing the updated keyword information</returns>
        // Task<UpdateNegativeKeywordResponse> UpdateNegativeKeywordAsync(UpdateNegativeKeywordRequest request);

        // /// <summary>
        // /// Delete negative keywords for Search Ads
        // /// </summary>
        // /// <param name="request">Request parameters for deleting negative keywords</param>
        // /// <returns>Response for the delete operation</returns>
        // Task<DeleteNegativeKeywordsResponse> DeleteNegativeKeywordsAsync(DeleteNegativeKeywordsRequest request);

        /// <summary>
        /// Download the list of negative keywords for Search Ads as an Excel file
        /// </summary>
        /// <param name="request">Request parameters for downloading negative keywords</param>
        /// <returns>Excel file stream containing the negative keywords</returns>
        Task<byte[]> DownloadNegativeKeywordsAsync(DownloadNegativeKeywordsRequest request);
    }
}
