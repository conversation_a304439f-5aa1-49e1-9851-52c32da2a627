using System;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.BusinessApplications
{
    /// <summary>
    /// DTO để lấy danh sách ứng dụng Business
    /// </summary>
    public class GetBusinessApplicationListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Từ khóa tìm kiếm
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// Application ID
        /// </summary>
        public string? ApplicationId { get; set; }

        /// <summary>
        /// BC ID
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// BC Name
        /// </summary>
        public string? BcName { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string? Comment { get; set; }

        /// <summary>
        /// Trạng thái hoạt động
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Trạng thái xử lý
        /// </summary>
        public BusinessApplicationStatus? Status { get; set; }

        /// <summary>
        /// Thời gian tạo token từ
        /// </summary>
        public DateTime? AccessTokenCreatedAtFrom { get; set; }

        /// <summary>
        /// Thời gian tạo token đến
        /// </summary>
        public DateTime? AccessTokenCreatedAtTo { get; set; }
    }
} 