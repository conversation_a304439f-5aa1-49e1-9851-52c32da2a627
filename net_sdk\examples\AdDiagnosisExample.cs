/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the AdDiagnosis API
    /// </summary>
    public class AdDiagnosisExample
    {
        /// <summary>
        /// Example of getting ad group diagnoses
        /// </summary>
        /// <param name="accessToken">Your TikTok Business API access token</param>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="adgroupIds">List of ad group IDs to diagnose</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task GetAdGroupDiagnosisExample(string accessToken, string advertiserId, List<string> adgroupIds)
        {
            try
            {
                // Create the TikTok Business API client
                using var client = new TikTokBusinessApiClient(accessToken);

                // Create filtering parameters
                var filtering = new AdDiagnosisFiltering
                {
                    AdgroupIds = adgroupIds,
                    IssueCategory = new List<string> { "CREATIVE", "BID_AND_BUDGET", "EVENT_TRACK" }
                };

                // Get diagnoses for the specified ad groups
                var response = await client.AdDiagnosis.GetDiagnosisAsync(
                    accessToken,
                    advertiserId,
                    filtering);

                Console.WriteLine($"Request ID: {response.RequestId}");

                if (response.Data?.Results != null)
                {
                    Console.WriteLine($"Found {response.Data.Results.Count} ad groups with diagnoses:");

                    foreach (var result in response.Data.Results)
                    {
                        Console.WriteLine($"\nAd Group: {result.AdgroupName} (ID: {result.AdgroupId})");
                        Console.WriteLine($"Diagnosis Time: {result.Diagnosis?.DiagnosisTime}");

                        if (result.Diagnosis?.Suggestions != null)
                        {
                            foreach (var suggestion in result.Diagnosis.Suggestions)
                            {
                                // Display creative suggestions
                                if (suggestion.Creative != null && suggestion.Creative.Count > 0)
                                {
                                    Console.WriteLine("  Creative Issues:");
                                    foreach (var creative in suggestion.Creative)
                                    {
                                        Console.WriteLine($"    - Ad: {creative.Name} (ID: {creative.AdId})");
                                        Console.WriteLine($"      Issue: {creative.IssueSuggestion}");
                                        Console.WriteLine($"      Video ID: {creative.Vid}");
                                        Console.WriteLine($"      Suggestion Time: {creative.SuggestionTime}");
                                    }
                                }

                                // Display bid and budget suggestions
                                if (suggestion.BidAndBudget != null && suggestion.BidAndBudget.Count > 0)
                                {
                                    Console.WriteLine("  Bid & Budget Issues:");
                                    foreach (var bidBudget in suggestion.BidAndBudget)
                                    {
                                        Console.WriteLine($"    - Issue: {bidBudget.IssueSuggestion}");
                                        Console.WriteLine($"      Current Bid: {bidBudget.Bid}");
                                        Console.WriteLine($"      Suggested Bid: {bidBudget.SuggestBid}");
                                        Console.WriteLine($"      Current Budget: {bidBudget.Budget}");
                                        Console.WriteLine($"      Suggested Budget: {bidBudget.SuggestBudget}");
                                        Console.WriteLine($"      Suggestion Time: {bidBudget.SuggestionTime}");

                                        // Display EDR information if available
                                        if (bidBudget.BidEdrInfo != null && bidBudget.BidEdrInfo.Count > 0)
                                        {
                                            Console.WriteLine("      Bid EDR Recommendations:");
                                            foreach (var edr in bidBudget.BidEdrInfo)
                                            {
                                                Console.WriteLine($"        Recommended Bid: {edr.RecommendedBid}");
                                                Console.WriteLine($"        Bid Increase Ratio: {edr.BidIncreaseRatio}%");
                                                Console.WriteLine($"        Estimated Cost: {edr.EstimatedCost}");
                                            }
                                        }

                                        if (bidBudget.BudgetEdrInfo != null && bidBudget.BudgetEdrInfo.Count > 0)
                                        {
                                            Console.WriteLine("      Budget EDR Recommendations:");
                                            foreach (var edr in bidBudget.BudgetEdrInfo)
                                            {
                                                Console.WriteLine($"        Recommended Budget: {edr.RecommendedBudget}");
                                                Console.WriteLine($"        Budget Increase Ratio: {edr.BudgetIncreaseRatio}%");
                                                Console.WriteLine($"        Estimated Conversion: {edr.EstimatedConversion}");
                                                Console.WriteLine($"        Estimated CPA: {edr.Cpa}");
                                            }
                                        }
                                    }
                                }

                                // Display event tracking suggestions
                                if (suggestion.EventTrack != null && suggestion.EventTrack.Count > 0)
                                {
                                    Console.WriteLine("  Event Tracking Issues:");
                                    foreach (var eventTrack in suggestion.EventTrack)
                                    {
                                        Console.WriteLine($"    - Issue: {eventTrack.IssueSuggestion}");
                                        Console.WriteLine($"      Pixel ID: {eventTrack.PixelId}");
                                        Console.WriteLine($"      Pixel Code: {eventTrack.PixelCode}");
                                        Console.WriteLine($"      Suggestion Time: {eventTrack.SuggestionTime}");
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    Console.WriteLine("No ad groups with diagnoses found.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting ad diagnosis: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }
        }

        /// <summary>
        /// Example of getting diagnoses for specific issue categories
        /// </summary>
        /// <param name="accessToken">Your TikTok Business API access token</param>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="adgroupIds">List of ad group IDs to diagnose</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task GetCreativeIssuesOnlyExample(string accessToken, string advertiserId, List<string> adgroupIds)
        {
            try
            {
                using var client = new TikTokBusinessApiClient(accessToken);

                // Filter for creative issues only
                var filtering = new AdDiagnosisFiltering
                {
                    AdgroupIds = adgroupIds,
                    IssueCategory = new List<string> { "CREATIVE" }
                };

                var response = await client.AdDiagnosis.GetDiagnosisAsync(
                    accessToken,
                    advertiserId,
                    filtering);

                Console.WriteLine("Creative Issues Only:");
                Console.WriteLine($"Request ID: {response.RequestId}");

                if (response.Data?.Results != null)
                {
                    foreach (var result in response.Data.Results)
                    {
                        Console.WriteLine($"Ad Group: {result.AdgroupName}");
                        
                        if (result.Diagnosis?.Suggestions != null)
                        {
                            foreach (var suggestion in result.Diagnosis.Suggestions)
                            {
                                if (suggestion.Creative != null)
                                {
                                    foreach (var creative in suggestion.Creative)
                                    {
                                        Console.WriteLine($"  - {creative.IssueSuggestion}: {creative.Name}");
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Main method to run the examples
        /// </summary>
        /// <param name="args">Command line arguments</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task Main(string[] args)
        {
            // Replace these with your actual values
            const string accessToken = "YOUR_ACCESS_TOKEN";
            const string advertiserId = "YOUR_ADVERTISER_ID";
            var adgroupIds = new List<string> { "YOUR_ADGROUP_ID_1", "YOUR_ADGROUP_ID_2" };

            Console.WriteLine("TikTok Business API - Ad Diagnosis Examples");
            Console.WriteLine("==========================================");

            // Example 1: Get all types of diagnoses
            Console.WriteLine("\n1. Getting all diagnoses for ad groups...");
            await GetAdGroupDiagnosisExample(accessToken, advertiserId, adgroupIds);

            // Example 2: Get only creative issues
            Console.WriteLine("\n2. Getting creative issues only...");
            await GetCreativeIssuesOnlyExample(accessToken, advertiserId, adgroupIds);

            Console.WriteLine("\nExamples completed.");
        }
    }
}
