using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service interface cho việc đồng bộ dữ liệu chiến dịch (Campaigns)
    /// </summary>
    public interface ICampaignSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ chiến dịch theo Advertiser ID và BC ID với filtering
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="objectiveType">Loại mục tiêu</param>
        /// <param name="operationStatus">Trạng thái hoạt động</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<CampaignSyncResult> SyncCampaignsAsync(string advertiserId, string bcId, string? objectiveType = null, string? operationStatus = null);

        /// <summary>
        /// Đồng bộ nhiều chiến dịch cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds">Danh sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<CampaignSyncResult> SyncManyCampaignsAsync(string bcId, List<string>? advertiserIds = null);

        /// <summary>
        /// Đồng bộ tất cả chiến dịch cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<CampaignSyncResult> SyncAllCampaignsAsync();
    }
}