using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.BalanceBusinessCenters
{
    /// <summary>
    /// DTO để lấy danh sách Business Center với thông tin số dư mới nhất
    /// </summary>
    public class GetLatestBalanceBusinessCenterListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Từ khóa tìm kiếm
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// ID của Business Center
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// Tên của Business Center
        /// </summary>
        public string? BcName { get; set; }

        /// <summary>
        /// Tiền tệ
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Số dư tài khoản từ
        /// </summary>
        public decimal? MinAccountBalance { get; set; }

        /// <summary>
        /// Số dư tài khoản đến
        /// </summary>
        public decimal? MaxAccountBalance { get; set; }

        /// <summary>
        /// Số dư tiền mặt từ
        /// </summary>
        public decimal? MinCashBalance { get; set; }

        /// <summary>
        /// Số dư tiền mặt đến
        /// </summary>
        public decimal? MaxCashBalance { get; set; }



        /// <summary>
        /// Ngày đồng bộ từ
        /// </summary>
        public DateTime? DateFrom { get; set; }

        /// <summary>
        /// Ngày đồng bộ đến
        /// </summary>
        public DateTime? DateTo { get; set; }

        /// <summary>
        /// Múi giờ
        /// </summary>
        public string? Timezone { get; set; }
    }
}
