# GMV Max DataWarehouse Design

## Tổng quan

DataWarehouse cho GMV Max được thiết kế để phục vụ dashboard với các yêu cầu cụ thể về hiệu suất quảng cáo, ROI, ROAS, ACOS và TACOS. Hệ thống hỗ trợ phân tích từ cấp độ Campaign xuống Product level.

## Kiến trúc Star Schema

```
                    Dim_Date ────────────────────────── Dim_Store
                        │                                       │
                        │                                       │
    Dim_BusinessCenter──┼──────── Fact_GmvMaxCampaign           │
             │          │              │                       │
             │          │              │                       │
             │          └─────── Fact_GmvMaxProduct            │
             │                         │                       │
             └─── Dim_AdAccount ───────┼──────── Dim_Product───┘
                      │                │              │
                      │                │              │
                      └─── Dim_Campaign─┼──────────────┘
                               │        │
                               │        │
                               └────────┘
```

## Cấu trúc bảng dữ liệu

### Entity Inheritance

Tất cả các entity DataWarehouse đều kế thừa từ `AuditedEntity` để có tính năng audit tự động:

-   **Fact Tables**: <PERSON><PERSON> thừa từ `AuditedEntity<Guid>` (Primary Key kiểu Guid)
-   **Dimension Tables**: <PERSON>ế thừa từ `AuditedEntity<int>` (Primary Key kiểu int)

Các thuộc tính audit tự động bao gồm:

-   `CreationTime`: Thời gian tạo bản ghi
-   `CreatorId`: ID người tạo
-   `LastModificationTime`: Thời gian sửa đổi cuối
-   `LastModifierId`: ID người sửa đổi cuối

### Fact Tables

| Fact Table              | Grain           | Mô tả                                          |
| ----------------------- | --------------- | ---------------------------------------------- |
| **Fact_GmvMaxCampaign** | 1 Campaign/Ngày | Tổng hợp hiệu suất chiến dịch GMV Max          |
| **Fact_GmvMaxProduct**  | 1 Product/Ngày  | Chi tiết hiệu suất từng sản phẩm trong GMV Max |

### Dimension Tables

| Dimension              | Grain                | SCD Type | Mô tả                         |
| ---------------------- | -------------------- | -------- | ----------------------------- |
| **Dim_Date**           | 1 Ngày               | Static   | Tiêu chí thời gian            |
| **Dim_BusinessCenter** | 1 BC/Version         | Type 2   | Tiêu chí tổ chức              |
| **Dim_AdAccount**      | 1 Ad Account/Version | Type 2   | Tiêu chí tài khoản quảng cáo  |
| **Dim_Campaign**       | 1 Campaign/Version   | Type 2   | Tiêu chí chiến dịch           |
| **Dim_Store**          | 1 Store/Version      | Type 2   | Tiêu chí TikTok Shop          |
| **Dim_Product**        | 1 Product/Version    | Type 2   | Tiêu chí sản phẩm TikTok Shop |

## Metrics chính cho Dashboard

### 1. Dashboard Overview

-   **Total Revenue**: Tổng doanh thu từ GMV Max
-   **Average ROI**: ROI trung bình
-   **No. Campaign**: Số lượng chiến dịch
-   **No. Orders**: Tổng số đơn hàng
-   **Spent Ads Estimate**: Chi phí quảng cáo ước tính
-   **Total Ads Spent**: Tổng chi phí quảng cáo thực tế

### 2. Report Charts (7 ngày gần nhất)

-   **Cost vs last 7 days**: So sánh chi phí theo ngày
-   **Orders (SKU) vs last 7 days**: So sánh đơn hàng theo ngày
-   **Cost per order vs last 7 days**: So sánh CPO theo ngày
-   **Gross revenue vs last 7 days**: So sánh doanh thu theo ngày
-   **ROI vs last 7 days**: So sánh ROI theo ngày
-   **P-Impression**: Lượt hiển thị
-   **P-Clicks**: Lượt nhấp

### 3. Overview Analytics

-   **SHOP**: Tên cửa hàng TikTok Shop
-   **CAMPAIGN**: Tên chiến dịch
-   **BID TYPE**: Loại đấu giá
-   **SCHEDULE TIME**: Thời gian lịch trình
-   **STATUS**: Trạng thái chiến dịch
-   **ORDERS**: Số đơn hàng
-   **TOTAL ADS SPENT**: Tổng chi phí quảng cáo
-   **REVENUE**: Doanh thu
-   **ADS REVENUE**: Doanh thu từ quảng cáo
-   **ROI**: Tỷ suất lợi nhuận
-   **CPO**: Chi phí mỗi đơn hàng

### 4. Advanced Filter

Cho phép lọc theo:

-   **Orders**: Số lượng đơn hàng
-   **ROI**: Tỷ suất lợi nhuận
-   **Cost**: Chi phí quảng cáo
-   **Gross Revenue**: Tổng doanh thu

Với các operators: `=`, `≥`, `≤`, `Between`

### 5. Views theo vai trò

#### View Nhà bán hàng

-   Tập trung vào **ROAS (Return On Ad Spend)**
-   Hiển thị: ROAS, Orders, GrossRevenue, Cost

#### View Quản lý Ads

-   Hiển thị thêm **ACOS** và **True ACOS (TACOS)**
-   **ACOS** = `Ad Spend / Ad Sales`
-   **True ACOS (TACOS)** = `Ad Spend / (Ad Sales + Organic Sales)`

## Công thức tính toán

### ROI (Return on Investment)

```
ROI = (GrossRevenue - Cost) / Cost
```

### ROAS (Return on Ad Spend)

```
ROAS = GrossRevenue / Cost
```

### ACOS (Advertising Cost of Sales)

```
ACOS = Cost / AdsRevenue
```

### TACOS (True ACOS)

```
TACOS = Cost / (AdsRevenue + OrganicRevenue)
```

### CPO (Cost Per Order)

```
CPO = Cost / Orders
```

### CTR (Click Through Rate)

```
CTR = Clicks / Impressions
```

### CPM (Cost Per Mille)

```
CPM = (Cost / Impressions) * 1000
```

### CPC (Cost Per Click)

```
CPC = Cost / Clicks
```

## ETL Process

### Source Tables

-   **RawGmvMaxCampaignsEntity**: Thông tin chiến dịch GMV Max
-   **RawGmvMaxProductCampaignReportEntity**: Báo cáo cấp chiến dịch
-   **RawGmvMaxProductDetailProductReportEntity**: Báo cáo chi tiết sản phẩm
-   **RawGmvMaxProductCreativeReportEntity**: Báo cáo creative
-   **RawGmvMaxLiveCampaignReportEntity**: Báo cáo live stream
-   **RawGmvMaxLiveDetailLivestreamReportEntity**: Báo cáo chi tiết live stream

### ETL Rules

#### Fact_GmvMaxCampaign

```sql
-- Từ RawGmvMaxProductCampaignReportEntity
INSERT INTO Fact_GmvMaxCampaign (
    DateKey, BusinessCenterKey, AdAccountKey, CampaignKey, StoreKey,
    CampaignId, StoreId, ShoppingAdsType, OperationStatus, BidType,
    RoasBid, Budget, Cost, NetCost, Orders, CostPerOrder,
    GrossRevenue, AdsRevenue, OrganicRevenue, ROI, ROAS, ACOS, TACOS,
    Impressions, Clicks, CTR, CPM, CPC, Currency
)
SELECT
    d.DateKey,
    bc.BusinessCenterKey,
    aa.AdAccountKey,
    c.CampaignKey,
    s.StoreKey,
    r.CampaignId,
    r.StoreId,
    'PRODUCT' as ShoppingAdsType,
    r.OperationStatus,
    r.BidType,
    r.RoasBid,
    r.Budget,
    r.Cost,
    r.NetCost,
    r.Orders,
    r.CostPerOrder,
    r.GrossRevenue,
    r.GrossRevenue as AdsRevenue, -- Giả định toàn bộ doanh thu từ ads
    NULL as OrganicRevenue,
    r.ROI,
    r.GrossRevenue / NULLIF(r.Cost, 0) as ROAS,
    r.Cost / NULLIF(r.GrossRevenue, 0) as ACOS,
    r.Cost / NULLIF(r.GrossRevenue, 0) as TACOS,
    r.Impressions,
    r.Clicks,
    r.Clicks / NULLIF(r.Impressions, 0) as CTR,
    (r.Cost / NULLIF(r.Impressions, 0)) * 1000 as CPM,
    r.Cost / NULLIF(r.Clicks, 0) as CPC,
    r.Currency
FROM RawGmvMaxProductCampaignReportEntity r
JOIN Dim_Date d ON CAST(r.Date AS DATE) = d.Date
JOIN Dim_BusinessCenter bc ON r.BcId = bc.BcId AND bc.IsCurrent = 1
JOIN Dim_AdAccount aa ON r.AdvertiserId = aa.AdvertiserId AND aa.IsCurrent = 1
JOIN Dim_Campaign c ON r.CampaignId = c.CampaignId AND c.IsCurrent = 1
JOIN Dim_Store s ON r.StoreId = s.StoreId AND s.IsCurrent = 1
```

#### Fact_GmvMaxProduct

```sql
-- Từ RawGmvMaxProductDetailProductReportEntity
INSERT INTO Fact_GmvMaxProduct (
    DateKey, BusinessCenterKey, AdAccountKey, CampaignKey, StoreKey, ProductKey,
    CampaignId, StoreId, ProductId, ProductName, SKU, Category,
    ProductPrice, Cost, NetCost, Orders, QuantitySold, CostPerOrder,
    GrossRevenue, AdsRevenue, OrganicRevenue, ROI, ROAS, ACOS, TACOS,
    Impressions, Clicks, CTR, CPM, CPC, ConversionRate, Currency
)
SELECT
    d.DateKey,
    bc.BusinessCenterKey,
    aa.AdAccountKey,
    c.CampaignKey,
    s.StoreKey,
    p.ProductKey,
    r.CampaignId,
    r.StoreId,
    r.ProductId,
    p.ProductName,
    p.SKU,
    p.Category,
    p.CurrentPrice,
    r.Cost,
    r.NetCost,
    r.Orders,
    r.QuantitySold,
    r.CostPerOrder,
    r.GrossRevenue,
    r.GrossRevenue as AdsRevenue,
    NULL as OrganicRevenue,
    r.ROI,
    r.GrossRevenue / NULLIF(r.Cost, 0) as ROAS,
    r.Cost / NULLIF(r.GrossRevenue, 0) as ACOS,
    r.Cost / NULLIF(r.GrossRevenue, 0) as TACOS,
    r.Impressions,
    r.Clicks,
    r.Clicks / NULLIF(r.Impressions, 0) as CTR,
    (r.Cost / NULLIF(r.Impressions, 0)) * 1000 as CPM,
    r.Cost / NULLIF(r.Clicks, 0) as CPC,
    r.Orders / NULLIF(r.Clicks, 0) as ConversionRate,
    r.Currency
FROM RawGmvMaxProductDetailProductReportEntity r
JOIN Dim_Date d ON CAST(r.Date AS DATE) = d.Date
JOIN Dim_BusinessCenter bc ON r.BcId = bc.BcId AND bc.IsCurrent = 1
JOIN Dim_AdAccount aa ON r.AdvertiserId = aa.AdvertiserId AND aa.IsCurrent = 1
JOIN Dim_Campaign c ON r.CampaignId = c.CampaignId AND c.IsCurrent = 1
JOIN Dim_Store s ON r.StoreId = s.StoreId AND s.IsCurrent = 1
JOIN Dim_Product p ON r.ProductId = p.ProductId AND p.IsCurrent = 1
```

## Use Cases thực tế

### Dashboard Overview Query

```sql
-- Tổng quan hiệu suất GMV Max
SELECT
    SUM(GrossRevenue) as TotalRevenue,
    AVG(ROI) as AverageROI,
    COUNT(DISTINCT CampaignId) as CampaignCount,
    SUM(Orders) as TotalOrders,
    SUM(Cost) as TotalCost,
    SUM(NetCost) as TotalNetCost
FROM Fact_GmvMaxCampaign
WHERE DateKey >= @Last7Days
```

### Campaign Performance Analysis

```sql
-- Phân tích hiệu suất chiến dịch
SELECT
    c.CampaignName,
    s.StoreName,
    f.Orders,
    f.Cost,
    f.GrossRevenue,
    f.ROI,
    f.ROAS,
    f.ACOS,
    f.TACOS,
    f.BidType,
    f.OperationStatus
FROM Fact_GmvMaxCampaign f
JOIN Dim_Campaign c ON f.CampaignKey = c.CampaignKey
JOIN Dim_Store s ON f.StoreKey = s.StoreKey
WHERE f.DateKey >= @StartDate AND f.DateKey <= @EndDate
ORDER BY f.ROI DESC
```

### Product Performance Drill-down

```sql
-- Drill-down theo sản phẩm
SELECT
    p.ProductName,
    p.Category,
    p.Brand,
    p.CurrentPrice,
    SUM(f.GrossRevenue) as TotalRevenue,
    AVG(f.ROI) as AverageROI,
    SUM(f.Orders) as TotalOrders,
    SUM(f.QuantitySold) as TotalQuantitySold,
    SUM(f.Cost) as TotalCost
FROM Fact_GmvMaxProduct f
JOIN Dim_Product p ON f.ProductKey = p.ProductKey
WHERE f.DateKey >= @StartDate AND f.DateKey <= @EndDate
GROUP BY p.ProductName, p.Category, p.Brand, p.CurrentPrice
ORDER BY TotalRevenue DESC
```

### Advanced Filtering

```sql
-- Lọc theo ROI và Orders
SELECT *
FROM Fact_GmvMaxCampaign
WHERE ROI >= @MinROI
  AND ROI <= @MaxROI
  AND Orders >= @MinOrders
  AND Orders <= @MaxOrders
  AND DateKey >= @StartDate
  AND DateKey <= @EndDate
```

### Time Series Analysis

```sql
-- Phân tích xu hướng theo thời gian
SELECT
    d.Date,
    d.DayOfWeek,
    SUM(f.Cost) as DailyCost,
    SUM(f.Orders) as DailyOrders,
    AVG(f.ROI) as AverageROI,
    SUM(f.GrossRevenue) as DailyRevenue
FROM Fact_GmvMaxCampaign f
JOIN Dim_Date d ON f.DateKey = d.DateKey
WHERE d.Date >= @Last7Days
GROUP BY d.Date, d.DayOfWeek
ORDER BY d.Date
```

## Entity Framework Integration

### AuditedEntity Benefits

Việc sử dụng `AuditedEntity` mang lại các lợi ích sau:

1. **Tự động Audit Trail**: Tất cả các thay đổi được ghi lại tự động
2. **Consistency**: Đảm bảo tính nhất quán trong việc theo dõi thay đổi
3. **ABP Framework Integration**: Tích hợp tốt với ABP framework
4. **Code Reduction**: Giảm code trùng lặp cho audit properties
5. **Automatic Population**: Các thuộc tính audit được tự động điền bởi framework

### Entity Configuration

```csharp
// Fact Tables
public class FactGmvMaxCampaignEntity : AuditedEntity<Guid>
public class FactGmvMaxProductEntity : AuditedEntity<Guid>

// Dimension Tables
public class DimStoreEntity : AuditedEntity<int>
public class DimProductEntity : AuditedEntity<int>
```

## Performance Optimization

### Indexing Strategy

```sql
-- Index cho Fact_GmvMaxCampaign
CREATE INDEX IX_Fact_GmvMaxCampaign_DateKey ON Fact_GmvMaxCampaign(DateKey);
CREATE INDEX IX_Fact_GmvMaxCampaign_CampaignKey ON Fact_GmvMaxCampaign(CampaignKey);
CREATE INDEX IX_Fact_GmvMaxCampaign_StoreKey ON Fact_GmvMaxCampaign(StoreKey);
CREATE INDEX IX_Fact_GmvMaxCampaign_Composite ON Fact_GmvMaxCampaign(DateKey, CampaignKey, StoreKey);

-- Index cho Fact_GmvMaxProduct
CREATE INDEX IX_Fact_GmvMaxProduct_DateKey ON Fact_GmvMaxProduct(DateKey);
CREATE INDEX IX_Fact_GmvMaxProduct_CampaignKey ON Fact_GmvMaxProduct(CampaignKey);
CREATE INDEX IX_Fact_GmvMaxProduct_ProductKey ON Fact_GmvMaxProduct(ProductKey);
CREATE INDEX IX_Fact_GmvMaxProduct_Composite ON Fact_GmvMaxProduct(DateKey, CampaignKey, ProductKey);
```

### Partitioning Strategy

-   **Monthly Partitioning**: Theo DateKey
-   **Archive Strategy**: Dữ liệu cũ hơn 12 tháng được archive

## Monitoring và Maintenance

### Data Quality Checks

-   Kiểm tra tính toàn vẹn dữ liệu
-   Validation các metrics tính toán
-   Monitoring ETL process

### Performance Monitoring

-   Query performance tracking
-   Index usage monitoring
-   Storage optimization

## Kết luận

DataWarehouse GMV Max được thiết kế để hỗ trợ đầy đủ các yêu cầu dashboard với khả năng phân tích từ cấp độ tổng quan xuống chi tiết sản phẩm. Hệ thống hỗ trợ các metrics quan trọng như ROI, ROAS, ACOS, TACOS và các tính năng lọc nâng cao theo yêu cầu.
