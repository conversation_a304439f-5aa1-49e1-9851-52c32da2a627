/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response for Smart+ Campaign list
    /// </summary>
    public class SmartPlusListResponse
    {
        /// <summary>
        /// A list of Smart+ Campaigns or Smart Performance Web Campaigns
        /// </summary>
        [JsonPropertyName("list")]
        public List<SmartPlusResponse>? List { get; set; }
    }

    /// <summary>
    /// Response for Smart+ Campaign report
    /// </summary>
    public class SmartPlusReportResponse
    {
        /// <summary>
        /// Data list
        /// </summary>
        [JsonPropertyName("list")]
        public List<SmartPlusReportItem>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public SmartPlusPageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Smart+ Campaign report item
    /// </summary>
    public class SmartPlusReportItem
    {
        /// <summary>
        /// Dimension data
        /// </summary>
        [JsonPropertyName("dimensions")]
        public SmartPlusReportDimensions? Dimensions { get; set; }

        /// <summary>
        /// Metric data
        /// </summary>
        [JsonPropertyName("metrics")]
        public SmartPlusReportMetrics? Metrics { get; set; }
    }

    /// <summary>
    /// Smart+ Campaign report dimensions
    /// </summary>
    public class SmartPlusReportDimensions
    {
        /// <summary>
        /// Material ID
        /// </summary>
        [JsonPropertyName("material_id")]
        public string? MaterialId { get; set; }

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }
    }

    /// <summary>
    /// Smart+ Campaign report metrics
    /// </summary>
    public class SmartPlusReportMetrics
    {
        /// <summary>
        /// Whether the material is removed from the Smart+ ads
        /// </summary>
        [JsonPropertyName("is_removed_from_spc")]
        public string? IsRemovedFromSpc { get; set; }

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Video name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }

        /// <summary>
        /// Ad text
        /// </summary>
        [JsonPropertyName("ad_text")]
        public string? AdText { get; set; }

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// The ID of the TikTok post to be used as an ad (Spark Ad)
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TikTokItemId { get; set; }

        /// <summary>
        /// The username of the TikTok account
        /// </summary>
        [JsonPropertyName("tiktok_name")]
        public string? TikTokName { get; set; }

        /// <summary>
        /// The name of the catalog
        /// </summary>
        [JsonPropertyName("catalog_name")]
        public string? CatalogName { get; set; }

        /// <summary>
        /// The time when the catalog was created
        /// </summary>
        [JsonPropertyName("catalog_create_time")]
        public string? CatalogCreateTime { get; set; }

        /// <summary>
        /// The time when the catalog was last modified
        /// </summary>
        [JsonPropertyName("catalog_modify_time")]
        public string? CatalogModifyTime { get; set; }

        /// <summary>
        /// Spend amount
        /// </summary>
        [JsonPropertyName("spend")]
        public string? Spend { get; set; }

        /// <summary>
        /// Number of impressions
        /// </summary>
        [JsonPropertyName("impressions")]
        public string? Impressions { get; set; }

        /// <summary>
        /// Number of clicks
        /// </summary>
        [JsonPropertyName("clicks")]
        public string? Clicks { get; set; }

        /// <summary>
        /// Click-through rate
        /// </summary>
        [JsonPropertyName("ctr")]
        public string? Ctr { get; set; }

        /// <summary>
        /// Cost per click
        /// </summary>
        [JsonPropertyName("cpc")]
        public string? Cpc { get; set; }

        /// <summary>
        /// Cost per mille (thousand impressions)
        /// </summary>
        [JsonPropertyName("cpm")]
        public string? Cpm { get; set; }

        /// <summary>
        /// Number of conversions
        /// </summary>
        [JsonPropertyName("conversions")]
        public string? Conversions { get; set; }

        /// <summary>
        /// Conversion rate
        /// </summary>
        [JsonPropertyName("conversion_rate")]
        public string? ConversionRate { get; set; }

        /// <summary>
        /// Cost per conversion
        /// </summary>
        [JsonPropertyName("cost_per_conversion")]
        public string? CostPerConversion { get; set; }

        /// <summary>
        /// Return on ad spend
        /// </summary>
        [JsonPropertyName("roas")]
        public string? Roas { get; set; }

        /// <summary>
        /// Video play actions
        /// </summary>
        [JsonPropertyName("video_play_actions")]
        public string? VideoPlayActions { get; set; }

        /// <summary>
        /// Video views at 25%
        /// </summary>
        [JsonPropertyName("video_views_p25")]
        public string? VideoViewsP25 { get; set; }

        /// <summary>
        /// Video views at 50%
        /// </summary>
        [JsonPropertyName("video_views_p50")]
        public string? VideoViewsP50 { get; set; }

        /// <summary>
        /// Video views at 75%
        /// </summary>
        [JsonPropertyName("video_views_p75")]
        public string? VideoViewsP75 { get; set; }

        /// <summary>
        /// Video views at 100%
        /// </summary>
        [JsonPropertyName("video_views_p100")]
        public string? VideoViewsP100 { get; set; }

        /// <summary>
        /// Number of likes
        /// </summary>
        [JsonPropertyName("likes")]
        public string? Likes { get; set; }

        /// <summary>
        /// Number of comments
        /// </summary>
        [JsonPropertyName("comments")]
        public string? Comments { get; set; }

        /// <summary>
        /// Number of shares
        /// </summary>
        [JsonPropertyName("shares")]
        public string? Shares { get; set; }

        /// <summary>
        /// Number of follows
        /// </summary>
        [JsonPropertyName("follows")]
        public string? Follows { get; set; }

        /// <summary>
        /// Profile visits
        /// </summary>
        [JsonPropertyName("profile_visits")]
        public string? ProfileVisits { get; set; }

        /// <summary>
        /// Frequency
        /// </summary>
        [JsonPropertyName("frequency")]
        public string? Frequency { get; set; }

        /// <summary>
        /// Reach
        /// </summary>
        [JsonPropertyName("reach")]
        public string? Reach { get; set; }
    }

    /// <summary>
    /// Pagination information for Smart+ Campaign reports
    /// </summary>
    public class SmartPlusPageInfo
    {
        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int Page { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of results
        /// </summary>
        [JsonPropertyName("total_number")]
        public int TotalNumber { get; set; }

        /// <summary>
        /// Total pages of results
        /// </summary>
        [JsonPropertyName("total_page")]
        public int TotalPage { get; set; }
    }
}
