using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using TikTok.Repositories;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Threading;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Background worker để xóa các bản ghi JobEntity đã hết hạn dựa trên trường ExpireAt
    /// Chạy mỗi ngày một lần để tối ưu lưu trữ
    /// </summary>
    public class JobCleanupWorker : AsyncPeriodicBackgroundWorkerBase, ITransientDependency
    {
        private readonly ILogger<JobCleanupWorker> _logger;

        public JobCleanupWorker(
            ILogger<JobCleanupWorker> logger,
            AbpAsyncTimer timer,
            IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
        {
            _logger = logger;
            // 1 giờ chạy 1 lần
            Timer.Period = 3600000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            _logger.LogDebug("Starting JobCleanupWorker - Cleaning up expired job records");

            try
            {
                using (var scope = ServiceScopeFactory.CreateScope())
                {
                    var jobRepository = scope.ServiceProvider.GetRequiredService<IJobRepository>();

                    // Xóa các bản ghi đã hết hạn dựa trên trường ExpireAt
                    var deletedCount = await jobRepository.CleanupExpiredJobsAsync();

                    if (deletedCount > 0)
                    {
                        _logger.LogDebug("JobCleanupWorker completed successfully - Deleted {Count} expired job records", deletedCount);
                    }
                    else
                    {
                        _logger.LogDebug("JobCleanupWorker completed - No expired job records to delete");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "JobCleanupWorker failed while cleaning up expired job records");
            }
        }
    }
}