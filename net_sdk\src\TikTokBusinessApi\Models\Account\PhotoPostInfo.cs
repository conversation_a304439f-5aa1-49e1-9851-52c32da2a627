/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Information about the photo post
    /// </summary>
    public class PhotoPostInfo
    {
        /// <summary>
        /// The privacy level of the photo post
        /// Enum values: PUBLIC_TO_EVERYONE, MUTUAL_FOLLOW_FRIENDS, FOLLOWER_OF_CREATOR, SELF_ONLY
        /// </summary>
        [JsonPropertyName("privacy_level")]
        public string PrivacyLevel { get; set; } = string.Empty;

        /// <summary>
        /// The title of the post
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// Post caption/description - which can contain #hashtags and @mentions
        /// </summary>
        [JsonPropertyName("caption")]
        public string? Caption { get; set; }

        /// <summary>
        /// Whether to automatically add recommended music to the post
        /// </summary>
        [JsonPropertyName("auto_add_music")]
        public bool? AutoAddMusic { get; set; }

        /// <summary>
        /// Whether to enable the Brand Organic Content toggle for the video
        /// </summary>
        [JsonPropertyName("is_brand_organic")]
        public bool? IsBrandOrganic { get; set; }

        /// <summary>
        /// Whether to enable the Branded Content toggle for the video
        /// </summary>
        [JsonPropertyName("is_branded_content")]
        public bool? IsBrandedContent { get; set; }

        /// <summary>
        /// Whether to disable the "Allow comments" setting for the published photo post
        /// </summary>
        [JsonPropertyName("disable_comment")]
        public bool? DisableComment { get; set; }
    }
}
