/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Get Partners Models

    /// <summary>
    /// Request for getting partners of a Business Center
    /// </summary>
    public class GetPartnersRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Filtering by names
        /// </summary>
        [JsonPropertyName("filtering")]
        public GetPartnersFiltering? Filtering { get; set; }
    }

    /// <summary>
    /// Filtering for get partners requests
    /// </summary>
    public class GetPartnersFiltering
    {
        /// <summary>
        /// Partner name keyword to filter the results
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }
    }

    /// <summary>
    /// Response for getting partners
    /// </summary>
    public class GetPartnersResponse
    {
        /// <summary>
        /// Partner list
        /// </summary>
        [JsonPropertyName("list")]
        public List<PartnerInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    #endregion

    #region Add Partner Models

    /// <summary>
    /// Request for adding a partner to a Business Center
    /// </summary>
    public class AddPartnerRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Partner Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("partner_id")]
        public string PartnerId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type (Required). Enum value: ADVERTISER
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// List of asset IDs. The assets need to be in the same type
        /// </summary>
        [JsonPropertyName("asset_ids")]
        public List<string>? AssetIds { get; set; }

        /// <summary>
        /// Ad account role assigned to the partner. Valid only when asset_type is ADVERTISER.
        /// Enum values: ADMIN, OPERATOR, ANALYST. Default value: ANALYST
        /// </summary>
        [JsonPropertyName("advertiser_role")]
        public string? AdvertiserRole { get; set; }

        /// <summary>
        /// Business Center user's permissions to the catalog. Valid when asset_type = CATALOG.
        /// Enum values: ADMIN, AD_PROMOTE
        /// </summary>
        [JsonPropertyName("catalog_role")]
        public string? CatalogRole { get; set; }
    }

    /// <summary>
    /// Response for adding a partner
    /// </summary>
    public class AddPartnerResponse
    {
        /// <summary>
        /// Response data (empty object on success)
        /// </summary>
        [JsonPropertyName("data")]
        public object? Data { get; set; }
    }

    #endregion

    #region Delete Partner Models

    /// <summary>
    /// Request for deleting a partner from a Business Center
    /// </summary>
    public class DeletePartnerRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Partner Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("partner_id")]
        public string PartnerId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for deleting a partner
    /// </summary>
    public class DeletePartnerResponse
    {
        /// <summary>
        /// Response data (empty object on success)
        /// </summary>
        [JsonPropertyName("data")]
        public object? Data { get; set; }
    }

    #endregion

    #region Cancel Asset Sharing Models

    /// <summary>
    /// Request for canceling the sharing of assets with a partner
    /// </summary>
    public class CancelAssetSharingRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Partner Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("partner_id")]
        public string PartnerId { get; set; } = string.Empty;

        /// <summary>
        /// List of asset IDs that you want to cancel the sharing of (Required)
        /// Currently, only one form library ID is allowed
        /// </summary>
        [JsonPropertyName("asset_ids")]
        public List<string> AssetIds { get; set; } = new List<string>();

        /// <summary>
        /// Asset type (Required). Enum values: ADVERTISER, CATALOG, TIKTOK_SHOP, STOREFRANT, and PIXEL
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for canceling asset sharing
    /// </summary>
    public class CancelAssetSharingResponse
    {
        /// <summary>
        /// Response data (empty object on success)
        /// </summary>
        [JsonPropertyName("data")]
        public object? Data { get; set; }
    }

    #endregion

    #region Get Partner Assets Models

    /// <summary>
    /// Request for getting assets of a partner
    /// </summary>
    public class GetPartnerAssetsRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Partner Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("partner_id")]
        public string PartnerId { get; set; } = string.Empty;

        /// <summary>
        /// Asset type (Required). Enum values: ADVERTISER, CATALOG, TIKTOK_SHOP, and PIXEL
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string AssetType { get; set; } = string.Empty;

        /// <summary>
        /// Share type (Required). Enum values:
        /// SHARED: Assets that are shared with you by a partner
        /// SHARING: Assets that you share with a partner
        /// </summary>
        [JsonPropertyName("share_type")]
        public string ShareType { get; set; } = string.Empty;

        /// <summary>
        /// Filtering by keyword
        /// </summary>
        [JsonPropertyName("filtering")]
        public PartnerAssetFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering for partner asset requests
    /// </summary>
    public class PartnerAssetFiltering
    {
        /// <summary>
        /// Keyword you want the assets filtered by
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }
    }

    /// <summary>
    /// Response for getting partner assets
    /// </summary>
    public class GetPartnerAssetsResponse
    {
        /// <summary>
        /// List of partner assets
        /// </summary>
        [JsonPropertyName("list")]
        public List<PartnerAssetInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Partner asset information
    /// </summary>
    public class PartnerAssetInfo
    {
        /// <summary>
        /// Asset ID
        /// If the asset_type is ADVERTISER, this is the advertiser ID
        /// If the asset_type is CATALOG, this is the catalog ID
        /// If the asset_type is TIKTOK_SHOP, this is the TikTok Shop ID
        /// If the asset_type is PIXEL, this is the Pixel ID
        /// </summary>
        [JsonPropertyName("asset_id")]
        public string? AssetId { get; set; }

        /// <summary>
        /// Asset type. Enum values: ADVERTISER, CATALOG, TIKTOK_SHOP, and PIXEL
        /// </summary>
        [JsonPropertyName("asset_type")]
        public string? AssetType { get; set; }

        /// <summary>
        /// Asset name
        /// </summary>
        [JsonPropertyName("asset_name")]
        public string? AssetName { get; set; }

        /// <summary>
        /// Ad account role. Enum values: ADMIN, OPERATOR, ANALYST
        /// Returned only when asset_type is ADVERTISER
        /// </summary>
        [JsonPropertyName("advertiser_role")]
        public string? AdvertiserRole { get; set; }

        /// <summary>
        /// Type of the ad account. Enum values: RESERVATION (reservation ad account), AUCTION (auction ad account)
        /// </summary>
        [JsonPropertyName("advertiser_account_type")]
        public string? AdvertiserAccountType { get; set; }

        /// <summary>
        /// Business Center user's permissions to the catalog. Valid when asset_type = CATALOG
        /// Enum values:
        /// ADMIN: The Business Center user has Admin permissions to the catalog
        /// AD_PROMOTE: The Business Center user can only use the catalog for promotions
        /// </summary>
        [JsonPropertyName("catalog_role")]
        public string? CatalogRole { get; set; }
    }

    #endregion
}
