/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Catalog Insights operations
    /// </summary>
    public interface ICatalogInsightsApi
    {
        /// <summary>
        /// Get the available filters for generating targeted insights on a limited number of products within an E-commerce catalog.
        /// The returned filter values can be used with GetTrendingProductsAsync to obtain insights about trending products in your catalog.
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID. The catalog needs to be an E-commerce catalog that contains at least 20 products.</param>
        /// <param name="filterType">The type of filter options. Enum values: CATEGORY_ID, BRAND, AVAILABILITY</param>
        /// <param name="page">Current page number. Default value: 1. Value range: ≥ 1.</param>
        /// <param name="pageSize">Page size. Default value: 10. Value range: 1-200.</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Catalog insight filters response containing available filter values</returns>
        Task<CatalogInsightFiltersResponse> GetFiltersAsync(
            string bcId,
            string catalogId,
            string filterType,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve up to top 50 trending products based on user engagement on TikTok within an E-commerce catalog.
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID. The catalog needs to be an E-commerce catalog that contains at least 20 products.</param>
        /// <param name="filtering">Filtering conditions (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Catalog insight products response containing trending products</returns>
        Task<CatalogInsightProductsResponse> GetTrendingProductsAsync(
            string bcId,
            string catalogId,
            CatalogInsightProductFiltering? filtering = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve the number of products in your E-commerce catalog that match the top 50 trending product categories 
        /// based on user engagement on TikTok, along with the product availability percentage.
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID. The catalog needs to be an E-commerce catalog that contains at least 20 products.</param>
        /// <param name="filtering">Filtering conditions (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Catalog insight categories response containing trending categories</returns>
        Task<CatalogInsightCategoriesResponse> GetTrendingCategoriesAsync(
            string bcId,
            string catalogId,
            CatalogInsightCategoryFiltering? filtering = null,
            CancellationToken cancellationToken = default);
    }
}
