/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Auth;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;
using TikTokBusinessApi.Serialization;
using TikTokBusinessApi.Core.Resilience;

namespace TikTokBusinessApi.Core
{
    /// <summary>
    /// API client for TikTok Business API
    /// </summary>
    public class ApiClient : IApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ApiClient>? _logger;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly IAuthenticationFactoryRegistry _authenticationFactoryRegistry;
        private bool _disposed;

        // Resilience utilities
        private IRetryPolicy _retryPolicy;
        private IRateLimiter _rateLimiter;
        private ICircuitBreaker _circuitBreaker;
        private IRequestThrottler _requestThrottler;

        /// <summary>
        /// Base path for API requests
        /// </summary>
        public string BasePath { get; set; } = "https://business-api.tiktok.com";

        /// <summary>
        /// API version (e.g., "v1.3")
        /// </summary>
        public string ApiVersion { get; set; } = "v1.3";

        /// <summary>
        /// API prefix (e.g., "open_api")
        /// </summary>
        public string ApiPrefix { get; set; } = "open_api";

        /// <summary>
        /// Maximum number of retry attempts for failed requests
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Delay between retry attempts in milliseconds
        /// </summary>
        public int RetryDelayMs { get; set; } = 1000;

        /// <summary>
        /// Whether to enable detailed request/response logging
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = false;

        /// <summary>
        /// Default headers to be sent with every request
        /// </summary>
        public Dictionary<string, string> DefaultHeaders { get; }

        /// <summary>
        /// Timeout for HTTP requests
        /// </summary>
        public TimeSpan Timeout
        {
            get => _httpClient.Timeout;
            set => _httpClient.Timeout = value;
        }

        /// <summary>
        /// Retry policy for handling failed requests
        /// </summary>
        public IRetryPolicy RetryPolicy
        {
            get => _retryPolicy;
            set => _retryPolicy = value ?? throw new ArgumentNullException(nameof(value));
        }

        /// <summary>
        /// Rate limiter for controlling request frequency
        /// </summary>
        public IRateLimiter RateLimiter
        {
            get => _rateLimiter;
            set => _rateLimiter = value ?? throw new ArgumentNullException(nameof(value));
        }

        /// <summary>
        /// Circuit breaker for handling service failures
        /// </summary>
        public ICircuitBreaker CircuitBreaker
        {
            get => _circuitBreaker;
            set => _circuitBreaker = value ?? throw new ArgumentNullException(nameof(value));
        }

        /// <summary>
        /// Request throttler for managing request load
        /// </summary>
        public IRequestThrottler RequestThrottler
        {
            get => _requestThrottler;
            set => _requestThrottler = value ?? throw new ArgumentNullException(nameof(value));
        }

        /// <summary>
        /// Initializes a new instance of the ApiClient class
        /// </summary>
        /// <param name="httpClient">HTTP client instance</param>
        /// <param name="logger">Logger instance</param>
        /// <param name="retryPolicy">Retry policy (optional)</param>
        /// <param name="rateLimiter">Rate limiter (optional)</param>
        /// <param name="circuitBreaker">Circuit breaker (optional)</param>
        /// <param name="requestThrottler">Request throttler (optional)</param>
        /// <param name="authenticationFactoryRegistry">Authentication factory registry (optional)</param>
        public ApiClient(
            HttpClient? httpClient = null,
            ILogger<ApiClient>? logger = null,
            IRetryPolicy? retryPolicy = null,
            IRateLimiter? rateLimiter = null,
            ICircuitBreaker? circuitBreaker = null,
            IRequestThrottler? requestThrottler = null,
            IAuthenticationFactoryRegistry? authenticationFactoryRegistry = null)
        {
            _httpClient = httpClient ?? new HttpClient();
            _logger = logger;

            DefaultHeaders = new Dictionary<string, string>
            {
                ["Business-SDK"] = "1",
                ["SDK-Language"] = "CSharp",
                ["SDK-Version"] = "1.0.0",
                ["User-Agent"] = "TikTokBusinessApi-CSharp/1.0.0"
            };

            _jsonOptions = JsonSerializerOptionsExtensions.CreateDefault();
            _authenticationFactoryRegistry = authenticationFactoryRegistry ?? new AuthenticationFactoryRegistry();

            // Initialize resilience utilities with defaults if not provided
            _retryPolicy = retryPolicy ?? new DefaultRetryPolicy();
            _rateLimiter = rateLimiter ?? new NoOpRateLimiter();
            _circuitBreaker = circuitBreaker ?? new NoOpCircuitBreaker();
            _requestThrottler = requestThrottler ?? new NoOpRequestThrottler();
        }

        /// <summary>
        /// Registers an authentication factory for a specific type
        /// </summary>
        /// <param name="authenticationType">Authentication type name</param>
        /// <param name="factory">Authentication factory</param>
        public void RegisterAuthenticationFactory(string authenticationType, IAuthenticationFactory factory)
        {
            _authenticationFactoryRegistry.RegisterFactory(authenticationType, factory);
        }

        /// <summary>
        /// Gets an authentication factory by type
        /// </summary>
        /// <param name="authenticationType">Authentication type name</param>
        /// <returns>Authentication factory or null if not found</returns>
        public IAuthenticationFactory? GetAuthenticationFactory(string authenticationType)
        {
            return _authenticationFactoryRegistry.GetFactory(authenticationType);
        }

        /// <summary>
        /// Creates an authentication instance using the appropriate factory
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>Authentication instance or null if not found</returns>
        public async Task<IAuthentication?> CreateAuthenticationAsync(AuthenticationContext context)
        {
            return await _authenticationFactoryRegistry.CreateAuthenticationAsync(context);
        }

        /// <summary>
        /// Gets the TikTok authentication context for managing access tokens
        /// </summary>
        /// <returns>TikTok authentication context or null if not configured</returns>
        public IAuthenticationContext? GetTikTokAuthenticationContext()
        {
            // For factory pattern, we can't maintain a persistent authentication context
            // This method is kept for backward compatibility but will return null
            _logger?.LogWarning("GetTikTokAuthenticationContext is not supported with factory pattern. Use CreateAuthenticationAsync instead.");
            return null;
        }

        /// <summary>
        /// Sets the access token for TikTok authentication
        /// </summary>
        /// <param name="accessToken">Access token to set</param>
        /// <returns>Task that completes when the token is set</returns>
        public async Task SetAccessTokenAsync(string? accessToken)
        {
            // For factory pattern, access tokens are managed per request context
            // This method is kept for backward compatibility but does nothing
            _logger?.LogWarning("SetAccessTokenAsync is not supported with factory pattern. Pass access token via AuthenticationContext instead.");
            await Task.CompletedTask;
        }

        /// <summary>
        /// Gets the current access token from TikTok authentication
        /// </summary>
        /// <returns>Current access token or null if not available</returns>
        public async Task<string?> GetAccessTokenAsync()
        {
            // For factory pattern, access tokens are managed per request context
            // This method is kept for backward compatibility but returns null
            _logger?.LogWarning("GetAccessTokenAsync is not supported with factory pattern. Access tokens are resolved per request.");
            return await Task.FromResult<string?>(null);
        }

        /// <summary>
        /// Makes an API call and returns the business model directly
        /// </summary>
        /// <typeparam name="T">Business model type</typeparam>
        /// <param name="path">API path</param>
        /// <param name="method">HTTP method</param>
        /// <param name="queryParams">Query parameters</param>
        /// <param name="body">Request body</param>
        /// <param name="headerParams">Header parameters</param>
        /// <param name="authNames">Authentication names to apply</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Business model data</returns>
        public virtual async Task<T> CallApiAsync<T>(
            string path,
            HttpMethod method,
            Dictionary<string, string>? queryParams = null,
            object? body = null,
            Dictionary<string, string>? headerParams = null,
            string[]? authNames = null,
            CancellationToken cancellationToken = default)
        {
            // Use default authentication context for backward compatibility
            var authContext = AuthenticationContext.Default();
            return await CallApiAsync<T>(path, method, queryParams, body, headerParams, authContext, cancellationToken);
        }

        /// <summary>
        /// Calls the API with authentication context
        /// </summary>
        /// <typeparam name="T">Response type</typeparam>
        /// <param name="path">API path</param>
        /// <param name="method">HTTP method</param>
        /// <param name="queryParams">Query parameters</param>
        /// <param name="body">Request body</param>
        /// <param name="headerParams">Header parameters</param>
        /// <param name="authenticationContext">Authentication context</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>API response</returns>
        public virtual async Task<T> CallApiAsync<T>(
            string path,
            HttpMethod method,
            Dictionary<string, string>? queryParams = null,
            object? body = null,
            Dictionary<string, string>? headerParams = null,
            AuthenticationContext? authenticationContext = null,
            CancellationToken cancellationToken = default)
        {
            // Use default authentication context if not provided
            authenticationContext ??= AuthenticationContext.Default();

            // Create request context for throttling
            var requestContext = new RequestContext
            {
                HttpMethod = method.Method,
                Path = path,
                CreatedAt = DateTimeOffset.UtcNow
            };

            // Apply throttling
            await _requestThrottler.ThrottleAsync(requestContext, cancellationToken);

            var startTime = DateTimeOffset.UtcNow;
            var success = false;

            try
            {
                // Execute request through circuit breaker and retry policy
                var result = await _circuitBreaker.ExecuteAsync(async ct =>
                {
                    return await _retryPolicy.ExecuteAsync(async retryToken =>
                    {
                        // Apply rate limiting
                        await _rateLimiter.WaitAsync(retryToken);

                        // Execute request and handle TikTok API response
                        var apiResponse = await CallApiInternalAsync<T>(
                            path, method, queryParams, body, headerParams, authenticationContext, retryToken);
                        var tikTokResponse = apiResponse.Data;

                        // check if response is type TikTokApiResponse<T>
                        if (tikTokResponse is  TikTokApiResponse<T> tikTokApiResponse)
                        {
                            // Check for TikTok API errors
                            if (tikTokApiResponse?.Code != 0)
                            {
                                var exception = TikTokExceptionFactory.CreateException(
                                    tikTokApiResponse?.RequestId ?? string.Empty,
                                    (int)(tikTokApiResponse?.Code ?? 0),
                                    tikTokApiResponse?.Message ?? "Unknown error",
                                    apiResponse.StatusCode);

                                throw exception;
                            }
                        }

                        return tikTokResponse == null ? default : tikTokResponse;
                    }, ct);
                }, cancellationToken);

                success = true;
                return result;
            }
            finally
            {
                // Record request completion for throttling metrics
                var responseTime = DateTimeOffset.UtcNow - startTime;
                _requestThrottler.RecordRequestCompletion(requestContext, responseTime, success);
            }
        }

        public static bool IsInstanceOfTikTokApiResponse(object obj)
        {
            if (obj == null) return false;

            var type = obj.GetType();

            while (type != null && type != typeof(object))
            {
                if (type.IsGenericType &&
                    type.GetGenericTypeDefinition() == typeof(TikTokApiResponse<>))
                {
                    return true;
                }

                type = type.BaseType;
            }

            return false;
        }

        /// <summary>
        /// Makes an internal API call returning the raw ApiResponse
        /// </summary>
        /// <typeparam name="T">Response type</typeparam>
        /// <param name="path">API path</param>
        /// <param name="method">HTTP method</param>
        /// <param name="queryParams">Query parameters</param>
        /// <param name="body">Request body</param>
        /// <param name="headerParams">Header parameters</param>
        /// <param name="authenticationContext">Authentication context</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>API response</returns>
        internal virtual async Task<ApiResponse<T>> CallApiInternalAsync<T>(
            string path,
            HttpMethod method,
            Dictionary<string, string>? queryParams = null,
            object? body = null,
            Dictionary<string, string>? headerParams = null,
            AuthenticationContext? authenticationContext = null,
            CancellationToken cancellationToken = default)
        {
            var uri = BuildUri(path, queryParams);
            using var request = new HttpRequestMessage(method, uri);
            // Add default headers
            foreach (var header in DefaultHeaders)
            {
                request.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            // Add custom headers
            if (headerParams != null)
            {
                foreach (var header in headerParams)
                {
                    request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                }
            }

            // Apply authentication and modify request body if needed
            object? finalBody = body;
            if (authenticationContext != null)
            {
                var auth = await CreateAuthenticationAsync(authenticationContext);
                if (auth != null)
                {
                    // Apply header-based authentication
                    auth.ApplyToRequest(request);

                    // Apply body-based authentication (for TikTok APIs)
                    if (auth is IBodyAuthentication bodyAuth)
                    {
                        finalBody = await bodyAuth.ApplyToRequestBodyAsync(request, finalBody);
                    }
                }
                else
                {
                    _logger?.LogWarning("Failed to create authentication for context: {UserId}, {AuthType}",
                        authenticationContext.UserId ?? "anonymous", authenticationContext.AuthenticationType);
                }
            }

            // Add request body (potentially modified by authentication)
            if (finalBody != null)
            {
                var json = JsonSerializer.Serialize(finalBody, _jsonOptions);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            }

            return await ExecuteRequestAsync<T>(request, uri, cancellationToken);
        }

        /// <summary>
        /// Call API and return raw byte array (for file downloads)
        /// </summary>
        /// <param name="path">API path</param>
        /// <param name="method">HTTP method</param>
        /// <param name="queryParams">Query parameters</param>
        /// <param name="body">Request body</param>
        /// <param name="headerParams">Header parameters</param>
        /// <param name="authenticationContext">Authentication context</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Raw byte array response</returns>
        public virtual async Task<byte[]> CallApiRawAsync(
            string path,
            HttpMethod method,
            Dictionary<string, string>? queryParams = null,
            object? body = null,
            Dictionary<string, string>? headerParams = null,
            AuthenticationContext? authenticationContext = null,
            CancellationToken cancellationToken = default)
        {
            var uri = BuildUri(path, queryParams);
            using var request = new HttpRequestMessage(method, uri);

            // Add default headers
            foreach (var header in DefaultHeaders)
            {
                request.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            // Add custom headers
            if (headerParams != null)
            {
                foreach (var header in headerParams)
                {
                    request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                }
            }

            // Apply authentication
            if (authenticationContext != null)
            {
                var auth = await CreateAuthenticationAsync(authenticationContext);
                if (auth != null)
                {
                    auth.ApplyToRequest(request);
                }
            }

            // Add request body
            if (body != null)
            {
                var json = JsonSerializer.Serialize(body, _jsonOptions);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            }

            _logger?.LogDebug("Making {Method} request to {Uri} for raw data", request.Method, uri);

            using var requestClone = await CloneRequestAsync(request, cancellationToken);
            var response = await _httpClient.SendAsync(requestClone, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger?.LogError("Raw API request failed with status {StatusCode}: {ErrorContent}",
                    response.StatusCode, errorContent);
                throw new ApiException($"Request failed with status {response.StatusCode}: {errorContent}");
            }

            return await response.Content.ReadAsByteArrayAsync(cancellationToken);
        }

        private async Task<ApiResponse<T>> ExecuteRequestAsync<T>(
            HttpRequestMessage request,
            Uri uri,
            CancellationToken cancellationToken)
        {
            if (EnableDetailedLogging)
            {
                _logger?.LogDebug("Making {Method} request to {Uri}", request.Method, uri);

                if (request.Content != null)
                {
                    var requestBody = await request.Content.ReadAsStringAsync(cancellationToken);
                    _logger?.LogDebug("Request body: {RequestBody}", requestBody);
                }
            }
            else
            {
                _logger?.LogDebug("Making {Method} request to {Uri}", request.Method, uri);
            }

#if DEBUG
            //Console.WriteLine($"[Debug] Request url: {uri}");
            //Console.WriteLine($"[Debug] Request: {JsonSerializer.Serialize(request, _jsonOptions)}");
            // Clone the request for execution
            using var requestClone = await CloneRequestAsync(request, cancellationToken);
            // Send the request
            Console.WriteLine($"[Debug] ===");
            Console.WriteLine($"[Debug] Sending request to: {uri}");
            var response = await _httpClient.SendAsync(requestClone, cancellationToken);
            //Console.WriteLine($"[Debug] Response: {JsonSerializer.Serialize(response, _jsonOptions)}");
#else

            // Clone the request for execution
            using var requestClone = await CloneRequestAsync(request, cancellationToken);
            var response = await _httpClient.SendAsync(requestClone, cancellationToken);
#endif
            var result = await ProcessResponseAsync<T>(response, cancellationToken);

            if (EnableDetailedLogging && result.Data != null)
            {
                var responseJson = JsonSerializer.Serialize(result.Data, _jsonOptions);
                _logger?.LogDebug("Response data: {ResponseData}", responseJson);
            }

            return result;
        }



        private static async Task<HttpRequestMessage> CloneRequestAsync(HttpRequestMessage original, CancellationToken cancellationToken)
        {
            var clone = new HttpRequestMessage(original.Method, original.RequestUri);

            // Copy headers
            foreach (var header in original.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            // Copy content if present
            if (original.Content != null)
            {
                var contentBytes = await original.Content.ReadAsByteArrayAsync(cancellationToken);
                clone.Content = new ByteArrayContent(contentBytes);

                // Copy content headers
                foreach (var header in original.Content.Headers)
                {
                    clone.Content.Headers.TryAddWithoutValidation(header.Key, header.Value);
                }
            }

            return clone;
        }

        private Uri BuildUri(string path, Dictionary<string, string>? queryParams)
        {
            // Construct the full versioned path: /open_api/v1.3{path}
            var versionedPath = $"/{ApiPrefix}/{ApiVersion}{path}";
            var uriBuilder = new UriBuilder(BasePath + versionedPath);

            if (queryParams?.Count > 0)
            {
                var query = new StringBuilder();
                foreach (var param in queryParams)
                {
                    if (query.Length > 0)
                        query.Append('&');
                    query.Append(Uri.EscapeDataString(param.Key));
                    query.Append('=');
                    query.Append(Uri.EscapeDataString(param.Value));
                }
                uriBuilder.Query = query.ToString();
            }

            return uriBuilder.Uri;
        }

        private async Task<ApiResponse<T>> ProcessResponseAsync<T>(HttpResponseMessage response, CancellationToken cancellationToken)
        {
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger?.LogDebug("Received response: {StatusCode} {Content}", response.StatusCode, content);

            if (!response.IsSuccessStatusCode)
            {
                throw new ApiException($"API call failed with status {response.StatusCode}: {content}")
                {
                    StatusCode = (int)response.StatusCode,
                    ResponseContent = content
                };
            }

            var apiResponse = new ApiResponse<T>
            {
                StatusCode = (int)response.StatusCode,
                Headers = new Dictionary<string, string>()
            };

            // Copy response headers
            foreach (var header in response.Headers)
            {
                apiResponse.Headers[header.Key] = string.Join(", ", header.Value);
            }

            // Deserialize response content
            if (!string.IsNullOrEmpty(content) && typeof(T) != typeof(string))
            {
                try
                {
                    apiResponse.Data = JsonSerializer.Deserialize<T>(content, _jsonOptions);
                }
                catch (JsonException ex)
                {
                    _logger?.LogError(ex, "Failed to deserialize response content: {Content}", content);
                    throw new ApiException($"Failed to deserialize response: {ex.Message}", ex);
                }
            }
            else if (typeof(T) == typeof(string))
            {
                apiResponse.Data = (T)(object)content;
            }

            return apiResponse;
        }

        /// <summary>
        /// Disposes the API client
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _httpClient?.Dispose();
                _disposed = true;
            }
        }
    }
}
