using System.ComponentModel.DataAnnotations;
using TikTok.Enums;

namespace TikTok.BusinessCenters
{
    /// <summary>
    /// DTO cho việc tạo mới trung tâm kinh doanh
    /// </summary>
    public class CreateBusinessCenterDto
    {
        /// <summary>
        /// ID của trung tâm kinh doanh
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// Tên trung tâm kinh doanh
        /// </summary>
        [Required]
        [StringLength(255)]
        public string Name { get; set; }

        /// <summary>
        /// Tên công ty của trung tâm kinh doanh
        /// </summary>
        [Required]
        [StringLength(255)]
        public string Company { get; set; }

        /// <summary>
        /// Đơn vị tiền tệ thanh toán
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Currency { get; set; }

        /// <summary>
        /// <PERSON><PERSON> vực đăng ký
        /// </summary>
        [Required]
        [StringLength(50)]
        public string RegisteredArea { get; set; }

        /// <summary>
        /// Trạng thái của trung tâm kinh doanh
        /// </summary>
        [Required]
        public BusinessCenterStatus Status { get; set; }

        /// <summary>
        /// Múi giờ
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Timezone { get; set; }

        /// <summary>
        /// Loại trung tâm kinh doanh
        /// </summary>
        [Required]
        public BusinessCenterType Type { get; set; }

        /// <summary>
        /// Vai trò người dùng
        /// </summary>
        [Required]
        public UserRole UserRole { get; set; }

        /// <summary>
        /// Vai trò tài chính mở rộng
        /// </summary>
        public ExtUserFinanceRole? ExtUserFinanceRole { get; set; }
    }
} 