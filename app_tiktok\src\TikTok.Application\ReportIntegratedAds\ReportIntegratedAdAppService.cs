using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTok.ReportIntegratedAds;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.ReportIntegratedAds
{
    /// <summary>
    /// Service implementation cho báo cáo tích hợp Ad
    /// </summary>
    public class ReportIntegratedAdAppService :
        CrudAppService<
            RawReportIntegratedAdEntity,
            ReportIntegratedAdDto,
            Guid,
            GetReportIntegratedAdListDto,
            CreateReportIntegratedAdDto,
            UpdateReportIntegratedAdDto>,
        IReportIntegratedAdAppService
    {
        private readonly IRawReportIntegratedAdRepository _reportIntegratedAdRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="repository">Repository</param>
        /// <param name="reportIntegratedAdRepository">Report Integrated Ad Repository</param>
        public ReportIntegratedAdAppService(
            IRepository<RawReportIntegratedAdEntity, Guid> repository,
            IRawReportIntegratedAdRepository reportIntegratedAdRepository) : base(repository)
        {
            _reportIntegratedAdRepository = reportIntegratedAdRepository;

            GetPolicyName = TikTokPermissions.ReportIntegratedAds.Default;
            GetListPolicyName = TikTokPermissions.ReportIntegratedAds.Default;
            CreatePolicyName = TikTokPermissions.ReportIntegratedAds.Create;
            UpdatePolicyName = TikTokPermissions.ReportIntegratedAds.Edit;
            DeletePolicyName = TikTokPermissions.ReportIntegratedAds.Delete;
        }

        /// <summary>
        /// Lấy danh sách báo cáo tích hợp Ad
        /// </summary>
        /// <param name="input">Thông tin đầu vào</param>
        /// <returns>Danh sách báo cáo tích hợp Ad</returns>
        public override async Task<PagedResultDto<ReportIntegratedAdDto>> GetListAsync(GetReportIntegratedAdListDto input)
        {
            var totalCount = await _reportIntegratedAdRepository.GetCountAsync(
                input.AdvertiserId,
                input.CampaignId,
                input.AdGroupId,
                input.AdId,
                input.AdName,
                input.StartDate,
                input.EndDate,
                input.Filter);

            var items = await _reportIntegratedAdRepository.GetListAsync(
                input.AdvertiserId,
                input.CampaignId,
                input.AdGroupId,
                input.AdId,
                input.AdName,
                input.StartDate,
                input.EndDate,
                input.Sorting,
                input.MaxResultCount,
                input.SkipCount,
                input.Filter);

            return new PagedResultDto<ReportIntegratedAdDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<RawReportIntegratedAdEntity>, List<ReportIntegratedAdDto>>(items)
            };
        }

        /// <summary>
        /// Lấy danh sách theo Advertiser IDs và khoảng thời gian
        /// </summary>
        /// <param name="advertiserIds">Danh sách ID tài khoản nhà quảng cáo</param>
        /// <param name="startDate">Ngày bắt đầu</param>
        /// <param name="endDate">Ngày kết thúc</param>
        /// <returns>Danh sách báo cáo tích hợp Ad</returns>
        public async Task<List<ReportIntegratedAdDto>> GetByAdvertiserIdsAndDateRangeAsync(
            List<string> advertiserIds,
            DateTime startDate,
            DateTime endDate)
        {
            var items = await _reportIntegratedAdRepository.GetByAdvertiserIdsAndDateRangeAsync(
                advertiserIds,
                startDate,
                endDate);

            return ObjectMapper.Map<List<RawReportIntegratedAdEntity>, List<ReportIntegratedAdDto>>(items);
        }

        /// <summary>
        /// Lấy báo cáo theo Ad ID và ngày
        /// </summary>
        /// <param name="adId">ID quảng cáo</param>
        /// <param name="date">Ngày báo cáo</param>
        /// <returns>Báo cáo tích hợp Ad</returns>
        public async Task<ReportIntegratedAdDto?> GetByAdIdAndDateAsync(
            string adId,
            DateTime date)
        {
            var items = await _reportIntegratedAdRepository.GetListAsync(
                adId: adId,
                startDate: date,
                endDate: date,
                maxResultCount: 1);

            var item = items.FirstOrDefault();
            return item != null ? ObjectMapper.Map<RawReportIntegratedAdEntity, ReportIntegratedAdDto>(item) : null;
        }

        /// <summary>
        /// Lấy danh sách báo cáo theo Ad ID
        /// </summary>
        /// <param name="adId">ID quảng cáo</param>
        /// <param name="startDate">Ngày bắt đầu</param>
        /// <param name="endDate">Ngày kết thúc</param>
        /// <returns>Danh sách báo cáo tích hợp Ad</returns>
        public async Task<List<ReportIntegratedAdDto>> GetByAdIdAsync(
            string adId,
            DateTime? startDate = null,
            DateTime? endDate = null)
        {
            var items = await _reportIntegratedAdRepository.GetListAsync(
                adId: adId,
                startDate: startDate,
                endDate: endDate);

            return ObjectMapper.Map<List<RawReportIntegratedAdEntity>, List<ReportIntegratedAdDto>>(items);
        }
    }
} 