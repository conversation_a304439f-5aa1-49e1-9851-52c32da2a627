/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Ad Account operations
    /// </summary>
    public interface IAdAccountApi
    {
        /// <summary>
        /// Get authorized ad accounts
        /// Use this endpoint to obtain a list of advertiser accounts that authorized an app.
        /// </summary>
        /// <param name="appId">The App id applied by the developer</param>
        /// <param name="secret">The private key of the developer's application</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of authorized advertiser accounts</returns>
        Task<TikTokApiResponse<AuthorizedAccountsResponse>> GetAuthorizedAccountsAsync(
            string appId,
            string secret,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get authorized ad accounts
        /// Use this endpoint to obtain a list of advertiser accounts that authorized an app.
        /// </summary>
        /// <param name="request">Request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of authorized advertiser accounts</returns>
        Task<TikTokApiResponse<AuthorizedAccountsResponse>> GetAuthorizedAccountsAsync(
            GetAuthorizedAccountsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get ad account details
        /// Use this endpoint to obtain the details of an advertiser's ad account.
        /// </summary>
        /// <param name="advertiserIds">List of advertiser IDs to query</param>
        /// <param name="fields">A list of information to be returned (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing ad account details</returns>
        Task<TikTokApiResponse<AdAccountDetailsResponse>> GetAdAccountDetailsAsync(
            List<string> advertiserIds,
            List<string>? fields = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get ad account details
        /// Use this endpoint to obtain the details of an advertiser's ad account.
        /// </summary>
        /// <param name="request">Request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing ad account details</returns>
        Task<TikTokApiResponse<AdAccountDetailsResponse>> GetAdAccountDetailsAsync(
            GetAdAccountDetailsRequest request,
            CancellationToken cancellationToken = default);
    }
}
