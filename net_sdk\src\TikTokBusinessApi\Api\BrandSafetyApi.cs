/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Brand Safety operations
    /// </summary>
    public class BrandSafetyApi : IBrandSafetyApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<BrandSafetyApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the BrandSafetyApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public BrandSafetyApi(IApiClient apiClient, ILogger<BrandSafetyApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get the Brand Safety Hub settings of an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing brand safety settings</returns>
        public async Task<Response<BrandSafetyResponse>> GetBrandSafetySettingsAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogDebug("Getting brand safety settings for advertiser {AdvertiserId}", advertiserId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<BrandSafetyResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BrandSafetyEndpoints.GetBrandSafetySettings}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Set or update the Brand Safety Hub settings of an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing brand safety settings to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated brand safety settings</returns>
        //public async Task<Response<BrandSafetyResponse>> UpdateBrandSafetySettingsAsync(
        //    string accessToken,
        //    BrandSafetyUpdateRequest body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(body.AdvertiserId));

        //    _logger?.LogDebug("Updating brand safety settings for advertiser {AdvertiserId}", body.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<BrandSafetyResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BrandSafetyEndpoints.UpdateBrandSafetySettings}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}
    }
}
