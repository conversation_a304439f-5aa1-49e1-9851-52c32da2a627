using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.Customers
{
    /// <summary>
    /// DTO cho tài khoản quảng cáo của khách hàng
    /// </summary>
    public class CustomerAdAccountDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID của khách hàng
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// ID của advertiser
        /// </summary>
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Tên của advertiser
        /// </summary>
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// ID của shop
        /// </summary>
        public string? ShopId { get; set; }

        /// <summary>
        /// Tên của shop
        /// </summary>
        public string? ShopName { get; set; }
    }

    /// <summary>
    /// DTO cho việc tạo mới tài khoản quảng cáo của khách hàng
    /// </summary>
    public class CreateCustomerAdAccountDto
    {
        /// <summary>
        /// ID của khách hàng
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// ID của advertiser
        /// </summary>
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Tên của advertiser
        /// </summary>
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// ID của shop
        /// </summary>
        public string? ShopId { get; set; }

        /// <summary>
        /// Tên của shop
        /// </summary>
        public string? ShopName { get; set; }
    }

    /// <summary>
    /// DTO cho việc cập nhật tài khoản quảng cáo của khách hàng
    /// </summary>
    public class UpdateCustomerAdAccountDto
    {
        /// <summary>
        /// ID của advertiser
        /// </summary>
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Tên của advertiser
        /// </summary>
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// ID của shop
        /// </summary>
        public string? ShopId { get; set; }

        /// <summary>
        /// Tên của shop
        /// </summary>
        public string? ShopName { get; set; }
    }
}
