# DimBusinessCenterEntity

## Thông tin bảng dữ liệu

-   **Table**: `DimBusinessCenters`
-   **TablePrefix**: `Dim_`
-   **TableName**: `Dim_DimBusinessCenters`
-   **DbSchema**: `TikTok`

## Mô tả

Dimension table lưu trữ thông tin Business Center. **Chỉ chứa những trường có thật trong RawBusinessCenterEntity**.

## Cấu trúc bảng

| Tên Field          | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                          |
| ------------------ | ------------ | -------- | ------ | ---------------------------------------------- |
| Id                 | Guid         | ✓        | -      | Khóa chính surrogate key (Identity)            |
| BcId               | string       | ✓        | 100    | ID duy nhất của Business Center (Business Key) |
| BcName             | string       | ✓        | 255    | Tên Business Center                            |
| Company            | string       | ✓        | 255    | Tên công ty                                    |
| Currency           | string       | ✓        | 10     | Đơn vị tiền tệ                                 |
| RegisteredArea     | string       | ✓        | 50     | Khu vực đăng ký                                |
| Status             | string       | ✓        | 50     | Trạng thái Business Center                     |
| Timezone           | string       | ✓        | 50     | Múi giờ                                        |
| Type               | string       | ✓        | 50     | Loại Business Center                           |
| UserRole           | string       | ✓        | 50     | Vai trò người dùng                             |
| ExtUserFinanceRole | string       | ✗        | 50     | Vai trò tài chính mở rộng                      |
| IsCurrent          | bool         | ✓        | -      | Record hiện tại (SCD Type 2)                   |
| EffectiveDate      | DateTime     | ✓        | -      | Ngày hiệu lực                                  |
| ExpirationDate     | DateTime     | ✗        | -      | Ngày hết hiệu lực                              |
| CreationTime       | DateTime     | ✓        | -      | Thời gian tạo bản ghi                          |
| RowVersion         | int          | ✓        | -      | Phiên bản bản ghi (SCD2)                       |

## ETL Mapping từ RawBusinessCenterEntity

-   **BcId** ← RawBusinessCenterEntity.BcId
-   **BcName** ← RawBusinessCenterEntity.Name
-   **Company** ← RawBusinessCenterEntity.Company
-   **Currency** ← RawBusinessCenterEntity.Currency
-   **RegisteredArea** ← RawBusinessCenterEntity.RegisteredArea
-   **Status** ← RawBusinessCenterEntity.Status
-   **Timezone** ← RawBusinessCenterEntity.Timezone
-   **Type** ← RawBusinessCenterEntity.Type
-   **UserRole** ← RawBusinessCenterEntity.UserRole
-   **ExtUserFinanceRole** ← RawBusinessCenterEntity.ExtUserFinanceRole

## Grain Definition

-   **Mỗi dòng đại diện cho**: 1 phiên bản Business Center
-   **Unique Key**: Id
-   **Business Key**: BcId

## SCD Type 2 - Tracked Changes

-   Status changes
-   UserRole changes
-   ExtUserFinanceRole changes
-   Company changes

## Business Questions

```sql
-- Business Centers theo trạng thái
SELECT Status, COUNT(*) FROM DimBusinessCenters WHERE IsCurrent = 1 GROUP BY Status

-- Business Centers theo khu vực
SELECT RegisteredArea, COUNT(*) FROM DimBusinessCenters WHERE IsCurrent = 1 GROUP BY RegisteredArea
```
