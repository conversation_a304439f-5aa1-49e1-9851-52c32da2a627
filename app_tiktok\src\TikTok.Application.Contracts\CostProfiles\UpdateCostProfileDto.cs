using System;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;

namespace TikTok.CostProfiles
{
    /// <summary>
    /// DTO cho việc cập nh<PERSON>t hồ sơ chi phí
    /// </summary>
    public class UpdateCostProfileDto
    {
        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        [Required]
        [StringLength(50)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// Tên nhà quảng cáo
        /// </summary>
        [Required]
        [StringLength(200)]
        public string AdvertiserName { get; set; }

        /// <summary>
        /// Tổng số tiền chi phí cho nhà quảng cáo
        /// </summary>
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Amount must be greater than or equal to 0")]
        public decimal Amount { get; set; }

        /// <summary>
        /// Số tiền chi phí tiền mặt cho nhà quảng cáo
        /// </summary>
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Cash amount must be greater than or equal to 0")]
        public decimal CashAmount { get; set; }

        /// <summary>
        /// Số tiền chi phí tín dụng quảng cáo cho nhà quảng cáo
        /// </summary>
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Grant amount must be greater than or equal to 0")]
        public decimal GrantAmount { get; set; }

        /// <summary>
        /// Số tiền thuế ước tính cho nhà quảng cáo
        /// </summary>
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Tax amount must be greater than or equal to 0")]
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// Tiền tệ của nhà quảng cáo
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Currency { get; set; }

        /// <summary>
        /// ID của Business Center
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// Thời gian đồng bộ dữ liệu (UTC) - lưu đầy đủ thời gian
        /// </summary>
        [Required]
        public DateTime Date { get; set; }
    }
}