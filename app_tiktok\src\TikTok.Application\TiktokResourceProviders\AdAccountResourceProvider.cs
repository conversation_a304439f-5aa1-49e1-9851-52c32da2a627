﻿using TikTok.Consts;
using TikTok.ResourceProviders;
using TikTok.ResourceProviders.AdAccounts;
using Volo.Abp.DependencyInjection;

namespace TikTok.TiktokResourceProviders
{
    public class AdAccountResourceProvider : ResourcePermissionProvider, IAdAccountResourceProvider
    {
        public AdAccountResourceProvider(IAbpLazyServiceProvider _lazyServiceProvider) : base(TiktokResourceType.AdAccount, _lazyServiceProvider)
        {
        }
    }
}