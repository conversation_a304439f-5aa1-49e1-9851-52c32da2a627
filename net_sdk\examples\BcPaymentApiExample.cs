/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the BC Payment API
    /// </summary>
    public class BcPaymentApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public BcPaymentApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example of processing a payment (recharge/refund)
        /// </summary>
        public async Task ProcessPaymentExample()
        {
            try
            {
                Console.WriteLine("Processing payment...");

                var paymentRequest = new ProcessPaymentRequest
                {
                    BcId = "your_bc_id_here",
                    TransferLevel = "ADVERTISER", // or "BC"
                    AdvertiserId = "your_advertiser_id_here", // Required when transfer_level is ADVERTISER
                    TransferType = "RECHARGE", // or "REFUND"
                    CashAmount = 100.00m, // Amount to process
                    // GrantAmount = 50.00m, // Alternative: voucher amount
                    // RequestId = "unique_request_id" // For idempotency when transfer_level is BC
                };

                var response = await _client.BcPayment.ProcessPaymentAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    paymentRequest);

                if (response.Data?.Data?.TransactionInfos != null)
                {
                    Console.WriteLine($"Payment processed successfully!");
                    Console.WriteLine($"Business Center ID: {response.Data.Data.BcId}");
                    Console.WriteLine($"Advertiser ID: {response.Data.Data.AdvertiserId}");
                    
                    foreach (var transaction in response.Data.Data.TransactionInfos)
                    {
                        Console.WriteLine($"Transaction ID: {transaction.TransactionId}");
                        Console.WriteLine($"Transaction Type: {transaction.TransactionType}");
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting advertiser balance and budget information
        /// </summary>
        public async Task GetAdvertiserBalanceExample()
        {
            try
            {
                Console.WriteLine("Getting advertiser balance...");

                var balanceRequest = new GetAdvertiserBalanceRequest
                {
                    BcId = "your_bc_id_here",
                    Fields = new List<string>
                    {
                        "budget_remaining",
                        "budget_frequency_restriction",
                        "budget_amount_restriction",
                        "min_transferable_amount"
                    },
                    Filtering = new AdvertiserBalanceFiltering
                    {
                        Keyword = "test", // Search by advertiser name or ID
                        AdvertiserStatus = new List<string> { "SHOW_ACCOUNT_STATUS_APPROVED" }
                    },
                    Page = 1,
                    PageSize = 10
                };

                var response = await _client.BcPayment.GetAdvertiserBalanceAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    balanceRequest);

                if (response.Data?.Data?.AdvertiserAccountList != null)
                {
                    Console.WriteLine($"Found {response.Data.Data.AdvertiserAccountList.Count} advertiser accounts:");
                    
                    foreach (var account in response.Data.Data.AdvertiserAccountList)
                    {
                        Console.WriteLine($"Advertiser ID: {account.AdvertiserId}");
                        Console.WriteLine($"Advertiser Name: {account.AdvertiserName}");
                        Console.WriteLine($"Account Balance: {account.AccountBalance} {account.Currency}");
                        Console.WriteLine($"Valid Cash Balance: {account.ValidCashBalance} {account.Currency}");
                        Console.WriteLine($"Budget Mode: {account.BudgetMode}");
                        Console.WriteLine($"Budget: {account.Budget}");
                        Console.WriteLine($"Budget Cost: {account.BudgetCost}");
                        Console.WriteLine($"Budget Remaining: {account.BudgetRemaining}");
                        Console.WriteLine($"Transferable Amount: {account.TransferableAmount}");
                        Console.WriteLine("---");
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting Business Center balance
        /// </summary>
        public async Task GetBcBalanceExample()
        {
            try
            {
                Console.WriteLine("Getting Business Center balance...");

                var balanceRequest = new GetBcBalanceRequest
                {
                    BcId = "your_bc_id_here"
                };

                var response = await _client.BcPayment.GetBcBalanceAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    balanceRequest);

                if (response.Data?.Data != null)
                {
                    var balance = response.Data.Data;
                    Console.WriteLine($"Business Center ID: {balance.BcId}");
                    Console.WriteLine($"Currency: {balance.Currency}");
                    Console.WriteLine($"Account Balance: {balance.AccountBalance}");
                    Console.WriteLine($"Valid Account Balance: {balance.ValidAccountBalance}");
                    Console.WriteLine($"Cash Balance: {balance.CashBalance}");
                    Console.WriteLine($"Valid Cash Balance: {balance.ValidCashBalance}");
                    Console.WriteLine($"Grant Balance: {balance.GrantBalance}");
                    Console.WriteLine($"Valid Grant Balance: {balance.ValidGrantBalance}");
                    Console.WriteLine($"Frozen Balance: {balance.FrozenBalance}");
                    Console.WriteLine($"Tax: {balance.Tax}");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting transaction records
        /// </summary>
        public async Task GetTransactionRecordsExample()
        {
            try
            {
                Console.WriteLine("Getting transaction records...");

                var transactionRequest = new GetBcAccountTransactionRequest
                {
                    BcId = "your_bc_id_here",
                    TransactionLevel = "ADVERTISER", // or "BC" or "PAYMENT_PORTFOLIO"
                    Filtering = new TransactionFiltering
                    {
                        StartTime = "2024-01-01 00:00:00",
                        EndTime = "2024-12-31 23:59:59",
                        TransactionTypes = new List<string> { "ADD_BALANCE", "BILL_PAYMENT" },
                        BillingTypes = new List<string> { "CASH", "CREDIT" }
                    },
                    Page = 1,
                    PageSize = 20
                };

                var response = await _client.BcPayment.GetBcAccountTransactionAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    transactionRequest);

                if (response.Data?.Data?.TransactionList != null)
                {
                    Console.WriteLine($"Found {response.Data.Data.TransactionList.Count} transaction records:");
                    
                    foreach (var transaction in response.Data.Data.TransactionList)
                    {
                        Console.WriteLine($"Transaction ID: {transaction.TransactionId}");
                        Console.WriteLine($"Account ID: {transaction.AccountId}");
                        Console.WriteLine($"Account Name: {transaction.AccountName}");
                        Console.WriteLine($"Amount: {transaction.Amount} {transaction.Currency}");
                        Console.WriteLine($"Transaction Type: {transaction.TransactionType}");
                        Console.WriteLine($"Billing Type: {transaction.BillingType}");
                        Console.WriteLine($"Create Time: {transaction.CreateTime}");
                        Console.WriteLine("---");
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting budget change history
        /// </summary>
        public async Task GetBudgetChangelogExample()
        {
            try
            {
                Console.WriteLine("Getting budget change history...");

                var changelogRequest = new GetBudgetChangelogRequest
                {
                    BcId = "your_bc_id_here",
                    AdvertiserId = "your_advertiser_id_here",
                    Filtering = new BudgetChangelogFiltering
                    {
                        StartDate = "2024-01-01",
                        EndDate = "2024-12-31"
                    },
                    Page = 1,
                    PageSize = 10
                };

                var response = await _client.BcPayment.GetBudgetChangelogAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    changelogRequest);

                if (response.Data?.Data?.ChangelogList != null)
                {
                    Console.WriteLine($"Found {response.Data.Data.ChangelogList.Count} budget changes:");
                    
                    foreach (var change in response.Data.Data.ChangelogList)
                    {
                        Console.WriteLine($"Operation Time: {change.OperationTime}");
                        Console.WriteLine($"Activity Type: {change.ActivityType}");
                        Console.WriteLine($"Previous Budget: {change.PreviousBudget} {change.Currency}");
                        Console.WriteLine($"Previous Budget Mode: {change.PreviousBudgetMode}");
                        Console.WriteLine($"Current Budget: {change.CurrentBudget} {change.Currency}");
                        Console.WriteLine($"Current Budget Mode: {change.CurrentBudgetMode}");
                        Console.WriteLine($"Operator: {change.OperatorName} ({change.OperatorId})");
                        Console.WriteLine("---");
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting cost records
        /// </summary>
        public async Task GetCostRecordsExample()
        {
            try
            {
                Console.WriteLine("Getting cost records...");

                var costRequest = new GetCostRecordsRequest
                {
                    BcId = "your_bc_id_here",
                    Filtering = new CostRecordsFiltering
                    {
                        StartDate = "2024-01-01",
                        EndDate = "2024-12-31",
                        Keyword = "test" // Search by advertiser name or ID
                    },
                    Page = 1,
                    PageSize = 20
                };

                var response = await _client.BcPayment.GetCostRecordsAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    costRequest);

                if (response.Data?.Data?.CostList != null)
                {
                    Console.WriteLine($"Found {response.Data.Data.CostList.Count} cost records:");
                    
                    foreach (var cost in response.Data.Data.CostList)
                    {
                        Console.WriteLine($"Advertiser ID: {cost.AdvertiserId}");
                        Console.WriteLine($"Advertiser Name: {cost.AdvertiserName}");
                        Console.WriteLine($"Total Amount: {cost.Amount} {cost.Currency}");
                        Console.WriteLine($"Cash Amount: {cost.CashAmount} {cost.Currency}");
                        Console.WriteLine($"Grant Amount: {cost.GrantAmount} {cost.Currency}");
                        Console.WriteLine($"Tax Amount: {cost.TaxAmount} {cost.Currency}");
                        Console.WriteLine("---");
                    }

                    if (response.Data.Data.TransactionSummary != null)
                    {
                        var summary = response.Data.Data.TransactionSummary;
                        Console.WriteLine("Business Center Summary:");
                        Console.WriteLine($"Total Amount: {summary.Amount} {summary.Currency}");
                        Console.WriteLine($"Cash Amount: {summary.CashAmount} {summary.Currency}");
                        Console.WriteLine($"Grant Amount: {summary.GrantAmount} {summary.Currency}");
                        Console.WriteLine($"Tax Amount: {summary.TaxAmount} {summary.Currency}");
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Run all examples
        /// </summary>
        public async Task RunAllExamples()
        {
            await ProcessPaymentExample();
            Console.WriteLine();
            
            await GetAdvertiserBalanceExample();
            Console.WriteLine();
            
            await GetBcBalanceExample();
            Console.WriteLine();
            
            await GetTransactionRecordsExample();
            Console.WriteLine();
            
            await GetBudgetChangelogExample();
            Console.WriteLine();
            
            await GetCostRecordsExample();
        }
    }
}
