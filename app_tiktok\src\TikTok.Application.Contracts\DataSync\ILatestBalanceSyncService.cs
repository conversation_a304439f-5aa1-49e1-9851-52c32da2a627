using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu ngân sách mới nhất (Latest Balance)
    /// Chỉ lưu trữ dữ liệu mới nhất, cập nhật khi có thay đổi
    /// </summary>
    public interface ILatestBalanceSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ ngân sách Business Center mới nhất theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<LatestBalanceSyncResult> SyncBcLatestBalanceAsync(string bcId);

        /// <summary>
        /// Đồng bộ ngân sách AdAccount mới nhất theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<LatestBalanceSyncResult> SyncAdAccountLatestBalanceAsync(string bcId);

        /// <summary>
        /// Đồng bộ tất cả ngân sách mới nhất (BC và AdAccount) theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<LatestBalanceSyncResult> SyncAllLatestBalancesAsync(string bcId);

        /// <summary>
        /// Đồng bộ tất cả ngân sách mới nhất cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<LatestBalanceSyncResult> SyncAllLatestBalancesForAllBcsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu ngân sách mới nhất
    /// </summary>
    public class LatestBalanceSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số AdAccount đã đồng bộ
        /// </summary>
        public int AdAccountCount { get; set; }

        /// <summary>
        /// Danh sách AdvertiserIds đã đồng bộ thành công
        /// </summary>
        public List<string> AdvertiserIds { get; set; } = new List<string>();

        /// <summary>
        /// ID của Business Center đã đồng bộ
        /// </summary>
        public string BcId { get; set; } = string.Empty;
    }
}