/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Net.Http;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Catalog Products operations
    /// </summary>
    public class CatalogProductsApi : ICatalogProductsApi
    {
        private readonly IApiClient _apiClient;

        /// <summary>
        /// Initializes a new instance of the CatalogProductsApi class
        /// </summary>
        /// <param name="apiClient">The API client</param>
        public CatalogProductsApi(IApiClient apiClient)
        {
            _apiClient = apiClient;
        }

        /// <summary>
        /// Upload products via a file URL
        /// </summary>
        /// <param name="request">Product file upload request</param>
        /// <returns>Product file upload response</returns>
        //public async Task<ProductFileUploadResponse> UploadProductsViaFileAsync(ProductFileUploadBody request)
        //{
        //    return await _apiClient.CallApiAsync<ProductFileUploadResponse>(
        //        CatalogProductsEndpoints.UploadProductsViaFile,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null);
        //}

        /// <summary>
        /// Upload products via JSON schema
        /// </summary>
        /// <param name="request">Product upload request</param>
        /// <returns>Product file upload response</returns>
        //public async Task<ProductFileUploadResponse> UploadProductsViaJsonAsync(ProductUploadBody request)
        //{
        //    return await _apiClient.CallApiAsync<ProductFileUploadResponse>(
        //        CatalogProductsEndpoints.UploadProductsViaJson,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null);
        //}

        /// <summary>
        /// Update products
        /// </summary>
        /// <param name="request">Product update request</param>
        /// <returns>Product file upload response</returns>
        //public async Task<ProductFileUploadResponse> UpdateProductsAsync(ProductUpdateBody request)
        //{
        //    return await _apiClient.CallApiAsync<ProductFileUploadResponse>(
        //        CatalogProductsEndpoints.UpdateProducts,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null);
        //}

        /// <summary>
        /// Delete products
        /// </summary>
        /// <param name="request">Product delete request</param>
        /// <returns>Product file upload response</returns>
        //public async Task<ProductFileUploadResponse> DeleteProductsAsync(ProductDeleteBody request)
        //{
        //    return await _apiClient.CallApiAsync<ProductFileUploadResponse>(
        //        CatalogProductsEndpoints.DeleteProducts,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null);
        //}

        /// <summary>
        /// Get products
        /// </summary>
        /// <param name="request">Product get request</param>
        /// <returns>Product get response</returns>
        public async Task<ProductGetResponse> GetProductsAsync(ProductGetRequest request)
        {
            return await _apiClient.CallApiAsync<ProductGetResponse>(
                CatalogProductsEndpoints.GetProducts,
                HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null);
        }

        /// <summary>
        /// Get product handling log
        /// </summary>
        /// <param name="request">Product log request</param>
        /// <returns>Product log response</returns>
        public async Task<ProductLogResponse> GetProductLogAsync(ProductLogRequest request)
        {
            return await _apiClient.CallApiAsync<ProductLogResponse>(
                CatalogProductsEndpoints.GetProductLog,
                HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null);
        }
    }
}
