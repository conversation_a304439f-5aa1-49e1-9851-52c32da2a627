using System;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTok.ReportIntegratedCampaigns;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.ReportIntegratedCampaigns
{
    /// <summary>
    /// Service implementation cho báo cáo tích hợp Campaign
    /// </summary>
    public class ReportIntegratedCampaignAppService :
        CrudAppService<
            RawReportIntegratedCampaignEntity,
            ReportIntegratedCampaignDto,
            Guid,
            GetReportIntegratedCampaignListDto,
            CreateReportIntegratedCampaignDto,
            UpdateReportIntegratedCampaignDto>,
        IReportIntegratedCampaignAppService
    {
        private readonly IRawReportIntegratedCampaignRepository _reportIntegratedCampaignRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="repository">Repository</param>
        /// <param name="reportIntegratedCampaignRepository">Report Integrated Campaign Repository</param>
        public ReportIntegratedCampaignAppService(
            IRepository<RawReportIntegratedCampaignEntity, Guid> repository,
            IRawReportIntegratedCampaignRepository reportIntegratedCampaignRepository) : base(repository)
        {
            _reportIntegratedCampaignRepository = reportIntegratedCampaignRepository;

            // Cấu hình permission
            GetPolicyName = TikTokPermissions.ReportIntegratedCampaigns.Default;
            GetListPolicyName = TikTokPermissions.ReportIntegratedCampaigns.Default;
            CreatePolicyName = TikTokPermissions.ReportIntegratedCampaigns.Create;
            UpdatePolicyName = TikTokPermissions.ReportIntegratedCampaigns.Edit;
            DeletePolicyName = TikTokPermissions.ReportIntegratedCampaigns.Delete;
        }

        /// <summary>
        /// Ghi đè phương thức GetListAsync để sử dụng repository
        /// </summary>
        /// <param name="input">Input để lấy danh sách</param>
        /// <returns>Danh sách báo cáo tích hợp Campaign</returns>
        public override async Task<PagedResultDto<ReportIntegratedCampaignDto>> GetListAsync(GetReportIntegratedCampaignListDto input)
        {
            var totalCount = await _reportIntegratedCampaignRepository.GetCountAsync(
                input.AdvertiserId,
                input.CampaignId,
                input.StartDate,
                input.EndDate,
                input.Filter);

            var items = await _reportIntegratedCampaignRepository.GetListAsync(
                input.AdvertiserId,
                input.CampaignId,
                input.StartDate,
                input.EndDate,
                input.Sorting,
                input.MaxResultCount,
                input.SkipCount,
                input.Filter);

            return new PagedResultDto<ReportIntegratedCampaignDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<RawReportIntegratedCampaignEntity[], ReportIntegratedCampaignDto[]>(items.ToArray())
            };
        }

        /// <summary>
        /// Lấy báo cáo theo Campaign ID và ngày
        /// </summary>
        /// <param name="campaignId">ID chiến dịch</param>
        /// <param name="date">Ngày báo cáo</param>
        /// <returns>Báo cáo tích hợp Campaign</returns>
        public async Task<ReportIntegratedCampaignDto?> GetByCampaignIdAndDateAsync(string campaignId, DateTime date)
        {
            var entity = await _reportIntegratedCampaignRepository.GetByCampaignIdAndDateAsync(campaignId, date);
            return entity != null ? ObjectMapper.Map<RawReportIntegratedCampaignEntity, ReportIntegratedCampaignDto>(entity) : null;
        }

        /// <summary>
        /// Lấy danh sách báo cáo theo Campaign ID
        /// </summary>
        /// <param name="campaignId">ID chiến dịch</param>
        /// <returns>Danh sách báo cáo tích hợp Campaign</returns>
        public async Task<PagedResultDto<ReportIntegratedCampaignDto>> GetByCampaignIdAsync(string campaignId)
        {
            var items = await _reportIntegratedCampaignRepository.GetByCampaignIdAsync(campaignId);
            var totalCount = items.Count;

            return new PagedResultDto<ReportIntegratedCampaignDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<RawReportIntegratedCampaignEntity[], ReportIntegratedCampaignDto[]>(items.ToArray())
            };
        }
    }
} 