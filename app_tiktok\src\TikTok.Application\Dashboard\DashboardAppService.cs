using System;
using System.Threading.Tasks;
using TikTok.Dashboard;
using TikTok.Repositories;
using Volo.Abp.Application.Services;

namespace TikTok.Dashboard
{
    /// <summary>
    /// Implementation cho Dashboard service
    /// </summary>
    public class DashboardAppService : ApplicationService, IDashboardAppService
    {
        private readonly IBusinessCenterRepository _businessCenterRepository;
        private readonly IAdAccountRepository _adAccountRepository;
        private readonly ICampaignRepository _campaignRepository;
        private readonly ICustomerRepository _customerRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="businessCenterRepository">Business Center Repository</param>
        /// <param name="adAccountRepository">Ad Account Repository</param>
        /// <param name="campaignRepository">Campaign Repository</param>
        /// <param name="customerRepository">Customer Repository</param>
        public DashboardAppService(
            IBusinessCenterRepository businessCenterRepository,
            IAdAccountRepository adAccountRepository,
            ICampaignRepository campaignRepository,
            ICustomerRepository customerRepository)
        {
            _businessCenterRepository = businessCenterRepository;
            _adAccountRepository = adAccountRepository;
            _campaignRepository = campaignRepository;
            _customerRepository = customerRepository;
        }

        /// <summary>
        /// Lấy thống kê overview cho dashboard
        /// </summary>
        /// <returns>Thống kê overview</returns>
        public async Task<DashboardOverviewDto> GetOverviewStatisticsAsync()
        {
            // Lấy số lượng từ các repository
            var businessCenterCount = await _businessCenterRepository.GetCountAsync();
            var adAccountCount = await _adAccountRepository.GetCountAsync();
            var campaignCount = await _campaignRepository.GetCountAsync();
            var customerCount = await _customerRepository.GetCountAsync();

            return new DashboardOverviewDto
            {
                BusinessCenterCount = businessCenterCount,
                AdAccountCount = adAccountCount,
                CampaignCount = campaignCount,
                CustomerCount = customerCount,
                LastUpdated = DateTime.UtcNow
            };
        }
    }
}
