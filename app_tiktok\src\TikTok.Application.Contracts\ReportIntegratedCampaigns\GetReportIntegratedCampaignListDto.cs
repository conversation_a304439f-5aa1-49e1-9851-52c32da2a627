using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TikTok.ReportIntegratedCampaigns
{
    /// <summary>
    /// DTO cho việc lấy danh sách báo cáo tích hợp Campaign
    /// </summary>
    public class GetReportIntegratedCampaignListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// ID tài khoản nhà quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// ID chiến dịch
        /// </summary>
        [StringLength(100)]
        public string? CampaignId { get; set; }

        /// <summary>
        /// Ngày bắt đầu
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Ngày kết thúc
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// <PERSON><PERSON> lọc tìm kiếm
        /// </summary>
        [StringLength(255)]
        public string? Filter { get; set; }
    }
} 