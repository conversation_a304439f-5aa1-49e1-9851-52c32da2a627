/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the BC Member API
    /// </summary>
    public class BcMemberApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public BcMemberApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example of getting BC members
        /// </summary>
        public async Task GetMembersExample()
        {
            try
            {
                var request = new GetBcMembersRequest
                {
                    BcId = "your-business-center-id",
                    Page = 1,
                    PageSize = 20,
                    Filtering = new BcMemberFiltering
                    {
                        Keyword = "john",
                        UserRole = "ADMIN",
                        RelationStatus = "BOUND"
                    }
                };

                var response = await _client.BcMember.GetMembersAsync("your-access-token", request);

                Console.WriteLine($"Request ID: {response.RequestId}");
                Console.WriteLine($"Total members: {response.Data?.PageInfo?.TotalNumber}");

                if (response.Data?.List != null)
                {
                    foreach (var member in response.Data.List)
                    {
                        Console.WriteLine($"Member: {member.UserName} ({member.UserEmail})");
                        Console.WriteLine($"  Role: {member.UserRole}");
                        Console.WriteLine($"  Status: {member.RelationStatus}");
                        Console.WriteLine($"  Finance Role: {member.ExtUserRole?.FinanceRole}");
                        Console.WriteLine();
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of inviting members to BC
        /// </summary>
        public async Task InviteMembersExample()
        {
            try
            {
                var request = new InviteBcMembersRequest
                {
                    BcId = "your-business-center-id",
                    Emails = new List<string>
                    {
                        "<EMAIL>",
                        "<EMAIL>"
                    },
                    UserRole = "STANDARD",
                    AssetIds = new List<long> { 123456789, 987654321 },
                    AdvertiserRole = "OPERATOR",
                    ExtUserRole = new ExtendedUserRole
                    {
                        FinanceRole = "ANALYST"
                    }
                };

                var response = await _client.BcMember.InviteMembersAsync("your-access-token", request);

                Console.WriteLine($"Members invited successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of updating a BC member
        /// </summary>
        public async Task UpdateMemberExample()
        {
            try
            {
                var request = new UpdateBcMemberRequest
                {
                    BcId = "your-business-center-id",
                    UserId = "user-id-to-update",
                    UserRole = "ADMIN",
                    UserName = "Updated User Name",
                    ExtUserRole = new ExtendedUserRole
                    {
                        FinanceRole = "MANAGER"
                    }
                };

                var response = await _client.BcMember.UpdateMemberAsync("your-access-token", request);

                Console.WriteLine($"Member updated successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of deleting a BC member by user ID
        /// </summary>
        public async Task DeleteMemberByUserIdExample()
        {
            try
            {
                var request = new DeleteBcMemberRequest
                {
                    BcId = "your-business-center-id",
                    UserId = "user-id-to-delete"
                };

                var response = await _client.BcMember.DeleteMemberAsync("your-access-token", request);

                Console.WriteLine($"Member deleted successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of deleting a BC member by email (for pending invitations)
        /// </summary>
        public async Task DeleteMemberByEmailExample()
        {
            try
            {
                var request = new DeleteBcMemberRequest
                {
                    BcId = "your-business-center-id",
                    UserEmail = "<EMAIL>"
                };

                var response = await _client.BcMember.DeleteMemberAsync("your-access-token", request);

                Console.WriteLine($"Member deleted successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of comprehensive BC member management workflow
        /// </summary>
        public async Task ComprehensiveWorkflowExample()
        {
            try
            {
                Console.WriteLine("=== BC Member Management Workflow ===");

                // 1. Get current members
                Console.WriteLine("\n1. Getting current members...");
                var getMembersRequest = new GetBcMembersRequest
                {
                    BcId = "your-business-center-id",
                    Page = 1,
                    PageSize = 10
                };

                var membersResponse = await _client.BcMember.GetMembersAsync("your-access-token", getMembersRequest);
                Console.WriteLine($"Found {membersResponse.Data?.List?.Count ?? 0} members");

                // 2. Invite new members
                Console.WriteLine("\n2. Inviting new members...");
                var inviteRequest = new InviteBcMembersRequest
                {
                    BcId = "your-business-center-id",
                    Emails = new List<string> { "<EMAIL>" },
                    UserRole = "STANDARD"
                };

                await _client.BcMember.InviteMembersAsync("your-access-token", inviteRequest);
                Console.WriteLine("New members invited successfully");

                // 3. Update existing member (if any)
                if (membersResponse.Data?.List?.Count > 0)
                {
                    var firstMember = membersResponse.Data.List[0];
                    if (!string.IsNullOrEmpty(firstMember.UserId))
                    {
                        Console.WriteLine("\n3. Updating existing member...");
                        var updateRequest = new UpdateBcMemberRequest
                        {
                            BcId = "your-business-center-id",
                            UserId = firstMember.UserId,
                            UserName = "Updated Name"
                        };

                        await _client.BcMember.UpdateMemberAsync("your-access-token", updateRequest);
                        Console.WriteLine("Member updated successfully");
                    }
                }

                Console.WriteLine("\n=== Workflow completed successfully ===");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose the client
        /// </summary>
        public void Dispose()
        {
            _client?.Dispose();
        }
    }
}
