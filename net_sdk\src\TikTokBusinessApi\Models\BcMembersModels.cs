/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Get Members Models

    /// <summary>
    /// Request for getting members of a Business Center
    /// </summary>
    public class GetMembersRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. The maximum page size is 20.
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public GetMembersFiltering? Filtering { get; set; }
    }

    /// <summary>
    /// Filtering for get members requests
    /// </summary>
    public class GetMembersFiltering
    {
        /// <summary>
        /// Keyword to filter by
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }

        /// <summary>
        /// Basic roles within Business Center. Enum values: ADMIN, STANDARD
        /// </summary>
        [JsonPropertyName("user_role")]
        public string? UserRole { get; set; }

        /// <summary>
        /// User's relation to the Business Center. Enum values: BOUND, PENDING, REJECTED
        /// </summary>
        [JsonPropertyName("relation_status")]
        public string? RelationStatus { get; set; }
    }

    /// <summary>
    /// Response for getting members
    /// </summary>
    public class GetMembersResponse
    {
        /// <summary>
        /// Member list
        /// </summary>
        [JsonPropertyName("list")]
        public List<BCMemberInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// BC Member information
    /// </summary>
    public class BCMemberInfo
    {
        /// <summary>
        /// User ID
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// User name
        /// </summary>
        [JsonPropertyName("user_name")]
        public string? UserName { get; set; }

        /// <summary>
        /// Basic roles within Business Center
        /// </summary>
        [JsonPropertyName("user_role")]
        public string? UserRole { get; set; }

        /// <summary>
        /// User's relation to the Business Center
        /// </summary>
        [JsonPropertyName("relation_status")]
        public string? RelationStatus { get; set; }

        /// <summary>
        /// User email address
        /// </summary>
        [JsonPropertyName("user_email")]
        public string? UserEmail { get; set; }

        /// <summary>
        /// Advanced role of the user in the Business Center other than basic role
        /// </summary>
        [JsonPropertyName("ext_user_role")]
        public ExtUserRole? ExtUserRole { get; set; }
    }

    /// <summary>
    /// Advanced role of the user in the Business Center
    /// </summary>
    public class ExtUserRole
    {
        /// <summary>
        /// User finance role. Enum values: MANAGER, ANALYST
        /// </summary>
        [JsonPropertyName("finance_role")]
        public string? FinanceRole { get; set; }
    }

    #endregion

    #region Invite Members Models

    /// <summary>
    /// Request for inviting members to a Business Center
    /// </summary>
    public class InviteMembersRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// List of emails. User emails that have been invited earlier will be ignored. (Required)
        /// </summary>
        [JsonPropertyName("emails")]
        public List<string> Emails { get; set; } = new List<string>();

        /// <summary>
        /// Basic roles within Business Center. Enum values: ADMIN, STANDARD
        /// </summary>
        [JsonPropertyName("user_role")]
        public string? UserRole { get; set; }

        /// <summary>
        /// List of ad account IDs assigned to the invited members. Maximum number is 50.
        /// </summary>
        [JsonPropertyName("asset_ids")]
        public List<long>? AssetIds { get; set; }

        /// <summary>
        /// Ad account role assigned to the members invited
        /// </summary>
        [JsonPropertyName("advertiser_role")]
        public string? AdvertiserRole { get; set; }

        /// <summary>
        /// Advanced role of the user in the Business Center other than basic role
        /// </summary>
        [JsonPropertyName("ext_user_role")]
        public ExtUserRole? ExtUserRole { get; set; }
    }

    /// <summary>
    /// Response for inviting members
    /// </summary>
    public class InviteMembersResponse
    {
        // Empty response body according to documentation
    }

    #endregion

    #region Update Member Models

    /// <summary>
    /// Request for updating a member's info in a Business Center
    /// </summary>
    public class UpdateMemberRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// User ID of the Business Center's member (Required)
        /// </summary>
        [JsonPropertyName("user_id")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// New basic role for the user in this Business Center. Enum values: ADMIN, STANDARD
        /// </summary>
        [JsonPropertyName("user_role")]
        public string? UserRole { get; set; }

        /// <summary>
        /// New name of the user
        /// </summary>
        [JsonPropertyName("user_name")]
        public string? UserName { get; set; }

        /// <summary>
        /// Advanced role of the user in the Business Center other than basic role
        /// </summary>
        [JsonPropertyName("ext_user_role")]
        public ExtUserRole? ExtUserRole { get; set; }
    }

    /// <summary>
    /// Response for updating member
    /// </summary>
    public class UpdateMemberResponse
    {
        // Empty response body according to documentation
    }

    #endregion

    #region Delete Member Models

    /// <summary>
    /// Request for deleting a member from a Business Center
    /// </summary>
    public class DeleteMemberRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// User ID. For members that have accepted the invitation to join the Business Center, delete by user_id.
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// User email. For members that haven't accepted the invitation, delete by user_email.
        /// </summary>
        [JsonPropertyName("user_email")]
        public string? UserEmail { get; set; }
    }

    /// <summary>
    /// Response for deleting member
    /// </summary>
    public class DeleteMemberResponse
    {
        // Empty response body according to documentation
    }

    #endregion
}
