/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API BC Management operations
    /// </summary>
    public class BcManagementApi : IBcManagementApi
    {
        private readonly IApiClient _apiClient;

        /// <summary>
        /// Initializes a new instance of the BcManagementApi class
        /// </summary>
        /// <param name="apiClient">API client for making requests</param>
        public BcManagementApi(IApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        }

        /// <inheritdoc />
        public async Task<TikTokApiResponse<GetBusinessCentersResponse>> GetBusinessCentersAsync(
            string? bcId = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default)
        {
            if (page < 1)
                throw new ArgumentException("Page must be greater than 0", nameof(page));

            if (pageSize < 1 || pageSize > 50)
                throw new ArgumentException("Page size must be between 1 and 50", nameof(pageSize));

            // create query params
            var queryParams = new Dictionary<string, string>()
            {
                ["page"] = page.ToString(),
                ["page_size"] = pageSize.ToString()
            };
            if (!string.IsNullOrWhiteSpace(bcId))
                queryParams["bc_id"] = bcId;

            return await _apiClient.CallApiAsync<TikTokApiResponse<GetBusinessCentersResponse>>(
                ApiEndpoints.BcManagement.GetBusinessCenters,
                System.Net.Http.HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<TikTokApiResponse<GetBusinessCenterChangelogResponse>> GetBusinessCenterChangelogAsync(
            GetBusinessCenterChangelogRequest request,
            CancellationToken cancellationToken = default)
        {
            ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            if (request.Page.HasValue && request.Page.Value < 1)
                throw new ArgumentException("Page must be greater than 0", nameof(request));

            if (request.PageSize.HasValue && (request.PageSize.Value < 1 || request.PageSize.Value > 50))
                throw new ArgumentException("Page size must be between 1 and 50", nameof(request));

            // Validate date range if both dates are provided
            if (request.Filtering?.StartDate != null && request.Filtering?.EndDate != null)
            {
                if (DateTime.TryParse(request.Filtering.StartDate, out var startDate) &&
                    DateTime.TryParse(request.Filtering.EndDate, out var endDate))
                {
                    if (startDate > endDate)
                        throw new ArgumentException("Start date cannot be after end date", nameof(request));
                }
            }

            return await _apiClient.CallApiAsync<TikTokApiResponse<GetBusinessCenterChangelogResponse>>(
                $"{ApiEndpoints.BcManagement.GetBusinessCenterChangelog}",
                System.Net.Http.HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
