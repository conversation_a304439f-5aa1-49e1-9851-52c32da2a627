using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.AdAccounts
{
    /// <summary>
    /// DTO để lấy danh sách tài khoản quảng cáo
    /// </summary>
    public class GetAdAccountListDto : PagedAndSortedResultRequestDto
    {

        public string? FilterText { get; set; }

        /// <summary>
        /// Từ khóa tìm kiếm khách hàng
        /// </summary>
        public string? CustomerFilterText { get; set; }

        /// <summary>
        /// ID duy nhất của tài khoản quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// ID của Business Center sở hữu tài khoản
        /// </summary>
        [StringLength(100)]
        public string? OwnerBcId { get; set; }

        /// <summary>
        /// Trạng thái tài khoản (đơn lẻ)
        /// </summary>
        public AdAccountStatus? Status { get; set; }

        /// <summary>
        /// Danh sách trạng thái tài khoản (nhiều)
        /// </summary>
        public List<AdAccountStatus>? Statuses { get; set; }

        /// <summary>
        /// Vai trò tài khoản
        /// </summary>
        public AdAccountRole? Role { get; set; }

        /// <summary>
        /// Tên tài khoản quảng cáo
        /// </summary>
        [StringLength(255)]
        public string? Name { get; set; }

        /// <summary>
        /// Tên công ty của tài khoản
        /// </summary>
        [StringLength(255)]
        public string? Company { get; set; }

        /// <summary>
        /// Mã danh mục ngành nghề
        /// </summary>
        [StringLength(50)]
        public string? Industry { get; set; }

        /// <summary>
        /// Địa chỉ tài khoản
        /// </summary>
        [StringLength(500)]
        public string? Address { get; set; }

        /// <summary>
        /// Mã quốc gia đăng ký (ví dụ: US, CN)
        /// </summary>
        [StringLength(10)]
        public string? Country { get; set; }

        /// <summary>
        /// Loại tài khoản
        /// </summary>
        public AdAccountType? AdvertiserAccountType { get; set; }

        /// <summary>
        /// Loại tiền tệ theo mã ISO 4217 (ví dụ: USD, EUR)
        /// </summary>
        [StringLength(10)]
        public string? Currency { get; set; }

        /// <summary>
        /// Mã ngôn ngữ sử dụng (ví dụ: en, zh)
        /// </summary>
        [StringLength(10)]
        public string? Language { get; set; }

        /// <summary>
        /// Số giấy phép kinh doanh
        /// </summary>
        [StringLength(100)]
        public string? LicenseNo { get; set; }

        /// <summary>
        /// Thời gian tạo tài khoản từ
        /// </summary>
        public DateTime? CreateTimeFrom { get; set; }

        /// <summary>
        /// Thời gian tạo tài khoản đến
        /// </summary>
        public DateTime? CreateTimeTo { get; set; }

        /// <summary>
        /// Số dư khả dụng từ
        /// </summary>
        public decimal? BalanceFrom { get; set; }

        /// <summary>
        /// Số dư khả dụng đến
        /// </summary>
        public decimal? BalanceTo { get; set; }
    }
} 