using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu báo cáo tích hợp Campaign
    /// </summary>
    public class ReportIntegratedCampaignSyncApplicationAppService : ApplicationService, IReportIntegratedCampaignSyncApplicationAppService
    {
        private readonly IReportIntegratedCampaignSyncService _reportIntegratedCampaignSyncService;

        public ReportIntegratedCampaignSyncApplicationAppService(IReportIntegratedCampaignSyncService reportIntegratedCampaignSyncService)
        {
            _reportIntegratedCampaignSyncService = reportIntegratedCampaignSyncService;
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp Campaign theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> b<PERSON><PERSON> đầ<PERSON> (t<PERSON><PERSON> chọn)</param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedCampaignSyncResult> SyncReportIntegratedCampaignAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _reportIntegratedCampaignSyncService.SyncReportIntegratedCampaignAsync(bcId, startDate, endDate);
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedCampaignSyncResult> SyncAllReportIntegratedCampaignForAllBcsAsync()
        {
            return await _reportIntegratedCampaignSyncService.SyncAllReportIntegratedCampaignForAllBcsAsync();
        }
    }
}