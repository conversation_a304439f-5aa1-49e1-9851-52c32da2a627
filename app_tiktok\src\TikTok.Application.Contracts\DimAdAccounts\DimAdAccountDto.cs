﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace TikTok.DimAdAccounts
{
    public class DimAdAccountDto: AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID tài khoản quảng cáo từ TikTok
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// Tên tài khoản quảng cáo
        /// </summary>
        public string AdvertiserName { get; set; }

        /// <summary>
        /// Loại tiền tệ sử dụng
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Múi giờ tài khoản
        /// </summary>
        public string Timezone { get; set; }

        /// <summary>
        /// Mã quốc gia
        /// </summary>
        public string Country { get; set; }

        /// <summary>
        /// Trạng thái tài khoản
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Tên công ty
        /// </summary>
        [StringLength(255)]
        public string? Company { get; set; }

        /// <summary>
        /// ID Business Center sở hữu
        /// </summary>
        public string OwnerBcId { get; set; }

        /// <summary>
        /// Tài khoản còn hoạt động
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Ngày bắt đầu hiệu lực (SCD2)
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Ngày hết hiệu lực (SCD2)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// Bản ghi hiện tại (SCD2)
        /// </summary>
        public bool IsCurrent { get; set; }

        /// <summary>
        /// Phiên bản bản ghi (SCD2)
        /// </summary>
        public int RowVersion { get; set; }
    }
}
