using System;
using System.Threading.Tasks;
using TikTok.ReportIntegratedCampaigns;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.ReportIntegratedCampaigns
{
    /// <summary>
    /// Interface cho App Service báo cáo tích hợp Campaign
    /// </summary>
    public interface IReportIntegratedCampaignAppService :
        ICrudAppService<
            ReportIntegratedCampaignDto,
            Guid,
            GetReportIntegratedCampaignListDto,
            CreateReportIntegratedCampaignDto,
            UpdateReportIntegratedCampaignDto>
    {
        /// <summary>
        /// Lấy báo cáo theo Campaign ID và ngày
        /// </summary>
        /// <param name="campaignId">ID chiến dịch</param>
        /// <param name="date">Ngày báo cáo</param>
        /// <returns>Báo cáo tích hợp Campaign</returns>
        Task<ReportIntegratedCampaignDto?> GetByCampaignIdAndDateAsync(string campaignId, DateTime date);

        /// <summary>
        /// Lấy danh sách báo cáo theo Campaign ID
        /// </summary>
        /// <param name="campaignId">ID chiến dịch</param>
        /// <returns>Danh sách báo cáo tích hợp Campaign</returns>
        Task<PagedResultDto<ReportIntegratedCampaignDto>> GetByCampaignIdAsync(string campaignId);
    }
} 