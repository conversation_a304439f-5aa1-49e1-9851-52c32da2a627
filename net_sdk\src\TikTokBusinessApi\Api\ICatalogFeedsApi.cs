/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Catalog Feeds operations
    /// </summary>
    public interface ICatalogFeedsApi
    {
        /// <summary>
        /// Create a catalog feed
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing feed creation parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created feed information</returns>
        //Task<Response<CatalogFeedResponse>> CreateFeedAsync(
        //    string accessToken,
        //    CatalogFeedCreateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get catalog feeds
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="feedId">Feed ID (optional - if not specified, all feeds for the catalog will be returned)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing feed information</returns>
        Task<Response<CatalogFeedsGetResponse>> GetFeedsAsync(
            string accessToken,
            string bcId,
            string catalogId,
            string? feedId = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Update a catalog feed
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing feed update parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated feed information</returns>
        //Task<Response<CatalogFeedResponse>> UpdateFeedAsync(
        //    string accessToken,
        //    CatalogFeedUpdateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a catalog feed
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing feed deletion parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deleted feed ID</returns>
        //Task<Response<CatalogFeedDeleteResponse>> DeleteFeedAsync(
        //    string accessToken,
        //    CatalogFeedDeleteBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the log of a catalog feed
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="feedId">Feed ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing feed log information</returns>
        Task<Response<CatalogFeedLogResponse>> GetFeedLogAsync(
            string accessToken,
            string bcId,
            string catalogId,
            string feedId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Update the schedule status of a catalog feed
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing schedule status update parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success</returns>
        //Task<Response<object>> UpdateFeedScheduleStatusAsync(
        //    string accessToken,
        //    CatalogFeedScheduleStatusUpdateBody body,
        //    CancellationToken cancellationToken = default);
    }
}
