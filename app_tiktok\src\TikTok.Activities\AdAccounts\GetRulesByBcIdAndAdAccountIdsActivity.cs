using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using TikTok.Application.Contracts.Rules;

namespace TikTok.Activities.AdAccounts
{
    [Activity(Category = "Quy tắc", DisplayName = "L<PERSON>y danh sách quy tắc theo BcId và AdAccountIds", Outcomes = new[] { OutcomeNames.Done })]
    public class GetRulesByBcIdAndAdAccountIdsActivity : UnitOfWorkActivity
    {
        private readonly IRuleAdAccountService _ruleAdAccountService;

        public GetRulesByBcIdAndAdAccountIdsActivity(
            IRuleAdAccountService ruleAdAccountService)
        {
            _ruleAdAccountService = ruleAdAccountService;
        }

        [ActivityInput(Hint = "Business Center ID", SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public string BcId { get; set; }

        [ActivityInput(Hint = "Danh sách Advertiser IDs", SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public List<string> AdvertiserIds { get; set; } = new List<string>();

        [ActivityOutput(Hint = "Danh sách quy tắc")]
        public List<RuleAdAccountFlatDto> Rules { get; set; } = new List<RuleAdAccountFlatDto>();

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            Rules = await _ruleAdAccountService.GetRulesByBcIdAndAdvertiserIdsAsync(BcId, AdvertiserIds);
            return Done();
        }
    }
}
