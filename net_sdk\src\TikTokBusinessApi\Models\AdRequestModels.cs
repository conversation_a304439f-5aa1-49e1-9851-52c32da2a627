/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request model for getting ads
    /// </summary>
    public class AdGetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Fields to return in the response
        /// </summary>
        [JsonPropertyName("fields")]
        public List<string>? Fields { get; set; }

        /// <summary>
        /// Field types to exclude from response
        /// </summary>
        [JsonPropertyName("exclude_field_types_in_response")]
        public List<string>? ExcludeFieldTypesInResponse { get; set; }

        /// <summary>
        /// Filtering criteria for ads
        /// </summary>
        [JsonPropertyName("filtering")]
        public AdGetFiltering? Filtering { get; set; }

        /// <summary>
        /// Page number for pagination (default: 1)
        /// </summary>
        [JsonPropertyName("page")]
        public int Page { get; set; } = 1;

        /// <summary>
        /// Page size for pagination (default: 10, max: 1000)
        /// </summary>
        [JsonPropertyName("page_size")]
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// Request model for creating ads
    /// </summary>
    public class AdCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        [Required]
        public string AdgroupId { get; set; } = string.Empty;

        /// <summary>
        /// List of creatives to create
        /// </summary>
        [JsonPropertyName("creatives")]
        [Required]
        public List<AdCreateCreative> Creatives { get; set; } = new();
    }

    /// <summary>
    /// Request model for updating ads
    /// </summary>
    public class AdUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        [Required]
        public string AdgroupId { get; set; } = string.Empty;

        /// <summary>
        /// Whether to use incremental update mode
        /// </summary>
        [JsonPropertyName("patch_update")]
        public bool? PatchUpdate { get; set; }

        /// <summary>
        /// List of creatives to update
        /// </summary>
        [JsonPropertyName("creatives")]
        [Required]
        public List<AdUpdateCreative> Creatives { get; set; } = new();
    }

    /// <summary>
    /// Request model for updating ad status
    /// </summary>
    public class AdStatusUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// List of ad IDs to update (conditional - either this or AcoAdIds must be set)
        /// </summary>
        [JsonPropertyName("ad_ids")]
        public List<string>? AdIds { get; set; }

        /// <summary>
        /// List of ACO ad IDs to update (conditional - either this or AdIds must be set)
        /// </summary>
        [JsonPropertyName("aco_ad_ids")]
        public List<string>? AcoAdIds { get; set; }

        /// <summary>
        /// Operation to perform: ENABLE, DISABLE, or DELETE
        /// </summary>
        [JsonPropertyName("operation_status")]
        [Required]
        public string OperationStatus { get; set; } = string.Empty;
    }
}
