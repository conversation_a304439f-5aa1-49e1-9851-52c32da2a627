param (
  [string]$modulePath = '',
  [string]$webAppFolder = '',
  [string]$reactAppFolder = '',
  [string]$image = '',
  [string]$output_root = '',
  [string]$artifact_name = '',
  [string]$version = 'latest',
  [string]$outputExtension = 'tar',
  [string]$migratorProject = '',
  [string]$nugetConfig = 'NuGet.Config'
)

$ErrorActionPreference = 'Stop'

function Write-Section($text) {
  Write-Host ""
  Write-Host "==================== $text ====================" -ForegroundColor Green
}

# Xác định root repo (script nằm ở docker/app-tiktok)
$scriptDir   = Split-Path -Parent $MyInvocation.MyCommand.Path
$repoRoot    = Join-Path $scriptDir "../../" | Resolve-Path
$moduleRoot  = Join-Path $repoRoot $modulePath

# Tạo thư mục output chuẩn
$imagesOut   = Join-Path $output_root "images"
$migratorOut = Join-Path $output_root "migrator"
New-Item -ItemType Directory -Force -Path $imagesOut   | Out-Null
New-Item -ItemType Directory -Force -Path $migratorOut | Out-Null

Write-Section "Restore .NET (solution/module)"
Push-Location $moduleRoot
if (Test-Path $nugetConfig) {
  dotnet restore --configfile $nugetConfig
} else {
  dotnet restore
}
Pop-Location

# ===== BUILD FRONTEND (TikTok.Web) =====
Write-Section "Build Frontend assets (Yarn)"
$reactRoot = Join-Path $moduleRoot $reactAppFolder
if (Test-Path (Join-Path $reactRoot "package.json")) {
  Push-Location $reactRoot

  # Cài dependencies bằng Yarn
  yarn install --frozen-lockfile || yarn install

  # Kiểm tra có script build:react hay không
  $pkgJson = Get-Content package.json -Raw | ConvertFrom-Json
  $hasBuildReact = $false
  if ($pkgJson.scripts.PSObject.Properties.Name -contains "build:react") {
    $hasBuildReact = $true
  }

  if ($hasBuildReact) {
    yarn run build:react
  } else {
    # fallback
    if ($pkgJson.scripts.PSObject.Properties.Name -contains "build") {
      yarn run build
    } else {
      Write-Host "Không tìm thấy script build/build:react trong package.json — bỏ qua bước build frontend." -ForegroundColor Yellow
    }
  }
  Pop-Location
} else {
  Write-Host "Không tìm thấy package.json trong $reactRoot — bỏ qua build frontend." -ForegroundColor Yellow
}

# ===== BUILD/PUBLISH WEB (TikTok.Web) =====
Write-Section "abp install-libs (nếu dùng ABP UI)"
$webRoot = Join-Path $moduleRoot $webAppFolder
Push-Location $webRoot
try {
  abp install-libs
} catch {
  Write-Host "abp install-libs lỗi hoặc không cần, tiếp tục..." -ForegroundColor Yellow
}

Write-Section "dotnet publish Web"
dotnet publish -c Release
Pop-Location

# ===== DOCKER BUILD & SAVE =====
Write-Section "Docker build image"
$tag = $image + ":" + $version
Push-Location $webRoot

# Yêu cầu Dockerfile tại TikTok.Web
if (-not (Test-Path (Join-Path $webRoot "Dockerfile"))) {
  throw "Không tìm thấy Dockerfile trong $webRoot"
}

# Build image
docker build -f Dockerfile -t $tag .

Write-Section "Docker save to TAR"
$tarPath = Join-Path $imagesOut "$artifact_name.$outputExtension"
docker save -o $tarPath $tag
Pop-Location

# ===== PUBLISH DbMigrator =====
if (![string]::IsNullOrWhiteSpace($migratorProject)) {
  Write-Section "Publish DbMigrator"
  $migratorRoot = Join-Path $moduleRoot $migratorProject
  if (-not (Test-Path $migratorRoot)) {
    throw "Không tìm thấy DbMigrator project: $migratorRoot"
  }

  $pubDir = Join-Path $migratorRoot "bin/Release/publish"
  dotnet publish $migratorRoot -c Release -o $pubDir

  # Copy toàn bộ publish output sang migrator/
  Copy-Item -Path (Join-Path $pubDir "*") -Destination $migratorOut -Recurse -Force

} else {
  Write-Host "Không chỉ định migratorProject — bỏ qua bước publish DbMigrator." -ForegroundColor Yellow
}

Write-Section "Kết quả artifact"
Write-Host "Images:   $tarPath"
Write-Host "Migrator: $migratorOut"