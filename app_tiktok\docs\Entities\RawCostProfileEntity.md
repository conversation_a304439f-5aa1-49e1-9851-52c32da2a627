# RawCostProfileEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawCostProfiles`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawCostProfiles`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho hồ sơ chi phí của tài khoản quảng cáo. Lưu trữ thông tin chi tiết về các loại chi phí khác nhau bao gồm chi phí tiền mặt, chi phí tín dụng quảng cáo, chi phí thuế và tổng chi phí cho từng tài khoản quảng cáo theo thời gian.

## Bảng dữ liệu: RawCostProfile

| Tên Field            | Kiểu dữ liệu | Bắt buộc | Đ<PERSON> dài | <PERSON><PERSON> tả                                                  |
| -------------------- | ------------ | -------- | ------ | ------------------------------------------------------ |
| Id                   | Guid         | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                  |
| AdvertiserId         | string       | ✅       | 50     | ID nhà quảng cáo                                       |
| AdvertiserName       | string       | ✅       | 200    | Tên nhà quảng cáo                                      |
| Amount               | decimal      | ✅       | -      | Tổng số tiền chi phí cho nhà quảng cáo                 |
| CashAmount           | decimal      | ✅       | -      | Số tiền chi phí tiền mặt cho nhà quảng cáo             |
| GrantAmount          | decimal      | ✅       | -      | Số tiền chi phí tín dụng quảng cáo cho nhà quảng cáo   |
| TaxAmount            | decimal      | ✅       | -      | Số tiền thuế ước tính cho nhà quảng cáo                |
| Currency             | string       | ✅       | 10     | Tiền tệ của nhà quảng cáo                              |
| BcId                 | string       | ✅       | 100    | ID của Business Center                                 |
| Date                 | DateTime     | ✅       | -      | Thời gian đồng bộ dữ liệu (UTC) - lưu đầy đủ thời gian |
| CreationTime         | DateTime     | ✅       | -      | Thời gian tạo bản ghi (Audit)                          |
| CreatorId            | Guid?        | ❌       | -      | ID người tạo (Audit)                                   |
| LastModificationTime | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (Audit)                         |
| LastModifierId       | Guid?        | ❌       | -      | ID người sửa đổi cuối (Audit)                          |

## Enums liên quan

### CostProfileType

-   **Cash**: Chi phí tiền mặt
-   **Grant**: Chi phí tín dụng quảng cáo
-   **Tax**: Chi phí thuế
-   **Total**: Tổng chi phí

## Cấu trúc dữ liệu

### Thông tin định danh

-   **AdvertiserId**: ID duy nhất của nhà quảng cáo từ TikTok API
-   **AdvertiserName**: Tên hiển thị của nhà quảng cáo
-   **BcId**: Liên kết với Business Center

### Thông tin chi phí

-   **Amount**: Tổng số tiền chi phí cho nhà quảng cáo (tổng của CashAmount + GrantAmount + TaxAmount)
-   **CashAmount**: Số tiền chi phí tiền mặt cho nhà quảng cáo
-   **GrantAmount**: Số tiền chi phí tín dụng quảng cáo cho nhà quảng cáo
-   **TaxAmount**: Số tiền thuế ước tính cho nhà quảng cáo

### Cài đặt tài chính

-   **Currency**: Đơn vị tiền tệ theo mã ISO 4217 (ví dụ: USD, EUR, VND)
-   **Date**: Thời gian đồng bộ dữ liệu (lưu trữ dưới dạng UTC)

### Thông tin bổ sung

-   **HasChanged()**: Method kiểm tra xem entity có thay đổi so với entity khác không

## Mục đích sử dụng

-   Lưu trữ thông tin chi phí chi tiết của tài khoản quảng cáo
-   Theo dõi các loại chi phí khác nhau (tiền mặt, tín dụng, thuế)
-   Phân tích xu hướng chi phí theo thời gian
-   Báo cáo tài chính và quản lý ngân sách
-   Đồng bộ dữ liệu từ TikTok API theo thời gian thực

## Ví dụ dữ liệu

```json
{
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "advertiserId": "*********",
    "advertiserName": "Công ty ABC",
    "amount": -1376616.0,
    "cashAmount": -1376616.0,
    "grantAmount": 0.0,
    "taxAmount": -68831.0,
    "currency": "USD",
    "bcId": "BC123456",
    "date": "2024-01-15T10:30:00Z",
    "creationTime": "2024-01-15T10:30:00Z"
}
```

## Lưu ý quan trọng

-   Tất cả các trường số tiền được lưu với độ chính xác 18,2 (18 chữ số, 2 chữ số thập phân)
-   Thời gian được lưu trữ dưới dạng UTC để đảm bảo tính nhất quán
-   Entity này được thiết kế để lưu trữ dữ liệu theo thời gian thực tế, có thể có nhiều bản ghi cho cùng một AdvertiserId với các thời điểm khác nhau
-   Method HasChanged() được sử dụng để kiểm tra sự thay đổi dữ liệu khi đồng bộ từ TikTok API
