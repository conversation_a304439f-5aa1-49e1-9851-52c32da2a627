/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Creative Portfolios endpoints
    /// </summary>
    public static class CreativePortfoliosEndpoints
    {
        /// <summary>
        /// Create a portfolio endpoint (v1.3)
        /// </summary>
        public const string CreatePortfolio = "/creative/portfolio/create/";

        /// <summary>
        /// Get a portfolio by ID endpoint (v1.3)
        /// </summary>
        public const string GetPortfolioById = "/creative/portfolio/get/";

        /// <summary>
        /// Get portfolios within an ad account endpoint (v1.3)
        /// </summary>
        public const string GetPortfolios = "/creative/portfolio/list/";

        /// <summary>
        /// Delete portfolios endpoint (v1.3)
        /// </summary>
        public const string DeletePortfolios = "/creative/portfolio/delete/";
    }
}
