# RawLatestBalanceAdAccountEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawLatestBalanceAdAccounts`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawLatestBalanceAdAccounts`
-   **DbSchema**: `null`

## Mô tả

Entity cho tài khoản quảng cáo với thông tin số dư và ngân sách mới nhất. Khác với `RawBalanceAdAccountEntity` lưu trữ lịch sử, entity này chỉ lưu trữ dữ liệu mới nhất và cập nhật khi có thay đổi.

**Lưu ý quan trọng về logic đồng bộ:**

-   Entity này chỉ lưu trữ dữ liệu mới nhất thay vì lưu trữ lịch sử
-   <PERSON>hi có thay đổi dữ liệu từ API, bản ghi hiện tại sẽ được cập nhật thay vì tạo bản ghi mới
-   Trường `Date` lưu đầy đủ thời gian UTC khi dữ liệu được đồng bộ mới nhất
-   Logic: Thêm mới nếu chưa có, cập nhật nếu có thay đổi

## Bảng dữ liệu: RawLatestBalanceAdAccount

| Tên Field                  | Kiểu dữ liệu               | Bắt buộc | Độ dài | Mô tả                                                  |
| -------------------------- | -------------------------- | -------- | ------ | ------------------------------------------------------ |
| Id                         | Guid                       | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                  |
| AdvertiserId               | string                     | ✅       | 50     | ID tài khoản nhà quảng cáo (Unique Index)             |
| AdvertiserName             | string                     | ✅       | 200    | Tên tài khoản nhà quảng cáo                            |
| AdvertiserStatus           | AdvertiserAccountStatus    | ✅       | -      | Trạng thái hiển thị của tài khoản nhà quảng cáo        |
| AdvertiserType             | AdAccountType              | ✅       | -      | Loại tài khoản nhà quảng cáo                           |
| Timezone                   | string                     | ✅       | 50     | Múi giờ của tài khoản nhà quảng cáo                    |
| Currency                   | string                     | ✅       | 10     | Tiền tệ của tài khoản nhà quảng cáo                    |
| AccountOpenDays            | int                        | ✅       | -      | Số ngày tài khoản nhà quảng cáo đã được mở             |
| BalanceReminder            | bool                       | ✅       | -      | Cảnh báo khi số dư đạt ngưỡng                          |
| Company                    | string                     | ❌       | 200    | Tên công ty của tài khoản nhà quảng cáo                |
| ContactName                | string                     | ❌       | 100    | Tên liên hệ của tài khoản nhà quảng cáo                |
| ContactEmail               | string                     | ❌       | 100    | Email liên hệ của tài khoản nhà quảng cáo              |
| CreateTime                 | DateTime                   | ✅       | -      | Thời gian mở tài khoản nhà quảng cáo (UTC+0)           |
| AccountBalance             | decimal                    | ❌       | -      | Tổng số dư tài khoản nhà quảng cáo                     |
| ValidAccountBalance        | decimal                    | ❌       | -      | Số dư tài khoản hợp lệ                                 |
| FrozenBalance              | decimal                    | ❌       | -      | Số dư bị đóng băng                                     |
| Tax                        | decimal                    | ❌       | -      | Thuế của tài khoản nhà quảng cáo                       |
| CashBalance                | decimal                    | ❌       | -      | Số dư tiền mặt                                         |
| ValidCashBalance           | decimal                    | ❌       | -      | Số dư tiền mặt hợp lệ                                  |
| GrantBalance               | decimal                    | ❌       | -      | Số dư phiếu giảm giá/voucher                           |
| ValidGrantBalance          | decimal                    | ❌       | -      | Số dư phiếu giảm giá/voucher hợp lệ                    |
| TransferableAmount         | decimal?                   | ❌       | -      | Số tiền có thể chuyển từ tài khoản nhà quảng cáo       |
| BudgetMode                 | BudgetMode                 | ❌       | -      | Chế độ ngân sách của tài khoản quảng cáo               |
| Budget                     | decimal                    | ❌       | -      | Ngân sách (tùy theo budget_mode)                       |
| BudgetCost                 | decimal                    | ❌       | -      | Chi phí ngân sách đã sử dụng                           |
| BudgetRemaining            | decimal                    | ❌       | -      | Ngân sách còn lại                                      |
| BudgetFrequencyRestriction | BudgetFrequencyRestriction | ❌       | -      | Thông tin hạn chế tần suất thay đổi ngân sách          |
| BudgetAmountRestriction    | BudgetAmountRestriction    | ❌       | -      | Thông tin hạn chế số tiền thay đổi ngân sách           |
| MinTransferableAmount      | MinTransferableAmount      | ❌       | -      | Số tiền tối thiểu có thể chuyển                        |
| BcId                       | string                     | ✅       | 100    | ID của Business Center                                 |
| LatestRechargeTime         | DateTime?                  | ❌       | -      | Thời gian nạp tiền lần cuối                            |
| FirstRechargeAmount        | decimal                    | ❌       | -      | Số tiền nạp lần đầu                                    |
| RechargeAmount             | decimal                    | ❌       | -      | Số tiền đã nạp                                         |
| RechargeCount              | int                        | ❌       | -      | Số lần nạp tiền                                        |
| Date                       | DateTime                   | ❌       | -      | Thời gian đồng bộ dữ liệu mới nhất (UTC)               |
| CreationTime               | DateTime                   | ✅       | -      | Thời gian tạo bản ghi (Audit)                          |
| CreatorId                  | Guid?                      | ❌       | -      | ID người tạo (Audit)                                   |
| LastModificationTime       | DateTime?                  | ❌       | -      | Thời gian sửa đổi cuối (Audit)                         |
| LastModifierId             | Guid?                      | ❌       | -      | ID người sửa đổi cuối (Audit)                          |

## Enums liên quan

### AdvertiserAccountStatus

-   Định nghĩa trạng thái của tài khoản nhà quảng cáo

### AdAccountType

-   Định nghĩa loại tài khoản nhà quảng cáo

### BudgetMode

-   Định nghĩa chế độ ngân sách

### BudgetFrequencyRestriction

-   Class chứa thông tin hạn chế tần suất thay đổi ngân sách

### BudgetAmountRestriction

-   Class chứa thông tin hạn chế số tiền thay đổi ngân sách

### MinTransferableAmount

-   Class chứa thông tin số tiền tối thiểu có thể chuyển

## Logic đồng bộ mới nhất (Latest)

### Cách thức hoạt động

1. **Kiểm tra bản ghi hiện tại**: Hệ thống sẽ tìm bản ghi theo `AdvertiserId`
2. **So sánh thay đổi**: So sánh dữ liệu từ API với bản ghi hiện tại
3. **Upsert**:
   - Nếu chưa có bản ghi: Tạo mới
   - Nếu có bản ghi và có thay đổi: Cập nhật bản ghi hiện tại
   - Nếu có bản ghi và không có thay đổi: Không làm gì
4. **Lưu thời gian UTC**: Thời gian cập nhật được chuyển đổi về UTC

### Các trường được so sánh để phát hiện thay đổi

-   AdvertiserName, AdvertiserStatus, AdvertiserType
-   AccountBalance, ValidAccountBalance, FrozenBalance, Tax
-   CashBalance, ValidCashBalance, GrantBalance, ValidGrantBalance
-   TransferableAmount, BudgetMode, Budget, BudgetCost, BudgetRemaining
-   Currency, AccountOpenDays, BalanceReminder
-   Company, ContactName, ContactEmail
-   BudgetFrequencyRestriction, BudgetAmountRestriction, MinTransferableAmount

### Lợi ích của logic Latest

-   **Hiệu suất cao**: Chỉ lưu trữ dữ liệu mới nhất, giảm kích thước database
-   **Truy vấn nhanh**: Không cần tìm kiếm trong lịch sử, luôn lấy bản ghi duy nhất
-   **Dễ dàng tích hợp**: Phù hợp cho các API cần dữ liệu thời gian thực
-   **Tiết kiệm storage**: Không lưu trữ lịch sử thay đổi

## So sánh với RawBalanceAdAccountEntity

| Tiêu chí                | RawBalanceAdAccountEntity             | RawLatestBalanceAdAccountEntity     |
| ----------------------- | ------------------------------------- | ----------------------------------- |
| **Lưu trữ dữ liệu**     | Lịch sử đầy đủ                        | Chỉ dữ liệu mới nhất                |
| **Khi có thay đổi**     | Tạo bản ghi mới                       | Cập nhật bản ghi hiện tại           |
| **Kích thước database** | Lớn (tăng theo thời gian)             | Nhỏ (cố định)                       |
| **Truy vấn**            | Phức tạp (cần filter theo thời gian)  | Đơn giản (lấy trực tiếp)            |
| **Mục đích sử dụng**    | Phân tích lịch sử, tracking thay đổi  | API realtime, dashboard hiện tại    |

## Mục đích sử dụng

-   Cung cấp dữ liệu tài khoản quảng cáo mới nhất cho dashboard và API
-   Hỗ trợ các tính năng realtime cần dữ liệu cập nhật
-   Tối ưu hiệu suất cho các truy vấn thường xuyên về trạng thái hiện tại
-   Giảm độ phức tạp trong việc lấy dữ liệu số dư và ngân sách hiện tại
-   Phù hợp cho các tích hợp cần dữ liệu nhanh và đơn giản
-   Theo dõi trạng thái tài khoản, số dư và ngân sách realtime
-   Quản lý thông tin liên hệ và cài đặt tài khoản hiện tại

## Lưu ý

-   Trường `AdvertiserId` nên có unique index để đảm bảo chỉ có một bản ghi cho mỗi advertiser
-   Các trường `LatestRechargeTime`, `FirstRechargeAmount`, `RechargeAmount`, `RechargeCount` được đánh dấu `Obsolete` trong code
-   Entity này kế thừa từ AuditedEntity để có đầy đủ thông tin audit
-   Trường `Date` lưu đầy đủ thời gian UTC thay vì chỉ ngày tháng năm
-   **Quan trọng**: Entity này bổ sung chứ không thay thế `RawBalanceAdAccountEntity`
-   Cần đảm bảo mapping đúng các complex objects: BudgetFrequencyRestriction, BudgetAmountRestriction, MinTransferableAmount