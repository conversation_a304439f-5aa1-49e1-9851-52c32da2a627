/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;

namespace TikTokBusinessApi.Core
{
    /// <summary>
    /// TikTok API environment configurations
    /// </summary>
    public enum TikTokEnvironmentType
    {
        /// <summary>
        /// Production environment
        /// </summary>
        Production,

        /// <summary>
        /// Sandbox environment for testing
        /// </summary>
        Sandbox,

        /// <summary>
        /// Custom environment with user-defined settings
        /// </summary>
        Custom
    }

    /// <summary>
    /// TikTok API environment configuration
    /// </summary>
    public class TikTokEnvironment
    {
        /// <summary>
        /// Environment type
        /// </summary>
        public TikTokEnvironmentType Type { get; set; }

        /// <summary>
        /// Base URL for the API
        /// </summary>
        public string BaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// API version
        /// </summary>
        public string ApiVersion { get; set; } = "v1.3";

        /// <summary>
        /// API prefix
        /// </summary>
        public string ApiPrefix { get; set; } = "open_api";

        /// <summary>
        /// Environment name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Environment description
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Whether this environment supports all features
        /// </summary>
        public bool SupportsAllFeatures { get; set; } = true;

        /// <summary>
        /// Rate limiting configuration for this environment
        /// </summary>
        public EnvironmentRateLimits RateLimits { get; set; } = new();

        /// <summary>
        /// Additional environment-specific headers
        /// </summary>
        public Dictionary<string, string> AdditionalHeaders { get; set; } = new();

        /// <summary>
        /// Environment-specific configuration
        /// </summary>
        public Dictionary<string, object> Configuration { get; set; } = new();

        /// <summary>
        /// Production environment configuration
        /// </summary>
        public static TikTokEnvironment Production => new()
        {
            Type = TikTokEnvironmentType.Production,
            Name = "Production",
            Description = "TikTok Business API Production Environment",
            BaseUrl = "https://business-api.tiktok.com",
            ApiVersion = "v1.3",
            ApiPrefix = "open_api",
            SupportsAllFeatures = true,
            RateLimits = new EnvironmentRateLimits
            {
                RequestsPerSecond = 10,
                RequestsPerMinute = 600,
                RequestsPerHour = 36000,
                ConcurrentRequests = 50
            }
        };

        /// <summary>
        /// Sandbox environment configuration
        /// </summary>
        public static TikTokEnvironment Sandbox => new()
        {
            Type = TikTokEnvironmentType.Sandbox,
            Name = "Sandbox",
            Description = "TikTok Business API Sandbox Environment",
            BaseUrl = "https://sandbox-ads.tiktok.com",
            ApiVersion = "v1.3",
            ApiPrefix = "open_api",
            SupportsAllFeatures = false,
            RateLimits = new EnvironmentRateLimits
            {
                RequestsPerSecond = 5,
                RequestsPerMinute = 300,
                RequestsPerHour = 18000,
                ConcurrentRequests = 25
            },
            AdditionalHeaders = new Dictionary<string, string>
            {
                ["X-Environment"] = "sandbox"
            }
        };

        /// <summary>
        /// Creates a custom environment configuration
        /// </summary>
        /// <param name="name">Environment name</param>
        /// <param name="baseUrl">Base URL</param>
        /// <param name="description">Environment description</param>
        /// <returns>Custom environment configuration</returns>
        public static TikTokEnvironment Custom(string name, string baseUrl, string? description = null)
        {
            return new TikTokEnvironment
            {
                Type = TikTokEnvironmentType.Custom,
                Name = name,
                Description = description ?? $"Custom TikTok API Environment: {name}",
                BaseUrl = baseUrl,
                ApiVersion = "v1.3",
                ApiPrefix = "open_api",
                SupportsAllFeatures = true,
                RateLimits = new EnvironmentRateLimits()
            };
        }

        /// <summary>
        /// Gets the full API base path
        /// </summary>
        /// <returns>Full API base path</returns>
        public string GetFullBasePath()
        {
            return $"{BaseUrl.TrimEnd('/')}/{ApiPrefix}/{ApiVersion}";
        }

        /// <summary>
        /// Validates the environment configuration
        /// </summary>
        /// <returns>Validation result</returns>
        public EnvironmentValidationResult Validate()
        {
            var result = new EnvironmentValidationResult();

            if (string.IsNullOrWhiteSpace(BaseUrl))
            {
                result.Errors.Add("BaseUrl is required");
            }
            else if (!Uri.TryCreate(BaseUrl, UriKind.Absolute, out var uri) || 
                     (uri.Scheme != "http" && uri.Scheme != "https"))
            {
                result.Errors.Add("BaseUrl must be a valid HTTP or HTTPS URL");
            }

            if (string.IsNullOrWhiteSpace(ApiVersion))
            {
                result.Errors.Add("ApiVersion is required");
            }

            if (string.IsNullOrWhiteSpace(ApiPrefix))
            {
                result.Errors.Add("ApiPrefix is required");
            }

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.Errors.Add("Name is required");
            }

            if (RateLimits.RequestsPerSecond <= 0)
            {
                result.Warnings.Add("RequestsPerSecond should be greater than 0");
            }

            return result;
        }
    }

    /// <summary>
    /// Environment-specific rate limiting configuration
    /// </summary>
    public class EnvironmentRateLimits
    {
        /// <summary>
        /// Maximum requests per second
        /// </summary>
        public double RequestsPerSecond { get; set; } = 10.0;

        /// <summary>
        /// Maximum requests per minute
        /// </summary>
        public int RequestsPerMinute { get; set; } = 600;

        /// <summary>
        /// Maximum requests per hour
        /// </summary>
        public int RequestsPerHour { get; set; } = 36000;

        /// <summary>
        /// Maximum concurrent requests
        /// </summary>
        public int ConcurrentRequests { get; set; } = 50;

        /// <summary>
        /// Burst allowance for short-term spikes
        /// </summary>
        public int BurstAllowance { get; set; } = 20;
    }

    /// <summary>
    /// Environment validation result
    /// </summary>
    public class EnvironmentValidationResult
    {
        /// <summary>
        /// Validation errors
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// Validation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// Whether the environment configuration is valid
        /// </summary>
        public bool IsValid => Errors.Count == 0;

        /// <summary>
        /// Whether there are any warnings
        /// </summary>
        public bool HasWarnings => Warnings.Count > 0;
    }
}
