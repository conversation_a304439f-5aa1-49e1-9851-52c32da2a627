using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service interface cho việc đồng bộ dữ liệu quản lý Business Center
    /// </summary>
    public interface IBcManagementSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ Business Center theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BcManagementSyncResult> SyncBusinessCenterAsync(string bcId);

        /// <summary>
        /// Đồng bộ tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BcManagementSyncResult> SyncAllBusinessCentersAsync();

        /// <summary>
        /// Đồng bộ changelog của Business Center
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> bắt đầu (optional)</param>
        /// <param name="endDate">Ngày kết thúc (optional)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BcManagementSyncResult> SyncBusinessCenterChangelogAsync(string bcId, string? startDate = null, string? endDate = null);
    }
}