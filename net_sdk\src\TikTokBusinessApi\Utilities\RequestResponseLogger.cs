/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TikTokBusinessApi.Utilities
{
    /// <summary>
    /// Comprehensive request/response logging utility for debugging
    /// </summary>
    public class RequestResponseLogger
    {
        private readonly ILogger<RequestResponseLogger>? _logger;
        private readonly RequestResponseLoggerOptions _options;

        /// <summary>
        /// Initializes a new instance of the RequestResponseLogger class
        /// </summary>
        /// <param name="options">Logging configuration options</param>
        /// <param name="logger">Logger instance (optional)</param>
        public RequestResponseLogger(RequestResponseLoggerOptions? options = null, ILogger<RequestResponseLogger>? logger = null)
        {
            _options = options ?? new RequestResponseLoggerOptions();
            _logger = logger;
        }

        /// <summary>
        /// Log an HTTP request
        /// </summary>
        /// <param name="request">HTTP request message</param>
        /// <param name="correlationId">Correlation ID for tracking</param>
        /// <returns>Task representing the async operation</returns>
        public async Task LogRequestAsync(HttpRequestMessage request, string correlationId)
        {
            if (!_options.LogRequests || _logger == null)
                return;

            try
            {
                var logEntry = new RequestLogEntry
                {
                    CorrelationId = correlationId,
                    Timestamp = DateTimeOffset.UtcNow,
                    Method = request.Method.ToString(),
                    Uri = SanitizeUri(request.RequestUri?.ToString() ?? ""),
                    Headers = await GetHeadersAsync(request.Headers),
                    ContentType = request.Content?.Headers?.ContentType?.ToString(),
                    ContentLength = request.Content?.Headers?.ContentLength,
                    Body = await GetRequestBodyAsync(request)
                };

                var logMessage = FormatRequestLog(logEntry);
                _logger.Log(_options.RequestLogLevel, "HTTP Request: {LogMessage}", logMessage);

                // Write to file if configured
                if (_options.WriteToFile && !string.IsNullOrEmpty(_options.LogFilePath))
                {
                    await WriteToFileAsync(logMessage);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to log HTTP request for correlation ID: {CorrelationId}", correlationId);
            }
        }

        /// <summary>
        /// Log an HTTP response
        /// </summary>
        /// <param name="response">HTTP response message</param>
        /// <param name="correlationId">Correlation ID for tracking</param>
        /// <param name="duration">Request duration</param>
        /// <returns>Task representing the async operation</returns>
        public async Task LogResponseAsync(HttpResponseMessage response, string correlationId, TimeSpan duration)
        {
            if (!_options.LogResponses || _logger == null)
                return;

            try
            {
                var logEntry = new ResponseLogEntry
                {
                    CorrelationId = correlationId,
                    Timestamp = DateTimeOffset.UtcNow,
                    StatusCode = (int)response.StatusCode,
                    StatusDescription = response.ReasonPhrase ?? "",
                    Headers = await GetHeadersAsync(response.Headers),
                    ContentType = response.Content?.Headers?.ContentType?.ToString(),
                    ContentLength = response.Content?.Headers?.ContentLength,
                    Body = await GetResponseBodyAsync(response),
                    Duration = duration
                };

                var logMessage = FormatResponseLog(logEntry);
                var logLevel = GetResponseLogLevel(response.StatusCode);
                _logger.Log(logLevel, "HTTP Response: {LogMessage}", logMessage);

                // Write to file if configured
                if (_options.WriteToFile && !string.IsNullOrEmpty(_options.LogFilePath))
                {
                    await WriteToFileAsync(logMessage);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to log HTTP response for correlation ID: {CorrelationId}", correlationId);
            }
        }

        /// <summary>
        /// Log an exception that occurred during request processing
        /// </summary>
        /// <param name="exception">Exception that occurred</param>
        /// <param name="correlationId">Correlation ID for tracking</param>
        /// <param name="duration">Request duration before failure</param>
        public void LogException(Exception exception, string correlationId, TimeSpan duration)
        {
            if (!_options.LogExceptions || _logger == null)
                return;

            try
            {
                var logEntry = new ExceptionLogEntry
                {
                    CorrelationId = correlationId,
                    Timestamp = DateTimeOffset.UtcNow,
                    ExceptionType = exception.GetType().Name,
                    Message = exception.Message,
                    StackTrace = _options.IncludeStackTrace ? exception.StackTrace : null,
                    Duration = duration
                };

                var logMessage = FormatExceptionLog(logEntry);
                _logger.LogError(exception, "HTTP Request Exception: {LogMessage}", logMessage);

                // Write to file if configured
                if (_options.WriteToFile && !string.IsNullOrEmpty(_options.LogFilePath))
                {
                    Task.Run(async () => await WriteToFileAsync(logMessage));
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to log HTTP exception for correlation ID: {CorrelationId}", correlationId);
            }
        }

        private async Task<string> GetRequestBodyAsync(HttpRequestMessage request)
        {
            if (!_options.LogRequestBody || request.Content == null)
                return "";

            try
            {
                var content = await request.Content.ReadAsStringAsync();
                return SanitizeBody(content);
            }
            catch
            {
                return "[Unable to read request body]";
            }
        }

        private async Task<string> GetResponseBodyAsync(HttpResponseMessage response)
        {
            if (!_options.LogResponseBody || response.Content == null)
                return "";

            try
            {
                var content = await response.Content.ReadAsStringAsync();
                return SanitizeBody(content);
            }
            catch
            {
                return "[Unable to read response body]";
            }
        }

        private async Task<Dictionary<string, string>> GetHeadersAsync(System.Net.Http.Headers.HttpHeaders headers)
        {
            if (!_options.LogHeaders)
                return new Dictionary<string, string>();

            var headerDict = new Dictionary<string, string>();
            
            foreach (var header in headers)
            {
                var key = header.Key;
                var value = string.Join(", ", header.Value);

                // Sanitize sensitive headers
                if (_options.SensitiveHeaders.Contains(key.ToLowerInvariant()))
                {
                    value = "[REDACTED]";
                }

                headerDict[key] = value;
            }

            return headerDict;
        }

        private string SanitizeUri(string uri)
        {
            if (string.IsNullOrEmpty(uri))
                return uri;

            // Remove or mask sensitive query parameters
            foreach (var sensitiveParam in _options.SensitiveQueryParameters)
            {
                if (uri.Contains($"{sensitiveParam}=", StringComparison.OrdinalIgnoreCase))
                {
                    var pattern = $@"({sensitiveParam}=)[^&]*";
                    uri = System.Text.RegularExpressions.Regex.Replace(uri, pattern, "$1[REDACTED]", 
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                }
            }

            return uri;
        }

        private string SanitizeBody(string body)
        {
            if (string.IsNullOrEmpty(body) || !_options.SanitizeBody)
                return body;

            try
            {
                // Try to parse as JSON and sanitize sensitive fields
                var jsonDoc = JsonDocument.Parse(body);
                var sanitized = SanitizeJsonElement(jsonDoc.RootElement);
                return JsonSerializer.Serialize(sanitized, new JsonSerializerOptions { WriteIndented = _options.PrettyPrintJson });
            }
            catch
            {
                // If not JSON, return as-is (could add other sanitization logic here)
                return body.Length > _options.MaxBodyLength ? 
                    body.Substring(0, _options.MaxBodyLength) + "... [TRUNCATED]" : body;
            }
        }

        private object SanitizeJsonElement(JsonElement element)
        {
            switch (element.ValueKind)
            {
                case JsonValueKind.Object:
                    var obj = new Dictionary<string, object>();
                    foreach (var property in element.EnumerateObject())
                    {
                        var key = property.Name;
                        var value = _options.SensitiveFields.Contains(key.ToLowerInvariant()) 
                            ? "[REDACTED]" 
                            : SanitizeJsonElement(property.Value);
                        obj[key] = value;
                    }
                    return obj;

                case JsonValueKind.Array:
                    var array = new List<object>();
                    foreach (var item in element.EnumerateArray())
                    {
                        array.Add(SanitizeJsonElement(item));
                    }
                    return array;

                case JsonValueKind.String:
                    return element.GetString() ?? "";

                case JsonValueKind.Number:
                    return element.TryGetInt64(out var longValue) ? longValue : element.GetDouble();

                case JsonValueKind.True:
                    return true;

                case JsonValueKind.False:
                    return false;

                case JsonValueKind.Null:
                    return null;

                default:
                    return element.ToString();
            }
        }

        private LogLevel GetResponseLogLevel(System.Net.HttpStatusCode statusCode)
        {
            return (int)statusCode switch
            {
                >= 500 => LogLevel.Error,
                >= 400 => LogLevel.Warning,
                _ => _options.ResponseLogLevel
            };
        }

        private string FormatRequestLog(RequestLogEntry entry)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"[{entry.CorrelationId}] {entry.Method} {entry.Uri}");
            
            if (_options.LogHeaders && entry.Headers.Count > 0)
            {
                sb.AppendLine("Headers:");
                foreach (var header in entry.Headers)
                {
                    sb.AppendLine($"  {header.Key}: {header.Value}");
                }
            }

            if (!string.IsNullOrEmpty(entry.Body))
            {
                sb.AppendLine($"Body: {entry.Body}");
            }

            return sb.ToString();
        }

        private string FormatResponseLog(ResponseLogEntry entry)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"[{entry.CorrelationId}] {entry.StatusCode} {entry.StatusDescription} ({entry.Duration.TotalMilliseconds:F2}ms)");
            
            if (_options.LogHeaders && entry.Headers.Count > 0)
            {
                sb.AppendLine("Headers:");
                foreach (var header in entry.Headers)
                {
                    sb.AppendLine($"  {header.Key}: {header.Value}");
                }
            }

            if (!string.IsNullOrEmpty(entry.Body))
            {
                sb.AppendLine($"Body: {entry.Body}");
            }

            return sb.ToString();
        }

        private string FormatExceptionLog(ExceptionLogEntry entry)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"[{entry.CorrelationId}] Exception: {entry.ExceptionType} ({entry.Duration.TotalMilliseconds:F2}ms)");
            sb.AppendLine($"Message: {entry.Message}");
            
            if (!string.IsNullOrEmpty(entry.StackTrace))
            {
                sb.AppendLine($"StackTrace: {entry.StackTrace}");
            }

            return sb.ToString();
        }

        private async Task WriteToFileAsync(string logMessage)
        {
            try
            {
                var logFilePath = _options.LogFilePath!;
                var directory = Path.GetDirectoryName(logFilePath);
                
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var timestamp = DateTimeOffset.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var formattedMessage = $"[{timestamp}] {logMessage}{Environment.NewLine}";
                
                await File.AppendAllTextAsync(logFilePath, formattedMessage);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to write log to file: {FilePath}", _options.LogFilePath);
            }
        }
    }

    /// <summary>
    /// Configuration options for request/response logging
    /// </summary>
    public class RequestResponseLoggerOptions
    {
        /// <summary>
        /// Whether to log HTTP requests
        /// </summary>
        public bool LogRequests { get; set; } = true;

        /// <summary>
        /// Whether to log HTTP responses
        /// </summary>
        public bool LogResponses { get; set; } = true;

        /// <summary>
        /// Whether to log exceptions
        /// </summary>
        public bool LogExceptions { get; set; } = true;

        /// <summary>
        /// Whether to log request headers
        /// </summary>
        public bool LogHeaders { get; set; } = true;

        /// <summary>
        /// Whether to log request body
        /// </summary>
        public bool LogRequestBody { get; set; } = true;

        /// <summary>
        /// Whether to log response body
        /// </summary>
        public bool LogResponseBody { get; set; } = true;

        /// <summary>
        /// Whether to sanitize sensitive data in request/response bodies
        /// </summary>
        public bool SanitizeBody { get; set; } = true;

        /// <summary>
        /// Whether to include stack traces in exception logs
        /// </summary>
        public bool IncludeStackTrace { get; set; } = true;

        /// <summary>
        /// Whether to write logs to file
        /// </summary>
        public bool WriteToFile { get; set; } = false;

        /// <summary>
        /// File path for log output (if WriteToFile is true)
        /// </summary>
        public string? LogFilePath { get; set; }

        /// <summary>
        /// Whether to pretty print JSON in logs
        /// </summary>
        public bool PrettyPrintJson { get; set; } = false;

        /// <summary>
        /// Maximum length of request/response body to log
        /// </summary>
        public int MaxBodyLength { get; set; } = 10000;

        /// <summary>
        /// Log level for HTTP requests
        /// </summary>
        public LogLevel RequestLogLevel { get; set; } = LogLevel.Debug;

        /// <summary>
        /// Log level for HTTP responses
        /// </summary>
        public LogLevel ResponseLogLevel { get; set; } = LogLevel.Debug;

        /// <summary>
        /// Headers that should be redacted in logs
        /// </summary>
        public HashSet<string> SensitiveHeaders { get; set; } = new(StringComparer.OrdinalIgnoreCase)
        {
            "authorization", "access-token", "x-api-key", "cookie", "set-cookie"
        };

        /// <summary>
        /// Query parameters that should be redacted in logs
        /// </summary>
        public HashSet<string> SensitiveQueryParameters { get; set; } = new(StringComparer.OrdinalIgnoreCase)
        {
            "access_token", "api_key", "password", "secret"
        };

        /// <summary>
        /// JSON fields that should be redacted in logs
        /// </summary>
        public HashSet<string> SensitiveFields { get; set; } = new(StringComparer.OrdinalIgnoreCase)
        {
            "password", "secret", "token", "access_token", "refresh_token", "api_key", "private_key"
        };
    }

    /// <summary>
    /// Log entry for HTTP requests
    /// </summary>
    internal class RequestLogEntry
    {
        public string CorrelationId { get; set; } = string.Empty;
        public DateTimeOffset Timestamp { get; set; }
        public string Method { get; set; } = string.Empty;
        public string Uri { get; set; } = string.Empty;
        public Dictionary<string, string> Headers { get; set; } = new();
        public string? ContentType { get; set; }
        public long? ContentLength { get; set; }
        public string Body { get; set; } = string.Empty;
    }

    /// <summary>
    /// Log entry for HTTP responses
    /// </summary>
    internal class ResponseLogEntry
    {
        public string CorrelationId { get; set; } = string.Empty;
        public DateTimeOffset Timestamp { get; set; }
        public int StatusCode { get; set; }
        public string StatusDescription { get; set; } = string.Empty;
        public Dictionary<string, string> Headers { get; set; } = new();
        public string? ContentType { get; set; }
        public long? ContentLength { get; set; }
        public string Body { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// Log entry for exceptions
    /// </summary>
    internal class ExceptionLogEntry
    {
        public string CorrelationId { get; set; } = string.Empty;
        public DateTimeOffset Timestamp { get; set; }
        public string ExceptionType { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? StackTrace { get; set; }
        public TimeSpan Duration { get; set; }
    }
}
