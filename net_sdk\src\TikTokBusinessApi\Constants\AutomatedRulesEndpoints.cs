/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API AutomatedRules endpoints
    /// </summary>
    public static class AutomatedRulesEndpoints
    {
        /// <summary>
        /// Create automated rules endpoint (v1.3)
        /// </summary>
        public const string CreateRules = "/optimizer/rule/create/";

        /// <summary>
        /// Get rules by ID endpoint (v1.3)
        /// </summary>
        public const string GetRulesById = "/optimizer/rule/get/";

        /// <summary>
        /// Get rules by filters endpoint (v1.3)
        /// </summary>
        public const string GetRulesByFilters = "/optimizer/rule/list/";

        /// <summary>
        /// Get rule results endpoint (v1.3)
        /// </summary>
        public const string GetRuleResults = "/optimizer/rule/result/list/";

        /// <summary>
        /// Get result details endpoint (v1.3)
        /// </summary>
        public const string GetResultDetails = "/optimizer/rule/result/get/";

        /// <summary>
        /// Update rules endpoint (v1.3)
        /// </summary>
        public const string UpdateRules = "/optimizer/rule/update/";

        /// <summary>
        /// Update rule statuses endpoint (v1.3)
        /// </summary>
        public const string UpdateRuleStatuses = "/optimizer/rule/update/status/";

        /// <summary>
        /// Bind/Unbind rules endpoint (v1.3)
        /// </summary>
        public const string BindUnbindRules = "/optimizer/rule/batch_bind/";
    }
}
