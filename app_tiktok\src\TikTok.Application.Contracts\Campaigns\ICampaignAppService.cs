using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.Campaigns
{
    /// <summary>
    /// Interface cho Campaign App Service
    /// </summary>
    public interface ICampaignAppService :
        ICrudAppService<
            CampaignDto,
            Guid,
            GetCampaignListDto,
            CreateCampaignDto,
            UpdateCampaignDto>
    {
        /// <summary>
        /// Lấy chiến dịch theo ID
        /// </summary>
        /// <param name="id">ID của chiến dịch</param>
        /// <returns>Chiến dịch</returns>
        Task<CampaignDto> GetAsync(Guid id);

        /// <summary>
        /// L<PERSON>y danh sách chiến dịch
        /// </summary>
        /// <param name="input">Tham số đầu vào</param>
        /// <returns>Danh sách chiến dịch</returns>
        Task<PagedResultDto<CampaignDto>> GetListAsync(GetCampaignListDto input);

        /// <summary>
        /// Tạo mới chiến dịch
        /// </summary>
        /// <param name="input">D<PERSON> liệu tạo mới</param>
        /// <returns>Chiến dịch đã tạo</returns>
        Task<CampaignDto> CreateAsync(CreateCampaignDto input);

        /// <summary>
        /// Cập nhật chiến dịch
        /// </summary>
        /// <param name="id">ID của chiến dịch</param>
        /// <param name="input">Dữ liệu cập nhật</param>
        /// <returns>Chiến dịch đã cập nhật</returns>
        Task<CampaignDto> UpdateAsync(Guid id, UpdateCampaignDto input);

        /// <summary>
        /// Xóa chiến dịch
        /// </summary>
        /// <param name="id">ID của chiến dịch</param>
        /// <returns></returns>
        Task DeleteAsync(Guid id);
    }
}