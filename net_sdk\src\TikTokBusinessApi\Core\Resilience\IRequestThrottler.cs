/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace TikTokBusinessApi.Core.Resilience
{
    /// <summary>
    /// Interface for request throttling implementations
    /// </summary>
    public interface IRequestThrottler
    {
        /// <summary>
        /// Throttles a request based on current load and policies
        /// </summary>
        /// <param name="requestContext">Context information about the request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task that completes when request can proceed</returns>
        Task ThrottleAsync(RequestContext requestContext, CancellationToken cancellationToken = default);

        /// <summary>
        /// Checks if a request should be throttled without waiting
        /// </summary>
        /// <param name="requestContext">Context information about the request</param>
        /// <returns>Throttle decision</returns>
        ThrottleDecision ShouldThrottle(RequestContext requestContext);

        /// <summary>
        /// Updates throttling metrics after a request completes
        /// </summary>
        /// <param name="requestContext">Context information about the request</param>
        /// <param name="responseTime">Time taken to complete the request</param>
        /// <param name="success">Whether the request was successful</param>
        void RecordRequestCompletion(RequestContext requestContext, TimeSpan responseTime, bool success);

        /// <summary>
        /// Gets current throttling metrics
        /// </summary>
        ThrottlingMetrics GetMetrics();

        /// <summary>
        /// Resets throttling state
        /// </summary>
        void Reset();
    }

    /// <summary>
    /// Context information for a request
    /// </summary>
    public class RequestContext
    {
        /// <summary>
        /// Unique identifier for the request
        /// </summary>
        public string RequestId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// HTTP method of the request
        /// </summary>
        public string HttpMethod { get; set; } = string.Empty;

        /// <summary>
        /// API endpoint path
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// Priority of the request (higher values = higher priority)
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// Client identifier (for per-client throttling)
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// User identifier (for per-user throttling)
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// Additional metadata for the request
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// Timestamp when the request was created
        /// </summary>
        public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;
    }

    /// <summary>
    /// Throttle decision result
    /// </summary>
    public class ThrottleDecision
    {
        /// <summary>
        /// Whether the request should be throttled
        /// </summary>
        public bool ShouldThrottle { get; set; }

        /// <summary>
        /// Delay before the request can proceed
        /// </summary>
        public TimeSpan Delay { get; set; }

        /// <summary>
        /// Reason for throttling
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Additional context information
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new();

        /// <summary>
        /// Creates a decision to allow the request immediately
        /// </summary>
        public static ThrottleDecision Allow() => new() { ShouldThrottle = false };

        /// <summary>
        /// Creates a decision to throttle the request
        /// </summary>
        /// <param name="delay">Delay before request can proceed</param>
        /// <param name="reason">Reason for throttling</param>
        public static ThrottleDecision Throttle(TimeSpan delay, string? reason = null) => new()
        {
            ShouldThrottle = true,
            Delay = delay,
            Reason = reason
        };
    }

    /// <summary>
    /// Throttling metrics
    /// </summary>
    public class ThrottlingMetrics
    {
        /// <summary>
        /// Total number of requests processed
        /// </summary>
        public long TotalRequests { get; set; }

        /// <summary>
        /// Number of requests that were throttled
        /// </summary>
        public long ThrottledRequests { get; set; }

        /// <summary>
        /// Current requests per second
        /// </summary>
        public double CurrentRequestsPerSecond { get; set; }

        /// <summary>
        /// Average response time
        /// </summary>
        public TimeSpan AverageResponseTime { get; set; }

        /// <summary>
        /// Current queue length (pending requests)
        /// </summary>
        public int QueueLength { get; set; }

        /// <summary>
        /// Throttling rate (0.0 to 1.0)
        /// </summary>
        public double ThrottlingRate => TotalRequests > 0 ? (double)ThrottledRequests / TotalRequests : 0.0;
    }

    /// <summary>
    /// Request throttler configuration options
    /// </summary>
    public class RequestThrottlerOptions
    {
        /// <summary>
        /// Maximum requests per second (default: 10)
        /// </summary>
        public double MaxRequestsPerSecond { get; set; } = 10.0;

        /// <summary>
        /// Maximum concurrent requests (default: 50)
        /// </summary>
        public int MaxConcurrentRequests { get; set; } = 50;

        /// <summary>
        /// Maximum queue size for pending requests (default: 100)
        /// </summary>
        public int MaxQueueSize { get; set; } = 100;

        /// <summary>
        /// Whether to enable adaptive throttling based on response times
        /// </summary>
        public bool EnableAdaptiveThrottling { get; set; } = true;

        /// <summary>
        /// Target response time for adaptive throttling
        /// </summary>
        public TimeSpan TargetResponseTime { get; set; } = TimeSpan.FromSeconds(2);

        /// <summary>
        /// Maximum delay for throttled requests
        /// </summary>
        public TimeSpan MaxThrottleDelay { get; set; } = TimeSpan.FromMinutes(1);
    }
}
