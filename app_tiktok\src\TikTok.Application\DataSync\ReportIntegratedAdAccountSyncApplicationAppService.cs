using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu báo cáo tích hợp AdAccount
    /// </summary>
    public class ReportIntegratedAdAccountSyncApplicationAppService : ApplicationService, IReportIntegratedAdAccountSyncApplicationAppService
    {
        private readonly IReportIntegratedAdAccountSyncService _reportIntegratedAdAccountSyncService;

        public ReportIntegratedAdAccountSyncApplicationAppService(
            IReportIntegratedAdAccountSyncService reportIntegratedAdAccountSyncService)
        {
            _reportIntegratedAdAccountSyncService = reportIntegratedAdAccountSyncService;
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdAccount theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> b<PERSON>t đầu (tùy chọn)</param>
        /// <param name="endDate">Ngày kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedAdAccountSyncResult> SyncReportIntegratedAdAccountAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _reportIntegratedAdAccountSyncService.SyncReportIntegratedAdAccountAsync(bcId, startDate, endDate);
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdAccount cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedAdAccountSyncResult> SyncAllReportIntegratedAdAccountForAllBcsAsync()
        {
            return await _reportIntegratedAdAccountSyncService.SyncAllReportIntegratedAdAccountForAllBcsAsync();
        }
    }
} 