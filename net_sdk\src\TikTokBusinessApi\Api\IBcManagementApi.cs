/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API BC Management operations
    /// </summary>
    public interface IBcManagementApi
    {
        /// <summary>
        /// Get the list of Business Centers that a user has access to
        /// </summary>
        /// <param name="bcId">The Business Center ID. When not passed, returns the user's entire list of Business Centers by default</param>
        /// <param name="page">Current number of pages. Default value: 1. Value range: ≥ 1</param>
        /// <param name="pageSize">Page size. Default value: 10. Value range: 1-50</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Business Centers response containing list of Business Centers and pagination info</returns>
        Task<TikTokApiResponse<GetBusinessCentersResponse>> GetBusinessCentersAsync(
            string? bcId = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the activity log of Business Centers. The activity log records the operation history of the Business Center
        /// </summary>
        /// <param name="request">Business Center changelog request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Business Center changelog response containing activity log entries and pagination info</returns>
        Task<TikTokApiResponse<GetBusinessCenterChangelogResponse>> GetBusinessCenterChangelogAsync(
            GetBusinessCenterChangelogRequest request,
            CancellationToken cancellationToken = default);
    }
}
