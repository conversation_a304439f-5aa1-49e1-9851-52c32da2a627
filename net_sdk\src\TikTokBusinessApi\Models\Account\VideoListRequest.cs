/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for getting video list
    /// </summary>
    public class VideoListRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// Requested fields. If not set, returns the default field only
        /// </summary>
        [JsonPropertyName("fields")]
        public List<string>? Fields { get; set; }

        /// <summary>
        /// Filters to apply to the result set
        /// </summary>
        [JsonPropertyName("filters")]
        public VideoListFilters? Filters { get; set; }

        /// <summary>
        /// Cursor for pagination
        /// </summary>
        [JsonPropertyName("cursor")]
        public long? Cursor { get; set; }

        /// <summary>
        /// The maximum number of videos that will be returned for each page
        /// </summary>
        [JsonPropertyName("max_count")]
        public int? MaxCount { get; set; }
    }

    /// <summary>
    /// Filters for video list request
    /// </summary>
    public class VideoListFilters
    {
        /// <summary>
        /// A list of video IDs to filter the insights for
        /// </summary>
        [JsonPropertyName("video_ids")]
        public List<string>? VideoIds { get; set; }

        /// <summary>
        /// Whether to filter videos to include only those that have been used as Spark Ads
        /// </summary>
        [JsonPropertyName("ad_post_only")]
        public bool? AdPostOnly { get; set; }
    }
}
