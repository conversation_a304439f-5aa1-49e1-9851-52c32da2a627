/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Split Test endpoints
    /// </summary>
    public static class SplitTestEndpoints
    {
        /// <summary>
        /// Create a split test endpoint (v1.3)
        /// </summary>
        public const string CreateSplitTest = "/split_test/create/";

        /// <summary>
        /// Update the time of a split test endpoint (v1.3)
        /// </summary>
        public const string UpdateSplitTest = "/split_test/update/";

        /// <summary>
        /// End a split test endpoint (v1.3)
        /// </summary>
        public const string EndSplitTest = "/split_test/end/";

        /// <summary>
        /// Get the results of a split test endpoint (v1.3)
        /// </summary>
        public const string GetSplitTestResult = "/split_test/result/get/";

        /// <summary>
        /// Run the winning ad group endpoint (v1.3)
        /// </summary>
        public const string PromoteSplitTest = "/split_test/promote/";
    }
}
