/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response model for ad creation
    /// </summary>
    public class AdCreateResponse
    {
        /// <summary>
        /// List of created ads
        /// </summary>
        [JsonPropertyName("ads")]
        public List<AdInfo>? Ads { get; set; }

        /// <summary>
        /// List of errors that occurred during creation
        /// </summary>
        [JsonPropertyName("errors")]
        public List<AdOperationError>? Errors { get; set; }
    }

    /// <summary>
    /// Response model for ad updates
    /// </summary>
    public class AdUpdateResponse
    {
        /// <summary>
        /// List of updated ads
        /// </summary>
        [JsonPropertyName("ads")]
        public List<AdInfo>? Ads { get; set; }

        /// <summary>
        /// List of errors that occurred during update
        /// </summary>
        [JsonPropertyName("errors")]
        public List<AdOperationError>? Errors { get; set; }
    }

    /// <summary>
    /// Error information for ad operations
    /// </summary>
    public class AdOperationError
    {
        /// <summary>
        /// Error code
        /// </summary>
        [JsonPropertyName("code")]
        public string? Code { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Ad ID that caused the error (if applicable)
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// Additional error details
        /// </summary>
        [JsonPropertyName("detail")]
        public string? Detail { get; set; }
    }
}
