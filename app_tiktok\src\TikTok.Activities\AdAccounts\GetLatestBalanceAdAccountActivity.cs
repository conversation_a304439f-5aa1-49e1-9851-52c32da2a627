﻿using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using TikTok.BalanceAdAccounts;
using TikTok.Entities;
using TikTok.Repositories;
using Volo.Abp.ObjectMapping;

namespace TikTok.Activities.AdAccounts
{
    [Activity(Category = "Tài khoản quảng cáo", DisplayName = "Lấy danh sách ngân sách mới nhất tài khoản quảng cáo", Outcomes = new[] { OutcomeNames.Done })]
    public class GetLatestBalanceAdAccountActivity : UnitOfWorkActivity
    {
        private readonly ILatestBalanceAdAccountRepository _latestBalanceAdAccountRepository;
        private readonly IObjectMapper _objectMapper;

        public GetLatestBalanceAdAccountActivity(ILatestBalanceAdAccountRepository latestBalanceAdAccountRepository, IObjectMapper objectMapper)
        {
            _latestBalanceAdAccountRepository = latestBalanceAdAccountRepository;
            _objectMapper = objectMapper;
        }

        [ActivityInput(Hint = "BcId", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public string BcId { get; set; }

        [ActivityInput(Hint = "AdvertiserIds", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public List<string> AdvertiserIds { get; set; }

        [ActivityOutput(Hint = "Output")]
        public List<BalanceAdAccountDto> Output { get; set; } = new List<BalanceAdAccountDto>();

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            if (string.IsNullOrEmpty(BcId))
            {
                throw new InvalidOperationException("BcId is required.");
            }

            var queryable = (await _latestBalanceAdAccountRepository.GetQueryableAsync())
                .Where(x => x.BcId == BcId);

            if (AdvertiserIds != null && AdvertiserIds.Any())
            {
                queryable = queryable.Where(x => AdvertiserIds.Contains(x.AdvertiserId));
            }

            Output = _objectMapper.Map<List<RawLatestBalanceAdAccountEntity>,List<BalanceAdAccountDto>>(queryable.ToList());

            return Done();
        }
    }
}