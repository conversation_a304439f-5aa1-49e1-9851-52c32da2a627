using System;
using Volo.Abp.DependencyInjection;

namespace TikTok.DateTimes;

public class DateTimeService : IDateTimeService, ITransientDependency
{
    private string DefaultTimezone => TimeZoneInfo.Local.Id;
    public static string UTC_TIMEZONE => "UTC+00:00";

    public System.DateTime GetDateTimeNow(string? timezone = null)
    {
        var targetTimezone = timezone ?? DefaultTimezone;
        var utcNow = System.DateTime.UtcNow;

        return ConvertFromUtc(utcNow, targetTimezone);
    }

    public DateTimeWithTimezoneDto GetDateTimeNowWithTimezone(string? timezone = null)
    {
        var targetTimezone = timezone ?? DefaultTimezone;
        var utcNow = System.DateTime.UtcNow;
        var localDateTime = ConvertFromUtc(utcNow, targetTimezone);
        var offset = GetTimezoneOffset(targetTimezone);

        return new DateTimeWithTimezoneDto(localDateTime, targetTimezone, offset);
    }

    public System.DateTime GetDateNow(string? timezone = null)
    {
        var targetTimezone = timezone ?? DefaultTimezone;
        var utcNow = System.DateTime.UtcNow;

        return ConvertFromUtc(utcNow, targetTimezone).Date; // Chỉ trả về ngày tháng năm
    }

    public DateTimeWithTimezoneDto GetDateNowWithTimezone(string? timezone = null)
    {
        var targetTimezone = timezone ?? DefaultTimezone;
        var utcNow = System.DateTime.UtcNow;
        var localDate = ConvertFromUtc(utcNow, targetTimezone).Date;
        var offset = GetTimezoneOffset(targetTimezone);

        return new DateTimeWithTimezoneDto(localDate, targetTimezone, offset);
    }

    public DateRangeDto GetDateRangeNow(string? timezone = null)
    {
        var targetTimezone = timezone ?? DefaultTimezone;
        var currentDate = GetDateTimeNow(targetTimezone);
        var agoneDay = currentDate.AddDays(-1);

        // Lấy 23:59:59 ngày hôm qua
        var startDateTime = new System.DateTime(
            agoneDay.Year,
            agoneDay.Month,
            agoneDay.Day,
            23, 59, 59, DateTimeKind.Unspecified
        );

        // Lấy 23:59:59 ngày hôm nay
        var endDateTime = new System.DateTime(
            currentDate.Year,
            currentDate.Month,
            currentDate.Day,
            23, 59, 59, DateTimeKind.Unspecified
        );

        return new DateRangeDto(startDateTime, endDateTime);
    }

    public DateRangeWithTimezoneDto GetDateRangeNowWithTimezone(string? timezone = null)
    {
        var targetTimezone = timezone ?? DefaultTimezone;
        var currentDate = GetDateTimeNow(targetTimezone);
        var agoneDay = currentDate.AddDays(-1);
        var offset = GetTimezoneOffset(targetTimezone);

        // Lấy 23:59:59 ngày hôm qua
        var startDateTime = new System.DateTime(
            agoneDay.Year,
            agoneDay.Month,
            agoneDay.Day,
            23, 59, 59, DateTimeKind.Unspecified
        );

        // Lấy 23:59:59 ngày hôm nay
        var endDateTime = new System.DateTime(
            currentDate.Year,
            currentDate.Month,
            currentDate.Day,
            23, 59, 59, DateTimeKind.Unspecified
        );

        return new DateRangeWithTimezoneDto(startDateTime, endDateTime, targetTimezone, offset);
    }

    public System.DateTime GetDateTimeUtcNow()
    {
        return System.DateTime.UtcNow;
    }

    public DateTimeWithTimezoneDto GetDateTimeUtcNowWithTimezone()
    {
        var utcNow = System.DateTime.UtcNow;
        return new DateTimeWithTimezoneDto(utcNow, UTC_TIMEZONE, TimeSpan.Zero);
    }

    public System.DateTime GetDateUtcNow()
    {
        return System.DateTime.UtcNow.Date; // Chỉ trả về ngày tháng năm
    }

    public DateTimeWithTimezoneDto GetDateUtcNowWithTimezone()
    {
        var utcDate = System.DateTime.UtcNow.Date;
        return new DateTimeWithTimezoneDto(utcDate, UTC_TIMEZONE, TimeSpan.Zero);
    }

    public DateRangeDto GetDateRangeUtcNow()
    {
        var utcNow = GetDateTimeUtcNow();

        // Lấy 23:59:59 ngày hôm qua UTC
        var agoDay = utcNow.AddDays(-1);
        // Lấy 23:59:59 ngày hôm qua
        var startDateTime = new System.DateTime(
            agoDay.Year,
            agoDay.Month,
            agoDay.Day,
            23, 59, 59, DateTimeKind.Utc
        );
        // Lấy 23:59:59 ngày hôm nay UTC
        var endDateTime = new System.DateTime(
            utcNow.Year,
            utcNow.Month,
            utcNow.Day,
            23, 59, 59, DateTimeKind.Utc
        );

        return new DateRangeDto(startDateTime, endDateTime);
    }

    public DateRangeWithTimezoneDto GetDateRangeUtcNowWithTimezone()
    {
        var utcNow = GetDateTimeUtcNow();

        // Lấy 23:59:59 ngày hôm qua UTC
        var agoDay = utcNow.AddDays(-1);
        var startDateTime = new System.DateTime(
            agoDay.Year,
            agoDay.Month,
            agoDay.Day,
            23, 59, 59, DateTimeKind.Utc
        );
        // Lấy 23:59:59 ngày hôm nay UTC
        var endDateTime = new System.DateTime(
            utcNow.Year,
            utcNow.Month,
            utcNow.Day,
            23, 59, 59, DateTimeKind.Utc
        );

        return new DateRangeWithTimezoneDto(startDateTime, endDateTime, UTC_TIMEZONE, TimeSpan.Zero);
    }

    public DateRangeDto GetDateRangeFromDateToNow(System.DateTime startDate, string? timezone = null)
    {
        var targetTimezone = timezone ?? DefaultTimezone;
        var currentDate = GetDateTimeNow(targetTimezone);

        // Chuyển startDate về 00:00:00 theo timezone
        var startDateTime = ConvertFromUtc(startDate.Date, targetTimezone);

        // Lấy 23:59:59 ngày hiện tại theo timezone
        var endDateTime = currentDate.Date.AddHours(23).AddMinutes(59).AddSeconds(59);

        return new DateRangeDto(startDateTime, endDateTime);
    }

    public System.DateTime ConvertFromUtc(System.DateTime utcDateTime, string timezone)
    {
        try
        {
            // Xử lý các format timezone phổ biến
            if (timezone.StartsWith("UTC"))
            {
                var offsetString = timezone.Substring(3); // Lấy phần "+07:00" hoặc "-05:00" hoặc "+10:30"
                if (TryParseTimeSpan(offsetString, out var offset))
                {
                    return utcDateTime.Add(offset);
                }
            }

            // Thử parse như timezone ID (ví dụ: "Asia/Ho_Chi_Minh")
            var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timezone);
            return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, timeZoneInfo);
        }
        catch (Exception)
        {
            // Nếu không parse được, fallback về timezone của hệ thống
            var localTimeZone = TimeZoneInfo.Local;
            return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, localTimeZone);
        }
    }

    public System.DateTime ConvertToUtc(System.DateTime localDateTime, string timezone)
    {
        try
        {
            // Xử lý các format timezone phổ biến
            if (timezone.StartsWith("UTC"))
            {
                var offsetString = timezone.Substring(3); // Lấy phần "+07:00" hoặc "-05:00" hoặc "+10:30"
                if (TryParseTimeSpan(offsetString, out var offset))
                {
                    return localDateTime.Subtract(offset);
                }
            }

            // Thử parse như timezone ID (ví dụ: "Asia/Ho_Chi_Minh")
            var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timezone);
            return TimeZoneInfo.ConvertTimeToUtc(localDateTime, timeZoneInfo);
        }
        catch (Exception)
        {
            // Nếu không parse được, fallback về timezone của hệ thống
            var localTimeZone = TimeZoneInfo.Local;
            return TimeZoneInfo.ConvertTimeToUtc(localDateTime, localTimeZone);
        }
    }

    public bool IsValidTimezone(string timezone)
    {
        if (string.IsNullOrWhiteSpace(timezone))
            return false;

        try
        {
            // Kiểm tra format UTC
            if (timezone.StartsWith("UTC"))
            {
                var offsetString = timezone.Substring(3);
                return TryParseTimeSpan(offsetString, out _);
            }

            // Kiểm tra timezone ID
            TimeZoneInfo.FindSystemTimeZoneById(timezone);
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public TimeSpan GetTimezoneOffset(string timezone)
    {
        try
        {
            // Xử lý các format timezone phổ biến
            if (timezone.StartsWith("UTC"))
            {
                var offsetString = timezone.Substring(3); // Lấy phần "+07:00" hoặc "-05:00" hoặc "+10:30"
                if (TryParseTimeSpan(offsetString, out var offset))
                {
                    return offset;
                }
            }

            // Thử parse như timezone ID (ví dụ: "Asia/Ho_Chi_Minh")
            var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timezone);
            return timeZoneInfo.GetUtcOffset(System.DateTime.UtcNow);
        }
        catch (Exception)
        {
            // Nếu không parse được, fallback về timezone của hệ thống
            return TimeZoneInfo.Local.GetUtcOffset(System.DateTime.UtcNow);
        }
    }

    private bool TryParseTimeSpan(string offsetString, out TimeSpan offset)
    {
        offset = TimeSpan.Zero;

        if (string.IsNullOrWhiteSpace(offsetString))
            return false;

        // Xử lý format +/-HH:MM hoặc +/-HH:MM:SS
        if (offsetString.Length >= 3 && (offsetString[0] == '+' || offsetString[0] == '-'))
        {
            var sign = offsetString[0] == '+' ? 1 : -1;
            var timePart = offsetString.Substring(1);

            // Tách phần giờ và phút
            var parts = timePart.Split(':');
            if (parts.Length >= 2 && int.TryParse(parts[0], out var hours) && int.TryParse(parts[1], out var minutes))
            {
                var totalMinutes = hours * 60 + minutes;

                // Xử lý giây nếu có
                if (parts.Length >= 3 && int.TryParse(parts[2], out var seconds))
                {
                    totalMinutes = totalMinutes * 60 + seconds;
                    offset = TimeSpan.FromSeconds(totalMinutes * sign);
                }
                else
                {
                    offset = TimeSpan.FromMinutes(totalMinutes * sign);
                }

                return true;
            }
        }

        // Fallback: thử parse bằng TimeSpan.TryParse
        return TimeSpan.TryParse(offsetString, out offset);
    }

    public UtcDateTimeDto ConvertToUtcWithTimezone(System.DateTime localDateTime, string timezone)
    {
        var utcDateTime = ConvertToUtc(localDateTime, timezone);
        return new UtcDateTimeDto(utcDateTime);
    }

}