﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu nhóm quảng cáo (Ad Groups)
    /// </summary>
    public interface IAdGroupSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ nhóm quảng cáo theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AdGroupSyncResult> SyncAdGroupAsync(string bcId);
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu nhóm quảng cáo
    /// </summary>
    public class AdGroupSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số Advertiser đã đồng bộ
        /// </summary>
        public int AdvertiserCount { get; set; }

        /// <summary>
        /// Số Campaign đã đồng bộ
        /// </summary>
        public int CampaignCount { get; set; }

        /// <summary>
        /// Số Ad Group đã đồng bộ
        /// </summary>
        public int AdGroupCount { get; set; }
    }
}