using System;
using System.Collections.Generic;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.AdAccounts
{
    /// <summary>
    /// DTO cho tài khoản quảng cáo
    /// </summary>
    public class AdAccountDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID duy nhất của tài khoản quảng cáo
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID của Business Center sở hữu tài khoản
        /// </summary>
        public string OwnerBcId { get; set; }

        /// <summary>
        /// Trạng thái tài khoản
        /// </summary>
        public AdAccountStatus Status { get; set; }

        /// <summary>
        /// Vai trò tài khoản
        /// </summary>
        public SupporterRole Role { get; set; }

        /// <summary>
        /// Lý do bị từ chối (nếu có)
        /// </summary>
        public string RejectionReason { get; set; }

        /// <summary>
        /// Tên tài khoản quảng cáo
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Múi giờ với GMT offset hoặc định dạng "Region/City"
        /// </summary>
        public string? Timezone { get; set; }

        /// <summary>
        /// Tên múi giờ hiển thị theo định dạng "Region/City"
        /// </summary>
        public string? DisplayTimezone { get; set; }

        /// <summary>
        /// Tên công ty của tài khoản
        /// </summary>
        public string Company { get; set; }

        /// <summary>
        /// Có thể chỉnh sửa tên công ty qua API hay không
        /// </summary>
        public bool CompanyNameEditable { get; set; }

        /// <summary>
        /// Mã danh mục ngành nghề
        /// </summary>
        public string Industry { get; set; }

        /// <summary>
        /// Địa chỉ tài khoản
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// Mã quốc gia đăng ký (ví dụ: US, CN)
        /// </summary>
        public string? Country { get; set; }

        /// <summary>
        /// Loại tài khoản
        /// </summary>
        public AdAccountType AdvertiserAccountType { get; set; }

        /// <summary>
        /// Loại tiền tệ theo mã ISO 4217 (ví dụ: USD, EUR)
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Tên người liên hệ (đã che)
        /// </summary>
        public string Contacter { get; set; }

        /// <summary>
        /// Email liên hệ (đã che)
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Số điện thoại di động (đã che)
        /// </summary>
        public string CellphoneNumber { get; set; }

        /// <summary>
        /// Số điện thoại cố định (đã che)
        /// </summary>
        public string TelephoneNumber { get; set; }

        /// <summary>
        /// Mã ngôn ngữ sử dụng (ví dụ: en, zh)
        /// </summary>
        public string? Language { get; set; }

        /// <summary>
        /// Số giấy phép kinh doanh
        /// </summary>
        public string LicenseNo { get; set; }

        /// <summary>
        /// URL xem trước giấy phép (có hiệu lực 1 giờ)
        /// </summary>
        public string LicenseUrl { get; set; }

        /// <summary>
        /// Mô tả thương hiệu/công ty
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Số dư khả dụng của tài khoản
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// Thời gian tạo tài khoản (Unix timestamp)
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// Cờ đánh dấu đã bị xóa khỏi Business Center
        /// </summary>
        public bool IsRemoved { get; set; }

        /// <summary>
        /// Thời gian xóa khỏi Business Center
        /// </summary>
        public DateTime? RemovedAt { get; set; }
        /// <summary>
        /// Nếu là support được assign quản lý tài khoản thì có quyền
        /// </summary>
        public List<string> Permissions { get; set; } = new();

        /// <summary>
        /// Tên khách hàng
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// ID khách hàng
        /// </summary>
        public string? CustomerId { get; set; }
    }
}