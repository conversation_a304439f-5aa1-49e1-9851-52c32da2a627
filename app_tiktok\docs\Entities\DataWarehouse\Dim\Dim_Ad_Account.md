# DimAdAccountEntity

## Thông tin bảng dữ liệu

-   **Table**: `DimAdAccounts`
-   **TablePrefix**: `Dim_`
-   **TableName**: `Dim_DimAdAccounts`
-   **DbSchema**: `Analytics`

## Mô tả

Dimension table cho tài khoản quảng cáo, hỗ trợ SCD Type 2. Chứa thông tin cần thiết để đáp ứng YÊU CẦU 1 và YÊU CẦU 3.

## Cấu trúc bảng: DimAdAccount

| Tên Field          | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                |
| ------------------ | ------------ | -------- | ------ | ------------------------------------ |
| **Id**             | Guid         | ✓        | -      | **Kh<PERSON><PERSON> chính (Surrogate Key)**       |
| **AdvertiserId**   | string       | ✓        | 100    | **ID tài khoản quảng cáo từ TikTok** |
| **AdvertiserName** | string       | ✓        | 255    | **Tên tài khoản quảng cáo**          |
| **Currency**       | string       | ✓        | 10     | **Loại tiền tệ sử dụng**             |
| **Timezone**       | string       | ✓        | 50     | **Múi giờ tài khoản**                |
| **Country**        | string       | ✓        | 10     | **Mã quốc gia**                      |
| **Status**         | string       | ✓        | 50     | **Trạng thái tài khoản**             |
| **Company**        | string       | ✗        | 255    | **Tên công ty**                      |
| **OwnerBcId**      | string       | ✓        | 100    | **ID Business Center sở hữu**        |
| **IsActive**       | bool         | ✓        | -      | **Tài khoản còn hoạt động**          |
| **EffectiveDate**  | DateTime     | ✓        | -      | **Ngày bắt đầu hiệu lực (SCD2)**     |
| **ExpiryDate**     | DateTime     | ✗        | -      | **Ngày hết hiệu lực (SCD2)**         |
| **IsCurrent**      | bool         | ✓        | -      | **Bản ghi hiện tại (SCD2)**          |
| **RowVersion**     | int          | ✓        | -      | **Phiên bản bản ghi (SCD2)**         |

## Đặc điểm của Dimension

-   **Type**: SCD Type 2 (Slowly Changing Dimension)
-   **Business Key**: AdvertiserId
-   **Current Records**: IsCurrent = true
-   **Historical Records**: IsCurrent = false

## Quan hệ với Dimension Tables

-   **DimBusinessCenter**: N-1 (Nhiều tài khoản thuộc về một BC)

## Nguồn dữ liệu

-   **RawAdAccount**:
    -   AdvertiserId ← AdvertiserId
    -   AdvertiserName ← Name
    -   Currency ← Currency
    -   Timezone ← Timezone
    -   Country ← Country
    -   Status ← Status
    -   Company ← Company
    -   OwnerBcId ← OwnerBcId

## Business Rules

1. **SCD Type 2**: Khi thông tin thay đổi, tạo bản ghi mới và đóng bản ghi cũ
2. **Tracked Changes**: Name, Status, Company, Currency (ít khi thay đổi)
3. **Active Records**: Chỉ có một bản ghi IsCurrent = true cho mỗi AdvertiserId
4. **Soft Delete**: IsActive = false thay vì xóa hẳn

## Indexes đề xuất

-   **Primary Key**: Id
-   **Business Key Index**: AdvertiserId + IsCurrent (Unique)
-   **Performance Index**: BusinessCenterId, Country, Currency
-   **Lookup Index**: AdvertiserName

## Đáp ứng yêu cầu

### ✅ YÊU CẦU 1: Dữ liệu chi tiêu hàng ngày

**Các trường cần thiết trong file export:**

-   ✅ **Account ID**: AdvertiserId
-   ✅ **Account Name**: AdvertiserName
-   ✅ **Time Zone**: Timezone
-   ✅ **Currency**: Currency

### ✅ YÊU CẦU 3: Dashboard thống kê

**Bảng kiểm tra chi tiêu chi tiết có thể lựa chọn:**

-   ✅ **Mã khách hàng**: OwnerBcId
-   ✅ **ID tài khoản khách hàng**: AdvertiserId
-   ✅ **Tên tài khoản**: AdvertiserName

**Ví dụ query export file:**

```sql
SELECT
    d.DateFormat_DDMMYYYY as Date,
    acc.AdvertiserId as AccountID,
    acc.AdvertiserName as AccountName,
    acc.Timezone as TimeZone,
    acc.Currency,
    type.AdTypeName as AdType,
    spend.DailySpend,
    spend.NonCreditSpend as NonCreditSpending
FROM FactDailySpend spend
JOIN DimDate d ON spend.DateId = d.Id
JOIN DimAdAccounts acc ON spend.AdAccountId = acc.Id AND acc.IsCurrent = 1
JOIN DimAdType type ON spend.AdTypeKey = type.AdTypeKey
WHERE d.FullDate BETWEEN @StartDate AND @EndDate
ORDER BY d.FullDate, acc.AdvertiserName
```

## Mục đích sử dụng

-   Export file báo cáo chi tiêu hàng ngày
-   Filtering và grouping trong dashboard
-   Phân tích theo quốc gia, tiền tệ
-   Theo dõi lịch sử thay đổi thông tin tài khoản
-   Hierarchical analysis (BC -> Account)
-   Multi-timezone reporting
