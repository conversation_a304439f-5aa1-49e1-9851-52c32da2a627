# RawReportIntegratedCampaignEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawReportIntegratedCampaigns`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawReportIntegratedCampaigns`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho báo cáo tích hợp của Campaign. Lưu trữ thông tin báo cáo chi tiêu theo giờ tổng hợp của Campaign, bao gồm các chỉ số hiệu suất quảng cáo, thông tin chiến dịch, và các metrics TikTok Shop.

## Bảng dữ liệu: RawReportIntegratedCampaign

| Tên Field                | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                                                                                                                                                                                    |
| ------------------------ | ------------ | -------- | ------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Id                       | Guid         | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                                                                                                                                                    |
| AdvertiserId             | string       | ✅       | 100    | ID tài khoản nhà quảng cáo - Được hỗ trợ ở cấp độ Nhà quảng cáo, Chiến dịch, Nhóm quảng cáo và Quảng cáo                                                                                                 |
| AdvertiserName           | string       | ❌       | 255    | Tên tài khoản nhà quảng cáo                                                                                                                                                                              |
| CampaignId               | string       | ✅       | 100    | ID chiến dịch                                                                                                                                                                                            |
| Date                     | DateTime     | ✅       | -      | Ngày giờ tổng hợp báo cáo, thời gian UTC format yyyy-MM-dd HH:mm:ss (theo giờ)                                                                                                                           |
| Spend                    | decimal      | ✅       | 18,2   | Chi tiêu - Tổng chi phí quảng cáo của bạn                                                                                                                                                                |
| CashSpend                | decimal      | ✅       | 18,2   | Chi phí Thanh toán bằng Tiền mặt - Số tiền ước tính bạn đã chi cho chiến dịch được thanh toán bằng tiền mặt. **[OBSOLETE]** - Không được đồng bộ khi lấy dữ liệu theo giờ (API không hỗ trợ)             |
| VoucherSpend             | decimal      | ✅       | 18,2   | Chi phí Thanh toán bằng Phiếu giảm giá - Số tiền ước tính bạn đã chi cho chiến dịch được thanh toán bằng phiếu giảm giá. **[OBSOLETE]** - Không được đồng bộ khi lấy dữ liệu theo giờ (API không hỗ trợ) |
| Impressions              | long         | ✅       | -      | Lần hiển thị - Số lần quảng cáo của bạn được hiển thị                                                                                                                                                    |
| Clicks                   | long         | ✅       | -      | Lần nhấp (đích đến) - Số lần nhấp từ quảng cáo của bạn đến một đích đến được chỉ định                                                                                                                    |
| Conversion               | long         | ✅       | -      | Chuyển đổi - Số lần quảng cáo của bạn dẫn đến sự kiện tối ưu hóa mà bạn đã chọn                                                                                                                          |
| CostPerConversion        | decimal      | ✅       | 18,2   | Chi phí mỗi chuyển đổi - Số tiền trung bình chi cho một chuyển đổi                                                                                                                                       |
| ConversionRateV2         | decimal      | ✅       | 18,4   | Tỷ lệ chuyển đổi (CVR) - Tỷ lệ phần trăm chuyển đổi bạn nhận được trên tổng số lần hiển thị trên quảng cáo của bạn                                                                                       |
| Result                   | long         | ✅       | -      | Kết quả - Số lần quảng cáo của bạn dẫn đến kết quả dự định dựa trên mục tiêu chiến dịch và mục tiêu tối ưu hóa của bạn                                                                                   |
| CostPerResult            | decimal      | ✅       | 18,2   | Chi phí mỗi kết quả - Chi phí trung bình cho mỗi kết quả từ quảng cáo của bạn                                                                                                                            |
| CampaignName             | string       | ❌       | 255    | Tên chiến dịch - Được hỗ trợ ở cấp độ Chiến dịch, Nhóm quảng cáo và Quảng cáo                                                                                                                            |
| ObjectiveType            | string       | ❌       | 100    | Mục tiêu quảng cáo - Được hỗ trợ ở cấp độ Chiến dịch, Nhóm quảng cáo và Quảng cáo                                                                                                                        |
| CampaignDedicateType     | string       | ❌       | 100    | Loại chiến dịch - Chiến dịch Chuyên dụng iOS14 hoặc chiến dịch thông thường                                                                                                                              |
| OnsiteShoppingRoas       | decimal      | ✅       | 18,4   | ROAS (Shop) - Lợi tức trên chi phí quảng cáo (ROAS) từ tổng doanh thu TikTok Shop được ghi nhận cho quảng cáo của bạn                                                                                    |
| TotalOnsiteShoppingValue | decimal      | ✅       | 18,2   | Tổng doanh thu (Shop) - Tổng doanh thu của các đơn hàng Shop được ghi nhận cho quảng cáo của bạn                                                                                                         |
| OnsiteShopping           | long         | ✅       | -      | Mua hàng (Shop) - Số lượng hành động gửi đơn hàng Shop được ghi nhận cho quảng cáo của bạn                                                                                                               |
| CostPerOnsiteShopping    | decimal      | ✅       | 18,2   | Chi phí mỗi lần mua hàng (Shop) - Chi phí trung bình cho mỗi lần mua hàng Shop được ghi nhận cho quảng cáo của bạn                                                                                       |
| ValuePerOnsiteShopping   | decimal      | ✅       | 18,2   | Giá trị đơn hàng trung bình (Shop) - Giá trị đơn hàng trung bình cho mỗi lần mua hàng Shop được ghi nhận cho quảng cáo của bạn                                                                           |
| Currency                 | string       | ✅       | 10     | Tiền tệ - Mã tiền tệ, ví dụ: USD                                                                                                                                                                         |
| BcId                     | string       | ✅       | 100    | ID Business Center                                                                                                                                                                                       |
| CreationTime             | DateTime     | ✅       | -      | Thời gian tạo bản ghi (Audit)                                                                                                                                                                            |
| CreatorId                | Guid?        | ❌       | -      | ID người tạo (Audit)                                                                                                                                                                                     |
| LastModificationTime     | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                                                                                                                                                           |
| LastModifierId           | Guid?        | ❌       | -      | ID người sửa đổi cuối (Audit)                                                                                                                                                                            |

## Cấu trúc dữ liệu

### Thông tin định danh

-   **AdvertiserId**: ID duy nhất của tài khoản quảng cáo từ TikTok API
-   **CampaignId**: ID duy nhất của chiến dịch
-   **BcId**: ID của Business Center sở hữu tài khoản quảng cáo
-   **Date**: Ngày giờ tổng hợp báo cáo theo thời gian UTC (lưu theo giờ)

### Thông tin chiến dịch

-   **AdvertiserName**: Tên hiển thị của tài khoản quảng cáo
-   **CampaignName**: Tên hiển thị của chiến dịch
-   **ObjectiveType**: Mục tiêu quảng cáo của chiến dịch
-   **CampaignDedicateType**: Loại chiến dịch (iOS14 chuyên dụng hoặc thông thường)

### Thông tin chi phí

-   **Spend**: Tổng chi phí quảng cáo đã chi
-   **CashSpend**: Chi phí thanh toán bằng tiền mặt (**[OBSOLETE]** - Không được đồng bộ khi lấy dữ liệu theo giờ)
-   **VoucherSpend**: Chi phí thanh toán bằng phiếu giảm giá (**[OBSOLETE]** - Không được đồng bộ khi lấy dữ liệu theo giờ)
-   **Currency**: Đơn vị tiền tệ sử dụng

### Chỉ số hiệu suất cơ bản

-   **Impressions**: Số lần quảng cáo được hiển thị
-   **Clicks**: Số lần nhấp vào quảng cáo
-   **Conversion**: Số lần chuyển đổi
-   **CostPerConversion**: Chi phí cho mỗi lần chuyển đổi
-   **ConversionRateV2**: Tỷ lệ chuyển đổi (CVR)

### Chỉ số kết quả

-   **Result**: Số lần đạt kết quả dự định theo mục tiêu chiến dịch
-   **CostPerResult**: Chi phí cho mỗi kết quả

### Chỉ số TikTok Shop

-   **OnsiteShoppingRoas**: ROAS từ doanh thu TikTok Shop
-   **TotalOnsiteShoppingValue**: Tổng doanh thu TikTok Shop
-   **OnsiteShopping**: Số lần mua hàng Shop
-   **CostPerOnsiteShopping**: Chi phí cho mỗi lần mua hàng Shop
-   **ValuePerOnsiteShopping**: Giá trị đơn hàng trung bình Shop

## Mục đích sử dụng

-   Lưu trữ báo cáo tổng hợp hàng ngày cho từng chiến dịch
-   Theo dõi hiệu suất chi tiết của từng chiến dịch
-   Phân tích hiệu quả TikTok Shop và chuyển đổi
-   Đánh giá mục tiêu và loại chiến dịch
-   Cung cấp dữ liệu cho báo cáo chi tiết cấp chiến dịch
-   Hỗ trợ tối ưu hóa chiến dịch dựa trên metrics

## Mối quan hệ

-   **RawAdAccountEntity**: Liên kết với tài khoản quảng cáo (AdvertiserId)
-   **RawCampaignEntity**: Liên kết với chiến dịch (CampaignId)
-   **RawBusinessCenterEntity**: Liên kết với Business Center (BcId)
-   **RawReportIntegratedAdAccountEntity**: Báo cáo tổng hợp cấp tài khoản
-   **RawReportIntegratedAdGroupEntity**: Báo cáo chi tiết cấp nhóm quảng cáo
-   **RawReportIntegratedAdEntity**: Báo cáo chi tiết cấp quảng cáo

## Indexes

-   **Unique Index**: `(CampaignId, Date)` - Đảm bảo không có báo cáo trùng lặp cho cùng một chiến dịch và ngày
-   **Index**: `AdvertiserId` - Tối ưu truy vấn theo tài khoản quảng cáo
-   **Index**: `CampaignId` - Tối ưu truy vấn theo chiến dịch
-   **Index**: `Date` - Tối ưu truy vấn theo ngày
-   **Index**: `Spend` - Tối ưu truy vấn theo chi phí
-   **Index**: `Impressions` - Tối ưu truy vấn theo số lần hiển thị
-   **Index**: `Clicks` - Tối ưu truy vấn theo số lần nhấp
-   **Index**: `Conversion` - Tối ưu truy vấn theo số lần chuyển đổi

## Lưu ý

-   Entity này kế thừa từ AuditedEntity để có đầy đủ thông tin audit
-   Tất cả các trường decimal được cấu hình với precision 18,2 hoặc 18,4 tùy theo yêu cầu
-   Date được lưu trữ theo thời gian UTC (khác với các entity khác sử dụng timezone của BC)
-   Báo cáo được tổng hợp theo giờ và không có báo cáo trùng lặp
-   Entity này cung cấp cái nhìn chi tiết về hiệu suất quảng cáo ở cấp độ chiến dịch
-   Bao gồm thông tin mục tiêu và loại chiến dịch để hỗ trợ phân tích
-   CampaignId là khóa ngoại quan trọng để liên kết với các entity khác
-   Các trường thông tin chiến dịch có thể null để đảm bảo tính linh hoạt
-   **CashSpend** và **VoucherSpend** đã được đánh dấu **[OBSOLETE]** do API không hỗ trợ khi lấy dữ liệu theo giờ
