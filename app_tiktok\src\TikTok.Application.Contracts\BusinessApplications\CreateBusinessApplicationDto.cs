using System;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;

namespace TikTok.BusinessApplications
{
    /// <summary>
    /// DTO để tạo mới ứng dụng Business
    /// </summary>
    public class CreateBusinessApplicationDto
    {
        /// <summary>
        /// ID duy nhất của ứng dụng Business
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ApplicationId { get; set; }

        /// <summary>
        /// Khóa bí mật của ứng dụng
        /// </summary>
        [Required]
        [StringLength(255)]
        public string Secret { get; set; }

        /// <summary>
        /// Token truy cập để thực hiện các API calls
        /// </summary>
        [StringLength(500)]
        public string? AccessToken { get; set; }

        /// <summary>
        /// Thời gian tạo token truy cập
        /// </summary>
        public DateTime? AccessTokenCreatedAt { get; set; }

        /// <summary>
        /// <PERSON><PERSON> <PERSON><PERSON> bổ sung về ứng dụng
        /// </summary>
        [StringLength(1000)]
        public string? Comment { get; set; }

        /// <summary>
        /// Trạng thái hoạt động của ứng dụng
        /// </summary>
        [Required]
        public bool IsActive { get; set; }

        /// <summary>
        /// Trạng thái xử lý hiện tại của ứng dụng
        /// </summary>
        [Required]
        public BusinessApplicationStatus Status { get; set; }

        /// <summary>
        /// Dữ liệu bổ sung được lưu trữ dưới dạng JSON
        /// </summary>
        public string? Payload { get; set; }

        /// <summary>
        /// ID của Business Center mà ứng dụng này thuộc về
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// Tên của Business Center mà ứng dụng này thuộc về
        /// </summary>
        [StringLength(255)]
        public string? BcName { get; set; }
    }
} 