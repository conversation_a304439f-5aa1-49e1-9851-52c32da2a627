using System;
using System.Threading.Tasks;
using TikTok.Permissions;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.Customers
{
    /// <summary>
    /// Interface cho Customer App Service
    /// </summary>
    public interface ICustomerAppService :
        ICrudAppService<
            CustomerDto,                     // Used to show customers
            Guid,                            // Primary key of the customer entity
            GetCustomerListDto,              // Used for paging/sorting
            CreateCustomerDto,               // Used to create a new customer
            UpdateCustomerDto>               // Used to update a customer
    {
        /// <summary>
        /// Lấy danh sách khách hàng với tìm kiếm và phân trang
        /// </summary>
        /// <param name="input">Thông tin tìm kiếm và phân trang</param>
        /// <returns>Danh sách khách hàng</returns>
        Task<PagedResultDto<CustomerDto>> GetListAsync(GetCustomerListDto input);

        /// <summary>
        /// L<PERSON>y thông tin khách hàng theo ID
        /// </summary>
        /// <param name="id">ID khách hàng</param>
        /// <returns>Thông tin khách hàng</returns>
        Task<CustomerDto> GetAsync(Guid id);

        /// <summary>
        /// Tạo mới khách hàng
        /// </summary>
        /// <param name="input">Thông tin khách hàng mới</param>
        /// <returns>Thông tin khách hàng đã tạo</returns>
        Task<CustomerDto> CreateAsync(CreateCustomerDto input);

        /// <summary>
        /// Cập nhật thông tin khách hàng
        /// </summary>
        /// <param name="id">ID khách hàng</param>
        /// <param name="input">Thông tin cập nhật</param>
        /// <returns>Thông tin khách hàng đã cập nhật</returns>
        Task<CustomerDto> UpdateAsync(Guid id, UpdateCustomerDto input);

        /// <summary>
        /// Xóa khách hàng
        /// </summary>
        /// <param name="id">ID khách hàng</param>
        Task DeleteAsync(Guid id);

        /// <summary>
        /// Tìm kiếm tài khoản quảng cáo với phân trang (deprecated - moved to AdAccountAppService)
        /// </summary>
        /// <param name="input">Thông tin tìm kiếm và phân trang</param>
        /// <returns>Danh sách tài khoản quảng cáo</returns>
        Task<AdAccountSearchResultListDto> SearchAdAccountsAsync(SearchAdAccountDto input);

        /// <summary>
        /// Import danh sách khách hàng từ file Excel
        /// </summary>
        /// <param name="fileBytes">Nội dung file</param>
        /// <param name="fileName">Tên file để xác định định dạng</param>
        /// <returns>Kết quả import</returns>
        Task<ImportCustomersResultDto> ImportExcelAsync(byte[] fileBytes, string fileName);
    }
}
