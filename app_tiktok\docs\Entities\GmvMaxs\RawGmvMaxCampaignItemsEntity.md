# RawGmvMaxCampaignItemsEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawGmvMaxCampaignItems`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawGmvMaxCampaignItems`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho danh sách bài đăng TikTok (Item List) của GMV Max Campaign. Chứa thông tin về các bài đăng TikTok được ủy quyền hoặc tùy chỉnh được liên kết với chiến dịch GMV Max, bao gồm thông tin video, danh tính liên kết và danh sách sản phẩm.

## Bảng dữ liệu: RawGmvMaxCampaignItems

| Tên Field                | Kiểu dữ liệu  | Bắt buộc | Đ<PERSON> dài | <PERSON><PERSON> tả                                                            |
| ------------------------ | ------------- | -------- | ------ | ---------------------------------------------------------------- |
| Id                       | Guid          | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                            |
| CampaignId               | string        | ✅       | 100    | ID của GMV Max Campaign (Foreign Key)                            |
| ItemId                   | string        | ✅       | 100    | ID của bài đăng TikTok                                           |
| Text                     | string        | ❌       | 2000   | Caption của bài đăng TikTok                                      |
| SpuIdList                | List<string>? | ❌       | -      | Danh sách Product SPU ID được liên kết với bài đăng (JSON array) |
| IdentityId               | string        | ❌       | 100    | ID danh tính liên kết với bài đăng                               |
| IdentityType             | string        | ❌       | 50     | Loại danh tính (AUTH_CODE, TT_USER, BC_AUTH_TT, TTS_TT)          |
| IdentityAuthorizedBcId   | string        | ❌       | 100    | ID Business Center được ủy quyền                                 |
| IdentityAuthorizedShopId | string        | ❌       | 100    | ID TikTok Shop được ủy quyền                                     |
| IdentityStoreId          | string        | ❌       | 100    | ID TikTok Shop của danh tính                                     |
| ProfileImage             | string        | ❌       | -      | URL ảnh đại diện tạm thời của TikTok account                     |
| UserName                 | string        | ❌       | 100    | Username của TikTok account                                      |
| VideoId                  | string        | ❌       | 100    | ID của video                                                     |
| VideoCoverUrl            | string        | ❌       | -      | URL tạm thời của ảnh bìa video                                   |
| PreviewUrl               | string        | ❌       | -      | URL xem trước tạm thời của video                                 |
| Height                   | int?          | ❌       | -      | Chiều cao video (pixels)                                         |
| Width                    | int?          | ❌       | -      | Chiều rộng video (pixels)                                        |
| BitRate                  | long?         | ❌       | -      | Bit rate của video (bps)                                         |
| Duration                 | decimal       | ❌       | -      | Thời lượng video (giây)                                          |
| Size                     | long?         | ❌       | -      | Kích thước video (bytes)                                         |
| Signature                | string        | ❌       | -      | MD5 của video                                                    |
| Format                   | string        | ❌       | 20     | Định dạng video (ví dụ: mp4)                                     |
| Definition               | string        | ❌       | 20     | Độ phân giải video (ví dụ: 1080p)                                |
| Fps                      | int?          | ❌       | -      | Frames per second của video                                      |
| CreationTime             | DateTime      | ✅       | -      | Thời gian tạo bản ghi (Audit)                                    |
| CreatorId                | Guid?         | ❌       | -      | ID người tạo (Audit)                                             |
| LastModificationTime     | DateTime?     | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                   |
| LastModifierId           | Guid?         | ❌       | -      | ID người sửa đổi cuối (Audit)                                    |

## Cấu trúc dữ liệu

### Thông tin liên kết

-   **CampaignId**: Liên kết với GMV Max Campaign chính
-   **ItemId**: ID duy nhất của bài đăng TikTok
-   **SpuIdList**: Danh sách các sản phẩm liên kết với bài đăng

### Thông tin danh tính

-   **IdentityId**: ID của danh tính TikTok
-   **IdentityType**: Loại danh tính (người dùng, business center, shop)
-   **IdentityAuthorizedBcId**: Business Center được ủy quyền
-   **IdentityAuthorizedShopId**: TikTok Shop được ủy quyền
-   **IdentityStoreId**: TikTok Shop của danh tính

### Thông tin tài khoản

-   **ProfileImage**: Ảnh đại diện của tài khoản TikTok
-   **UserName**: Tên người dùng TikTok

### Thông tin video

-   **VideoId**: ID duy nhất của video
-   **VideoCoverUrl**: URL ảnh bìa video
-   **PreviewUrl**: URL xem trước video
-   **Text**: Caption của bài đăng

### Thuộc tính kỹ thuật video

-   **Height/Width**: Kích thước video (pixels)
-   **BitRate**: Tốc độ bit (bps)
-   **Duration**: Thời lượng (giây)
-   **Size**: Kích thước file (bytes)
-   **Signature**: MD5 hash của video
-   **Format**: Định dạng video
-   **Definition**: Độ phân giải
-   **Fps**: Số khung hình/giây

## Navigation Properties

-   **Campaign**: Liên kết tới RawGmvMaxCampaignsEntity

## Mục đích sử dụng

-   Lưu trữ thông tin chi tiết về các bài đăng TikTok trong GMV Max Campaign
-   Quản lý danh sách video và nội dung quảng cáo
-   Theo dõi liên kết giữa bài đăng và sản phẩm
-   Lưu trữ thông tin kỹ thuật của video để tối ưu hóa hiển thị
-   Hỗ trợ quản lý ủy quyền và danh tính
-   Tích hợp với TikTok Business API cho GMV Max campaigns
