using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.DimProducts
{
    public class DimProductDto : EntityDto<Guid>
    {
        /// <summary>
        /// ID sản phẩm từ TikTok API
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// Tên sản phẩm
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// <PERSON>ô tả sản phẩm
        /// </summary>
        public string? ProductDescription { get; set; }

        /// <summary>
        /// SKU sản phẩm
        /// </summary>
        public string? Sku { get; set; }

        /// <summary>
        /// Danh mục sản phẩm
        /// </summary>
        public string? Category { get; set; }

        /// <summary>
        /// Thương hiệu
        /// </summary>
        public string? Brand { get; set; }

        /// <summary>
        /// Giá sản phẩm
        /// </summary>
        public decimal? Price { get; set; }

        /// <summary>
        /// <PERSON><PERSON><PERSON> sản phẩm (USD)
        /// </summary>
        public decimal? PriceUSD { get; set; }

        /// <summary>
        /// Gi<PERSON> sản phẩm (VND)
        /// </summary>
        public decimal? PriceVND { get; set; }

        /// <summary>
        /// Trạng thái sản phẩm
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Đường link sản phẩm
        /// </summary>
        public string? ProductUrl { get; set; }

        /// <summary>
        /// Đường link hình ảnh
        /// </summary>
        public string? ImageUrl { get; set; }

        /// <summary>
        /// Ngày tạo sản phẩm
        /// </summary>
        public DateTime? CreatedDate { get; set; }

        /// <summary>
        /// Ngày cập nhật gần nhất
        /// </summary>
        public DateTime? LastModifiedDate { get; set; }
    }
}
