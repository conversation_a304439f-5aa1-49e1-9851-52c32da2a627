using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu quản lý Business Center
    /// </summary>
    public interface IBcManagementSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ Business Center theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BcManagementSyncResult> SyncBusinessCenterAsync(string bcId);

        /// <summary>
        /// Đồng bộ tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BcManagementSyncResult> SyncAllBusinessCentersAsync();

        /// <summary>
        /// Đồng bộ changelog của Business Center
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> bắt đầu (optional)</param>
        /// <param name="endDate">Ngày kết thúc (optional)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BcManagementSyncResult> SyncBusinessCenterChangelogAsync(string bcId, string? startDate = null, string? endDate = null);
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu quản lý Business Center
    /// </summary>
    public class BcManagementSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số changelog đã đồng bộ
        /// </summary>
        public int ChangelogCount { get; set; }
    }
}