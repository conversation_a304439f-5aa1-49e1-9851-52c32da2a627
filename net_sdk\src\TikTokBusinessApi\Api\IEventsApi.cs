/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Events operations
    /// </summary>
    public interface IEventsApi
    {
        #region App Events

        /// <summary>
        /// Report an app event to TikTok
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">App event request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> ReportAppEventAsync(
        //    string accessToken,
        //    AppEventRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Report app events in bulk to TikTok
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">App events batch request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response with batch operation results</returns>
        //Task<Response<EventsBatchResponse>> ReportAppEventsBatchAsync(
        //    string accessToken,
        //    AppEventBatchRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get information about an app
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="appId">App ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing app information</returns>
        Task<Response<AppInfoResponse>> GetAppInfoAsync(
            string accessToken,
            string advertiserId,
            string appId,
            CancellationToken cancellationToken = default);

        #endregion

        #region Web Events

        /// <summary>
        /// Report a web event to TikTok
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Web event request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> ReportWebEventAsync(
        //    string accessToken,
        //    WebEventRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Report web events in bulk to TikTok
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Web events batch request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response with batch operation results</returns>
        //Task<Response<EventsBatchResponse>> ReportWebEventsBatchAsync(
        //    string accessToken,
        //    WebEventBatchRequest request,
        //    CancellationToken cancellationToken = default);

        #endregion

        #region Offline Events

        /// <summary>
        /// Create a new offline event set
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Create offline event set request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created event set ID</returns>
        //Task<Response<CreateOfflineEventSetResponse>> CreateOfflineEventSetAsync(
        //    string accessToken,
        //    CreateOfflineEventSetRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update an existing offline event set
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Update offline event set request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> UpdateOfflineEventSetAsync(
        //    string accessToken,
        //    UpdateOfflineEventSetRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get offline event sets
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Get offline event sets request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing offline event sets</returns>
        Task<Response<GetOfflineEventSetsResponse>> GetOfflineEventSetsAsync(
            string accessToken,
            GetOfflineEventSetsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete an offline event set
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Delete offline event set request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> DeleteOfflineEventSetAsync(
        //    string accessToken,
        //    DeleteOfflineEventSetRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Report offline events to TikTok
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Report offline events request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> ReportOfflineEventsAsync(
        //    string accessToken,
        //    ReportOfflineEventsRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Report offline events in bulk to TikTok
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Report offline events batch request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response with batch operation results</returns>
        //Task<Response<EventsBatchResponse>> ReportOfflineEventsBatchAsync(
        //    string accessToken,
        //    ReportOfflineEventsBatchRequest request,
        //    CancellationToken cancellationToken = default);

        #endregion
    }
}
