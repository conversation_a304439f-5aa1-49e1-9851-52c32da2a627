/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for creating automated rules
    /// </summary>
    public class AutomatedRulesCreateBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// List of rules to create
        /// </summary>
        [JsonPropertyName("rules")]
        public List<AutomatedRule> Rules { get; set; } = new List<AutomatedRule>();

        /// <summary>
        /// Language of the error messages
        /// </summary>
        [JsonPropertyName("lang")]
        public string? Lang { get; set; }
    }

    /// <summary>
    /// Request body for updating automated rules
    /// </summary>
    public class AutomatedRulesUpdateBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// List of rules to update
        /// </summary>
        [JsonPropertyName("rules")]
        public List<AutomatedRuleUpdate> Rules { get; set; } = new List<AutomatedRuleUpdate>();

        /// <summary>
        /// Language of the error messages
        /// </summary>
        [JsonPropertyName("lang")]
        public string? Lang { get; set; }
    }

    /// <summary>
    /// Request body for updating rule statuses
    /// </summary>
    public class AutomatedRulesStatusUpdateBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// IDs of the rules to update status
        /// </summary>
        [JsonPropertyName("rule_ids")]
        public List<string> RuleIds { get; set; } = new List<string>();

        /// <summary>
        /// Operation type (TURN_ON, TURN_OFF, DELETE)
        /// </summary>
        [JsonPropertyName("operate_type")]
        public string OperateType { get; set; } = string.Empty;

        /// <summary>
        /// Language of the response error message
        /// </summary>
        [JsonPropertyName("lang")]
        public string? Lang { get; set; }
    }

    /// <summary>
    /// Request body for binding/unbinding rules
    /// </summary>
    public class AutomatedRulesBindBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Information about binding
        /// </summary>
        [JsonPropertyName("bind_info")]
        public List<RuleBindInfo> BindInfo { get; set; } = new List<RuleBindInfo>();

        /// <summary>
        /// Language of the returned messages
        /// </summary>
        [JsonPropertyName("lang")]
        public string? Lang { get; set; }
    }

    /// <summary>
    /// Request body for getting result details
    /// </summary>
    public class AutomatedRulesResultDetailsBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Result details
        /// </summary>
        [JsonPropertyName("result_detail")]
        public List<ResultDetailRequest> ResultDetail { get; set; } = new List<ResultDetailRequest>();

        /// <summary>
        /// Language of the execution results
        /// </summary>
        [JsonPropertyName("lang")]
        public string? Lang { get; set; }
    }

    /// <summary>
    /// Automated rule definition
    /// </summary>
    public class AutomatedRule
{
        /// <summary>
        /// Objects that you want to apply the rules to
        /// </summary>
        [JsonPropertyName("apply_objects")]
        public List<RuleApplyObject> ApplyObjects { get; set; } = new List<RuleApplyObject>();

        /// <summary>
        /// Conditions for the rule
        /// </summary>
        [JsonPropertyName("conditions")]
        public List<RuleCondition> Conditions { get; set; } = new List<RuleCondition>();

        /// <summary>
        /// Actions to be taken when the condition is met
        /// </summary>
        [JsonPropertyName("actions")]
        public List<RuleAction> Actions { get; set; } = new List<RuleAction>();

        /// <summary>
        /// Notification settings
        /// </summary>
        [JsonPropertyName("notification")]
        public RuleNotification Notification { get; set; } = new RuleNotification();

        /// <summary>
        /// Rule execution settings
        /// </summary>
        [JsonPropertyName("rule_exec_info")]
        public RuleExecutionInfo RuleExecInfo { get; set; } = new RuleExecutionInfo();

        /// <summary>
        /// User timezone
        /// </summary>
        [JsonPropertyName("tzone")]
        public string? Tzone { get; set; }

        /// <summary>
        /// Name of the rule
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;
    }

    /// <summary>
    /// Automated rule update definition (extends AutomatedRule with rule_id)
    /// </summary>
    public class AutomatedRuleUpdate : AutomatedRule
    {
        /// <summary>
        /// ID of the rule to update
        /// </summary>
        [JsonPropertyName("rule_id")]
        public string RuleId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Objects that rules apply to
    /// </summary>
    public class RuleApplyObject
    {
        /// <summary>
        /// Category of the object (CAMPAIGN, ADGROUP, AD)
        /// </summary>
        [JsonPropertyName("dimension")]
        public string Dimension { get; set; } = string.Empty;

        /// <summary>
        /// IDs of the objects
        /// </summary>
        [JsonPropertyName("dimension_ids")]
        public List<string>? DimensionIds { get; set; }

        /// <summary>
        /// Conditions to filter objects
        /// </summary>
        [JsonPropertyName("pre_condition_type")]
        public string PreConditionType { get; set; } = string.Empty;

        /// <summary>
        /// Bind type of the rule (BIND, UNBIND) - used in responses
        /// </summary>
        [JsonPropertyName("bind_type")]
        public string? BindType { get; set; }
    }

    /// <summary>
    /// Rule condition definition
    /// </summary>
    public class RuleCondition
    {
        /// <summary>
        /// Subject that the condition will be based on
        /// </summary>
        [JsonPropertyName("subject_type")]
        public string SubjectType { get; set; } = string.Empty;

        /// <summary>
        /// Time range for the condition
        /// </summary>
        [JsonPropertyName("range_type")]
        public string? RangeType { get; set; }

        /// <summary>
        /// Operator to connect a condition with a value
        /// </summary>
        [JsonPropertyName("match_type")]
        public string MatchType { get; set; } = string.Empty;

        /// <summary>
        /// Values for the condition
        /// </summary>
        [JsonPropertyName("values")]
        public List<string> Values { get; set; } = new List<string>();

        /// <summary>
        /// Calculation type (ALL_OBJECTS, OF_EACH_OBJECT)
        /// </summary>
        [JsonPropertyName("calculation_type")]
        public string? CalculationType { get; set; }
    }

    /// <summary>
    /// Rule action definition
    /// </summary>
    public class RuleAction
    {
        /// <summary>
        /// Type of action to take
        /// </summary>
        [JsonPropertyName("subject_type")]
        public string SubjectType { get; set; } = string.Empty;

        /// <summary>
        /// Action type (INCREASE, DECREASE, ADJUST_TO)
        /// </summary>
        [JsonPropertyName("action_type")]
        public string? ActionType { get; set; }

        /// <summary>
        /// Value type (EXACT, PERCENT)
        /// </summary>
        [JsonPropertyName("value_type")]
        public string? ValueType { get; set; }

        /// <summary>
        /// Budget value settings
        /// </summary>
        [JsonPropertyName("value")]
        public RuleActionValue? Value { get; set; }

        /// <summary>
        /// Frequency settings
        /// </summary>
        [JsonPropertyName("frequency_info")]
        public RuleFrequencyInfo? FrequencyInfo { get; set; }
    }

    /// <summary>
    /// Rule action value definition
    /// </summary>
    public class RuleActionValue
    {
        /// <summary>
        /// The budget value
        /// </summary>
        [JsonPropertyName("value")]
        public float? Value { get; set; }

        /// <summary>
        /// The maximum/minimum budget limit
        /// </summary>
        [JsonPropertyName("limit")]
        public float? Limit { get; set; }

        /// <summary>
        /// Whether to use limit
        /// </summary>
        [JsonPropertyName("use_limit")]
        public bool? UseLimit { get; set; }
    }

    /// <summary>
    /// Rule frequency information
    /// </summary>
    public class RuleFrequencyInfo
    {
        /// <summary>
        /// Frequency type
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Custom frequency type
        /// </summary>
        [JsonPropertyName("custom_frequency_type")]
        public string? CustomFrequencyType { get; set; }

        /// <summary>
        /// Time value for custom frequency
        /// </summary>
        [JsonPropertyName("time")]
        public int? Time { get; set; }

        /// <summary>
        /// Count value for custom frequency
        /// </summary>
        [JsonPropertyName("count")]
        public int? Count { get; set; }
    }

    /// <summary>
    /// Rule notification settings
    /// </summary>
    public class RuleNotification
    {
        /// <summary>
        /// Notification type
        /// </summary>
        [JsonPropertyName("notification_type")]
        public string NotificationType { get; set; } = string.Empty;

        /// <summary>
        /// Email notification settings
        /// </summary>
        [JsonPropertyName("email_setting")]
        public RuleEmailSetting? EmailSetting { get; set; }
    }

    /// <summary>
    /// Rule email notification settings
    /// </summary>
    public class RuleEmailSetting
    {
        /// <summary>
        /// Notification period
        /// </summary>
        [JsonPropertyName("notification_period")]
        public string? NotificationPeriod { get; set; }

        /// <summary>
        /// Email execution time
        /// </summary>
        [JsonPropertyName("email_exec_time")]
        public List<string>? EmailExecTime { get; set; }

        /// <summary>
        /// No result notification flag
        /// </summary>
        [JsonPropertyName("no_result_notification")]
        public bool? NoResultNotification { get; set; }

        /// <summary>
        /// Mute option
        /// </summary>
        [JsonPropertyName("mute_option")]
        public string? MuteOption { get; set; }
    }

    /// <summary>
    /// Rule execution information
    /// </summary>
    public class RuleExecutionInfo
    {
        /// <summary>
        /// Execution time type
        /// </summary>
        [JsonPropertyName("exec_time_type")]
        public string ExecTimeType { get; set; } = string.Empty;

        /// <summary>
        /// Execution time
        /// </summary>
        [JsonPropertyName("exec_time")]
        public string? ExecTime { get; set; }

        /// <summary>
        /// Time period information
        /// </summary>
        [JsonPropertyName("time_period_info")]
        public List<RuleTimePeriodInfo>? TimePeriodInfo { get; set; }
    }

    /// <summary>
    /// Rule time period information
    /// </summary>
    public class RuleTimePeriodInfo
    {
        /// <summary>
        /// Numbers representing days
        /// </summary>
        [JsonPropertyName("num")]
        public List<int>? Num { get; set; }

        /// <summary>
        /// Start time
        /// </summary>
        [JsonPropertyName("start_time")]
        public string? StartTime { get; set; }

        /// <summary>
        /// End time
        /// </summary>
        [JsonPropertyName("end_time")]
        public string? EndTime { get; set; }

        /// <summary>
        /// Date type
        /// </summary>
        [JsonPropertyName("date_type")]
        public string? DateType { get; set; }
    }

    /// <summary>
    /// Rule bind information
    /// </summary>
    public class RuleBindInfo
    {
        /// <summary>
        /// ID of the rule
        /// </summary>
        [JsonPropertyName("rule_id")]
        public string RuleId { get; set; } = string.Empty;

        /// <summary>
        /// Category of the object
        /// </summary>
        [JsonPropertyName("dimension")]
        public string Dimension { get; set; } = string.Empty;

        /// <summary>
        /// IDs of the objects
        /// </summary>
        [JsonPropertyName("dimension_ids")]
        public List<string> DimensionIds { get; set; } = new List<string>();

        /// <summary>
        /// Bind type (BIND, UNBIND)
        /// </summary>
        [JsonPropertyName("bind_type")]
        public string BindType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Result detail request
    /// </summary>
    public class ResultDetailRequest
    {
        /// <summary>
        /// Rule execution ID
        /// </summary>
        [JsonPropertyName("exec_id")]
        public string ExecId { get; set; } = string.Empty;

        /// <summary>
        /// Rule ID
        /// </summary>
        [JsonPropertyName("rule_id")]
        public string RuleId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Automated rules filtering options
    /// </summary>
    public class AutomatedRulesFiltering
    {
        /// <summary>
        /// Status of rules
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Rule IDs or rule names
        /// </summary>
        [JsonPropertyName("rule_info")]
        public List<string>? RuleInfo { get; set; }

        /// <summary>
        /// Data dimension
        /// </summary>
        [JsonPropertyName("data_dimension")]
        public string? DataDimension { get; set; }

        /// <summary>
        /// Filter by action type (for rule results)
        /// </summary>
        [JsonPropertyName("action")]
        public string? Action { get; set; }

        /// <summary>
        /// Time range filter (for rule results)
        /// </summary>
        [JsonPropertyName("time")]
        public List<string>? Time { get; set; }
    }

    /// <summary>
    /// Response for creating/updating automated rules
    /// </summary>
    public class AutomatedRulesResponse
    {
        /// <summary>
        /// IDs of the rules that have been created or updated
        /// </summary>
        [JsonPropertyName("rule_ids")]
        public List<string> RuleIds { get; set; } = new List<string>();
    }

    /// <summary>
    /// Response for getting automated rules
    /// </summary>
    public class AutomatedRulesGetResponse
    {
        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }

        /// <summary>
        /// List of rules
        /// </summary>
        [JsonPropertyName("rules")]
        public List<AutomatedRuleInfo> Rules { get; set; } = new List<AutomatedRuleInfo>();
    }

    /// <summary>
    /// Automated rule information (response model)
    /// </summary>
    public class AutomatedRuleInfo
    {
        /// <summary>
        /// Objects that the rule applies to
        /// </summary>
        [JsonPropertyName("apply_objects")]
        public List<RuleApplyObject> ApplyObjects { get; set; } = new List<RuleApplyObject>();

        /// <summary>
        /// Rule conditions
        /// </summary>
        [JsonPropertyName("conditions")]
        public List<RuleCondition> Conditions { get; set; } = new List<RuleCondition>();

        /// <summary>
        /// Rule actions
        /// </summary>
        [JsonPropertyName("actions")]
        public List<RuleAction> Actions { get; set; } = new List<RuleAction>();

        /// <summary>
        /// Notification settings
        /// </summary>
        [JsonPropertyName("notification")]
        public RuleNotification? Notification { get; set; }

        /// <summary>
        /// Rule execution information
        /// </summary>
        [JsonPropertyName("rule_exec_info")]
        public RuleExecutionInfo? RuleExecInfo { get; set; }

        /// <summary>
        /// Last check result summary
        /// </summary>
        [JsonPropertyName("last_check_result_summary")]
        public RuleCheckResultSummary? LastCheckResultSummary { get; set; }

        /// <summary>
        /// Rule ID
        /// </summary>
        [JsonPropertyName("rule_id")]
        public string? RuleId { get; set; }

        /// <summary>
        /// Rule name
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Rule status
        /// </summary>
        [JsonPropertyName("rule_status")]
        public string? RuleStatus { get; set; }

        /// <summary>
        /// Creation date and time
        /// </summary>
        [JsonPropertyName("create_datetime")]
        public string? CreateDatetime { get; set; }

        /// <summary>
        /// Rule check result (for rule results response)
        /// </summary>
        [JsonPropertyName("rule_check_result")]
        public RuleCheckResultSummary? RuleCheckResult { get; set; }
    }

    /// <summary>
    /// Rule check result summary
    /// </summary>
    public class RuleCheckResultSummary
    {
        /// <summary>
        /// Date and time when the rule was checked
        /// </summary>
        [JsonPropertyName("check_datetime")]
        public string? CheckDatetime { get; set; }

        /// <summary>
        /// Date and time when the rule was executed
        /// </summary>
        [JsonPropertyName("exec_datetime")]
        public string? ExecDatetime { get; set; }

        /// <summary>
        /// Number of objects that have been changed
        /// </summary>
        [JsonPropertyName("change_success")]
        public int? ChangeSuccess { get; set; }

        /// <summary>
        /// Number of objects that have not been changed
        /// </summary>
        [JsonPropertyName("no_change")]
        public int? NoChange { get; set; }

        /// <summary>
        /// Number of objects that failed to be changed
        /// </summary>
        [JsonPropertyName("change_fail")]
        public int? ChangeFail { get; set; }

        /// <summary>
        /// Rule execution ID
        /// </summary>
        [JsonPropertyName("task_exec_id")]
        public string? TaskExecId { get; set; }
    }

    /// <summary>
    /// Response for getting result details
    /// </summary>
    public class AutomatedRulesResultDetailsResponse
    {
        /// <summary>
        /// Result details
        /// </summary>
        [JsonPropertyName("result_details")]
        public List<RuleResultDetail> ResultDetails { get; set; } = new List<RuleResultDetail>();
    }

    /// <summary>
    /// Rule result detail
    /// </summary>
    public class RuleResultDetail
    {
        /// <summary>
        /// Rule ID
        /// </summary>
        [JsonPropertyName("rule_id")]
        public string? RuleId { get; set; }

        /// <summary>
        /// Rule execution ID
        /// </summary>
        [JsonPropertyName("exec_id")]
        public string? ExecId { get; set; }

        /// <summary>
        /// Action subject type
        /// </summary>
        [JsonPropertyName("action_subject_type")]
        public string? ActionSubjectType { get; set; }

        /// <summary>
        /// Action status
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Error message if action failed
        /// </summary>
        [JsonPropertyName("error_message")]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Category of the object
        /// </summary>
        [JsonPropertyName("object_dimension")]
        public string? ObjectDimension { get; set; }

        /// <summary>
        /// Object ID
        /// </summary>
        [JsonPropertyName("object_dimension_id")]
        public string? ObjectDimensionId { get; set; }

        /// <summary>
        /// Object name
        /// </summary>
        [JsonPropertyName("object_name")]
        public string? ObjectName { get; set; }
    }
}
