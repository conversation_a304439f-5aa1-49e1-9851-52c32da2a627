using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service interface cho việc đồng bộ dữ liệu báo cáo GMV Max Live Detail Livestream
    /// </summary>
    public interface IGmvMaxLiveLivestreamSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Detail Livestream theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> bắ<PERSON> đầ<PERSON> (tùy chọn)</param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxLiveLivestreamSyncResult> SyncGmvMaxLiveLivestreamAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Detail Livestream cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxLiveLivestreamSyncResult> SyncAllGmvMaxLiveLivestreamForAllBcsAsync();
    }
}
