# RawAssetEntity

## Tổng quan

Entity đại diện cho tài sản (Asset) trong hệ thống TikTok Business. Entity này lưu trữ thông tin về các tài sản quảng cáo, bao gồm Ad Accounts, Catalogs, TikTok Shops và TikTok Accounts được quản lý bởi Business Center.

## Thông tin cơ bản

-   **Namespace**: `TikTok.Entities`
-   **Kế thừa**: `AuditedEntity<Guid>`
-   **Mục đích**: Lưu trữ dữ liệu tài sản từ TikTok Business API

## Cấu trúc dữ liệu

### Properties

| Property                | Type                   | Required | Max Length | Mô tả                                                            |
| ----------------------- | ---------------------- | -------- | ---------- | ---------------------------------------------------------------- |
| `AssetId`               | `string`               | ✅       | 100        | ID duy nhất của tài sản                                          |
| `AssetName`             | `string`               | ✅       | 255        | Tên của tài sản                                                  |
| `AssetType`             | `AssetType`            | ✅       | -          | Loại tài sản (Ad Account, Catalog, TikTok Shop, TikTok Account)  |
| `BcId`                  | `string`               | ✅       | 100        | ID của Business Center sở hữu tài sản                            |
| `AdvertiserAccountType` | `AdAccountType?`       | ❌       | -          | Loại tài khoản quảng cáo                                         |
| `AdvertiserRole`        | `AdvertiserRole?`      | ❌       | -          | Quyền của người dùng Business Center đối với tài khoản quảng cáo |
| `CatalogRole`           | `CatalogRole?`         | ❌       | -          | Quyền của người dùng Business Center đối với danh mục            |
| `AdCreationEligible`    | `AdCreationEligible?`  | ❌       | -          | Cho biết liệu danh mục có thể được sử dụng trong quảng cáo       |
| `StoreRole`             | `StoreRole?`           | ❌       | -          | Quyền của người dùng Business Center đối với TikTok Shop         |
| `TtAccountRoles`        | `List<TtAccountRole>?` | ❌       | -          | Quyền của người dùng Business Center đối với tài khoản TikTok    |
| `OwnerBcName`           | `string?`              | ❌       | 255        | Tên của Business Center sở hữu tài sản này                       |
| `IsRemoved`             | `bool`                 | ❌       | -          | Cờ đánh dấu đã bị xóa khỏi Business Center                       |
|| `RemovedAt`             | `DateTime?`            | ❌       | -          | Thời gian xóa khỏi Business Center (UTC)                         |
|| `RelationType`          | `RelationType?`        | ❌       | -          | Loại quan hệ giữa Business Center và tài sản                     |
|| `RelationStatus`        | `RelationStatus?`      | ❌       | -          | Trạng thái quan hệ giữa Business Center và tài sản               |
|| `AdvertiserStatus`      | `AdAccountStatus?`     | ❌       | -          | Trạng thái tài khoản quảng cáo                                    |

### Properties kế thừa từ AuditedEntity

-   `Id` (Guid): Khóa chính
-   `CreationTime` (DateTime): Thời gian tạo
-   `CreatorId` (Guid?): ID người tạo
-   `LastModificationTime` (DateTime?): Thời gian cập nhật cuối
-   `LastModifierId` (Guid?): ID người cập nhật cuối

## Constructors

### Constructor mặc định

```csharp
public RawAssetEntity()
```

**Đặc điểm:**

-   Khởi tạo `TtAccountRoles` với một `List<TtAccountRole>` rỗng

### Constructor với ID

```csharp
public RawAssetEntity(Guid id)
```

**Tham số:**

-   `id` (Guid): ID của entity

**Đặc điểm:**

-   Khởi tạo `TtAccountRoles` với một `List<TtAccountRole>` rỗng

## Methods

### HasIsChanged

```csharp
public bool HasIsChanged(RawAssetEntity other)
```

**Mục đích:** Kiểm tra xem entity có thay đổi so với entity khác không

**Tham số:**

-   `other` (RawAssetEntity): Entity khác để so sánh

**Trả về:**

-   `bool`: True nếu có thay đổi, False nếu không có thay đổi

**Mô tả:** So sánh tất cả các properties của entity hiện tại với entity khác, bao gồm cả danh sách `TtAccountRoles`.

### UpdateFrom

```csharp
public void UpdateFrom(RawAssetEntity source)
```

**Mục đích:** Cập nhật dữ liệu từ entity khác

**Tham số:**

-   `source` (RawAssetEntity): Entity nguồn để cập nhật dữ liệu

**Mô tả:** Sao chép tất cả các giá trị từ entity nguồn sang entity hiện tại, bao gồm cả việc tạo bản sao mới của danh sách `TtAccountRoles`.

## Đặc điểm quan trọng

### Khóa chính

Entity này sử dụng `Id` (Guid) làm khóa chính, được kế thừa từ `AuditedEntity<Guid>`.

### Validation

-   Các trường bắt buộc có attribute `[Required]`
-   Các trường string có giới hạn độ dài tối đa
-   `AssetId` và `BcId` có giới hạn 100 ký tự
-   `AssetName` và `OwnerBcName` có giới hạn 255 ký tự

### Quản lý trạng thái xóa

-   `IsRemoved`: Boolean flag để đánh dấu tài sản đã bị xóa
-   `RemovedAt`: Timestamp khi tài sản bị xóa (UTC)

### Quan hệ và trạng thái

Entity này lưu trữ thông tin về quan hệ giữa Business Center và tài sản:

-   **RelationType**: Loại quan hệ sở hữu (OWNER_BC, OWNER_PARTNER, OWNER_INDIVIDUAL)
-   **RelationStatus**: Trạng thái quan hệ (BOUND, UNBOUND, PENDING, REJECTED) - null cho CATALOG, LEAD, TT_ACCOUNT
-   **AdvertiserStatus**: Trạng thái tài khoản quảng cáo (sử dụng enum AdAccountStatus)

### Quyền và vai trò

Entity này lưu trữ nhiều loại quyền khác nhau tùy thuộc vào loại tài sản:

-   **AdvertiserRole**: Quyền đối với Ad Account
-   **CatalogRole**: Quyền đối với Catalog
-   **StoreRole**: Quyền đối với TikTok Shop (AD_PROMOTION)
-   **TtAccountRoles**: Danh sách quyền đối với TikTok Accounts

## Sử dụng trong hệ thống

### Mục đích chính

1. **Quản lý tài sản**: Lưu trữ thông tin về tất cả tài sản trong Business Center
2. **Phân quyền**: Theo dõi quyền của người dùng đối với từng loại tài sản
3. **Tích hợp API**: Đồng bộ dữ liệu tài sản từ TikTok Business API
4. **Báo cáo**: Cung cấp dữ liệu cho các báo cáo về tài sản

### Tích hợp với API

Entity này thường được sử dụng trong:

-   `AssetSyncService`: Đồng bộ dữ liệu tài sản từ TikTok API
-   Các service quản lý quyền và vai trò
-   Background jobs đồng bộ dữ liệu tài sản

### Các loại tài sản được hỗ trợ

| AssetType      | Mô tả               | Quyền liên quan                       |
| -------------- | ------------------- | ------------------------------------- |
| Ad Account     | Tài khoản quảng cáo | AdvertiserRole, AdvertiserAccountType |
| Catalog        | Danh mục sản phẩm   | CatalogRole, AdCreationEligible       |
| TikTok Shop    | Cửa hàng TikTok     | StoreRole                             |
| TikTok Account | Tài khoản TikTok    | TtAccountRoles                        |

## Ví dụ sử dụng

```csharp
// Tạo entity Ad Account
var adAccountAsset = new RawAssetEntity
{
    AssetId = "AD_ACCOUNT_123",
    AssetName = "My Ad Account",
    AssetType = AssetType.AdAccount,
    BcId = "BC_456",
    AdvertiserAccountType = AdAccountType.Business,
    AdvertiserRole = AdvertiserRole.Admin,
    AdvertiserStatus = AdAccountStatus.STATUS_ENABLE,
    RelationType = RelationType.OWNER_BC,
    RelationStatus = RelationStatus.BOUND,
    OwnerBcName = "My Business Center"
};

// Tạo entity Catalog
var catalogAsset = new RawAssetEntity
{
    AssetId = "CATALOG_789",
    AssetName = "Product Catalog",
    AssetType = AssetType.Catalog,
    BcId = "BC_456",
    CatalogRole = CatalogRole.Admin,
    AdCreationEligible = AdCreationEligible.Yes
};

// Tạo entity với ID
var assetWithId = new RawAssetEntity(Guid.NewGuid())
{
    AssetId = "TIKTOK_SHOP_101",
    AssetName = "My TikTok Shop",
    AssetType = AssetType.TikTokShop,
    BcId = "BC_456",
    StoreRole = StoreRole.Owner
};

// Đánh dấu tài sản đã bị xóa
adAccountAsset.IsRemoved = true;
adAccountAsset.RemovedAt = DateTime.UtcNow;

// Sử dụng các method mới
var originalAsset = new RawAssetEntity
{
    AssetId = "AD_ACCOUNT_123",
    AssetName = "My Ad Account",
    AssetType = AssetType.AdAccount,
    BcId = "BC_456"
};

var updatedAsset = new RawAssetEntity
{
    AssetId = "AD_ACCOUNT_123",
    AssetName = "Updated Ad Account",
    AssetType = AssetType.AdAccount,
    BcId = "BC_456"
};

// Kiểm tra thay đổi
bool hasChanged = originalAsset.HasIsChanged(updatedAsset); // true

// Cập nhật dữ liệu
originalAsset.UpdateFrom(updatedAsset);
hasChanged = originalAsset.HasIsChanged(updatedAsset); // false
```

## Lưu ý khi phát triển

1. **Quản lý quyền**: Cần xử lý logic phân quyền dựa trên loại tài sản
2. **Soft Delete**: Sử dụng `IsRemoved` và `RemovedAt` để thực hiện soft delete
3. **Validation**: Kiểm tra tính hợp lệ của dữ liệu trước khi lưu trữ
4. **Synchronization**: Đảm bảo đồng bộ dữ liệu với TikTok API định kỳ
5. **Performance**: Cân nhắc indexing cho các trường thường query như `BcId`, `AssetType`

## Quan hệ với các Entity khác

-   **BusinessCenterEntity**: Thông tin về Business Center sở hữu tài sản
-   **RawAdAccountEntity**: Chi tiết về Ad Account
-   **RawCampaignEntity**: Chi tiết về Campaign (liên quan đến Ad Account)
-   **RawAssetEntity**: Có thể có quan hệ parent-child với các tài sản con

## Workflow xử lý

1. **Thu thập dữ liệu**: Từ TikTok Business API thông qua `AssetSyncService`
2. **Phân loại**: Xác định loại tài sản và quyền tương ứng
3. **Lưu trữ**: Lưu vào database với thông tin đầy đủ
4. **Cập nhật**: Đồng bộ định kỳ để cập nhật trạng thái và quyền
5. **Soft Delete**: Đánh dấu xóa thay vì xóa thực sự khi tài sản bị loại bỏ

## Các trường hợp sử dụng đặc biệt

### Quản lý quyền phức tạp

```csharp
// Kiểm tra quyền đối với Ad Account
if (asset.AssetType == AssetType.AdAccount &&
    asset.AdvertiserRole == AdvertiserRole.Admin)
{
    // Có quyền admin đối với Ad Account
}

// Kiểm tra quyền đối với Catalog
if (asset.AssetType == AssetType.Catalog &&
    asset.CatalogRole == CatalogRole.Admin &&
    asset.AdCreationEligible == AdCreationEligible.Yes)
{
    // Có quyền admin và có thể tạo quảng cáo từ catalog
}
```

### Xử lý soft delete

```csharp
// Lọc tài sản chưa bị xóa
var activeAssets = assets.Where(a => !a.IsRemoved);

// Lọc tài sản đã bị xóa trong khoảng thời gian
var recentlyRemovedAssets = assets.Where(a =>
    a.IsRemoved &&
    a.RemovedAt >= DateTime.UtcNow.AddDays(-30));
```
