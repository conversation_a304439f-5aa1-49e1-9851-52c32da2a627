/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for hashtag suggestions
    /// </summary>
    public class HashtagSuggestionResponse
    {
        /// <summary>
        /// List of hashtag suggestions
        /// </summary>
        [JsonPropertyName("suggestions")]
        public List<HashtagSuggestion> Suggestions { get; set; } = new List<HashtagSuggestion>();
    }

    /// <summary>
    /// Individual hashtag suggestion
    /// </summary>
    public class HashtagSuggestion
    {
        /// <summary>
        /// The name of the recommended hashtag
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The number of views that the recommended hashtag has received
        /// </summary>
        [JsonPropertyName("view_count")]
        public long ViewCount { get; set; }
    }
}
