/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Playable Ads endpoints
    /// </summary>
    public static class PlayableAdsEndpoints
    {
        /// <summary>
        /// Upload a playable creative endpoint (v1.3)
        /// </summary>
        public const string UploadPlayable = "/playable/upload/";

        /// <summary>
        /// Check the status of a playable creative endpoint (v1.3)
        /// </summary>
        public const string ValidatePlayable = "/playable/validate/";

        /// <summary>
        /// Save a playable creative endpoint (v1.3)
        /// </summary>
        public const string SavePlayable = "/playable/save/";

        /// <summary>
        /// Get playable creatives endpoint (v1.3)
        /// </summary>
        public const string GetPlayables = "/playable/get/";

        /// <summary>
        /// Delete a playable creative endpoint (v1.3)
        /// </summary>
        public const string DeletePlayable = "/playable/delete/";
    }
}
