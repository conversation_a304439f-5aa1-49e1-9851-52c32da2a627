/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Ad operations
    /// </summary>
    public interface IAdApi
    {
        /// <summary>
        /// Get ads with optional filtering and pagination
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="fields">Fields to return (optional)</param>
        /// <param name="excludeFieldTypesInResponse">Field types to exclude from response (optional)</param>
        /// <param name="filtering">Filtering criteria (optional)</param>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10, max: 1000)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad get response containing list of ads and pagination info</returns>
        Task<AdGetResponse> GetAdsAsync(
            string advertiserId,
            List<string>? fields = null,
            List<string>? excludeFieldTypesInResponse = null,
            AdGetFiltering? filtering = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create new ads
        /// </summary>
        /// <param name="request">Ad creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad create response containing created ad IDs and details</returns>
        //Task<AdCreateResponse> CreateAdsAsync(
        //    AdCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update existing ads
        /// </summary>
        /// <param name="request">Ad update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad update response containing updated ad IDs and details</returns>
        //Task<AdUpdateResponse> UpdateAdsAsync(
        //    AdUpdateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update ad status (enable, disable, or delete)
        /// </summary>
        /// <param name="request">Ad status update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad status update response containing updated ad IDs and status</returns>
        //Task<AdStatusUpdateResponse> UpdateAdStatusAsync(
        //    AdStatusUpdateRequest request,
        //    CancellationToken cancellationToken = default);
    }
}
