﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Repositories;
using TikTok.Cache;
using Volo.Abp.Caching;
using Volo.Abp.ObjectMapping;

namespace TikTok.BusinessApplications
{
    public class BusinessApplicationCache : IBusinessApplicationCache, ICacheService
    {
        private readonly IBusinessApplicationRepository _repository;
        private readonly IDistributedCache<List<BusinessApplicationDto>, string> _businessApplicationListCache;
        private readonly IObjectMapper _mapper;
        private const string BusinessApplicationAllCacheKey = "BusinessApplication:All";
        private static readonly TimeSpan CacheExpiration = CacheConst.CACHE_EXPIRATION;
        private ILogger<BusinessApplicationCache> _logger;

        public BusinessApplicationCache(
            IBusinessApplicationRepository repository,
            IDistributedCache<List<BusinessApplicationDto>, string> businessApplicationListCache,
            IObjectMapper mapper,
            ILogger<BusinessApplicationCache> logger)
        {
            _repository = repository;
            _businessApplicationListCache = businessApplicationListCache;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<BusinessApplicationDto?> GetByBcIdAsync(string bcId)
        {
            var businessApplications = await GetAllActiveAsync();
            var businessApplication = businessApplications.FirstOrDefault(x => x.BcId == bcId);
            return businessApplication;
        }

        public async Task<BusinessApplicationDto?> GetById(Guid id)
        {
            var businessApplications = await GetAllActiveAsync();
            var businessApplication = businessApplications.FirstOrDefault(x => x.Id == id);
            return businessApplication;
        }

        public async Task<List<BusinessApplicationDto>> GetAllActiveAsync()
        {
            var cacheKey = BusinessApplicationAllCacheKey;
            var businessApplications = await _businessApplicationListCache.GetOrAddAsync(
                cacheKey,
                async () =>
                {
                    var bcs = await _repository.GetListAsync(x => x.IsActive);

                    return _mapper.Map<List<BusinessApplicationEntity>, List<BusinessApplicationDto>>(bcs);
                },
                () => new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = CacheExpiration
                });
            return businessApplications;
        }

        public async Task CleanCache()
        {
            await _businessApplicationListCache.RemoveAsync(BusinessApplicationAllCacheKey, hideErrors: true);
        }

        // ICacheService implementation
        public string CacheName => "Business Application Cache";

        public async Task<List<string>> GetCacheKeysAsync()
        {
            return new List<string> { BusinessApplicationAllCacheKey };
        }

        public async Task<bool> ClearCacheAsync()
        {
            try
            {
                await _businessApplicationListCache.RemoveAsync(BusinessApplicationAllCacheKey, hideErrors: true);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ClearCacheByKeyAsync(string cacheKey)
        {
            try
            {
                if (cacheKey == BusinessApplicationAllCacheKey)
                {
                    await _businessApplicationListCache.RemoveAsync(cacheKey, hideErrors: true);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<CacheInfoDto> GetCacheInfoAsync()
        {
            var cacheKeys = await GetCacheKeysAsync();
            return new CacheInfoDto
            {
                CacheName = CacheName,
                ActiveKeysCount = cacheKeys.Count,
                CacheKeys = cacheKeys,
                CreatedAt = DateTime.UtcNow,
                LastAccessedAt = DateTime.UtcNow,
                LastModifiedAt = DateTime.UtcNow,
                Status = "Active",
                Description = "Cache cho danh sách Business Application"
            };
        }
    }
}