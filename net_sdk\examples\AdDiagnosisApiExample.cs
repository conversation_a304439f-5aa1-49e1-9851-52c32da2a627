/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the Ad Diagnosis API
    /// </summary>
    public class AdDiagnosisApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public AdDiagnosisApiExample(string appId, string accessToken)
        {
            var configuration = Configuration.CreateWithCredentials(appId, accessToken);
            _client = new TikTokBusinessApiClient(configuration);
        }

        /// <summary>
        /// Example: Get diagnosis for all ad groups under an advertiser
        /// </summary>
        public async Task GetAllDiagnosisAsync(string advertiserId)
        {
            try
            {
                Console.WriteLine($"Getting diagnosis for advertiser: {advertiserId}");

                var response = await _client.AdDiagnosis.GetDiagnosisAsync(advertiserId);

                Console.WriteLine($"Found {response.Results?.Count ?? 0} ad groups with diagnosis");

                if (response.Results != null)
                {
                    foreach (var result in response.Results)
                    {
                        Console.WriteLine($"\nAd Group: {result.AdgroupName} (ID: {result.AdgroupId})");
                        Console.WriteLine($"Diagnosis Time: {result.Diagnosis?.DiagnosisTime}");

                        if (result.Diagnosis?.Suggestions != null)
                        {
                            foreach (var suggestion in result.Diagnosis.Suggestions)
                            {
                                // Creative suggestions
                                if (suggestion.Creative != null && suggestion.Creative.Count > 0)
                                {
                                    Console.WriteLine("  Creative Issues:");
                                    foreach (var creative in suggestion.Creative)
                                    {
                                        Console.WriteLine($"    - Ad: {creative.Name} (ID: {creative.AdId})");
                                        Console.WriteLine($"      Issue: {creative.IssueSuggestion}");
                                        Console.WriteLine($"      Video ID: {creative.Vid}");
                                    }
                                }

                                // Bid and budget suggestions
                                if (suggestion.BidAndBudget != null && suggestion.BidAndBudget.Count > 0)
                                {
                                    Console.WriteLine("  Bid & Budget Issues:");
                                    foreach (var bidBudget in suggestion.BidAndBudget)
                                    {
                                        Console.WriteLine($"    - Issue: {bidBudget.IssueSuggestion}");
                                        if (bidBudget.SuggestBid.HasValue)
                                            Console.WriteLine($"      Suggested Bid: {bidBudget.SuggestBid}");
                                        if (bidBudget.SuggestBudget.HasValue)
                                            Console.WriteLine($"      Suggested Budget: {bidBudget.SuggestBudget}");
                                    }
                                }

                                // Event tracking suggestions
                                if (suggestion.EventTrack != null && suggestion.EventTrack.Count > 0)
                                {
                                    Console.WriteLine("  Event Tracking Issues:");
                                    foreach (var eventTrack in suggestion.EventTrack)
                                    {
                                        Console.WriteLine($"    - Issue: {eventTrack.IssueSuggestion}");
                                        Console.WriteLine($"      Pixel ID: {eventTrack.PixelId}");
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get diagnosis for specific ad groups with filtering
        /// </summary>
        public async Task GetFilteredDiagnosisAsync(string advertiserId, List<string> adGroupIds)
        {
            try
            {
                Console.WriteLine($"Getting diagnosis for specific ad groups in advertiser: {advertiserId}");

                var filtering = new AdDiagnosisFiltering
                {
                    AdgroupIds = adGroupIds,
                    IssueCategory = new List<string> { "CREATIVE", "BID_AND_BUDGET" }
                };

                var response = await _client.AdDiagnosis.GetDiagnosisAsync(advertiserId, filtering);

                Console.WriteLine($"Found {response.Results?.Count ?? 0} ad groups with diagnosis");

                // Process results similar to above example
                // ... (implementation details omitted for brevity)
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get diagnosis for creative issues only
        /// </summary>
        public async Task GetCreativeDiagnosisAsync(string advertiserId)
        {
            try
            {
                Console.WriteLine($"Getting creative diagnosis for advertiser: {advertiserId}");

                var filtering = new AdDiagnosisFiltering
                {
                    IssueCategory = new List<string> { "CREATIVE" }
                };

                var response = await _client.AdDiagnosis.GetDiagnosisAsync(advertiserId, filtering);

                Console.WriteLine($"Found {response.Results?.Count ?? 0} ad groups with creative issues");

                // Process creative-specific results
                // ... (implementation details omitted for brevity)
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _client?.Dispose();
        }
    }

    /// <summary>
    /// Program entry point for running examples
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Replace with your actual app ID and access token
            var appId = "your-app-id";
            var accessToken = "your-access-token";
            var advertiserId = "your-advertiser-id";

            var example = new AdDiagnosisApiExample(appId, accessToken);

            try
            {
                // Example 1: Get all diagnosis
                await example.GetAllDiagnosisAsync(advertiserId);

                // Example 2: Get diagnosis for specific ad groups
                var adGroupIds = new List<string> { "adgroup_1", "adgroup_2" };
                await example.GetFilteredDiagnosisAsync(advertiserId, adGroupIds);

                // Example 3: Get creative diagnosis only
                await example.GetCreativeDiagnosisAsync(advertiserId);
            }
            finally
            {
                example.Dispose();
            }
        }
    }
}
