# RawGmvMaxCampaignsEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawGmvMaxCampaigns`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawGmvMaxCampaigns`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho TikTok GMV Max Campaign. GMV Max là giải pháp quảng cáo tự động của TikTok tập trung vào tối ưu hóa Gross Merchandise Value (GMV) thông qua việc sử dụng AI để tạo và tối ưu hóa nội dung quảng cáo, liên kết trực tiếp với TikTok Shop để thúc đẩy doanh số bán hàng.

## Bảng dữ liệu: RawGmvMaxCampaigns

| Tên Field                       | Kiểu dữ liệu               | B<PERSON><PERSON> bu<PERSON> | <PERSON><PERSON> dà<PERSON> | <PERSON><PERSON> tả                                                                      |
| ------------------------------- | -------------------------- | -------- | ------ | -------------------------------------------------------------------------- |
| Id                              | Guid                       | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                      |
| BcId                            | string                     | ✅       | 100    | ID Business Center                                                         |
| AdvertiserId                    | string                     | ✅       | 100    | ID nhà quảng cáo                                                           |
| CampaignId                      | string                     | ✅       | 100    | ID chiến dịch GMV Max (Unique Index)                                       |
| CampaignName                    | string                     | ✅       | 500    | Tên chiến dịch                                                             |
| OperationStatus                 | string                     | ❌       | 50     | Trạng thái hoạt động của GMV Max Campaign (DISABLE: Tắt, ENABLE: Bật)      |
| CreateTime                      | DateTime                   | ✅       | -      | Thời gian tạo chiến dịch (UTC)                                             |
| ModifyTime                      | DateTime                   | ✅       | -      | Thời gian sửa đổi gần nhất (UTC)                                           |
| ObjectiveType                   | string                     | ✅       | 50     | Mục tiêu quảng cáo (PRODUCT_SALES)                                         |
| SecondaryStatus                 | string                     | ❌       | 100    | Trạng thái phụ của chiến dịch                                              |
| RoiProtectionCompensationStatus | GmvMaxRoiProtectionStatus? | ❌       | -      | Trạng thái bảo vệ ROI (IN_EFFECT, NOT_ELIGIBLE)                            |
| StoreId                         | string                     | ✅       | 100    | ID của TikTok Shop                                                         |
| StoreAuthorizedBcId             | string                     | ❌       | 100    | ID của Business Center được ủy quyền truy cập TikTok Shop                  |
| ShoppingAdsType                 | GmvMaxShoppingAdsType?     | ❌       | -      | Loại GMV Max Campaign (PRODUCT: Sản phẩm, LIVE: Live stream)               |
| ProductSpecificType             | GmvMaxProductSpecificType? | ❌       | -      | Cách chọn sản phẩm (ALL, CUSTOMIZED_PRODUCTS, UNSET)                       |
| OptimizationGoal                | OptimizationGoal?          | ❌       | -      | Mục tiêu tối ưu hóa (VALUE: Doanh thu tổng)                                |
| RoiProtectionEnabled            | bool?                      | ❌       | -      | Có kích hoạt bảo vệ ROI hay không                                          |
| DeepBidType                     | DeepBidType?               | ❌       | -      | Chiến lược đấu giá (VO_MIN_ROAS: ROAS tối thiểu)                           |
| RoasBid                         | decimal                    | ❌       | -      | Mục tiêu ROI, chỉ có khi deep_bid_type là VO_MIN_ROAS                      |
| Budget                          | decimal                    | ❌       | -      | Ngân sách hàng ngày                                                        |
| ScheduleType                    | GmvMaxScheduleType?        | ❌       | -      | Loại lịch trình (SCHEDULE_FROM_NOW, SCHEDULE_START_END)                    |
| ScheduleStartTime               | DateTime?                  | ❌       | -      | Thời gian bắt đầu chiến dịch (UTC+0)                                       |
| ScheduleEndTime                 | DateTime?                  | ❌       | -      | Thời gian kết thúc chiến dịch (UTC+0)                                      |
| Placements                      | List<string>?              | ❌       | -      | Nền tảng đặt quảng cáo (JSON array): PLACEMENT_TIKTOK, PLACEMENT_PANGLE    |
| LocationIds                     | List<string>?              | ❌       | -      | Danh sách ID vị trí địa lý được nhắm mục tiêu (JSON array)                 |
| AgeGroups                       | List<string>?              | ❌       | -      | Danh sách nhóm tuổi được nhắm mục tiêu (JSON array)                        |
| ItemGroupIds                    | List<string>?              | ❌       | -      | Danh sách ID nhóm sản phẩm SPU (JSON array)                                |
| ProductVideoSpecificType        | GmvMaxVideoSpecificType?   | ❌       | -      | Chế độ chọn video (AUTO_SELECTION, CUSTOM_SELECTION, UNSET)                |
| AffiliatePostsEnabled           | bool?                      | ❌       | -      | Có kích hoạt affiliate posts cho Product GMV Max Campaign hay không        |
| CampaignCustomAnchorVideoId     | string                     | ❌       | 100    | ID của bộ sưu tập customized posts được tạo trong Product GMV Max Campaign |
| SyncedAt                        | DateTime                   | ✅       | -      | Thời gian đồng bộ dữ liệu (UTC)                                            |
| CreationTime                    | DateTime                   | ✅       | -      | Thời gian tạo bản ghi (Audit)                                              |
| CreatorId                       | Guid?                      | ❌       | -      | ID người tạo (Audit)                                                       |
| LastModificationTime            | DateTime?                  | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                             |
| LastModifierId                  | Guid?                      | ❌       | -      | ID người sửa đổi cuối (Audit)                                              |

## Enums liên quan

### GmvMaxRoiProtectionStatus

-   **IN_EFFECT**: Bảo vệ ROI đang có hiệu lực
-   **NOT_ELIGIBLE**: Không đủ điều kiện bảo vệ ROI

### GmvMaxShoppingAdsType

-   **PRODUCT**: Campaign quảng cáo sản phẩm
-   **LIVE**: Campaign quảng cáo live stream

### GmvMaxProductSpecificType

-   **ALL**: Tự động từ tất cả sản phẩm
-   **CUSTOMIZED_PRODUCTS**: Chỉ định sản phẩm cụ thể
-   **UNSET**: Chưa thiết lập

### OptimizationGoal

-   **VALUE**: Tối ưu hóa doanh thu tổng (Gross Merchandise Value)

### DeepBidType

-   **VO_MIN_ROAS**: Tối ưu hóa với ROAS tối thiểu

### GmvMaxScheduleType

-   **SCHEDULE_FROM_NOW**: Chạy liên tục từ thời điểm hiện tại
-   **SCHEDULE_START_END**: Chạy trong khoảng thời gian cụ thể

### GmvMaxVideoSpecificType

-   **AUTO_SELECTION**: Tự động chọn video
-   **CUSTOM_SELECTION**: Thủ công chọn video
-   **UNSET**: Chưa thiết lập

## Cấu trúc dữ liệu

### Thông tin cơ bản

-   **BcId**: ID Business Center quản lý
-   **AdvertiserId**: ID tài khoản nhà quảng cáo
-   **CampaignId**: ID duy nhất của chiến dịch GMV Max
-   **CampaignName**: Tên hiển thị của chiến dịch

### Cấu hình chiến dịch

-   **ObjectiveType**: Mục tiêu chính (thường là PRODUCT_SALES)
-   **ShoppingAdsType**: Loại quảng cáo (sản phẩm hoặc live)
-   **ProductSpecificType**: Cách chọn sản phẩm quảng cáo
-   **OptimizationGoal**: Mục tiêu tối ưu hóa

### Tối ưu hóa và đấu giá

-   **DeepBidType**: Chiến lược đấu giá
-   **RoasBid**: Mục tiêu ROAS tối thiểu
-   **RoiProtectionEnabled**: Kích hoạt bảo vệ ROI
-   **RoiProtectionCompensationStatus**: Trạng thái bảo vệ ROI

### Ngân sách và lịch trình

-   **Budget**: Ngân sách hàng ngày
-   **ScheduleType**: Loại lịch trình chạy
-   **ScheduleStartTime/ScheduleEndTime**: Thời gian bắt đầu và kết thúc

### Targeting và định hướng

-   **Placements**: Nền tảng đặt quảng cáo
-   **LocationIds**: Vị trí địa lý mục tiêu
-   **AgeGroups**: Nhóm tuổi mục tiêu
-   **ItemGroupIds**: Nhóm sản phẩm cụ thể

### TikTok Shop Integration

-   **StoreId**: ID TikTok Shop liên kết
-   **StoreAuthorizedBcId**: Business Center được ủy quyền

### Video và nội dung

-   **ProductVideoSpecificType**: Cách chọn video quảng cáo
-   **AffiliatePostsEnabled**: Kích hoạt affiliate posts
-   **CampaignCustomAnchorVideoId**: ID bộ sưu tập video anchor

### Trạng thái và hoạt động

-   **OperationStatus**: Trạng thái hiện tại (ENABLE/DISABLE)
-   **SecondaryStatus**: Trạng thái phụ chi tiết
-   **CreateTime/ModifyTime**: Thời gian tạo và sửa đổi

## Navigation Properties

-   **Identities**: Danh sách danh tính TikTok (RawGmvMaxCampaignIdentitiesEntity)
-   **Items**: Danh sách bài đăng TikTok (RawGmvMaxCampaignItemsEntity)
-   **CustomAnchorVideos**: Danh sách video anchor tùy chỉnh (RawGmvMaxCampaignCustomAnchorVideosEntity)

## Phương thức đặc biệt

### HasChanged()

```csharp
public bool HasChanged(RawGmvMaxCampaignsEntity other)
```

So sánh entity hiện tại với entity khác để phát hiện thay đổi. Bao gồm:

-   So sánh tất cả các trường scalar
-   So sánh danh sách JSON (Placements, LocationIds, AgeGroups, ItemGroupIds)
-   Sử dụng AreListsEqual() để so sánh danh sách một cách chính xác

## Đặc điểm chính của GMV Max

### AI-Powered Automation

-   **Tự động tạo nội dung**: Sử dụng AI để tạo video quảng cáo từ sản phẩm
-   **Tối ưu hóa thông minh**: Tự động điều chỉnh targeting và bidding
-   **Anchor Videos**: Sử dụng video mẫu để định hướng AI

### TikTok Shop Integration

-   **Liên kết trực tiếp**: Kết nối với catalog sản phẩm TikTok Shop
-   **Theo dõi GMV**: Tối ưu hóa dựa trên doanh thu thực tế
-   **Affiliate Posts**: Hỗ trợ creator marketing

### Performance Protection

-   **ROI Protection**: Bảo vệ lợi tức đầu tư
-   **ROAS Bidding**: Đấu giá dựa trên ROAS mục tiêu
-   **Budget Control**: Kiểm soát ngân sách tự động

## Mục đích sử dụng

-   Lưu trữ thông tin chi tiết về GMV Max Campaign
-   Quản lý cấu hình AI và automation
-   Theo dõi hiệu suất và ROI protection
-   Tích hợp với TikTok Shop ecosystem
-   Hỗ trợ creator marketing và affiliate posts
-   Tối ưu hóa doanh thu GMV tự động
-   Tích hợp với TikTok Business API cho GMV Max solutions

## Quan hệ với các entity khác

-   **RawGmvMaxCampaignIdentitiesEntity**: One-to-Many, quản lý danh tính TikTok
-   **RawGmvMaxCampaignItemsEntity**: One-to-Many, danh sách bài đăng và video
-   **RawGmvMaxCampaignCustomAnchorVideosEntity**: One-to-Many, video anchor tùy chỉnh

## Use Cases thực tế

### E-commerce Brand Campaign

```
Thương hiệu thời trang tạo GMV Max Campaign:
- ShoppingAdsType: PRODUCT
- ProductSpecificType: CUSTOMIZED_PRODUCTS (chọn sản phẩm hot)
- OptimizationGoal: VALUE (tối ưu doanh thu)
- RoiProtectionEnabled: true (bảo vệ ROI)
```

### Live Commerce Campaign

```
Shop bán hàng qua live stream:
- ShoppingAdsType: LIVE
- ProductSpecificType: ALL (quảng cáo tất cả sản phẩm)
- AffiliatePostsEnabled: true (hợp tác với KOL)
```

# Dữ liệu mẫu từ API 
```
{
    "code": 0,
    "message": "OK",
    "request_id": "2025072910130951AEEB2A1FEBCF4BEF9F",
    "data": {
        "advertiser_id": "7288604809509322753",
        "age_groups": [
            "AGE_18_24",
            "AGE_25_34",
            "AGE_35_44",
            "AGE_45_54",
            "AGE_55_100"
        ],
        "budget": 1000000,
        "campaign_id": "1834652076820514",
        "campaign_name": "GMV Max_CV8_CVXam_CVPhuDo",
        "custom_anchor_video_list": [],
        "deep_bid_type": "VO_MIN_ROAS",
        "item_group_ids": [
            "1729749552985705361",
            "1731600707452372881",
            "1731574448173451153"
        ],
        "item_list": [
            {
                "identity_info": {
                    "identity_id": "6915417480602600450",
                    "identity_type": "TTS_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/7652de6aa3b2127dad6b293b1f80fc35~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=a297fc92&x-expires=1753927200&x-signature=gdV0rsbvRFbSomONy5nkBs65cY8%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=maliva",
                    "store_id": "7495035197756378001",
                    "user_name": ""
                },
                "item_id": "7364637561160994055",
                "spu_id_list": [
                    "1729749552985705361"
                ],
                "text": "Chân váy hot đang S.A.L.E . Nhanh tay hốt liền #chanvay #chanvayxinh #virual #xuhuong ",
                "video_info": {
                    "bit_rate": 9943332,
                    "definition": "1080p",
                    "duration": 72.749,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1920,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/32e0004aa08db7d2553fda91cc46161e/68897ffe/video/tos/alisg/tos-alisg-pve-0037c001/oMsp5HfTDIplwo0KAmFgQQRavBfomwBQEIgwEN/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&cv=1&br=5486&bt=2743&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=aTVkZzQ4MzdnaDQ2NWU5ZEBpM3FteHQ5cjk6cjMzODczNEAxNTIzYzIyNS4xYy8tNGNiYSNwMy8xMmRrMm5gLS1kMS1zcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "ea32cb9ccf71729db48fb6a0ab58d76d",
                    "size": 0,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/363f59ed4d4d4205a891e424a04be40d_1714713312~tplv-noop.image?t=9276707c&x-expires=1753841662&x-signature=GJ7WN89DL6CBPZumaQfE0OL9U2o%3D",
                    "video_id": "v10044g50000coq756nog65q4u2j6ns0",
                    "width": 1080
                }
            },
            {
                "identity_info": {
                    "identity_id": "6915417480602600450",
                    "identity_type": "TTS_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/7652de6aa3b2127dad6b293b1f80fc35~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=a297fc92&x-expires=1753927200&x-signature=gdV0rsbvRFbSomONy5nkBs65cY8%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=maliva",
                    "store_id": "7495035197756378001",
                    "user_name": ""
                },
                "item_id": "7308359142215879954",
                "spu_id_list": [
                    "1729749552985705361"
                ],
                "text": "Em chân váy bằng 2 bát phở đặc biệt nè các tình iu , nhanh tay múc luôn em noáaaa🥰🥰🥰#chanvay #chanvaytuihop #chanvayjean #xuhuong ",
                "video_info": {
                    "bit_rate": 7190340,
                    "definition": "1080p",
                    "duration": 52.291,
                    "format": "mp4",
                    "fps": 36,
                    "height": 1920,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/ef0fcce9340f0f84bf62927670d6a6a6/68897fea/video/tos/alisg/tos-alisg-pve-0037c001/o8AtBMWsRTA4BEQpi0PBZgMPEiYp2iQI9VyMx/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&cv=1&br=4038&bt=2019&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=OmQ4ZTM3OTxkN2Y0MzlpNUBpM2twd2s5cmY7bzMzODczNEBgYGAwYS0yNjYxYzI1XzYyYSMzZmk2MmQ0L2pgLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00088000",
                    "signature": "b38242fceb38df64bd29fc26cda2ba66",
                    "size": 0,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/8a120255f1c14dab8b00fb74388b1abf_1701609970~tplv-noop.image?t=9276707c&x-expires=1753841642&x-signature=qweP2NGErPR%2FX9vTy2p7FTTl2dw%3D",
                    "video_id": "v14044g50000clm82c7og65h9tlmih60",
                    "width": 1080
                }
            },
            {
                "identity_info": {
                    "identity_id": "6915417480602600450",
                    "identity_type": "TTS_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/7652de6aa3b2127dad6b293b1f80fc35~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=a297fc92&x-expires=1753927200&x-signature=gdV0rsbvRFbSomONy5nkBs65cY8%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=maliva",
                    "store_id": "7495035197756378001",
                    "user_name": ""
                },
                "item_id": "7514143997472886033",
                "spu_id_list": [
                    "1731600707452372881"
                ],
                "text": "Chân váy Hot ring ngay còn kịp MUA 1 ĐƯỢC 3 #chanvay #chanvayjean #chanvaydep ",
                "video_info": {
                    "bit_rate": 2060875,
                    "definition": "540p",
                    "duration": 96.134,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/2bdd4a897eedc4bf158f6481a88d15dc/68898016/video/tos/alisg/tos-alisg-pve-0037c001/oQUD6XOnEhrQEwFgohrA8m5fCo1RBTERHIB5Nf/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&br=4024&bt=2012&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=OTRpMzQ4ZGQ4O2c1NDloPEBpM2Vza3Q5cjdzNDMzODczNEAyYS81MWFgX18xMGFhXmJeYSNxc2tkMmRzaDBhLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "3e65ed968fb031dbbc14e6faee2c1aad",
                    "size": 24765031,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/oIq5GPQIznQXjbLQAEKQjNbLteS5fKIoDG0CAf~tplv-noop.image?t=9276707c&x-expires=1753841686&x-signature=tGdgMNhH1wJtU%2BqaUDQfpBFh46Y%3D",
                    "video_id": "v14044g50000d13pk4vog65qghnpvbt0",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_authorized_bc_id": "7288604756459896834",
                    "identity_id": "8baaa0cf-de58-55fc-847f-556d6268785f",
                    "identity_type": "BC_AUTH_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/65a3b223ce55bc9eee20aae002c777a6~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=0d6ba1e2&x-expires=1753927200&x-signature=VnIEfdkuZXVrRHoDlzvOyxKskdU%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=sg1",
                    "user_name": "anh.long1777"
                },
                "item_id": "7513973974770912519",
                "spu_id_list": [
                    "1731574448173451153"
                ],
                "text": "Chân váy dễ mix đồ nè mấy nàng !! #tiktokshop66 #SaleVuiSinhNhat #chanvayjean #chanvay #chanvayxam #xuhuong ",
                "video_info": {
                    "bit_rate": 2238241,
                    "definition": "540p",
                    "duration": 44.8,
                    "format": "mp4",
                    "fps": 29,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/8cda419ab6008a29a8baf8bb4d6b7814/68897fe2/video/tos/alisg/tos-alisg-pve-0037c001/ooEGQgZRGHX6fFAiAIeeAfA08LjGvhGRZisGVc/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&cv=1&br=4370&bt=2185&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=ZWY5Z2loOTc4Ojk4NDg5N0Bpajs6ZnI5cm1pNDMzODczNEAyLzNhLi9hXzUxNjM0MDIwYSNpcmBoMmQ0cjBhLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00088000",
                    "signature": "467227de20a4f03e6b5535a4f9c3cf35",
                    "size": 12534151,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/o4GRGZWeGhIPqQGJFHGNe6LfX49GAAiQjofaQR~tplv-noop.image?t=9276707c&x-expires=1753841634&x-signature=99ucU5RD%2BIpsNi3K2zNhQ%2FYx5yU%3D",
                    "video_id": "v14044g50000d13fuj7og65okcc7u8lg",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_authorized_bc_id": "7288604756459896834",
                    "identity_id": "8baaa0cf-de58-55fc-847f-556d6268785f",
                    "identity_type": "BC_AUTH_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/65a3b223ce55bc9eee20aae002c777a6~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=0d6ba1e2&x-expires=1753927200&x-signature=VnIEfdkuZXVrRHoDlzvOyxKskdU%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=sg1",
                    "user_name": "anh.long1777"
                },
                "item_id": "7513957916571995400",
                "spu_id_list": [
                    "1731600707452372881"
                ],
                "text": "Hàng về là lên luôn cho các nàng lựa !!! #tiktokshop66  #SaleVuiSinhNhat  #chanvayphudo #chanvayjean  #chanvay  #xuhuong ",
                "video_info": {
                    "bit_rate": 2287413,
                    "definition": "540p",
                    "duration": 53.7,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/a1764fd1e66ea6ddebfc6508a4b94d17/68897feb/video/tos/alisg/tos-alisg-pve-0037c001/oEoRJfN8EpvoBtqEIFOG0BA1QRFsHUfavgTDCE/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&cv=1&br=4466&bt=2233&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=Ojw8aGRpMzNkO2c4NWdoOEBpM2doOXk5cjRpNDMzODczNEBjNDE2YTJfNTExY19eYGAwYSNobmZpMmRjLjBhLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00088000",
                    "signature": "e750ee955a584e6ae13c05442d087fe0",
                    "size": 15354262,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/ogTGFNgRfAIG4CZTJDRoaPBo8EBUFEMtqv0Iuf~tplv-noop.image?t=9276707c&x-expires=1753841643&x-signature=q8OJio7amhcX3D1brjOpV7yBzVo%3D",
                    "video_id": "v14044g50000d13f11fog65vl6ieqdk0",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_id": "6915417480602600450",
                    "identity_type": "TTS_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/7652de6aa3b2127dad6b293b1f80fc35~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=a297fc92&x-expires=1753927200&x-signature=gdV0rsbvRFbSomONy5nkBs65cY8%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=maliva",
                    "store_id": "7495035197756378001",
                    "user_name": ""
                },
                "item_id": "7513909510801476880",
                "spu_id_list": [
                    "1731600707452372881"
                ],
                "text": "Chân váy MUA 1 ĐƯỢC 3 nhanh tay #chanvay #chanvayjean #chanvaydep #hottrend #virual ",
                "video_info": {
                    "bit_rate": 2211676,
                    "definition": "540p",
                    "duration": 80.6,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/5ab6627596b1b27eef5bf12bc83ea521/68898006/video/tos/alisg/tos-alisg-pve-0037c001/oEisoBIxQgDJgIArQzCEBfcNaUFwRRBEnq64fg/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&br=4318&bt=2159&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=NDxkZGk8ZmZkNTw3ODc3OkBpajNqNnI5cmRmNDMzODczNEBhNjZiYTJfNjIxMWJfLjY0YSNqNmRyMmRzNTBhLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "ba20288e580703d7239ca2fdf841b663",
                    "size": 22282638,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/owQRDfxrEEyUQQxB4LogFBcGQsRsAfJatBG0Id~tplv-noop.image?t=9276707c&x-expires=1753841670&x-signature=uHikblZhK8yR26k1y%2B%2F6muWkKNc%3D",
                    "video_id": "v14044g50000d13c8avog65ou3gg90mg",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_authorized_bc_id": "7288604756459896834",
                    "identity_id": "8baaa0cf-de58-55fc-847f-556d6268785f",
                    "identity_type": "BC_AUTH_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/65a3b223ce55bc9eee20aae002c777a6~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=0d6ba1e2&x-expires=1753927200&x-signature=VnIEfdkuZXVrRHoDlzvOyxKskdU%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=sg1",
                    "user_name": "anh.long1777"
                },
                "item_id": "7513462282110635271",
                "spu_id_list": [
                    "1731574448173451153"
                ],
                "text": "Chân váy đẹp, chân váy xinh. Chỉ một chăm kaaaaaaaaaaaa. #TiktokShop66 #SaleVuiSinhNhat #chanvayjean  #chanvayxam #chanvay  #chanvayxinh ",
                "video_info": {
                    "bit_rate": 1081523,
                    "definition": "540p",
                    "duration": 123.834,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/accb300de29d7987f2ec1bf595a561ed/68898031/video/tos/alisg/tos-alisg-pve-0037c001/o0BVI9Z3AxF9pPQUCBdLyxAVipavzYYKsiEEE/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&cv=1&br=2112&bt=1056&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=M2k6OjM2aWdlM2Y0NGc4OUBpamw8eWs5cnJsNDMzODczNEA0NDViYzQ0XjMxXzE0Yl4vYSNeZzZiMmRrbC9hLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "f3d1d4ad90beace1ad62502a0c6e1385",
                    "size": 16741169,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/ocvYZYKa9BL6xdlB6dEsIFYVVyURMEAiEINAi~tplv-noop.image?t=9276707c&x-expires=1753841713&x-signature=pSJdGh%2B18b6dQ0UxdtwtJFxaIKM%3D",
                    "video_id": "v14044g50000d12ioonog65hev99jiag",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_authorized_bc_id": "7288604756459896834",
                    "identity_id": "8baaa0cf-de58-55fc-847f-556d6268785f",
                    "identity_type": "BC_AUTH_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/65a3b223ce55bc9eee20aae002c777a6~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=0d6ba1e2&x-expires=1753927200&x-signature=VnIEfdkuZXVrRHoDlzvOyxKskdU%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=sg1",
                    "user_name": "anh.long1777"
                },
                "item_id": "7513413929603747079",
                "spu_id_list": [
                    "1731600707452372881"
                ],
                "text": "Lụm ngay một iêm chân váy xinh chữa lành đi các nàngggggg #TiktokShop66  #SaleVuiSinhNhat  #chanvayjean  #chanvayphudo  #99k ",
                "video_info": {
                    "bit_rate": 1277985,
                    "definition": "540p",
                    "duration": 97.2,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/5533b651f51406c16a15dd1212d38535/68898017/video/tos/alisg/tos-alisg-pve-0037c001/oIUgAoztfABTQ9DbExGFIfEgEFRhQRWOCUEIBX/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&cv=1&br=2496&bt=1248&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=OjY5ZDNnZzs2OmU0ZTg8OUBpam5qeWo5cjlqNDMzODczNEBgMWJhXjViNS0xYjU0NjAzYSNfMTVjMmRzMS9hLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "93aa5989c96d6480ac43123964a3c8dd",
                    "size": 15527520,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/ooAIrIuGAocCIQELefQjDIABQfF6jiQ4g3FpDo~tplv-noop.image?t=9276707c&x-expires=1753841687&x-signature=JQNLgmqYOJsqiUArMV1FamzH1DM%3D",
                    "video_id": "v14044g50000d12g46vog65gfv8g4kbg",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_id": "6915417480602600450",
                    "identity_type": "TTS_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/7652de6aa3b2127dad6b293b1f80fc35~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=a297fc92&x-expires=1753927200&x-signature=gdV0rsbvRFbSomONy5nkBs65cY8%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=maliva",
                    "store_id": "7495035197756378001",
                    "user_name": ""
                },
                "item_id": "7513392568596548880",
                "spu_id_list": [
                    "1731600707452372881"
                ],
                "text": "HOT CHÂN VÁY MUA 1 ĐƯỢC 3 có quần trong #chanvay #chanvayxinh #chanvayjean ",
                "video_info": {
                    "bit_rate": 2236574,
                    "definition": "540p",
                    "duration": 80.067,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/74f3305397326e0b6860d7d07ad66856/68898006/video/tos/alisg/tos-alisg-pve-0037c001/okITFRBoGRE76FfI2ENDzgQEUUBfA0cWzEV2EO/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&cv=1&br=4368&bt=2184&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=M2g8OWY7ODQ5ZTtmaWY5OUBpM246OXU5cmdoNDMzODczNEBhMTQ1YS1iNi8xMzAxYWNjYSMvcm80MmQ0cS9hLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "98658805e5317751d74f5130267402a2",
                    "size": 22384478,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/oU03EcXRg62RAIzvo3IZ2rF0EfBEUOf7BUGED3~tplv-noop.image?t=9276707c&x-expires=1753841670&x-signature=gRfyLKoDCCrzCOWP2q5X0DfbAKk%3D",
                    "video_id": "v14044g50000d12etd7og65r76r7uk20",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_authorized_bc_id": "7288604756459896834",
                    "identity_id": "8baaa0cf-de58-55fc-847f-556d6268785f",
                    "identity_type": "BC_AUTH_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/65a3b223ce55bc9eee20aae002c777a6~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=0d6ba1e2&x-expires=1753927200&x-signature=VnIEfdkuZXVrRHoDlzvOyxKskdU%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=sg1",
                    "user_name": "anh.long1777"
                },
                "item_id": "7513114165016415506",
                "spu_id_list": [
                    "1731574448173451153"
                ],
                "text": "Mua 1 được 3 là có thật. Nhấp tay lẹ nào mấy chị đẹp!!! #TiktokShop66 #SaleVuiSinhNhat #chanvayxam  #chanvayjean  #chanvaydep ",
                "video_info": {
                    "bit_rate": 1237683,
                    "definition": "540p",
                    "duration": 95.367,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/890245acc856f2c95227d38934132736/68898015/video/tos/alisg/tos-alisg-pve-0037c001/oUfeoFDxjIDJnEEHwDEIKFCA8QUAt5OlhQfOWh/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&cv=1&br=2416&bt=1208&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=Ojs7aTZlMzNmNzVoZzk4NkBpM3BvNXY5cmt5NDMzODczNEBjMTMuMDVeNTIxY2EzLy8tYSM0NWhzMmRzLy5hLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "abfd6475b142bd581d051c6751636278",
                    "size": 14754273,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/okHK4fe8DQq0cH5FAoFEo0jHsCfiQVDQEoAVIh~tplv-noop.image?t=9276707c&x-expires=1753841685&x-signature=UIBpxZH4J7chkjavz63ODRNstQg%3D",
                    "video_id": "v14044g50000d11v2hvog65sv2kl8m70",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_authorized_bc_id": "7288604756459896834",
                    "identity_id": "8baaa0cf-de58-55fc-847f-556d6268785f",
                    "identity_type": "BC_AUTH_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/65a3b223ce55bc9eee20aae002c777a6~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=0d6ba1e2&x-expires=1753927200&x-signature=VnIEfdkuZXVrRHoDlzvOyxKskdU%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=sg1",
                    "user_name": "anh.long1777"
                },
                "item_id": "7512474069825096978",
                "spu_id_list": [
                    "1731574448173451153"
                ],
                "text": "Sắm lẹ còn đi biển tránh nóng các nàng ơiiiiii #TiktokShop66 #SaleVuiSinhNhat #chanvayhottrend #chanvayjean #chanvayxam",
                "video_info": {
                    "bit_rate": 1758339,
                    "definition": "540p",
                    "duration": 109.434,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/e6f417193fb1176a3292fafbc5bb23ef/68898023/video/tos/alisg/tos-alisg-pve-0037c001/o0FQUICEDMkqaByHgEfyA6Q8AFHvWoBEQ4eRaB/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&br=3434&bt=1717&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=ODczNmRlZ2kzPDY1ZmRmO0BpMzZyd3Y5cjN0NDMzODczNEAtYV81XzVhXi4xX2MwYzBhYSNzLXAuMmRraS1hLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "37e63c4ba89519758d73312f7bd03aaa",
                    "size": 24052772,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/o0UfaFqYA4ARMD4HIeqBCHdMNOEEQoiUgDBBIv~tplv-noop.image?t=9276707c&x-expires=1753841699&x-signature=gG3HH8e9ckpdN%2FJL9GtiDGKWzXk%3D",
                    "video_id": "v14044g50000d10ql0nog65s1tso03v0",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_id": "6915417480602600450",
                    "identity_type": "TTS_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/7652de6aa3b2127dad6b293b1f80fc35~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=a297fc92&x-expires=1753927200&x-signature=gdV0rsbvRFbSomONy5nkBs65cY8%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=maliva",
                    "store_id": "7495035197756378001",
                    "user_name": ""
                },
                "item_id": "7512372006990515474",
                "spu_id_list": [
                    "1731574448173451153"
                ],
                "text": "Lụm ngay em chân váy chào hè đi các nàng ơiiiiiii #TiktokShop66 #SaleVuiSinhNhat",
                "video_info": {
                    "bit_rate": 1384138,
                    "definition": "540p",
                    "duration": 102.134,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/07a338a9ce654c5010d4f749aa10e15d/6889801c/video/tos/alisg/tos-alisg-pve-0037c001/oEAmAqOIwihQHCKQI0iByzfaE27Swb5pAqBBtI/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&br=2702&bt=1351&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=OGUzNjdlZjlpPGY6OzRkOUBpM2g5dGw5cjNuNDMzODczNEBjLzFeLmMzXzMxYmI0YF40YSNhbmMwMmRzby1hLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "62260aa257f00a8c635059733009bd10",
                    "size": 17670947,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/o4pQCCGxQHTW0ynADAeggoefjsnmMDPQI8Bg5o~tplv-noop.image?t=9276707c&x-expires=1753841692&x-signature=C%2ByNIpgowTG6SMfUOXEKdL269Ic%3D",
                    "video_id": "v14044g50000d10kr0vog65i3qf6qed0",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_id": "7469783429044356114",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/1fc54c104b355b39c5216b8a62637db5~tplv-tiktokx-cropcenter:100:100.webp?dr=14579&refresh_token=94d082ce&x-expires=1753927200&x-signature=rBccTLLIuNHClzgdQ5Yg%2FDrsLfI%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=bcd0ceb0&idc=sg1",
                    "user_name": ""
                },
                "item_id": "7512729977666768146",
                "spu_id_list": [
                    "1731574448173451153"
                ],
                "text": "CHÂN VÁY JEANS NỮ CHỮ A MÀU XÁM CAP CAO HOTTREND, Chị em điều yêu thích đây #chanvaynu #chanvayjean #chanvay ",
                "video_info": {
                    "bit_rate": 878925,
                    "definition": "540p",
                    "duration": 20.806,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/c19533cf8fbf5ef2bac43b0e8ef59ce6/68897fca/video/tos/alisg/tos-alisg-pve-0037c001/oIXCn0JHiIdUAiEhtQqILwAl6dCIAFBxCELfIJ/?a=1233&bti=a3VoYmhsMTd3Z0BgXnBsYXNfcSteYA%3D%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&br=1716&bt=858&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=ZzhoNjo4ZDY7NTo6ZjNmPEBpajx4bGs5cmg8NDMzODczNEA2Xi8tMl42Xy8xMzZjNDEtYSMvbl4wMmRzNC5hLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e000b8000",
                    "signature": "467856ed1f214a548fb789a052b5d7e1",
                    "size": 2285866,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/ow3tQ5whAdIjAQeDQfgaK8WeI0DFoS8KCkBUMq~tplv-noop.image?t=9276707c&x-expires=1753841610&x-signature=bllQJTJq3ay1V%2B%2BzHhsmzGCEZZg%3D",
                    "video_id": "v14044g50000d1197evog65h3iauq92g",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_id": "7048215523993748481",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/6d3f537d8d056068b3e477b88e10485c~tplv-tiktokx-cropcenter:100:100.webp?dr=14579&refresh_token=c6c99e79&x-expires=1753927200&x-signature=e0yihVrO4pC4ZbLG35aXiQbWUJY%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=bcd0ceb0&idc=sg1",
                    "user_name": ""
                },
                "item_id": "7486249953925369096",
                "spu_id_list": [
                    "1729749552985705361"
                ],
                "text": "Chân vây jean xanh rêu phủ dơ , cứ mặc là thành  hóttt gơn nha🔥🔥#xuhuong #xuhuongtiktok #fyp #xh #giare #chanvay #chanvayxinh #sunghiepchuong ",
                "video_info": {
                    "bit_rate": 1027806,
                    "definition": "540p",
                    "duration": 35.781,
                    "format": "mp4",
                    "fps": 25,
                    "height": 932,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/d28d1e9bba3bcdf59099df8cb50d9eaf/68897fd9/video/tos/alisg/tos-alisg-pve-0037c001/ogeEjEQDgxIBOCGFfxnRBgAkQPD555IdIn3I3U/?a=1233&bti=a3VoYmhsMTd3Z0BgXnBsYXNfcSteYA%3D%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&cv=1&br=2006&bt=1003&cs=0&ds=6&eid=12800&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=ODozZTY2NzxmNjw7N2Q7N0BpM3J2eWw5cjw7eTMzODczNEAvNl4tMDAwXi0xYF4vMGMwYSNoMzEwMmRzLmZgLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00088000",
                    "signature": "c0fd73d79dab48b86995847c370c48e6",
                    "size": 4596992,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/oUiqIqcfkLYInECITg8zHCiBoMqB8HwAADytkA~tplv-noop.image?t=9276707c&x-expires=1753841625&x-signature=PZ92iw%2FBfPS%2FDWpRwPcfU9UB58E%3D",
                    "video_id": "v14044g50000cvi819vog65i3v4s6ok0",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_id": "6902637823900943362",
                    "profile_image": "",
                    "user_name": ""
                },
                "item_id": "7469057035611884808",
                "spu_id_list": [
                    "1729749552985705361"
                ],
                "text": "",
                "video_info": {
                    "video_id": ""
                }
            },
            {
                "identity_info": {
                    "identity_id": "7396205582631371783",
                    "profile_image": "https://p9-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/3aeef452461f1f27f64f90c38d282195~tplv-tiktokx-cropcenter:100:100.webp?dr=14579&refresh_token=9b0da02a&x-expires=1753927200&x-signature=C%2FLJOXWhpBimdu9doYnBeQKQXvY%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=bcd0ceb0&idc=sg1",
                    "user_name": ""
                },
                "item_id": "7435917187619015954",
                "spu_id_list": [
                    "1729749552985705361"
                ],
                "text": "-Cả set này xinh vô cùng luôn đó❤️💋😙😙#fyp #thoitrang #outfit #xuhuong #xuhuongtiktok #áo #áo Croptop Xóp #áotrevai #aotrevaixinh #aotrevai ",
                "video_info": {
                    "bit_rate": 792800,
                    "definition": "540p",
                    "duration": 11.534,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1018,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/58438fb0a984ab5f49795d8abc05d6c2/68897fc1/video/tos/alisg/tos-alisg-pve-0037c001/o0DgIXjoILAaBYAARjeGoDzS1rvE4hOGeDseCV/?a=1233&bti=a3VoYmhsMTd3Z0BgXnBsYXNfcSteYA%3D%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&cv=1&br=1548&bt=774&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=ZzgzOTNpNmY5ODY1NGllNEBpM2RtZHI5cjN0djMzODczNEBgLmAzNTAuNTIxLV40XmMwYSNxNm1fMmRjcmxgLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e000b0000",
                    "signature": "cdbd646bde6b711f42db2c7b0ca1e127",
                    "size": 1143020,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/osVjBt8ElWRZBQcQOBFnAXZDg0UfmBqqfaMEpw~tplv-noop.image?t=9276707c&x-expires=1753841601&x-signature=F%2B%2BTwJCNg3wamPoeJPiSSup%2B0y8%3D",
                    "video_id": "v14044g50000csoqu0fog65obapj9at0",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_id": "6817226396906046465",
                    "profile_image": "https://p9-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/6d113589c5789b7f895a2a74b25a1d30~tplv-tiktokx-cropcenter:100:100.webp?dr=14579&refresh_token=7f72f305&x-expires=1753927200&x-signature=%2FqW%2BBJVOPEm7QVenlOWAoSGO7Ww%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=bcd0ceb0&idc=sg1",
                    "user_name": ""
                },
                "item_id": "7493274749515205896",
                "spu_id_list": [
                    "1729749552985705361"
                ],
                "text": "#chanvay  #vhanvaybo #thoitrangnu  #doxinh ",
                "video_info": {
                    "bit_rate": 1385230,
                    "definition": "540p",
                    "duration": 28.352,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/33af01c1bea6f53539da3d3fd37e9a63/68897fd2/video/tos/alisg/tos-alisg-pve-0037c001/oIcCAfPq9EA1FEdl2od19PQ6hwiwQABrzIkIri/?a=1233&bti=a3VoYmhsMTd3Z0BgXnBsYXNfcSteYA%3D%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&cv=1&br=2704&bt=1352&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=ZzU3ZjhoM2Q1ODY3OjRpO0BpajhtPGo5cmlxeTMzODczNEA2XjFfNF4wXmExLzU0MS0xYSNkYGA1MmQ0XnJgLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e000b8000",
                    "signature": "314734dd3fa8bbaa245b35966620ffed",
                    "size": 4909257,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/okfJ9M8ADBCEBsAhg5DLoPQcfInUSdjJXFCIfL~tplv-noop.image?t=9276707c&x-expires=1753841618&x-signature=oDFSeK1tBZ9b06VStTKxnPn8IOE%3D",
                    "video_id": "v14044g50000cvunaf7og65g89cjc5gg",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_authorized_bc_id": "7288604756459896834",
                    "identity_id": "8baaa0cf-de58-55fc-847f-556d6268785f",
                    "identity_type": "BC_AUTH_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/65a3b223ce55bc9eee20aae002c777a6~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=0d6ba1e2&x-expires=1753927200&x-signature=VnIEfdkuZXVrRHoDlzvOyxKskdU%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=sg1",
                    "user_name": "anh.long1777"
                },
                "item_id": "7527270796771757330",
                "spu_id_list": [
                    "1731600707452372881"
                ],
                "text": "Hàng nóng về rồi mấy ní ơiiiii #chanvayjean  #chanvayphudo  #chanvay  #chanvayxinh  #chanvayhotttrend  #chanvaydep ",
                "video_info": {
                    "bit_rate": 1924099,
                    "definition": "540p",
                    "duration": 79.167,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/dcb9d4f2d4f17b8dde5866758398cc28/68898005/video/tos/alisg/tos-alisg-pve-0037c001/okHf4WMbjIDRARoPmYQgdVCAeB7AemIa7Q8cHn/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&cv=1&br=3758&bt=1879&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=NzdoaWlmOWRoODpnOjk6N0Bpamt0d245cjw2NDMzODczNEAvMS0wNmFjNmMxYzZjL2JjYSNvcW5vMmRzaW9hLS1kMWBzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "d5535a3e7200868862eb854c9b67442b",
                    "size": 19040653,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/osqWPxD3BIhP3201AuiDukITxAfP0XAIigw3CO~tplv-noop.image?t=9276707c&x-expires=1753841669&x-signature=0gSMYels%2BP5oX0Dyrz4Uop8cWVc%3D",
                    "video_id": "v1c044g50000d1r3l9vog65krtqqthrg",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_authorized_bc_id": "7288604756459896834",
                    "identity_id": "8baaa0cf-de58-55fc-847f-556d6268785f",
                    "identity_type": "BC_AUTH_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/65a3b223ce55bc9eee20aae002c777a6~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=0d6ba1e2&x-expires=1753927200&x-signature=VnIEfdkuZXVrRHoDlzvOyxKskdU%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=sg1",
                    "user_name": "anh.long1777"
                },
                "item_id": "7525292600849485064",
                "spu_id_list": [
                    "1731574448173451153"
                ],
                "text": "Nàng nào chán mặc quần dài vô lụm em nó nhaaa #chanvayjean  #chanvay  #chanvayxam ",
                "video_info": {
                    "bit_rate": 1763920,
                    "definition": "540p",
                    "duration": 92.167,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/b6c3064ef0465f502b272f14e40a0856/68898012/video/tos/alisg/tos-alisg-pve-0037c001/oEf0ttIVCyBNUv8iaNircIYhAETSEwA75AQuTA/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&br=3444&bt=1722&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=M2Q0aDNpaTY0ODU5OjZmM0BpMzNsbnU5cmhtNDMzODczNEBhNWM1NS40NWIxMV9hLmBgYSMvbmY2MmRjX2thLS1kMWBzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "a52d923634eb434321f1a1458e22f592",
                    "size": 20321908,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/o4xArcBqiFAcNNvoZIDEAAtCT7UIIuEfw05S1i~tplv-noop.image?t=9276707c&x-expires=1753841682&x-signature=6hcnQjvXaNkU80yfzP4sTKwWD5Q%3D",
                    "video_id": "v1c044g50000d1njbefog65r9kiiq020",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_id": "6915417480602600450",
                    "identity_type": "TTS_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/7652de6aa3b2127dad6b293b1f80fc35~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=a297fc92&x-expires=1753927200&x-signature=gdV0rsbvRFbSomONy5nkBs65cY8%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=maliva",
                    "store_id": "7495035197756378001",
                    "user_name": ""
                },
                "item_id": "7524325534151494929",
                "spu_id_list": [
                    "1731600707452372881"
                ],
                "text": "Chân váy tặng kèm đai nhanh chân k hết chương trình khuyến mại các ní ơi  #chanvay #chanvayjean #chanvaydep #xuhuong #virual ",
                "video_info": {
                    "bit_rate": 2953597,
                    "definition": "540p",
                    "duration": 111.3,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/62d3d085e05a629746b60be228d5b2db/68898025/video/tos/alisg/tos-alisg-pve-0037c001/oEtWEbURMEK85BDMBngFZa6RIfxeoCEBGQX9rA/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&br=5768&bt=2884&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=NmllZDVlZWloZTM0OTtkOUBpanNyO3g5cmp2NDMzODczNEAtYGBgYGNfNjQxLTNjLl9eYSNhbG9yMmRrX2lhLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "785c171a43cd4bd4e7d97e564c685457",
                    "size": 41091921,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/o4REMJFaZIDIC9GAXbUgfGE5orIBRAeBUZm8xB~tplv-noop.image?t=9276707c&x-expires=1753841701&x-signature=0gmnSFXNM01h3nxLNdya%2B9lDtTA%3D",
                    "video_id": "v14044g50000d1lsbgnog65uu8roopdg",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_authorized_bc_id": "7288604756459896834",
                    "identity_id": "8baaa0cf-de58-55fc-847f-556d6268785f",
                    "identity_type": "BC_AUTH_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/65a3b223ce55bc9eee20aae002c777a6~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=0d6ba1e2&x-expires=1753927200&x-signature=VnIEfdkuZXVrRHoDlzvOyxKskdU%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=sg1",
                    "user_name": "anh.long1777"
                },
                "item_id": "7520616844189895943",
                "spu_id_list": [
                    "1731574448173451153"
                ],
                "text": "Chữa lành bằng em chân váy xinh xinh đi mấy người đẹpppp #chanvayjean  #chanvay  #chanvayxam ",
                "video_info": {
                    "bit_rate": 1243788,
                    "definition": "540p",
                    "duration": 100.567,
                    "format": "mp4",
                    "fps": 30,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/e9756a6a9920cd8e47cdf5b7d4c03f1a/6889801a/video/tos/alisg/tos-alisg-pve-0037c001/o4egpFCj4DQqvtIemAdZA2bRQfgkElDEGKI0Sg/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&br=2428&bt=1214&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=OGU0NjQ4PDdkZTNmNzk7aEBpM2p0Nm45cjw8NDMzODczNEAuNmFgMV41XjQxLy4xYjA1YSM1ZTNlMmQ0Y2NhLS1kMWBzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "f2c49d05e158554cbee83bec3401ea56",
                    "size": 15635511,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/osA9tP4GQCmSBZjLIBeSRAgIIDaeevDAzjgUKJ~tplv-noop.image?t=9276707c&x-expires=1753841690&x-signature=LUzK1fDOBUBeLQ1b6kfmoFvtLw4%3D",
                    "video_id": "v1c044g50000d1f9f97og65kh36qhg80",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_authorized_bc_id": "7288604756459896834",
                    "identity_id": "8baaa0cf-de58-55fc-847f-556d6268785f",
                    "identity_type": "BC_AUTH_TT",
                    "profile_image": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/65a3b223ce55bc9eee20aae002c777a6~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&refresh_token=0d6ba1e2&x-expires=1753927200&x-signature=VnIEfdkuZXVrRHoDlzvOyxKskdU%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=8aecc5ac&idc=sg1",
                    "user_name": "anh.long1777"
                },
                "item_id": "7515034853612473608",
                "spu_id_list": [
                    "1731600707452372881"
                ],
                "text": "Lụm ngay iêm chân váy dễ mix đồ đi mấy nànggggg #chanvayjean #chanvay #chanvayphudo #xuhuong ",
                "video_info": {
                    "bit_rate": 1384279,
                    "definition": "540p",
                    "duration": 61.734,
                    "format": "mp4",
                    "fps": 29,
                    "height": 1024,
                    "preview_url": "https://v16-tt4b.tiktokcdn.com/3d372391c22bb371211aa3a0cd6025b9/68897ff3/video/tos/alisg/tos-alisg-pve-0037c001/oEiIgJEKx9sGL1BiAwfsiTAqQIIKQKWuAWJ0xC/?a=1233&bti=Nzc2QDAzNmA%3D&ch=0&cr=0&dr=0&lr=unwatermarked&cd=0%7C0%7C0%7C0&br=2702&bt=1351&cs=0&ds=6&ft=.bvrXInz7ThJUHPKXq8Zmo&mime_type=video_mp4&qs=0&rc=PGVlM2c8ZzY3Njo1Njw1NUBpM2Q1a2s5cnFmNDMzODczNEA0LjQtLWEtXl8xMTJhMl5iYSNqMWg2MmRjNTJhLS1kMTFzcw%3D%3D&vvpl=1&l=2025072910130951AEEB2A1FEBCF4BEF9F&btag=e00090000",
                    "signature": "8eed3d544b106673c5895efcb63de031",
                    "size": 10682136,
                    "video_cover_url": "https://p16-sign-sg.tiktokcdn.com/tos-alisg-p-0037/ocI3nIA07JKxIiiuxsdBGwDCqKAfAuTvgIHGiW~tplv-noop.image?t=9276707c&x-expires=1753841651&x-signature=anFpAYpboUe9svMZ6BcpJvCYLUw%3D",
                    "video_id": "v14044g50000d15c8nfog65h9hk24am0",
                    "width": 576
                }
            },
            {
                "identity_info": {
                    "identity_id": "6741586090190341121",
                    "profile_image": "",
                    "user_name": ""
                },
                "item_id": "7513470958204767495",
                "spu_id_list": [
                    "1731600707452372881"
                ],
                "text": "",
                "video_info": {
                    "video_id": ""
                }
            }
        ],
        "location_ids": [
            "1562822"
        ],
        "operation_status": "ENABLE",
        "optimization_goal": "VALUE",
        "placements": [
            "PLACEMENT_TIKTOK"
        ],
        "product_specific_type": "CUSTOMIZED_PRODUCTS",
        "product_video_specific_type": "CUSTOM_SELECTION",
        "roas_bid": 17.2,
        "roi_protection_enabled": true,
        "schedule_end_time": "2035-06-09 16:38:06",
        "schedule_start_time": "2025-06-11 16:38:06",
        "schedule_type": "SCHEDULE_FROM_NOW",
        "shopping_ads_type": "PRODUCT",
        "store_authorized_bc_id": "7288604756459896834",
        "store_id": "7495035197756378001"
    }
}
```