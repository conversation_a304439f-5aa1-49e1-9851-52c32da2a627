/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Subscription operations
    /// </summary>
    public class SubscriptionApi : ISubscriptionApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<SubscriptionApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the SubscriptionApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public SubscriptionApi(IApiClient apiClient, ILogger<SubscriptionApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        // /// <summary>
        // /// Create a subscription to ad account suspension status, leads, ad group, ad, 
        // /// TikTok Creator Marketplace (TCM) Spark Ads authorization, or TTO campaign video linking
        // /// </summary>
        // /// <param name="body">Request body containing subscription details</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing the subscription ID</returns>
        // public async Task<SubscriptionCreateResponse> CreateSubscriptionAsync(
        //     SubscriptionCreateBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     if (string.IsNullOrWhiteSpace(body.AppId))
        //         throw new ArgumentException("AppId is required", nameof(body));

        //     if (string.IsNullOrWhiteSpace(body.Secret))
        //         throw new ArgumentException("Secret is required", nameof(body));

        //     if (string.IsNullOrWhiteSpace(body.SubscribeEntity))
        //         throw new ArgumentException("SubscribeEntity is required", nameof(body));

        //     if (string.IsNullOrWhiteSpace(body.CallbackUrl))
        //         throw new ArgumentException("CallbackUrl is required", nameof(body));

        //     if (body.SubscriptionDetail == null)
        //         throw new ArgumentException("SubscriptionDetail is required", nameof(body));

        //     if (string.IsNullOrWhiteSpace(body.SubscriptionDetail.AccessToken))
        //         throw new ArgumentException("AccessToken in SubscriptionDetail is required", nameof(body));

        //     _logger?.LogInformation("Creating subscription for app {AppId} with entity {SubscribeEntity}", 
        //         body.AppId, body.SubscribeEntity);

        //     var response = await _apiClient.CallApiAsync<SubscriptionCreateResponse>(
        //         SubscriptionEndpoints.Subscribe,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: null,
        //         authenticationContext: null,
        //         cancellationToken: cancellationToken);

        //     _logger?.LogInformation("Successfully created subscription with ID {SubscriptionId}", 
        //         response.SubscriptionId);

        //     return response;
        // }

        /// <summary>
        /// Get the subscription details of a developer app
        /// </summary>
        /// <param name="request">Request parameters for getting subscription details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing subscription information</returns>
        public async Task<SubscriptionGetResponse> GetSubscriptionsAsync(
            SubscriptionGetRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrWhiteSpace(request.AppId))
                throw new ArgumentException("AppId is required", nameof(request));

            if (string.IsNullOrWhiteSpace(request.Secret))
                throw new ArgumentException("Secret is required", nameof(request));

            _logger?.LogInformation("Getting subscriptions for app {AppId}", request.AppId);

            var queryParams = new Dictionary<string, string>
            {
                ["app_id"] = request.AppId,
                ["secret"] = request.Secret
            };

            if (!string.IsNullOrWhiteSpace(request.SubscribeEntity))
                queryParams["subscribe_entity"] = request.SubscribeEntity;

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();

            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            var response = await _apiClient.CallApiAsync<SubscriptionGetResponse>(
                SubscriptionEndpoints.GetSubscriptions,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authenticationContext: null,
                cancellationToken: cancellationToken);

            _logger?.LogInformation("Successfully retrieved {Count} subscriptions for app {AppId}", 
                response.Subscriptions?.Count ?? 0, request.AppId);

            return response;
        }

        // /// <summary>
        // /// Cancel a subscription
        // /// </summary>
        // /// <param name="body">Request body containing subscription cancellation details</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing the cancelled subscription ID</returns>
        // public async Task<SubscriptionCancelResponse> CancelSubscriptionAsync(
        //     SubscriptionCancelBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     if (string.IsNullOrWhiteSpace(body.AppId))
        //         throw new ArgumentException("AppId is required", nameof(body));

        //     if (string.IsNullOrWhiteSpace(body.Secret))
        //         throw new ArgumentException("Secret is required", nameof(body));

        //     if (string.IsNullOrWhiteSpace(body.SubscriptionId))
        //         throw new ArgumentException("SubscriptionId is required", nameof(body));

        //     _logger?.LogInformation("Cancelling subscription {SubscriptionId} for app {AppId}", 
        //         body.SubscriptionId, body.AppId);

        //     var response = await _apiClient.CallApiAsync<SubscriptionCancelResponse>(
        //         SubscriptionEndpoints.Unsubscribe,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: null,
        //         authenticationContext: null,
        //         cancellationToken: cancellationToken);

        //     _logger?.LogInformation("Successfully cancelled subscription {SubscriptionId}", 
        //         response.SubscriptionId);

        //     return response;
        // }
    }
}
