using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu tài khoản quảng cáo
    /// </summary>
    public class AdAccountSyncApplicationAppService : ApplicationService, IAdAccountSyncApplicationAppService
    {
        private readonly IAdAccountSyncService _adAccountSyncService;

        public AdAccountSyncApplicationAppService(IAdAccountSyncService adAccountSyncService)
        {
            _adAccountSyncService = adAccountSyncService;
        }

        /// <summary>
        /// Đồng bộ tài khoản quảng cáo theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AdAccountSyncResult> SyncAdAccountsAsync(string bcId)
        {
            return await _adAccountSyncService.SyncAdAccountsAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ tài khoản quảng cáo theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của tài khoản quảng cáo</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AdAccountSyncResult> SyncAdAccountAsync(string advertiserId)
        {
            return await _adAccountSyncService.SyncAdAccountAsync(advertiserId);
        }

        /// <summary>
        /// Đồng bộ tất cả tài khoản quảng cáo cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AdAccountSyncResult> SyncAllAdAccountsForAllBcsAsync()
        {
            return await _adAccountSyncService.SyncAllAdAccountsForAllBcsAsync();
        }
    }
}