using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTok.BalanceBusinessCenters;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp;

namespace TikTok.BalanceBusinessCenters
{
    /// <summary>
    /// Service implementation cho ngân sách Business Center
    /// </summary>
    public class BalanceBusinessCenterAppService :
        CrudAppService<
            RawBalanceBusinessCenterEntity,
            BalanceBusinessCenterDto,
            Guid,
            GetBalanceBusinessCenterListDto,
            CreateBalanceBusinessCenterDto,
            UpdateBalanceBusinessCenterDto>,
        IBalanceBusinessCenterAppService
    {
        private readonly IBalanceBusinessCenterRepository _balanceBusinessCenterRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="repository">Repository</param>
        /// <param name="balanceBusinessCenterRepository">Balance Business Center Repository</param>
        public BalanceBusinessCenterAppService(
            IRepository<RawBalanceBusinessCenterEntity, Guid> repository,
            IBalanceBusinessCenterRepository balanceBusinessCenterRepository) : base(repository)
        {
            _balanceBusinessCenterRepository = balanceBusinessCenterRepository;

            // Cấu hình permission
            GetPolicyName = TikTokPermissions.BalanceBusinessCenters.Default;
            GetListPolicyName = TikTokPermissions.BalanceBusinessCenters.Default;
            CreatePolicyName = TikTokPermissions.BalanceBusinessCenters.Create;
            UpdatePolicyName = TikTokPermissions.BalanceBusinessCenters.Edit;
            DeletePolicyName = TikTokPermissions.BalanceBusinessCenters.Delete;
        }

        /// <summary>
        /// Lấy ngân sách Business Center theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Ngân sách Business Center</returns>
        [Authorize(TikTokPermissions.BalanceBusinessCenters.Default)]
        public async Task<BalanceBusinessCenterDto> GetByBcIdAsync(string bcId)
        {
            var entity = await _balanceBusinessCenterRepository.GetByBcIdAsync(bcId);
            return ObjectMapper.Map<RawBalanceBusinessCenterEntity, BalanceBusinessCenterDto>(entity);
        }

        /// <summary>
        /// Lấy ngân sách Business Center theo BC ID và ngày
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="date">Ngày cần lấy dữ liệu</param>
        /// <returns>Ngân sách Business Center</returns>
        [Authorize(TikTokPermissions.BalanceBusinessCenters.Default)]
        public async Task<BalanceBusinessCenterDto> GetByBcIdAndDateAsync(string bcId, DateTime date)
        {
            // Đảm bảo chỉ so sánh ngày tháng năm, không có giờ phút giây
            var dateOnly = date.Date;
            var entity = await _balanceBusinessCenterRepository.GetByBcIdAndDateAsync(bcId, dateOnly);
            return ObjectMapper.Map<RawBalanceBusinessCenterEntity, BalanceBusinessCenterDto>(entity);
        }

        /// <summary>
        /// Lấy bản ghi mới nhất theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Bản ghi mới nhất</returns>
        [Authorize(TikTokPermissions.BalanceBusinessCenters.Default)]
        public async Task<BalanceBusinessCenterDto> GetLatestByBcIdAsync(string bcId)
        {
            var entity = await _balanceBusinessCenterRepository.GetLatestByBcIdAsync(bcId);
            if (entity == null)
            {
                throw new UserFriendlyException($"Không tìm thấy ngân sách Business Center với BC ID: {bcId}");
            }

            return ObjectMapper.Map<RawBalanceBusinessCenterEntity, BalanceBusinessCenterDto>(entity);
        }

        /// <summary>
        /// Lấy danh sách ngân sách Business Center theo tiền tệ
        /// </summary>
        /// <param name="currency">Tiền tệ</param>
        /// <returns>Danh sách ngân sách Business Center</returns>
        [Authorize(TikTokPermissions.BalanceBusinessCenters.Default)]
        public async Task<PagedResultDto<BalanceBusinessCenterDto>> GetByCurrencyAsync(string currency)
        {
            var entities = await _balanceBusinessCenterRepository.GetByCurrencyAsync(currency);
            var dtos = ObjectMapper.Map<RawBalanceBusinessCenterEntity[], BalanceBusinessCenterDto[]>(entities.ToArray());

            return new PagedResultDto<BalanceBusinessCenterDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        /// <summary>
        /// Ghi đè phương thức GetListAsync để sử dụng repository
        /// </summary>
        /// <param name="input">Input để lấy danh sách</param>
        /// <returns>Danh sách ngân sách Business Center</returns>
        public override async Task<PagedResultDto<BalanceBusinessCenterDto>> GetListAsync(GetBalanceBusinessCenterListDto input)
        {
            // Sử dụng repository để lấy danh sách và tổng số
            var entities = await _balanceBusinessCenterRepository.GetListAsync(
                sorting: input.Sorting,
                maxResultCount: input.MaxResultCount,
                skipCount: input.SkipCount,
                filter: input.Filter,
                bcId: input.BcId,
                currency: input.Currency,
                minAccountBalance: input.MinAccountBalance,
                maxAccountBalance: input.MaxAccountBalance,
                minCashBalance: input.MinCashBalance,
                maxCashBalance: input.MaxCashBalance,
                dateFrom: input.DateFrom,
                dateTo: input.DateTo,
                timezone: input.Timezone);

            var totalCount = await _balanceBusinessCenterRepository.GetCountAsync(
                filter: input.Filter,
                bcId: input.BcId,
                currency: input.Currency,
                minAccountBalance: input.MinAccountBalance,
                maxAccountBalance: input.MaxAccountBalance,
                minCashBalance: input.MinCashBalance,
                maxCashBalance: input.MaxCashBalance,
                dateFrom: input.DateFrom,
                dateTo: input.DateTo,
                timezone: input.Timezone);

            var dtos = ObjectMapper.Map<RawBalanceBusinessCenterEntity[], BalanceBusinessCenterDto[]>(entities.ToArray());

            return new PagedResultDto<BalanceBusinessCenterDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }

        /// <summary>
        /// Ghi đè phương thức CreateAsync để thêm validation
        /// </summary>
        /// <param name="input">Input để tạo mới</param>
        /// <returns>Ngân sách Business Center đã tạo</returns>
        [Authorize(TikTokPermissions.BalanceBusinessCenters.Create)]
        public override async Task<BalanceBusinessCenterDto> CreateAsync(CreateBalanceBusinessCenterDto input)
        {
            // Kiểm tra xem BC ID đã tồn tại chưa
            if (await _balanceBusinessCenterRepository.IsBcIdExistsAsync(input.BcId))
            {
                throw new UserFriendlyException($"BC ID '{input.BcId}' đã tồn tại.");
            }

            // Đảm bảo Date chỉ lưu ngày tháng năm nếu có giá trị
            if (input.Date.HasValue)
            {
                input.Date = input.Date.Value.Date;
            }

            return await base.CreateAsync(input);
        }

        /// <summary>
        /// Ghi đè phương thức UpdateAsync để đảm bảo Date được lưu đúng cách
        /// </summary>
        /// <param name="id">ID của entity</param>
        /// <param name="input">Input để cập nhật</param>
        /// <returns>Ngân sách Business Center đã cập nhật</returns>
        [Authorize(TikTokPermissions.BalanceBusinessCenters.Edit)]
        public override async Task<BalanceBusinessCenterDto> UpdateAsync(Guid id, UpdateBalanceBusinessCenterDto input)
        {
            // Đảm bảo Date chỉ lưu ngày tháng năm nếu có giá trị
            if (input.Date.HasValue)
            {
                input.Date = input.Date.Value.Date;
            }

            return await base.UpdateAsync(id, input);
        }
    }
}