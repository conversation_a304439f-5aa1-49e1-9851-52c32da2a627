/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Get Product Sets Models

    /// <summary>
    /// Request for getting product sets
    /// </summary>
    public class GetProductSetsRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the catalog that you want to get product sets from (Required)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the product set that you want to get (Optional)
        /// If not passed, returns complete list of product sets
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string? ProductSetId { get; set; }

        /// <summary>
        /// Whether to return the response parameter product_count
        /// Default value: true
        /// </summary>
        [JsonPropertyName("return_product_count")]
        public bool? ReturnProductCount { get; set; }
    }

    /// <summary>
    /// Response for getting product sets
    /// </summary>
    public class GetProductSetsResponse
    {
        /// <summary>
        /// A list of product sets
        /// </summary>
        [JsonPropertyName("list")]
        public List<ProductSetInfo> List { get; set; } = new List<ProductSetInfo>();
    }



    #endregion

    #region Get Products in Set Models

    /// <summary>
    /// Request for getting products in a product set
    /// </summary>
    public class GetProductsInSetRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the catalog that you want to get product sets from (Required)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the product set that you want to get (Required)
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string ProductSetId { get; set; } = string.Empty;

        /// <summary>
        /// The number of the current page. The default value is 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// The page size. The default size is 20
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Response for getting products in a product set
    /// </summary>
    public class GetProductsInSetResponse
    {
        /// <summary>
        /// The product set ID
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string ProductSetId { get; set; } = string.Empty;

        /// <summary>
        /// The catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// The number of products in the product set
        /// </summary>
        [JsonPropertyName("product_count")]
        public int? ProductCount { get; set; }

        /// <summary>
        /// A list of products
        /// </summary>
        [JsonPropertyName("products")]
        public List<ProductSetProductInfo> Products { get; set; } = new List<ProductSetProductInfo>();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Product information in a product set
    /// </summary>
    public class ProductSetProductInfo
    {
        /// <summary>
        /// The product ID
        /// </summary>
        [JsonPropertyName("product_id")]
        public long ProductId { get; set; }

        /// <summary>
        /// The product name
        /// </summary>
        [JsonPropertyName("product_name")]
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// A unique ID for the E-commerce product (E-commerce catalog products only)
        /// </summary>
        [JsonPropertyName("sku_id")]
        public string? SkuId { get; set; }

        /// <summary>
        /// A unique ID for the hotel (hotel catalog products only)
        /// </summary>
        [JsonPropertyName("hotel_id")]
        public string? HotelId { get; set; }

        /// <summary>
        /// A unique ID for the flight (flight catalog products only)
        /// </summary>
        [JsonPropertyName("flight_id")]
        public string? FlightId { get; set; }

        /// <summary>
        /// A unique ID for the destination (destination catalog products only)
        /// </summary>
        [JsonPropertyName("destination_id")]
        public string? DestinationId { get; set; }

        /// <summary>
        /// A unique ID for the vehicle (Auto-Inventory or Auto-Model catalog products only)
        /// </summary>
        [JsonPropertyName("vehicle_id")]
        public string? VehicleId { get; set; }
    }

    #endregion

    #region Create Product Set Models

    /// <summary>
    /// Request for creating a product set
    /// </summary>
    public class CreateProductSetRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// The catalog ID (Required)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// The product set name. It must be less than 28 characters (Required)
        /// </summary>
        [JsonPropertyName("product_set_name")]
        public string ProductSetName { get; set; } = string.Empty;

        /// <summary>
        /// The filter conditions (Required)
        /// </summary>
        [JsonPropertyName("conditions")]
        public ProductSetConditions Conditions { get; set; } = new ProductSetConditions();
    }

    /// <summary>
    /// Response for creating a product set
    /// </summary>
    public class CreateProductSetResponse
    {
        /// <summary>
        /// The product set ID
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string ProductSetId { get; set; } = string.Empty;

        /// <summary>
        /// The product set name
        /// </summary>
        [JsonPropertyName("product_set_name")]
        public string ProductSetName { get; set; } = string.Empty;

        /// <summary>
        /// The number of products in the product set
        /// </summary>
        [JsonPropertyName("product_count")]
        public int? ProductCount { get; set; }
    }

    #endregion

    #region Update Product Set Models

    /// <summary>
    /// Request for updating a product set
    /// </summary>
    public class UpdateProductSetRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// The catalog ID (Required)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// The product set ID (Required)
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string ProductSetId { get; set; } = string.Empty;

        /// <summary>
        /// The product set name. It must be less than 28 characters (Conditional)
        /// You need to pass at least product_set_name or conditions or both
        /// </summary>
        [JsonPropertyName("product_set_name")]
        public string? ProductSetName { get; set; }

        /// <summary>
        /// The filter conditions (Conditional)
        /// You need to pass at least product_set_name or conditions or both
        /// </summary>
        [JsonPropertyName("conditions")]
        public ProductSetConditions? Conditions { get; set; }
    }

    /// <summary>
    /// Response for updating a product set
    /// </summary>
    public class UpdateProductSetResponse
    {
        /// <summary>
        /// The product set ID
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string ProductSetId { get; set; } = string.Empty;

        /// <summary>
        /// The product set name
        /// </summary>
        [JsonPropertyName("product_set_name")]
        public string ProductSetName { get; set; } = string.Empty;

        /// <summary>
        /// The number of products in the product set
        /// </summary>
        [JsonPropertyName("product_count")]
        public int? ProductCount { get; set; }
    }

    #endregion

    #region Delete Product Sets Models

    /// <summary>
    /// Request for deleting product sets
    /// </summary>
    public class DeleteProductSetsRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// The product catalog ID (Required)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// The IDs of the product sets that you want to delete (Required)
        /// You can include 1 to 10 IDs in the list
        /// </summary>
        [JsonPropertyName("product_set_ids")]
        public List<string> ProductSetIds { get; set; } = new List<string>();
    }

    /// <summary>
    /// Response for deleting product sets
    /// </summary>
    public class DeleteProductSetsResponse
    {
        /// <summary>
        /// The IDs of the product sets that have been deleted
        /// </summary>
        [JsonPropertyName("product_set_ids")]
        public List<string> ProductSetIds { get; set; } = new List<string>();
    }

    #endregion
}
