using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.AdAccounts
{
    /// <summary>
    /// Service interface để truy vấn dữ liệu người hỗ trợ cho các Activity
    /// </summary>
    public interface IAdAccountSupporterQueryService : IScopedDependency
    {
        /// <summary>
        /// Lấy danh sách người hỗ trợ theo AdvertiserIds
        /// </summary>
        /// <param name="advertiserIds">Danh sách AdvertiserIds</param>
        /// <param name="includeInactive"><PERSON><PERSON> bao gồm người hỗ trợ không hoạt động không</param>
        /// <returns>Danh sách AdAccountSupporterListDto</returns>
        Task<List<AdAccountSupporterListDto>> GetSupportersByAdvertiserIdsAsync(
            List<string> advertiserIds, 
            bool includeInactive = false);

        /// <summary>
        /// L<PERSON>y danh sách người hỗ trợ theo Business Center ID
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="includeInactive">Có bao gồm người hỗ trợ không hoạt động không</param>
        /// <returns>Danh sách AdAccountSupporterListDto</returns>
        Task<List<AdAccountSupporterListDto>> GetSupportersByBcIdAsync(
            string bcId, 
            bool includeInactive = false);

        /// <summary>
        /// Lấy danh sách người hỗ trợ theo AdvertiserIds và Business Center ID
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="advertiserIds">Danh sách AdvertiserIds</param>
        /// <param name="includeInactive">Có bao gồm người hỗ trợ không hoạt động không</param>
        /// <returns>Danh sách AdAccountSupporterListDto</returns>
        Task<List<AdAccountSupporterListDto>> GetSupportersByBcIdAndAdvertiserIdsAsync(
            string bcId, 
            List<string> advertiserIds, 
            bool includeInactive = false);

    }
}
