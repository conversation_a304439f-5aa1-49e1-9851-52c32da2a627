/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Create Split Test Models

    /// <summary>
    /// Request body for creating a split test
    /// </summary>
    public class SplitTestCreateBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Split test level. Enum values: ADGROUP, CAMPAIGN. Default: ADGROUP
        /// </summary>
        [JsonPropertyName("split_test_level")]
        public string? SplitTestLevel { get; set; }

        /// <summary>
        /// adgroup_ids bound to this split-test (for ad group level split test)
        /// </summary>
        [JsonPropertyName("object_ids")]
        public List<string>? ObjectIds { get; set; }

        /// <summary>
        /// List of cells in split test (required when split_test_level is CAMPAIGN)
        /// </summary>
        [JsonPropertyName("cells")]
        public List<SplitTestCell>? Cells { get; set; }

        /// <summary>
        /// Split test variable
        /// </summary>
        [JsonPropertyName("test_variable")]
        public string? TestVariable { get; set; }

        /// <summary>
        /// The key metric you want to monitor
        /// </summary>
        [JsonPropertyName("key_metric")]
        public string? KeyMetric { get; set; }

        /// <summary>
        /// The total split test budget (required when split_test_level is ADGROUP)
        /// </summary>
        [JsonPropertyName("budget")]
        public float? Budget { get; set; }

        /// <summary>
        /// The start time of this split test, in the format of YYYY-MM-DD HH:MM:SS (UTC+0)
        /// </summary>
        [JsonPropertyName("start_time")]
        public string StartTime { get; set; } = string.Empty;

        /// <summary>
        /// The end time of this split test, in the format of YYYY-MM-DD HH:MM:SS (UTC+0)
        /// </summary>
        [JsonPropertyName("end_time")]
        public string EndTime { get; set; } = string.Empty;
    }

    /// <summary>
    /// Split test cell information
    /// </summary>
    public class SplitTestCell
    {
        /// <summary>
        /// List of campaign_id or adgroup_id in the cell
        /// </summary>
        [JsonPropertyName("object_ids")]
        public List<string> ObjectIds { get; set; } = new List<string>();
    }

    /// <summary>
    /// Response for creating a split test
    /// </summary>
    public class SplitTestCreateResponse
    {
        /// <summary>
        /// Split test group ID
        /// </summary>
        [JsonPropertyName("split_test_group_id")]
        public string SplitTestGroupId { get; set; } = string.Empty;
    }

    #endregion

    #region Update Split Test Models

    /// <summary>
    /// Request body for updating a split test
    /// </summary>
    public class SplitTestUpdateBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Split test group ID
        /// </summary>
        [JsonPropertyName("split_test_group_id")]
        public string SplitTestGroupId { get; set; } = string.Empty;

        /// <summary>
        /// The start time of this split test, in the format of YYYY-MM-DD HH:MM:SS (UTC+0)
        /// </summary>
        [JsonPropertyName("start_time")]
        public string StartTime { get; set; } = string.Empty;

        /// <summary>
        /// The end time of this split test, in the format of YYYY-MM-DD HH:MM:SS (UTC+0)
        /// </summary>
        [JsonPropertyName("end_time")]
        public string EndTime { get; set; } = string.Empty;
    }

    #endregion

    #region End Split Test Models

    /// <summary>
    /// Request body for ending a split test
    /// </summary>
    public class SplitTestEndBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Split test group ID
        /// </summary>
        [JsonPropertyName("split_test_group_id")]
        public string SplitTestGroupId { get; set; } = string.Empty;
    }

    #endregion

    #region Promote Split Test Models

    /// <summary>
    /// Request body for promoting the winning ad group in a split test
    /// </summary>
    public class SplitTestPromoteBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Split test group ID
        /// </summary>
        [JsonPropertyName("split_test_group_id")]
        public string SplitTestGroupId { get; set; } = string.Empty;

        /// <summary>
        /// The winning ad group ID
        /// </summary>
        [JsonPropertyName("winning_object_id")]
        public string WinningObjectId { get; set; } = string.Empty;
    }

    #endregion

    #region Get Split Test Result Models

    /// <summary>
    /// Response for getting split test results
    /// </summary>
    public class SplitTestResultResponse
    {
        /// <summary>
        /// Status of the split test
        /// </summary>
        [JsonPropertyName("split_test_status")]
        public string SplitTestStatus { get; set; } = string.Empty;

        /// <summary>
        /// Split test level
        /// </summary>
        [JsonPropertyName("split_test_level")]
        public string SplitTestLevel { get; set; } = string.Empty;

        /// <summary>
        /// Information about the ad groups associated with the split test
        /// </summary>
        [JsonPropertyName("object_info")]
        public List<SplitTestObjectInfo>? ObjectInfo { get; set; }

        /// <summary>
        /// Cell list of the split test
        /// </summary>
        [JsonPropertyName("cells")]
        public List<SplitTestCell>? Cells { get; set; }

        /// <summary>
        /// Split test variable
        /// </summary>
        [JsonPropertyName("test_variable")]
        public string? TestVariable { get; set; }

        /// <summary>
        /// The key metric when you created this split test
        /// </summary>
        [JsonPropertyName("key_metric")]
        public string? KeyMetric { get; set; }

        /// <summary>
        /// The start time of this split test
        /// </summary>
        [JsonPropertyName("start_time")]
        public string? StartTime { get; set; }

        /// <summary>
        /// The end time of this split test
        /// </summary>
        [JsonPropertyName("end_time")]
        public string? EndTime { get; set; }

        /// <summary>
        /// Test result information
        /// </summary>
        [JsonPropertyName("p_value_info")]
        public List<SplitTestPValueInfo>? PValueInfo { get; set; }
    }

    /// <summary>
    /// Split test object information
    /// </summary>
    public class SplitTestObjectInfo
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdgroupId { get; set; }

        /// <summary>
        /// Ad group name
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdgroupName { get; set; }

        /// <summary>
        /// Optimization goal of the ad group
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string? OptimizationGoal { get; set; }
    }

    /// <summary>
    /// Split test P-value information
    /// </summary>
    public class SplitTestPValueInfo
    {
        /// <summary>
        /// Metric name
        /// </summary>
        [JsonPropertyName("metric")]
        public string Metric { get; set; } = string.Empty;

        /// <summary>
        /// P value of each metric
        /// </summary>
        [JsonPropertyName("p_value")]
        public float PValue { get; set; }
    }

    #endregion
}
