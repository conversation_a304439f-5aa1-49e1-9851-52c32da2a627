/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi.Api;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the BC Asset Groups API
    /// </summary>
    public class BcAssetGroupsExample
    {
        private readonly BcAssetsApi _bcAssetsApi;
        private readonly string _accessToken;
        private readonly string _bcId;

        public BcAssetGroupsExample(string accessToken, string bcId)
        {
            _accessToken = accessToken;
            _bcId = bcId;
            _bcAssetsApi = new BcAssetsApi();
        }

        /// <summary>
        /// Example: Create an Asset Group
        /// </summary>
        public async Task CreateAssetGroupExample()
        {
            try
            {
                var request = new CreateAssetGroupRequest
                {
                    BcId = _bcId,
                    AssetGroupName = "My Asset Group",
                    Assets = new List<AssetGroupAsset>
                    {
                        new AssetGroupAsset
                        {
                            AssetId = "1234567890123456789", // Replace with actual advertiser ID
                            AssetType = "ADVERTISER"
                        }
                    },
                    Members = new List<AssetGroupMember>
                    {
                        new AssetGroupMember
                        {
                            MemberId = "9876543210987654321", // Replace with actual member ID
                            AssetRoles = new Dictionary<string, string>
                            {
                                { "ADVERTISER", "ADVERTISER_ROLE_ADMIN" }
                            }
                        }
                    }
                };

                var response = await _bcAssetsApi.CreateAssetGroupAsync(_accessToken, request);
                
                Console.WriteLine($"Asset Group created successfully!");
                Console.WriteLine($"Asset Group ID: {response.Data?.AssetGroupId}");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating Asset Group: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: List all Asset Groups
        /// </summary>
        public async Task ListAssetGroupsExample()
        {
            try
            {
                var request = new ListAssetGroupsRequest
                {
                    BcId = _bcId,
                    Page = 1,
                    PageSize = 10,
                    Filtering = new AssetGroupListFiltering
                    {
                        Keyword = "My Asset Group" // Optional: filter by name or ID
                    }
                };

                var response = await _bcAssetsApi.ListAssetGroupsAsync(_accessToken, request);
                
                Console.WriteLine($"Found {response.Data?.AssetGroups?.Count} Asset Groups:");
                
                if (response.Data?.AssetGroups != null)
                {
                    foreach (var assetGroup in response.Data.AssetGroups)
                    {
                        Console.WriteLine($"- ID: {assetGroup.AssetGroupId}, Name: {assetGroup.AssetGroupName}");
                    }
                }

                Console.WriteLine($"Page Info: {response.Data?.PageInfo?.Page}/{response.Data?.PageInfo?.TotalPage}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error listing Asset Groups: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get Asset Group details (assets)
        /// </summary>
        public async Task GetAssetGroupAssetsExample(string assetGroupId)
        {
            try
            {
                var request = new GetAssetGroupRequest
                {
                    BcId = _bcId,
                    AssetGroupId = assetGroupId,
                    QueryEntity = "ASSET",
                    Page = 1,
                    PageSize = 10
                };

                var response = await _bcAssetsApi.GetAssetGroupAsync(_accessToken, request);
                
                Console.WriteLine($"Asset Group: {response.Data?.AssetGroup?.AssetGroupName}");
                Console.WriteLine($"Assets in Asset Group:");
                
                if (response.Data?.AssetGroup?.Assets != null)
                {
                    foreach (var asset in response.Data.AssetGroup.Assets)
                    {
                        Console.WriteLine($"- Asset ID: {asset.AssetId}, Type: {asset.AssetType}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting Asset Group assets: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get Asset Group details (members)
        /// </summary>
        public async Task GetAssetGroupMembersExample(string assetGroupId)
        {
            try
            {
                var request = new GetAssetGroupRequest
                {
                    BcId = _bcId,
                    AssetGroupId = assetGroupId,
                    QueryEntity = "MEMBER",
                    Page = 1,
                    PageSize = 10
                };

                var response = await _bcAssetsApi.GetAssetGroupAsync(_accessToken, request);
                
                Console.WriteLine($"Asset Group: {response.Data?.AssetGroup?.AssetGroupName}");
                Console.WriteLine($"Members in Asset Group:");
                
                if (response.Data?.AssetGroup?.Members != null)
                {
                    foreach (var member in response.Data.AssetGroup.Members)
                    {
                        Console.WriteLine($"- Member ID: {member.MemberId}");
                        if (member.AssetRoles != null)
                        {
                            foreach (var role in member.AssetRoles)
                            {
                                Console.WriteLine($"  Role for {role.Key}: {role.Value}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting Asset Group members: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Update Asset Group name
        /// </summary>
        public async Task UpdateAssetGroupNameExample(string assetGroupId)
        {
            try
            {
                var request = new UpdateAssetGroupRequest
                {
                    BcId = _bcId,
                    AssetGroupId = assetGroupId,
                    UpdateEntity = "NAME",
                    AssetGroupName = "Updated Asset Group Name"
                };

                var response = await _bcAssetsApi.UpdateAssetGroupAsync(_accessToken, request);
                
                Console.WriteLine($"Asset Group name updated successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating Asset Group name: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Add assets to Asset Group
        /// </summary>
        public async Task AddAssetsToAssetGroupExample(string assetGroupId)
        {
            try
            {
                var request = new UpdateAssetGroupRequest
                {
                    BcId = _bcId,
                    AssetGroupId = assetGroupId,
                    UpdateEntity = "ASSET",
                    Action = "ADD",
                    Assets = new List<AssetGroupAsset>
                    {
                        new AssetGroupAsset
                        {
                            AssetId = "1111111111111111111", // Replace with actual advertiser ID
                            AssetType = "ADVERTISER"
                        }
                    }
                };

                var response = await _bcAssetsApi.UpdateAssetGroupAsync(_accessToken, request);
                
                Console.WriteLine($"Assets added to Asset Group successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error adding assets to Asset Group: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Delete Asset Group
        /// </summary>
        public async Task DeleteAssetGroupExample(string assetGroupId)
        {
            try
            {
                var request = new DeleteAssetGroupsRequest
                {
                    BcId = _bcId,
                    AssetGroupIds = new List<string> { assetGroupId }
                };

                var response = await _bcAssetsApi.DeleteAssetGroupsAsync(_accessToken, request);
                
                Console.WriteLine($"Asset Group deleted successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting Asset Group: {ex.Message}");
            }
        }

        /// <summary>
        /// Run all examples
        /// </summary>
        public async Task RunAllExamples()
        {
            Console.WriteLine("=== BC Asset Groups API Examples ===\n");

            // Create Asset Group
            Console.WriteLine("1. Creating Asset Group...");
            await CreateAssetGroupExample();
            Console.WriteLine();

            // List Asset Groups
            Console.WriteLine("2. Listing Asset Groups...");
            await ListAssetGroupsExample();
            Console.WriteLine();

            // Note: For the remaining examples, you would need actual Asset Group IDs
            // Console.WriteLine("3. Getting Asset Group assets...");
            // await GetAssetGroupAssetsExample("your_asset_group_id");
            // Console.WriteLine();

            // Console.WriteLine("4. Getting Asset Group members...");
            // await GetAssetGroupMembersExample("your_asset_group_id");
            // Console.WriteLine();

            // Console.WriteLine("5. Updating Asset Group name...");
            // await UpdateAssetGroupNameExample("your_asset_group_id");
            // Console.WriteLine();

            // Console.WriteLine("6. Adding assets to Asset Group...");
            // await AddAssetsToAssetGroupExample("your_asset_group_id");
            // Console.WriteLine();

            // Console.WriteLine("7. Deleting Asset Group...");
            // await DeleteAssetGroupExample("your_asset_group_id");
            // Console.WriteLine();

            Console.WriteLine("=== Examples completed ===");
        }
    }
}
