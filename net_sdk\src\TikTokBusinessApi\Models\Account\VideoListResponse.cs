/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for video list
    /// </summary>
    public class VideoListResponse
    {
        /// <summary>
        /// The list of public video posts of the TikTok account
        /// </summary>
        [JsonPropertyName("videos")]
        public List<VideoInfo> Videos { get; set; } = new();

        /// <summary>
        /// Cursor for the next page of results
        /// </summary>
        [JsonPropertyName("cursor")]
        public long Cursor { get; set; }

        /// <summary>
        /// Whether an additional page of data is available
        /// </summary>
        [JsonPropertyName("has_more")]
        public bool HasMore { get; set; }
    }

    /// <summary>
    /// Video information and metrics
    /// </summary>
    public class VideoInfo
    {
        /// <summary>
        /// Unique identifier for the video
        /// </summary>
        [JsonPropertyName("item_id")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// Preview thumbnail URL for the video
        /// </summary>
        [JsonPropertyName("thumbnail_url")]
        public string? ThumbnailUrl { get; set; }

        /// <summary>
        /// A shareable URL for the video
        /// </summary>
        [JsonPropertyName("share_url")]
        public string? ShareUrl { get; set; }

        /// <summary>
        /// An embeddable URL for the video
        /// </summary>
        [JsonPropertyName("embed_url")]
        public string? EmbedUrl { get; set; }

        /// <summary>
        /// The caption/description for the video
        /// </summary>
        [JsonPropertyName("caption")]
        public string? Caption { get; set; }

        /// <summary>
        /// Duration of the video, in seconds
        /// </summary>
        [JsonPropertyName("video_duration")]
        public float? VideoDuration { get; set; }

        /// <summary>
        /// Likes. The total number of likes the video has received
        /// </summary>
        [JsonPropertyName("likes")]
        public int? Likes { get; set; }

        /// <summary>
        /// Comments. The total number of comments the video has received
        /// </summary>
        [JsonPropertyName("comments")]
        public int? Comments { get; set; }

        /// <summary>
        /// Shares. The total number of times the video has been shared
        /// </summary>
        [JsonPropertyName("shares")]
        public int? Shares { get; set; }

        /// <summary>
        /// Favorites. The total number of times the video has been added to favorites
        /// </summary>
        [JsonPropertyName("favorites")]
        public int? Favorites { get; set; }

        /// <summary>
        /// Unix/epoch date-time when the video was posted
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Reach. The number of people who watched your published content at least once
        /// </summary>
        [JsonPropertyName("reach")]
        public int? Reach { get; set; }

        /// <summary>
        /// Video views. The number of times viewers watched your video
        /// </summary>
        [JsonPropertyName("video_views")]
        public int? VideoViews { get; set; }

        /// <summary>
        /// Total play time. The amount of time viewers spent watching your video
        /// </summary>
        [JsonPropertyName("total_time_watched")]
        public float? TotalTimeWatched { get; set; }

        /// <summary>
        /// Average watch time. The average time viewers spent watching your video
        /// </summary>
        [JsonPropertyName("average_time_watched")]
        public float? AverageTimeWatched { get; set; }

        /// <summary>
        /// Watched full video rate. The percentage of viewers who finish watching your video
        /// </summary>
        [JsonPropertyName("full_video_watched_rate")]
        public float? FullVideoWatchedRate { get; set; }

        /// <summary>
        /// New followers. The number of viewers who started following you
        /// </summary>
        [JsonPropertyName("new_followers")]
        public int? NewFollowers { get; set; }

        /// <summary>
        /// Profile views. The total number of profile views from users who visited through your video
        /// </summary>
        [JsonPropertyName("profile_views")]
        public int? ProfileViews { get; set; }

        /// <summary>
        /// Website clicks. The total number of clicks on your website link
        /// </summary>
        [JsonPropertyName("website_clicks")]
        public int? WebsiteClicks { get; set; }

        /// <summary>
        /// Phone number clicks. The total number of clicks on your phone number link
        /// </summary>
        [JsonPropertyName("phone_number_clicks")]
        public int? PhoneNumberClicks { get; set; }

        /// <summary>
        /// Lead submissions. The total number of leads collected
        /// </summary>
        [JsonPropertyName("lead_submissions")]
        public int? LeadSubmissions { get; set; }

        /// <summary>
        /// App download link clicks. The total number of clicks on your app download link
        /// </summary>
        [JsonPropertyName("app_download_clicks")]
        public int? AppDownloadClicks { get; set; }

        /// <summary>
        /// Email clicks. The total number of times people have clicked the Email button
        /// </summary>
        [JsonPropertyName("email_clicks")]
        public int? EmailClicks { get; set; }

        /// <summary>
        /// Address clicks. The total number of times people have clicked the Address button
        /// </summary>
        [JsonPropertyName("address_clicks")]
        public int? AddressClicks { get; set; }

        /// <summary>
        /// Audience retention. This metric indicates how many of your viewers are still watching
        /// </summary>
        [JsonPropertyName("video_view_retention")]
        public List<VideoViewRetention>? VideoViewRetention { get; set; }

        /// <summary>
        /// Traffic source. This metric helps you understand the breakdown of traffic sources
        /// </summary>
        [JsonPropertyName("impression_sources")]
        public List<ImpressionSource>? ImpressionSources { get; set; }

        /// <summary>
        /// Gender. The distribution of your viewers by gender
        /// </summary>
        [JsonPropertyName("audience_genders")]
        public List<AudienceGender>? AudienceGenders { get; set; }

        /// <summary>
        /// Top countries. The distribution of your viewers by the top countries or regions
        /// </summary>
        [JsonPropertyName("audience_countries")]
        public List<AudienceCountry>? AudienceCountries { get; set; }

        /// <summary>
        /// Top cities. The distribution of your viewers by the top cities
        /// </summary>
        [JsonPropertyName("audience_cities")]
        public List<AudienceCity>? AudienceCities { get; set; }

        /// <summary>
        /// Viewer types. The breakdown of your audience into new viewers versus returning viewers
        /// </summary>
        [JsonPropertyName("audience_types")]
        public List<AudienceType>? AudienceTypes { get; set; }

        /// <summary>
        /// Engagement likes. The distribution of your viewers who liked your video at specific points
        /// </summary>
        [JsonPropertyName("engagement_likes")]
        public List<EngagementLike>? EngagementLikes { get; set; }
    }

    /// <summary>
    /// Video view retention data
    /// </summary>
    public class VideoViewRetention
    {
        /// <summary>
        /// A specific second in the video's timeline
        /// </summary>
        [JsonPropertyName("second")]
        public string Second { get; set; } = string.Empty;

        /// <summary>
        /// The percentage of viewers who are still watching your video at the specific second
        /// </summary>
        [JsonPropertyName("percentage")]
        public float Percentage { get; set; }
    }

    /// <summary>
    /// Impression source data
    /// </summary>
    public class ImpressionSource
    {
        /// <summary>
        /// The traffic source type
        /// </summary>
        [JsonPropertyName("impression_source")]
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// The percentage of views from the source type
        /// </summary>
        [JsonPropertyName("percentage")]
        public float Percentage { get; set; }
    }

    /// <summary>
    /// Audience type data
    /// </summary>
    public class AudienceType
    {
        /// <summary>
        /// Audience type
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Percentage of viewers associated with the audience type
        /// </summary>
        [JsonPropertyName("percentage")]
        public float Percentage { get; set; }
    }

    /// <summary>
    /// Engagement like data
    /// </summary>
    public class EngagementLike
    {
        /// <summary>
        /// A specific second in the video's timeline
        /// </summary>
        [JsonPropertyName("second")]
        public string Second { get; set; } = string.Empty;

        /// <summary>
        /// Percentage of viewers who liked your video at the specific second
        /// </summary>
        [JsonPropertyName("percentage")]
        public float Percentage { get; set; }
    }
}
