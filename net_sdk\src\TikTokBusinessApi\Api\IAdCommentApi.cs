/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Ad Comment operations
    /// </summary>
    public interface IAdCommentApi
    {
        /// <summary>
        /// Get comments under video ads of an ad account
        /// </summary>
        /// <param name="request">Comment list request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Comment list response containing comments and pagination info</returns>
        Task<AdCommentListResponse> GetCommentsAsync(
            AdCommentListRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get related comments (original comment and replies, or reply and its original comment)
        /// </summary>
        /// <param name="request">Comment reference request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Comment list response containing related comments</returns>
        Task<AdCommentListResponse> GetRelatedCommentsAsync(
            AdCommentReferenceRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Update comment statuses (hide or show comments)
        /// </summary>
        /// <param name="request">Comment status update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        //Task UpdateCommentStatusAsync(
        //    AdCommentStatusUpdateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Reply to a comment
        /// </summary>
        /// <param name="request">Comment reply request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Comment reply response containing created reply details</returns>
        //Task<AdCommentReplyResponse> ReplyToCommentAsync(
        //    AdCommentReplyRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a comment
        /// </summary>
        /// <param name="request">Comment delete request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        //Task DeleteCommentAsync(
        //    AdCommentDeleteRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a comment export task
        /// </summary>
        /// <param name="request">Comment export task request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Export task response containing task ID</returns>
        //Task<AdCommentExportTaskResponse> CreateCommentExportTaskAsync(
        //    AdCommentExportTaskRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Check the status of a comment export task
        /// </summary>
        /// <param name="request">Export task status request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Export task status response</returns>
        //Task<AdCommentExportTaskStatusResponse> GetCommentExportTaskStatusAsync(
        //    AdCommentExportTaskStatusRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Download exported comments
        /// </summary>
        /// <param name="request">Export task status request (contains advertiser_id and task_id)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Export download response containing CSV data</returns>
        Task<AdCommentExportDownloadResponse> DownloadCommentExportAsync(
            AdCommentExportTaskStatusRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create blocked words
        /// </summary>
        /// <param name="request">Blocked words create request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        //Task CreateBlockedWordsAsync(
        //    AdCommentBlockedWordsCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update a blocked word
        /// </summary>
        /// <param name="request">Blocked word update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        //Task UpdateBlockedWordAsync(
        //    AdCommentBlockedWordUpdateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Check the statuses of words (whether they are blocked)
        /// </summary>
        /// <param name="request">Blocked words check request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Blocked words check response</returns>
        // Task<AdCommentBlockedWordsCheckResponse> CheckBlockedWordsAsync(
        //     AdCommentBlockedWordsCheckRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the list of blocked words for an ad account
        /// </summary>
        /// <param name="request">Blocked words get request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Blocked words list response</returns>
        Task<AdCommentBlockedWordsListResponse> GetBlockedWordsAsync(
            AdCommentBlockedWordsGetRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete blocked words
        /// </summary>
        /// <param name="request">Blocked words delete request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        //Task DeleteBlockedWordsAsync(
        //    AdCommentBlockedWordsDeleteRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a blocked word export task
        /// </summary>
        /// <param name="request">Blocked word export task request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Export task response containing task ID</returns>
        //Task<AdCommentBlockedWordExportTaskResponse> CreateBlockedWordExportTaskAsync(
        //    AdCommentBlockedWordExportTaskRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Check the status of a blocked word export task
        /// </summary>
        /// <param name="request">Export task status request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Export task status response</returns>
        Task<AdCommentBlockedWordExportTaskStatusResponse> GetBlockedWordExportTaskStatusAsync(
            AdCommentBlockedWordExportTaskStatusRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Download exported blocked words
        /// </summary>
        /// <param name="request">Export task status request (contains advertiser_id and task_id)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Export download response containing CSV data</returns>
        Task<AdCommentBlockedWordExportDownloadResponse> DownloadBlockedWordExportAsync(
            AdCommentBlockedWordExportTaskStatusRequest request,
            CancellationToken cancellationToken = default);
    }
}
