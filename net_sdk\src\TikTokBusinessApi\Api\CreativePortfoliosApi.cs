/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Creative Portfolios operations
    /// </summary>
    public class CreativePortfoliosApi : ICreativePortfoliosApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<CreativePortfoliosApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the CreativePortfoliosApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public CreativePortfoliosApi(IApiClient apiClient, ILogger<CreativePortfoliosApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        // /// <summary>
        // /// Create a portfolio of creative assets
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing portfolio details</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing created portfolio ID</returns>
        // public async Task<Response<CreativePortfolioCreateResponse>> CreatePortfolioAsync(
        //     string accessToken,
        //     CreativePortfolioCreateBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogDebug("Creating creative portfolio for advertiser {AdvertiserId}", body.AdvertiserId);

        //     var queryParams = new Dictionary<string, string>();
        //     var headerParams = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<CreativePortfolioCreateResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativePortfoliosEndpoints.CreatePortfolio}",
        //         HttpMethod.Post,
        //         queryParams: queryParams,
        //         body: body,
        //         headerParams: headerParams,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get an existing creative portfolio by its ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="creativePortfolioId">ID of the creative portfolio to get</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing portfolio details</returns>
        public async Task<Response<CreativePortfolioGetResponse>> GetPortfolioByIdAsync(
            string accessToken,
            string advertiserId,
            string creativePortfolioId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrWhiteSpace(creativePortfolioId))
                throw new ArgumentException("Creative portfolio ID cannot be null or empty", nameof(creativePortfolioId));

            _logger?.LogDebug("Getting creative portfolio {PortfolioId} for advertiser {AdvertiserId}", 
                creativePortfolioId, advertiserId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["creative_portfolio_id"] = creativePortfolioId
            };

            var headerParams = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<CreativePortfolioGetResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativePortfoliosEndpoints.GetPortfolioById}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headerParams,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Retrieve portfolios created within an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="filtering">Filtering conditions</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of portfolios</returns>
        public async Task<Response<CreativePortfoliosListResponse>> GetPortfoliosAsync(
            string accessToken,
            string advertiserId,
            CreativePortfolioFiltering? filtering = null,
            int page = 1,
            int pageSize = 20,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (page < 1)
                throw new ArgumentException("Page must be greater than 0", nameof(page));
            if (pageSize < 1 || pageSize > 100)
                throw new ArgumentException("Page size must be between 1 and 100", nameof(pageSize));

            _logger?.LogDebug("Getting creative portfolios for advertiser {AdvertiserId}, page {Page}, size {PageSize}", 
                advertiserId, page, pageSize);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["page"] = page.ToString(),
                ["page_size"] = pageSize.ToString()
            };

            if (filtering != null)
            {
                queryParams["filtering"] = JsonSerializer.Serialize(filtering);
            }

            var headerParams = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<CreativePortfoliosListResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativePortfoliosEndpoints.GetPortfolios}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headerParams,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <summary>
        // /// Bulk delete creative portfolios
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing portfolio IDs to delete</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response indicating success or failure</returns>
        // public async Task<Response<object>> DeletePortfoliosAsync(
        //     string accessToken,
        //     CreativePortfolioDeleteBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));
        //     if (body.CreativePortfolioIds == null || body.CreativePortfolioIds.Count == 0)
        //         throw new ArgumentException("Creative portfolio IDs cannot be null or empty", nameof(body));
        //     if (body.CreativePortfolioIds.Count > 100)
        //         throw new ArgumentException("Cannot delete more than 100 portfolios at once", nameof(body));

        //     _logger?.LogDebug("Deleting {Count} creative portfolios for advertiser {AdvertiserId}", 
        //         body.CreativePortfolioIds.Count, body.AdvertiserId);

        //     var queryParams = new Dictionary<string, string>();
        //     var headerParams = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<object>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativePortfoliosEndpoints.DeletePortfolios}",
        //         HttpMethod.Post,
        //         queryParams: queryParams,
        //         body: body,
        //         headerParams: headerParams,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }
    }
}
