/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Creative model for ad creation
    /// </summary>
    public class AdCreateCreative
    {
        /// <summary>
        /// Ad name
        /// </summary>
        [JsonPropertyName("ad_name")]
        [Required]
        public string AdName { get; set; } = string.Empty;

        /// <summary>
        /// Identity type
        /// </summary>
        [JsonPropertyName("identity_type")]
        [Required]
        public string IdentityType { get; set; } = string.Empty;

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        [Required]
        public string IdentityId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// Product catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }

        /// <summary>
        /// Different dimensions to choose products
        /// </summary>
        [JsonPropertyName("product_specific_type")]
        public string? ProductSpecificType { get; set; }

        /// <summary>
        /// Product SPU IDs
        /// </summary>
        [JsonPropertyName("item_group_ids")]
        public List<string>? ItemGroupIds { get; set; }

        /// <summary>
        /// The ID of the product set
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string? ProductSetId { get; set; }

        /// <summary>
        /// IDs of the SKUs
        /// </summary>
        [JsonPropertyName("sku_ids")]
        public List<string>? SkuIds { get; set; }

        /// <summary>
        /// IDs of vehicles
        /// </summary>
        [JsonPropertyName("vehicle_ids")]
        public List<string>? VehicleIds { get; set; }

        /// <summary>
        /// The list of Showcase products that you want to use in your ad
        /// </summary>
        [JsonPropertyName("showcase_products")]
        public List<ShowcaseProduct>? ShowcaseProducts { get; set; }

        /// <summary>
        /// The ad format
        /// </summary>
        [JsonPropertyName("ad_format")]
        [Required]
        public string AdFormat { get; set; } = string.Empty;

        /// <summary>
        /// The video type that you use for Product Sales scenarios
        /// </summary>
        [JsonPropertyName("vertical_video_strategy")]
        public string? VerticalVideoStrategy { get; set; }

        /// <summary>
        /// Dynamic format
        /// </summary>
        [JsonPropertyName("dynamic_format")]
        public string? DynamicFormat { get; set; }

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// A list of image IDs
        /// </summary>
        [JsonPropertyName("image_ids")]
        public List<string>? ImageIds { get; set; }

        /// <summary>
        /// The index that specifies the additional images to be used in the VSA Carousel ad
        /// </summary>
        [JsonPropertyName("carousel_image_index")]
        public int? CarouselImageIndex { get; set; }

        /// <summary>
        /// Call-to-action for the end card
        /// </summary>
        [JsonPropertyName("end_card_cta")]
        public string? EndCardCta { get; set; }

        /// <summary>
        /// A list of product details to display in your Automotive Carousel Ad for Inventory
        /// </summary>
        [JsonPropertyName("product_display_field_list")]
        public List<string>? ProductDisplayFieldList { get; set; }

        /// <summary>
        /// The type of disclaimer to show in the Automotive Carousel Ads for Models
        /// </summary>
        [JsonPropertyName("auto_disclaimer_types")]
        public List<string>? AutoDisclaimerTypes { get; set; }

        /// <summary>
        /// The ID of the piece of music that is used in the TikTok Carousel Ad
        /// </summary>
        [JsonPropertyName("music_id")]
        public string? MusicId { get; set; }

        /// <summary>
        /// The ID of the TikTok post to be used as Spark Ad
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TiktokItemId { get; set; }

        /// <summary>
        /// Whether to disable the promotional use of the music in the Spark Ad
        /// </summary>
        [JsonPropertyName("promotional_music_disabled")]
        public bool? PromotionalMusicDisabled { get; set; }

        /// <summary>
        /// Whether to enable dueting for the Spark Ad
        /// </summary>
        [JsonPropertyName("item_duet_status")]
        public string? ItemDuetStatus { get; set; }

        /// <summary>
        /// Whether to enable stitching for the Spark Ad
        /// </summary>
        [JsonPropertyName("item_stitch_status")]
        public string? ItemStitchStatus { get; set; }

        /// <summary>
        /// Indicates whether the ad is a Spark Ads dark post
        /// </summary>
        [JsonPropertyName("dark_post_status")]
        public string? DarkPostStatus { get; set; }

        /// <summary>
        /// Default is false. If branded_content_disabled is true, you cannot modify dark_post_status
        /// </summary>
        [JsonPropertyName("branded_content_disabled")]
        public bool? BrandedContentDisabled { get; set; }

        /// <summary>
        /// Product video package ID
        /// </summary>
        [JsonPropertyName("shopping_ads_video_package_id")]
        public string? ShoppingAdsVideoPackageId { get; set; }

        /// <summary>
        /// The ad text
        /// </summary>
        [JsonPropertyName("ad_text")]
        public string? AdText { get; set; }

        /// <summary>
        /// Call-to-action text
        /// </summary>
        [JsonPropertyName("call_to_action")]
        public string? CallToAction { get; set; }

        /// <summary>
        /// The ID of the CTA portfolio that you want to use in your ads
        /// </summary>
        [JsonPropertyName("call_to_action_id")]
        public string? CallToActionId { get; set; }

        /// <summary>
        /// Creative portfolio ID
        /// </summary>
        [JsonPropertyName("card_id")]
        public string? CardId { get; set; }

        /// <summary>
        /// Landing page URL
        /// </summary>
        [JsonPropertyName("landing_page_url")]
        public string? LandingPageUrl { get; set; }

        /// <summary>
        /// A list of URL parameters
        /// </summary>
        [JsonPropertyName("utm_params")]
        public List<UtmParam>? UtmParams { get; set; }

        /// <summary>
        /// Page ID
        /// </summary>
        [JsonPropertyName("page_id")]
        public long? PageId { get; set; }

        /// <summary>
        /// The Custom Product Page (CPP) URL
        /// </summary>
        [JsonPropertyName("cpp_url")]
        public string? CppUrl { get; set; }

        /// <summary>
        /// The category of the TikTok pages that you want to promote
        /// </summary>
        [JsonPropertyName("tiktok_page_category")]
        public string? TiktokPageCategory { get; set; }

        /// <summary>
        /// The region code for the phone number that the audience can click on the ad to call
        /// </summary>
        [JsonPropertyName("phone_region_code")]
        public string? PhoneRegionCode { get; set; }

        /// <summary>
        /// The phone number that the audience can click on the ad to call
        /// </summary>
        [JsonPropertyName("phone_region_calling_code")]
        public string? PhoneRegionCallingCode { get; set; }

        /// <summary>
        /// The phone number that the audience can click on the ad to call
        /// </summary>
        [JsonPropertyName("phone_number")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// The specific location where you want your audience to go if they have your app installed
        /// </summary>
        [JsonPropertyName("deeplink")]
        public string? Deeplink { get; set; }

        /// <summary>
        /// The deeplink type
        /// </summary>
        [JsonPropertyName("deeplink_type")]
        public string? DeeplinkType { get; set; }

        /// <summary>
        /// The format type of the deeplink
        /// </summary>
        [JsonPropertyName("deeplink_format_type")]
        public string? DeeplinkFormatType { get; set; }

        /// <summary>
        /// The source of the deeplink to be used in the Shopping Ads
        /// </summary>
        [JsonPropertyName("shopping_ads_deeplink_type")]
        public string? ShoppingAdsDeeplinkType { get; set; }

        /// <summary>
        /// A list of deeplink URL parameters
        /// </summary>
        [JsonPropertyName("deeplink_utm_params")]
        public List<UtmParam>? DeeplinkUtmParams { get; set; }

        /// <summary>
        /// In the Shopping Ads retargeting scenario, the fallback website type when the deeplink fails to be triggered
        /// </summary>
        [JsonPropertyName("shopping_ads_fallback_type")]
        public string? ShoppingAdsFallbackType { get; set; }

        /// <summary>
        /// Fallback Type. If the audience do not have the app installed
        /// </summary>
        [JsonPropertyName("fallback_type")]
        public string? FallbackType { get; set; }

        /// <summary>
        /// Dynamic destination strategy
        /// </summary>
        [JsonPropertyName("dynamic_destination")]
        public string? DynamicDestination { get; set; }

        /// <summary>
        /// The ID of the automatic message to use in a TikTok Direct Messaging Ad
        /// </summary>
        [JsonPropertyName("auto_message_id")]
        public string? AutoMessageId { get; set; }

        /// <summary>
        /// Whether to turn on the AIGC self-disclosure toggle
        /// </summary>
        [JsonPropertyName("aigc_disclosure_type")]
        public string? AigcDisclosureType { get; set; }

        /// <summary>
        /// Type of disclaimer that you want to add to the ad
        /// </summary>
        [JsonPropertyName("disclaimer_type")]
        public string? DisclaimerType { get; set; }

        /// <summary>
        /// The text-only disclaimer that you want to add to the ad
        /// </summary>
        [JsonPropertyName("disclaimer_text")]
        public DisclaimerText? DisclaimerTextObj { get; set; }

        /// <summary>
        /// The clickable disclaimer or clickable disclaimers that you want to add to the ad
        /// </summary>
        [JsonPropertyName("disclaimer_clickable_texts")]
        public List<DisclaimerClickableText>? DisclaimerClickableTexts { get; set; }

        /// <summary>
        /// The pixel ID that you'd like to track
        /// </summary>
        [JsonPropertyName("tracking_pixel_id")]
        public long? TrackingPixelId { get; set; }

        /// <summary>
        /// The ID of the application that you want to track
        /// </summary>
        [JsonPropertyName("tracking_app_id")]
        public string? TrackingAppId { get; set; }

        /// <summary>
        /// A list of Offline Event set IDs that you want to track
        /// </summary>
        [JsonPropertyName("tracking_offline_event_set_ids")]
        public List<string>? TrackingOfflineEventSetIds { get; set; }

        /// <summary>
        /// The ID of the message event set that you want to measure in the Instant Messaging Ad
        /// </summary>
        [JsonPropertyName("tracking_message_event_set_id")]
        public string? TrackingMessageEventSetId { get; set; }

        /// <summary>
        /// Post-bid third-party viewability measurement partner
        /// </summary>
        [JsonPropertyName("viewability_postbid_partner")]
        public string? ViewabilityPostbidPartner { get; set; }

        /// <summary>
        /// The wrapped VAST URL used by the post-bid third-party partner to measure viewability
        /// </summary>
        [JsonPropertyName("viewability_vast_url")]
        public string? ViewabilityVastUrl { get; set; }

        /// <summary>
        /// Post-bid third-party brand safety measurement partner
        /// </summary>
        [JsonPropertyName("brand_safety_postbid_partner")]
        public string? BrandSafetyPostbidPartner { get; set; }

        /// <summary>
        /// The wrapped VAST URL used by the post-bid third-party partner to measure brand safety
        /// </summary>
        [JsonPropertyName("brand_safety_vast_url")]
        public string? BrandSafetyVastUrl { get; set; }

        /// <summary>
        /// Default Impression Tracking URL
        /// </summary>
        [JsonPropertyName("impression_tracking_url")]
        public string? ImpressionTrackingUrl { get; set; }

        /// <summary>
        /// Click Tracking URL
        /// </summary>
        [JsonPropertyName("click_tracking_url")]
        public string? ClickTrackingUrl { get; set; }

        /// <summary>
        /// The monitoring URL for video view
        /// </summary>
        [JsonPropertyName("video_view_tracking_url")]
        public string? VideoViewTrackingUrl { get; set; }

        /// <summary>
        /// Playable material url
        /// </summary>
        [JsonPropertyName("playable_url")]
        public string? PlayableUrl { get; set; }

        /// <summary>
        /// The status of the ad when created
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string? OperationStatus { get; set; }

        /// <summary>
        /// Creative type for Live Shopping Ads, Product Shopping Ads, App Pre-Registration, Automotive Ads for Inventory, or Automotive Ads for Models scenarios
        /// </summary>
        [JsonPropertyName("creative_type")]
        public string? CreativeType { get; set; }

        /// <summary>
        /// App name that is displayed in the ad
        /// </summary>
        [JsonPropertyName("app_name")]
        public string? AppName { get; set; }

        /// <summary>
        /// The display name of landing page or pure exposure ad
        /// </summary>
        [JsonPropertyName("display_name")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// ID of the avatar image
        /// </summary>
        [JsonPropertyName("avatar_icon_web_uri")]
        public string? AvatarIconWebUri { get; set; }

        /// <summary>
        /// Whether you grant displaying some of your ads in our TikTok For Business Creative Center
        /// </summary>
        [JsonPropertyName("creative_authorized")]
        public bool? CreativeAuthorized { get; set; }

        /// <summary>
        /// Schedule ID for Reach & Frequency ads
        /// </summary>
        [JsonPropertyName("schedule_id")]
        public string? ScheduleId { get; set; }
    }

    /// <summary>
    /// Creative model for ad updates
    /// </summary>
    public class AdUpdateCreative
    {
        /// <summary>
        /// Ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        [Required]
        public string AdId { get; set; } = string.Empty;

        /// <summary>
        /// Ad name
        /// </summary>
        [JsonPropertyName("ad_name")]
        public string? AdName { get; set; }

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Identity type
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// Different dimensions to choose products
        /// </summary>
        [JsonPropertyName("product_specific_type")]
        public string? ProductSpecificType { get; set; }

        /// <summary>
        /// Product SPU IDs
        /// </summary>
        [JsonPropertyName("item_group_ids")]
        public List<string>? ItemGroupIds { get; set; }

        /// <summary>
        /// The ID of the product set
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string? ProductSetId { get; set; }

        /// <summary>
        /// IDs of the SKUs
        /// </summary>
        [JsonPropertyName("sku_ids")]
        public List<string>? SkuIds { get; set; }

        /// <summary>
        /// The ad format
        /// </summary>
        [JsonPropertyName("ad_format")]
        public string? AdFormat { get; set; }

        /// <summary>
        /// The video type that you use for Product Sales scenarios
        /// </summary>
        [JsonPropertyName("vertical_video_strategy")]
        public string? VerticalVideoStrategy { get; set; }

        /// <summary>
        /// Dynamic format
        /// </summary>
        [JsonPropertyName("dynamic_format")]
        public string? DynamicFormat { get; set; }

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// A list of image IDs
        /// </summary>
        [JsonPropertyName("image_ids")]
        public List<string>? ImageIds { get; set; }

        /// <summary>
        /// The index that specifies the additional images to be used in the VSA Carousel ad
        /// </summary>
        [JsonPropertyName("carousel_image_index")]
        public int? CarouselImageIndex { get; set; }

        /// <summary>
        /// Call-to-action for the end card
        /// </summary>
        [JsonPropertyName("end_card_cta")]
        public string? EndCardCta { get; set; }

        /// <summary>
        /// The ID of the piece of music that is used in the TikTok Carousel Ad
        /// </summary>
        [JsonPropertyName("music_id")]
        public string? MusicId { get; set; }

        /// <summary>
        /// The ID of the TikTok post to be used as Spark Ad
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TiktokItemId { get; set; }

        /// <summary>
        /// Whether to disable the promotional use of the music in the Spark Ad
        /// </summary>
        [JsonPropertyName("promotional_music_disabled")]
        public bool? PromotionalMusicDisabled { get; set; }

        /// <summary>
        /// Whether to enable dueting for the Spark Ad
        /// </summary>
        [JsonPropertyName("item_duet_status")]
        public string? ItemDuetStatus { get; set; }

        /// <summary>
        /// Whether to enable stitching for the Spark Ad
        /// </summary>
        [JsonPropertyName("item_stitch_status")]
        public string? ItemStitchStatus { get; set; }

        /// <summary>
        /// Indicates whether the ad is a Spark Ads dark post
        /// </summary>
        [JsonPropertyName("dark_post_status")]
        public string? DarkPostStatus { get; set; }

        /// <summary>
        /// Product video package ID
        /// </summary>
        [JsonPropertyName("shopping_ads_video_package_id")]
        public string? ShoppingAdsVideoPackageId { get; set; }

        /// <summary>
        /// The ad text
        /// </summary>
        [JsonPropertyName("ad_text")]
        public string? AdText { get; set; }

        /// <summary>
        /// Call-to-action text
        /// </summary>
        [JsonPropertyName("call_to_action")]
        public string? CallToAction { get; set; }

        /// <summary>
        /// The ID of the CTA portfolio that you want to use in your ads
        /// </summary>
        [JsonPropertyName("call_to_action_id")]
        public string? CallToActionId { get; set; }

        /// <summary>
        /// Creative portfolio ID
        /// </summary>
        [JsonPropertyName("card_id")]
        public string? CardId { get; set; }

        /// <summary>
        /// Landing page URL
        /// </summary>
        [JsonPropertyName("landing_page_url")]
        public string? LandingPageUrl { get; set; }

        /// <summary>
        /// A list of URL parameters
        /// </summary>
        [JsonPropertyName("utm_params")]
        public List<UtmParam>? UtmParams { get; set; }

        /// <summary>
        /// Page ID
        /// </summary>
        [JsonPropertyName("page_id")]
        public long? PageId { get; set; }

        /// <summary>
        /// The Custom Product Page (CPP) URL
        /// </summary>
        [JsonPropertyName("cpp_url")]
        public string? CppUrl { get; set; }

        /// <summary>
        /// The category of the TikTok pages that you want to promote
        /// </summary>
        [JsonPropertyName("tiktok_page_category")]
        public string? TiktokPageCategory { get; set; }

        /// <summary>
        /// The region code for the phone number that the audience can click on the ad to call
        /// </summary>
        [JsonPropertyName("phone_region_code")]
        public string? PhoneRegionCode { get; set; }

        /// <summary>
        /// The phone number that the audience can click on the ad to call
        /// </summary>
        [JsonPropertyName("phone_region_calling_code")]
        public string? PhoneRegionCallingCode { get; set; }

        /// <summary>
        /// The phone number that the audience can click on the ad to call
        /// </summary>
        [JsonPropertyName("phone_number")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// The specific location where you want your audience to go if they have your app installed
        /// </summary>
        [JsonPropertyName("deeplink")]
        public string? Deeplink { get; set; }

        /// <summary>
        /// The deeplink type
        /// </summary>
        [JsonPropertyName("deeplink_type")]
        public string? DeeplinkType { get; set; }

        /// <summary>
        /// The format type of the deeplink
        /// </summary>
        [JsonPropertyName("deeplink_format_type")]
        public string? DeeplinkFormatType { get; set; }

        /// <summary>
        /// The source of the deeplink to be used in the Shopping Ads
        /// </summary>
        [JsonPropertyName("shopping_ads_deeplink_type")]
        public string? ShoppingAdsDeeplinkType { get; set; }

        /// <summary>
        /// A list of deeplink URL parameters
        /// </summary>
        [JsonPropertyName("deeplink_utm_params")]
        public List<UtmParam>? DeeplinkUtmParams { get; set; }

        /// <summary>
        /// In the Shopping Ads retargeting scenario, the fallback website type when the deeplink fails to be triggered
        /// </summary>
        [JsonPropertyName("shopping_ads_fallback_type")]
        public string? ShoppingAdsFallbackType { get; set; }

        /// <summary>
        /// Fallback Type. If the audience do not have the app installed
        /// </summary>
        [JsonPropertyName("fallback_type")]
        public string? FallbackType { get; set; }

        /// <summary>
        /// Dynamic destination strategy
        /// </summary>
        [JsonPropertyName("dynamic_destination")]
        public string? DynamicDestination { get; set; }

        /// <summary>
        /// The ID of the automatic message to use in a TikTok Direct Messaging Ad
        /// </summary>
        [JsonPropertyName("auto_message_id")]
        public string? AutoMessageId { get; set; }

        /// <summary>
        /// Whether to turn on the AIGC self-disclosure toggle
        /// </summary>
        [JsonPropertyName("aigc_disclosure_type")]
        public string? AigcDisclosureType { get; set; }

        /// <summary>
        /// Type of disclaimer that you want to add to the ad
        /// </summary>
        [JsonPropertyName("disclaimer_type")]
        public string? DisclaimerType { get; set; }

        /// <summary>
        /// The text-only disclaimer that you want to add to the ad
        /// </summary>
        [JsonPropertyName("disclaimer_text")]
        public DisclaimerText? DisclaimerTextObj { get; set; }

        /// <summary>
        /// The clickable disclaimer or clickable disclaimers that you want to add to the ad
        /// </summary>
        [JsonPropertyName("disclaimer_clickable_texts")]
        public List<DisclaimerClickableText>? DisclaimerClickableTexts { get; set; }

        /// <summary>
        /// The pixel ID that you'd like to track
        /// </summary>
        [JsonPropertyName("tracking_pixel_id")]
        public long? TrackingPixelId { get; set; }

        /// <summary>
        /// The ID of the application that you want to track
        /// </summary>
        [JsonPropertyName("tracking_app_id")]
        public string? TrackingAppId { get; set; }

        /// <summary>
        /// A list of Offline Event set IDs that you want to track
        /// </summary>
        [JsonPropertyName("tracking_offline_event_set_ids")]
        public List<string>? TrackingOfflineEventSetIds { get; set; }

        /// <summary>
        /// Whether Moat Viewability Verification is enabled for the ad
        /// </summary>
        [JsonPropertyName("vast_moat_enabled")]
        public bool? VastMoatEnabled { get; set; }

        /// <summary>
        /// Post-bid third-party viewability measurement partner
        /// </summary>
        [JsonPropertyName("viewability_postbid_partner")]
        public string? ViewabilityPostbidPartner { get; set; }

        /// <summary>
        /// The wrapped VAST URL used by the post-bid third-party partner to measure viewability
        /// </summary>
        [JsonPropertyName("viewability_vast_url")]
        public string? ViewabilityVastUrl { get; set; }

        /// <summary>
        /// Post-bid third-party brand safety measurement partner
        /// </summary>
        [JsonPropertyName("brand_safety_postbid_partner")]
        public string? BrandSafetyPostbidPartner { get; set; }

        /// <summary>
        /// The wrapped VAST URL used by the post-bid third-party partner to measure brand safety
        /// </summary>
        [JsonPropertyName("brand_safety_vast_url")]
        public string? BrandSafetyVastUrl { get; set; }

        /// <summary>
        /// Default Impression Tracking URL
        /// </summary>
        [JsonPropertyName("impression_tracking_url")]
        public string? ImpressionTrackingUrl { get; set; }

        /// <summary>
        /// Click Tracking URL
        /// </summary>
        [JsonPropertyName("click_tracking_url")]
        public string? ClickTrackingUrl { get; set; }

        /// <summary>
        /// The monitoring URL for video view
        /// </summary>
        [JsonPropertyName("video_view_tracking_url")]
        public string? VideoViewTrackingUrl { get; set; }

        /// <summary>
        /// Playable material url
        /// </summary>
        [JsonPropertyName("playable_url")]
        public string? PlayableUrl { get; set; }

        /// <summary>
        /// Creative type for Live Shopping Ads, Product Shopping Ads, App Pre-Registration, Automotive Ads for Inventory, or Automotive Ads for Models scenarios
        /// </summary>
        [JsonPropertyName("creative_type")]
        public string? CreativeType { get; set; }

        /// <summary>
        /// App name that is displayed in the ad
        /// </summary>
        [JsonPropertyName("app_name")]
        public string? AppName { get; set; }

        /// <summary>
        /// The display name of landing page or pure exposure ad
        /// </summary>
        [JsonPropertyName("display_name")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// ID of the avatar image
        /// </summary>
        [JsonPropertyName("avatar_icon_web_uri")]
        public string? AvatarIconWebUri { get; set; }

        /// <summary>
        /// Whether you grant displaying some of your ads in our TikTok For Business Creative Center
        /// </summary>
        [JsonPropertyName("creative_authorized")]
        public bool? CreativeAuthorized { get; set; }
    }
}
