using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using TikTok.Application.Contracts.LatestNotifications;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using Volo.Abp.ObjectMapping;

namespace TikTok.Activities.SendMessages
{
    [Activity(Category = "Thông báo", DisplayName = "Cập nhật thông báo mới nhất", Outcomes = new[] { OutcomeNames.Done })]
    public class UpdateLatestNotificationsActivity : UnitOfWorkActivity
    {
        private readonly ILatestNotificationRepository _latestNotificationRepository;
        private readonly ILogger<UpdateLatestNotificationsActivity> _logger;
        private readonly IObjectMapper _objectMapper;

        public UpdateLatestNotificationsActivity(
            ILatestNotificationRepository latestNotificationRepository,
            ILogger<UpdateLatestNotificationsActivity> logger,
            IObjectMapper objectMapper)
        {
            _latestNotificationRepository = latestNotificationRepository;
            _logger = logger;
            _objectMapper = objectMapper;
        }

        [ActivityInput(Hint = "Loại thông báo", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public NotificationType NotificationType { get; set; }

        [ActivityInput(Hint = "Danh sách thông báo cần cập nhật", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public List<LatestNotificationDto> Notifications { get; set; } = new List<LatestNotificationDto>();

        [ActivityOutput(Hint = "Danh sách thông báo đã cập nhật")]
        public List<LatestNotificationDto> Output { get; set; } = new List<LatestNotificationDto>();

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            if (Notifications == null || Notifications.Count == 0)
            {
                _logger?.LogWarning("UpdateLatestNotificationsActivity: Notifications is null or empty");
                Output = new List<LatestNotificationDto>();
                return Done();
            }

            // Bước 1: Lấy danh sách thông báo hiện có theo Type và ObjectIds
            var objectIds = Notifications.Select(x => x.ObjectId).ToList();
            var existingNotifications = await _latestNotificationRepository.GetByTypeAndObjectIdsAsync(NotificationType, objectIds);

            var listInsert = new List<LatestNotificationEntity>();
            var listUpdate = new List<LatestNotificationEntity>();

            foreach (var notificationDto in Notifications)
            {
                var existingNotification = existingNotifications.FirstOrDefault(x => x.ObjectId == notificationDto.ObjectId && x.Rule == notificationDto.Rule && x.Type == notificationDto.Type);
                if (existingNotification == null)
                {
                    // Tạo mới thông báo
                    var newNotification = new LatestNotificationEntity(
                        notificationDto.ObjectId,
                        NotificationType,
                        notificationDto.Payload,
                        notificationDto.Rule
                    );
                    listInsert.Add(newNotification);
                }
                else
                {
                    // Cập nhật thông báo hiện có
                    existingNotification.UpdateNotificationInfo(notificationDto.Payload);
                    existingNotification.UpdateLastNotifiedTime();
                    listUpdate.Add(existingNotification);
                }
            }

            if (listInsert.Count > 0)
            {
                await _latestNotificationRepository.InsertManyAsync(listInsert);
                Output.AddRange(_objectMapper.Map<List<LatestNotificationEntity>, List<LatestNotificationDto>>(listInsert));
            }
            if (listUpdate.Count > 0)
            {
                await _latestNotificationRepository.UpdateManyAsync(listUpdate);
                Output.AddRange(_objectMapper.Map<List<LatestNotificationEntity>, List<LatestNotificationDto>>(listUpdate));
            }

            return Done();
        }
    }
}