﻿using System;
using System.Collections.Generic;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.AdGroups
{
    /// <summary>
    /// DTO cho nhóm quảng cáo
    /// </summary>
    public class AdGroupDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID chiến dịch
        /// </summary>
        public string CampaignId { get; set; }

        /// <summary>
        /// Tên chiến dịch
        /// </summary>
        public string CampaignName { get; set; }

        /// <summary>
        /// Nguồn gốc của chiến dịch mà nhóm quảng cáo thuộc về
        /// </summary>
        public string CampaignSystemOrigin { get; set; }

        /// <summary>
        /// Có phải nhóm quảng cáo trong chiến dịch tự động hay không
        /// </summary>
        public bool IsSmartPerformanceCampaign { get; set; }

        /// <summary>
        /// ID nhóm quảng cáo
        /// </summary>
        public string AdgroupId { get; set; }

        /// <summary>
        /// Tên nhóm quảng cáo
        /// </summary>
        public string AdgroupName { get; set; }

        /// <summary>
        /// Thời gian tạo nhóm quảng cáo
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// Thời gian sửa đổi nhóm quảng cáo
        /// </summary>
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// Loại quảng cáo mua sắm
        /// </summary>
        public string ShoppingAdsType { get; set; }

        /// <summary>
        /// ID danh tính
        /// </summary>
        public string IdentityId { get; set; }

        /// <summary>
        /// Loại danh tính
        /// </summary>
        public string IdentityType { get; set; }

        /// <summary>
        /// ID BC được ủy quyền cho danh tính
        /// </summary>
        public string IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// Nguồn sản phẩm để lấy sản phẩm quảng bá
        /// </summary>
        public string ProductSource { get; set; }

        /// <summary>
        /// ID catalog
        /// </summary>
        public string CatalogId { get; set; }

        /// <summary>
        /// ID BC mà catalog thuộc về
        /// </summary>
        public string CatalogAuthorizedBcId { get; set; }

        /// <summary>
        /// ID của TikTok Shop
        /// </summary>
        public string StoreId { get; set; }

        /// <summary>
        /// ID BC được ủy quyền truy cập store
        /// </summary>
        public string StoreAuthorizedBcId { get; set; }

        /// <summary>
        /// Loại khuyến mãi (Vị trí tối ưu hóa)
        /// </summary>
        public string PromotionType { get; set; }

        /// <summary>
        /// Loại mục tiêu khuyến mãi cho mục tiêu Lead Generation
        /// </summary>
        public string PromotionTargetType { get; set; }

        /// <summary>
        /// Loại ứng dụng nhắn tin tức thời hoặc URL tùy chỉnh
        /// </summary>
        public string MessagingAppType { get; set; }

        /// <summary>
        /// ID tài khoản ứng dụng nhắn tin tức thời
        /// </summary>
        public string MessagingAppAccountId { get; set; }

        /// <summary>
        /// Mã vùng cho số điện thoại WhatsApp hoặc Zalo
        /// </summary>
        public string PhoneRegionCode { get; set; }

        /// <summary>
        /// Mã gọi vùng cho số điện thoại WhatsApp hoặc Zalo
        /// </summary>
        public string PhoneRegionCallingCode { get; set; }

        /// <summary>
        /// Số điện thoại WhatsApp hoặc Zalo
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// Loại TikTok Instant Page trong nhóm quảng cáo
        /// </summary>
        public string PromotionWebsiteType { get; set; }

        /// <summary>
        /// ID ứng dụng được quảng bá
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// Loại ứng dụng được quảng bá
        /// </summary>
        public string AppType { get; set; }

        /// <summary>
        /// Link tải ứng dụng
        /// </summary>
        public string AppDownloadUrl { get; set; }

        /// <summary>
        /// ID Pixel (chỉ áp dụng cho landing page)
        /// </summary>
        public string PixelId { get; set; }

        /// <summary>
        /// Sự kiện chuyển đổi cho nhóm quảng cáo
        /// </summary>
        public string OptimizationEvent { get; set; }

        /// <summary>
        /// Trạng thái tối ưu hóa deep funnel
        /// </summary>
        public string DeepFunnelOptimizationStatus { get; set; }

        /// <summary>
        /// Loại nguồn sự kiện
        /// </summary>
        public string DeepFunnelEventSource { get; set; }

        /// <summary>
        /// ID nguồn sự kiện
        /// </summary>
        public string DeepFunnelEventSourceId { get; set; }

        /// <summary>
        /// Sự kiện tối ưu hóa deep funnel
        /// </summary>
        public string DeepFunnelOptimizationEvent { get; set; }

        /// <summary>
        /// Chiến lược vị trí quyết định nơi hiển thị quảng cáo
        /// </summary>
        public string PlacementType { get; set; }

        /// <summary>
        /// Các ứng dụng nơi bạn muốn phân phối quảng cáo
        /// </summary>
        public List<string> Placements { get; set; }

        /// <summary>
        /// Các vị trí phụ trong TikTok cho quảng cáo
        /// </summary>
        public List<string> TiktokSubplacements { get; set; }

        /// <summary>
        /// Có bao gồm quảng cáo trong Search Ads hay không
        /// </summary>
        public bool SearchResultEnabled { get; set; }

        /// <summary>
        /// Có bật từ khóa tự động hay không
        /// </summary>
        public bool AutomatedKeywordsEnabled { get; set; }

        /// <summary>
        /// Có cho phép bình luận trên quảng cáo TikTok hay không
        /// </summary>
        public bool CommentDisabled { get; set; }

        /// <summary>
        /// Người dùng có thể tải video quảng cáo trên TikTok hay không
        /// </summary>
        public bool VideoDownloadDisabled { get; set; }

        /// <summary>
        /// Có tắt chia sẻ lên nền tảng bên thứ ba hay không
        /// </summary>
        public bool ShareDisabled { get; set; }

        /// <summary>
        /// Danh sách ID ứng dụng Pangle bị chặn
        /// </summary>
        public List<string> BlockedPangleAppIds { get; set; }

        /// <summary>
        /// Loại đối tượng retargeting ứng dụng
        /// </summary>
        public string AudienceType { get; set; }

        /// <summary>
        /// Có bật tự động targeting hay không (deprecated)
        /// </summary>
        public bool AutoTargetingEnabled { get; set; }

        /// <summary>
        /// Loại retargeting của quảng cáo mua sắm
        /// </summary>
        public string ShoppingAdsRetargetingType { get; set; }

        /// <summary>
        /// Thời gian hiệu lực cho hành động đối tượng chỉ định
        /// </summary>
        public int? ShoppingAdsRetargetingActionsDays { get; set; }

        /// <summary>
        /// Mối quan hệ logic giữa đối tượng retargeting VSA và đối tượng tùy chỉnh
        /// </summary>
        public string ShoppingAdsRetargetingCustomAudienceRelation { get; set; }

        /// <summary>
        /// ID các vị trí được nhắm mục tiêu
        /// </summary>
        public List<string> LocationIds { get; set; }

        /// <summary>
        /// ID mã ZIP hoặc mã bưu điện của các vị trí được nhắm mục tiêu
        /// </summary>
        public List<string> ZipcodeIds { get; set; }

        /// <summary>
        /// Mã ngôn ngữ bạn muốn nhắm mục tiêu
        /// </summary>
        public List<string> Languages { get; set; }

        /// <summary>
        /// Giới tính bạn muốn nhắm mục tiêu
        /// </summary>
        public string Gender { get; set; }

        /// <summary>
        /// Nhóm tuổi bạn muốn nhắm mục tiêu
        /// </summary>
        public List<string> AgeGroups { get; set; }

        /// <summary>
        /// Sức mua bạn muốn nhắm mục tiêu
        /// </summary>
        public string SpendingPower { get; set; }

        /// <summary>
        /// Thu nhập hộ gia đình bạn muốn nhắm mục tiêu
        /// </summary>
        public List<string> HouseholdIncome { get; set; }

        /// <summary>
        /// Danh sách ID đối tượng
        /// </summary>
        public List<string> AudienceIds { get; set; }

        /// <summary>
        /// Có bật Smart audience hay không
        /// </summary>
        public bool SmartAudienceEnabled { get; set; }

        /// <summary>
        /// Danh sách ID đối tượng loại trừ
        /// </summary>
        public List<string> ExcludedAudienceIds { get; set; }

        /// <summary>
        /// ID từ khóa sở thích chung để nhắm mục tiêu đối tượng
        /// </summary>
        public List<string> InterestCategoryIds { get; set; }

        /// <summary>
        /// ID từ khóa sở thích bổ sung để nhắm mục tiêu đối tượng
        /// </summary>
        public List<string> InterestKeywordIds { get; set; }

        /// <summary>
        /// ID danh mục ý định mua hàng
        /// </summary>
        public List<string> PurchaseIntentionKeywordIds { get; set; }

        /// <summary>
        /// Có bật Smart interests & behaviors hay không
        /// </summary>
        public bool SmartInterestBehaviorEnabled { get; set; }

        /// <summary>
        /// ID đối tượng Pangle muốn bao gồm
        /// </summary>
        public List<string> IncludedPangleAudiencePackageIds { get; set; }

        /// <summary>
        /// ID đối tượng Pangle muốn loại trừ
        /// </summary>
        public List<string> ExcludedPangleAudiencePackageIds { get; set; }

        /// <summary>
        /// Hệ điều hành thiết bị bạn muốn nhắm mục tiêu
        /// </summary>
        public List<string> OperatingSystems { get; set; }

        /// <summary>
        /// Phiên bản Android tối thiểu
        /// </summary>
        public string MinAndroidVersion { get; set; }

        /// <summary>
        /// Thiết bị iOS bạn muốn nhắm mục tiêu
        /// </summary>
        public string Ios14Targeting { get; set; }

        /// <summary>
        /// Phiên bản iOS tối thiểu của đối tượng
        /// </summary>
        public string MinIosVersion { get; set; }

        /// <summary>
        /// Liệu chiến dịch có được tính vào hạn ngạch chiến dịch chuyên biệt iOS 14 hay không
        /// </summary>
        public string Ios14QuotaType { get; set; }

        /// <summary>
        /// Danh sách ID mô hình thiết bị
        /// </summary>
        public List<string> DeviceModelIds { get; set; }

        /// <summary>
        /// Loại mạng bạn muốn nhắm mục tiêu
        /// </summary>
        public List<string> NetworkTypes { get; set; }

        /// <summary>
        /// Nhà mạng bạn muốn nhắm mục tiêu
        /// </summary>
        public List<string> CarrierIds { get; set; }

        /// <summary>
        /// ID nhà cung cấp dịch vụ Internet được nhắm mục tiêu
        /// </summary>
        public List<string> IspIds { get; set; }

        /// <summary>
        /// Phạm vi giá thiết bị nhắm mục tiêu
        /// </summary>
        public List<int> DevicePriceRanges { get; set; }

        /// <summary>
        /// ID Saved Audience
        /// </summary>
        public string SavedAudienceId { get; set; }

        /// <summary>
        /// ID thẻ ngữ cảnh
        /// </summary>
        public List<string> ContextualTagIds { get; set; }

        /// <summary>
        /// Loại an toàn thương hiệu
        /// </summary>
        public string BrandSafetyType { get; set; }

        /// <summary>
        /// Đối tác an toàn thương hiệu
        /// </summary>
        public string BrandSafetyPartner { get; set; }

        /// <summary>
        /// Có bật lọc kho dữ liệu hay không
        /// </summary>
        public bool InventoryFilterEnabled { get; set; }

        /// <summary>
        /// ID danh mục loại trừ nội dung
        /// </summary>
        public List<string> CategoryExclusionIds { get; set; }

        /// <summary>
        /// ID danh mục độ nhạy cảm dọc
        /// </summary>
        public string VerticalSensitivityId { get; set; }

        /// <summary>
        /// Chế độ ngân sách
        /// </summary>
        public string BudgetMode { get; set; }

        /// <summary>
        /// Ngân sách nhóm quảng cáo
        /// </summary>
        public decimal Budget { get; set; }

        /// <summary>
        /// Ngân sách quảng cáo được lên lịch cho ngày hôm sau
        /// </summary>
        public decimal? ScheduledBudget { get; set; }

        /// <summary>
        /// Loại lịch trình
        /// </summary>
        public string ScheduleType { get; set; }

        /// <summary>
        /// Thời gian bắt đầu phân phối quảng cáo
        /// </summary>
        public DateTime? ScheduleStartTime { get; set; }

        /// <summary>
        /// Thời gian kết thúc phân phối quảng cáo
        /// </summary>
        public DateTime? ScheduleEndTime { get; set; }

        /// <summary>
        /// Loại phân phối cho ngày
        /// </summary>
        public string Dayparting { get; set; }

        /// <summary>
        /// Mục tiêu tối ưu hóa
        /// </summary>
        public string OptimizationGoal { get; set; }

        /// <summary>
        /// Mục tiêu thứ cấp khi optimization_goal là INSTALL hoặc VALUE
        /// </summary>
        public string SecondaryOptimizationEvent { get; set; }

        /// <summary>
        /// ID bộ sự kiện tin nhắn để sử dụng trong Nhóm quảng cáo nhắn tin tức thời
        /// </summary>
        public string MessageEventSetId { get; set; }

        /// <summary>
        /// Tần suất
        /// </summary>
        public int? Frequency { get; set; }

        /// <summary>
        /// Lịch trình tần suất
        /// </summary>
        public int? FrequencySchedule { get; set; }

        /// <summary>
        /// Chiến lược đặt giá thầu
        /// </summary>
        public string BidType { get; set; }

        /// <summary>
        /// Giá thầu
        /// </summary>
        public decimal? BidPrice { get; set; }

        /// <summary>
        /// Chi phí mục tiêu cho mỗi chuyển đổi cho oCPM
        /// </summary>
        public decimal? ConversionBidPrice { get; set; }

        /// <summary>
        /// Chiến lược đặt giá thầu cho sự kiện trong ứng dụng
        /// </summary>
        public string DeepBidType { get; set; }

        /// <summary>
        /// Mục tiêu ROAS cho Tối ưu hóa giá trị
        /// </summary>
        public decimal? RoasBid { get; set; }

        /// <summary>
        /// Cửa sổ thời gian của chiến lược đặt giá thầu chỉ định cho VBO
        /// </summary>
        public string VboWindow { get; set; }

        /// <summary>
        /// Cách tính toán và đo lường Cost per View
        /// </summary>
        public string BidDisplayMode { get; set; }

        /// <summary>
        /// Giá thầu Deep CPA
        /// </summary>
        public decimal? DeepCpaBid { get; set; }

        /// <summary>
        /// Thời lượng phát video được tối ưu hóa
        /// </summary>
        public string CpvVideoDuration { get; set; }

        /// <summary>
        /// Tỷ lệ giữ chân ngày 2
        /// </summary>
        public decimal? NextDayRetention { get; set; }

        /// <summary>
        /// Cửa sổ attribution nhấp chuột cho nhóm quảng cáo
        /// </summary>
        public string ClickAttributionWindow { get; set; }

        /// <summary>
        /// Cửa sổ attribution engaged view-through cho nhóm quảng cáo
        /// </summary>
        public string EngagedViewAttributionWindow { get; set; }

        /// <summary>
        /// Cửa sổ attribution view-through cho nhóm quảng cáo
        /// </summary>
        public string ViewAttributionWindow { get; set; }

        /// <summary>
        /// Số lượng sự kiện (Loại thống kê) cho nhóm quảng cáo
        /// </summary>
        public string AttributionEventCount { get; set; }

        /// <summary>
        /// Sự kiện tính phí
        /// </summary>
        public string BillingEvent { get; set; }

        /// <summary>
        /// Tốc độ
        /// </summary>
        public string Pacing { get; set; }

        /// <summary>
        /// Trạng thái hoạt động
        /// </summary>
        public string OperationStatus { get; set; }

        /// <summary>
        /// Trạng thái nhóm quảng cáo (trạng thái thứ cấp)
        /// </summary>
        public string SecondaryStatus { get; set; }

        /// <summary>
        /// Loại thống kê giá thầu chuyển đổi
        /// </summary>
        public string StatisticType { get; set; }

        /// <summary>
        /// Liệu sản phẩm được quảng bá có phải là thực phẩm HFSS hay không
        /// </summary>
        public bool IsHfss { get; set; }

        /// <summary>
        /// Chiến lược phân phối creative
        /// </summary>
        public string CreativeMaterialMode { get; set; }

        /// <summary>
        /// Cho biết nhóm quảng cáo có sử dụng trang hồ sơ ứng dụng hay không
        /// </summary>
        public string AdgroupAppProfilePageState { get; set; }

        /// <summary>
        /// Tùy chọn loại feed
        /// </summary>
        public string FeedType { get; set; }

        /// <summary>
        /// ID nhóm kiểm tra phân tách
        /// </summary>
        public string SplitTestGroupId { get; set; }

        /// <summary>
        /// Trạng thái kiểm tra phân tách
        /// </summary>
        public string SplitTestStatus { get; set; }

        /// <summary>
        /// Liệu chiến dịch có cấu trúc mới hay không
        /// </summary>
        public bool IsNewStructure { get; set; }

        /// <summary>
        /// Có bỏ qua giai đoạn học hay không
        /// </summary>
        public bool SkipLearningPhase { get; set; }

        /// <summary>
        /// Constructor mặc định
        /// </summary>
        public AdGroupDto()
        {
            Placements = new List<string>();
            TiktokSubplacements = new List<string>();
            BlockedPangleAppIds = new List<string>();
            LocationIds = new List<string>();
            ZipcodeIds = new List<string>();
            Languages = new List<string>();
            AgeGroups = new List<string>();
            HouseholdIncome = new List<string>();
            AudienceIds = new List<string>();
            ExcludedAudienceIds = new List<string>();
            InterestCategoryIds = new List<string>();
            InterestKeywordIds = new List<string>();
            PurchaseIntentionKeywordIds = new List<string>();
            IncludedPangleAudiencePackageIds = new List<string>();
            ExcludedPangleAudiencePackageIds = new List<string>();
            OperatingSystems = new List<string>();
            DeviceModelIds = new List<string>();
            NetworkTypes = new List<string>();
            CarrierIds = new List<string>();
            IspIds = new List<string>();
            DevicePriceRanges = new List<int>();
            ContextualTagIds = new List<string>();
            CategoryExclusionIds = new List<string>();
        }
    }
}