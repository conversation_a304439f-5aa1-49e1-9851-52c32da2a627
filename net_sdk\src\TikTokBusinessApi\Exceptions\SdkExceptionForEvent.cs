/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;

namespace TikTokBusinessApi.Exceptions
{
    /// <summary>
    /// Exception thrown by the SDK for event-related errors
    /// </summary>
    public class SdkExceptionForEvent : SdkException
    {
        /// <summary>
        /// Event data associated with the error
        /// </summary>
        public object? EventData { get; set; }

        /// <summary>
        /// Initializes a new instance of the SdkExceptionForEvent class
        /// </summary>
        public SdkExceptionForEvent()
        {
        }

        /// <summary>
        /// Initializes a new instance of the SdkExceptionForEvent class with a message
        /// </summary>
        /// <param name="message">Exception message</param>
        public SdkExceptionForEvent(string message) : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the SdkExceptionForEvent class with a message and inner exception
        /// </summary>
        /// <param name="message">Exception message</param>
        /// <param name="innerException">Inner exception</param>
        public SdkExceptionForEvent(string message, Exception innerException) : base(message, innerException)
        {
        }

        /// <summary>
        /// Initializes a new instance of the SdkExceptionForEvent class with full details
        /// </summary>
        /// <param name="requestId">Request ID</param>
        /// <param name="errorCode">Error code</param>
        /// <param name="message">Exception message</param>
        /// <param name="eventData">Event data</param>
        public SdkExceptionForEvent(string requestId, int errorCode, string message, object? eventData) 
            : base(requestId, errorCode, message)
        {
            EventData = eventData;
        }

        /// <summary>
        /// Returns a string representation of the exception
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"SdkExceptionForEvent: {Message} (Code: {ErrorCode}, RequestId: {RequestId}, EventData: {EventData})";
        }
    }
}
