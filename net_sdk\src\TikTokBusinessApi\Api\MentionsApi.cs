/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Mentions operations
    /// </summary>
    public class MentionsApi : IMentionsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<MentionsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the MentionsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public MentionsApi(IApiClient apiClient, ILogger<MentionsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <inheritdoc />
        public async Task<GetMentionedPostsResponse> GetMentionedPostsAsync(
            GetMentionedPostsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BusinessId))
                throw new ArgumentException("Business ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting mentioned posts for business {BusinessId}", request.BusinessId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = request.BusinessId
            };

            if (request.Fields?.Any() == true)
                queryParams["fields"] = $"[{string.Join(",", request.Fields.Select(f => $"\"{f}\""))}]";

            if (request.SortField.HasValue)
                queryParams["sort_field"] = request.SortField.Value.ToString();

            if (request.SortType.HasValue)
                queryParams["sort_type"] = request.SortType.Value.ToString();

            if (request.NumberOfDays.HasValue)
                queryParams["number_of_days"] = request.NumberOfDays.Value.ToString();

            if (request.Regions?.Any() == true)
                queryParams["regions"] = $"[{string.Join(",", request.Regions.Select(r => $"\"{r}\""))}]";

            if (request.Cursor.HasValue)
                queryParams["cursor"] = request.Cursor.Value.ToString();

            if (request.MaxCount.HasValue)
                queryParams["max_count"] = request.MaxCount.Value.ToString();

            return await _apiClient.CallApiAsync<GetMentionedPostsResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MentionsEndpoints.GetMentionedPosts}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<GetMentionedPostDetailsResponse> GetMentionedPostDetailsAsync(
            GetMentionedPostDetailsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BusinessId))
                throw new ArgumentException("Business ID cannot be null or empty", nameof(request));

            if (string.IsNullOrEmpty(request.ItemId))
                throw new ArgumentException("Item ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting mentioned post details for business {BusinessId}, item {ItemId}", 
                request.BusinessId, request.ItemId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = request.BusinessId,
                ["item_id"] = request.ItemId
            };

            if (request.Fields?.Any() == true)
                queryParams["fields"] = $"[{string.Join(",", request.Fields.Select(f => $"\"{f}\""))}]";

            return await _apiClient.CallApiAsync<GetMentionedPostDetailsResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MentionsEndpoints.GetMentionedPostDetails}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<GetTopKeywordsResponse> GetTopKeywordsAsync(
            GetTopKeywordsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BusinessId))
                throw new ArgumentException("Business ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting top keywords for business {BusinessId}", request.BusinessId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = request.BusinessId
            };

            if (request.Regions?.Any() == true)
                queryParams["regions"] = $"[{string.Join(",", request.Regions.Select(r => $"\"{r}\""))}]";

            return await _apiClient.CallApiAsync<GetTopKeywordsResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MentionsEndpoints.GetTopKeywords}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<GetTopHashtagsResponse> GetTopHashtagsAsync(
            GetTopHashtagsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BusinessId))
                throw new ArgumentException("Business ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting top hashtags for business {BusinessId}", request.BusinessId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = request.BusinessId
            };

            if (request.Regions?.Any() == true)
                queryParams["regions"] = $"[{string.Join(",", request.Regions.Select(r => $"\"{r}\""))}]";

            return await _apiClient.CallApiAsync<GetTopHashtagsResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MentionsEndpoints.GetTopHashtags}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<GetBrandHashtagContentResponse> GetBrandHashtagContentAsync(
            GetBrandHashtagContentRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BusinessId))
                throw new ArgumentException("Business ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting brand hashtag content for business {BusinessId}", request.BusinessId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = request.BusinessId
            };

            if (request.Fields?.Any() == true)
                queryParams["fields"] = $"[{string.Join(",", request.Fields.Select(f => $"\"{f}\""))}]";

            if (request.SortField.HasValue)
                queryParams["sort_field"] = request.SortField.Value.ToString();

            if (request.SortOrder.HasValue)
                queryParams["sort_order"] = request.SortOrder.Value.ToString();

            if (request.NumberOfDays.HasValue)
                queryParams["number_of_days"] = request.NumberOfDays.Value.ToString();

            if (request.Regions?.Any() == true)
                queryParams["regions"] = $"[{string.Join(",", request.Regions.Select(r => $"\"{r}\""))}]";

            if (request.Cursor.HasValue)
                queryParams["cursor"] = request.Cursor.Value.ToString();

            if (request.MaxCount.HasValue)
                queryParams["max_count"] = request.MaxCount.Value.ToString();

            return await _apiClient.CallApiAsync<GetBrandHashtagContentResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MentionsEndpoints.GetBrandHashtagContent}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<GetValidBrandHashtagsResponse> GetValidBrandHashtagsAsync(
            GetValidBrandHashtagsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BusinessId))
                throw new ArgumentException("Business ID cannot be null or empty", nameof(request));

            if (string.IsNullOrEmpty(request.Username))
                throw new ArgumentException("Username cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting valid brand hashtags for business {BusinessId}, username {Username}", 
                request.BusinessId, request.Username);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = request.BusinessId,
                ["username"] = request.Username
            };

            return await _apiClient.CallApiAsync<GetValidBrandHashtagsResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MentionsEndpoints.GetValidBrandHashtags}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <inheritdoc />
        // public async Task<EnableBrandHashtagsResponse> EnableBrandHashtagsAsync(
        //     EnableBrandHashtagsRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.BusinessId))
        //         throw new ArgumentException("Business ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrEmpty(request.Username))
        //         throw new ArgumentException("Username cannot be null or empty", nameof(request));

        //     if (request.Hashtags?.Any() != true)
        //         throw new ArgumentException("Hashtags cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Enabling brand hashtags for business {BusinessId}, username {Username}",
        //         request.BusinessId, request.Username);

        //     return await _apiClient.CallApiAsync<EnableBrandHashtagsResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MentionsEndpoints.EnableBrandHashtags}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        public async Task<GetEnabledHashtagsResponse> GetEnabledHashtagsAsync(
            GetEnabledHashtagsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BusinessId))
                throw new ArgumentException("Business ID cannot be null or empty", nameof(request));

            if (string.IsNullOrEmpty(request.Username))
                throw new ArgumentException("Username cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting enabled hashtags for business {BusinessId}, username {Username}",
                request.BusinessId, request.Username);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = request.BusinessId,
                ["username"] = request.Username
            };

            return await _apiClient.CallApiAsync<GetEnabledHashtagsResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MentionsEndpoints.GetEnabledHashtags}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <inheritdoc />
        // public async Task<DeleteEnabledHashtagsResponse> DeleteEnabledHashtagsAsync(
        //     DeleteEnabledHashtagsRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.BusinessId))
        //         throw new ArgumentException("Business ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrEmpty(request.Username))
        //         throw new ArgumentException("Username cannot be null or empty", nameof(request));

        //     if (string.IsNullOrEmpty(request.Hashtag))
        //         throw new ArgumentException("Hashtag cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Deleting enabled hashtag {Hashtag} for business {BusinessId}, username {Username}",
        //         request.Hashtag, request.BusinessId, request.Username);

        //     return await _apiClient.CallApiAsync<DeleteEnabledHashtagsResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MentionsEndpoints.DeleteEnabledHashtags}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        public async Task<GetMentionsInCommentsResponse> GetMentionsInCommentsAsync(
            GetMentionsInCommentsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BusinessId))
                throw new ArgumentException("Business ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting mentions in comments for business {BusinessId}", request.BusinessId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = request.BusinessId
            };

            if (request.Fields?.Any() == true)
                queryParams["fields"] = $"[{string.Join(",", request.Fields.Select(f => $"\"{f}\""))}]";

            if (request.SortField.HasValue)
                queryParams["sort_field"] = request.SortField.Value.ToString();

            if (request.SortType.HasValue)
                queryParams["sort_type"] = request.SortType.Value.ToString();

            if (request.NumberOfDays.HasValue)
                queryParams["number_of_days"] = request.NumberOfDays.Value.ToString();

            if (request.Regions?.Any() == true)
                queryParams["regions"] = $"[{string.Join(",", request.Regions.Select(r => $"\"{r}\""))}]";

            if (request.Cursor.HasValue)
                queryParams["cursor"] = request.Cursor.Value.ToString();

            if (request.MaxCount.HasValue)
                queryParams["max_count"] = request.MaxCount.Value.ToString();

            return await _apiClient.CallApiAsync<GetMentionsInCommentsResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MentionsEndpoints.GetMentionsInComments}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<GetCommentMentionDetailsResponse> GetCommentMentionDetailsAsync(
            GetCommentMentionDetailsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BusinessId))
                throw new ArgumentException("Business ID cannot be null or empty", nameof(request));

            if (string.IsNullOrEmpty(request.CommentId))
                throw new ArgumentException("Comment ID cannot be null or empty", nameof(request));

            if (string.IsNullOrEmpty(request.ItemId))
                throw new ArgumentException("Item ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting comment mention details for business {BusinessId}, comment {CommentId}, item {ItemId}",
                request.BusinessId, request.CommentId, request.ItemId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = request.BusinessId,
                ["comment_id"] = request.CommentId,
                ["item_id"] = request.ItemId
            };

            if (request.Fields?.Any() == true)
                queryParams["fields"] = $"[{string.Join(",", request.Fields.Select(f => $"\"{f}\""))}]";

            return await _apiClient.CallApiAsync<GetCommentMentionDetailsResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MentionsEndpoints.GetCommentMentionDetails}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
