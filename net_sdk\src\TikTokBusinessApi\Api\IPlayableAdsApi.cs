/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Playable Ads operations
    /// </summary>
    public interface IPlayableAdsApi
    {
        /// <summary>
        /// Upload a playable creative
        /// </summary>
        /// <param name="request">Playable upload request</param>
        /// <param name="playablePackage">Playable Ad asset package (ZIP file, max 5MB)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing playable ID</returns>
        //Task<PlayableUploadResponse> UploadPlayableAsync(
        //    PlayableUploadRequest request,
        //    byte[]? playablePackage = null,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Check the validation and audit status of the uploaded playable creative
        /// </summary>
        /// <param name="request">Playable validate request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing playable status and details</returns>
        //Task<PlayableValidateResponse> ValidatePlayableAsync(
        //    PlayableValidateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Save a playable creative
        /// </summary>
        /// <param name="request">Playable save request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing saved playable details</returns>
        //Task<PlayableSaveResponse> SavePlayableAsync(
        //    PlayableSaveRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a list of playable creatives
        /// </summary>
        /// <param name="request">Playable get request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of playable creatives</returns>
        Task<PlayableGetResponse> GetPlayablesAsync(
            PlayableGetRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a playable creative
        /// </summary>
        /// <param name="request">Playable delete request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deleted playable ID</returns>
        //Task<PlayableDeleteResponse> DeletePlayableAsync(
        //    PlayableDeleteRequest request,
        //    CancellationToken cancellationToken = default);
    }
}
