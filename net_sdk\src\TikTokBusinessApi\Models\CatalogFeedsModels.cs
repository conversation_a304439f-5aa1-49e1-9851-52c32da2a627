/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for creating a catalog feed
    /// </summary>
    public class CatalogFeedCreateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Name of the feed
        /// </summary>
        [JsonPropertyName("feed_name")]
        public string FeedName { get; set; } = string.Empty;

        /// <summary>
        /// The update mode (OVERWRITE, INCREMENTAL)
        /// </summary>
        [JsonPropertyName("update_mode")]
        public string UpdateMode { get; set; } = string.Empty;

        /// <summary>
        /// Schedule data
        /// </summary>
        [JsonPropertyName("schedule_param")]
        public FeedScheduleParam? ScheduleParam { get; set; }
    }

    /// <summary>
    /// Request body for updating a catalog feed
    /// </summary>
    public class CatalogFeedUpdateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string FeedId { get; set; } = string.Empty;

        /// <summary>
        /// The update mode (OVERWRITE, INCREMENTAL, SUPPLEMENT)
        /// </summary>
        [JsonPropertyName("update_mode")]
        public string UpdateMode { get; set; } = string.Empty;

        /// <summary>
        /// Schedule data
        /// </summary>
        [JsonPropertyName("schedule_param")]
        public FeedScheduleParam? ScheduleParam { get; set; }
    }

    /// <summary>
    /// Request body for deleting a catalog feed
    /// </summary>
    public class CatalogFeedDeleteBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string FeedId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request body for updating feed schedule status
    /// </summary>
    public class CatalogFeedScheduleStatusUpdateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string FeedId { get; set; } = string.Empty;

        /// <summary>
        /// Schedule status (ON, OFF)
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;
    }



    /// <summary>
    /// Response for creating/updating a catalog feed
    /// </summary>
    public class CatalogFeedResponse
    {
        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string FeedId { get; set; } = string.Empty;

        /// <summary>
        /// Feed name
        /// </summary>
        [JsonPropertyName("feed_name")]
        public string FeedName { get; set; } = string.Empty;

        /// <summary>
        /// The schedule status of the feed (ON, OFF)
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Information about the last update
        /// </summary>
        [JsonPropertyName("last_update_param")]
        public FeedLastUpdateParam? LastUpdateParam { get; set; }

        /// <summary>
        /// Date and time for the next feed run
        /// </summary>
        [JsonPropertyName("next_update_time")]
        public string? NextUpdateTime { get; set; }

        /// <summary>
        /// Number of products in the feed
        /// </summary>
        [JsonPropertyName("number_of_products")]
        public int NumberOfProducts { get; set; }
    }

    /// <summary>
    /// Response for deleting a catalog feed
    /// </summary>
    public class CatalogFeedDeleteResponse
    {
        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string FeedId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting catalog feeds
    /// </summary>
    public class CatalogFeedsGetResponse
    {
        /// <summary>
        /// Feed list
        /// </summary>
        [JsonPropertyName("feed_list")]
        public List<CatalogFeedInfo> FeedList { get; set; } = new List<CatalogFeedInfo>();
    }

    /// <summary>
    /// Catalog feed information
    /// </summary>
    public class CatalogFeedInfo
    {
        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string FeedId { get; set; } = string.Empty;

        /// <summary>
        /// Feed name
        /// </summary>
        [JsonPropertyName("feed_name")]
        public string FeedName { get; set; } = string.Empty;

        /// <summary>
        /// The schedule status of the feed (ON, OFF)
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Information about the last update
        /// </summary>
        [JsonPropertyName("last_update_param")]
        public FeedLastUpdateParam? LastUpdateParam { get; set; }

        /// <summary>
        /// Date and time for the next feed run
        /// </summary>
        [JsonPropertyName("next_update_time")]
        public string? NextUpdateTime { get; set; }

        /// <summary>
        /// Number of products in the feed
        /// </summary>
        [JsonPropertyName("number_of_products")]
        public int NumberOfProducts { get; set; }
    }

    /// <summary>
    /// Response for getting catalog feed logs
    /// </summary>
    public class CatalogFeedLogResponse
    {
        /// <summary>
        /// Log entry list
        /// </summary>
        [JsonPropertyName("feed_logs")]
        public List<FeedLogEntry> FeedLogs { get; set; } = new List<FeedLogEntry>();
    }
}
