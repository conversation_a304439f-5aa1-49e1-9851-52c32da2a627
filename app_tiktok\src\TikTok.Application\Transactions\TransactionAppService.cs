using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTok.Transactions;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp;

namespace TikTok.Transactions
{
    /// <summary>
    /// Service implementation cho giao dịch
    /// </summary>
    public class TransactionAppService :
        CrudAppService<
            RawTransactionEntity,
            TransactionDto,
            Guid,
            GetTransactionListDto,
            CreateTransactionDto,
            UpdateTransactionDto>,
        ITransactionAppService
    {
        private readonly ITransactionRepository _transactionRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="repository">Repository</param>
        /// <param name="transactionRepository">Transaction Repository</param>
        public TransactionAppService(
            IRepository<RawTransactionEntity, Guid> repository,
            ITransactionRepository transactionRepository) : base(repository)
        {
            _transactionRepository = transactionRepository;
        }

        /// <summary>
        /// Lấy giao dịch theo Transaction ID
        /// </summary>
        /// <param name="transactionId">ID của giao dịch</param>
        /// <returns>Giao dịch</returns>
        [Authorize(TikTokPermissions.Transactions.Default)]
        public async Task<TransactionDto> GetByTransactionIdAsync(string transactionId)
        {
            var entity = await _transactionRepository.GetByTransactionIdAsync(transactionId);
            return ObjectMapper.Map<RawTransactionEntity, TransactionDto>(entity);
        }

        /// <summary>
        /// Lấy danh sách giao dịch theo Business Center ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Danh sách giao dịch</returns>
        [Authorize(TikTokPermissions.Transactions.Default)]
        public async Task<PagedResultDto<TransactionDto>> GetByBcIdAsync(string bcId)
        {
            var entities = await _transactionRepository.GetByBcIdAsync(bcId);
            var dtos = ObjectMapper.Map<RawTransactionEntity[], TransactionDto[]>(entities.ToArray());

            return new PagedResultDto<TransactionDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        /// <summary>
        /// Lấy danh sách giao dịch theo Account ID
        /// </summary>
        /// <param name="accountId">ID của tài khoản quảng cáo</param>
        /// <returns>Danh sách giao dịch</returns>
        [Authorize(TikTokPermissions.Transactions.Default)]
        public async Task<PagedResultDto<TransactionDto>> GetByAccountIdAsync(string accountId)
        {
            var entities = await _transactionRepository.GetByAccountIdAsync(accountId);
            var dtos = ObjectMapper.Map<RawTransactionEntity[], TransactionDto[]>(entities.ToArray());

            return new PagedResultDto<TransactionDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        /// <summary>
        /// Lấy danh sách giao dịch theo Payment Portfolio ID
        /// </summary>
        /// <param name="paymentPortfolioId">ID của danh mục thanh toán</param>
        /// <returns>Danh sách giao dịch</returns>
        [Authorize(TikTokPermissions.Transactions.Default)]
        public async Task<PagedResultDto<TransactionDto>> GetByPaymentPortfolioIdAsync(string paymentPortfolioId)
        {
            var entities = await _transactionRepository.GetByPaymentPortfolioIdAsync(paymentPortfolioId);
            var dtos = ObjectMapper.Map<RawTransactionEntity[], TransactionDto[]>(entities.ToArray());

            return new PagedResultDto<TransactionDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        /// <summary>
        /// Ghi đè phương thức GetListAsync để sử dụng repository
        /// </summary>
        /// <param name="input">Input để lấy danh sách</param>
        /// <returns>Danh sách giao dịch</returns>
        public override async Task<PagedResultDto<TransactionDto>> GetListAsync(GetTransactionListDto input)
        {
            // Sử dụng repository để lấy danh sách và tổng số
            var entities = await _transactionRepository.GetListAsync(
                sorting: input.Sorting,
                maxResultCount: input.MaxResultCount,
                skipCount: input.SkipCount,
                filter: input.Filter,
                transactionId: input.TransactionId,
                paymentPortfolioId: input.PaymentPortfolioId,
                accountId: input.AccountId,
                bcId: input.BcId,
                bcName: input.BcName,
                accountName: input.AccountName,
                amountType: input.AmountType,
                transactionType: input.TransactionType,
                billingType: input.BillingType,
                currency: input.Currency,
                transactionLevel: input.TransactionLevel,
                createTimeFrom: input.CreateTimeFrom,
                createTimeTo: input.CreateTimeTo,
                amountFrom: input.AmountFrom,
                amountTo: input.AmountTo);

            var totalCount = await _transactionRepository.GetCountAsync(
                filter: input.Filter,
                transactionId: input.TransactionId,
                paymentPortfolioId: input.PaymentPortfolioId,
                accountId: input.AccountId,
                bcId: input.BcId,
                bcName: input.BcName,
                accountName: input.AccountName,
                amountType: input.AmountType,
                transactionType: input.TransactionType,
                billingType: input.BillingType,
                currency: input.Currency,
                transactionLevel: input.TransactionLevel,
                createTimeFrom: input.CreateTimeFrom,
                createTimeTo: input.CreateTimeTo,
                amountFrom: input.AmountFrom,
                amountTo: input.AmountTo);

            var dtos = ObjectMapper.Map<RawTransactionEntity[], TransactionDto[]>(entities.ToArray());

            return new PagedResultDto<TransactionDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }

        /// <summary>
        /// Ghi đè phương thức CreateAsync để thêm validation
        /// </summary>
        /// <param name="input">Input để tạo mới</param>
        /// <returns>Giao dịch đã tạo</returns>
        [Authorize(TikTokPermissions.Transactions.Create)]
        public override async Task<TransactionDto> CreateAsync(CreateTransactionDto input)
        {
            // Kiểm tra xem Transaction ID đã tồn tại chưa
            if (await _transactionRepository.IsTransactionIdExistsAsync(input.TransactionId))
            {
                throw new UserFriendlyException($"Transaction ID '{input.TransactionId}' đã tồn tại.");
            }

            return await base.CreateAsync(input);
        }

        /// <summary>
        /// Ghi đè phương thức UpdateAsync để thêm validation
        /// </summary>
        /// <param name="id">ID của entity</param>
        /// <param name="input">Input để cập nhật</param>
        /// <returns>Giao dịch đã cập nhật</returns>
        [Authorize(TikTokPermissions.Transactions.Edit)]
        public override async Task<TransactionDto> UpdateAsync(Guid id, UpdateTransactionDto input)
        {
            return await base.UpdateAsync(id, input);
        }
    }
} 