/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Catalog Feeds operations
    /// </summary>
    public class CatalogFeedsApi : ICatalogFeedsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<CatalogFeedsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the CatalogFeedsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public CatalogFeedsApi(IApiClient apiClient, ILogger<CatalogFeedsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Create a catalog feed
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing feed creation parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created feed information</returns>
        //public async Task<Response<CatalogFeedResponse>> CreateFeedAsync(
        //    string accessToken,
        //    CatalogFeedCreateBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrEmpty(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));

        //    _logger?.LogDebug("Creating catalog feed for bc_id: {BcId}, catalog_id: {CatalogId}",
        //        body.BcId, body.CatalogId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    var endpoint = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogFeedsEndpoints.CreateFeed}";

        //    return await _apiClient.CallApiAsync<Response<CatalogFeedResponse>>(
        //        endpoint,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get catalog feeds
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="feedId">Feed ID (optional - if not specified, all feeds for the catalog will be returned)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing feed information</returns>
        public async Task<Response<CatalogFeedsGetResponse>> GetFeedsAsync(
            string accessToken,
            string bcId,
            string catalogId,
            string? feedId = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            if (string.IsNullOrEmpty(bcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));

            if (string.IsNullOrEmpty(catalogId))
                throw new ArgumentException("Catalog ID cannot be null or empty", nameof(catalogId));

            _logger?.LogDebug("Getting catalog feeds for bc_id: {BcId}, catalog_id: {CatalogId}, feed_id: {FeedId}",
                bcId, catalogId, feedId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = bcId,
                ["catalog_id"] = catalogId
            };

            if (!string.IsNullOrEmpty(feedId))
            {
                queryParams["feed_id"] = feedId;
            }

            var endpoint = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogFeedsEndpoints.GetFeeds}";

            return await _apiClient.CallApiAsync<Response<CatalogFeedsGetResponse>>(
                endpoint,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Update a catalog feed
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing feed update parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated feed information</returns>
        //public async Task<Response<CatalogFeedResponse>> UpdateFeedAsync(
        //    string accessToken,
        //    CatalogFeedUpdateBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrEmpty(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));

        //    _logger?.LogDebug("Updating catalog feed for bc_id: {BcId}, catalog_id: {CatalogId}, feed_id: {FeedId}",
        //        body.BcId, body.CatalogId, body.FeedId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    var endpoint = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogFeedsEndpoints.UpdateFeed}";

        //    return await _apiClient.CallApiAsync<Response<CatalogFeedResponse>>(
        //        endpoint,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Delete a catalog feed
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing feed deletion parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deleted feed ID</returns>
        //public async Task<Response<CatalogFeedDeleteResponse>> DeleteFeedAsync(
        //    string accessToken,
        //    CatalogFeedDeleteBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrEmpty(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));

        //    _logger?.LogDebug("Deleting catalog feed for bc_id: {BcId}, catalog_id: {CatalogId}, feed_id: {FeedId}",
        //        body.BcId, body.CatalogId, body.FeedId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    var endpoint = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogFeedsEndpoints.DeleteFeed}";

        //    return await _apiClient.CallApiAsync<Response<CatalogFeedDeleteResponse>>(
        //        endpoint,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get the log of a catalog feed
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="feedId">Feed ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing feed log information</returns>
        public async Task<Response<CatalogFeedLogResponse>> GetFeedLogAsync(
            string accessToken,
            string bcId,
            string catalogId,
            string feedId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            if (string.IsNullOrEmpty(bcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));

            if (string.IsNullOrEmpty(catalogId))
                throw new ArgumentException("Catalog ID cannot be null or empty", nameof(catalogId));

            if (string.IsNullOrEmpty(feedId))
                throw new ArgumentException("Feed ID cannot be null or empty", nameof(feedId));

            _logger?.LogDebug("Getting catalog feed log for bc_id: {BcId}, catalog_id: {CatalogId}, feed_id: {FeedId}",
                bcId, catalogId, feedId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = bcId,
                ["catalog_id"] = catalogId,
                ["feed_id"] = feedId
            };

            var endpoint = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogFeedsEndpoints.GetFeedLog}";

            return await _apiClient.CallApiAsync<Response<CatalogFeedLogResponse>>(
                endpoint,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Update the schedule status of a catalog feed
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing schedule status update parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success</returns>
        //public async Task<Response<object>> UpdateFeedScheduleStatusAsync(
        //    string accessToken,
        //    CatalogFeedScheduleStatusUpdateBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrEmpty(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));

        //    _logger?.LogDebug("Updating catalog feed schedule status for bc_id: {BcId}, catalog_id: {CatalogId}, feed_id: {FeedId}, status: {Status}",
        //        body.BcId, body.CatalogId, body.FeedId, body.Status);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    var endpoint = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogFeedsEndpoints.UpdateFeedScheduleStatus}";

        //    return await _apiClient.CallApiAsync<Response<object>>(
        //        endpoint,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}
    }
}
