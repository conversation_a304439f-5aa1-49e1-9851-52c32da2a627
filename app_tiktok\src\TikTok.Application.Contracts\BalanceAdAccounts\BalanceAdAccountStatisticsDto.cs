using System;
using System.Collections.Generic;
using TikTok.Enums;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// DTO cho thống kê tài khoản nhà quảng cáo
    /// </summary>
    public class BalanceAdAccountStatisticsDto
    {
        /// <summary>
        /// Tổng số tài khoản
        /// </summary>
        public long TotalAccounts { get; set; }

        /// <summary>
        /// Tổng số dư tài khoản
        /// </summary>
        public decimal TotalAccountBalance { get; set; }

        /// <summary>
        /// Tổng ngân sách
        /// </summary>
        public decimal TotalBudget { get; set; }

        /// <summary>
        /// Số tài khoản theo trạng thái
        /// </summary>
        public Dictionary<AdvertiserAccountStatus, long> AccountsByStatus { get; set; }

        /// <summary>
        /// Số tài khoản theo loại
        /// </summary>
        public Dictionary<AdAccountType, long> AccountsByType { get; set; }

        /// <summary>
        /// Số tài khoản theo tiền tệ
        /// </summary>
        public Dictionary<string, long> AccountsByCurrency { get; set; }

        /// <summary>
        /// Số tài khoản có số dư thấp (dưới 100)
        /// </summary>
        public long LowBalanceAccounts { get; set; }

        /// <summary>
        /// Số tài khoản mới trong tháng này
        /// </summary>
        public long NewAccountsThisMonth { get; set; }

        /// <summary>
        /// Số tài khoản mới trong tuần này
        /// </summary>
        public long NewAccountsThisWeek { get; set; }

        /// <summary>
        /// Số dư trung bình
        /// </summary>
        public decimal AverageAccountBalance { get; set; }

        /// <summary>
        /// Ngân sách trung bình
        /// </summary>
        public decimal AverageBudget { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public BalanceAdAccountStatisticsDto()
        {
            AccountsByStatus = new Dictionary<AdvertiserAccountStatus, long>();
            AccountsByType = new Dictionary<AdAccountType, long>();
            AccountsByCurrency = new Dictionary<string, long>();
        }
    }
}