# RawBusinessCenterEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawBusinessCenters`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawBusinessCenters`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho trung tâm kinh doanh (Business Center). Lưu trữ thông tin cơ bản về Business Center bao gồm thông tin công ty, cài đặt khu vực, tiền tệ, múi giờ và vai trò người dùng trong hệ thống TikTok Business.

## Bảng dữ liệu: RawBusinessCenter

| Tên Field            | Kiểu dữ liệu         | Bắt buộc | Độ dài | Mô tả                                 |
| -------------------- | -------------------- | -------- | ------ | ------------------------------------- |
| Id                   | Guid                 | ✓        | -      | ID duy nhất của bản ghi (Primary Key) |
| BcId                 | string               | ✓        | 100    | ID của trung tâm kinh doanh           |
| Name                 | string               | ✓        | 255    | Tên trung tâm kinh doanh              |
| Company              | string               | ✓        | 255    | Tên công ty của trung tâm kinh doanh  |
| Currency             | string               | ✓        | 10     | Đơn vị tiền tệ thanh toán             |
| RegisteredArea       | string               | ✓        | 50     | Khu vực đăng ký                       |
| Status               | BusinessCenterStatus | ✓        | -      | Trạng thái của trung tâm kinh doanh   |
| Timezone             | string               | ✓        | 50     | Múi giờ                               |
| Type                 | BusinessCenterType   | ✓        | -      | Loại trung tâm kinh doanh             |
| UserRole             | UserRole             | ✓        | -      | Vai trò người dùng                    |
| ExtUserFinanceRole   | ExtUserFinanceRole?  | ❌       | -      | Vai trò tài chính mở rộng             |
| CreationTime         | DateTime             | ✓        | -      | Thời gian tạo bản ghi (Audit)         |
| CreatorId            | Guid?                | ❌       | -      | ID người tạo (Audit)                  |
| LastModificationTime | DateTime?            | ❌       | -      | Thời gian sửa đổi cuối (Audit)        |
| LastModifierId       | Guid?                | ❌       | -      | ID người sửa đổi cuối (Audit)         |

## Enums liên quan

### BusinessCenterStatus

-   Định nghĩa trạng thái của Business Center (active, inactive, suspended, etc.)

### BusinessCenterType

-   Định nghĩa loại Business Center (individual, business, agency, etc.)

### UserRole

-   Định nghĩa vai trò người dùng trong Business Center (owner, admin, member, etc.)

### ExtUserFinanceRole

-   Định nghĩa vai trò tài chính mở rộng (finance_admin, finance_viewer, etc.)

## Cấu trúc dữ liệu

### Thông tin định danh

-   **BcId**: ID duy nhất của Business Center từ TikTok API
-   **Name**: Tên hiển thị của Business Center
-   **Company**: Tên công ty sở hữu Business Center

### Cài đặt khu vực và tiền tệ

-   **Currency**: Đơn vị tiền tệ chính của Business Center
-   **RegisteredArea**: Khu vực đăng ký (quốc gia/vùng lãnh thổ)
-   **Timezone**: Múi giờ để tính toán thời gian

### Trạng thái và phân quyền

-   **Status**: Trạng thái hoạt động của Business Center
-   **Type**: Loại Business Center (cá nhân, doanh nghiệp, đại lý)
-   **UserRole**: Vai trò của người dùng hiện tại
-   **ExtUserFinanceRole**: Vai trò tài chính mở rộng (nếu có)

## Mục đích sử dụng

-   Lưu trữ thông tin cơ bản về Business Center
-   Quản lý cài đặt khu vực và tiền tệ
-   Theo dõi trạng thái hoạt động
-   Quản lý phân quyền người dùng
-   Làm cơ sở cho các entity khác liên quan đến Business Center

## Mối quan hệ

-   **RawAdAccountEntity**: Business Center sở hữu nhiều tài khoản quảng cáo
-   **RawBalanceBusinessCenterEntity**: Thông tin số dư của Business Center
-   **RawTransactionEntity**: Giao dịch liên quan đến Business Center

## Lưu ý

-   Entity này kế thừa từ AuditedEntity để có đầy đủ thông tin audit
-   BcId là khóa ngoại quan trọng để liên kết với các entity khác
-   Thông tin múi giờ và tiền tệ ảnh hưởng đến cách hiển thị dữ liệu trong hệ thống
-   Vai trò tài chính mở rộng có thể null để đảm bảo tính linh hoạt
