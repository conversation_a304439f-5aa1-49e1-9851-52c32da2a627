using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Customers;
using TikTok.Permissions;
using TikTok.ResourcePermissions;
using TikTok.Entities;
using TikTok.Repositories;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Uow;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System.IO;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.HSSF.UserModel;
using TikTok.Enums;
using TikTok.ResourceProviders;
using TikTok.ResourceProviders.AdAccounts;
using TikTok.Users;
using Volo.Abp.Application.Dtos;
using System.Linq.Dynamic.Core;

namespace TikTok.SupportManagement
{
    public class SupportManagementAppService : ApplicationService, ISupportManagementAppService
    {
        private readonly ICustomerAppService _customerAppService;
        private readonly IdentityUserManager _userManager;
        private readonly IdentityRoleManager _roleManager;
        private readonly IResourcePermissionAppService _resourcePermissionAppService;
        private readonly ICustomerRepository _customerRepository;
        private readonly ICustomerAdAccountRepository _customerAdAccountRepository;
        private readonly IAdAccountRepository _adAccountRepository;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly ILogger<SupportManagementAppService> _logger;
        private readonly IIdentityUserRepository _identityUserRepository;
        private readonly IAdAccountResourceProvider _adAccountResourceProvider;
        

        public SupportManagementAppService(
            ICustomerAppService customerAppService,
            IdentityUserManager userManager,
            IdentityRoleManager roleManager,
            IResourcePermissionAppService resourcePermissionAppService,
            ICustomerRepository customerRepository,
            ICustomerAdAccountRepository customerAdAccountRepository,
            IAdAccountRepository adAccountRepository,
            IUnitOfWorkManager unitOfWorkManager,
            ILogger<SupportManagementAppService> logger,
            IIdentityUserRepository identityUserRepository,
            IAdAccountResourceProvider adAccountResourceProvider)
        {
            _customerAppService = customerAppService;
            _userManager = userManager;
            _roleManager = roleManager;
            _resourcePermissionAppService = resourcePermissionAppService;
            _customerRepository = customerRepository;
            _customerAdAccountRepository = customerAdAccountRepository;
            _adAccountRepository = adAccountRepository;
            _unitOfWorkManager = unitOfWorkManager;
            _logger = logger;
            _identityUserRepository = identityUserRepository;
            _adAccountResourceProvider = adAccountResourceProvider;
        }

        public async Task<ImportCustomersResultDto> ImportExcelAsync(byte[] fileBytes, string fileName, string? connectionId = null)
        {
            if (fileBytes == null || fileBytes.Length == 0)
            {
                throw new UserFriendlyException("File rỗng");
            }

            var result = new ImportCustomersResultDto
            {
                Total = 0,
                Created = 0,
                Skipped = 0,
                SupportsCreated = 0,
                SupportsUpdated = 0,
                PermissionsCreated = 0,
                Message = string.Empty
            };

            try
            {
                // Bước 1: Parse File Excel/CSV (toàn bộ cột 1-25) - Không có UoW
                var parsedRows = ParseRows(fileBytes, fileName);
                result.Total = parsedRows.Count;

                if (parsedRows.Count == 0)
                {
                    result.Message = "File không có dữ liệu";
                    return result;
                }

                // Bước 2: Nhóm dữ liệu theo CustomerId - Không có UoW
                var customerGroups = parsedRows
                    .Where(r => !string.IsNullOrWhiteSpace(r.CustomerId))
                    .GroupBy(r => r.CustomerId?.Trim())
                    .ToList();

                var rowsWithIdCount = customerGroups.Sum(g => g.Count());
                result.Skipped += parsedRows.Count - rowsWithIdCount;

                // Bước 3: Xử lý theo Batch (200 records/batch) - Lưu Customer và CustomerAdAccount - CÓ UoW
                var batchSize = 200;
                var totalBatches = (int)Math.Ceiling((double)customerGroups.Count / batchSize);
                
                for (int i = 0; i < totalBatches; i++)
                {
                    var batchGroups = customerGroups.Skip(i * batchSize).Take(batchSize).ToList();
                    await SaveBatchAsync(batchGroups, result);
                }

                // Bước 4: Nhóm dữ liệu theo số điện thoại của tài khoản support (loại bỏ trùng lặp) - Không có UoW
                var supportGroups = parsedRows
                    .Where(r => !string.IsNullOrWhiteSpace(r.SupportPhoneNumber))
                    .GroupBy(r => r.SupportPhoneNumber?.Trim())
                    .ToList();

                // Bước 5: Validate thông tin support (chỉ kiểm tra không null/empty) - Không có UoW
                var validSupportGroups = supportGroups
                    .Where(g => !string.IsNullOrWhiteSpace(g.Key)) // Chỉ kiểm tra phone number không null/empty
                    .ToList();

                // Bước 6: Xử lý tài khoản Support - CÓ UoW
                var supportMapping = await ProcessSupportAccountsAsync(validSupportGroups, result);

                // Bước 7: Phân quyền Support - AdAccount - CÓ UoW
                await AssignSupportPermissionsAsync(parsedRows, supportMapping, result);

                result.Message = $"Import hoàn thành: {result.Created} khách hàng mới, {result.SupportsCreated} support mới, {result.PermissionsCreated} quyền được tạo. Bỏ qua: {result.Skipped} dòng.";
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi import Excel");
                result.Message = $"Lỗi: {ex.Message}";
            }

            return result;
        }

        [RemoteService(false)]
        public async Task<PagedResultDto<SupporterListItemDto>> GetSupportersAsync(GetSupporterListInput input)
        {
            var usersInRole = await _userManager.GetUsersInRoleAsync("supporter");

            if (!string.IsNullOrWhiteSpace(input.Filter))
            {
                var filter = input.Filter.Trim().ToLowerInvariant();
                usersInRole = usersInRole.Where(u =>
                    (!string.IsNullOrWhiteSpace(u.UserName) && u.UserName.ToLowerInvariant().Contains(filter)) ||
                    (!string.IsNullOrWhiteSpace(u.Name) && u.Name.ToLowerInvariant().Contains(filter)) ||
                    (!string.IsNullOrWhiteSpace(u.Email) && u.Email.ToLowerInvariant().Contains(filter)) ||
                    (!string.IsNullOrWhiteSpace(u.PhoneNumber) && u.PhoneNumber.ToLowerInvariant().Contains(filter))
                ).ToList();
            }

            var totalCount = usersInRole.Count;

            // Compute AdAccountsCount for ALL filtered users (needed to sort by this field)
            var allUserIds = usersInRole.Select(u => u.Id).ToList();
            var resourceAssignments = await _adAccountResourceProvider.GetListAsync(new TikTok.ResourceProviders.GetResourcePermissionAssignedUserDto
            {
                UserIds = allUserIds,
                Permissions = new List<string> { TikTokPermissions.AdAccounts.Default }
            });

            var adAccCountByUser = resourceAssignments
                .GroupBy(x => x.UserId)
                .ToDictionary(g => g.Key, g => g.Select(z => z.ResourceId).Distinct(StringComparer.OrdinalIgnoreCase).Count());

            // Sorting
            string sortKey = "name";
            string sortDir = "asc";
            if (!string.IsNullOrWhiteSpace(input.Sorting))
            {
                var parts = input.Sorting.Split(' ', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
                if (parts.Length >= 1)
                {
                    sortKey = parts[0];
                    if (parts.Length >= 2) sortDir = parts[1];
                }
            }

            Func<Volo.Abp.Identity.IdentityUser, object> selector = sortKey switch
            {
                "adAccountsCount" => u => adAccCountByUser.TryGetValue(u.Id, out var c) ? c : 0,
                "userName" => u => u.UserName ?? string.Empty,
                "email" => u => u.Email ?? string.Empty,
                "phoneNumber" => u => u.PhoneNumber ?? string.Empty,
                _ => u => u.Name ?? u.UserName ?? string.Empty
            };

            IEnumerable<Volo.Abp.Identity.IdentityUser> ordered = sortDir.Equals("desc", StringComparison.OrdinalIgnoreCase)
                ? usersInRole.OrderByDescending(selector)
                : usersInRole.OrderBy(selector);

            var pageItems = ordered.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();

            var items = pageItems.Select(u => new SupporterListItemDto
            {
                Id = u.Id,
                UserName = u.UserName,
                Name = u.Name,
                Email = u.Email,
                PhoneNumber = u.PhoneNumber,
                AdAccountsCount = adAccCountByUser.TryGetValue(u.Id, out var c) ? c : 0
            }).ToList();

            return new PagedResultDto<SupporterListItemDto>
            {
                TotalCount = totalCount,
                Items = items
            };
        }

        [RemoteService(false)]
        public async Task<PagedResultDto<CustomerAdAccountFlatDto>> GetSupporterAdAccountsAsync(Guid supporterId, GetCustomerAdAccountListDto input)
        {
            // Get all advertiserIds assigned to this supporter
            var resources = await _adAccountResourceProvider.GetListAsync(new TikTok.ResourceProviders.GetResourcePermissionAssignedUserDto
            {
                UserId = supporterId,
                Permissions = new List<string> { TikTokPermissions.AdAccounts.Default }
            });

            var advertiserIds = resources.Select(r => r.ResourceId).Distinct(StringComparer.OrdinalIgnoreCase).ToList();
            if (!advertiserIds.Any())
            {
                return new PagedResultDto<CustomerAdAccountFlatDto>
                {
                    TotalCount = 0,
                    Items = new List<CustomerAdAccountFlatDto>()
                };
            }

            // Query CustomerAdAccount with customer info by advertiserIds
            var total = await _customerAdAccountRepository.GetCountByAdvertiserIdsAsync(advertiserIds, input.Filter);

            // Map client sorting keys to entity fields
            string? sorting = null;
            if (!string.IsNullOrWhiteSpace(input.Sorting))
            {
                var parts = input.Sorting.Split(' ', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
                if (parts.Length >= 1)
                {
                    var key = parts[0];
                    var dir = parts.Length >= 2 ? parts[1] : "asc";
                    sorting = key switch
                    {
                        // Customer columns map to navigation
                        "customerName" => $"Customer.CustomerName {dir}",
                        "customerIdCode" => $"Customer.CustomerId {dir}",
                        // Flat columns
                        "advertiserId" => $"AdvertiserId {dir}",
                        "advertiserName" => $"AdvertiserName {dir}",
                        "shopId" => $"ShopId {dir}",
                        "shopName" => $"ShopName {dir}",
                        _ => input.Sorting
                    };
                }
            }

            var list = await _customerAdAccountRepository.GetListWithCustomerInfoByAdvertiserIdsAsync(
                advertiserIds,
                sorting,
                input.MaxResultCount,
                input.SkipCount,
                input.Filter
            );

            var flatDtos = list.Select(ad => new CustomerAdAccountFlatDto
            {
                Id = ad.Id,
                CustomerId = ad.CustomerId,
                CustomerIdCode = ad.Customer?.CustomerId ?? string.Empty,
                CustomerName = ad.Customer?.CustomerName ?? string.Empty,
                AdvertiserId = ad.AdvertiserId,
                AdvertiserName = ad.AdvertiserName,
                ShopId = ad.ShopId,
                ShopName = ad.ShopName,
                CreationTime = ad.CreationTime,
                CreatorId = ad.CreatorId,
                LastModificationTime = ad.LastModificationTime,
                LastModifierId = ad.LastModifierId
            }).ToList();

            return new PagedResultDto<CustomerAdAccountFlatDto>
            {
                TotalCount = total,
                Items = flatDtos
            };
        }

        private async Task SaveBatchAsync(List<IGrouping<string?, ParsedRow>> batchGroups, ImportCustomersResultDto result)
        {
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var listInsert = new List<CustomerEntity>();
                var listUpdate = new List<CustomerEntity>();

                // Lấy trước khách hàng hiện có theo CustomerId (string)
                var customerIds = batchGroups.Select(x => x.Key).ToList();
                var allCustomers = await _customerRepository.GetListAsync(x => customerIds.Contains(x.CustomerId));

                // Lấy trước ad accounts hiện có của các khách hàng trong batch
                var existingCustomerGuids = allCustomers.Select(c => c.Id).ToList();
                var existingAdAccounts = existingCustomerGuids.Count > 0
                    ? await _customerAdAccountRepository.GetListAsync(x => existingCustomerGuids.Contains(x.CustomerId))
                    : new List<CustomerAdAccountEntity>();

                // Tạo map: CustomerId(Guid) -> { normalizedAdvertiserId -> entity }
                string Normalize(string? s) => (s ?? string.Empty).Trim().ToLowerInvariant();
                var adAccountMap = existingAdAccounts
                    .Where(a => !string.IsNullOrWhiteSpace(a.AdvertiserId))
                    .GroupBy(a => a.CustomerId)
                    .ToDictionary(
                        g => g.Key,
                        g => g
                            .GroupBy(a => Normalize(a.AdvertiserId))
                            .ToDictionary(gg => gg.Key, gg => gg.First())
                    );

                var adAccountsToInsert = new List<CustomerAdAccountEntity>();
                var adAccountsToUpdate = new List<CustomerAdAccountEntity>();

                foreach (var group in batchGroups)
                {
                    var customerId = group.Key;
                    if (string.IsNullOrEmpty(customerId))
                        continue;

                    var first = group.First();
                    var normalizedCustomerId = customerId.Trim();

                    var existing = allCustomers.FirstOrDefault(x => x.CustomerId.ToLower().Trim().Equals(normalizedCustomerId.ToLower().Trim(), StringComparison.InvariantCultureIgnoreCase));

                    // Dedupe theo AdvertiserId trong chính group này
                    var distinctAds = group
                        .Where(x => !string.IsNullOrWhiteSpace(x.AdvertiserId))
                        .GroupBy(x => Normalize(x.AdvertiserId))
                        .Select(g => g.First())
                        .ToList();

                    if (existing != null)
                    {
                        // Update thông tin customer
                        existing.CustomerName = string.IsNullOrWhiteSpace(first.CustomerName) ? first.CustomerId! : first.CustomerName!;
                        existing.PhoneNumber = string.IsNullOrWhiteSpace(first.PhoneNumber) ? null : first.PhoneNumber;
                        existing.CustomerType = NormalizeCustomerType(first.CustomerTypeText);

                        var perCustomerMap = adAccountMap.TryGetValue(existing.Id, out var m) ? m : new Dictionary<string, CustomerAdAccountEntity>();

                        foreach (var row in distinctAds)
                        {
                            var key = Normalize(row.AdvertiserId);
                            if (perCustomerMap.TryGetValue(key, out var existedAd))
                            {
                                bool changed = false;
                                var newName = string.IsNullOrWhiteSpace(row.AdvertiserName) ? null : row.AdvertiserName;
                                var newShopId = string.IsNullOrWhiteSpace(row.ShopId) ? null : row.ShopId;
                                var newShopName = string.IsNullOrWhiteSpace(row.ShopName) ? null : row.ShopName;

                                if (existedAd.AdvertiserName != newName) { existedAd.AdvertiserName = newName; changed = true; }
                                if (existedAd.ShopId != newShopId) { existedAd.ShopId = newShopId; changed = true; }
                                if (existedAd.ShopName != newShopName) { existedAd.ShopName = newShopName; changed = true; }

                                if (changed)
                                {
                                    adAccountsToUpdate.Add(existedAd);
                                }
                            }
                            else
                            {
                                adAccountsToInsert.Add(new CustomerAdAccountEntity(Guid.NewGuid())
                                {
                                    CustomerId = existing.Id,
                                    AdvertiserId = string.IsNullOrWhiteSpace(row.AdvertiserId) ? null : row.AdvertiserId,
                                    AdvertiserName = string.IsNullOrWhiteSpace(row.AdvertiserName) ? null : row.AdvertiserName,
                                    ShopId = string.IsNullOrWhiteSpace(row.ShopId) ? null : row.ShopId,
                                    ShopName = string.IsNullOrWhiteSpace(row.ShopName) ? null : row.ShopName
                                });
                            }
                        }

                        listUpdate.Add(existing);
                    }
                    else
                    {
                        // Tạo mới customer
                        var customer = new CustomerEntity(Guid.NewGuid())
                        {
                            CustomerId = first.CustomerId!,
                            CustomerName = string.IsNullOrWhiteSpace(first.CustomerName) ? first.CustomerId! : first.CustomerName!,
                            AccountName = null,
                            PhoneNumber = string.IsNullOrWhiteSpace(first.PhoneNumber) ? null : first.PhoneNumber,
                            CustomerType = NormalizeCustomerType(first.CustomerTypeText),
                            Website = null
                        };

                        listInsert.Add(customer);
                        result.Created++;

                        // Thêm ad account từ danh sách đã dedupe
                        foreach (var row in distinctAds)
                        {
                            adAccountsToInsert.Add(new CustomerAdAccountEntity(Guid.NewGuid())
                            {
                                CustomerId = customer.Id,
                                AdvertiserId = string.IsNullOrWhiteSpace(row.AdvertiserId) ? null : row.AdvertiserId,
                                AdvertiserName = string.IsNullOrWhiteSpace(row.AdvertiserName) ? null : row.AdvertiserName,
                                ShopId = string.IsNullOrWhiteSpace(row.ShopId) ? null : row.ShopId,
                                ShopName = string.IsNullOrWhiteSpace(row.ShopName) ? null : row.ShopName
                            });
                        }
                    }
                }

                if (listInsert.Count > 0)
                {
                    await _customerRepository.InsertManyAsync(listInsert);
                }
                if (listUpdate.Count > 0)
                {
                    await _customerRepository.UpdateManyAsync(listUpdate);
                }
                if (adAccountsToInsert.Count > 0)
                {
                    await _customerAdAccountRepository.InsertManyAsync(adAccountsToInsert);
                }
                if (adAccountsToUpdate.Count > 0)
                {
                    await _customerAdAccountRepository.UpdateManyAsync(adAccountsToUpdate);
                }

                await uow.CompleteAsync();
            }
        }

        private sealed class ParsedRow
        {
            public string? CustomerId { get; set; }
            public string? CustomerName { get; set; }
            public string? AdvertiserName { get; set; }
            public string? AdvertiserId { get; set; }
            public string? ShopName { get; set; }
            public string? ShopId { get; set; }
            public string? PhoneNumber { get; set; }
            public string? CustomerTypeText { get; set; }
            // Thêm thông tin support từ cột 24, 25
            public string? SupportPhoneNumber { get; set; }
            public string? SupportEmail { get; set; }
        }

        private List<ParsedRow> ParseRows(byte[] fileBytes, string fileName)
        {
            if (fileName.EndsWith(".csv", StringComparison.OrdinalIgnoreCase))
            {
                return ParseCsv(fileBytes);
            }

            using var stream = new MemoryStream(fileBytes);
            IWorkbook workbook;
            if (fileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
            {
                workbook = new XSSFWorkbook(stream);
            }
            else if (fileName.EndsWith(".xls", StringComparison.OrdinalIgnoreCase))
            {
                workbook = new HSSFWorkbook(stream);
            }
            else
            {
                throw new UserFriendlyException("Định dạng file không được hỗ trợ. Hãy dùng .xlsx, .xls hoặc .csv");
            }

            var rows = new List<ParsedRow>();
            var sheet = workbook.GetSheetAt(0);
            if (sheet == null)
            {
                return rows;
            }

            for (int r = 1; r <= sheet.LastRowNum; r++)
            {
                var row = sheet.GetRow(r);
                if (row == null) continue;

                rows.Add(new ParsedRow
                {
                    CustomerId = row.GetCell(1)?.ToString()?.Trim(),
                    CustomerName = row.GetCell(2)?.ToString()?.Trim(),
                    AdvertiserName = row.GetCell(3)?.ToString()?.Trim(),
                    AdvertiserId = row.GetCell(4)?.ToString()?.Trim(),
                    ShopName = row.GetCell(5)?.ToString()?.Trim(),
                    ShopId = row.GetCell(6)?.ToString()?.Trim(),
                    PhoneNumber = row.GetCell(9)?.ToString()?.Trim(),
                    CustomerTypeText = row.GetCell(10)?.ToString()?.Trim(),
                    // Đọc thông tin support từ cột 24, 25
                    SupportPhoneNumber = row.GetCell(23)?.ToString()?.Trim(), // Cột 24 (index 23)
                    SupportEmail = row.GetCell(24)?.ToString()?.Trim(), // Cột 25 (index 24)
                });
            }

            // Count skipped rows missing essential fields
            return rows;
        }

        private List<ParsedRow> ParseCsv(byte[] fileBytes)
        {
            var rows = new List<ParsedRow>();
            using var reader = new StreamReader(new MemoryStream(fileBytes));
            // Skip header
            string? line = reader.ReadLine();
            while ((line = reader.ReadLine()) != null)
            {
                var cells = line.Split(',');
                if (cells.Length < 11)
                {
                    continue;
                }
                rows.Add(new ParsedRow
                {
                    CustomerId = cells[1]?.Trim(),
                    CustomerName = cells[2]?.Trim(),
                    AdvertiserName = cells[3]?.Trim(),
                    AdvertiserId = cells[4]?.Trim(),
                    ShopName = cells[5]?.Trim(),
                    ShopId = cells[6]?.Trim(),
                    PhoneNumber = cells[9]?.Trim(),
                    CustomerTypeText = cells[10]?.Trim(),
                    // Đọc thông tin support từ cột 24, 25
                    SupportPhoneNumber = cells.Length > 23 ? cells[23]?.Trim() : null, // Cột 24 (index 23)
                    SupportEmail = cells.Length > 24 ? cells[24]?.Trim() : null, // Cột 25 (index 24)
                });
            }
            return rows;
        }

        private CustomerType NormalizeCustomerType(string? typeText)
        {
            if (string.IsNullOrWhiteSpace(typeText))
                return CustomerType.RETAIL;

            var normalized = typeText.ToLowerInvariant();
            if (normalized.Contains("đại lý") || normalized.Contains("dai ly") || normalized.Contains("agency"))
            {
                return CustomerType.AGENCY;
            }
            if (normalized.Contains("khách lẻ") || normalized.Contains("khach le") || normalized.Contains("retail"))
            {
                return CustomerType.RETAIL;
            }
            return CustomerType.RETAIL;
        }

        

        private async Task<Dictionary<string, Guid>> ProcessSupportAccountsAsync(List<IGrouping<string?, ParsedRow>> supportGroups, ImportCustomersResultDto result)
        {
            var supportMapping = new Dictionary<string, Guid>();

            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true))
            {
                var existingSupportUsers = await _userManager.GetUsersInRoleAsync("supporter");
                var nextSupportNumber = (existingSupportUsers?.Count ?? 0) + 1;
                foreach (var group in supportGroups)
                {
                    var phoneNumber = group.Key;
                    if (string.IsNullOrWhiteSpace(phoneNumber))
                    {
                        continue;
                    }
                    var firstRow = group.First();
                    var email = firstRow.SupportEmail;

                    // Tìm support theo số điện thoại (ưu tiên theo PhoneNumber) qua repository (tránh IQueryable store)
                    var phoneCandidates = await _identityUserRepository.GetListAsync(filter: phoneNumber);
                    var existingSupport = phoneCandidates.FirstOrDefault(u => u.PhoneNumber == phoneNumber);
                    
                    if (existingSupport == null && !string.IsNullOrWhiteSpace(email))
                    {
                        // Tìm theo email nếu không tìm thấy theo phone
                        existingSupport = await _userManager.FindByEmailAsync(email);
                    }

                    if (existingSupport != null)
                    {
                        // Cập nhật thông tin support nếu cần
                        if (!string.IsNullOrWhiteSpace(email) && existingSupport.Email != email)
                        {
                            await _userManager.SetEmailAsync(existingSupport, email);
                            await _userManager.UpdateAsync(existingSupport);
                        }
                        supportMapping[phoneNumber] = existingSupport.Id;
                        result.SupportsUpdated++;
                    }
                    else
                    {
                        // Tạo mới support
                        // Đặt tên theo dạng support{n}, đảm bảo không trùng
                        string userNameCandidate;
                        while (true)
                        {
                            userNameCandidate = $"support{nextSupportNumber}";
                            var existedName = await _userManager.FindByNameAsync(userNameCandidate);
                            if (existedName == null)
                            {
                                break;
                            }
                            nextSupportNumber++;
                        }

                        var newSupport = new Volo.Abp.Identity.IdentityUser(Guid.NewGuid(), userNameCandidate, email);
                        newSupport.SetPhoneNumber(phoneNumber, true);
                        
                        var createResult = await _userManager.CreateAsync(newSupport, "CSupport@123*#");
                        if (createResult.Succeeded)
                        {
                            // Gán role "supporter"
                            await _userManager.AddToRoleAsync(newSupport, "supporter");
                            nextSupportNumber++;
                            supportMapping[phoneNumber] = newSupport.Id;
                            result.SupportsCreated++;
                        }
                        else
                        {
                            var errorText = $"Không thể tạo support {phoneNumber}: {string.Join(", ", createResult.Errors.Select(e => e.Description))}";
                            _logger.LogWarning(errorText);
                            result.Errors.Add(errorText);
                        }
                    }
                }

                await uow.CompleteAsync();
            }

            return supportMapping;
        }

        private async Task AssignSupportPermissionsAsync(List<ParsedRow> parsedRows, Dictionary<string, Guid> supportMapping, ImportCustomersResultDto result)
        {
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true))
            {
                // Tạo tập cặp (SupporterId, AdvertiserId) duy nhất từ dữ liệu hợp lệ
                var uniquePairs = new HashSet<(Guid SupporterId, string AdvertiserId)>();

                foreach (var row in parsedRows)
                {
                    if (string.IsNullOrWhiteSpace(row.SupportPhoneNumber) || string.IsNullOrWhiteSpace(row.AdvertiserId))
                    {
                        continue;
                    }

                    if (!supportMapping.TryGetValue(row.SupportPhoneNumber, out var supporterId))
                    {
                        continue;
                    }

                    var advertiserId = row.AdvertiserId.Trim();
                    if (string.IsNullOrWhiteSpace(advertiserId))
                    {
                        continue;
                    }

                    uniquePairs.Add((supporterId, advertiserId));
                }

                if (uniquePairs.Count == 0)
                {
                    await uow.CompleteAsync();
                    return;
                }

                // Kiểm tra tồn tại RawAdAccount theo danh sách AdvertiserId
                var distinctAdvertiserIds = uniquePairs.Select(p => p.AdvertiserId).Distinct(StringComparer.OrdinalIgnoreCase).ToList();
                var existingAdAccounts = await _adAccountRepository.GetListByAdvertiserIdsAsync(distinctAdvertiserIds, includeRemoved: false);
                var existingAdvertiserIdSet = new HashSet<string>(existingAdAccounts.Select(a => a.AdvertiserId?.Trim()).Where(x => !string.IsNullOrWhiteSpace(x))!, StringComparer.OrdinalIgnoreCase);

                // Lọc chỉ giữ các cặp có AdvertiserId tồn tại
                var filteredPairs = uniquePairs.Where(p => existingAdvertiserIdSet.Contains(p.AdvertiserId)).ToList();

                var skippedMissingCount = uniquePairs.Count - filteredPairs.Count;
                if (skippedMissingCount > 0)
                {
                    _logger.LogDebug("Bỏ qua {Count} phân quyền do không tìm thấy RawAdAccount tương ứng", skippedMissingCount);
                }

                // Gán quyền bằng Resource Provider với TikTokPermissions.AdAccounts.Default
                try
                {
                    var assignInputs = filteredPairs
                        .Select(p => new TikTok.ResourceProviders.AssignPermissionForResourceDto
                        {
                            UserId = p.SupporterId,
                            ResourceId = p.AdvertiserId,
                            Permissions = new List<string> { TikTokPermissions.AdAccounts.Default }
                        })
                        .ToList();

                    var assigned = await _adAccountResourceProvider.AssignPermission(assignInputs);
                    if (assigned)
                    {
                        result.PermissionsCreated += filteredPairs.Count;
                    }
                    else
                    {
                        var errorText = $"AssignPermission trả về false cho {filteredPairs.Count} cặp supporter-adAccount";
                        _logger.LogWarning(errorText);
                        result.Errors.Add(errorText);
                    }
                }
                catch (Exception ex)
                {
                    var errorText = $"Không thể gán quyền bằng provider cho {filteredPairs.Count} cặp supporter-adAccount: {ex.Message}";
                    _logger.LogWarning(ex, errorText);
                    result.Errors.Add(errorText);
                }

                await uow.CompleteAsync();
            }
        }
    }
}
