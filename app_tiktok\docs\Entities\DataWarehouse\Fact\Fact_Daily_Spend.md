# FactDailySpendEntity

## Thông tin bảng dữ liệu

-   **Table**: `FactDailySpends`
-   **TablePrefix**: `Fact_`
-   **TableName**: `Fact_FactDailySpends`
-   **DbSchema**: `TikTok`

## Mô tả

Fact table lưu trữ thông tin chi tiêu chi tiết của tài khoản quảng cáo theo thời gian. Dữ liệu được tổng hợp từ RawCostProfileEntity để phân tích xu hướng chi phí và các loại chi phí khác nhau (tiền mặt, tín dụng, thuế).

## Cấu trúc bảng

| Tên Field            | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                       |
| -------------------- | ------------ | -------- | ------ | ------------------------------------------- |
| Id                   | Guid         | ✓        | -      | ID duy nhất của bản ghi (Primary Key)       |
| DimDateId            | int          | ✓        | -      | Khóa ngoại liên kết với Dim_Date (YYYYMMDD) |
| DimAdAccountId       | Guid         | ✓        | -      | Khóa ngoại liên kết với Dim_AdAccount       |
| DimBusinessCenterId  | Guid         | ✓        | -      | Khóa ngoại liên kết với Dim_BusinessCenter  |
| AdvertiserId         | string       | ✓        | 50     | ID nhà quảng cáo (Business Key)             |
| AdvertiserName       | string       | ✓        | 200    | Tên nhà quảng cáo                           |
| BcId                 | string       | ✓        | 100    | ID Business Center (Business Key)           |
| TotalAmount          | decimal      | ✓        | 18,2   | Tổng số tiền chi phí cho nhà quảng cáo      |
| TotalAmountVND       | decimal      | ✓        | 18,2   | Tổng số tiền chi phí cho nhà quảng cáo (VND)|
| TotalAmountUSD       | decimal      | ✓        | 18,2   | Tổng số tiền chi phí cho nhà quảng cáo (USD)|
| Currency             | string       | ✓        | 10     | Tiền tệ theo mã ISO 4217                    |
| Date                 | DateTime     | ✓        | -      | Ngày chi tiêu (UTC)                         |
| CreationTime         | DateTime     | ✓        | -      | Thời gian tạo bản ghi (Audit)               |
| CreatorId            | Guid?        | ✗        | -      | ID người tạo (Audit)                        |
| LastModificationTime | DateTime?    | ✗        | -      | Thời gian sửa đổi cuối (Audit)              |
| LastModifierId       | Guid?        | ✗        | -      | ID người sửa đổi cuối (Audit)               |

## Grain Definition

-   **Mỗi dòng đại diện cho**: Chi tiêu của 1 tài khoản quảng cáo trong 1 ngày
-   **Unique Key**: (AdvertiserId, DateKey)

## Relationships

### Foreign Keys

-   **DimDateId** → DimDates.Id
-   **DimAdAccountId** → DimAdAccounts.Id
-   **DimBusinessCenterId** → DimBusinessCenters.Id

## Measures (Các chỉ số có thể tính toán)

### Measures cơ bản

-   **TotalAmount**: Tổng chi phí

### Measures tính toán

-   **CashRatio**: CashAmount / TotalAmount
-   **GrantRatio**: GrantAmount / TotalAmount
-   **TaxRatio**: TaxAmount / TotalAmount
-   **NetAmount**: TotalAmount - TaxAmount
-   **AvgDailySpend**: TotalAmount / Số ngày trong kỳ

## Indexes

### Primary Index

-   **PK_Fact_ChiTieu**: Id (Clustered)

### Foreign Key Indexes

-   **IX_Fact_ChiTieu_DimDateId**: DimDateId
-   **IX_Fact_ChiTieu_DimAdAccountId**: DimAdAccountId
-   **IX_Fact_ChiTieu_DimBusinessCenterId**: DimBusinessCenterId

### Composite Indexes

-   **IX_Fact_ChiTieu_AdvertiserId_Date**: (AdvertiserId, DimDateId) UNIQUE
-   **IX_Fact_ChiTieu_BcId_Date**: (BcId, DimDateId)

### Measure Indexes

-   **IX_Fact_ChiTieu_TotalAmount**: TotalAmount
-   **IX_Fact_ChiTieu_Date**: Date

## ETL Mapping

### Source → Target

-   **RawCostProfileEntity.AdvertiserId** → AdvertiserId
-   **RawCostProfileEntity.AdvertiserName** → AdvertiserName
-   **RawCostProfileEntity.BcId** → BcId
-   **RawCostProfileEntity.Amount** → TotalAmount
-   **RawCostProfileEntity.Currency** → Currency
-   **RawCostProfileEntity.Date** → Date

### Business Rules

1. Chỉ load dữ liệu có Date >= ngày hiện tại - 2 năm
2. Nếu TotalAmount < 0, chuyển thành 0
3. Currency được chuẩn hóa theo uppercase
4. DimDateId được tính từ Date theo format YYYYMMDD

## Validation Rules

-   **TotalAmount** = CashAmount + GrantAmount + TaxAmount
-   **Currency** phải thuộc danh sách được hỗ trợ (USD, EUR, VND, etc.)
-   **Date** không được lớn hơn ngày hiện tại
-   **AdvertiserId** và **BcId** phải tồn tại trong Dimension tables

## Business Questions được hỗ trợ

1.  **Chi tiêu theo ngày/tuần/tháng của từng tài khoản**

        ```sql

          SELECT SUM(TotalAmount) FROM FactDailySpends

    WHERE DimDateId BETWEEN 20250701 AND 20250731
    GROUP BY AdvertiserId

    ```

    ```

2.  **Tỷ lệ chi phí tiền mặt vs tín dụng**

    ```sql
    SELECT AdvertiserId,
           SUM(CashAmount)/SUM(TotalAmount) as CashRatio,
           SUM(GrantAmount)/SUM(TotalAmount) as GrantRatio
    FROM Fact_ChiTieu GROUP BY AdvertiserId
    ```

3.  **Top tài khoản chi tiêu nhiều nhất**
    ```sql
    SELECT TOP 10 AdvertiserName, SUM(TotalAmount) as TotalSpend
    FROM Fact_ChiTieu GROUP BY AdvertiserName
    ORDER BY TotalSpend DESC
    ```

## Performance Considerations

-   **Partitioning**: Partition theo DateKey (monthly)
-   **Compression**: Sử dụng Page compression
-   **Statistics**: Tự động update statistics cho các cột key
-   **Archive**: Archive dữ liệu cũ hơn 5 năm sang bảng history

## Data Quality Checks

-   **Completeness**: Đảm bảo không có gap trong dữ liệu theo ngày
-   **Accuracy**: TotalAmount = CashAmount + GrantAmount + TaxAmount
-   **Consistency**: Tổng chi tiêu Fact phải bằng tổng Raw data
-   **Timeliness**: Dữ liệu được cập nhật trong vòng 4 giờ sau khi sync từ API
