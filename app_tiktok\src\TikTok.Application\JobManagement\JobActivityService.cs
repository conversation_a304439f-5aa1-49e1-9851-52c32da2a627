using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TikTok.JobManagement
{
    /// <summary>
    /// Service implementation cho lưu trữ thời gian hoạt động của các job
    /// </summary>
    public class JobActivityService : IJobActivityService
    {
        private readonly ILogger<JobActivityService> _logger;
        private DateTime? _lastManagerJobRun;
        private DateTime? _lastRegisterJobRun;

        public JobActivityService(
            ILogger<JobActivityService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Cập nhật thời gian chạy cuối cùng của Manager Job
        /// </summary>
        /// <param name="lastRun">Thời gian chạy cuối cùng</param>
        public async Task UpdateLastManagerJobRunAsync(DateTime lastRun)
        {
            try
            {
                _lastManagerJobRun = lastRun;
                _logger.LogDebug("Updated last Manager Job run time: {LastRun}", lastRun);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update last Manager Job run time");
            }
        }

        /// <summary>
        /// Cập nhật thời gian chạy cuối cùng của Register Job
        /// </summary>
        /// <param name="lastRun">Thời gian chạy cuối cùng</param>
        public async Task UpdateLastRegisterJobRunAsync(DateTime lastRun)
        {
            try
            {
                _lastRegisterJobRun = lastRun;

                _logger.LogDebug("Updated last Register Job run time: {LastRun}", lastRun);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update last Register Job run time");
            }
        }

        /// <summary>
        /// Lấy thời gian chạy cuối cùng của Manager Job
        /// </summary>
        /// <returns>Thời gian chạy cuối cùng</returns>
        public async Task<DateTime?> GetLastManagerJobRunAsync()
        {
            try
            {
                return _lastManagerJobRun;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get last Manager Job run time");
                return null;
            }
        }

        /// <summary>
        /// Lấy thời gian chạy cuối cùng của Register Job
        /// </summary>
        /// <returns>Thời gian chạy cuối cùng</returns>
        public async Task<DateTime?> GetLastRegisterJobRunAsync()
        {
            try
            {
                return _lastRegisterJobRun;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get last Register Job run time");
                return null;
            }
        }
    }
}