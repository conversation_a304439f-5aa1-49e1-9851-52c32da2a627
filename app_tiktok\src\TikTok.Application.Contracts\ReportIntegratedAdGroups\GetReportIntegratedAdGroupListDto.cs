using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TikTok.ReportIntegratedAdGroups
{
    /// <summary>
    /// DTO cho việc lấy danh sách báo cáo tích hợp AdGroup
    /// </summary>
    public class GetReportIntegratedAdGroupListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// ID tài khoản nhà quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// ID chiến dịch
        /// </summary>
        [StringLength(100)]
        public string? CampaignId { get; set; }

        /// <summary>
        /// ID nhóm quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? AdGroupId { get; set; }

        /// <summary>
        /// Tên nhóm quảng cáo
        /// </summary>
        [StringLength(255)]
        public string? AdGroupName { get; set; }

        /// <summary>
        /// Ng<PERSON>y bắt đầu
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Ngày kết thúc
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Bộ lọc tìm kiếm
        /// </summary>
        [StringLength(500)]
        public string? Filter { get; set; }
    }
} 