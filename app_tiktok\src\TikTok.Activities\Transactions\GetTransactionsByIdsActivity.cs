using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Enums;
using TikTok.Transactions;

namespace TikTok.Activities.Transactions
{
    [Activity(Category = "Giao dịch", DisplayName = "Lấy danh sách giao dịch theo Transaction IDs", Outcomes = new[] { OutcomeNames.Done })]
    public class GetTransactionsByIdsActivity : UnitOfWorkActivity
    {
        private readonly ITransactionQueryService _transactionQueryService;

        public GetTransactionsByIdsActivity(ITransactionQueryService transactionQueryService)
        {
            _transactionQueryService = transactionQueryService;
        }

        [ActivityInput(Hint = "Danh sách Transaction IDs", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public List<string> TransactionIds { get; set; } = new List<string>();

        [ActivityInput(Hint = "Cấp độ giao dịch (tùy chọn)", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public TransactionLevel? TransactionLevel { get; set; }

        [ActivityOutput(Hint = "Danh sách giao dịch")]
        public List<TransactionDto> Output { get; set; } = new List<TransactionDto>();

        [ActivityOutput(Hint = "Danh sách Id tài khoản quảng cáo")]
        public List<string> AdvertiserIds { get; set; } = new List<string>();


        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            if (TransactionIds == null || TransactionIds.Count == 0)
            {
                throw new System.InvalidOperationException("TransactionIds không được để trống.");
            }

            // Gọi service để lấy danh sách giao dịch
            Output = await _transactionQueryService.GetTransactionsByIdsAsync(TransactionIds, TransactionLevel);

            AdvertiserIds = Output.Where(x => !string.IsNullOrEmpty(x.AccountId )).Select(x => x.AccountId).Distinct().ToList();
            return Done();
        }
    }
}
