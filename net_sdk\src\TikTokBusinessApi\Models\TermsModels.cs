/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Get Terms Models

    /// <summary>
    /// Request for getting terms
    /// </summary>
    public class TermGetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The language of the agreement. Enum values: EN, JA, ZH. Default value: EN.
        /// </summary>
        public string? Lang { get; set; }

        /// <summary>
        /// Type of agreement. Enum values: LeadAds.
        /// </summary>
        [Required]
        public string TermType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting terms
    /// </summary>
    public class TermGetResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public TermGetData? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string? RequestId { get; set; }
    }

    /// <summary>
    /// Data for get terms response
    /// </summary>
    public class TermGetData
    {
        /// <summary>
        /// The content of the agreement
        /// </summary>
        [JsonPropertyName("terms")]
        public List<string>? Terms { get; set; }
    }

    #endregion

    #region Sign Terms Models

    /// <summary>
    /// Request for signing terms
    /// </summary>
    public class TermConfirmRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Agreement type. Enum values: LeadAds.
        /// </summary>
        [Required]
        [JsonPropertyName("term_type")]
        public string TermType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for signing terms
    /// </summary>
    public class TermConfirmResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Response data (empty for this endpoint)
        /// </summary>
        [JsonPropertyName("data")]
        public object? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string? RequestId { get; set; }
    }

    #endregion

    #region Check Terms Status Models

    /// <summary>
    /// Request for checking terms status
    /// </summary>
    public class TermCheckRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Type of agreement. Enum values: LeadAds.
        /// </summary>
        [Required]
        public string TermType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for checking terms status
    /// </summary>
    public class TermCheckResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public TermCheckData? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string? RequestId { get; set; }
    }

    /// <summary>
    /// Data for check terms status response
    /// </summary>
    public class TermCheckData
    {
        /// <summary>
        /// Signing status of the agreement. True means "Signed successfully", False means "Not signed".
        /// </summary>
        [JsonPropertyName("confirmed")]
        public bool Confirmed { get; set; }
    }

    #endregion
}
