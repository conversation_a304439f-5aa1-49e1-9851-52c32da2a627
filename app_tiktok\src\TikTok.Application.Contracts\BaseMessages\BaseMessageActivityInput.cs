﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.AdAccounts;
using TikTok.BusinessCenters;

namespace TikTok.BaseMessages
{
    public class BaseMessageActivityInput
    {
        public string BcId { get; set; }
        public string AdAccountId { get; set; }
        public string? Phonenumber { get; set; }
        public string? Name { get; set; }
        public string? TextContent { get; set; }

        /// <summary>
        /// Attachment URLs
        /// </summary>
        public List<string>? Attachments { get; set; }
        /// <summary>
        /// Zalo Owner Id
        /// </summary>
        public string? OwnerId { get; set; }
    }
}
