/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Change Log endpoints
    /// </summary>
    public static class ChangeLogEndpoints
    {
        /// <summary>
        /// Create a change log download task endpoint (v1.3)
        /// </summary>
        public const string CreateTask = "/changelog/task/create/";

        /// <summary>
        /// Check the status of a download task endpoint (v1.3)
        /// </summary>
        public const string CheckTaskStatus = "/changelog/task/check/";

        /// <summary>
        /// Get the downloaded file endpoint (v1.3)
        /// </summary>
        public const string DownloadTask = "/changelog/task/download/";
    }
}
