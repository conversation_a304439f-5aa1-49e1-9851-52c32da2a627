/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the Catalogs API
    /// </summary>
    public class CatalogsApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public CatalogsApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example: Create a new catalog
        /// </summary>
        public async Task CreateCatalogExample()
        {
            try
            {
                Console.WriteLine("=== Create Catalog Example ===");

                var catalogCreateBody = new CatalogCreateBody
                {
                    BcId = "your-business-center-id",
                    Name = "My E-commerce Catalog",
                    CatalogType = "ECOM",
                    CatalogConf = new CatalogConfiguration
                    {
                        RegionCode = "US",
                        Currency = "USD",
                        Channel = "CLIENT"
                    }
                };

                var response = await _client.Catalogs.CreateCatalogAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    catalogCreateBody);

                Console.WriteLine($"Catalog created successfully!");
                Console.WriteLine($"Catalog ID: {response.Data?.CatalogId}");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get all catalogs under a Business Center
        /// </summary>
        public async Task GetCatalogsExample()
        {
            try
            {
                Console.WriteLine("=== Get Catalogs Example ===");

                var queryParams = new Dictionary<string, string>
                {
                    ["bc_id"] = "your-business-center-id",
                    ["page"] = "1",
                    ["page_size"] = "10"
                };

                var response = await _client.Catalogs.GetCatalogsAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    queryParams);

                Console.WriteLine($"Retrieved {response.Data?.List?.Count ?? 0} catalogs");
                
                if (response.Data?.List != null)
                {
                    foreach (var catalog in response.Data.List)
                    {
                        Console.WriteLine($"- Catalog ID: {catalog.CatalogId}");
                        Console.WriteLine($"  Name: {catalog.CatalogName}");
                        Console.WriteLine($"  Type: {catalog.CatalogType}");
                        Console.WriteLine($"  Ad Creation Eligible: {catalog.AdCreationEligible}");
                        Console.WriteLine($"  Created: {catalog.CreateTime}");
                        Console.WriteLine();
                    }
                }

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Update a catalog name
        /// </summary>
        public async Task UpdateCatalogExample()
        {
            try
            {
                Console.WriteLine("=== Update Catalog Example ===");

                var catalogUpdateBody = new CatalogUpdateBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    Name = "Updated Catalog Name"
                };

                var response = await _client.Catalogs.UpdateCatalogAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    catalogUpdateBody);

                Console.WriteLine($"Catalog updated successfully!");
                Console.WriteLine($"Catalog ID: {response.Data?.CatalogId}");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get catalog lexicon
        /// </summary>
        public async Task GetCatalogLexiconExample()
        {
            try
            {
                Console.WriteLine("=== Get Catalog Lexicon Example ===");

                var queryParams = new Dictionary<string, string>
                {
                    ["bc_id"] = "your-business-center-id",
                    ["catalog_id"] = "your-catalog-id"
                };

                var response = await _client.Catalogs.GetCatalogLexiconAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    queryParams);

                Console.WriteLine($"Retrieved {response.Data?.List?.Count ?? 0} lexicon items");
                
                if (response.Data?.List != null)
                {
                    foreach (var lexicon in response.Data.List)
                    {
                        Console.WriteLine($"- Lexicon ID: {lexicon.LexiconId}");
                        Console.WriteLine($"  Name: {lexicon.Name}");
                        Console.WriteLine($"  Type: {lexicon.Type}");
                        Console.WriteLine($"  Metadata: {lexicon.Metadata}");
                        Console.WriteLine($"  Default Value: {lexicon.DefaultValue}");
                        Console.WriteLine();
                    }
                }

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get available countries/regions
        /// </summary>
        public async Task GetAvailableCountriesExample()
        {
            try
            {
                Console.WriteLine("=== Get Available Countries Example ===");

                var queryParams = new Dictionary<string, string>
                {
                    ["bc_id"] = "your-business-center-id"
                };

                var response = await _client.Catalogs.GetAvailableCountriesAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    queryParams);

                Console.WriteLine($"Available regions: {response.Data?.RegionCodes?.Count ?? 0}");
                
                if (response.Data?.RegionCodes != null)
                {
                    Console.WriteLine($"Region codes: {string.Join(", ", response.Data.RegionCodes)}");
                }

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get location and currency information
        /// </summary>
        public async Task GetLocationCurrencyExample()
        {
            try
            {
                Console.WriteLine("=== Get Location Currency Example ===");

                var response = await _client.Catalogs.GetLocationCurrencyAsync(
                    _client.ApiClient.Configuration.AccessToken);

                Console.WriteLine($"Retrieved {response.Data?.List?.Count ?? 0} location-currency mappings");
                
                if (response.Data?.List != null)
                {
                    foreach (var location in response.Data.List.Take(5)) // Show first 5 for brevity
                    {
                        Console.WriteLine($"- Location: {location.Location}");
                        Console.WriteLine($"  Currencies: {string.Join(", ", location.Currency ?? new List<string>())}");
                        Console.WriteLine();
                    }
                    
                    if (response.Data.List.Count > 5)
                    {
                        Console.WriteLine($"... and {response.Data.List.Count - 5} more locations");
                    }
                }

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get catalog overview
        /// </summary>
        public async Task GetCatalogOverviewExample()
        {
            try
            {
                Console.WriteLine("=== Get Catalog Overview Example ===");

                var queryParams = new Dictionary<string, string>
                {
                    ["bc_id"] = "your-business-center-id",
                    ["catalog_id"] = "your-catalog-id"
                };

                var response = await _client.Catalogs.GetCatalogOverviewAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    queryParams);

                Console.WriteLine($"Catalog Overview:");
                Console.WriteLine($"- Approved products: {response.Data?.Approved ?? 0}");
                Console.WriteLine($"- Rejected products: {response.Data?.Rejected ?? 0}");
                Console.WriteLine($"- Processing products: {response.Data?.Processing ?? 0}");
                Console.WriteLine($"- Organic approved: {response.Data?.OrganicApproved ?? 0}");
                Console.WriteLine($"- Organic rejected: {response.Data?.OrganicRejected ?? 0}");
                Console.WriteLine($"- Organic processing: {response.Data?.OrganicProcessing ?? 0}");

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Bind an event source to a catalog
        /// </summary>
        public async Task BindEventSourceExample()
        {
            try
            {
                Console.WriteLine("=== Bind Event Source Example ===");

                var bindBody = new CatalogEventSourceBindBody
                {
                    AdvertiserId = "your-advertiser-id",
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    AppId = "your-app-id", // Optional: Mobile app ID
                    PixelCode = "your-pixel-code" // Optional: Website pixel code
                };

                var response = await _client.Catalogs.BindEventSourceAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    bindBody);

                Console.WriteLine($"Event source bound successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Unbind an event source from a catalog
        /// </summary>
        public async Task UnbindEventSourceExample()
        {
            try
            {
                Console.WriteLine("=== Unbind Event Source Example ===");

                var unbindBody = new CatalogEventSourceUnbindBody
                {
                    AdvertiserId = "your-advertiser-id",
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    AppId = "your-app-id", // Optional: Mobile app ID to unbind
                    PixelCode = "your-pixel-code" // Optional: Website pixel code to unbind
                };

                var response = await _client.Catalogs.UnbindEventSourceAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    unbindBody);

                Console.WriteLine($"Event source unbound successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get event source binding information
        /// </summary>
        public async Task GetEventSourceBindingExample()
        {
            try
            {
                Console.WriteLine("=== Get Event Source Binding Example ===");

                var queryParams = new Dictionary<string, string>
                {
                    ["bc_id"] = "your-business-center-id",
                    ["catalog_id"] = "your-catalog-id"
                };

                var response = await _client.Catalogs.GetEventSourceBindingAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    queryParams);

                Console.WriteLine($"Event Source Binding Information:");
                Console.WriteLine($"- Catalog ID: {response.Data?.CatalogId}");
                Console.WriteLine($"- Number of event sources: {response.Data?.EventSources?.Count ?? 0}");

                if (response.Data?.EventSources != null)
                {
                    foreach (var eventSource in response.Data.EventSources)
                    {
                        Console.WriteLine($"  Event Source:");
                        Console.WriteLine($"    - Name: {eventSource.EventSourceName}");
                        if (!string.IsNullOrEmpty(eventSource.AppId))
                            Console.WriteLine($"    - App ID: {eventSource.AppId}");
                        if (!string.IsNullOrEmpty(eventSource.PixelCode))
                            Console.WriteLine($"    - Pixel Code: {eventSource.PixelCode}");
                        Console.WriteLine();
                    }
                }

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        #region Feed Examples

        /// <summary>
        /// Example: Create a new feed
        /// </summary>
        public async Task CreateFeedExample()
        {
            try
            {
                Console.WriteLine("=== Create Feed Example ===");

                var feedCreateBody = new FeedCreateBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    FeedName = "My Product Feed",
                    UpdateMode = "INCREMENTAL",
                    ScheduleParam = new FeedScheduleParam
                    {
                        Source = new FeedSource
                        {
                            Uri = "https://example.com/product-feed.csv",
                            Username = "feed_user", // Optional: if feed is password protected
                            Password = "feed_password" // Optional: if feed is password protected
                        },
                        IntervalType = "DAILY",
                        IntervalCount = 1,
                        Timezone = "America/New_York",
                        Hour = 2, // 2 AM
                        Minute = 0
                    }
                };

                var response = await _client.Catalogs.CreateFeedAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    feedCreateBody);

                Console.WriteLine($"Feed created successfully!");
                Console.WriteLine($"Feed ID: {response.Data?.FeedId}");
                Console.WriteLine($"Feed Name: {response.Data?.FeedName}");
                Console.WriteLine($"Status: {response.Data?.Status}");
                Console.WriteLine($"Next Update Time: {response.Data?.NextUpdateTime}");
                Console.WriteLine($"Number of Products: {response.Data?.NumberOfProducts}");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get feeds for a catalog
        /// </summary>
        public async Task GetFeedsExample()
        {
            try
            {
                Console.WriteLine("=== Get Feeds Example ===");

                var queryParams = new Dictionary<string, string>
                {
                    ["bc_id"] = "your-business-center-id",
                    ["catalog_id"] = "your-catalog-id"
                    // Optional: ["feed_id"] = "specific-feed-id" to get a specific feed
                };

                var response = await _client.Catalogs.GetFeedsAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    queryParams);

                Console.WriteLine($"Retrieved {response.Data?.FeedList?.Count ?? 0} feeds");

                if (response.Data?.FeedList != null)
                {
                    foreach (var feed in response.Data.FeedList)
                    {
                        Console.WriteLine($"  Feed ID: {feed.FeedId}");
                        Console.WriteLine($"  Feed Name: {feed.FeedName}");
                        Console.WriteLine($"  Status: {feed.Status}");
                        Console.WriteLine($"  Number of Products: {feed.NumberOfProducts}");
                        Console.WriteLine($"  Next Update Time: {feed.NextUpdateTime}");

                        if (feed.LastUpdateParam != null)
                        {
                            Console.WriteLine($"  Last Update Mode: {feed.LastUpdateParam.UpdateMode}");
                            Console.WriteLine($"  Feed URI: {feed.LastUpdateParam.Uri}");
                            Console.WriteLine($"  Interval Type: {feed.LastUpdateParam.IntervalType}");
                            Console.WriteLine($"  Timezone: {feed.LastUpdateParam.Timezone}");
                        }
                        Console.WriteLine();
                    }
                }

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Update a feed
        /// </summary>
        public async Task UpdateFeedExample()
        {
            try
            {
                Console.WriteLine("=== Update Feed Example ===");

                var feedUpdateBody = new FeedUpdateBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    FeedId = "your-feed-id",
                    UpdateMode = "SUPPLEMENT", // Can be OVERWRITE, INCREMENTAL, or SUPPLEMENT
                    ScheduleParam = new FeedScheduleParam
                    {
                        Source = new FeedSource
                        {
                            Uri = "https://example.com/updated-product-feed.csv"
                        },
                        IntervalType = "HOURLY",
                        IntervalCount = 6, // Every 6 hours
                        Timezone = "UTC",
                        Hour = 0,
                        Minute = 30
                    }
                };

                var response = await _client.Catalogs.UpdateFeedAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    feedUpdateBody);

                Console.WriteLine($"Feed updated successfully!");
                Console.WriteLine($"Feed ID: {response.Data?.FeedId}");
                Console.WriteLine($"Feed Name: {response.Data?.FeedName}");
                Console.WriteLine($"Status: {response.Data?.Status}");
                Console.WriteLine($"Next Update Time: {response.Data?.NextUpdateTime}");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Delete a feed
        /// </summary>
        public async Task DeleteFeedExample()
        {
            try
            {
                Console.WriteLine("=== Delete Feed Example ===");

                var feedDeleteBody = new FeedDeleteBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    FeedId = "your-feed-id"
                };

                var response = await _client.Catalogs.DeleteFeedAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    feedDeleteBody);

                Console.WriteLine($"Feed deleted successfully!");
                Console.WriteLine($"Deleted Feed ID: {response.Data?.FeedId}");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get feed logs
        /// </summary>
        public async Task GetFeedLogsExample()
        {
            try
            {
                Console.WriteLine("=== Get Feed Logs Example ===");

                var queryParams = new Dictionary<string, string>
                {
                    ["bc_id"] = "your-business-center-id",
                    ["catalog_id"] = "your-catalog-id",
                    ["feed_id"] = "your-feed-id"
                };

                var response = await _client.Catalogs.GetFeedLogsAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    queryParams);

                Console.WriteLine($"Retrieved {response.Data?.FeedLogs?.Count ?? 0} log entries");

                if (response.Data?.FeedLogs != null)
                {
                    foreach (var logEntry in response.Data.FeedLogs)
                    {
                        Console.WriteLine($"  Update Status:");
                        if (logEntry.LastUpdateStatus != null)
                        {
                            Console.WriteLine($"    Process Status: {logEntry.LastUpdateStatus.ProcessStatus}");
                            Console.WriteLine($"    Added: {logEntry.LastUpdateStatus.AddCount}");
                            Console.WriteLine($"    Updated: {logEntry.LastUpdateStatus.UpdateCount}");
                            Console.WriteLine($"    Removed: {logEntry.LastUpdateStatus.RemoveCount}");
                            Console.WriteLine($"    Errors: {logEntry.LastUpdateStatus.ErrorCount}");
                            Console.WriteLine($"    Warnings: {logEntry.LastUpdateStatus.WarnCount}");
                        }

                        Console.WriteLine($"  Update Time:");
                        if (logEntry.LastUpdateTime != null)
                        {
                            Console.WriteLine($"    Start Time: {logEntry.LastUpdateTime.StartTime}");
                            Console.WriteLine($"    End Time: {logEntry.LastUpdateTime.EndTime}");
                        }
                        Console.WriteLine();
                    }
                }

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Update feed schedule status
        /// </summary>
        public async Task UpdateFeedScheduleStatusExample()
        {
            try
            {
                Console.WriteLine("=== Update Feed Schedule Status Example ===");

                var feedScheduleUpdateBody = new FeedScheduleUpdateBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    FeedId = "your-feed-id",
                    Status = "OFF" // Can be "ON" or "OFF"
                };

                var response = await _client.Catalogs.UpdateFeedScheduleStatusAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    feedScheduleUpdateBody);

                Console.WriteLine($"Feed schedule status updated successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        #endregion

        /// <summary>
        /// Run all examples
        /// </summary>
        public async Task RunAllExamples()
        {
            await CreateCatalogExample();
            Console.WriteLine(new string('-', 50));

            await GetCatalogsExample();
            Console.WriteLine(new string('-', 50));

            await UpdateCatalogExample();
            Console.WriteLine(new string('-', 50));

            await GetCatalogLexiconExample();
            Console.WriteLine(new string('-', 50));

            await GetAvailableCountriesExample();
            Console.WriteLine(new string('-', 50));

            await GetLocationCurrencyExample();
            Console.WriteLine(new string('-', 50));

            await GetCatalogOverviewExample();
            Console.WriteLine(new string('-', 50));

            await BindEventSourceExample();
            Console.WriteLine(new string('-', 50));

            await UnbindEventSourceExample();
            Console.WriteLine(new string('-', 50));

            await GetEventSourceBindingExample();
            Console.WriteLine(new string('-', 50));

            // Feed Examples
            await CreateFeedExample();
            Console.WriteLine(new string('-', 50));

            await GetFeedsExample();
            Console.WriteLine(new string('-', 50));

            await UpdateFeedExample();
            Console.WriteLine(new string('-', 50));

            await GetFeedLogsExample();
            Console.WriteLine(new string('-', 50));

            await UpdateFeedScheduleStatusExample();
            Console.WriteLine(new string('-', 50));

            await DeleteFeedExample();
            Console.WriteLine(new string('-', 50));

            // Product Management Examples
            await UploadProductsViaFileExample();
            Console.WriteLine(new string('-', 50));

            await UploadProductsExample();
            Console.WriteLine(new string('-', 50));

            await UpdateProductsExample();
            Console.WriteLine(new string('-', 50));

            await GetProductsExample();
            Console.WriteLine(new string('-', 50));

            await GetProductLogExample();
            Console.WriteLine(new string('-', 50));

            await DeleteProductsExample();
            Console.WriteLine(new string('-', 50));

            // Product Set Management Examples
            await CreateProductSetExample();
            Console.WriteLine(new string('-', 50));

            await GetProductSetsExample();
            Console.WriteLine(new string('-', 50));

            await GetProductSetProductsExample();
            Console.WriteLine(new string('-', 50));

            await UpdateProductSetExample();
            Console.WriteLine(new string('-', 50));

            await DeleteProductSetsExample();
        }

        #region Product Management Examples

        /// <summary>
        /// Example: Upload products via file URL
        /// </summary>
        public async Task UploadProductsViaFileExample()
        {
            try
            {
                Console.WriteLine("=== Upload Products Via File Example ===");

                var uploadBody = new ProductFileUploadBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    FileUrl = "https://example.com/products.csv",
                    FeedId = "your-feed-id", // Optional
                    UpdateMode = "INCREMENTAL" // OVERWRITE or INCREMENTAL
                };

                var response = await _client.Catalogs.UploadProductsViaFileAsync(
                    _client.Configuration.AccessToken,
                    uploadBody);

                if (response.Data != null)
                {
                    Console.WriteLine($"✓ Products upload initiated successfully!");
                    Console.WriteLine($"  Feed Log ID: {response.Data.FeedLogId}");
                    Console.WriteLine($"  Request ID: {response.RequestId}");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"✗ SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Upload products via JSON schema
        /// </summary>
        public async Task UploadProductsExample()
        {
            try
            {
                Console.WriteLine("=== Upload Products Via JSON Example ===");

                // Example E-commerce products
                var products = new List<object>
                {
                    new
                    {
                        sku_id = "SKU001",
                        title = "Premium Wireless Headphones",
                        description = "High-quality wireless headphones with noise cancellation",
                        availability = "IN_STOCK",
                        image_url = "https://example.com/headphones.jpg",
                        brand = "TechBrand",
                        price = new { current_price = "199.99", currency = "USD" },
                        landing_page = new { url = "https://example.com/headphones" },
                        google_product_category = "Electronics > Audio > Headphones"
                    },
                    new
                    {
                        sku_id = "SKU002",
                        title = "Smart Fitness Watch",
                        description = "Advanced fitness tracking with heart rate monitor",
                        availability = "IN_STOCK",
                        image_url = "https://example.com/watch.jpg",
                        brand = "FitTech",
                        price = new { current_price = "299.99", currency = "USD" },
                        landing_page = new { url = "https://example.com/watch" },
                        google_product_category = "Electronics > Wearables > Smart Watches"
                    }
                };

                var uploadBody = new ProductUploadBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    Products = products,
                    FeedId = "your-feed-id" // Optional
                };

                var response = await _client.Catalogs.UploadProductsAsync(
                    _client.Configuration.AccessToken,
                    uploadBody);

                if (response.Data != null)
                {
                    Console.WriteLine($"✓ {products.Count} products uploaded successfully!");
                    Console.WriteLine($"  Feed Log ID: {response.Data.FeedLogId}");
                    Console.WriteLine($"  Request ID: {response.RequestId}");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"✗ SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Update existing products
        /// </summary>
        public async Task UpdateProductsExample()
        {
            try
            {
                Console.WriteLine("=== Update Products Example ===");

                // Example product updates
                var products = new List<object>
                {
                    new
                    {
                        sku_id = "SKU001",
                        title = "Premium Wireless Headphones - Updated",
                        price = new { current_price = "179.99", currency = "USD" },
                        availability = "IN_STOCK"
                    },
                    new
                    {
                        sku_id = "SKU002",
                        price = new { current_price = "279.99", currency = "USD" },
                        availability = "LIMITED_STOCK"
                    }
                };

                var updateBody = new ProductUpdateBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    Products = products,
                    FeedId = "your-feed-id" // Optional
                };

                var response = await _client.Catalogs.UpdateProductsAsync(
                    _client.Configuration.AccessToken,
                    updateBody);

                if (response.Data != null)
                {
                    Console.WriteLine($"✓ {products.Count} products updated successfully!");
                    Console.WriteLine($"  Feed Log ID: {response.Data.FeedLogId}");
                    Console.WriteLine($"  Request ID: {response.RequestId}");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"✗ SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get products from catalog
        /// </summary>
        public async Task GetProductsExample()
        {
            try
            {
                Console.WriteLine("=== Get Products Example ===");

                var getRequest = new ProductGetRequest
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    Page = 1,
                    PageSize = 10,
                    // Optional filters
                    SkuIds = new List<string> { "SKU001", "SKU002" },
                    Conditions = new ProductFilterConditions
                    {
                        And = new List<ProductFilterCondition>
                        {
                            new ProductFilterCondition
                            {
                                Field = "availability",
                                Operator = "EQ",
                                Values = new List<string> { "IN_STOCK" }
                            }
                        }
                    }
                };

                var response = await _client.Catalogs.GetProductsAsync(
                    _client.Configuration.AccessToken,
                    getRequest);

                if (response.Data?.List != null)
                {
                    Console.WriteLine($"✓ Retrieved {response.Data.List.Count} products");

                    foreach (var product in response.Data.List.Take(3)) // Show first 3
                    {
                        Console.WriteLine($"  - SKU: {product.SkuId}");
                        Console.WriteLine($"    Title: {product.Title}");
                        Console.WriteLine($"    Availability: {product.Availability}");
                        Console.WriteLine($"    Audit Status: {product.Audit?.AuditStatus}");
                        Console.WriteLine();
                    }

                    if (response.Data.PageInfo != null)
                    {
                        Console.WriteLine($"  Page Info: {response.Data.PageInfo.Page}/{response.Data.PageInfo.TotalPage}");
                        Console.WriteLine($"  Total Count: {response.Data.PageInfo.TotalNumber}");
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"✗ SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get product handling log
        /// </summary>
        public async Task GetProductLogExample()
        {
            try
            {
                Console.WriteLine("=== Get Product Log Example ===");

                var logRequest = new ProductLogRequest
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    FeedLogId = "your-feed-log-id", // From upload/update response
                    Language = "en" // Optional
                };

                var response = await _client.Catalogs.GetProductLogAsync(
                    _client.Configuration.AccessToken,
                    logRequest);

                if (response.Data != null)
                {
                    Console.WriteLine($"✓ Product log retrieved successfully");
                    Console.WriteLine($"  Status: {response.Data.Status}");
                    Console.WriteLine($"  Total Products: {response.Data.TotalCount}");

                    if (response.Data.LastUpdateStatus != null)
                    {
                        var status = response.Data.LastUpdateStatus;
                        Console.WriteLine($"  Last Update:");
                        Console.WriteLine($"    Process Status: {status.ProcessStatus}");
                        Console.WriteLine($"    Added: {status.AddCount}");
                        Console.WriteLine($"    Updated: {status.UpdateCount}");
                        Console.WriteLine($"    Removed: {status.RemoveCount}");
                        Console.WriteLine($"    Errors: {status.ErrorCount}");
                        Console.WriteLine($"    Warnings: {status.WarnCount}");
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"✗ SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Delete products from catalog
        /// </summary>
        public async Task DeleteProductsExample()
        {
            try
            {
                Console.WriteLine("=== Delete Products Example ===");

                var deleteBody = new ProductDeleteBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    FeedId = "your-feed-id", // Optional
                    // For E-commerce catalogs
                    SkuIds = new List<string> { "SKU001", "SKU002" },
                    // For Hotel catalogs (use one type at a time)
                    // HotelIds = new List<string> { "HOTEL001", "HOTEL002" },
                    // For Flight catalogs
                    // FlightIds = new List<string> { "FLIGHT001", "FLIGHT002" },
                    // For Destination catalogs
                    // DestinationIds = new List<string> { "DEST001", "DEST002" },
                    // For Auto catalogs
                    // VehicleIds = new List<string> { "VEH001", "VEH002" }
                };

                var response = await _client.Catalogs.DeleteProductsAsync(
                    _client.Configuration.AccessToken,
                    deleteBody);

                if (response.Data != null)
                {
                    Console.WriteLine($"✓ Products deletion initiated successfully!");
                    Console.WriteLine($"  Feed Log ID: {response.Data.FeedLogId}");
                    Console.WriteLine($"  Request ID: {response.RequestId}");
                    Console.WriteLine("  Note: Use GetProductLogAsync to check deletion status");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"✗ SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Error: {ex.Message}");
            }
        }

        #endregion

        #region Product Set Management Examples

        /// <summary>
        /// Example: Create a product set
        /// </summary>
        public async Task CreateProductSetExample()
        {
            try
            {
                Console.WriteLine("=== Create Product Set Example ===");

                var conditions = new ProductSetConditions
                {
                    And = new List<ProductSetCondition>
                    {
                        new ProductSetCondition("price", "LT", "250"),
                        new ProductSetCondition("brand", "I_CONTAIN", new List<string> { "TikTok", "Example" })
                    }
                };

                var createBody = new ProductSetCreateBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    ProductSetName = "Affordable Products",
                    Conditions = conditions
                };

                var response = await _client.Catalogs.CreateProductSetAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    createBody);

                Console.WriteLine($"Product set created successfully!");
                Console.WriteLine($"Product Set ID: {response.Data?.ProductSetId}");
                Console.WriteLine($"Product Set Name: {response.Data?.ProductSetName}");
                Console.WriteLine($"Product Count: {response.Data?.ProductCount}");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get product sets
        /// </summary>
        public async Task GetProductSetsExample()
        {
            try
            {
                Console.WriteLine("=== Get Product Sets Example ===");

                var request = new ProductSetGetRequest
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    // ProductSetId = "specific-product-set-id", // Optional: get specific product set
                    ReturnProductCount = true
                };

                var response = await _client.Catalogs.GetProductSetsAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    request);

                Console.WriteLine($"Retrieved {response.Data?.List.Count} product sets");

                if (response.Data?.List != null)
                {
                    foreach (var productSet in response.Data.List)
                    {
                        Console.WriteLine($"- Product Set ID: {productSet.ProductSetId}");
                        Console.WriteLine($"  Name: {productSet.ProductSetName}");
                        Console.WriteLine($"  Product Count: {productSet.ProductCount}");
                        Console.WriteLine($"  Catalog ID: {productSet.CatalogId}");
                    }
                }

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get products in a product set
        /// </summary>
        public async Task GetProductSetProductsExample()
        {
            try
            {
                Console.WriteLine("=== Get Product Set Products Example ===");

                var request = new ProductSetProductGetRequest
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    ProductSetId = "your-product-set-id",
                    Page = 1,
                    PageSize = 20
                };

                var response = await _client.Catalogs.GetProductSetProductsAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    request);

                Console.WriteLine($"Product Set ID: {response.Data?.ProductSetId}");
                Console.WriteLine($"Total Products: {response.Data?.ProductCount}");
                Console.WriteLine($"Retrieved {response.Data?.Products.Count} products on this page");

                if (response.Data?.Products != null)
                {
                    foreach (var product in response.Data.Products)
                    {
                        Console.WriteLine($"- Product ID: {product.ProductId}");
                        Console.WriteLine($"  Name: {product.ProductName}");
                        if (!string.IsNullOrEmpty(product.SkuId))
                            Console.WriteLine($"  SKU ID: {product.SkuId}");
                    }
                }

                if (response.Data?.PageInfo != null)
                {
                    Console.WriteLine($"Page: {response.Data.PageInfo.Page}/{response.Data.PageInfo.TotalPage}");
                    Console.WriteLine($"Total Results: {response.Data.PageInfo.TotalNumber}");
                }

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Update a product set
        /// </summary>
        public async Task UpdateProductSetExample()
        {
            try
            {
                Console.WriteLine("=== Update Product Set Example ===");

                var conditions = new ProductSetConditions
                {
                    Or = new List<ProductSetCondition>
                    {
                        new ProductSetCondition("price", "LT", "200"),
                        new ProductSetCondition("sale_price_effective_start_date", "GTE", "2021-03-04 12:00:00")
                    }
                };

                var updateBody = new ProductSetUpdateBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    ProductSetId = "your-product-set-id",
                    ProductSetName = "Updated Product Set Name", // Optional
                    Conditions = conditions // Optional
                };

                var response = await _client.Catalogs.UpdateProductSetAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    updateBody);

                Console.WriteLine($"Product set updated successfully!");
                Console.WriteLine($"Product Set ID: {response.Data?.ProductSetId}");
                Console.WriteLine($"Product Set Name: {response.Data?.ProductSetName}");
                Console.WriteLine($"Product Count: {response.Data?.ProductCount}");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Delete product sets
        /// </summary>
        public async Task DeleteProductSetsExample()
        {
            try
            {
                Console.WriteLine("=== Delete Product Sets Example ===");

                var deleteBody = new ProductSetDeleteBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    ProductSetIds = new List<string>
                    {
                        "product-set-id-1",
                        "product-set-id-2",
                        "product-set-id-3"
                    }
                };

                var response = await _client.Catalogs.DeleteProductSetsAsync(
                    _client.ApiClient.Configuration.AccessToken,
                    deleteBody);

                Console.WriteLine($"Product sets deleted successfully!");
                Console.WriteLine($"Deleted Product Set IDs:");

                if (response.Data?.ProductSetIds != null)
                {
                    foreach (var productSetId in response.Data.ProductSetIds)
                    {
                        Console.WriteLine($"- {productSetId}");
                    }
                }

                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.Code}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        #endregion

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _client?.Dispose();
        }
    }

    /// <summary>
    /// Program entry point for running the examples
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Replace with your actual access token
            const string accessToken = "your-access-token";
            
            var example = new CatalogsApiExample(accessToken);
            
            try
            {
                Console.WriteLine("=== TikTok Business API - Catalogs Examples ===\n");
                
                await example.RunAllExamples();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
            finally
            {
                example.Dispose();
            }
        }
    }
}
