﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.BusinessApplications;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.Cache;
using Volo.Abp.Caching;
using Volo.Abp.ObjectMapping;

namespace TikTok.JobManagement
{
    /// <summary>
    /// Service implementation cho cache cấu hình job
    /// </summary>
    public class JobTypeConfigurationCache : IJobTypeConfigurationCache, ICacheService
    {
        private readonly ILogger<JobTypeConfigurationCache> _logger;
        private readonly IJobTypeConfigurationRepository _jobTypeConfigurationRepository;
        private readonly IDistributedCache<List<JobTypeConfigurationDto>, string> _configurationCache;

        private readonly IObjectMapper _mapper;

        // Cache key constants
        private const string ConfigurationCacheKey = "JobTypeConfiguration:All";

        private static readonly TimeSpan CacheExpiration = CacheConst.CACHE_EXPIRATION;

        public JobTypeConfigurationCache(
            ILogger<JobTypeConfigurationCache> logger,
            IJobTypeConfigurationRepository jobTypeConfigurationRepository,
            IDistributedCache<List<JobTypeConfigurationDto>, string> configurationCache,
            IObjectMapper mapper)
        {
            _logger = logger;
            _jobTypeConfigurationRepository = jobTypeConfigurationRepository;
            _configurationCache = configurationCache;
            _mapper = mapper;
        }

        public async Task<JobTypeConfigurationDto?> GetByIdAsync(Guid id)
        {
            var jobTypes = await GetAllActiveAsync();
            var jobType = jobTypes.FirstOrDefault(x => x.Id == id);
            if (jobType == null)
            {
                _logger.LogWarning("Không tìm thấy cấu hình job type với id: {id}", id);
                return null;
            }
            return jobType;
        }

        public async Task<JobTypeConfigurationDto?> GetByCommandTypeAsync(CommandType commandType)
        {
            var jobTypes = await GetAllActiveAsync();
            var jobType = jobTypes.FirstOrDefault(x => x.CommandType == commandType);
            if (jobType == null)
            {
                _logger.LogWarning("Không tìm thấy cấu hình job type với CommandType: {commandType}", commandType);
                return null;
            }
            return jobType;
        }

        public async Task<List<JobTypeConfigurationDto>> GetAllActiveAsync()
        {
            var cacheKey = ConfigurationCacheKey;
            var jobTypes = await _configurationCache.GetOrAddAsync(
                cacheKey,
                async () =>
                {
                    var jobTypes = await _jobTypeConfigurationRepository.GetListAsync(x => x.IsActive);

                    return _mapper.Map<List<JobTypeConfigurationEntity>, List<JobTypeConfigurationDto>>(jobTypes);
                },
                () => new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = CacheExpiration
                });
            return jobTypes ?? new List<JobTypeConfigurationDto>();
        }

        public async Task CleanCache()
        {
            await _configurationCache.RemoveAsync(ConfigurationCacheKey, hideErrors: true);
        }

        // ICacheService implementation
        public string CacheName => "Job Type Configuration Cache";

        public async Task<List<string>> GetCacheKeysAsync()
        {
            return new List<string> { ConfigurationCacheKey };
        }

        public async Task<bool> ClearCacheAsync()
        {
            try
            {
                await _configurationCache.RemoveAsync(ConfigurationCacheKey, hideErrors: true);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ClearCacheByKeyAsync(string cacheKey)
        {
            try
            {
                if (cacheKey == ConfigurationCacheKey)
                {
                    await _configurationCache.RemoveAsync(cacheKey, hideErrors: true);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<CacheInfoDto> GetCacheInfoAsync()
        {
            var cacheKeys = await GetCacheKeysAsync();
            return new CacheInfoDto
            {
                CacheName = CacheName,
                ActiveKeysCount = cacheKeys.Count,
                CacheKeys = cacheKeys,
                CreatedAt = DateTime.UtcNow,
                LastAccessedAt = DateTime.UtcNow,
                LastModifiedAt = DateTime.UtcNow,
                Status = "Active",
                Description = "Cache cho danh sách Job Type Configuration"
            };
        }
    }
}