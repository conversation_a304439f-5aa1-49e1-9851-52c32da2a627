/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Spark Ads operations
    /// </summary>
    public class SparkAdsApi : ISparkAdsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<SparkAdsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the SparkAdsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public SparkAdsApi(IApiClient apiClient, ILogger<SparkAdsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get info about a Spark Ad post
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request containing advertiser ID and authorization code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing Spark Ad post information</returns>
        public async Task<SparkAdInfoResponse> GetSparkAdInfoAsync(
            string accessToken,
            SparkAdInfoRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogInformation("Getting Spark Ad info for advertiser {AdvertiserId} with auth code {AuthCode}", 
                request.AdvertiserId, request.AuthCode);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["auth_code"] = request.AuthCode
            };

            var response = await _apiClient.CallApiAsync<Response<SparkAdInfoResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SparkAdsEndpoints.GetSparkAdInfo}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);

            return response.Data ?? new SparkAdInfoResponse();
        }

        /// <summary>
        /// Apply an authorization code
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request containing advertiser ID and authorization codes</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        //public async Task AuthorizeSparkAdAsync(
        //    string accessToken,
        //    SparkAdAuthorizeRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Authorizing Spark Ad for advertiser {AdvertiserId} with auth code {AuthCode}", 
        //        request.AdvertiserId, request.AuthCode);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SparkAdsEndpoints.AuthorizeSparkAd}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get Spark Ad posts
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request containing search criteria and pagination parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of Spark Ad posts</returns>
        public async Task<SparkAdPostsResponse> GetSparkAdPostsAsync(
            string accessToken,
            SparkAdPostsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogInformation("Getting Spark Ad posts for advertiser {AdvertiserId}", request.AdvertiserId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId
            };

            if (request.ItemTypes != null && request.ItemTypes.Count > 0)
            {
                queryParams["item_types"] = string.Join(",", request.ItemTypes);
            }

            if (!string.IsNullOrWhiteSpace(request.Keyword))
            {
                queryParams["keyword"] = request.Keyword;
            }

            if (request.Page.HasValue)
            {
                queryParams["page"] = request.Page.Value.ToString();
            }

            if (request.PageSize.HasValue)
            {
                queryParams["page_size"] = request.PageSize.Value.ToString();
            }

            var response = await _apiClient.CallApiAsync<Response<SparkAdPostsResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SparkAdsEndpoints.GetSparkAdPosts}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);

            return response.Data ?? new SparkAdPostsResponse();
        }

        /// <summary>
        /// Unbind a Spark Ad post
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request containing advertiser ID and item ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        //public async Task UnbindSparkAdAsync(
        //    string accessToken,
        //    SparkAdUnbindRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Unbinding Spark Ad for advertiser {AdvertiserId} with item ID {ItemId}", 
        //        request.AdvertiserId, request.ItemId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SparkAdsEndpoints.UnbindSparkAd}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}
    }
}
