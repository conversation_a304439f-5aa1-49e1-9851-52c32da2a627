using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu GMV Max Campaign
    /// </summary>
    public interface IGmvMaxCampaignSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ GMV Max Campaign theo Advertiser ID và BC ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxCampaignSyncResult> SyncGmvMaxCampaignsAsync(string advertiserId, string bcId, List<string>? unauthorizedAdvertiserIds = null);

        /// <summary>
        /// Đồng bộ nhiều GMV Max Campaign cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds"><PERSON>h sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxCampaignSyncResult> SyncManyGmvMaxCampaignsAsync(string bcId, List<string>? advertiserIds = null);

        /// <summary>
        /// Đồng bộ tất cả GMV Max Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxCampaignSyncResult> SyncAllGmvMaxCampaignsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ GMV Max Campaign
    /// </summary>
    public class GmvMaxCampaignSyncResult : SyncResultBase
    {
        /// <summary>
        /// Tổng số bản ghi đã xử lý
        /// </summary>
        public override int TotalSynced => NewRecords + UpdatedRecords + ErrorRecords;

        /// <summary>
        /// Thời gian bắt đầu đồng bộ
        /// </summary>
        public System.DateTime StartTime { get; set; }

        /// <summary>
        /// Thời gian kết thúc đồng bộ
        /// </summary>
        public System.DateTime EndTime { get; set; }

        /// <summary>
        /// Thời gian thực hiện (tính bằng giây)
        /// </summary>
        public double DurationSeconds => (EndTime - StartTime).TotalSeconds;

        public string? GetSummaryMessage()
        {
            return $"Đồng bộ tổng: {TotalSynced}, New: {NewRecords}, Update: {UpdatedRecords}, Error: {ErrorRecords}, ErrorMessage: {ErrorMessage}";
        }
    }
} 