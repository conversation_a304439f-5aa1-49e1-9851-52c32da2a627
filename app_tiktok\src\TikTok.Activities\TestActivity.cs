﻿using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using Newtonsoft.Json;

namespace TikTok.Activities
{
    [Activity(Category = "Test", DisplayName = "Test", Outcomes = new[] { OutcomeNames.Done })]
    public class TestActivity : UnitOfWorkActivity
    {
        [ActivityInput(Hint = "String Input", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public string Input { get; set; }

        [ActivityInput(Hint = "Int Input 2", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public int Input2 { get; set; }

        [ActivityInput(Hint = "Objet Input 3", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public object Input3 { get; set; }

        [ActivityInput(Hint = "Objet Input 4", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public object Input4 { get; set; }

        [ActivityInput(Hint = "Objet Input 5", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public object Input5 { get; set; }

        [ActivityOutput(Hint = "Test Output")]
        public string Output { get; set; }

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            string output = string.Empty;
            if (!string.IsNullOrEmpty(Input))
            {
                output += $"Input: {Input}\n";
            }

            if (Input2 != 0)
            {
                output += $"Input2: {Input2}\n";
            }

            if (Input3 != null)
            {
                output += $"Input3: {JsonConvert.SerializeObject(Input3)}\n";
            }

            if (Input4 != null)
            {
                output += $"Input4: {JsonConvert.SerializeObject(Input4)}\n";
            }

            if (Input5 != null)
            {
                output += $"Input5: {JsonConvert.SerializeObject(Input5)}\n";
            }

            Output = output;

            return Done();
        }
    }
}