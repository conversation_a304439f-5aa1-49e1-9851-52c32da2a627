# FactAdvertisementEntity

## Thông tin bảng dữ liệu

-   **Table**: `FactAdvertisements`
-   **TablePrefix**: `Fact_`
-   **TableName**: `Fact_FactAdvertisements`
-   **DbSchema**: `TikTok`

## Mô tả

Fact table lưu trữ hiệu suất quảng cáo từ 4 nguồn dữ liệu khác nhau. **Type field bắt buộc** để ETL biết lưu dữ liệu từ cấp độ nào.

## Cấu trúc bảng

| Tên Field                | Kiểu dữ liệu | Bắt buộc | Độ dài | <PERSON>ô tả                                                                 |
| ------------------------ | ------------ | -------- | ------ | --------------------------------------------------------------------- |
| Id                       | Guid         | ✓        | -      | ID duy nhất của bản ghi (Primary Key)                                 |
| DimDateId                | int          | ✓        | -      | Khóa ngoại liên kết với Dim_Date (YYYYMMDD)                           |
| DimAdAccountId           | Guid         | ✓        | -      | Khóa ngoại liên kết với Dim_AdAccount                                 |
| DimBusinessCenterId      | Guid         | ✓        | -      | Khóa ngoại liên kết với Dim_BusinessCenter                            |
| DimCampaignId            | Guid?        | ✗        | -      | Khóa ngoại liên kết với Dim_Campaign (null nếu Type = BC/AdAccount)   |
| DimAdGroupId             | Guid?        | ✗        | -      | Khóa ngoại liên kết với Dim_AdGroup (null nếu không phải AdGroup)     |
| DimAdId                  | Guid?        | ✗        | -      | Khóa ngoại liên kết với Dim_Ad (null nếu không phải Ad)               |
| **Type**                 | **string**   | **✓**    | **20** | **"BC", "AdAccount", "Campaign", "AdGroup", "Ad" - BẮT BUỘC cho ETL** |
| EntityId                 | string       | ✓        | 100    | ID của entity theo từng cấp độ                                        |
| EntityName               | string       | ✓        | 255    | Tên của entity                                                        |
| AdvertiserId             | string       | ✓        | 100    | ID tài khoản quảng cáo                                                |
| BcId                     | string       | ✓        | 100    | ID Business Center                                                    |
| Spend                    | decimal      | ✓        | 18,2   | Chi tiêu tổng                                                         |
| Impressions              | long         | ✓        | -      | Lượt hiển thị                                                         |
| Clicks                   | long         | ✓        | -      | Lượt nhấp                                                             |
| Ctr                      | decimal      | ✓        | 18,4   | Tỷ lệ nhấp                                                            |
| Cpm                      | decimal      | ✓        | 18,2   | Chi phí mỗi 1000 lượt hiển thị                                        |
| Cpc                      | decimal      | ✓        | 18,2   | Chi phí mỗi lượt nhấp                                                 |
| Conversion               | long         | ✓        | -      | Số lượt chuyển đổi                                                    |
| CostPerConversion        | decimal      | ✓        | 18,2   | Chi phí mỗi lượt chuyển đổi                                           |
| ConversionRate           | decimal      | ✓        | 18,4   | Tỷ lệ chuyển đổi                                                      |
| Reach                    | long         | ✓        | -      | Số người tiếp cận                                                     |
| Frequency                | decimal      | ✗        | 18,4   | Tần suất hiển thị (chỉ AdAccount, AdGroup, Ad)                        |
| VideoPlayActions         | long         | ✗        | -      | Lượt phát video (chỉ Ad)                                              |
| VideoWatched2s           | long         | ✗        | -      | Lượt xem video 2s (chỉ Ad)                                            |
| VideoWatched6s           | long         | ✗        | -      | Lượt xem video 6s (chỉ Ad)                                            |
| VideoViewsP25            | long         | ✗        | -      | Lượt xem video 25% (chỉ Ad)                                           |
| VideoViewsP50            | long         | ✗        | -      | Lượt xem video 50% (chỉ Ad)                                           |
| VideoViewsP75            | long         | ✗        | -      | Lượt xem video 75% (chỉ Ad)                                           |
| VideoViewsP100           | long         | ✗        | -      | Lượt xem video 100% (chỉ Ad)                                          |
| AverageVideoPlay         | decimal      | ✗        | 18,2   | Thời gian phát trung bình (chỉ Ad)                                    |
| EngagedView              | long         | ✗        | -      | Lượt xem tập trung 6s (chỉ Ad)                                        |
| OnsiteShoppingRoas       | decimal      | ✓        | 18,4   | ROAS TikTok Shop                                                      |
| TotalOnsiteShoppingValue | decimal      | ✓        | 18,2   | Tổng doanh thu TikTok Shop                                            |
| OnsiteShopping           | long         | ✓        | -      | Số lượt mua hàng Shop                                                 |
| CostPerOnsiteShopping    | decimal      | ✓        | 18,2   | Chi phí mỗi lượt mua Shop                                             |
| ValuePerOnsiteShopping   | decimal      | ✓        | 18,2   | Giá trị đơn hàng trung bình Shop                                      |
| Currency                 | string       | ✓        | 10     | Tiền tệ                                                               |
| Date                     | DateTime     | ✓        | -      | Ngày báo cáo (UTC)                                                    |
| CreationTime             | DateTime     | ✓        | -      | Thời gian tạo bản ghi                                                 |

## Type Values và ETL Mapping

### Type = "BC" (từ RawReportIntegratedBcEntity)

-   **EntityId** ← BcId
-   **EntityName** ← BC Name lookup
-   **Spend** ← RawReportIntegratedBcEntity.Spend
-   **Impressions** ← RawReportIntegratedBcEntity.Impressions
-   **Clicks** ← RawReportIntegratedBcEntity.Clicks
-   **Date** ← RawReportIntegratedBcEntity.Date

### Type = "AdAccount" (từ RawReportIntegratedAdAccountEntity)

-   **EntityId** ← RawReportIntegratedAdAccountEntity.AdvertiserId
-   **EntityName** ← RawReportIntegratedAdAccountEntity.AdvertiserName
-   **Spend** ← RawReportIntegratedAdAccountEntity.Spend
-   **Impressions** ← RawReportIntegratedAdAccountEntity.Impressions
-   **Clicks** ← RawReportIntegratedAdAccountEntity.Clicks
-   **Ctr** ← RawReportIntegratedAdAccountEntity.Ctr
-   **Cpm** ← RawReportIntegratedAdAccountEntity.Cpm
-   **Cpc** ← RawReportIntegratedAdAccountEntity.Cpc
-   **Reach** ← RawReportIntegratedAdAccountEntity.Reach
-   **Frequency** ← RawReportIntegratedAdAccountEntity.Frequency
-   **Date** ← RawReportIntegratedAdAccountEntity.Date

### Type = "Campaign" (từ RawReportIntegratedCampaignEntity)

-   **EntityId** ← RawReportIntegratedCampaignEntity.CampaignId
-   **EntityName** ← RawReportIntegratedCampaignEntity.CampaignName
-   **Spend** ← RawReportIntegratedCampaignEntity.Spend
-   **Impressions** ← RawReportIntegratedCampaignEntity.Impressions
-   **Clicks** ← RawReportIntegratedCampaignEntity.Clicks
-   **Conversion** ← RawReportIntegratedCampaignEntity.Conversion
-   **CostPerConversion** ← RawReportIntegratedCampaignEntity.CostPerConversion
-   **ConversionRate** ← RawReportIntegratedCampaignEntity.ConversionRateV2
-   **OnsiteShoppingRoas** ← RawReportIntegratedCampaignEntity.OnsiteShoppingRoas
-   **TotalOnsiteShoppingValue** ← RawReportIntegratedCampaignEntity.TotalOnsiteShoppingValue
-   **Date** ← RawReportIntegratedCampaignEntity.Date

### Type = "AdGroup" (từ RawReportIntegratedAdGroupEntity)

-   **EntityId** ← RawReportIntegratedAdGroupEntity.AdGroupId
-   **EntityName** ← RawReportIntegratedAdGroupEntity.AdGroupName
-   **Spend** ← RawReportIntegratedAdGroupEntity.Spend
-   **Impressions** ← RawReportIntegratedAdGroupEntity.Impressions
-   **Clicks** ← RawReportIntegratedAdGroupEntity.Clicks
-   **Ctr** ← RawReportIntegratedAdGroupEntity.Ctr
-   **Cpm** ← RawReportIntegratedAdGroupEntity.Cpm
-   **Cpc** ← RawReportIntegratedAdGroupEntity.Cpc
-   **Conversion** ← RawReportIntegratedAdGroupEntity.Conversion
-   **CostPerConversion** ← RawReportIntegratedAdGroupEntity.CostPerConversion
-   **ConversionRate** ← RawReportIntegratedAdGroupEntity.ConversionRateV2
-   **Reach** ← RawReportIntegratedAdGroupEntity.Reach
-   **Frequency** ← RawReportIntegratedAdGroupEntity.Frequency
-   **OnsiteShoppingRoas** ← RawReportIntegratedAdGroupEntity.OnsiteShoppingRoas
-   **TotalOnsiteShoppingValue** ← RawReportIntegratedAdGroupEntity.TotalOnsiteShoppingValue
-   **Date** ← RawReportIntegratedAdGroupEntity.Date

### Type = "Ad" (từ RawReportIntegratedAdEntity)

-   **EntityId** ← RawReportIntegratedAdEntity.AdId
-   **EntityName** ← RawReportIntegratedAdEntity.AdName
-   **Spend** ← RawReportIntegratedAdEntity.Spend
-   **Impressions** ← RawReportIntegratedAdEntity.Impressions
-   **Clicks** ← RawReportIntegratedAdEntity.Clicks
-   **Ctr** ← RawReportIntegratedAdEntity.Ctr
-   **Cpm** ← RawReportIntegratedAdEntity.Cpm
-   **Cpc** ← RawReportIntegratedAdEntity.Cpc
-   **Conversion** ← RawReportIntegratedAdEntity.Conversion
-   **CostPerConversion** ← RawReportIntegratedAdEntity.CostPerConversion
-   **Reach** ← RawReportIntegratedAdEntity.Reach
-   **Frequency** ← RawReportIntegratedAdEntity.Frequency
-   **VideoPlayActions** ← RawReportIntegratedAdEntity.VideoPlayActions
-   **VideoWatched2s** ← RawReportIntegratedAdEntity.VideoWatched2s
-   **VideoWatched6s** ← RawReportIntegratedAdEntity.VideoWatched6s
-   **VideoViewsP25** ← RawReportIntegratedAdEntity.VideoViewsP25
-   **VideoViewsP50** ← RawReportIntegratedAdEntity.VideoViewsP50
-   **VideoViewsP75** ← RawReportIntegratedAdEntity.VideoViewsP75
-   **VideoViewsP100** ← RawReportIntegratedAdEntity.VideoViewsP100
-   **AverageVideoPlay** ← RawReportIntegratedAdEntity.AverageVideoPlay
-   **EngagedView** ← RawReportIntegratedAdEntity.EngagedView
-   **OnsiteShoppingRoas** ← RawReportIntegratedAdEntity.OnsiteShoppingRoas
-   **TotalOnsiteShoppingValue** ← RawReportIntegratedAdEntity.TotalOnsiteShoppingValue
-   **Date** ← RawReportIntegratedAdEntity.Date

## Grain Definition

-   **Mỗi dòng đại diện cho**: Hiệu suất 1 entity ở 1 cấp độ trong 1 ngày
-   **Unique Key**: (EntityId, Type, DimDateId)

## Field Availability by Type

| Field Category    | BC  | AdAccount | Campaign | AdGroup | Ad  |
| ----------------- | --- | --------- | -------- | ------- | --- |
| **Basic Metrics** | ✅  | ✅        | ✅       | ✅      | ✅  |
| **Frequency**     | ❌  | ✅        | ❌       | ✅      | ✅  |
| **Video Metrics** | ❌  | ❌        | ❌       | ❌      | ✅  |
| **TikTok Shop**   | ✅  | ❌        | ✅       | ✅      | ✅  |

### Lưu ý về dữ liệu:

-   **Frequency**: Chỉ có trong AdAccount, AdGroup, Ad levels
-   **Video Metrics**: Chỉ có trong Ad level (VideoPlayActions, VideoWatched\*, AverageVideoPlay, EngagedView)
-   **TikTok Shop**: Có ở hầu hết levels trừ AdAccount

## Business Questions

```sql
-- Hiệu suất theo cấp độ (drill-down/up)
SELECT Type, SUM(Spend), SUM(TotalOnsiteShoppingValue),
       SUM(TotalOnsiteShoppingValue)/SUM(Spend) as ROAS
FROM Fact_QuangCao
WHERE DateKey >= ********
GROUP BY Type

-- So sánh GMV Max performance
SELECT EntityName, Spend, TotalOnsiteShoppingValue, OnsiteShoppingRoas
FROM Fact_QuangCao
WHERE Type = 'Campaign' AND OnsiteShoppingRoas > 0
ORDER BY OnsiteShoppingRoas DESC

-- Phân tích video performance (chỉ Ad level)
SELECT EntityName,
       VideoViewsP100 / NULLIF(VideoPlayActions, 0) as CompletionRate,
       VideoWatched6s / NULLIF(Impressions, 0) as ViewRate,
       AverageVideoPlay
FROM Fact_QuangCao
WHERE Type = 'Ad' AND VideoPlayActions > 0
ORDER BY CompletionRate DESC

-- Phân tích frequency capping
SELECT Type,
       AVG(Frequency) as AvgFrequency,
       AVG(Reach) as AvgReach,
       SUM(Impressions)/SUM(Reach) as CalculatedFrequency
FROM Fact_QuangCao
WHERE Type IN ('AdAccount', 'AdGroup', 'Ad') AND Reach > 0
GROUP BY Type
```
