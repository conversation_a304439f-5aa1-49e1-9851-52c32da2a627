using Microsoft.AspNetCore.Authorization;
using System.Threading.Tasks;
using TikTok.Permissions;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu giao dịch
    /// </summary>
    public class TransactionSyncApplicationAppService : ApplicationService, ITransactionSyncApplicationAppService
    {
        private readonly ITransactionSyncService _transactionSyncService;

        public TransactionSyncApplicationAppService(ITransactionSyncService transactionSyncService)
        {
            _transactionSyncService = transactionSyncService;
        }

        /// <summary>
        /// Đồng bộ dữ liệu giao dịch theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        [Authorize(TikTokPermissions.Transactions.Sync)]
        public async Task<TransactionSyncResult> SyncTransactionsAsync(string bcId)
        {
            return await _transactionSyncService.SyncTransactionsAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ dữ liệu giao dịch cho tất cả Business Applications
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        [Authorize(TikTokPermissions.Transactions.Sync)]
        public async Task<TransactionSyncResult> SyncAllTransactionsAsync()
        {
            return await _transactionSyncService.SyncAllTransactionsAsync();
        }
    }
}