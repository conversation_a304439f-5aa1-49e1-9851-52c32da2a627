/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Threading.Tasks;
using TikTokBusinessApi.Api;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the AuthenticationApi
    /// </summary>
    public class AuthenticationApiExample
    {
        /// <summary>
        /// Example of obtaining a long-term access token
        /// </summary>
        /// <param name="appId">Your TikTok Business API app ID</param>
        /// <param name="secret">Your TikTok Business API app secret</param>
        /// <param name="authCode">Authorization code from the authorization flow</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task GetLongTermAccessTokenExample(string appId, string secret, string authCode)
        {
            try
            {
                // Create the authentication API client
                var authApi = new AuthenticationApi();

                // Create the request
                var request = new LongTermAccessTokenRequest
                {
                    AppId = appId,
                    Secret = secret,
                    AuthCode = authCode
                };

                // Get the long-term access token
                var response = await authApi.GetLongTermAccessTokenAsync(request);

                if (response.Data != null && response.Data.Code == 0)
                {
                    Console.WriteLine($"Successfully obtained long-term access token!");
                    Console.WriteLine($"Access Token: {response.Data.Data?.AccessToken}");
                    Console.WriteLine($"Advertiser IDs: {string.Join(", ", response.Data.Data?.AdvertiserIds ?? Array.Empty<string>())}");
                    Console.WriteLine($"Scope: {string.Join(", ", response.Data.Data?.Scope ?? Array.Empty<int>())}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
                else
                {
                    Console.WriteLine($"Failed to obtain access token: {response.Data?.Message}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Exception: {ex.Message}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Exception: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of obtaining an access token via /oauth/token/
        /// </summary>
        /// <param name="clientId">Your TikTok Business API client ID</param>
        /// <param name="clientSecret">Your TikTok Business API client secret</param>
        /// <param name="code">Authorization code from the authorization flow</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task GetAccessTokenExample(string clientId, string clientSecret, string code)
        {
            try
            {
                // Create the authentication API client
                var authApi = new AuthenticationApi();

                // Create the request
                var request = new AccessTokenRequest
                {
                    ClientId = clientId,
                    ClientSecret = clientSecret,
                    Code = code,
                    GrantType = "authorization_code"
                };

                // Get the access token
                var response = await authApi.GetAccessTokenAsync(request);

                if (response.Data != null && response.Data.Code == 0)
                {
                    Console.WriteLine($"Successfully obtained access token!");
                    Console.WriteLine($"Access Token: {response.Data.AccessToken}");
                    Console.WriteLine($"Token Type: {response.Data.TokenType}");
                    Console.WriteLine($"Advertiser IDs: {string.Join(", ", response.Data.AdvertiserIds)}");
                    Console.WriteLine($"Scope: {string.Join(", ", response.Data.Scope)}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
                else
                {
                    Console.WriteLine($"Failed to obtain access token: {response.Data?.Message}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Exception: {ex.Message}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Exception: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of obtaining a short-term access token for TikTok account access
        /// </summary>
        /// <param name="clientId">Your TikTok Business API client ID</param>
        /// <param name="clientSecret">Your TikTok Business API client secret</param>
        /// <param name="authCode">Authorization code from the TikTok account authorization flow</param>
        /// <param name="redirectUri">Redirect URI that matches your app configuration</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task GetShortTermAccessTokenExample(string clientId, string clientSecret, string authCode, string redirectUri)
        {
            try
            {
                // Create the authentication API client
                var authApi = new AuthenticationApi();

                // Create the request
                var request = new ShortTermAccessTokenRequest
                {
                    ClientId = clientId,
                    ClientSecret = clientSecret,
                    AuthCode = authCode,
                    RedirectUri = redirectUri,
                    GrantType = "authorization_code"
                };

                // Get the short-term access token
                var response = await authApi.GetShortTermAccessTokenAsync(request);

                if (response.Data != null && response.Data.Code == 0)
                {
                    Console.WriteLine($"Successfully obtained short-term access token!");
                    Console.WriteLine($"Access Token: {response.Data.Data?.AccessToken}");
                    Console.WriteLine($"Token Type: {response.Data.Data?.TokenType}");
                    Console.WriteLine($"Expires In: {response.Data.Data?.ExpiresIn} seconds");
                    Console.WriteLine($"Refresh Token: {response.Data.Data?.RefreshToken}");
                    Console.WriteLine($"Refresh Token Expires In: {response.Data.Data?.RefreshTokenExpiresIn} seconds");
                    Console.WriteLine($"Open ID: {response.Data.Data?.OpenId}");
                    Console.WriteLine($"Scope: {response.Data.Data?.Scope}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
                else
                {
                    Console.WriteLine($"Failed to obtain short-term access token: {response.Data?.Message}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Exception: {ex.Message}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Exception: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of refreshing a short-term access token
        /// </summary>
        /// <param name="clientId">Your TikTok Business API client ID</param>
        /// <param name="clientSecret">Your TikTok Business API client secret</param>
        /// <param name="refreshToken">Refresh token obtained from the initial token request</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task RefreshShortTermAccessTokenExample(string clientId, string clientSecret, string refreshToken)
        {
            try
            {
                // Create the authentication API client
                var authApi = new AuthenticationApi();

                // Create the request
                var request = new RefreshShortTermTokenRequest
                {
                    ClientId = clientId,
                    ClientSecret = clientSecret,
                    RefreshToken = refreshToken,
                    GrantType = "refresh_token"
                };

                // Refresh the short-term access token
                var response = await authApi.RefreshShortTermAccessTokenAsync(request);

                if (response.Data != null && response.Data.Code == 0)
                {
                    Console.WriteLine($"Successfully refreshed short-term access token!");
                    Console.WriteLine($"New Access Token: {response.Data.Data?.AccessToken}");
                    Console.WriteLine($"Token Type: {response.Data.Data?.TokenType}");
                    Console.WriteLine($"Expires In: {response.Data.Data?.ExpiresIn} seconds");
                    Console.WriteLine($"New Refresh Token: {response.Data.Data?.RefreshToken}");
                    Console.WriteLine($"Refresh Token Expires In: {response.Data.Data?.RefreshTokenExpiresIn} seconds");
                    Console.WriteLine($"Open ID: {response.Data.Data?.OpenId}");
                    Console.WriteLine($"Scope: {response.Data.Data?.Scope}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
                else
                {
                    Console.WriteLine($"Failed to refresh short-term access token: {response.Data?.Message}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Exception: {ex.Message}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Exception: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of revoking a long-term access token
        /// </summary>
        /// <param name="appId">Your TikTok Business API app ID</param>
        /// <param name="secret">Your TikTok Business API app secret</param>
        /// <param name="accessToken">The access token to revoke</param>
        /// <param name="authToken">Authorized access token for authentication</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task RevokeLongTermAccessTokenExample(string appId, string secret, string accessToken, string authToken)
        {
            try
            {
                // Create the authentication API client
                var authApi = new AuthenticationApi();

                // Create the request
                var request = new RevokeLongTermTokenRequest
                {
                    AppId = appId,
                    Secret = secret,
                    AccessToken = accessToken
                };

                // Revoke the long-term access token
                var response = await authApi.RevokeLongTermAccessTokenAsync(request, authToken);

                if (response.Data != null && response.Data.Code == 0)
                {
                    Console.WriteLine($"Successfully revoked long-term access token!");
                    Console.WriteLine($"App ID: {response.Data.Data?.AppId}");
                    Console.WriteLine($"Affected Advertiser IDs: {string.Join(", ", response.Data.Data?.AdvertiserIds ?? Array.Empty<string>())}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
                else
                {
                    Console.WriteLine($"Failed to revoke long-term access token: {response.Data?.Message}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Exception: {ex.Message}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Exception: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of revoking a short-term access token
        /// </summary>
        /// <param name="clientId">Your TikTok Business API client ID</param>
        /// <param name="clientSecret">Your TikTok Business API client secret</param>
        /// <param name="accessToken">The access token to revoke</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task RevokeShortTermAccessTokenExample(string clientId, string clientSecret, string accessToken)
        {
            try
            {
                // Create the authentication API client
                var authApi = new AuthenticationApi();

                // Create the request
                var request = new RevokeShortTermTokenRequest
                {
                    ClientId = clientId,
                    ClientSecret = clientSecret,
                    AccessToken = accessToken
                };

                // Revoke the short-term access token
                var response = await authApi.RevokeShortTermAccessTokenAsync(request);

                if (response.Data != null && response.Data.Code == 0)
                {
                    Console.WriteLine($"Successfully revoked short-term access token!");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
                else
                {
                    Console.WriteLine($"Failed to revoke short-term access token: {response.Data?.Message}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Exception: {ex.Message}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Exception: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }
    }
}
