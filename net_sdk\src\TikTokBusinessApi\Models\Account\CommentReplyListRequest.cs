/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for getting replies to a comment
    /// </summary>
    public class CommentReplyListRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for owned TikTok video to list comments on
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for comment on an owned TikTok video to list replies on
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string CommentId { get; set; } = string.Empty;

        /// <summary>
        /// Enumerated status of comment reply visibility
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Specific field to sort comment replies by
        /// </summary>
        [JsonPropertyName("sort_field")]
        public string? SortField { get; set; }

        /// <summary>
        /// Specific order to sort comment replies by
        /// </summary>
        [JsonPropertyName("sort_order")]
        public string? SortOrder { get; set; }

        /// <summary>
        /// Cursor for pagination
        /// </summary>
        [JsonPropertyName("cursor")]
        public int? Cursor { get; set; }

        /// <summary>
        /// The maximum number of comment replies that will be returned for each page
        /// </summary>
        [JsonPropertyName("max_count")]
        public int? MaxCount { get; set; }
    }
}
