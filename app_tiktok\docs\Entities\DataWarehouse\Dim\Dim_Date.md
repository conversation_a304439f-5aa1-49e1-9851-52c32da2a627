# DimDateEntity

## Thông tin bảng dữ liệu
- **Table**: `DimDates`
- **TablePrefix**: `Dim_`
- **TableName**: `Dim_DimDates`
- **DbSchema**: `Analytics`

## Mô tả

Dimension table cho thời gian, hỗ trợ phân tích theo ngày/tuần/tháng/quý như yêu cầu trong YÊU CẦU 3 Dashboard thống kê.

## Cấu trúc bảng: DimDate

| Tên Field               | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                           |
| ----------------------- | ------------ | -------- | ------ | ----------------------------------------------- |
| **Id**                  | int          | ✓        | -      | **<PERSON>h<PERSON><PERSON> ch<PERSON> (format: YYYYMMDD)**               |
| **FullDate**            | DateTime     | ✓        | -      | **Ng<PERSON><PERSON> đầy đủ**                                 |
| **Year**                | int          | ✓        | -      | **Năm**                                         |
| **Month**               | int          | ✓        | -      | **Tháng (1-12)**                                |
| **Quarter**             | int          | ✓        | -      | **Quý (1-4)**                                   |
| **Week**                | int          | ✓        | -      | **Tuần trong năm (1-53)**                       |
| **DayOfWeek**           | int          | ✓        | -      | **Thứ trong tuần (1=CN, 2=T2,...)**             |
| **MonthName**           | string       | ✓        | 20     | **Tên tháng (January, February,...)**           |
| **MonthNameVi**         | string       | ✓        | 20     | **Tên tháng tiếng Việt (Tháng 1, Tháng 2,...)** |
| **QuarterName**         | string       | ✓        | 10     | **Tên quý (Q1, Q2, Q3, Q4)**                    |
| **YearMonth**           | string       | ✓        | 10     | **Năm-Tháng (YYYY-MM)**                         |
| **YearQuarter**         | string       | ✓        | 10     | **Năm-Quý (YYYY-Q1)**                           |
| **IsWeekend**           | bool         | ✓        | -      | **Có phải cuối tuần không**                     |
| **DateFormat_DDMMYYYY** | string       | ✓        | 10     | **Format DD/MM/YYYY cho display**               |

## Đặc điểm của Dimension

-   **Type**: Type 0 (Không thay đổi)
-   **Range**: Từ 2020-01-01 đến 2030-12-31
-   **Pre-populated**: Dữ liệu được tạo sẵn khi khởi tạo hệ thống

## Business Rules

1. **Date Range**: Hỗ trợ từ năm 2020 đến 2030
2. **Weekend**: Thứ 7 và Chủ nhật là cuối tuần
3. **Week Calculation**: Tuần bắt đầu từ Chủ nhật
4. **Quarter**: Q1 (Jan-Mar), Q2 (Apr-Jun), Q3 (Jul-Sep), Q4 (Oct-Dec)

## Indexes đề xuất

-   **Primary Key**: Id
-   **Performance Index**: Year, Month, Quarter, Week
-   **Lookup Index**: FullDate

## Đáp ứng yêu cầu

### ✅ YÊU CẦU 3: Dashboard thống kê

**Thống kê chi tiêu theo các mốc thời gian cố định:**

-   ✅ **Ngày**: FullDate, Id
-   ✅ **Tuần**: Week, YearMonth grouping
-   ✅ **Tháng**: Month, MonthName, YearMonth
-   ✅ **Quý**: Quarter, QuarterName, YearQuarter

**Các khoảng thời gian tùy chỉnh:**

-   ✅ Range filtering bằng Id hoặc FullDate
-   ✅ Period comparison (YoY, MoM, QoQ)

**Ví dụ queries:**

```sql
-- Thống kê theo tháng
SELECT d.YearMonth, d.MonthNameVi, SUM(f.DailySpend)
FROM FactDailySpends f
JOIN DimDates d ON f.DateId = d.Id
GROUP BY d.YearMonth, d.MonthNameVi
ORDER BY d.YearMonth

-- Thống kê theo quý
SELECT d.YearQuarter, SUM(f.DailySpend)
FROM FactDailySpends f
JOIN DimDates d ON f.DateId = d.Id
GROUP BY d.YearQuarter
ORDER BY d.YearQuarter

-- Khoảng thời gian tùy chỉnh
SELECT SUM(f.DailySpend)
FROM FactDailySpends f
JOIN DimDates d ON f.DateId = d.Id
WHERE d.FullDate BETWEEN '2024-01-01' AND '2024-03-31'
```

## Mục đích sử dụng

-   Phân tích xu hướng theo thời gian
-   Báo cáo theo ngày/tuần/tháng/quý
-   Dashboard timeline filtering
-   So sánh performance theo periods
-   Seasonal analysis
-   Pivot table time hierarchies
