# Tính năng Quản lý thông báo mới nhất

## Tổng quan

Tính năng Quản lý thông báo mới nhất được thiết kế theo chuẩn ABP để quản lý các thông báo hệ thống một cách hiệu quả. Tính năng này cho phép:

-   L<PERSON>u trữ thông báo theo loại và đối tượng
-   <PERSON> dõi thời gian gửi thông báo cuối cùng
-   Quản lý rule thông báo
-   Tối ưu hiệu suất với indexing

## Cấu trúc

### 1. Domain Layer

#### Entity: `LatestNotificationEntity`

```csharp
public class LatestNotificationEntity : CreationAuditedEntity<Guid>
{
    public string ObjectId { get; set; }           // Id của đối tượng
    public NotificationType Type { get; set; }     // Loại thông báo
    public string? Payload { get; set; }           // Thông tin
    public string? Rule { get; set; }              // Rule thông báo
    public DateTime LastNotifiedTime { get; set; } // Thời gian gửi cuối
}
```

#### Enum: `NotificationType`

```csharp
public enum NotificationType
{
    Balance = 1,           // Thông báo về số dư
    Transaction = 2,       // Thông báo về giao dịch
    Campaign = 3,          // Thông báo về chiến dịch
    AdAccount = 4,         // Thông báo về tài khoản quảng cáo
    BusinessCenter = 5,    // Thông báo về Business Center
    Asset = 6,             // Thông báo về tài sản
    Report = 7,            // Thông báo về báo cáo
    Job = 8,               // Thông báo về công việc
    Customer = 9,          // Thông báo về khách hàng
    System = 10            // Thông báo hệ thống
}
```

#### Repository Interface: `ILatestNotificationRepository`

```csharp
public interface ILatestNotificationRepository : IRepository<LatestNotificationEntity, Guid>
{
    // Lấy thông báo theo Type và danh sách objectIds
    Task<List<LatestNotificationEntity>> GetByTypeAndObjectIdsAsync(NotificationType type, List<string> objectIds);

    // Lấy thông báo theo Type và objectId
    Task<LatestNotificationEntity?> GetByTypeAndObjectIdAsync(NotificationType type, string objectId);

    // Cập nhật hoặc tạo mới thông báo
    Task<LatestNotificationEntity> UpsertAsync(string objectId, NotificationType type, string? payload = null, string? rule = null);

    // Các method khác...
}
```

### 2. Entity Framework Core Layer

#### DbContext Configuration

-   Entity được đăng ký trong `TikTokDbContext`
-   Cấu hình unique index trên `(Type, ObjectId)`
-   Cấu hình các index cho hiệu suất query
-   Cấu hình enum conversion

#### Repository Implementation: `LatestNotificationRepository`

-   Implement đầy đủ các method từ interface
-   Sử dụng Entity Framework Core cho data access
-   Tối ưu query với async/await

## Cách sử dụng

### 1. Inject Repository

```csharp
public class YourService
{
    private readonly ILatestNotificationRepository _notificationRepository;

    public YourService(ILatestNotificationRepository notificationRepository)
    {
        _notificationRepository = notificationRepository;
    }
}
```

### 2. Tạo hoặc cập nhật thông báo

```csharp
// Tạo thông báo mới hoặc cập nhật nếu đã tồn tại
var notification = await _notificationRepository.UpsertAsync(
    objectId: "ad_account_123",
    type: NotificationType.Balance,
    payload: "Số dư thấp: $50.00",
    rule: "balance_threshold"
);
```

### 3. Lấy thông báo theo Type và ObjectIds

```csharp
var objectIds = new List<string> { "ad_account_123", "ad_account_456" };
var notifications = await _notificationRepository.GetByTypeAndObjectIdsAsync(
    NotificationType.Balance,
    objectIds
);
```

### 4. Lấy thông báo chưa được gửi

```csharp
// Lấy thông báo chưa được gửi trong 24 giờ qua
var unnotifiedNotifications = await _notificationRepository.GetUnnotifiedAsync(hours: 24);
```

### 5. Xóa thông báo cũ

```csharp
// Xóa thông báo cũ hơn 30 ngày
var deletedCount = await _notificationRepository.DeleteOldNotificationsAsync(olderThanDays: 30);
```

## Database Schema

### Bảng: `LatestNotifications`

| Cột                  | Kiểu dữ liệu     | Mô tả                   |
| -------------------- | ---------------- | ----------------------- |
| Id                   | uniqueidentifier | Khóa chính              |
| ObjectId             | nvarchar(255)    | Id của đối tượng        |
| Type                 | int              | Loại thông báo (enum)   |
| Payload              | nvarchar(4000)   | Thông tin thông báo     |
| Rule                 | nvarchar(1000)   | Rule thông báo          |
| LastNotifiedTime     | datetime2        | Thời gian gửi cuối      |
| CreationTime         | datetime2        | Thời gian tạo           |
| LastModificationTime | datetime2        | Thời gian cập nhật cuối |
| IsDeleted            | bit              | Soft delete flag        |
| DeletionTime         | datetime2        | Thời gian xóa           |

### Indexes

-   **Primary Key**: `Id`
-   **Unique Index**: `(Type, ObjectId)` - Đảm bảo không có duplicate
-   **Performance Indexes**:
    -   `Type`
    -   `ObjectId`
    -   `LastNotifiedTime`
    -   `CreationTime`
    -   `LastModificationTime`

## Migration

Để tạo migration cho tính năng này:

```bash
cd app_tiktok/src/TikTok.EntityFrameworkCore
dotnet ef migrations add AddLatestNotifications
dotnet ef database update
```

## Best Practices

1. **Sử dụng Upsert**: Luôn sử dụng `UpsertAsync` thay vì tạo mới để tránh duplicate
2. **Indexing**: Đã cấu hình sẵn các index quan trọng cho hiệu suất
3. **Soft Delete**: Entity hỗ trợ soft delete để bảo toàn dữ liệu
4. **Async/Await**: Tất cả operations đều async để tối ưu performance
5. **Validation**: Kiểm tra dữ liệu trước khi lưu vào database

## Ví dụ sử dụng thực tế

### Scenario: Thông báo số dư thấp

```csharp
public class BalanceNotificationService
{
    private readonly ILatestNotificationRepository _notificationRepository;

    public async Task NotifyLowBalanceAsync(string adAccountId, decimal currentBalance, decimal threshold)
    {
        if (currentBalance < threshold)
        {
            var payload = JsonSerializer.Serialize(new
            {
                CurrentBalance = currentBalance,
                Threshold = threshold,
                Currency = "USD"
            });

            await _notificationRepository.UpsertAsync(
                objectId: adAccountId,
                type: NotificationType.Balance,
                payload: payload,
                rule: "low_balance_alert"
            );
        }
    }
}
```

### Scenario: Batch Processing

```csharp
public async Task ProcessBatchNotificationsAsync()
{
    // Lấy tất cả thông báo chưa được gửi
    var unnotifiedNotifications = await _notificationRepository.GetUnnotifiedAsync(hours: 1);

    foreach (var notification in unnotifiedNotifications)
    {
        // Xử lý gửi thông báo
        await SendNotificationAsync(notification);

        // Cập nhật thời gian gửi
        notification.UpdateLastNotifiedTime();
        await _notificationRepository.UpdateAsync(notification);
    }
}
```
