using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.AdAccounts;
using TikTok.Users;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.AdAccounts
{
    /// <summary>
    /// Service interface cho tài khoản quảng cáo
    /// </summary>
    public interface IAdAccountAppService :
        ICrudAppService<
            AdAccountDto,
            Guid,
            GetAdAccountListDto,
            CreateAdAccountDto,
            UpdateAdAccountDto>
    {
        /// <summary>
        /// Lấy tài khoản quảng cáo theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của tài khoản quảng cáo</param>
        /// <returns>Tài khoản quảng cáo</returns>
        Task<AdAccountDto> GetByAdvertiserIdAsync(string advertiserId);

        /// <summary>
        /// L<PERSON>y danh sách tài khoản quảng cáo theo Business Center ID
        /// </summary>
        /// <param name="ownerBcId">ID của Business Center</param>
        /// <returns>Danh sách tài khoản quảng cáo</returns>
        Task<PagedResultDto<AdAccountDto>> GetByOwnerBcIdAsync(string ownerBcId);

        /// <summary>
        /// Gán người hỗ trợ và tài khoản quảng cáo
        /// </summary>
        /// <param name="advertiserId"></param>
        /// <param name="supporterId"></param>
        /// <param name="role"></param>
        /// <returns></returns>
        Task AssignSupporterAsync(string advertiserId, Guid supporterId, List<string> permissions);
        Task RemoveSupporterAsync(string advertiserId, Guid supporterId);
        Task UpdateSupporterConfigurationAsync(string advertiserId, List<SupporterAssignmentDto> assignments);
        Task<SupporterConfigurationDto> GetSupporterConfigurationAsync(string advertiserId);
    }
}