# docker/app-tiktok/tiktok-build-azure-pipeline-linux.yml
trigger: none

pool:
  name: CommeAsiaLinuxPool

variables:
  # Th<PERSON> mục output theo yêu cầu
  buildNumber: '$(Build.BuildNumber)'
  outputRoot: '$(Build.SourcesDirectory)/output/$(Build.BuildNumber)'
  imagesOut: '$(Build.SourcesDirectory)/output/$(Build.BuildNumber)/images'
  migratorOut: '$(Build.SourcesDirectory)/output/$(Build.BuildNumber)/migrator'

  # Thông tin module TikTok
  modulePath: 'app_tiktok'
  webAppFolder: 'src/TikTok.Web'
  reactAppFolder: 'src/TikTok.Web'
  migratorProject: 'src/TikTok.DbMigrator'
  imageName: 'module/tiktok.web' # bạn có thể đổi thành registry name sau này ở CD
  artifactFileName: 'tiktok.web-$(Build.BuildNumber)'
  dockerTarExt: 'tar'

steps:
  - task: UseDotNet@2
    displayName: 'Install .NET SDK 8.x'
    inputs:
      packageType: sdk
      version: 8.x
      includePreviewVersions: true
      performMultiLevelLookup: true

  - task: CmdLine@2
    continueOnError: true
    inputs:
      script: 'dotnet tool install -g Volo.Abp.Cli --version 8.3.4'

  - task: JoeJulik.install-node-and-npm-task.custom-build-release-task.NodeAndNpmTool@1
    displayName: 'Use Node 18.x'
    inputs:
      versionSpec: 18.x
      checkLatest: true

  - script: |
      npm i -g yarn
      yarn -v
    displayName: 'Install Yarn (global)'

  # npm auth cho Web (sử dụng .npmrc trong TikTok.Web)
  - task: npmAuthenticate@0
    displayName: 'npmAuthenticate TikTok.Web'
    inputs:
      workingFile: '$(Build.SourcesDirectory)/app_tiktok/src/TikTok.Web/.npmrc'

  # Tạo thư mục output
  - script: |
      mkdir -p "$(outputRoot)"
      mkdir -p "$(imagesOut)"
      mkdir -p "$(migratorOut)"
    displayName: 'Prepare output folders'

  # Gọi script build (PowerShell Core trên Linux - pwsh)
  - task: PowerShell@2
    displayName: 'Build TikTok Web image & Migrator (Linux)'
    inputs:
      targetType: filePath
      filePath: 'docker/app-tiktok/build-images-locally-linux.ps1'
      pwsh: true
      arguments: >
        -version "$(Build.BuildNumber)"
        -modulePath "$(modulePath)"
        -webAppFolder "$(webAppFolder)"
        -reactAppFolder "$(reactAppFolder)"
        -image "$(imageName)"
        -output_root "$(outputRoot)"
        -artifact_name "$(artifactFileName)"
        -outputExtension "$(dockerTarExt)"
        -migratorProject "$(migratorProject)"
        -nugetConfig "NuGet.Config"

  # Xuất artifact
  - task: PublishPipelineArtifact@1
    displayName: 'Publish TikTok build artifact'
    inputs:
      targetPath: '$(outputRoot)'
      artifact: 'tiktok-$(Build.BuildNumber)'
      publishLocation: 'pipeline'

  - script: |
      rm -rf $(outputRoot)
    displayName: 'Removing build file'

  # (Tuỳ chọn) Dọn cache để tiết kiệm dung lượng agent
  - script: |
      docker system prune -af || true
    displayName: 'Docker prune (optional)'
