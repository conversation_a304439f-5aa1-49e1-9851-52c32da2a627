﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace TikTok.FactDailySpends.Dtos
{
    public class FactDailySpendDto: AuditedEntityDto<Guid>
    {
        /// <summary>
        /// Khóa ngoại liên kết với Dim_Date (YYYYMMDD)
        /// </summary>
        public int DimDateId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_AdAccount
        /// </summary>
        public Guid DimAdAccountId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_BusinessCenter
        /// </summary>
        public Guid DimBusinessCenterId { get; set; }

        /// <summary>
        /// ID nhà quảng cáo (Business Key)
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// Tên nhà quảng cáo
        /// </summary>
        public string AdvertiserName { get; set; }

        /// <summary>
        /// ID Business Center (Business Key)
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// Tổng số tiền chi phí cho nhà quảng cáo
        /// </summary>
        public decimal TotalAmount { get; set; }
        public decimal? TotalAmountVND { get; set; }
        public decimal? TotalAmountUSD { get; set; }

        /// <summary>
        /// Số tiền chi phí tiền mặt
        /// </summary>
        public decimal CashAmount { get; set; }
        public decimal? CashAmountVND { get; set; }
        public decimal? CashAmountUSD { get; set; }

        /// <summary>
        /// Số tiền chi phí tín dụng quảng cáo
        /// </summary>
        public decimal GrantAmount { get; set; }
        public decimal? GrantAmountVND { get; set; }
        public decimal? GrantAmountUSD { get; set; }


        /// <summary>
        /// Số tiền thuế ước tính
        /// </summary>
        public decimal TaxAmount { get; set; }
        public decimal? TaxAmountVND { get; set; }
        public decimal? TaxAmountUSD { get; set; }

        /// <summary>
        /// Tiền tệ theo mã ISO 4217
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Ngày chi tiêu (UTC)
        /// </summary>
        public DateTime Date { get; set; }
    }
}
