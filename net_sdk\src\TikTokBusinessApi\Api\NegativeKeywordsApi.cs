/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Negative Keywords operations
    /// </summary>
    public class NegativeKeywordsApi : INegativeKeywordsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<NegativeKeywordsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the NegativeKeywordsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public NegativeKeywordsApi(IApiClient apiClient, ILogger<NegativeKeywordsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get the list of negative keywords for Search Ads
        /// </summary>
        /// <param name="request">Request parameters for getting negative keywords</param>
        /// <returns>Response containing the list of negative keywords</returns>
        public async Task<GetNegativeKeywordsResponse> GetNegativeKeywordsAsync(GetNegativeKeywordsRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.AdvertiserId))
                throw new ArgumentException("AdvertiserId is required", nameof(request));

            if (string.IsNullOrEmpty(request.ObjectType))
                throw new ArgumentException("ObjectType is required", nameof(request));

            if (string.IsNullOrEmpty(request.ObjectId))
                throw new ArgumentException("ObjectId is required", nameof(request));

            _logger?.LogDebug("Getting negative keywords for {ObjectType} {ObjectId} under advertiser {AdvertiserId}", 
                request.ObjectType, request.ObjectId, request.AdvertiserId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{NegativeKeywordsEndpoints.GetNegativeKeywords}";

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["object_type"] = request.ObjectType,
                ["object_id"] = request.ObjectId
            };

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();

            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetNegativeKeywordsResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: CancellationToken.None);
        }

        // /// <summary>
        // /// Create negative keywords for Search Ads
        // /// </summary>
        // /// <param name="request">Request parameters for creating negative keywords</param>
        // /// <returns>Response for the create operation</returns>
        // public async Task<CreateNegativeKeywordsResponse> CreateNegativeKeywordsAsync(CreateNegativeKeywordsRequest request)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.AdvertiserId))
        //         throw new ArgumentException("AdvertiserId is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.ObjectType))
        //         throw new ArgumentException("ObjectType is required", nameof(request));

        //     if (request.ObjectIds == null || request.ObjectIds.Count == 0)
        //         throw new ArgumentException("ObjectIds is required and cannot be empty", nameof(request));

        //     if (request.Keywords == null || request.Keywords.Count == 0)
        //         throw new ArgumentException("Keywords is required and cannot be empty", nameof(request));

        //     _logger?.LogDebug("Creating {KeywordCount} negative keywords for {ObjectType} objects under advertiser {AdvertiserId}", 
        //         request.Keywords.Count, request.ObjectType, request.AdvertiserId);

        //     var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{NegativeKeywordsEndpoints.CreateNegativeKeywords}";

        //     return await _apiClient.CallApiAsync<CreateNegativeKeywordsResponse>(
        //         path,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: CancellationToken.None);
        // }

        // /// <summary>
        // /// Update a negative keyword for Search Ads
        // /// </summary>
        // /// <param name="request">Request parameters for updating a negative keyword</param>
        // /// <returns>Response containing the updated keyword information</returns>
        // public async Task<UpdateNegativeKeywordResponse> UpdateNegativeKeywordAsync(UpdateNegativeKeywordRequest request)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.AdvertiserId))
        //         throw new ArgumentException("AdvertiserId is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.ObjectType))
        //         throw new ArgumentException("ObjectType is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.ObjectId))
        //         throw new ArgumentException("ObjectId is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.OldKeywordId))
        //         throw new ArgumentException("OldKeywordId is required", nameof(request));

        //     if (request.Keyword == null)
        //         throw new ArgumentException("Keyword is required", nameof(request));

        //     _logger?.LogDebug("Updating negative keyword {KeywordId} for {ObjectType} {ObjectId} under advertiser {AdvertiserId}", 
        //         request.OldKeywordId, request.ObjectType, request.ObjectId, request.AdvertiserId);

        //     var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{NegativeKeywordsEndpoints.UpdateNegativeKeyword}";

        //     return await _apiClient.CallApiAsync<UpdateNegativeKeywordResponse>(
        //         path,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: CancellationToken.None);
        // }

        // /// <summary>
        // /// Delete negative keywords for Search Ads
        // /// </summary>
        // /// <param name="request">Request parameters for deleting negative keywords</param>
        // /// <returns>Response for the delete operation</returns>
        // public async Task<DeleteNegativeKeywordsResponse> DeleteNegativeKeywordsAsync(DeleteNegativeKeywordsRequest request)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.AdvertiserId))
        //         throw new ArgumentException("AdvertiserId is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.ObjectType))
        //         throw new ArgumentException("ObjectType is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.ObjectId))
        //         throw new ArgumentException("ObjectId is required", nameof(request));

        //     if (request.KeywordIds == null || request.KeywordIds.Count == 0)
        //         throw new ArgumentException("KeywordIds is required and cannot be empty", nameof(request));

        //     _logger?.LogDebug("Deleting {KeywordCount} negative keywords for {ObjectType} {ObjectId} under advertiser {AdvertiserId}", 
        //         request.KeywordIds.Count, request.ObjectType, request.ObjectId, request.AdvertiserId);

        //     var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{NegativeKeywordsEndpoints.DeleteNegativeKeywords}";

        //     return await _apiClient.CallApiAsync<DeleteNegativeKeywordsResponse>(
        //         path,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: CancellationToken.None);
        // }

        /// <summary>
        /// Download the list of negative keywords for Search Ads as an Excel file
        /// </summary>
        /// <param name="request">Request parameters for downloading negative keywords</param>
        /// <returns>Excel file stream containing the negative keywords</returns>
        public async Task<byte[]> DownloadNegativeKeywordsAsync(DownloadNegativeKeywordsRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.AdvertiserId))
                throw new ArgumentException("AdvertiserId is required", nameof(request));

            if (string.IsNullOrEmpty(request.ObjectType))
                throw new ArgumentException("ObjectType is required", nameof(request));

            if (string.IsNullOrEmpty(request.ObjectId))
                throw new ArgumentException("ObjectId is required", nameof(request));

            _logger?.LogDebug("Downloading negative keywords for {ObjectType} {ObjectId} under advertiser {AdvertiserId}", 
                request.ObjectType, request.ObjectId, request.AdvertiserId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{NegativeKeywordsEndpoints.DownloadNegativeKeywords}";

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["object_type"] = request.ObjectType,
                ["object_id"] = request.ObjectId
            };

            return await _apiClient.CallApiAsync<byte[]>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: CancellationToken.None);
        }
    }
}
