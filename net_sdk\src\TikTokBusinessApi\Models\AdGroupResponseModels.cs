/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response data for ad group create requests
    /// </summary>
    public class AdGroupCreateResponse
    {
        /// <summary>
        /// List of ad group creation results
        /// </summary>
        [JsonPropertyName("list")]
        public List<AdGroupCreateResult>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupCreateResponse class
        /// </summary>
        public AdGroupCreateResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupCreateResponse(Count={List?.Count})";
        }
    }

    /// <summary>
    /// Result of ad group creation
    /// </summary>
    public class AdGroupCreateResult
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdgroupId { get; set; }

        /// <summary>
        /// Ad group name
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdgroupName { get; set; }

        /// <summary>
        /// Error information if creation failed
        /// </summary>
        [JsonPropertyName("errors")]
        public List<AdGroupError>? Errors { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupCreateResult class
        /// </summary>
        public AdGroupCreateResult()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupCreateResult(AdgroupId={AdgroupId}, AdgroupName={AdgroupName})";
        }
    }

    /// <summary>
    /// Response data for ad group update requests
    /// </summary>
    public class AdGroupUpdateResponse
    {
        /// <summary>
        /// List of ad group update results
        /// </summary>
        [JsonPropertyName("list")]
        public List<AdGroupUpdateResult>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupUpdateResponse class
        /// </summary>
        public AdGroupUpdateResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupUpdateResponse(Count={List?.Count})";
        }
    }

    /// <summary>
    /// Result of ad group update
    /// </summary>
    public class AdGroupUpdateResult
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdgroupId { get; set; }

        /// <summary>
        /// Ad group name
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdgroupName { get; set; }

        /// <summary>
        /// Error information if update failed
        /// </summary>
        [JsonPropertyName("errors")]
        public List<AdGroupError>? Errors { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupUpdateResult class
        /// </summary>
        public AdGroupUpdateResult()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupUpdateResult(AdgroupId={AdgroupId}, AdgroupName={AdgroupName})";
        }
    }

    /// <summary>
    /// Response data for ad group status update requests
    /// </summary>
    public class AdGroupStatusUpdateResponse
    {
        /// <summary>
        /// List of ad group status update results
        /// </summary>
        [JsonPropertyName("list")]
        public List<AdGroupStatusUpdateResult>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupStatusUpdateResponse class
        /// </summary>
        public AdGroupStatusUpdateResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupStatusUpdateResponse(Count={List?.Count})";
        }
    }

    /// <summary>
    /// Result of ad group status update
    /// </summary>
    public class AdGroupStatusUpdateResult
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdgroupId { get; set; }

        /// <summary>
        /// Error information if status update failed
        /// </summary>
        [JsonPropertyName("errors")]
        public List<AdGroupError>? Errors { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupStatusUpdateResult class
        /// </summary>
        public AdGroupStatusUpdateResult()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupStatusUpdateResult(AdgroupId={AdgroupId})";
        }
    }

    /// <summary>
    /// Response data for ad group budget update requests
    /// </summary>
    public class AdGroupBudgetUpdateResponse
    {
        /// <summary>
        /// List of ad group budget update results
        /// </summary>
        [JsonPropertyName("list")]
        public List<AdGroupBudgetUpdateResult>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupBudgetUpdateResponse class
        /// </summary>
        public AdGroupBudgetUpdateResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupBudgetUpdateResponse(Count={List?.Count})";
        }
    }

    /// <summary>
    /// Result of ad group budget update
    /// </summary>
    public class AdGroupBudgetUpdateResult
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdgroupId { get; set; }

        /// <summary>
        /// Error information if budget update failed
        /// </summary>
        [JsonPropertyName("errors")]
        public List<AdGroupError>? Errors { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupBudgetUpdateResult class
        /// </summary>
        public AdGroupBudgetUpdateResult()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupBudgetUpdateResult(AdgroupId={AdgroupId})";
        }
    }

    /// <summary>
    /// Error information for ad group operations
    /// </summary>
    public class AdGroupError
    {
        /// <summary>
        /// Error code
        /// </summary>
        [JsonPropertyName("code")]
        public string? Code { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Field that caused the error
        /// </summary>
        [JsonPropertyName("field")]
        public string? Field { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupError class
        /// </summary>
        public AdGroupError()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupError(Code={Code}, Message={Message}, Field={Field})";
        }
    }
}
