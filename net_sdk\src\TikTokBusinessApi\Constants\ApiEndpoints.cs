/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API endpoints
    /// </summary>
    public static class ApiEndpoints
    {
        /// <summary>
        /// Account API endpoints
        /// </summary>
        public static class Account
        {
            /// <summary>
            /// Get token information endpoint
            /// </summary>
            public const string TokenInfo = "/tt_user/token_info/get/";

            /// <summary>
            /// Get business analytics endpoint
            /// </summary>
            public const string BusinessAnalytics = "/business/get/";

            /// <summary>
            /// Get business video list endpoint
            /// </summary>
            public const string BusinessVideoList = "/business/video/list/";

            /// <summary>
            /// Get business benchmark endpoint
            /// </summary>
            public const string BusinessBenchmark = "/business/benchmark/";

            /// <summary>
            /// Get business video settings endpoint
            /// </summary>
            public const string VideoSettings = "/business/video/settings/";

            /// <summary>
            /// Get business comment list endpoint
            /// </summary>
            public const string CommentList = "/business/comment/list/";

            /// <summary>
            /// Get business comment reply list endpoint
            /// </summary>
            public const string CommentReplyList = "/business/comment/reply/list/";

            /// <summary>
            /// Create business comment endpoint
            /// </summary>
            public const string CommentCreate = "/business/comment/create/";

            /// <summary>
            /// Create business comment reply endpoint
            /// </summary>
            public const string CommentReplyCreate = "/business/comment/reply/create/";

            /// <summary>
            /// Like business comment endpoint
            /// </summary>
            public const string CommentLike = "/business/comment/like/";

            /// <summary>
            /// Hide business comment endpoint (v1.3)
            /// </summary>
            public const string BusinessCommentHide = "/business/comment/hide/";

            /// <summary>
            /// Delete business comment endpoint (v1.3)
            /// </summary>
            public const string BusinessCommentDelete = "/business/comment/delete/";

            /// <summary>
            /// Publish business video endpoint (v1.3)
            /// </summary>
            public const string BusinessVideoPublish = "/business/video/publish/";

            /// <summary>
            /// Publish business photo endpoint (v1.3)
            /// </summary>
            public const string BusinessPhotoPublish = "/business/photo/publish/";

            /// <summary>
            /// Get business publish status endpoint (v1.3)
            /// </summary>
            public const string BusinessPublishStatus = "/business/publish/status/";

            /// <summary>
            /// Get business hashtag suggestion endpoint (v1.3)
            /// </summary>
            public const string BusinessHashtagSuggestion = "/business/hashtag/suggestion/";

            /// <summary>
            /// Set business post authorize setting endpoint (v1.3)
            /// </summary>
            public const string BusinessPostAuthorizeSetting = "/business/post/authorize/setting/";

            /// <summary>
            /// Authorize business post endpoint (v1.3)
            /// </summary>
            public const string BusinessPostAuthorize = "/business/post/authorize/";

            /// <summary>
            /// Get business post authorize status endpoint (v1.3)
            /// </summary>
            public const string BusinessPostAuthorizeStatus = "/business/post/authorize/status/";

            /// <summary>
            /// Delete business post authorize endpoint (v1.3)
            /// </summary>
            public const string BusinessPostAuthorizeDelete = "/business/post/authorize/delete/";

            /// <summary>
            /// Add URL property endpoint (v1.3)
            /// </summary>
            public const string UrlPropertyAdd = "/url_property/add/";

            /// <summary>
            /// Verify URL property endpoint (v1.3)
            /// </summary>
            public const string UrlPropertyVerify = "/url_property/verify/";

            /// <summary>
            /// Delete URL property endpoint (v1.3)
            /// </summary>
            public const string UrlPropertyDelete = "/url_property/delete/";

            /// <summary>
            /// List URL properties endpoint (v1.3)
            /// </summary>
            public const string UrlPropertyList = "/url_property/list/";

            /// <summary>
            /// Update webhook endpoint (v1.3)
            /// </summary>
            public const string WebhookUpdate = "/webhook/update/";

            /// <summary>
            /// List webhook configurations endpoint (v1.3)
            /// </summary>
            public const string WebhookList = "/webhook/list/";

            /// <summary>
            /// Delete webhook endpoint (v1.3)
            /// </summary>
            public const string WebhookDelete = "/webhook/delete/";

            /// <summary>
            /// Add business property endpoint
            /// </summary>
            public const string BusinessPropertyAdd = "/business/property/add/";

            /// <summary>
            /// Verify business property endpoint
            /// </summary>
            public const string BusinessPropertyVerify = "/business/property/verify/";

            /// <summary>
            /// Delete business property endpoint
            /// </summary>
            public const string BusinessPropertyDelete = "/business/property/delete/";

            /// <summary>
            /// Get business property list endpoint
            /// </summary>
            public const string BusinessPropertyList = "/business/property/list/";

            /// <summary>
            /// Update business webhook endpoint
            /// </summary>
            public const string BusinessWebhookUpdate = "/business/webhook/update/";

            /// <summary>
            /// Get business webhook endpoint
            /// </summary>
            public const string BusinessWebhookGet = "/business/webhook/get/";

            /// <summary>
            /// Delete business webhook endpoint
            /// </summary>
            public const string BusinessWebhookDelete = "/business/webhook/delete/";
        }

        /// <summary>
        /// Ad API endpoints
        /// </summary>
        public static class Ad
        {
            /// <summary>
            /// Create ad endpoint
            /// </summary>
            public const string Create = "/ad/create/";

            /// <summary>
            /// Get ad endpoint
            /// </summary>
            public const string Get = "/ad/get/";

            /// <summary>
            /// Update ad endpoint
            /// </summary>
            public const string Update = "/ad/update/";

            /// <summary>
            /// Update ad status endpoint
            /// </summary>
            public const string UpdateStatus = "/ad/status/update/";
        }

        /// <summary>
        /// Authentication API endpoints
        /// </summary>
        public static class Authentication
        {
            /// <summary>
            /// Get long-term access token endpoint (v1.3)
            /// </summary>
            public const string GetLongTermAccessToken = "/oauth2/access_token/";

            /// <summary>
            /// Get access token endpoint (v1.3) - supports both JSON and form-encoded
            /// </summary>
            public const string GetAccessToken = "/oauth/token/";

            /// <summary>
            /// Revoke long-term access token endpoint (v1.3)
            /// </summary>
            public const string RevokeLongTermAccessToken = "/oauth2/revoke_token/";

            /// <summary>
            /// Get short-term access token endpoint (v1.3)
            /// </summary>
            public const string GetShortTermAccessToken = "/tt_user/oauth2/token/";

            /// <summary>
            /// Refresh short-term access token endpoint (v1.3)
            /// </summary>
            public const string RefreshShortTermAccessToken = "/tt_user/oauth2/refresh_token/";

            /// <summary>
            /// Revoke short-term access token endpoint (v1.3)
            /// </summary>
            public const string RevokeShortTermAccessToken = "/tt_user/oauth2/revoke/";
        }

        /// <summary>
        /// Authorization API endpoints (legacy)
        /// </summary>
        public static class Authorization
        {
            /// <summary>
            /// Get authorization URL endpoint
            /// </summary>
            public const string GetUrl = "/oauth2/authorize/";

            /// <summary>
            /// Get access token endpoint
            /// </summary>
            public const string GetAccessToken = "/oauth2/access_token/";

            /// <summary>
            /// Refresh access token endpoint
            /// </summary>
            public const string RefreshAccessToken = "/oauth2/refresh_token/";
        }

        /// <summary>
        /// Ad Account API endpoints
        /// </summary>
        public static class AdAccount
        {
            /// <summary>
            /// Get authorized ad accounts endpoint
            /// </summary>
            public const string GetAuthorizedAccounts = "/oauth2/advertiser/get/";

            /// <summary>
            /// Get ad account details endpoint
            /// </summary>
            public const string GetAccountInfo = "/advertiser/info/";
        }

        /// <summary>
        /// Ad Comment API endpoints
        /// </summary>
        public static class AdComment
        {
            /// <summary>
            /// Get comments under video ads endpoint
            /// </summary>
            public const string GetComments = "/comment/list/";

            /// <summary>
            /// Get related comments endpoint
            /// </summary>
            public const string GetRelatedComments = "/comment/reference/";

            /// <summary>
            /// Update comment statuses endpoint
            /// </summary>
            public const string UpdateCommentStatus = "/comment/status/update/";

            /// <summary>
            /// Reply to a comment endpoint
            /// </summary>
            public const string ReplyToComment = "/comment/post/";

            /// <summary>
            /// Delete a comment endpoint
            /// </summary>
            public const string DeleteComment = "/comment/delete/";

            /// <summary>
            /// Create comment export task endpoint
            /// </summary>
            public const string CreateCommentExportTask = "/comment/task/create/";

            /// <summary>
            /// Check comment export task status endpoint
            /// </summary>
            public const string CheckCommentExportTaskStatus = "/comment/task/check/";

            /// <summary>
            /// Download comment export task endpoint
            /// </summary>
            public const string DownloadCommentExport = "/comment/task/download/";

            /// <summary>
            /// Create blocked words endpoint
            /// </summary>
            public const string CreateBlockedWords = "/blockedword/create/";

            /// <summary>
            /// Update a blocked word endpoint
            /// </summary>
            public const string UpdateBlockedWord = "/blockedword/update/";

            /// <summary>
            /// Check blocked word statuses endpoint
            /// </summary>
            public const string CheckBlockedWords = "/blockedword/check/";

            /// <summary>
            /// Get blocked words list endpoint
            /// </summary>
            public const string GetBlockedWords = "/blockedword/list/";

            /// <summary>
            /// Delete blocked words endpoint
            /// </summary>
            public const string DeleteBlockedWords = "/blockedword/delete/";

            /// <summary>
            /// Create blocked word export task endpoint
            /// </summary>
            public const string CreateBlockedWordExportTask = "/blockedword/task/create/";

            /// <summary>
            /// Check blocked word export task status endpoint
            /// </summary>
            public const string CheckBlockedWordExportTaskStatus = "/blockedword/task/check/";

            /// <summary>
            /// Download blocked word export task endpoint
            /// </summary>
            public const string DownloadBlockedWordExport = "/blockedword/task/download/";
        }

        /// <summary>
        /// Ad Diagnosis API endpoints
        /// </summary>
        public static class AdDiagnosis
        {
            /// <summary>
            /// Get diagnoses for ad groups endpoint
            /// </summary>
            public const string Get = "/tool/diagnosis/get/";
        }

        /// <summary>
        /// Ad Group API endpoints
        /// </summary>
        public static class AdGroup
        {
            /// <summary>
            /// Get ad groups endpoint
            /// </summary>
            public const string Get = "/adgroup/get/";

            /// <summary>
            /// Get ad group quota endpoint
            /// </summary>
            public const string GetQuota = "/adgroup/quota/";

            /// <summary>
            /// Estimate audience size endpoint
            /// </summary>
            public const string EstimateAudienceSize = "/ad/audience_size/estimate/";

            /// <summary>
            /// Create ad groups endpoint
            /// </summary>
            public const string Create = "/adgroup/create/";

            /// <summary>
            /// Update ad groups endpoint
            /// </summary>
            public const string Update = "/adgroup/update/";

            /// <summary>
            /// Update ad group status endpoint
            /// </summary>
            public const string UpdateStatus = "/adgroup/status/update/";

            /// <summary>
            /// Update ad group budget endpoint
            /// </summary>
            public const string UpdateBudget = "/adgroup/budget/update/";
        }

        /// <summary>
        /// Ad Review API endpoints
        /// </summary>
        public static class AdReview
        {
            /// <summary>
            /// Get review info of ad groups endpoint
            /// </summary>
            public const string GetAdGroupReviewInfo = "/adgroup/review_info/";

            /// <summary>
            /// Get review info of ads endpoint
            /// </summary>
            public const string GetAdReviewInfo = "/ad/review_info/";

            /// <summary>
            /// Appeal a rejection endpoint
            /// </summary>
            public const string AppealAdGroupRejection = "/adgroup/appeal/";
        }

        /// <summary>
        /// Audience API endpoints
        /// </summary>
        public static class Audience
        {
            /// <summary>
            /// Upload an audience file endpoint
            /// </summary>
            public const string FileUpload = "/dmp/custom_audience/file/upload/";

            /// <summary>
            /// Create an audience by file endpoint
            /// </summary>
            public const string Create = "/dmp/custom_audience/create/";

            /// <summary>
            /// Create an audience by rules endpoint
            /// </summary>
            public const string CreateByRules = "/dmp/custom_audience/rule/create/";

            /// <summary>
            /// Get all audiences endpoint
            /// </summary>
            public const string List = "/dmp/custom_audience/list/";

            /// <summary>
            /// Get audience details endpoint
            /// </summary>
            public const string Get = "/dmp/custom_audience/get/";

            /// <summary>
            /// Update an audience endpoint
            /// </summary>
            public const string Update = "/dmp/custom_audience/update/";

            /// <summary>
            /// Delete audiences endpoint
            /// </summary>
            public const string Delete = "/dmp/custom_audience/delete/";

            /// <summary>
            /// Share audiences endpoint
            /// </summary>
            public const string Share = "/dmp/custom_audience/share/";

            /// <summary>
            /// Cancel audience sharing endpoint
            /// </summary>
            public const string ShareCancel = "/dmp/custom_audience/share/cancel/";

            /// <summary>
            /// Get sharing log of a custom audience endpoint
            /// </summary>
            public const string ShareLog = "/dmp/custom_audience/share/log/";

            /// <summary>
            /// Apply audiences to ad groups endpoint
            /// </summary>
            public const string Apply = "/dmp/custom_audience/apply/";

            /// <summary>
            /// Get apply log of a custom audience endpoint
            /// </summary>
            public const string ApplyLog = "/dmp/custom_audience/apply/log/";

            /// <summary>
            /// Create a lookalike audience endpoint
            /// </summary>
            public const string CreateLookalike = "/dmp/custom_audience/lookalike/create/";

            /// <summary>
            /// Update a lookalike audience endpoint
            /// </summary>
            public const string UpdateLookalike = "/dmp/custom_audience/lookalike/update/";
        }

        /// <summary>
        /// Segment API endpoints (Streaming API)
        /// </summary>
        public static class Segment
        {
            /// <summary>
            /// Create/Delete audience segment endpoint
            /// </summary>
            public const string Audience = "/segment/audience/";

            /// <summary>
            /// Add/Delete audience segment mappings endpoint
            /// </summary>
            public const string Mapping = "/segment/mapping/";
        }

        /// <summary>
        /// Saved Audience API endpoints
        /// </summary>
        public static class SavedAudience
        {
            /// <summary>
            /// Create saved audience endpoint
            /// </summary>
            public const string Create = "/dmp/saved_audience/create/";

            /// <summary>
            /// Delete saved audience endpoint
            /// </summary>
            public const string Delete = "/dmp/saved_audience/delete/";

            /// <summary>
            /// List saved audiences endpoint
            /// </summary>
            public const string List = "/dmp/saved_audience/list/";
        }

        /// <summary>
        /// Pangle Audience API endpoints
        /// </summary>
        public static class PangleAudience
        {
            /// <summary>
            /// Get Pangle audience package endpoint
            /// </summary>
            public const string GetPackage = "/pangle/audience_package/get/";
        }

        /// <summary>
        /// AutomatedRules API endpoints
        /// </summary>
        public static class AutomatedRules
        {
            /// <summary>
            /// Create automated rules endpoint (v1.3)
            /// </summary>
            public const string CreateRules = "/optimizer/rule/create/";

            /// <summary>
            /// Get rules by ID endpoint (v1.3)
            /// </summary>
            public const string GetRulesById = "/optimizer/rule/get/";

            /// <summary>
            /// Get rules by filters endpoint (v1.3)
            /// </summary>
            public const string GetRulesByFilters = "/optimizer/rule/list/";

            /// <summary>
            /// Get rule results endpoint (v1.3)
            /// </summary>
            public const string GetRuleResults = "/optimizer/rule/result/list/";

            /// <summary>
            /// Get result details endpoint (v1.3)
            /// </summary>
            public const string GetResultDetails = "/optimizer/rule/result/get/";

            /// <summary>
            /// Update rules endpoint (v1.3)
            /// </summary>
            public const string UpdateRules = "/optimizer/rule/update/";

            /// <summary>
            /// Update rule statuses endpoint (v1.3)
            /// </summary>
            public const string UpdateRuleStatuses = "/optimizer/rule/update/status/";

            /// <summary>
            /// Bind/Unbind rules endpoint (v1.3)
            /// </summary>
            public const string BindUnbindRules = "/optimizer/rule/batch_bind/";
        }

        /// <summary>
        /// BC Management API endpoints
        /// </summary>
        public static class BcManagement
        {
            /// <summary>
            /// Get Business Centers endpoint (v1.3)
            /// </summary>
            public const string GetBusinessCenters = "/bc/get/";

            /// <summary>
            /// Get Business Center changelog endpoint (v1.3)
            /// </summary>
            public const string GetBusinessCenterChangelog = "/changelog/get/";
        }

        /// <summary>
        /// BC Member API endpoints
        /// </summary>
        public static class BcMember
        {
            /// <summary>
            /// Get the members of a BC endpoint (v1.3)
            /// </summary>
            public const string GetMembers = "/bc/member/get/";

            /// <summary>
            /// Invite members to a BC endpoint (v1.3)
            /// </summary>
            public const string InviteMembers = "/bc/member/invite/";

            /// <summary>
            /// Update info of a BC member endpoint (v1.3)
            /// </summary>
            public const string UpdateMember = "/bc/member/update/";

            /// <summary>
            /// Delete a member from a BC endpoint (v1.3)
            /// </summary>
            public const string DeleteMember = "/bc/member/delete/";
        }

        /// <summary>
        /// BC Assets API endpoints
        /// </summary>
        public static class BcAssets
        {
            /// <summary>
            /// Create an ad account endpoint (v1.3)
            /// </summary>
            public const string CreateAdvertiser = "/bc/advertiser/create/";

            /// <summary>
            /// Update an ad account endpoint (v1.3)
            /// </summary>
            public const string UpdateAdvertiser = "/advertiser/update/";

            /// <summary>
            /// Disable an ad account endpoint (v1.3)
            /// </summary>
            public const string DisableAdvertiser = "/bc/advertiser/disable/";

            /// <summary>
            /// Upload a business certificate endpoint (v1.3)
            /// </summary>
            public const string UploadBusinessCertificate = "/bc/image/upload/";

            /// <summary>
            /// Get qualifications within a Business Center endpoint (v1.3)
            /// </summary>
            public const string GetQualifications = "/bc/advertiser/qualification/get/";

            /// <summary>
            /// Check UnionPay verification requirement endpoint (v1.3)
            /// </summary>
            public const string CheckUnionPayVerification = "/bc/advertiser/unionpay_info/check/";

            /// <summary>
            /// Submit UnionPay verification endpoint (v1.3)
            /// </summary>
            public const string SubmitUnionPayVerification = "/bc/advertiser/unionpay_info/submit/";

            /// <summary>
            /// Get assets endpoint (v1.3)
            /// </summary>
            public const string GetAssets = "/bc/asset/get/";

            /// <summary>
            /// Get assets as admin endpoint (v1.3)
            /// </summary>
            public const string GetAssetsAsAdmin = "/bc/asset/admin/get/";

            /// <summary>
            /// Assign an asset endpoint (v1.3)
            /// </summary>
            public const string AssignAsset = "/bc/asset/assign/";

            /// <summary>
            /// Unassign an asset endpoint (v1.3)
            /// </summary>
            public const string UnassignAsset = "/bc/asset/unassign/";

            /// <summary>
            /// Transfer a pixel from advertiser to BC endpoint (v1.3)
            /// </summary>
            public const string TransferPixel = "/bc/pixel/transfer/";

            /// <summary>
            /// Link/unlink a pixel to ad accounts endpoint (v1.3)
            /// </summary>
            public const string UpdatePixelLink = "/bc/pixel/link/update/";

            /// <summary>
            /// Get ad accounts linked to a pixel endpoint (v1.3)
            /// </summary>
            public const string GetPixelLinks = "/bc/pixel/link/get/";

            /// <summary>
            /// Get partners by an asset endpoint (v1.3)
            /// </summary>
            public const string GetAssetPartners = "/bc/asset/partner/get/";

            /// <summary>
            /// Get members by an asset endpoint (v1.3)
            /// </summary>
            public const string GetAssetMembers = "/bc/asset/member/get/";

            /// <summary>
            /// Delete assets from a BC endpoint (v1.3)
            /// </summary>
            public const string DeleteAssets = "/bc/asset/admin/delete/";

            /// <summary>
            /// Get binding info of an asset endpoint (v1.3)
            /// </summary>
            public const string GetAssetBindingQuota = "/asset/bind/quota/";

            /// <summary>
            /// Create an Asset Group endpoint (v1.3)
            /// </summary>
            public const string CreateAssetGroup = "/bc/asset_group/create/";

            /// <summary>
            /// Update an Asset Group endpoint (v1.3)
            /// </summary>
            public const string UpdateAssetGroup = "/bc/asset_group/update/";

            /// <summary>
            /// Get all Asset Groups endpoint (v1.3)
            /// </summary>
            public const string ListAssetGroups = "/bc/asset_group/list/";

            /// <summary>
            /// Get the details of an Asset Group endpoint (v1.3)
            /// </summary>
            public const string GetAssetGroup = "/bc/asset_group/get/";

            /// <summary>
            /// Delete Asset Groups endpoint (v1.3)
            /// </summary>
            public const string DeleteAssetGroups = "/bc/asset_group/delete/";
        }

        /// <summary>
        /// BC Payment API endpoints
        /// </summary>
        public static class BcPayment
        {
            /// <summary>
            /// Process a payment (transfer) endpoint (v1.3)
            /// </summary>
            public const string ProcessPayment = "/bc/transfer/";

            /// <summary>
            /// Get the balance and budget of ad accounts endpoint (v1.3)
            /// </summary>
            public const string GetAdvertiserBalance = "/advertiser/balance/get/";

            /// <summary>
            /// Get the balance of a BC endpoint (v1.3)
            /// </summary>
            public const string GetBcBalance = "/bc/balance/get/";

            /// <summary>
            /// Get the transaction records of a BC or ad accounts endpoint (v1.3)
            /// </summary>
            public const string GetBcAccountTransaction = "/bc/account/transaction/get/";

            /// <summary>
            /// Get the transaction records of ad accounts endpoint (v1.3)
            /// </summary>
            public const string GetAdvertiserTransaction = "/advertiser/transaction/get/";

            /// <summary>
            /// Get the transaction records of a BC endpoint (v1.3)
            /// </summary>
            public const string GetBcTransaction = "/bc/transaction/get/";

            /// <summary>
            /// Get the budget change history of an ad account endpoint (v1.3)
            /// </summary>
            public const string GetBudgetChangelog = "/bc/account/budget/changelog/get/";

            /// <summary>
            /// Get the cost records of a BC and ad accounts endpoint (v1.3)
            /// </summary>
            public const string GetCostRecords = "/bc/account/cost/get/";
        }

        /// <summary>
        /// BC Invoice API endpoints
        /// </summary>
        public static class BcInvoice
        {
            /// <summary>
            /// Get the invoices of a BC endpoint (v1.3)
            /// </summary>
            public const string GetInvoices = "/bc/invoice/get/";

            /// <summary>
            /// Get the unpaid amount of a BC endpoint (v1.3)
            /// </summary>
            public const string GetUnpaidAmount = "/bc/invoice/unpaid/get/";

            /// <summary>
            /// Download an individual invoice synchronously endpoint (v1.3)
            /// </summary>
            public const string DownloadInvoice = "/bc/invoice/download/";

            /// <summary>
            /// Create an asynchronous download task endpoint (v1.3)
            /// </summary>
            public const string CreateDownloadTask = "/bc/invoice/task/create/";

            /// <summary>
            /// Get asynchronous download task (BILLING_REPORT) endpoint (v1.3)
            /// </summary>
            public const string GetDownloadTask = "/bc/invoice/task/get/";

            /// <summary>
            /// Get asynchronous download task list (INVOICE_LIST and INVOICE_BATCH) endpoint (v1.3)
            /// </summary>
            public const string GetDownloadTaskList = "/bc/invoice/task/list/";
        }

        /// <summary>
        /// BC Billing Group API endpoints
        /// </summary>
        public static class BcBillingGroup
        {
            /// <summary>
            /// Create a billing group endpoint (v1.3)
            /// </summary>
            public const string CreateBillingGroup = "/bc/billing_group/create/";

            /// <summary>
            /// Update a billing group endpoint (v1.3)
            /// </summary>
            public const string UpdateBillingGroup = "/bc/billing_group/update/";

            /// <summary>
            /// Get billing groups endpoint (v1.3)
            /// </summary>
            public const string GetBillingGroups = "/bc/billing_group/get/";

            /// <summary>
            /// Get billing group advertisers endpoint (v1.3)
            /// </summary>
            public const string GetBillingGroupAdvertisers = "/bc/billing_group/advertiser/list/";
        }

        /// <summary>
        /// BC Partners API endpoints
        /// </summary>
        public static class BcPartners
        {
            /// <summary>
            /// Get the partners of a BC endpoint (v1.3)
            /// </summary>
            public const string GetPartners = "/bc/partner/get/";

            /// <summary>
            /// Add a partner to a BC endpoint (v1.3)
            /// </summary>
            public const string AddPartner = "/bc/partner/add/";

            /// <summary>
            /// Delete a partner from a BC endpoint (v1.3)
            /// </summary>
            public const string DeletePartner = "/bc/partner/delete/";

            /// <summary>
            /// Cancel the sharing of assets with a partner endpoint (v1.3)
            /// </summary>
            public const string CancelAssetSharing = "/bc/partner/asset/delete/";

            /// <summary>
            /// Get the assets of a partner endpoint (v1.3)
            /// </summary>
            public const string GetPartnerAssets = "/bc/partner/asset/get/";
        }

        /// <summary>
        /// BC Reporting API endpoints
        /// </summary>
        public static class BcReporting
        {
            /// <summary>
            /// Get currencies and registration areas for ad accounts endpoint (v1.3)
            /// </summary>
            public const string GetAdvertiserAttribute = "/bc/advertiser/attribute/";
        }

        /// <summary>
        /// Brand Safety API endpoints
        /// </summary>
        public static class BrandSafety
        {
            /// <summary>
            /// Get the Brand Safety Hub settings of an ad account endpoint (v1.3)
            /// </summary>
            public const string Get = "/tiktok_inventory_filters/get/";

            /// <summary>
            /// Set or update the Brand Safety Hub settings of an ad account endpoint (v1.3)
            /// </summary>
            public const string Update = "/tiktok_inventory_filters/update/";
        }

        /// <summary>
        /// Business Messaging API endpoints
        /// </summary>
        public static class BusinessMessaging
        {
            /// <summary>
            /// Send a message to a conversation endpoint
            /// </summary>
            public const string SendMessage = "/business/message/send/";

            /// <summary>
            /// Get a list of conversations endpoint
            /// </summary>
            public const string GetConversations = "/business/message/conversation/list/";

            /// <summary>
            /// Get a list of messages in a conversation endpoint
            /// </summary>
            public const string GetMessages = "/business/message/content/list/";

            /// <summary>
            /// Upload an image endpoint
            /// </summary>
            public const string UploadMedia = "/business/message/media/upload/";

            /// <summary>
            /// Download an image from a message endpoint
            /// </summary>
            public const string DownloadMedia = "/business/message/media/download/";

            /// <summary>
            /// Create a Business Messaging Webhook configuration endpoint
            /// </summary>
            public const string CreateWebhook = "/business/webhook/update/";

            /// <summary>
            /// Get a Business Messaging Webhook configuration endpoint
            /// </summary>
            public const string GetWebhook = "/business/webhook/list/";

            /// <summary>
            /// Delete a Business Messaging Webhook configuration endpoint
            /// </summary>
            public const string DeleteWebhook = "/business/webhook/delete/";

            /// <summary>
            /// Create an automatic message for a Business Account endpoint
            /// </summary>
            public const string CreateAutoMessage = "/business/message/auto_message/create/";

            /// <summary>
            /// Update the automatic message for a Business Account endpoint
            /// </summary>
            public const string UpdateAutoMessage = "/business/message/auto_message/update/";

            /// <summary>
            /// Turn on or turn off an automatic message for a Business Account endpoint
            /// </summary>
            public const string UpdateAutoMessageStatus = "/business/message/auto_message/status/update/";

            /// <summary>
            /// Get the automatic messages for a Business Account endpoint
            /// </summary>
            public const string GetAutoMessages = "/business/message/auto_message/get/";

            /// <summary>
            /// Delete the automatic message for a Business Account endpoint
            /// </summary>
            public const string DeleteAutoMessage = "/business/message/auto_message/delete/";

            /// <summary>
            /// Sort the automatic message for a Business Account endpoint
            /// </summary>
            public const string SortAutoMessages = "/business/message/auto_message/sort/";
        }

        /// <summary>
        /// Campaign API endpoints
        /// </summary>
        public static class Campaign
        {
            /// <summary>
            /// Get campaigns endpoint (v1.3)
            /// </summary>
            public const string Get = "/campaign/get/";

            /// <summary>
            /// Create campaign endpoint (v1.3)
            /// </summary>
            public const string Create = "/campaign/create/";

            /// <summary>
            /// Update campaign endpoint (v1.3)
            /// </summary>
            public const string Update = "/campaign/update/";

            /// <summary>
            /// Update campaign status endpoint (v1.3)
            /// </summary>
            public const string UpdateStatus = "/campaign/status/update/";

            /// <summary>
            /// Get campaign quota endpoint (v1.3) - Deprecated
            /// </summary>
            public const string GetQuota = "/campaign/quota/get/";

            /// <summary>
            /// Get campaign quota info endpoint (v1.3)
            /// </summary>
            public const string GetQuotaInfo = "/campaign/quota/info/";

            /// <summary>
            /// Create campaign copy task endpoint (v1.3)
            /// </summary>
            public const string CreateCopyTask = "/campaign/copy/task/create/";

            /// <summary>
            /// Check campaign copy task endpoint (v1.3)
            /// </summary>
            public const string CheckCopyTask = "/campaign/copy/task/check/";
        }

        /// <summary>
        /// Catalog API endpoints
        /// </summary>
        public static class Catalog
        {
            /// <summary>
            /// Create catalog endpoint
            /// </summary>
            public const string Create = "/catalog/create/";

            /// <summary>
            /// Update catalog endpoint
            /// </summary>
            public const string Update = "/catalog/update/";

            /// <summary>
            /// Delete catalog endpoint
            /// </summary>
            public const string Delete = "/catalog/delete/";

            /// <summary>
            /// Get catalogs endpoint
            /// </summary>
            public const string Get = "/catalog/get/";

            /// <summary>
            /// Get catalog lexicon endpoint
            /// </summary>
            public const string GetLexicon = "/catalog/lexicon/get/";

            /// <summary>
            /// Migrate catalog to Business Center endpoint
            /// </summary>
            public const string Capitalize = "/catalog/capitalize/";

            /// <summary>
            /// Get available countries/regions endpoint
            /// </summary>
            public const string GetAvailableCountry = "/catalog/available_country/get/";

            /// <summary>
            /// Get location and currency information endpoint
            /// </summary>
            public const string GetLocationCurrency = "/catalog/location_currency/get/";

            /// <summary>
            /// Get catalog overview endpoint
            /// </summary>
            public const string GetOverview = "/catalog/overview/";

            /// <summary>
            /// Bind event source to catalog endpoint
            /// </summary>
            public const string BindEventSource = "/catalog/eventsource/bind/";

            /// <summary>
            /// Unbind event source from catalog endpoint
            /// </summary>
            public const string UnbindEventSource = "/catalog/eventsource/unbind/";

            /// <summary>
            /// Get event source binding info endpoint
            /// </summary>
            public const string GetEventSourceBinding = "/catalog/eventsource_bind/get/";

            /// <summary>
            /// Create feed endpoint
            /// </summary>
            public const string CreateFeed = "/catalog/feed/create/";

            /// <summary>
            /// Get feeds endpoint
            /// </summary>
            public const string GetFeeds = "/catalog/feed/get/";

            /// <summary>
            /// Update feed endpoint
            /// </summary>
            public const string UpdateFeed = "/catalog/feed/update/";

            /// <summary>
            /// Delete feed endpoint
            /// </summary>
            public const string DeleteFeed = "/catalog/feed/delete/";

            /// <summary>
            /// Get feed logs endpoint
            /// </summary>
            public const string GetFeedLogs = "/catalog/feed/log/";

            /// <summary>
            /// Update feed schedule status endpoint
            /// </summary>
            public const string UpdateFeedScheduleStatus = "/catalog/feed/schedule/update/";

            /// <summary>
            /// Upload products via file URL endpoint
            /// </summary>
            public const string UploadProductsViaFile = "/catalog/product/file/";

            /// <summary>
            /// Upload products via JSON schema endpoint
            /// </summary>
            public const string UploadProducts = "/catalog/product/upload/";

            /// <summary>
            /// Update products endpoint
            /// </summary>
            public const string UpdateProducts = "/catalog/product/update/";

            /// <summary>
            /// Delete products endpoint
            /// </summary>
            public const string DeleteProducts = "/catalog/product/delete/";

            /// <summary>
            /// Get products endpoint
            /// </summary>
            public const string GetProducts = "/catalog/product/get/";

            /// <summary>
            /// Get product handling log endpoint
            /// </summary>
            public const string GetProductLog = "/catalog/product/log/";

            /// <summary>
            /// Get product sets endpoint
            /// </summary>
            public const string GetProductSets = "/catalog/set/get/";

            /// <summary>
            /// Get products in a product set endpoint
            /// </summary>
            public const string GetProductSetProducts = "/catalog/set/product/get/";

            /// <summary>
            /// Create product set endpoint
            /// </summary>
            public const string CreateProductSet = "/catalog/set/create/";

            /// <summary>
            /// Update product set endpoint
            /// </summary>
            public const string UpdateProductSet = "/catalog/set/update/";

            /// <summary>
            /// Delete product sets endpoint
            /// </summary>
            public const string DeleteProductSets = "/catalog/set/delete/";
        }
    }
}
