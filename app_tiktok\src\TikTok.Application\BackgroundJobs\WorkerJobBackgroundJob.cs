using Hangfire;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using TikTok.BackgroundJobs.Workers;
using TikTok.Enums;
using TikTok.Repositories;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;

namespace TikTok.BackgroundJobs
{
    /// <summary>
    /// Background job để xử lý WorkerJob
    /// </summary>
    public class WorkerJobBackgroundJob : AsyncBackgroundJob<WorkerJobArgs>, ITransientDependency
    {
        private readonly ILogger<WorkerJobBackgroundJob> _logger;
        private readonly WorkerJobFactory _workerJobFactory;
        private readonly IWorkerInfoRepository _workerInfoRepository;

        public WorkerJobBackgroundJob(
            ILogger<WorkerJobBackgroundJob> logger,
            WorkerJobFactory workerJobFactory,
            IWorkerInfoRepository workerInfoRepository)
        {
            _logger = logger;
            _workerJobFactory = workerJobFactory;
            _workerInfoRepository = workerInfoRepository;
        }

        [AutomaticRetry(Attempts = 0)]
        public override async Task ExecuteAsync(WorkerJobArgs args)
        {
            try
            {
                _logger.LogDebug("Starting WorkerJobBackgroundJob for job {JobId} with worker {WorkerId}, CommandType: {CommandType}",
                    args.JobId, args.WorkerId, args.CommandType);

                // Tạo worker dựa trên CommandType
                var workerJob = _workerJobFactory.CreateWorker(args.CommandType);

                // Thực hiện công việc
                var result = await workerJob.ExecuteAsync(args);

                if (result.IsSuccess)
                {
                    _logger.LogDebug("WorkerJobBackgroundJob completed successfully for job {JobId} in {ProcessingTimeMs}ms",
                        args.JobId, result.ProcessingTimeMs);
                    _logger.LogDebug("Result Data: {ResultData}", result.ResultData);
                }
                else
                {
                    _logger.LogError("WorkerJobBackgroundJob failed for job {JobId}: {ErrorMessage}",
                        args.JobId, result.ErrorMessage);
                }
                // Cập nhật trạng thái của worker
                var worker = await _workerInfoRepository.GetByWorkerIdAsync(args.WorkerId);
                if (worker != null)
                {
                    worker.Status = WorkerStatus.Idle;
                    worker.CompletedAt = DateTime.UtcNow;
                    worker.LastHeartbeat = DateTime.UtcNow;
                    await _workerInfoRepository.UpdateAsync(worker);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WorkerJobBackgroundJob failed for job {JobId}", args.JobId);
            }
        }
    }
}