/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Tools operations
    /// </summary>
    public interface IToolsApi
    {
        // /// <summary>
        // /// Search for location targeting tags by keyword
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing search parameters</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing targeting tags</returns>
        // Task<Response<SearchTargetingResponse>> SearchTargetingAsync(
        //     string accessToken,
        //     SearchTargetingRequest body,
        //     CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Get information about location targeting tags by ID
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing targeting IDs</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing targeting tag information</returns>
        // Task<Response<GetTargetingInfoResponse>> GetTargetingInfoAsync(
        //     string accessToken,
        //     GetTargetingInfoRequest body,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get available locations by different settings
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="placements">The apps where you want to deliver your ads</param>
        /// <param name="objectiveType">Your objective type</param>
        /// <param name="levelRange">Location level you want to get</param>
        /// <param name="language">The language of the returned location name</param>
        /// <param name="shoppingAdsType">Shopping ads type</param>
        /// <param name="promotionType">Promotion type</param>
        /// <param name="promotionTargetType">The promotion type for Lead Generation objective</param>
        /// <param name="operatingSystem">Operating systems that you want to target</param>
        /// <param name="brandSafetyType">Brand safety type</param>
        /// <param name="brandSafetyPartner">Brand safety partner</param>
        /// <param name="rfCampaignType">Campaign type for RF_REACH objective</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing available locations</returns>
        Task<Response<GetRegionResponse>> GetRegionAsync(
            string accessToken,
            string advertiserId,
            List<string> placements,
            string objectiveType,
            string? levelRange = null,
            string? language = null,
            string? shoppingAdsType = null,
            string? promotionType = null,
            string? promotionTargetType = null,
            string? operatingSystem = null,
            string? brandSafetyType = null,
            string? brandSafetyPartner = null,
            string? rfCampaignType = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get available locations by advertiser ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="language">The language you want the returned region name to be translated into</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing available locations</returns>
        Task<Response<SearchRegionResponse>> SearchRegionAsync(
            string accessToken,
            string advertiserId,
            string? language = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the enum values of language codes
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing language codes</returns>
        Task<Response<GetLanguageResponse>> GetLanguageAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Search for targeting categories and hashtags for interests and behaviors
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="targetingType">The primary targeting type</param>
        /// <param name="subTargetingTypes">The secondary targeting types</param>
        /// <param name="searchKeywords">A list of seed keywords</param>
        /// <param name="language">The language of the seed keywords</param>
        /// <param name="filtering">Filtering conditions</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing targeting categories and hashtags</returns>
        Task<Response<SearchInterestAndBehaviorResponse>> SearchInterestAndBehaviorAsync(
            string accessToken,
            string advertiserId,
            string targetingType,
            List<string>? subTargetingTypes = null,
            List<string>? searchKeywords = null,
            string? language = null,
            InterestBehaviorFiltering? filtering = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get general interest category enumeration values
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="version">Version of interest category</param>
        /// <param name="language">Category name language in response</param>
        /// <param name="placements">The apps where you want to deliver your ads</param>
        /// <param name="specialIndustries">Special ad categories</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing interest categories</returns>
        Task<Response<GetInterestCategoryResponse>> GetInterestCategoryAsync(
            string accessToken,
            string advertiserId,
            int? version = null,
            string? language = null,
            List<string>? placements = null,
            List<string>? specialIndustries = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Search for additional interest categories
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="keywords">A list of seed keywords</param>
        /// <param name="keyword">One seed keyword</param>
        /// <param name="mode">Search mode</param>
        /// <param name="language">Keyword language</param>
        /// <param name="limit">Number of additional interest categories you want to get</param>
        /// <param name="audienceType">Audience type</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing recommended keywords</returns>
        //Task<Response<RecommendInterestKeywordResponse>> RecommendInterestKeywordAsync(
        //    string accessToken,
        //    string advertiserId,
        //    List<string>? keywords = null,
        //    string? keyword = null,
        //    string? mode = null,
        //    string? language = null,
        //    int? limit = null,
        //    string? audienceType = null,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get additional interest categories by ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="keywordQuery">Information of the additional interest category you want to get</param>
        /// <param name="filtering">Filtering conditions</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing interest keywords</returns>
        Task<Response<GetInterestKeywordResponse>> GetInterestKeywordAsync(
            string accessToken,
            string advertiserId,
            List<InterestKeywordQuery> keywordQuery,
            InterestKeywordFiltering? filtering = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get action category enumeration values
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="specialIndustries">Special ad categories</param>
        /// <param name="language">Category name language in response</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing action categories</returns>
        Task<Response<GetActionCategoryResponse>> GetActionCategoryAsync(
            string accessToken,
            string advertiserId,
            List<string>? specialIndustries = null,
            string? language = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Search for targeting hashtags based on seed keywords
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="keywords">Keywords that you want to get recommended hashtags for</param>
        /// <param name="operatorType">The operator to be used between the keywords</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing recommended hashtags</returns>
        //Task<Response<RecommendHashtagResponse>> RecommendHashtagAsync(
        //    string accessToken,
        //    string advertiserId,
        //    List<string> keywords,
        //    string? operatorType = null,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the targeting hashtag names and statuses by ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="keywordIds">List of keyword IDs</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing hashtag information</returns>
        Task<Response<GetHashtagResponse>> GetHashtagAsync(
            string accessToken,
            string advertiserId,
            List<string> keywordIds,
            CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Get recommended interest and action categories based on historical performance data
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing advertiser ID, region codes, and optional app ID</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing recommended categories</returns>
        // Task<Response<RecommendTargetingCategoryResponse>> RecommendTargetingCategoryAsync(
        //     string accessToken,
        //     RecommendTargetingCategoryRequest body,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the enumeration values of operating system version
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="osType">OS type</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing OS versions</returns>
        Task<Response<GetOsVersionResponse>> GetOsVersionAsync(
            string accessToken,
            string advertiserId,
            string osType,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get device model enumeration values
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing device models</returns>
        Task<Response<GetDeviceModelResponse>> GetDeviceModelAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the carrier enumeration values that represents carriers in different countries or locations
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing carriers</returns>
        Task<Response<GetCarrierResponse>> GetCarrierAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get targeting tags (ISP IDs)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="locationIds">IDs of the locations that you want to get the supported ISP IDs for</param>
        /// <param name="scene">The targeting type that the targeting tags are used for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing targeting tags</returns>
        Task<Response<GetTargetingListResponse>> GetTargetingListAsync(
            string accessToken,
            string advertiserId,
            List<string> locationIds,
            string scene,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the contextual tags that you can target
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="objectiveType">Advertising objective</param>
        /// <param name="regionCodes">Country or region codes</param>
        /// <param name="brandSafetyType">Brand safety type</param>
        /// <param name="rfCampaignType">Campaign type for RF_REACH objective</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing contextual tags</returns>
        Task<Response<GetContextualTagResponse>> GetContextualTagAsync(
            string accessToken,
            string advertiserId,
            string objectiveType,
            List<string>? regionCodes = null,
            string? brandSafetyType = null,
            string? rfCampaignType = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the detailed information of contextual tags
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="contextualTagIds">Contextual tag IDs</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing contextual tag details</returns>
        Task<Response<GetContextualTagInfoResponse>> GetContextualTagInfoAsync(
            string accessToken,
            string advertiserId,
            List<string> contextualTagIds,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get region calling codes and region codes for phone numbers
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing phone region codes</returns>
        Task<Response<PhoneRegionCodeResponse>> GetPhoneRegionCodeAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get time zone enumeration values, and their offsets from GMT
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing time zones</returns>
        Task<Response<TimezoneResponse>> GetTimezoneAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the corresponding TikTok in-app link for an open TikTok URL
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="url">Open URL that you want to get the internal link for</param>
        /// <param name="urlType">Type of the open TikTok URL</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TikTok in-app link</returns>
        Task<Response<OpenUrlResponse>> GetOpenUrlAsync(
            string accessToken,
            string advertiserId,
            string url,
            string urlType,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get content categories or vertical sensitivity categories that can be excluded from appearing next to your ads
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="objectiveType">Advertising objective</param>
        /// <param name="brandSafetyType">Brand safety type</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing content exclusion categories</returns>
        Task<Response<ContentExclusionResponse>> GetContentExclusionAsync(
            string accessToken,
            string advertiserId,
            string objectiveType,
            string? brandSafetyType = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the detailed information of content categories or vertical categories that can be excluded from appearing next to your ads
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="categoryIds">IDs of the content exclusion categories or vertical sensitivity categories</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing content exclusion category details</returns>
        Task<Response<ContentExclusionInfoResponse>> GetContentExclusionInfoAsync(
            string accessToken,
            string advertiserId,
            List<string> categoryIds,
            CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Get a suggested bid value for your ad group based on basic campaign and ad group settings
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing bid recommendation parameters</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing suggested bid</returns>
        // Task<Response<BidRecommendResponse>> RecommendBidAsync(
        //     string accessToken,
        //     BidRecommendRequest body,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Check whether certain settings are eligible for Value-Based Optimization (VBO), and get the available VBO bidding strategies
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="objectiveType">Advertising objective</param>
        /// <param name="promotionType">Promotion type</param>
        /// <param name="placements">The apps where you want to deliver your ads</param>
        /// <param name="appPromotionType">App promotion type (required when objective_type is APP_PROMOTION)</param>
        /// <param name="campaignType">Campaign type</param>
        /// <param name="isAdvancedDedicatedCampaign">Whether the campaign is an Advanced Dedicated Campaign</param>
        /// <param name="disableSkanCampaign">Whether to disable SKAN (SKAdNetwork) attribution</param>
        /// <param name="bidAlignType">The attribution type for the Dedicated Campaign</param>
        /// <param name="ios14QuotaType">Whether the campaign will be counted towards the iOS 14 Dedicated Campaign quota</param>
        /// <param name="campaignAppProfilePageState">Whether to use App Profile Page at the campaign level to optimize delivery</param>
        /// <param name="pixelId">Pixel ID</param>
        /// <param name="appId">The Application ID of the promoted app</param>
        /// <param name="optimizationEvent">Conversion event for the ad group</param>
        /// <param name="storeId">ID of the TikTok Shop</param>
        /// <param name="isSmartPerformanceCampaign">Whether the campaign is a Smart+ Campaign or not</param>
        /// <param name="budgetOptimizeOn">Whether to enable Campaign Budget Optimization (CBO)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing VBO status information</returns>
        Task<Response<VboStatusResponse>> GetVboStatusAsync(
            string accessToken,
            string advertiserId,
            string objectiveType,
            string promotionType,
            List<string> placements,
            string? appPromotionType = null,
            string? campaignType = null,
            bool? isAdvancedDedicatedCampaign = null,
            bool? disableSkanCampaign = null,
            string? bidAlignType = null,
            string? ios14QuotaType = null,
            string? campaignAppProfilePageState = null,
            string? pixelId = null,
            string? appId = null,
            string? optimizationEvent = null,
            string? storeId = null,
            bool? isSmartPerformanceCampaign = null,
            bool? budgetOptimizeOn = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the authorization status of your Brand Safety partner
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="partner">Brand Safety post bid measurement partner</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing brand safety partner status</returns>
        Task<Response<BrandSafetyPartnerStatusResponse>> GetBrandSafetyPartnerStatusAsync(
            string accessToken,
            string advertiserId,
            string partner,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Check whether a URL is a custom URL scheme, Apple's universal link, or Android App Link, and whether the URL is valid
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="url">The URL that you want to get verification results for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing URL validation results</returns>
        //Task<Response<UrlValidationResponse>> ValidateUrlAsync(
        //    string accessToken,
        //    string advertiserId,
        //    string url,
        //    CancellationToken cancellationToken = default);
    }
}
