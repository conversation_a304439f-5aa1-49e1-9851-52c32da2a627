/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Catalog Feeds endpoints
    /// </summary>
    public static class CatalogFeedsEndpoints
    {
        /// <summary>
        /// Create a feed endpoint (v1.3)
        /// </summary>
        public const string CreateFeed = "/catalog/feed/create/";

        /// <summary>
        /// Get feeds endpoint (v1.3)
        /// </summary>
        public const string GetFeeds = "/catalog/feed/get/";

        /// <summary>
        /// Update a feed endpoint (v1.3)
        /// </summary>
        public const string UpdateFeed = "/catalog/feed/update/";

        /// <summary>
        /// Delete a feed endpoint (v1.3)
        /// </summary>
        public const string DeleteFeed = "/catalog/feed/delete/";

        /// <summary>
        /// Get the log of a feed endpoint (v1.3)
        /// </summary>
        public const string GetFeedLog = "/catalog/feed/log/";

        /// <summary>
        /// Update the schedule status of a feed endpoint (v1.3)
        /// </summary>
        public const string UpdateFeedScheduleStatus = "/catalog/feed/schedule/status/update/";
    }
}
