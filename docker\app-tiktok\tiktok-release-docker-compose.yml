version: "3.4"

services:
  web:
    image: module/tiktok.web:${COMPOSE_VERSION:-"latest"}
    restart: on-failure
    environment:
      - ASPNETCORE_URLS=http://+:5000;
      - App__SelfUrl=${DOMAIN:-https://localhost:44383}
      - ConnectionStrings__Default=${CONNECTIONSTRING:-Server=127.0.0.1,1433;Database=TikTok;User=sa;Password=****************; 
      - ASPNETCORE_ENVIRONMENT=Production
      - DBMS_TYPE=${DBMS_TYPE:-SQLSERVER}
      - TikTok__Auth__RedirectUri=${DOMAIN:-https://localhost:44383}
      - NestJS__BaseUrl=${DOMAIN_ZALO:-http://localhost:3000}
      - Elsa__Server__BaseUrl=${DOMAIN:-https://localhost:44383}
      - B<PERSON>bStorage__BasePath=${BLOBSTORAGE:-./BlobFile}
      - TZ=Asia/Bangkok
    volumes:
      - ../certs:/root/certificate
      - ~/BlobfileTiktok:/app/BlobFile
    ports:
      - "${EXPOSED_PORT:-5000}:5000"
    expose:
      - "${EXPOSED_PORT:-5000}"
    extra_hosts:
      - "host.docker.internal:host-gateway"