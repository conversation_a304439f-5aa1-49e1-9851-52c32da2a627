/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Page operations
    /// </summary>
    public interface IPageApi
    {
        /// <summary>
        /// Get the Page ID. After a page is created, you can get the page ID and then use the page ID in your ads.
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting pages</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing page information</returns>
        Task<Response<PageGetResponse>> GetPagesAsync(
            string accessToken,
            PageGetRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a TikTok Instant Page (TIP) Editor SDK access token. The TIP Editor SDK access token is required for loading the TIP Editor SDK.
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for creating TIP SDK access token</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the TIP SDK access token</returns>
        //Task<Response<TipSdkAccessTokenCreateResponse>> CreateTipSdkAccessTokenAsync(
        //    string accessToken,
        //    TipSdkAccessTokenCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Verify whether a TikTok Instant Page (TIP) Editor SDK access token has expired.
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for validating TIP SDK access token</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing validation result</returns>
        //Task<Response<TipSdkAccessTokenValidateResponse>> ValidateTipSdkAccessTokenAsync(
        //    string accessToken,
        //    TipSdkAccessTokenValidateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Renew a TikTok Instant Page (TIP) Editor SDK access token. Once renewed, the same token will remain valid for another 24 hours.
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for renewing TIP SDK access token</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> RenewTipSdkAccessTokenAsync(
        //    string accessToken,
        //    TipSdkAccessTokenRenewRequest request,
        //    CancellationToken cancellationToken = default);
    }
}
