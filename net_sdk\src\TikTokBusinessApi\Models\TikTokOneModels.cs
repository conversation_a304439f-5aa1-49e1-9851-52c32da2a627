/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Creator Access Token Models

    /// <summary>
    /// Request body for obtaining Creator access token
    /// </summary>
    public class CreatorAccessTokenRequest
    {
        /// <summary>
        /// ID of your developer app
        /// </summary>
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Secret of your developer app
        /// </summary>
        [JsonPropertyName("client_secret")]
        public string ClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// The way of generating the access token
        /// </summary>
        [JsonPropertyName("grant_type")]
        public string GrantType { get; set; } = string.Empty;

        /// <summary>
        /// The authorization code generated by TikTok account holder authorization URL
        /// </summary>
        [JsonPropertyName("auth_code")]
        public string? AuthCode { get; set; }

        /// <summary>
        /// The redirect URL which the client will be directed to
        /// </summary>
        [JsonPropertyName("redirect_uri")]
        public string? RedirectUri { get; set; }

        /// <summary>
        /// Refresh token to renew an existing access token
        /// </summary>
        [JsonPropertyName("refresh_token")]
        public string? RefreshToken { get; set; }
    }

    /// <summary>
    /// Response for Creator access token operations
    /// </summary>
    public class CreatorAccessTokenResponse
    {
        /// <summary>
        /// The short-term token for permission verification
        /// </summary>
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// The type of token
        /// </summary>
        [JsonPropertyName("token_type")]
        public string TokenType { get; set; } = string.Empty;

        /// <summary>
        /// The scope of permissions granted to the access token
        /// </summary>
        [JsonPropertyName("scope")]
        public string Scope { get; set; } = string.Empty;

        /// <summary>
        /// The time remaining until the access token expires, measured in seconds
        /// </summary>
        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }

        /// <summary>
        /// The refresh token, which can be used to renew the access token
        /// </summary>
        [JsonPropertyName("refresh_token")]
        public string RefreshToken { get; set; } = string.Empty;

        /// <summary>
        /// The time remaining until the refresh token expires, measured in seconds
        /// </summary>
        [JsonPropertyName("refresh_token_expires_in")]
        public int RefreshTokenExpiresIn { get; set; }

        /// <summary>
        /// Application specific unique ID of the TikTok account
        /// </summary>
        [JsonPropertyName("open_id")]
        public string OpenId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request body for revoking Creator access token
    /// </summary>
    public class RevokeCreatorAccessTokenRequest
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Secret of your developer application
        /// </summary>
        [JsonPropertyName("client_secret")]
        public string ClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// The Creator access token that you want to revoke
        /// </summary>
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;
    }

    #endregion

    #region Creator Token Info Models

    /// <summary>
    /// Request body for getting Creator token info
    /// </summary>
    public class CreatorTokenInfoRequest
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// Authorized access token for permission verification
        /// </summary>
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for Creator token info
    /// </summary>
    public class CreatorTokenInfoResponse
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// Scope of permissions for the specified access token
        /// </summary>
        [JsonPropertyName("scope")]
        public string Scope { get; set; } = string.Empty;

        /// <summary>
        /// Application specific unique ID of the TikTok creator account
        /// </summary>
        [JsonPropertyName("creator_id")]
        public string CreatorId { get; set; } = string.Empty;
    }

    #endregion

    #region TTO Creator Marketplace Account Models

    /// <summary>
    /// Response for TTO Creator Marketplace accounts
    /// </summary>
    public class TtoCreatorMarketplaceAccountsResponse
    {
        /// <summary>
        /// The ID list of TikTok One Creator Marketplace accounts that authorized the developer app
        /// </summary>
        [JsonPropertyName("tto_tcm_account_ids")]
        public List<string> TtoTcmAccountIds { get; set; } = new List<string>();
    }

    /// <summary>
    /// Response for TTO account info
    /// </summary>
    public class TtoAccountInfoResponse
    {
        /// <summary>
        /// The ID of the TTO Creator Marketplace account
        /// </summary>
        [JsonPropertyName("tto_tcm_account_id")]
        public string TtoTcmAccountId { get; set; } = string.Empty;

        /// <summary>
        /// The name of the TTO Creator Marketplace account
        /// </summary>
        [JsonPropertyName("account_name")]
        public string AccountName { get; set; } = string.Empty;

        /// <summary>
        /// The time zone code of the TTO Creator Marketplace account
        /// </summary>
        [JsonPropertyName("timezone")]
        public string Timezone { get; set; } = string.Empty;

        /// <summary>
        /// The country or region code of the TTO Creator Marketplace account
        /// </summary>
        [JsonPropertyName("country")]
        public string Country { get; set; } = string.Empty;

        /// <summary>
        /// The business verification status of the TTO Creator Marketplace account
        /// </summary>
        [JsonPropertyName("business_verification_status")]
        public string BusinessVerificationStatus { get; set; } = string.Empty;
    }

    #endregion

    #region Creator Insights Models

    /// <summary>
    /// Creator rate information
    /// </summary>
    public class CreatorRate
    {
        /// <summary>
        /// Amount of the rate
        /// </summary>
        [JsonPropertyName("rate")]
        public decimal Rate { get; set; }

        /// <summary>
        /// Currency for the rate amount, in ISO 4217 code format
        /// </summary>
        [JsonPropertyName("currency")]
        public string Currency { get; set; } = string.Empty;
    }

    /// <summary>
    /// Creator label information
    /// </summary>
    public class CreatorLabel
    {
        /// <summary>
        /// The ID of the label
        /// </summary>
        [JsonPropertyName("label_id")]
        public string LabelId { get; set; } = string.Empty;

        /// <summary>
        /// The name of the label
        /// </summary>
        [JsonPropertyName("label_name")]
        public string LabelName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Audience demographic information
    /// </summary>
    public class AudienceDemographic
    {
        /// <summary>
        /// The demographic value (country, gender, age, device, usage)
        /// </summary>
        [JsonPropertyName("country")]
        public string? Country { get; set; }

        /// <summary>
        /// Gender demographic
        /// </summary>
        [JsonPropertyName("gender")]
        public string? Gender { get; set; }

        /// <summary>
        /// Age demographic
        /// </summary>
        [JsonPropertyName("age")]
        public string? Age { get; set; }

        /// <summary>
        /// Device demographic
        /// </summary>
        [JsonPropertyName("device")]
        public string? Device { get; set; }

        /// <summary>
        /// Usage demographic
        /// </summary>
        [JsonPropertyName("usage")]
        public string? Usage { get; set; }

        /// <summary>
        /// Percentage of this demographic
        /// </summary>
        [JsonPropertyName("percentage")]
        public decimal Percentage { get; set; }
    }

    #endregion

    #region Authorized TTO Creator Insights Models

    /// <summary>
    /// Response for authorized TTO Creator insights
    /// </summary>
    public class AuthorizedTtoCreatorInsightsResponse
    {
        /// <summary>
        /// Temporary URL for the profile photo of the creator
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string ProfileImage { get; set; } = string.Empty;

        /// <summary>
        /// The username (handle) of the creator
        /// </summary>
        [JsonPropertyName("handle_name")]
        public string HandleName { get; set; } = string.Empty;

        /// <summary>
        /// The display name (nickname) for the creator
        /// </summary>
        [JsonPropertyName("display_name")]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// The bio description of the creator
        /// </summary>
        [JsonPropertyName("bio")]
        public string Bio { get; set; } = string.Empty;

        /// <summary>
        /// The total number of accounts that the creator is following
        /// </summary>
        [JsonPropertyName("following_count")]
        public int FollowingCount { get; set; }

        /// <summary>
        /// The total number of followers for the creator
        /// </summary>
        [JsonPropertyName("followers_count")]
        public int FollowersCount { get; set; }

        /// <summary>
        /// Total likes
        /// </summary>
        [JsonPropertyName("likes_count")]
        public int LikesCount { get; set; }

        /// <summary>
        /// The total number of public videos posted by the creator
        /// </summary>
        [JsonPropertyName("videos_count")]
        public int VideosCount { get; set; }

        /// <summary>
        /// Creator starting rate
        /// </summary>
        [JsonPropertyName("creator_rate")]
        public CreatorRate? CreatorRate { get; set; }

        /// <summary>
        /// The code of the country or region where the creator is located
        /// </summary>
        [JsonPropertyName("country_code")]
        public string CountryCode { get; set; } = string.Empty;

        /// <summary>
        /// The list of creator industry labels
        /// </summary>
        [JsonPropertyName("content_labels")]
        public List<CreatorLabel> ContentLabels { get; set; } = new List<CreatorLabel>();

        /// <summary>
        /// The list of content tag labels
        /// </summary>
        [JsonPropertyName("industry_labels")]
        public List<CreatorLabel> IndustryLabels { get; set; } = new List<CreatorLabel>();

        /// <summary>
        /// Three countries or regions that have the largest audience size
        /// </summary>
        [JsonPropertyName("audience_countries")]
        public List<AudienceDemographic> AudienceCountries { get; set; } = new List<AudienceDemographic>();

        /// <summary>
        /// Audience gender information
        /// </summary>
        [JsonPropertyName("audience_genders")]
        public List<AudienceDemographic> AudienceGenders { get; set; } = new List<AudienceDemographic>();

        /// <summary>
        /// Audience age distribution
        /// </summary>
        [JsonPropertyName("audience_ages")]
        public List<AudienceDemographic> AudienceAges { get; set; } = new List<AudienceDemographic>();

        /// <summary>
        /// Audience device information
        /// </summary>
        [JsonPropertyName("audience_devices")]
        public List<AudienceDemographic> AudienceDevices { get; set; } = new List<AudienceDemographic>();

        /// <summary>
        /// Audience TikTok usage
        /// </summary>
        [JsonPropertyName("audience_usages")]
        public List<AudienceDemographic> AudienceUsages { get; set; } = new List<AudienceDemographic>();
    }

    #endregion

    #region Media Insights Models

    /// <summary>
    /// Video post information
    /// </summary>
    public class VideoPost
    {
        /// <summary>
        /// Unique identifier for the video
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// The display name (nickname) for the creator
        /// </summary>
        [JsonPropertyName("display_name")]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Temporary URL for profile photo of the creator
        /// </summary>
        [JsonPropertyName("thumbnail_url")]
        public string ThumbnailUrl { get; set; } = string.Empty;

        /// <summary>
        /// An embeddable link for this TikTok video
        /// </summary>
        [JsonPropertyName("embed_url")]
        public string EmbedUrl { get; set; } = string.Empty;

        /// <summary>
        /// Video description
        /// </summary>
        [JsonPropertyName("caption")]
        public string Caption { get; set; } = string.Empty;

        /// <summary>
        /// Total number of likes on the video
        /// </summary>
        [JsonPropertyName("likes")]
        public int Likes { get; set; }

        /// <summary>
        /// Total number of comments on the video
        /// </summary>
        [JsonPropertyName("comments")]
        public int Comments { get; set; }

        /// <summary>
        /// Total number of times the video has been shared
        /// </summary>
        [JsonPropertyName("shares")]
        public int Shares { get; set; }

        /// <summary>
        /// The number of times viewers watched your video
        /// </summary>
        [JsonPropertyName("video_views")]
        public int VideoViews { get; set; }

        /// <summary>
        /// The total number of times the video has been added to favorites
        /// </summary>
        [JsonPropertyName("favorites")]
        public int Favorites { get; set; }

        /// <summary>
        /// The time when the video was created, in the format of an Epoch/Unix timestamp in milliseconds
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for authorized TTO Media insights
    /// </summary>
    public class AuthorizedTtoMediaInsightsResponse
    {
        /// <summary>
        /// List of posts within the creator's TikTok account
        /// </summary>
        [JsonPropertyName("posts")]
        public List<VideoPost> Posts { get; set; } = new List<VideoPost>();

        /// <summary>
        /// Whether an additional page of data is available
        /// </summary>
        [JsonPropertyName("has_more")]
        public bool HasMore { get; set; }

        /// <summary>
        /// Timestamp cursor
        /// </summary>
        [JsonPropertyName("cursor")]
        public long Cursor { get; set; }
    }

    #endregion

    #region Public Account Insights Models

    /// <summary>
    /// Response for TTO Public Account insights
    /// </summary>
    public class TtoPublicAccountInsightsResponse
    {
        /// <summary>
        /// The unique handle name of the creator
        /// </summary>
        [JsonPropertyName("handle_name")]
        public string HandleName { get; set; } = string.Empty;

        /// <summary>
        /// The display name of the creator
        /// </summary>
        [JsonPropertyName("display_name")]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Temporary URL for profile photo of the creator
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string ProfileImage { get; set; } = string.Empty;

        /// <summary>
        /// The bio description of the creator
        /// </summary>
        [JsonPropertyName("bio")]
        public string Bio { get; set; } = string.Empty;

        /// <summary>
        /// The number of followers for the creator
        /// </summary>
        [JsonPropertyName("followers_count")]
        public int FollowersCount { get; set; }

        /// <summary>
        /// The number of accounts that the creator is following
        /// </summary>
        [JsonPropertyName("following_count")]
        public int FollowingCount { get; set; }

        /// <summary>
        /// The number of public videos posted by the creator
        /// </summary>
        [JsonPropertyName("videos_count")]
        public int VideosCount { get; set; }

        /// <summary>
        /// The number of likes on public videos posted by the creator
        /// </summary>
        [JsonPropertyName("likes_count")]
        public int LikesCount { get; set; }

        /// <summary>
        /// The unique ID of the creator
        /// </summary>
        [JsonPropertyName("creator_id")]
        public string CreatorId { get; set; } = string.Empty;

        /// <summary>
        /// Median views
        /// </summary>
        [JsonPropertyName("median_views")]
        public int MedianViews { get; set; }

        /// <summary>
        /// Engagement rate
        /// </summary>
        [JsonPropertyName("engagement_rate")]
        public decimal EngagementRate { get; set; }

        /// <summary>
        /// The starting price of the creator
        /// </summary>
        [JsonPropertyName("creator_price")]
        public decimal CreatorPrice { get; set; }

        /// <summary>
        /// The currency for the starting price, in ISO 4217 format
        /// </summary>
        [JsonPropertyName("currency")]
        public string Currency { get; set; } = string.Empty;

        /// <summary>
        /// The list of creator industry labels
        /// </summary>
        [JsonPropertyName("industry_labels")]
        public List<CreatorLabel> IndustryLabels { get; set; } = new List<CreatorLabel>();

        /// <summary>
        /// The list of content tag labels
        /// </summary>
        [JsonPropertyName("content_labels")]
        public List<CreatorLabel> ContentLabels { get; set; } = new List<CreatorLabel>();
    }

    #endregion

    #region Creator Labels Models

    /// <summary>
    /// Response for TTO creator labels
    /// </summary>
    public class TtoCreatorLabelsResponse
    {
        /// <summary>
        /// The list of creator industry labels
        /// </summary>
        [JsonPropertyName("industry_labels")]
        public List<CreatorLabel> IndustryLabels { get; set; } = new List<CreatorLabel>();

        /// <summary>
        /// The list of content tag labels
        /// </summary>
        [JsonPropertyName("content_labels")]
        public List<CreatorLabel> ContentLabels { get; set; } = new List<CreatorLabel>();
    }

    #endregion

    #region Creator Rankings Models

    /// <summary>
    /// Creator ranking information
    /// </summary>
    public class CreatorRanking
    {
        /// <summary>
        /// The change in rank position for the creator compared to the rank in the last time period
        /// </summary>
        [JsonPropertyName("ranking_change")]
        public string RankingChange { get; set; } = string.Empty;

        /// <summary>
        /// The temporary URL for the profile image of the creator
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string ProfileImage { get; set; } = string.Empty;

        /// <summary>
        /// The code of the country or region where the creator is located
        /// </summary>
        [JsonPropertyName("country")]
        public string Country { get; set; } = string.Empty;

        /// <summary>
        /// The handle name of the creator
        /// </summary>
        [JsonPropertyName("handle")]
        public string Handle { get; set; } = string.Empty;

        /// <summary>
        /// The display name of the creator
        /// </summary>
        [JsonPropertyName("display_name")]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// The list of industry labels associated with the creator
        /// </summary>
        [JsonPropertyName("industry_labels")]
        public List<CreatorLabel>? IndustryLabels { get; set; }

        /// <summary>
        /// The list of content tag labels associated with the creator
        /// </summary>
        [JsonPropertyName("content_labels")]
        public List<CreatorLabel> ContentLabels { get; set; } = new List<CreatorLabel>();

        /// <summary>
        /// Score (commercial score or follower growth score)
        /// </summary>
        [JsonPropertyName("score")]
        public decimal Score { get; set; }

        /// <summary>
        /// Follower count
        /// </summary>
        [JsonPropertyName("number_of_followers")]
        public int NumberOfFollowers { get; set; }

        /// <summary>
        /// Branded content video views
        /// </summary>
        [JsonPropertyName("branded_content_video_views")]
        public int? BrandedContentVideoViews { get; set; }

        /// <summary>
        /// Branded content engagement rate
        /// </summary>
        [JsonPropertyName("branded_content_engagement_rate")]
        public decimal? BrandedContentEngagementRate { get; set; }

        /// <summary>
        /// Total video views
        /// </summary>
        [JsonPropertyName("total_video_views")]
        public int TotalVideoViews { get; set; }

        /// <summary>
        /// Engagement rate
        /// </summary>
        [JsonPropertyName("engagement_rate")]
        public decimal EngagementRate { get; set; }

        /// <summary>
        /// The creator's starting base price
        /// </summary>
        [JsonPropertyName("starting_price")]
        public int? StartingPrice { get; set; }

        /// <summary>
        /// The currency for the creator's starting base price
        /// </summary>
        [JsonPropertyName("starting_price_currency")]
        public string StartingPriceCurrency { get; set; } = string.Empty;

        /// <summary>
        /// The number of followers gained during the specified time period
        /// </summary>
        [JsonPropertyName("follower_growth")]
        public int? FollowerGrowth { get; set; }
    }



    /// <summary>
    /// Response for TTO creator rankings
    /// </summary>
    public class TtoCreatorRankingsResponse
    {
        /// <summary>
        /// The list of up to the top 100 creators, sorted by rank
        /// </summary>
        [JsonPropertyName("creators")]
        public List<CreatorRanking> Creators { get; set; } = new List<CreatorRanking>();

        /// <summary>
        /// The UTC+0 time when the ranking data was last updated
        /// </summary>
        [JsonPropertyName("last_updated_at")]
        public string LastUpdatedAt { get; set; } = string.Empty;

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new PageInfo();
    }

    #endregion

    #region Discover Creators Models

    /// <summary>
    /// Discovered creator information
    /// </summary>
    public class DiscoveredCreator
    {
        /// <summary>
        /// The handle name (username) of the creator
        /// </summary>
        [JsonPropertyName("handle_name")]
        public string HandleName { get; set; } = string.Empty;

        /// <summary>
        /// The display name of the creator
        /// </summary>
        [JsonPropertyName("display_name")]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Temporary URL for profile photo of the creator
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string ProfileImage { get; set; } = string.Empty;

        /// <summary>
        /// The total number of followers of the creator
        /// </summary>
        [JsonPropertyName("followers_count")]
        public int FollowersCount { get; set; }

        /// <summary>
        /// The total number of accounts that the creator follows on TikTok
        /// </summary>
        [JsonPropertyName("following_count")]
        public int FollowingCount { get; set; }

        /// <summary>
        /// The total number of times people have liked the creator's videos
        /// </summary>
        [JsonPropertyName("likes_count")]
        public int LikesCount { get; set; }

        /// <summary>
        /// The total number of public videos posted by the creator
        /// </summary>
        [JsonPropertyName("videos_count")]
        public int VideosCount { get; set; }
    }

    /// <summary>
    /// Response for discover TTO creators
    /// </summary>
    public class DiscoverTtoCreatorsResponse
    {
        /// <summary>
        /// Discovered creators
        /// </summary>
        [JsonPropertyName("creators")]
        public List<DiscoveredCreator> Creators { get; set; } = new List<DiscoveredCreator>();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new PageInfo();
    }

    #endregion
}
