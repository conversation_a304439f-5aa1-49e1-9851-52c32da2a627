/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for webhook list
    /// </summary>
    public class WebhookListResponse
    {
        /// <summary>
        /// The webhook URL that will receive the webhook events
        /// </summary>
        [JsonPropertyName("webhook_url")]
        public string WebhookUrl { get; set; } = string.Empty;

        /// <summary>
        /// List of webhook event types that you have subscribed to
        /// </summary>
        [JsonPropertyName("event_types")]
        public List<string> EventTypes { get; set; } = new List<string>();
    }
}
