/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response for getting store list
    /// </summary>
    public class StoreListResponse
    {
        /// <summary>
        /// Information about the available stores under the ad account
        /// </summary>
        [JsonPropertyName("stores")]
        public List<Store> Stores { get; set; } = new List<Store>();
    }

    /// <summary>
    /// Store information
    /// </summary>
    public class Store
    {
        /// <summary>
        /// ID of the Business Center that is authorized to access the store
        /// </summary>
        [JsonPropertyName("store_authorized_bc_id")]
        public string StoreAuthorizedBcId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_id")]
        public string StoreId { get; set; } = string.Empty;

        /// <summary>
        /// Store type. Enum values: TIKTOK_SHOP (TikTok Shop)
        /// </summary>
        [JsonPropertyName("store_type")]
        public string StoreType { get; set; } = string.Empty;

        /// <summary>
        /// Store name
        /// </summary>
        [JsonPropertyName("store_name")]
        public string StoreName { get; set; } = string.Empty;

        /// <summary>
        /// The shop code of the first-party store (TikTok Shop)
        /// </summary>
        [JsonPropertyName("store_code")]
        public string? StoreCode { get; set; }

        /// <summary>
        /// ID of the catalog that is bound to the store (deprecated)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }

        /// <summary>
        /// Region codes that the store can be used to target
        /// </summary>
        [JsonPropertyName("targeting_region_codes")]
        public List<string> TargetingRegionCodes { get; set; } = new List<string>();

        /// <summary>
        /// TikTok account binding status
        /// </summary>
        [JsonPropertyName("tiktok_account_binding_status")]
        public string? TikTokAccountBindingStatus { get; set; }
    }

    /// <summary>
    /// Response for getting store products
    /// </summary>
    public class StoreProductsResponse
    {
        /// <summary>
        /// The list of products within the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_products")]
        public List<StoreProduct> StoreProducts { get; set; } = new List<StoreProduct>();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Store product information
    /// </summary>
    public class StoreProduct
    {
        /// <summary>
        /// TikTok Shop ID
        /// </summary>
        [JsonPropertyName("store_id")]
        public string StoreId { get; set; } = string.Empty;

        /// <summary>
        /// SPU ID of the product
        /// </summary>
        [JsonPropertyName("item_group_id")]
        public string ItemGroupId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the catalog that the product belongs to (deprecated)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }

        /// <summary>
        /// The title of the product
        /// </summary>
        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// The URL of the product image
        /// </summary>
        [JsonPropertyName("product_image_url")]
        public string ProductImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// The minimum price of the product
        /// </summary>
        [JsonPropertyName("min_price")]
        public string MinPrice { get; set; } = string.Empty;

        /// <summary>
        /// The maximum price of the product
        /// </summary>
        [JsonPropertyName("max_price")]
        public string MaxPrice { get; set; } = string.Empty;

        /// <summary>
        /// The code of the currency in which the prices are specified
        /// </summary>
        [JsonPropertyName("currency")]
        public string Currency { get; set; } = string.Empty;

        /// <summary>
        /// The historical sales of the product
        /// </summary>
        [JsonPropertyName("historical_sales")]
        public int HistoricalSales { get; set; }

        /// <summary>
        /// The category of the product
        /// </summary>
        [JsonPropertyName("category")]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// The status of the product. Enum values: AVAILABLE, NOT_AVAILABLE
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// The status of the product in an enabled Product GMV Max Campaign
        /// </summary>
        [JsonPropertyName("gmv_max_ads_status")]
        public string? GmvMaxAdsStatus { get; set; }

        /// <summary>
        /// Whether the product is being promoted in enabled Video Shopping Ads or Product Shopping Ads
        /// </summary>
        [JsonPropertyName("is_running_custom_shop_ads")]
        public bool? IsRunningCustomShopAds { get; set; }
    }

    /// <summary>
    /// Filtering conditions for store products
    /// </summary>
    public class StoreProductFiltering
    {
        /// <summary>
        /// Product SPU (standard product unit) IDs. Max size: 10
        /// </summary>
        [JsonPropertyName("item_group_ids")]
        public List<string>? ItemGroupIds { get; set; }

        /// <summary>
        /// The type of ad or campaign that the products are eligible for
        /// </summary>
        [JsonPropertyName("ad_creation_eligible")]
        public string? AdCreationEligible { get; set; }

        /// <summary>
        /// Product name
        /// </summary>
        [JsonPropertyName("product_name")]
        public string? ProductName { get; set; }
    }
}
