using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu ngân sách
    /// </summary>
    public class BalanceSyncService : BaseSyncService, IBalanceSyncService
    {
        private readonly IBalanceBusinessCenterRepository _balanceBusinessCenterRepository;
        private readonly IBalanceAdAccountRepository _balanceAdAccountRepository;
        private readonly IRepository<RawBalanceBusinessCenterEntity, Guid> _balanceBusinessCenterEntityRepository;
        private readonly IRepository<RawBalanceAdAccountEntity, Guid> _balanceAdAccountEntityRepository;

        public BalanceSyncService(
            IServiceProvider serviceProvider,
            IBalanceBusinessCenterRepository balanceBusinessCenterRepository,
            IBalanceAdAccountRepository balanceAdAccountRepository,
            IRepository<RawBalanceBusinessCenterEntity, Guid> balanceBusinessCenterEntityRepository,
            IRepository<RawBalanceAdAccountEntity, Guid> balanceAdAccountEntityRepository,
            ILogger<BalanceSyncService> logger) : base(serviceProvider, logger)
        {
            _balanceBusinessCenterRepository = balanceBusinessCenterRepository;
            _balanceAdAccountRepository = balanceAdAccountRepository;
            _balanceBusinessCenterEntityRepository = balanceBusinessCenterEntityRepository;
            _balanceAdAccountEntityRepository = balanceAdAccountEntityRepository;
        }

        /// <summary>
        /// Đồng bộ ngân sách Business Center theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BalanceSyncResult> SyncBcBalanceAsync(string bcId)
        {
            var result = new BalanceSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ ngân sách BC cho BC: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // 3. Đồng bộ ngân sách BC
                var bcBalanceResult = await SyncBcBalanceFromApiAsync(tikTokClient, bcId);
                result.TotalSynced = bcBalanceResult.TotalSynced;
                result.NewRecords = bcBalanceResult.NewRecords;
                result.UpdatedRecords = bcBalanceResult.UpdatedRecords;
                result.BcCount = bcBalanceResult.BcCount;

                _logger.LogDebug("Hoàn thành đồng bộ ngân sách BC cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ ngân sách BC cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ ngân sách BC: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ ngân sách BC cho BC: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ ngân sách AdAccount theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BalanceSyncResult> SyncAdAccountBalanceAsync(string bcId)
        {
            var result = new BalanceSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ ngân sách AdAccount cho BC: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // 3. Đồng bộ ngân sách AdAccount
                var adAccountBalanceResult = await SyncAdAccountBalanceFromApiAsync(tikTokClient, bcId);
                result.TotalSynced = adAccountBalanceResult.TotalSynced;
                result.NewRecords = adAccountBalanceResult.NewRecords;
                result.UpdatedRecords = adAccountBalanceResult.UpdatedRecords;
                result.AdAccountCount = adAccountBalanceResult.AdAccountCount;

                _logger.LogDebug("Hoàn thành đồng bộ ngân sách AdAccount cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (BusinessException ex)
            {
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ ngân sách AdAccount cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ ngân sách AdAccount: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ ngân sách AdAccount cho BC: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả ngân sách (BC và AdAccount) theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BalanceSyncResult> SyncAllBalancesAsync(string bcId)
        {
            var result = new BalanceSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả ngân sách cho BC: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // 3. Đồng bộ ngân sách BC
                var bcBalanceResult = await SyncBcBalanceFromApiAsync(tikTokClient, bcId);

                // 4. Đồng bộ ngân sách AdAccount
                var adAccountBalanceResult = await SyncAdAccountBalanceFromApiAsync(tikTokClient, bcId);

                // 5. Tổng hợp kết quả
                result.TotalSynced = bcBalanceResult.TotalSynced + adAccountBalanceResult.TotalSynced;
                result.NewRecords = bcBalanceResult.NewRecords + adAccountBalanceResult.NewRecords;
                result.UpdatedRecords = bcBalanceResult.UpdatedRecords + adAccountBalanceResult.UpdatedRecords;
                result.BcCount = bcBalanceResult.BcCount;
                result.AdAccountCount = adAccountBalanceResult.AdAccountCount;

                _logger.LogDebug("Hoàn thành đồng bộ tất cả ngân sách cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}, AdAccount: {AdAccountCount}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount, result.AdAccountCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả ngân sách cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tất cả ngân sách: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả ngân sách cho BC: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả ngân sách cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BalanceSyncResult> SyncAllBalancesForAllBcsAsync()
        {
            var result = new BalanceSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả ngân sách cho tất cả BC");

                // Lấy tất cả Business Applications active
                var allBusinessApplications = await _businessApplicationCache.GetAllActiveAsync();
                var bcGroups = allBusinessApplications.GroupBy(x => x.BcId);

                if (!bcGroups.Any())
                {
                    result.ErrorMessage = "Không tìm thấy ứng dụng Business active nào";
                    _logger.LogWarning(result.ErrorMessage);
                    return result;
                }

                // Đồng bộ từng BC
                foreach (var bcGroup in bcGroups)
                {
                    var bcId = bcGroup.Key;
                    try
                    {
                        var bcResult = await SyncAllBalancesAsync(bcId);
                        result.TotalSynced += bcResult.TotalSynced;
                        result.NewRecords += bcResult.NewRecords;
                        result.UpdatedRecords += bcResult.UpdatedRecords;
                        result.BcCount += bcResult.BcCount;
                        result.AdAccountCount += bcResult.AdAccountCount;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ ngân sách cho BC: {BcId}", bcId);
                        // Tiếp tục với BC tiếp theo
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ tất cả ngân sách cho tất cả BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}, AdAccount: {AdAccountCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount, result.AdAccountCount);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tất cả ngân sách: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả ngân sách cho tất cả BC");
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ ngân sách BC từ TikTok API
        /// </summary>
        private async Task<BalanceSyncResult> SyncBcBalanceFromApiAsync(TikTokBusinessApiClient client, string bcId)
        {
            var result = new BalanceSyncResult();

            try
            {
                var request = new GetBcBalanceRequest
                {
                    BcId = bcId
                };

                var response = await client.BcPayments.GetBcBalanceAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy ngân sách BC: {response.Message}");
                }

                if (response?.Data == null)
                {
                    _logger.LogWarning("Không có dữ liệu ngân sách BC cho BC: {BcId}", bcId);
                    return result;
                }

                // Lấy thời gian hiện tại theo timezone và chuyển về UTC
                var utcDatetime = _dateTimeService.GetDateTimeUtcNowWithTimezone();
                var newBalance = MapApiBcBalanceToEntity(response.Data, bcId);
                // Gán thời gian hiện tại cho bản ghi mới
                newBalance.Date = utcDatetime.DateTime;
                newBalance.Timezone = utcDatetime.Timezone;

                // Lấy bản ghi mới nhất
                var latestBalance = await _balanceBusinessCenterRepository.GetLatestByBcIdAsync(bcId);
                var isChange = false;

                if (latestBalance == null)
                {
                    // Tạo mới nếu chưa có bản ghi nào
                    await _balanceBusinessCenterEntityRepository.InsertAsync(newBalance);
                    result.NewRecords = 1;
                    isChange = true;
                }
                else
                {
                    // So sánh dữ liệu để xem có thay đổi không và thời gian đồng bộ mới hơn
                    if (latestBalance.HasChanged(newBalance) && newBalance.Date > latestBalance.Date)
                    {
                        // Tạo bản ghi mới nếu có thay đổi
                        await _balanceBusinessCenterEntityRepository.InsertAsync(newBalance);
                        result.NewRecords = 1;
                        isChange = true;
                    }
                }

                result.TotalSynced = 1;
                result.BcCount = 1;

                _logger.LogDebug("Đồng bộ ngân sách BC thành công cho BC: {BcId}, Thời gian UTC: {DateTime}",
                    bcId, utcDatetime.DateTime.ToString("yyyy-MM-dd HH:mm:ss"));

                if (isChange)
                {
                    // TODO: Require a Message to Elsa
                }
            }
            catch (BusinessException bex)
            {
                _logger.LogError(bex, "Lỗi khi đồng bộ ngân sách BC từ API cho BC: {BcId}", bcId);
                result.Code = bex.Code ?? string.Empty;
                result.ErrorMessage = bex.Message;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ ngân sách AdAccount từ TikTok API
        /// </summary>
        private async Task<BalanceSyncResult> SyncAdAccountBalanceFromApiAsync(TikTokBusinessApiClient client, string bcId)
        {
            var result = new BalanceSyncResult();

            try
            {
                int page = 1;
                const int pageSize = 50;
                bool hasMoreData = true;

                while (hasMoreData)
                {
                    var request = new GetAdvertiserBalanceRequest
                    {
                        BcId = bcId,
                        Page = page,
                        PageSize = pageSize
                    };

                    var response = await client.BcPayments.GetAdvertiserBalanceAsync(request);
                    if (!TikTokApiCodes.IsSuccess(response.Code))
                    {
                        throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy ngân sách AdAccount: {response.Message}");
                    }

                    if (response?.Data?.AdvertiserAccountList == null || !response.Data.AdvertiserAccountList.Any())
                    {
                        hasMoreData = false;
                        break;
                    }

                    // Handle balance sync for page
                    _logger.LogDebug("Đồng bộ ngân sách AdAccount cho BC: {BcId}, Trang: {Page}", bcId, page);
                    await SyncBalanceForPage(bcId, page, result, response);
                    _logger.LogDebug("Đồng bộ ngân sách AdAccount cho BC: {BcId}, Trang: {Page}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                        bcId, page, result.TotalSynced, result.NewRecords, result.UpdatedRecords);

                    // Kiểm tra xem còn dữ liệu không
                    if (response.Data.AdvertiserAccountList.Count < pageSize)
                    {
                        hasMoreData = false;
                    }
                    else
                    {
                        page++;
                    }
                }
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        private async Task SyncBalanceForPage(string bcId, int page, BalanceSyncResult result, GetAdvertiserBalanceResponse response)
        {
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                // Tạo danh sách thêm mới
                var newBalances = new List<RawBalanceAdAccountEntity>();

                var newBalanceEntities = MapApiAdAccountBalanceToEntities(response.Data.AdvertiserAccountList, bcId);

                // advertiserIds
                var advertiserIds = newBalanceEntities.Select(x => x.AdvertiserId).ToList();

                // get latest balance by advertiserIds and date range
                var latestBalances = await _balanceAdAccountRepository.GetLatestByManyAdvertiserIdsAsync(advertiserIds);

                // Xử lý từng AdAccount balance
                foreach (var newBalance in newBalanceEntities)
                {
                    // Lấy bản ghi mới nhất
                    var latestBalance = latestBalances.FirstOrDefault(x => x.AdvertiserId == newBalance.AdvertiserId);
                    if (latestBalance == null)
                    {
                        // Tạo mới nếu chưa có bản ghi nào
                        newBalances.Add(newBalance);
                        result.NewRecords++;
                    }
                    else
                    {
                        // So sánh dữ liệu để xem có thay đổi không và thời gian đồng bộ mới hơn
                        if (latestBalance.HasChanged(newBalance) && newBalance.Date > latestBalance.Date)
                        {
                            // Tạo bản ghi mới nếu có thay đổi
                            newBalances.Add(newBalance);
                            result.NewRecords++;
                        }
                    }

                    result.TotalSynced++;
                    result.AdAccountCount++;
                }

                // Thêm mới
                if (newBalances.Any())
                {
                    await _balanceAdAccountEntityRepository.InsertManyAsync(newBalances);
                }
                await uow.CompleteAsync();
            }
        }

        /// <summary>
        /// Map dữ liệu ngân sách BC từ API sang Entity
        /// </summary>
        private RawBalanceBusinessCenterEntity MapApiBcBalanceToEntity(BcBalanceData apiBalance, string bcId)
        {
            return new RawBalanceBusinessCenterEntity
            {
                BcId = bcId,
                Currency = apiBalance.Currency ?? "USD",
                AccountBalance = apiBalance.AccountBalance,
                ValidAccountBalance = apiBalance.ValidAccountBalance,
                FrozenBalance = apiBalance.FrozenBalance,
                Tax = apiBalance.Tax,
                CashBalance = apiBalance.CashBalance,
                ValidCashBalance = apiBalance.ValidCashBalance,
                GrantBalance = apiBalance.GrantBalance,
                ValidGrantBalance = apiBalance.ValidGrantBalance,
            };
        }

        private List<RawBalanceAdAccountEntity> MapApiAdAccountBalanceToEntities(List<AdvertiserAccountBalance> apiBalances, string bcId)
        {
            var date = _dateTimeService.GetDateTimeUtcNow();
            return apiBalances.Select(x => MapApiAdAccountBalanceToEntity(x, bcId, date)).ToList();
        }

        /// <summary>
        /// Map dữ liệu ngân sách AdAccount từ API sang Entity
        /// </summary>
        private RawBalanceAdAccountEntity MapApiAdAccountBalanceToEntity(AdvertiserAccountBalance apiBalance, string bcId, DateTime? date = null)
        {

            if (date == null)
            {
                date = _dateTimeService.GetDateTimeUtcNow();
            }

            var createEntity = new RawBalanceAdAccountEntity(Guid.NewGuid())
            {
                AdvertiserId = apiBalance.AdvertiserId ?? "",
                AdvertiserName = apiBalance.AdvertiserName ?? "",
                AdvertiserStatus = Enum.TryParse(apiBalance.AdvertiserStatus, out AdvertiserAccountStatus result) ? result : AdvertiserAccountStatus.SHOW_ACCOUNT_STATUS_NOT_APPROVED,
                AdvertiserType = Enum.TryParse(apiBalance.AdvertiserType, out AdAccountType advertiserType) ? advertiserType : AdAccountType.RESERVATION,
                Timezone = apiBalance.Timezone ?? DateTimeService.UTC_TIMEZONE,
                Currency = apiBalance.Currency ?? "USD",
                AccountOpenDays = apiBalance.AccountOpenDays ?? 0,
                BalanceReminder = apiBalance.BalanceReminder ?? false,
                Company = apiBalance.Company,
                ContactName = apiBalance.ContactName,
                ContactEmail = apiBalance.ContactEmail,
                CreateTime = ParseDateTime(apiBalance.CreateTime) ?? date.Value,
                AccountBalance = apiBalance.AccountBalance ?? 0,
                ValidAccountBalance = apiBalance.ValidAccountBalance ?? 0,
                FrozenBalance = apiBalance.FrozenBalance ?? 0,
                Tax = apiBalance.Tax ?? 0,
                CashBalance = apiBalance.CashBalance ?? 0,
                ValidCashBalance = apiBalance.ValidCashBalance ?? 0,
                GrantBalance = apiBalance.GrantBalance ?? 0,
                ValidGrantBalance = apiBalance.ValidGrantBalance ?? 0,
                TransferableAmount = apiBalance.TransferableAmount,
                BudgetMode = Enum.TryParse(apiBalance.BudgetMode, out BudgetMode budgetMode) ? budgetMode : BudgetMode.UNLIMITED,
                Budget = apiBalance.Budget ?? 0,
                BudgetCost = apiBalance.BudgetCost ?? 0,
                BudgetRemaining = apiBalance.BudgetRemaining ?? 0,
                BcId = bcId,
                Date = date.Value,
                LatestRechargeTime = null, // Not available from API
                FirstRechargeAmount = 0, // Not available from API
                RechargeAmount = 0, // Not available from API
                RechargeCount = 0, // Not available from API
            };

            // map budget frequency restriction
            if (apiBalance.BudgetFrequencyRestriction != null)
            {
                createEntity.BudgetFrequencyRestriction = MapBudgetFrequencyRestriction(apiBalance.BudgetFrequencyRestriction);
            }
            // map budget amount restriction
            if (apiBalance.BudgetAmountRestriction != null)
            {
                createEntity.BudgetAmountRestriction = MapBudgetAmountRestriction(apiBalance.BudgetAmountRestriction);
            }
            // map min transferable amount
            if (apiBalance.MinTransferableAmount != null)
            {
                createEntity.MinTransferableAmount = MapMinTransferableAmount(apiBalance.MinTransferableAmount);
            }

            return createEntity;
        }

        private Entities.BudgetFrequencyRestriction MapBudgetFrequencyRestriction(TikTokBusinessApi.Models.BudgetFrequencyRestriction budgetFrequencyRestriction)
        {
            return new Entities.BudgetFrequencyRestriction
            {
                TotalCount = budgetFrequencyRestriction.TotalCount ?? 0,
                UsedCount = budgetFrequencyRestriction.UsedCount ?? 0,
                RemainingCount = budgetFrequencyRestriction.RemainingCount ?? 0,
                EffectiveStartTime = ParseDateTime(budgetFrequencyRestriction.EffectiveStartTime),
                EffectiveEndTime = ParseDateTime(budgetFrequencyRestriction.EffectiveEndTime)
            };
        }

        private Entities.BudgetAmountRestriction MapBudgetAmountRestriction(TikTokBusinessApi.Models.BudgetAmountRestriction budgetAmountRestriction)
        {
            return new Entities.BudgetAmountRestriction
            {
                MinimumAmount = budgetAmountRestriction.MinimumAmount
            };
        }

        private Entities.MinTransferableAmount MapMinTransferableAmount(TikTokBusinessApi.Models.MinTransferableAmount minTransferableAmount)
        {
            return new Entities.MinTransferableAmount
            {
                CashAmount = minTransferableAmount.CashAmount,
                GrantAmount = minTransferableAmount.GrantAmount,
                CreditAmount = minTransferableAmount.CreditAmount
            };
        }
    }
}