using System;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;

namespace TikTok.ReportIntegratedBcs
{
    /// <summary>
    /// DTO cho việc tạo mới báo cáo tích hợp Business Center
    /// </summary>
    public class CreateReportIntegratedBcDto
    {
        /// <summary>
        /// ID Business Center
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// Ngày tổng hợp báo cáo
        /// </summary>
        [Required]
        public DateTime Date { get; set; }

        /// <summary>
        /// Chi phí
        /// </summary>
        [Required]
        public decimal Spend { get; set; }

        /// <summary>
        /// Chi phí ròng
        /// </summary>
        [Required]
        public decimal BilledCost { get; set; }

        /// <summary>
        /// Chi phí (Tiền mặt)
        /// </summary>
        [Required]
        public decimal CashSpend { get; set; }

        /// <summary>
        /// Chi phí (Phiếu giảm giá)
        /// </summary>
        [Required]
        public decimal VoucherSpend { get; set; }

        /// <summary>
        /// Chi phí (Hoàn tiền)
        /// </summary>
        [Required]
        public decimal CashbackCouponSpend { get; set; }

        /// <summary>
        /// Chi phí (Thuế)
        /// </summary>
        [Required]
        public decimal TaxSpend { get; set; }

        /// <summary>
        /// CPC - Chi phí mỗi lượt nhấp
        /// </summary>
        [Required]
        public decimal Cpc { get; set; }

        /// <summary>
        /// CPM - Chi phí mỗi nghìn lượt hiển thị
        /// </summary>
        [Required]
        public decimal Cpm { get; set; }

        /// <summary>
        /// Lượt hiển thị
        /// </summary>
        [Required]
        public long Impressions { get; set; }

        /// <summary>
        /// Lượt nhấp
        /// </summary>
        [Required]
        public long Clicks { get; set; }

        /// <summary>
        /// CTR - Tỷ lệ nhấp
        /// </summary>
        [Required]
        public decimal Ctr { get; set; }

        /// <summary>
        /// Chuyển đổi
        /// </summary>
        [Required]
        public long Conversion { get; set; }

        /// <summary>
        /// CPA - Chi phí mỗi chuyển đổi
        /// </summary>
        [Required]
        public decimal CostPerConversion { get; set; }

        /// <summary>
        /// CVR - Tỷ lệ chuyển đổi
        /// </summary>
        [Required]
        public decimal ConversionRate { get; set; }

        /// <summary>
        /// Phạm vi tiếp cận
        /// </summary>
        [Required]
        public long Reach { get; set; }

        /// <summary>
        /// Chuyển đổi thời gian thực
        /// </summary>
        [Required]
        public long RealTimeConversion { get; set; }

        /// <summary>
        /// CPA thời gian thực
        /// </summary>
        [Required]
        public decimal RealTimeCostPerConversion { get; set; }

        /// <summary>
        /// CVR thời gian thực
        /// </summary>
        [Required]
        public decimal RealTimeConversionRate { get; set; }

        /// <summary>
        /// Chuyển đổi (SKAN)
        /// </summary>
        [Required]
        public long SkanConversion { get; set; }

        /// <summary>
        /// CPA (SKAN)
        /// </summary>
        [Required]
        public decimal SkanCostPerConversion { get; set; }

        /// <summary>
        /// CVR (SKAN)
        /// </summary>
        [Required]
        public decimal SkanConversionRate { get; set; }

        /// <summary>
        /// Lượt xem video 2 giây
        /// </summary>
        [Required]
        public long VideoWatched2s { get; set; }

        /// <summary>
        /// Lượt xem video 6 giây
        /// </summary>
        [Required]
        public long VideoWatched6s { get; set; }

        /// <summary>
        /// Lượt xem video 100%
        /// </summary>
        [Required]
        public long VideoViewsP100 { get; set; }

        /// <summary>
        /// Lượt xem video 75%
        /// </summary>
        [Required]
        public long VideoViewsP75 { get; set; }

        /// <summary>
        /// Lượt xem video 50%
        /// </summary>
        [Required]
        public long VideoViewsP50 { get; set; }

        /// <summary>
        /// Lượt xem video 25%
        /// </summary>
        [Required]
        public long VideoViewsP25 { get; set; }
    }
}