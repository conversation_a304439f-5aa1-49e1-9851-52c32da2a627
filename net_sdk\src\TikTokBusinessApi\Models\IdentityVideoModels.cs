/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Video Info Models

    /// <summary>
    /// Video information
    /// </summary>
    public class VideoInfo
    {
        /// <summary>
        /// The type of TikTok post
        /// Enum values: VIDEO (video post), CAROUSEL (photo post)
        /// </summary>
        [JsonPropertyName("item_type")]
        public string ItemType { get; set; } = string.Empty;

        /// <summary>
        /// TikTok post ID
        /// </summary>
        [JsonPropertyName("item_id")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// Status of the TikTok post
        /// Enum values: ITEM_STATUS_HESITATE_RECOMMEND, STATUS_ONLY_FRIEND_SEE, ITEM_STATUS_ONLY_AUTHOR_SEE
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Text for the TikTok post
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; } = string.Empty;

        /// <summary>
        /// Authorization Information
        /// </summary>
        [JsonPropertyName("auth_info")]
        public AuthInfo? AuthInfo { get; set; }

        /// <summary>
        /// Information about anchors
        /// </summary>
        [JsonPropertyName("anchor_list")]
        public List<AnchorInfo>? AnchorList { get; set; }

        /// <summary>
        /// Information about the video post
        /// When item_type is CAROUSEL, the value of this field will be null.
        /// </summary>
        [JsonPropertyName("video_info")]
        public VideoDetailInfo? Video { get; set; }

        /// <summary>
        /// Information about the photo post
        /// When item_type is VIDEO, the value of this field will be null.
        /// </summary>
        [JsonPropertyName("carousel_info")]
        public CarouselInfo? CarouselInfo { get; set; }
    }

    /// <summary>
    /// Video detail information (used in video info response)
    /// </summary>
    public class VideoDetail
    {
        /// <summary>
        /// The type of TikTok post
        /// Enum values: VIDEO (video post), CAROUSEL (photo post)
        /// </summary>
        [JsonPropertyName("item_type")]
        public string ItemType { get; set; } = string.Empty;

        /// <summary>
        /// TikTok post ID
        /// </summary>
        [JsonPropertyName("item_id")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// Visibility status of the TikTok post
        /// Enum values: ITEM_STATUS_HESITATE_RECOMMEND, STATUS_ONLY_FRIEND_SEE, ITEM_STATUS_ONLY_AUTHOR_SEE
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Text for the TikTok post
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; } = string.Empty;

        /// <summary>
        /// Anchor information
        /// </summary>
        [JsonPropertyName("anchor_list")]
        public List<AnchorInfo>? AnchorList { get; set; }

        /// <summary>
        /// Authorization status (returned only when identity_type is set to AUTH_CODE)
        /// </summary>
        [JsonPropertyName("auth_info")]
        public AuthInfo? AuthInfo { get; set; }

        /// <summary>
        /// Information about the video post
        /// When item_type is CAROUSEL, the value of this field will be null.
        /// </summary>
        [JsonPropertyName("video_info")]
        public VideoDetailInfo? Video { get; set; }

        /// <summary>
        /// Information about the photo post
        /// When item_type is VIDEO, the value of this field will be null.
        /// </summary>
        [JsonPropertyName("carousel_info")]
        public CarouselInfo? CarouselInfo { get; set; }
    }

    /// <summary>
    /// Authorization information
    /// </summary>
    public class AuthInfo
    {
        /// <summary>
        /// Authorization status
        /// </summary>
        [JsonPropertyName("ad_auth_status")]
        public string AdAuthStatus { get; set; } = string.Empty;

        /// <summary>
        /// Time when the authorization code expires (UTC+0)
        /// Format: "YYYY-MM-DD HH:MM:SS"
        /// </summary>
        [JsonPropertyName("auth_end_time")]
        public string AuthEndTime { get; set; } = string.Empty;

        /// <summary>
        /// Time when the authorization code is valid from (UTC+0)
        /// Format: "YYYY-MM-DD HH:MM:SS"
        /// </summary>
        [JsonPropertyName("auth_start_time")]
        public string AuthStartTime { get; set; } = string.Empty;

        /// <summary>
        /// The time when the authorization starts (UTC+0)
        /// Format: "YYYY-MM-DD HH:MM:SS"
        /// </summary>
        [JsonPropertyName("invite_start_time")]
        public string InviteStartTime { get; set; } = string.Empty;

        /// <summary>
        /// The time when the authorization starts (UTC+0) - alternative field name
        /// Format: "YYYY-MM-DD HH:MM:SS"
        /// </summary>
        [JsonPropertyName("invite_start_time_stamp")]
        public string? InviteStartTimeStamp { get; set; }
    }

    /// <summary>
    /// Anchor information
    /// </summary>
    public class AnchorInfo
    {
        /// <summary>
        /// Anchor ID
        /// </summary>
        [JsonPropertyName("anchor_id")]
        public string AnchorId { get; set; } = string.Empty;

        /// <summary>
        /// Anchor title
        /// </summary>
        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Anchor status
        /// Enum values: CHECK_ING, CHECK_FAILED, CHECK_SUCCESS, CHECKING
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Anchor URL
        /// </summary>
        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// Regions that the ad can be delivered to
        /// This is a list of country or region codes
        /// </summary>
        [JsonPropertyName("product_regions")]
        public List<string>? ProductRegions { get; set; }
    }

    /// <summary>
    /// Video detail information
    /// </summary>
    public class VideoDetailInfo
    {
        /// <summary>
        /// Bit rates, in bps
        /// </summary>
        [JsonPropertyName("bit_rate")]
        public int BitRate { get; set; }

        /// <summary>
        /// Duration of the video, in seconds
        /// </summary>
        [JsonPropertyName("duration")]
        public double Duration { get; set; }

        /// <summary>
        /// Video size, in bytes
        /// </summary>
        [JsonPropertyName("size")]
        public int Size { get; set; }

        /// <summary>
        /// Video height
        /// </summary>
        [JsonPropertyName("height")]
        public int Height { get; set; }

        /// <summary>
        /// Video width
        /// </summary>
        [JsonPropertyName("width")]
        public int Width { get; set; }

        /// <summary>
        /// URL to the video poster
        /// It is valid for an hour. When it expires, you need to call the endpoint again to get a new URL.
        /// </summary>
        [JsonPropertyName("poster_url")]
        public string PosterUrl { get; set; } = string.Empty;

        /// <summary>
        /// MD5 for the video file
        /// </summary>
        [JsonPropertyName("signature")]
        public string Signature { get; set; } = string.Empty;

        /// <summary>
        /// Video preview link
        /// It is valid for an hour. When it expires, you need to get a new URL.
        /// </summary>
        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// Format of the video
        /// </summary>
        [JsonPropertyName("format")]
        public string Format { get; set; } = string.Empty;
    }

    /// <summary>
    /// Carousel (photo post) information
    /// </summary>
    public class CarouselInfo
    {
        /// <summary>
        /// Information about the images used in the photo post
        /// The images are returned in the same order as they are shown in the photo post.
        /// </summary>
        [JsonPropertyName("image_info")]
        public List<ImageInfo> ImageInfo { get; set; } = new();

        /// <summary>
        /// Information about the music used in the photo post
        /// If the photo post is directly published in the TikTok App without music, the value of this field will be null.
        /// </summary>
        [JsonPropertyName("music_info")]
        public MusicInfo? MusicInfo { get; set; }
    }

    /// <summary>
    /// Image information
    /// </summary>
    public class ImageInfo
    {
        /// <summary>
        /// Image ID
        /// </summary>
        [JsonPropertyName("image_id")]
        public string ImageId { get; set; } = string.Empty;

        /// <summary>
        /// The URL of the image
        /// Validity period: 90 days
        /// </summary>
        [JsonPropertyName("image_url")]
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// The height of the image, measured in pixels
        /// </summary>
        [JsonPropertyName("image_height")]
        public int ImageHeight { get; set; }

        /// <summary>
        /// The width of the image, measured in pixels
        /// </summary>
        [JsonPropertyName("image_width")]
        public int ImageWidth { get; set; }
    }

    /// <summary>
    /// Music information
    /// </summary>
    public class MusicInfo
    {
        /// <summary>
        /// Music ID
        /// </summary>
        [JsonPropertyName("music_id")]
        public string MusicId { get; set; } = string.Empty;

        /// <summary>
        /// The URL of the music
        /// Validity period: 90 days
        /// </summary>
        [JsonPropertyName("music_url")]
        public string MusicUrl { get; set; } = string.Empty;

        /// <summary>
        /// The duration of the music, in seconds
        /// </summary>
        [JsonPropertyName("music_duration")]
        public int MusicDuration { get; set; }
    }

    #endregion

    #region Live Video Models

    /// <summary>
    /// Live video information
    /// </summary>
    public class LiveVideoInfo
    {
        /// <summary>
        /// Live video ID
        /// </summary>
        [JsonPropertyName("live_id")]
        public string LiveId { get; set; } = string.Empty;

        /// <summary>
        /// Finish timestamp for a live video
        /// Example: "2023-12-07 08:13:44"
        /// </summary>
        [JsonPropertyName("finish_timestamp")]
        public string FinishTimestamp { get; set; } = string.Empty;
    }

    #endregion
}
