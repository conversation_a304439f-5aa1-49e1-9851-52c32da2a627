/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Catalog Product Sets operations
    /// </summary>
    public interface ICatalogProductSetsApi
    {
        /// <summary>
        /// Get a list of product sets or one specified product set in a catalog under your Business Center
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting product sets</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing product sets information</returns>
        Task<GetProductSetsResponse> GetProductSetsAsync(
            string accessToken,
            GetProductSetsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get products in a product set
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting products in a set</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing products in the set</returns>
        Task<GetProductsInSetResponse> GetProductsInSetAsync(
            string accessToken,
            GetProductsInSetRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a product set in a catalog under your Business Center
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for creating a product set</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created product set information</returns>
        //Task<CreateProductSetResponse> CreateProductSetAsync(
        //    string accessToken,
        //    CreateProductSetRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update the filter conditions or name of a product set
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for updating a product set</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated product set information</returns>
        //Task<UpdateProductSetResponse> UpdateProductSetAsync(
        //    string accessToken,
        //    UpdateProductSetRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete product sets in a catalog under a Business Center
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for deleting product sets</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deleted product set IDs</returns>
        //Task<DeleteProductSetsResponse> DeleteProductSetsAsync(
        //    string accessToken,
        //    DeleteProductSetsRequest request,
        //    CancellationToken cancellationToken = default);
    }
}
