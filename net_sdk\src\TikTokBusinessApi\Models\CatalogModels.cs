/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for creating a catalog
    /// </summary>
    public class CatalogCreateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog name (max 128 characters)
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Catalog type
        /// </summary>
        [JsonPropertyName("catalog_type")]
        public string CatalogType { get; set; } = string.Empty;

        /// <summary>
        /// Catalog configuration information
        /// </summary>
        [JsonPropertyName("catalog_conf")]
        public CatalogConfiguration CatalogConf { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the CatalogCreateBody class
        /// </summary>
        public CatalogCreateBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogCreateBody class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="name">Catalog name</param>
        /// <param name="catalogType">Catalog type</param>
        /// <param name="catalogConf">Catalog configuration</param>
        public CatalogCreateBody(string bcId, string name, string catalogType, CatalogConfiguration catalogConf)
        {
            BcId = bcId;
            Name = name;
            CatalogType = catalogType;
            CatalogConf = catalogConf;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogCreateBody(BcId={BcId}, Name={Name}, CatalogType={CatalogType})";
        }
    }

    /// <summary>
    /// Catalog configuration information
    /// </summary>
    public class CatalogConfiguration
    {
        /// <summary>
        /// The code of the targeted region
        /// </summary>
        [JsonPropertyName("region_code")]
        public string RegionCode { get; set; } = string.Empty;

        /// <summary>
        /// Currency
        /// </summary>
        [JsonPropertyName("currency")]
        public string Currency { get; set; } = string.Empty;

        /// <summary>
        /// Channel for creating the catalog
        /// </summary>
        [JsonPropertyName("channel")]
        public string? Channel { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogConfiguration class
        /// </summary>
        public CatalogConfiguration()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogConfiguration class with parameters
        /// </summary>
        /// <param name="regionCode">Region code</param>
        /// <param name="currency">Currency</param>
        /// <param name="channel">Channel</param>
        public CatalogConfiguration(string regionCode, string currency, string? channel = null)
        {
            RegionCode = regionCode;
            Currency = currency;
            Channel = channel;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogConfiguration(RegionCode={RegionCode}, Currency={Currency}, Channel={Channel})";
        }
    }

    /// <summary>
    /// Request body for updating a catalog
    /// </summary>
    public class CatalogUpdateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// New catalog name (max 128 characters)
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the CatalogUpdateBody class
        /// </summary>
        public CatalogUpdateBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogUpdateBody class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="name">New catalog name</param>
        public CatalogUpdateBody(string bcId, string catalogId, string name)
        {
            BcId = bcId;
            CatalogId = catalogId;
            Name = name;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogUpdateBody(BcId={BcId}, CatalogId={CatalogId}, Name={Name})";
        }
    }

    /// <summary>
    /// Request body for deleting a catalog
    /// </summary>
    public class CatalogDeleteBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the CatalogDeleteBody class
        /// </summary>
        public CatalogDeleteBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogDeleteBody class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        public CatalogDeleteBody(string bcId, string catalogId)
        {
            BcId = bcId;
            CatalogId = catalogId;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogDeleteBody(BcId={BcId}, CatalogId={CatalogId})";
        }
    }

    /// <summary>
    /// Request body for migrating a catalog to a Business Center
    /// </summary>
    public class CatalogCapitalizeBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the CatalogCapitalizeBody class
        /// </summary>
        public CatalogCapitalizeBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogCapitalizeBody class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="catalogId">Catalog ID</param>
        public CatalogCapitalizeBody(string bcId, string advertiserId, string catalogId)
        {
            BcId = bcId;
            AdvertiserId = advertiserId;
            CatalogId = catalogId;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogCapitalizeBody(BcId={BcId}, AdvertiserId={AdvertiserId}, CatalogId={CatalogId})";
        }
    }

    /// <summary>
    /// Response data for catalog operations (create, update, delete)
    /// </summary>
    public class CatalogOperationResponse
    {
        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the CatalogOperationResponse class
        /// </summary>
        public CatalogOperationResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogOperationResponse class with parameters
        /// </summary>
        /// <param name="catalogId">Catalog ID</param>
        public CatalogOperationResponse(string catalogId)
        {
            CatalogId = catalogId;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogOperationResponse(CatalogId={CatalogId})";
        }
    }

    /// <summary>
    /// Response data for getting catalogs
    /// </summary>
    public class CatalogGetResponse
    {
        /// <summary>
        /// List of catalogs
        /// </summary>
        [JsonPropertyName("list")]
        public List<CatalogInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogGetResponse class
        /// </summary>
        public CatalogGetResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogGetResponse class with parameters
        /// </summary>
        /// <param name="list">List of catalogs</param>
        /// <param name="pageInfo">Pagination information</param>
        public CatalogGetResponse(List<CatalogInfo>? list = null, PageInfo? pageInfo = null)
        {
            List = list;
            PageInfo = pageInfo;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogGetResponse(List={List?.Count} catalogs, PageInfo={PageInfo})";
        }
    }

    /// <summary>
    /// Catalog information
    /// </summary>
    public class CatalogInfo
    {
        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog name
        /// </summary>
        [JsonPropertyName("catalog_name")]
        public string CatalogName { get; set; } = string.Empty;

        /// <summary>
        /// Catalog type
        /// </summary>
        [JsonPropertyName("catalog_type")]
        public string CatalogType { get; set; } = string.Empty;

        /// <summary>
        /// Whether the catalog can be used in ads
        /// </summary>
        [JsonPropertyName("ad_creation_eligible")]
        public string? AdCreationEligible { get; set; }

        /// <summary>
        /// Creation time
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Update time
        /// </summary>
        [JsonPropertyName("update_time")]
        public string? UpdateTime { get; set; }

        /// <summary>
        /// Business Center information
        /// </summary>
        [JsonPropertyName("bc_info")]
        public CatalogBusinessCenterInfo? BcInfo { get; set; }

        /// <summary>
        /// Catalog configuration information
        /// </summary>
        [JsonPropertyName("catalog_conf")]
        public CatalogConfigurationInfo? CatalogConf { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogInfo class
        /// </summary>
        public CatalogInfo()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogInfo(CatalogId={CatalogId}, CatalogName={CatalogName}, CatalogType={CatalogType})";
        }
    }

    /// <summary>
    /// Business Center information for catalogs
    /// </summary>
    public class CatalogBusinessCenterInfo
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Business Center name
        /// </summary>
        [JsonPropertyName("bc_name")]
        public string BcName { get; set; } = string.Empty;

        /// <summary>
        /// URL of the profile image
        /// </summary>
        [JsonPropertyName("picture_url")]
        public string? PictureUrl { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogBusinessCenterInfo class
        /// </summary>
        public CatalogBusinessCenterInfo()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogBusinessCenterInfo(BcId={BcId}, BcName={BcName})";
        }
    }

    /// <summary>
    /// Catalog configuration information for responses
    /// </summary>
    public class CatalogConfigurationInfo
    {
        /// <summary>
        /// Targeted country
        /// </summary>
        [JsonPropertyName("country")]
        public string? Country { get; set; }

        /// <summary>
        /// Unit of currency
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// Channel for creating the catalog
        /// </summary>
        [JsonPropertyName("channel")]
        public string? Channel { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogConfigurationInfo class
        /// </summary>
        public CatalogConfigurationInfo()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogConfigurationInfo(Country={Country}, Currency={Currency}, Channel={Channel})";
        }
    }

    /// <summary>
    /// Response data for getting catalog lexicon
    /// </summary>
    public class CatalogLexiconResponse
    {
        /// <summary>
        /// List of catalog lexicons
        /// </summary>
        [JsonPropertyName("list")]
        public List<CatalogLexiconInfo>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogLexiconResponse class
        /// </summary>
        public CatalogLexiconResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogLexiconResponse class with parameters
        /// </summary>
        /// <param name="list">List of catalog lexicons</param>
        public CatalogLexiconResponse(List<CatalogLexiconInfo>? list = null)
        {
            List = list;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogLexiconResponse(List={List?.Count} lexicons)";
        }
    }

    /// <summary>
    /// Catalog lexicon information
    /// </summary>
    public class CatalogLexiconInfo
    {
        /// <summary>
        /// Lexicon ID
        /// </summary>
        [JsonPropertyName("lexicon_id")]
        public string LexiconId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Lexicon name
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Lexicon type
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Metadata field
        /// </summary>
        [JsonPropertyName("metadata")]
        public string Metadata { get; set; } = string.Empty;

        /// <summary>
        /// Default word
        /// </summary>
        [JsonPropertyName("default_value")]
        public string DefaultValue { get; set; } = string.Empty;

        /// <summary>
        /// Update time
        /// </summary>
        [JsonPropertyName("update_time")]
        public string? UpdateTime { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogLexiconInfo class
        /// </summary>
        public CatalogLexiconInfo()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogLexiconInfo(LexiconId={LexiconId}, Name={Name}, Type={Type})";
        }
    }

    /// <summary>
    /// Response data for getting available countries/regions
    /// </summary>
    public class CatalogAvailableCountryResponse
    {
        /// <summary>
        /// List of region codes
        /// </summary>
        [JsonPropertyName("region_codes")]
        public List<string>? RegionCodes { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogAvailableCountryResponse class
        /// </summary>
        public CatalogAvailableCountryResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogAvailableCountryResponse class with parameters
        /// </summary>
        /// <param name="regionCodes">List of region codes</param>
        public CatalogAvailableCountryResponse(List<string>? regionCodes = null)
        {
            RegionCodes = regionCodes;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogAvailableCountryResponse(RegionCodes={RegionCodes?.Count} regions)";
        }
    }

    /// <summary>
    /// Response data for getting location and currency information
    /// </summary>
    public class CatalogLocationCurrencyResponse
    {
        /// <summary>
        /// List of location and currency information
        /// </summary>
        [JsonPropertyName("list")]
        public List<LocationCurrencyInfo>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogLocationCurrencyResponse class
        /// </summary>
        public CatalogLocationCurrencyResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogLocationCurrencyResponse class with parameters
        /// </summary>
        /// <param name="list">List of location and currency information</param>
        public CatalogLocationCurrencyResponse(List<LocationCurrencyInfo>? list = null)
        {
            List = list;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogLocationCurrencyResponse(List={List?.Count} locations)";
        }
    }

    /// <summary>
    /// Location and currency information
    /// </summary>
    public class LocationCurrencyInfo
    {
        /// <summary>
        /// Country or region code
        /// </summary>
        [JsonPropertyName("location")]
        public string Location { get; set; } = string.Empty;

        /// <summary>
        /// List of currency codes supported for the location
        /// </summary>
        [JsonPropertyName("currency")]
        public List<string>? Currency { get; set; }

        /// <summary>
        /// Initializes a new instance of the LocationCurrencyInfo class
        /// </summary>
        public LocationCurrencyInfo()
        {
        }

        /// <summary>
        /// Initializes a new instance of the LocationCurrencyInfo class with parameters
        /// </summary>
        /// <param name="location">Location code</param>
        /// <param name="currency">List of currency codes</param>
        public LocationCurrencyInfo(string location, List<string>? currency = null)
        {
            Location = location;
            Currency = currency;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"LocationCurrencyInfo(Location={Location}, Currency={Currency?.Count} currencies)";
        }
    }

    /// <summary>
    /// Response data for getting catalog overview
    /// </summary>
    public class CatalogOverviewResponse
    {
        /// <summary>
        /// Number of approved products in the catalog
        /// </summary>
        [JsonPropertyName("approved")]
        public int? Approved { get; set; }

        /// <summary>
        /// Number of rejected products in the catalog
        /// </summary>
        [JsonPropertyName("rejected")]
        public int? Rejected { get; set; }

        /// <summary>
        /// Number of processing products in the catalog
        /// </summary>
        [JsonPropertyName("processing")]
        public int? Processing { get; set; }

        /// <summary>
        /// Number of approved products bound with TikTok Shopping
        /// </summary>
        [JsonPropertyName("organic_approved")]
        public int? OrganicApproved { get; set; }

        /// <summary>
        /// Number of rejected products bound with TikTok Shopping
        /// </summary>
        [JsonPropertyName("organic_rejected")]
        public int? OrganicRejected { get; set; }

        /// <summary>
        /// Number of processing products bound with TikTok Shopping
        /// </summary>
        [JsonPropertyName("organic_processing")]
        public int? OrganicProcessing { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogOverviewResponse class
        /// </summary>
        public CatalogOverviewResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogOverviewResponse(Approved={Approved}, Rejected={Rejected}, Processing={Processing})";
        }
    }

    /// <summary>
    /// Request body for binding an event source to a catalog
    /// </summary>
    public class CatalogEventSourceBindBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Mobile application ID (optional)
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Website Pixel code (optional)
        /// </summary>
        [JsonPropertyName("pixel_code")]
        public string? PixelCode { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogEventSourceBindBody class
        /// </summary>
        public CatalogEventSourceBindBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogEventSourceBindBody class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="appId">Mobile application ID (optional)</param>
        /// <param name="pixelCode">Website Pixel code (optional)</param>
        public CatalogEventSourceBindBody(string advertiserId, string bcId, string catalogId, string? appId = null, string? pixelCode = null)
        {
            AdvertiserId = advertiserId;
            BcId = bcId;
            CatalogId = catalogId;
            AppId = appId;
            PixelCode = pixelCode;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogEventSourceBindBody(AdvertiserId={AdvertiserId}, BcId={BcId}, CatalogId={CatalogId}, AppId={AppId}, PixelCode={PixelCode})";
        }
    }

    /// <summary>
    /// Request body for unbinding an event source from a catalog
    /// </summary>
    public class CatalogEventSourceUnbindBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Mobile application ID (optional)
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Website Pixel code (optional)
        /// </summary>
        [JsonPropertyName("pixel_code")]
        public string? PixelCode { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogEventSourceUnbindBody class
        /// </summary>
        public CatalogEventSourceUnbindBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogEventSourceUnbindBody class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="appId">Mobile application ID (optional)</param>
        /// <param name="pixelCode">Website Pixel code (optional)</param>
        public CatalogEventSourceUnbindBody(string advertiserId, string bcId, string catalogId, string? appId = null, string? pixelCode = null)
        {
            AdvertiserId = advertiserId;
            BcId = bcId;
            CatalogId = catalogId;
            AppId = appId;
            PixelCode = pixelCode;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogEventSourceUnbindBody(AdvertiserId={AdvertiserId}, BcId={BcId}, CatalogId={CatalogId}, AppId={AppId}, PixelCode={PixelCode})";
        }
    }

    /// <summary>
    /// Event source information
    /// </summary>
    public class EventSource
    {
        /// <summary>
        /// Event source name
        /// </summary>
        [JsonPropertyName("event_source_name")]
        public string EventSourceName { get; set; } = string.Empty;

        /// <summary>
        /// Mobile application ID
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Website Pixel code
        /// </summary>
        [JsonPropertyName("pixel_code")]
        public string? PixelCode { get; set; }

        /// <summary>
        /// Initializes a new instance of the EventSource class
        /// </summary>
        public EventSource()
        {
        }

        /// <summary>
        /// Initializes a new instance of the EventSource class with parameters
        /// </summary>
        /// <param name="eventSourceName">Event source name</param>
        /// <param name="appId">Mobile application ID</param>
        /// <param name="pixelCode">Website Pixel code</param>
        public EventSource(string eventSourceName, string? appId = null, string? pixelCode = null)
        {
            EventSourceName = eventSourceName;
            AppId = appId;
            PixelCode = pixelCode;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"EventSource(EventSourceName={EventSourceName}, AppId={AppId}, PixelCode={PixelCode})";
        }
    }

    /// <summary>
    /// Response for getting event source binding information
    /// </summary>
    public class CatalogEventSourceBindResponse
    {
        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// List of event sources bound to the catalog
        /// </summary>
        [JsonPropertyName("event_sources")]
        public List<EventSource>? EventSources { get; set; }

        /// <summary>
        /// Initializes a new instance of the CatalogEventSourceBindResponse class
        /// </summary>
        public CatalogEventSourceBindResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the CatalogEventSourceBindResponse class with parameters
        /// </summary>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="eventSources">List of event sources</param>
        public CatalogEventSourceBindResponse(string catalogId, List<EventSource>? eventSources = null)
        {
            CatalogId = catalogId;
            EventSources = eventSources;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CatalogEventSourceBindResponse(CatalogId={CatalogId}, EventSources={EventSources?.Count} sources)";
        }
    }

    #region Feed Models

    /// <summary>
    /// Request body for creating a catalog feed
    /// </summary>
    public class FeedCreateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Name of the feed
        /// </summary>
        [JsonPropertyName("feed_name")]
        public string FeedName { get; set; } = string.Empty;

        /// <summary>
        /// The update mode. Enum values: OVERWRITE, INCREMENTAL
        /// </summary>
        [JsonPropertyName("update_mode")]
        public string UpdateMode { get; set; } = string.Empty;

        /// <summary>
        /// Schedule data
        /// </summary>
        [JsonPropertyName("schedule_param")]
        public FeedScheduleParam? ScheduleParam { get; set; }
    }

    /// <summary>
    /// Request body for updating a catalog feed
    /// </summary>
    public class FeedUpdateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string FeedId { get; set; } = string.Empty;

        /// <summary>
        /// The update mode. Enum values: OVERWRITE, INCREMENTAL, SUPPLEMENT
        /// </summary>
        [JsonPropertyName("update_mode")]
        public string UpdateMode { get; set; } = string.Empty;

        /// <summary>
        /// Schedule data
        /// </summary>
        [JsonPropertyName("schedule_param")]
        public FeedScheduleParam? ScheduleParam { get; set; }
    }

    /// <summary>
    /// Request body for deleting a catalog feed
    /// </summary>
    public class FeedDeleteBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string FeedId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request body for updating feed schedule status
    /// </summary>
    public class FeedScheduleUpdateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string FeedId { get; set; } = string.Empty;

        /// <summary>
        /// Feed schedule status. Enum values: ON, OFF
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// Schedule parameters for feed operations
    /// </summary>
    public class FeedScheduleParam
    {
        /// <summary>
        /// Feed source
        /// </summary>
        [JsonPropertyName("source")]
        public FeedSource? Source { get; set; }

        /// <summary>
        /// Schedule interval. Enum values: HOURLY, DAILY, MONTHLY
        /// </summary>
        [JsonPropertyName("interval_type")]
        public string? IntervalType { get; set; }

        /// <summary>
        /// Number of intervals between two consecutive feed runs
        /// </summary>
        [JsonPropertyName("interval_count")]
        public int? IntervalCount { get; set; }

        /// <summary>
        /// Time zone of the schedule
        /// </summary>
        [JsonPropertyName("timezone")]
        public string? Timezone { get; set; }

        /// <summary>
        /// For monthly schedules, the day of the month to fetch feed
        /// </summary>
        [JsonPropertyName("day_of_month")]
        public int? DayOfMonth { get; set; }

        /// <summary>
        /// Hour of the day to fetch feed (0-23)
        /// </summary>
        [JsonPropertyName("hour")]
        public int? Hour { get; set; }

        /// <summary>
        /// Minute of the day to fetch feed (0-59)
        /// </summary>
        [JsonPropertyName("minute")]
        public int? Minute { get; set; }
    }

    /// <summary>
    /// Feed source information
    /// </summary>
    public class FeedSource
    {
        /// <summary>
        /// Data Feed URL, the URL where your file is hosted
        /// </summary>
        [JsonPropertyName("uri")]
        public string? Uri { get; set; }

        /// <summary>
        /// Username for authentication if the feed is password protected
        /// </summary>
        [JsonPropertyName("username")]
        public string? Username { get; set; }

        /// <summary>
        /// Password for authentication if the feed is password protected
        /// </summary>
        [JsonPropertyName("password")]
        public string? Password { get; set; }
    }

    #endregion

    #region Feed Response Models

    /// <summary>
    /// Response for feed operations (create, update, delete)
    /// </summary>
    public class FeedOperationResponse
    {
        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string? FeedId { get; set; }

        /// <summary>
        /// Feed name
        /// </summary>
        [JsonPropertyName("feed_name")]
        public string? FeedName { get; set; }

        /// <summary>
        /// The schedule status of the feed. Enum values: ON, OFF
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Information about the last update
        /// </summary>
        [JsonPropertyName("last_update_param")]
        public FeedLastUpdateParam? LastUpdateParam { get; set; }

        /// <summary>
        /// Date and time for the next feed run
        /// </summary>
        [JsonPropertyName("next_update_time")]
        public string? NextUpdateTime { get; set; }

        /// <summary>
        /// Number of products in the feed
        /// </summary>
        [JsonPropertyName("number_of_products")]
        public int? NumberOfProducts { get; set; }
    }

    /// <summary>
    /// Response for getting feeds
    /// </summary>
    public class FeedGetResponse
    {
        /// <summary>
        /// Feed list
        /// </summary>
        [JsonPropertyName("feed_list")]
        public List<FeedInfo>? FeedList { get; set; }
    }

    /// <summary>
    /// Feed information
    /// </summary>
    public class FeedInfo
    {
        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string? FeedId { get; set; }

        /// <summary>
        /// Feed name
        /// </summary>
        [JsonPropertyName("feed_name")]
        public string? FeedName { get; set; }

        /// <summary>
        /// The schedule status of the feed. Enum values: ON, OFF
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Information about the last update
        /// </summary>
        [JsonPropertyName("last_update_param")]
        public FeedLastUpdateParam? LastUpdateParam { get; set; }

        /// <summary>
        /// Date and time for the next feed run
        /// </summary>
        [JsonPropertyName("next_update_time")]
        public string? NextUpdateTime { get; set; }

        /// <summary>
        /// Number of products in the feed
        /// </summary>
        [JsonPropertyName("number_of_products")]
        public int? NumberOfProducts { get; set; }
    }

    /// <summary>
    /// Last update parameters for a feed
    /// </summary>
    public class FeedLastUpdateParam
    {
        /// <summary>
        /// Data Feed URL, the URL where your file is hosted
        /// </summary>
        [JsonPropertyName("uri")]
        public string? Uri { get; set; }

        /// <summary>
        /// The update mode. Enum values: OVERWRITE, INCREMENTAL, SUPPLEMENT
        /// </summary>
        [JsonPropertyName("update_mode")]
        public string? UpdateMode { get; set; }

        /// <summary>
        /// Time zone of the schedule
        /// </summary>
        [JsonPropertyName("timezone")]
        public string? Timezone { get; set; }

        /// <summary>
        /// Schedule interval. Enum values: HOURLY, DAILY, MONTHLY
        /// </summary>
        [JsonPropertyName("interval_type")]
        public string? IntervalType { get; set; }

        /// <summary>
        /// Number of intervals between two consecutive feed runs
        /// </summary>
        [JsonPropertyName("interval_count")]
        public int? IntervalCount { get; set; }

        /// <summary>
        /// For monthly schedules, the day of the month to fetch feed
        /// </summary>
        [JsonPropertyName("day_of_month")]
        public int? DayOfMonth { get; set; }

        /// <summary>
        /// Hour of the day to fetch feed
        /// </summary>
        [JsonPropertyName("hour")]
        public int? Hour { get; set; }

        /// <summary>
        /// Minute of the day to fetch feed
        /// </summary>
        [JsonPropertyName("minute")]
        public int? Minute { get; set; }
    }

    /// <summary>
    /// Response for feed logs
    /// </summary>
    public class FeedLogResponse
    {
        /// <summary>
        /// Log entry list
        /// </summary>
        [JsonPropertyName("feed_logs")]
        public List<FeedLogEntry>? FeedLogs { get; set; }
    }

    /// <summary>
    /// Feed log entry
    /// </summary>
    public class FeedLogEntry
    {
        /// <summary>
        /// Update status information
        /// </summary>
        [JsonPropertyName("last_update_status")]
        public FeedUpdateStatus? LastUpdateStatus { get; set; }

        /// <summary>
        /// Update time information
        /// </summary>
        [JsonPropertyName("last_update_time")]
        public FeedUpdateTime? LastUpdateTime { get; set; }
    }

    /// <summary>
    /// Feed update status information
    /// </summary>
    public class FeedUpdateStatus
    {
        /// <summary>
        /// Number of products that were added
        /// </summary>
        [JsonPropertyName("add_count")]
        public int? AddCount { get; set; }

        /// <summary>
        /// Number of products that failed to add, update, or remove
        /// </summary>
        [JsonPropertyName("error_count")]
        public int? ErrorCount { get; set; }

        /// <summary>
        /// Number of products that were removed
        /// </summary>
        [JsonPropertyName("remove_count")]
        public int? RemoveCount { get; set; }

        /// <summary>
        /// Status of the update operation. Enum values: PROCESSING, SUCCESS, FAILED, WAITING
        /// </summary>
        [JsonPropertyName("process_status")]
        public string? ProcessStatus { get; set; }

        /// <summary>
        /// Number of products that were updated
        /// </summary>
        [JsonPropertyName("update_count")]
        public int? UpdateCount { get; set; }

        /// <summary>
        /// Number of warnings received
        /// </summary>
        [JsonPropertyName("warn_count")]
        public int? WarnCount { get; set; }
    }

    /// <summary>
    /// Feed update time information
    /// </summary>
    public class FeedUpdateTime
    {
        /// <summary>
        /// Date and time when the update operation ended
        /// </summary>
        [JsonPropertyName("end_time")]
        public string? EndTime { get; set; }

        /// <summary>
        /// Date and time when the update operation started
        /// </summary>
        [JsonPropertyName("start_time")]
        public string? StartTime { get; set; }
    }

    #endregion

    #region Product Set Response Models

    /// <summary>
    /// Product set information
    /// </summary>
    public class ProductSetInfo
    {
        /// <summary>
        /// Product set ID
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string ProductSetId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Product set name
        /// </summary>
        [JsonPropertyName("product_set_name")]
        public string ProductSetName { get; set; } = string.Empty;

        /// <summary>
        /// Number of products in the product set (returned when return_product_count is true)
        /// </summary>
        [JsonPropertyName("product_count")]
        public int? ProductCount { get; set; }

        /// <summary>
        /// Filter conditions
        /// </summary>
        [JsonPropertyName("conditions")]
        public ProductSetConditions? Conditions { get; set; }
    }

    /// <summary>
    /// Response for getting product sets
    /// </summary>
    public class ProductSetGetResponse
    {
        /// <summary>
        /// List of product sets
        /// </summary>
        [JsonPropertyName("list")]
        public List<ProductSetInfo> List { get; set; } = new();
    }

    /// <summary>
    /// Response for creating a product set
    /// </summary>
    public class ProductSetCreateResponse
    {
        /// <summary>
        /// Product set ID
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string ProductSetId { get; set; } = string.Empty;

        /// <summary>
        /// Product set name
        /// </summary>
        [JsonPropertyName("product_set_name")]
        public string ProductSetName { get; set; } = string.Empty;

        /// <summary>
        /// Number of products in the product set
        /// </summary>
        [JsonPropertyName("product_count")]
        public int ProductCount { get; set; }
    }

    /// <summary>
    /// Response for updating a product set
    /// </summary>
    public class ProductSetUpdateResponse
    {
        /// <summary>
        /// Product set ID
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string ProductSetId { get; set; } = string.Empty;

        /// <summary>
        /// Product set name
        /// </summary>
        [JsonPropertyName("product_set_name")]
        public string ProductSetName { get; set; } = string.Empty;

        /// <summary>
        /// Number of products in the product set
        /// </summary>
        [JsonPropertyName("product_count")]
        public int ProductCount { get; set; }
    }

    /// <summary>
    /// Response for deleting product sets
    /// </summary>
    public class ProductSetDeleteResponse
    {
        /// <summary>
        /// IDs of the product sets that have been deleted
        /// </summary>
        [JsonPropertyName("product_set_ids")]
        public List<string> ProductSetIds { get; set; } = new();
    }

    /// <summary>
    /// Product information in a product set
    /// </summary>
    public class ProductSetProduct
    {
        /// <summary>
        /// Product ID
        /// </summary>
        [JsonPropertyName("product_id")]
        public long ProductId { get; set; }

        /// <summary>
        /// Product name
        /// </summary>
        [JsonPropertyName("product_name")]
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// SKU ID (for E-commerce catalog products)
        /// </summary>
        [JsonPropertyName("sku_id")]
        public string? SkuId { get; set; }

        /// <summary>
        /// Hotel ID (for hotel catalog products)
        /// </summary>
        [JsonPropertyName("hotel_id")]
        public string? HotelId { get; set; }

        /// <summary>
        /// Flight ID (for flight catalog products)
        /// </summary>
        [JsonPropertyName("flight_id")]
        public string? FlightId { get; set; }

        /// <summary>
        /// Destination ID (for destination catalog products)
        /// </summary>
        [JsonPropertyName("destination_id")]
        public string? DestinationId { get; set; }

        /// <summary>
        /// Vehicle ID (for Auto-Inventory or Auto-Model catalog products)
        /// </summary>
        [JsonPropertyName("vehicle_id")]
        public string? VehicleId { get; set; }
    }

    /// <summary>
    /// Response for getting products in a product set
    /// </summary>
    public class ProductSetProductGetResponse
    {
        /// <summary>
        /// Product set ID
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string ProductSetId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Number of products in the product set
        /// </summary>
        [JsonPropertyName("product_count")]
        public int ProductCount { get; set; }

        /// <summary>
        /// List of products
        /// </summary>
        [JsonPropertyName("products")]
        public List<ProductSetProduct> Products { get; set; } = new();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    #endregion
}
