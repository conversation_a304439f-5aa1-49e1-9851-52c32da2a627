# RawAdAccountEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawAdAccounts`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawAdAccounts`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho tài khoản quảng cáo (Ad Account). Lưu trữ thông tin chi tiết về tài khoản quảng cáo bao gồm thông tin cơ bản, cài đặt khu vực, thông tin liên hệ, gi<PERSON>y phép kinh doanh và số dư tài khoản.

## Bảng dữ liệu: RawAdAccount

| Tên Field             | Kiểu dữ liệu    | Bắt buộc | Độ dài | Mô tả                                                                                      |
| --------------------- | --------------- | -------- | ------ | ------------------------------------------------------------------------------------------ |
| Id                    | Guid            | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                                      |
| AdvertiserId          | string          | ✅       | 100    | ID duy nhất của tài khoản quảng cáo                                                        |
| OwnerBcId             | string          | ✅       | 100    | ID của Business Center sở hữu tài khoản                                                    |
| Status                | AdAccountStatus | ✅       | -      | Trạng thái tài khoản                                                                       |
| Role                  | AdAccountRole   | ✅       | -      | Vai trò tài khoản                                                                          |
| RejectionReason       | string          | ❌       | 500    | Lý do bị từ chối (nếu có)                                                                  |
| Name                  | string          | ✅       | 255    | Tên tài khoản quảng cáo                                                                    |
| Timezone              | string          | ✅       | 50     | Múi giờ gốc với GMT offset hoặc định dạng "Region/City" (để tham chiếu khi convert từ UTC) |
| DisplayTimezone       | string          | ❌       | 100    | Tên múi giờ hiển thị theo định dạng "Region/City"                                          |
| Company               | string          | ❌       | 255    | Tên công ty của tài khoản                                                                  |
| CompanyNameEditable   | bool            | ❌       | -      | Có thể chỉnh sửa tên công ty qua API hay không                                             |
| Industry              | string          | ❌       | 50     | Mã danh mục ngành nghề                                                                     |
| Address               | string          | ❌       | 500    | Địa chỉ tài khoản                                                                          |
| Country               | string          | ✅       | 10     | Mã quốc gia đăng ký (ví dụ: US, CN)                                                        |
| AdvertiserAccountType | AdAccountType   | ✅       | -      | Loại tài khoản                                                                             |
| Currency              | string          | ✅       | 10     | Loại tiền tệ theo mã ISO 4217 (ví dụ: USD, EUR)                                            |
| Contacter             | string          | ❌       | 255    | Tên người liên hệ (đã che)                                                                 |
| Email                 | string          | ❌       | 255    | Email liên hệ (đã che)                                                                     |
| CellphoneNumber       | string          | ❌       | 20     | Số điện thoại di động (đã che)                                                             |
| TelephoneNumber       | string          | ❌       | 20     | Số điện thoại cố định (đã che)                                                             |
| Language              | string          | ✅       | 10     | Mã ngôn ngữ sử dụng (ví dụ: en, zh)                                                        |
| LicenseNo             | string          | ❌       | 100    | Số giấy phép kinh doanh                                                                    |
| LicenseUrl            | string          | ❌       | 500    | URL xem trước giấy phép (có hiệu lực 1 giờ)                                                |
| Description           | string          | ❌       | 1000   | Mô tả thương hiệu/công ty                                                                  |
| Balance               | decimal         | ❌       | -      | Số dư khả dụng của tài khoản                                                               |
| CreateTime            | DateTime        | ❌       | -      | Thời gian tạo tài khoản (lưu trữ dưới dạng UTC, được convert từ Unix timestamp)            |
| IsRemoved             | bool            | ❌       | -      | Cờ đánh dấu đã bị xóa khỏi Business Center                                                 |
| RemovedAt             | DateTime?       | ❌       | -      | Thời gian xóa khỏi Business Center (UTC)                                                   |
| CreationTime          | DateTime        | ✅       | -      | Thời gian tạo bản ghi (Audit)                                                              |
| CreatorId             | Guid?           | ❌       | -      | ID người tạo (Audit)                                                                       |
| LastModificationTime  | DateTime?       | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                                             |
| LastModifierId        | Guid?           | ❌       | -      | ID người sửa đổi cuối (Audit)                                                              |

## Enums liên quan

### AdAccountStatus

-   Định nghĩa trạng thái của tài khoản quảng cáo (active, inactive, suspended, etc.)

### AdAccountRole

-   Định nghĩa vai trò của tài khoản (owner, admin, member, etc.)

### AdAccountType

-   Định nghĩa loại tài khoản quảng cáo (individual, business, agency, etc.)

## Cấu trúc dữ liệu

### Thông tin định danh

-   **AdvertiserId**: ID duy nhất của tài khoản quảng cáo từ TikTok API
-   **OwnerBcId**: Liên kết với Business Center sở hữu
-   **Name**: Tên hiển thị của tài khoản quảng cáo
-   **Status**: Trạng thái hoạt động hiện tại
-   **Role**: Vai trò của tài khoản trong Business Center

### Thông tin công ty và địa chỉ

-   **Company**: Tên công ty sở hữu tài khoản
-   **CompanyNameEditable**: Có thể chỉnh sửa tên công ty hay không
-   **Industry**: Ngành nghề kinh doanh
-   **Address**: Địa chỉ đăng ký
-   **Country**: Quốc gia đăng ký
-   **Description**: Mô tả thương hiệu/công ty

### Cài đặt khu vực và ngôn ngữ

-   **Timezone**: Múi giờ gốc với GMT offset (để tham chiếu khi convert từ UTC)
-   **DisplayTimezone**: Tên múi giờ hiển thị
-   **Language**: Ngôn ngữ sử dụng
-   **Currency**: Đơn vị tiền tệ

### Thông tin liên hệ (đã che)

-   **Contacter**: Tên người liên hệ
-   **Email**: Email liên hệ
-   **CellphoneNumber**: Số điện thoại di động
-   **TelephoneNumber**: Số điện thoại cố định

### Giấy phép và tài chính

-   **LicenseNo**: Số giấy phép kinh doanh
-   **LicenseUrl**: URL xem trước giấy phép (có thời hạn)
-   **Balance**: Số dư khả dụng
-   **CreateTime**: Thời gian tạo tài khoản (lưu trữ dưới dạng UTC)

### Quản lý xóa khỏi Business Center

-   **IsRemoved**: Cờ đánh dấu đã bị xóa khỏi Business Center
-   **RemovedAt**: Thời gian xóa khỏi Business Center

### Thông tin bổ sung

-   **RejectionReason**: Lý do bị từ chối (nếu có)
-   **AdvertiserAccountType**: Loại tài khoản quảng cáo

## Mục đích sử dụng

-   Lưu trữ thông tin chi tiết về tài khoản quảng cáo
-   Quản lý thông tin công ty và địa chỉ
-   Theo dõi trạng thái và vai trò tài khoản
-   Quản lý thông tin liên hệ (đã bảo mật)
-   Theo dõi giấy phép kinh doanh
-   Quản lý số dư tài khoản
-   Quản lý trạng thái xóa khỏi Business Center
-   Làm cơ sở cho các entity khác liên quan đến tài khoản quảng cáo

## Mối quan hệ

-   **RawBusinessCenterEntity**: Tài khoản thuộc về Business Center (OwnerBcId)
-   **RawBalanceAdAccountEntity**: Thông tin số dư chi tiết của tài khoản
-   **RawCampaignEntity**: Chiến dịch thuộc về tài khoản
-   **RawTransactionEntity**: Giao dịch liên quan đến tài khoản

## Lưu ý

-   Entity này kế thừa từ AuditedEntity để có đầy đủ thông tin audit
-   AdvertiserId là khóa ngoại quan trọng để liên kết với các entity khác
-   Thông tin liên hệ được che để bảo mật
-   URL giấy phép có hiệu lực 1 giờ
-   Tất cả các trường thông tin liên hệ có thể null để đảm bảo tính linh hoạt
-   **CreateTime được lưu trữ dưới dạng UTC** để đảm bảo tính nhất quán về thời gian
-   **Timezone lưu trữ múi giờ gốc** để có thể convert ngược lại từ UTC khi cần thiết
-   Khi đồng bộ dữ liệu, CreateTime từ API (Unix timestamp) sẽ được parse và convert sang UTC trước khi lưu vào database
