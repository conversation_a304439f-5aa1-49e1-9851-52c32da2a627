using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Repositories;

namespace TikTok.Customers
{
    /// <summary>
    /// Service implementation để truy vấn dữ liệu khách hàng cho các Activity
    /// </summary>
    public class CustomerQueryService : ICustomerQueryService
    {
        private readonly ICustomerRepository _customerRepository;
        private readonly IAdAccountRepository _adAccountRepository;

        public CustomerQueryService(
            ICustomerRepository customerRepository,
            IAdAccountRepository adAccountRepository)
        {
            _customerRepository = customerRepository;
            _adAccountRepository = adAccountRepository;
        }

        public async Task<List<CustomerAdvertiserDto>> GetCustomersByAdvertiserIdsAsync(
            List<string> advertiserIds,
            bool includeAdAccounts = true)
        {
            if (advertiserIds == null || !advertiserIds.Any())
            {
                return await GetAllCustomersAsync(includeAdAccounts);
            }

            // Optimize: Only load customers that have the specified advertiser IDs
            var customers = await _customerRepository.WithDetailsAsync(x => x.AdAccounts);

            var filteredCustomers = customers
                .Where(x => x.AdAccounts.Any(adAccount => 
                    !string.IsNullOrEmpty(adAccount.AdvertiserId) && 
                    advertiserIds.Contains(adAccount.AdvertiserId)))
                .ToList();

            return await MapToCustomerAdvertiserDtosAsync(filteredCustomers, advertiserIds, includeAdAccounts);
        }

        public async Task<List<CustomerAdvertiserDto>> GetCustomersByBcIdAsync(
            string bcId,
            bool includeAdAccounts = true)
        {
            if (string.IsNullOrEmpty(bcId))
            {
                return new List<CustomerAdvertiserDto>();
            }
            
            // Optimize: Get advertiser IDs first, then filter customers
            var advertiserIds = await _adAccountRepository.GetByBcIdAsync(bcId);
            
            if (!advertiserIds.Any())
            {
                return new List<CustomerAdvertiserDto>();
            }

            return await GetCustomersByAdvertiserIdsAsync(advertiserIds, includeAdAccounts);
        }

        public async Task<List<CustomerAdvertiserDto>> GetAllCustomersAsync(bool includeAdAccounts = true)
        {
            // Optimize: Only load customers with their ad accounts in a single query
            var customers = (await _customerRepository.WithDetailsAsync(x => x.AdAccounts)).ToList();

            // Extract unique advertiser IDs for filtering
            var advertiserIds = customers
                .SelectMany(x => x.AdAccounts)
                .Where(x => !string.IsNullOrEmpty(x.AdvertiserId))
                .Select(x => x.AdvertiserId)
                .Distinct()
                .ToList();

            return await MapToCustomerAdvertiserDtosAsync(customers, advertiserIds, includeAdAccounts);
        }

        public async Task<CustomerAdvertiserDto?> GetCustomerByCustomerIdAsync(
            string customerId,
            bool includeAdAccounts = true)
        {
            if (string.IsNullOrEmpty(customerId))
            {
                return null;
            }

            // Optimize: Use specific method to get customer by ID with ad accounts
            var customer = await _customerRepository.GetByCustomerIdAsync(customerId);

            if (customer == null)
            {
                return null;
            }

            // Extract advertiser IDs from the customer's ad accounts
            var advertiserIds = customer.AdAccounts?
                .Where(x => !string.IsNullOrEmpty(x.AdvertiserId))
                .Select(x => x.AdvertiserId)
                .ToList() ?? new List<string>();

            var customerList = new List<CustomerEntity> { customer };
            var result = await MapToCustomerAdvertiserDtosAsync(customerList, advertiserIds, includeAdAccounts);

            return result.FirstOrDefault();
        }

        private async Task<List<CustomerAdvertiserDto>> MapToCustomerAdvertiserDtosAsync(
            List<CustomerEntity> customers,
            List<string> advertiserIds,
            bool includeAdAccounts)
        {
            var result = new List<CustomerAdvertiserDto>();

            foreach (var customer in customers)
            {
                if (customer.AdAccounts != null && customer.AdAccounts.Any())
                {
                    // Filter ad accounts by advertiser IDs if provided
                    var relevantAdAccounts = advertiserIds == null || !advertiserIds.Any()
                        ? customer.AdAccounts
                        : customer.AdAccounts.Where(adAccount => 
                            !string.IsNullOrEmpty(adAccount.AdvertiserId) && 
                            advertiserIds.Contains(adAccount.AdvertiserId));

                    foreach (var adAccount in relevantAdAccounts)
                    {
                        result.Add(new CustomerAdvertiserDto
                        {
                            CustomerId = customer.CustomerId,
                            CustomerName = customer.CustomerName,
                            AdvertiserId = includeAdAccounts ? adAccount.AdvertiserId : null,
                            AdvertiserName = includeAdAccounts ? adAccount.AdvertiserName : null
                        });
                    }
                }
                else
                {
                    // Khách hàng không có AdAccount nào
                    result.Add(new CustomerAdvertiserDto
                    {
                        CustomerId = customer.CustomerId,
                        CustomerName = customer.CustomerName,
                        AdvertiserId = null,
                        AdvertiserName = null
                    });
                }
            }

            return result;
        }
    }
}