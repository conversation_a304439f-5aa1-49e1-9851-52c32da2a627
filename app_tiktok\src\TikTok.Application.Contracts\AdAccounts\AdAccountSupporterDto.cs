﻿using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace TikTok.AdAccounts
{
    [Obsolete]
    public class AdAccountSupporterDto: EntityDto<Guid>
    {
        public string AdvertiserId { get; set; } // From TikTok API
        public Guid SupporterId { get; set; } // ABP User ID
        public DateTime AssignedDate { get; set; }
        public bool IsActive { get; set; }
        public Guid? AssignedByUserId { get; set; } // Who assigned this
        public SupporterRole Role { get; set; } // "Primary", "Secondary", "Viewer"
    }
}
