using System;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.JobManagement
{
    /// <summary>
    /// Interface cho service lưu trữ thời gian hoạt động của các job
    /// </summary>
    public interface IJobActivityService : ISingletonDependency
    {
        /// <summary>
        /// Cập nhật thời gian chạy cuối cùng của Manager Job
        /// </summary>
        /// <param name="lastRun">Thời gian chạy cuối cùng</param>
        Task UpdateLastManagerJobRunAsync(DateTime lastRun);

        /// <summary>
        /// Cập nhật thời gian chạy cuối cùng của Register Job
        /// </summary>
        /// <param name="lastRun">Thời gian chạy cuối cùng</param>
        Task UpdateLastRegisterJobRunAsync(DateTime lastRun);

        /// <summary>
        /// L<PERSON>y thời gian chạy cuối cùng của Manager Job
        /// </summary>
        /// <returns>Thời gian chạy cuối cùng</returns>
        Task<DateTime?> GetLastManagerJobRunAsync();

        /// <summary>
        /// Lấy thời gian chạy cuối cùng của Register Job
        /// </summary>
        /// <returns>Thời gian chạy cuối cùng</returns>
        Task<DateTime?> GetLastRegisterJobRunAsync();
    }
}