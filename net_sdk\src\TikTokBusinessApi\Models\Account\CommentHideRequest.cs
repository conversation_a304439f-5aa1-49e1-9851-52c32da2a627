/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for hiding or unhiding a comment on an owned video
    /// </summary>
    public class CommentHideRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for comment on an owned TikTok video to hide/unhide
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string CommentId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for owned TikTok video
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Specific operation to be performed on the comment - hide or unhide
        /// Enum values: HIDE, UNHIDE
        /// </summary>
        [JsonPropertyName("action")]
        public string Action { get; set; } = string.Empty;
    }
}
