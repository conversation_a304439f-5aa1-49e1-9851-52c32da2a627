/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Catalog Videos operations
    /// </summary>
    public class CatalogVideosApi : ICatalogVideosApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<CatalogVideosApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the CatalogVideosApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public CatalogVideosApi(IApiClient apiClient, ILogger<CatalogVideosApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Upload catalog videos via a file URL
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing upload parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing feed log ID</returns>
        //public async Task<Response<CatalogVideoUploadResponse>> UploadVideoFileAsync(
        //    string accessToken,
        //    CatalogVideoUploadBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));

        //    _logger?.LogInformation("Uploading catalog videos for catalog {CatalogId} in BC {BcId}", 
        //        body.CatalogId, body.BcId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<CatalogVideoUploadResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogVideosEndpoints.UploadVideoFile}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get the catalog video handling log
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting video log</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing video handling log details</returns>
        public async Task<Response<CatalogVideoLogResponse>> GetVideoLogAsync(
            string accessToken,
            CatalogVideoLogRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogInformation("Getting catalog video log {FeedLogId} for catalog {CatalogId}", 
                request.FeedLogId, request.CatalogId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["catalog_id"] = request.CatalogId,
                ["feed_log_id"] = request.FeedLogId
            };

            if (!string.IsNullOrWhiteSpace(request.Language))
            {
                queryParams["language"] = request.Language;
            }

            return await _apiClient.CallApiAsync<Response<CatalogVideoLogResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogVideosEndpoints.GetVideoLog}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the uploaded catalog videos within a catalog
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting videos</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of catalog videos</returns>
        public async Task<Response<CatalogVideosGetResponse>> GetVideosAsync(
            string accessToken,
            CatalogVideosGetRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogInformation("Getting catalog videos for catalog {CatalogId} in BC {BcId}", 
                request.CatalogId, request.BcId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["catalog_id"] = request.CatalogId
            };

            if (request.CatalogVideoIds != null && request.CatalogVideoIds.Count > 0)
            {
                queryParams["catalog_video_ids"] = string.Join(",", request.CatalogVideoIds);
            }
            else
            {
                if (request.Page.HasValue)
                {
                    queryParams["page"] = request.Page.Value.ToString();
                }

                if (request.PageSize.HasValue)
                {
                    queryParams["page_size"] = request.PageSize.Value.ToString();
                }
            }

            return await _apiClient.CallApiAsync<Response<CatalogVideosGetResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogVideosEndpoints.GetVideos}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Delete uploaded catalog videos
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing videos to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<object>> DeleteVideosAsync(
        //    string accessToken,
        //    CatalogVideosDeleteBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));

        //    _logger?.LogInformation("Deleting catalog videos for catalog {CatalogId} in BC {BcId}", 
        //        body.CatalogId, body.BcId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogVideosEndpoints.DeleteVideos}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}
    }
}
