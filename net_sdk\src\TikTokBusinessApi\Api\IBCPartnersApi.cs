/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API BC Partners operations
    /// </summary>
    public interface IBCPartnersApi
    {
        /// <summary>
        /// Get the list of partners of a Business Center. You need to be an Admin of the Business Center.
        /// </summary>
        /// <param name="request">Request parameters for getting partners</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the list of partners</returns>
        Task<GetPartnersResponse> GetPartnersAsync(
            GetPartnersRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Add a partner and optionally share your assets to a Business Center. A Business Center can only share assets it owns. You need to be an Admin of the Business Center.
        /// </summary>
        /// <param name="request">Request parameters for adding a partner</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response confirming the partner was added</returns>
        //Task<AddPartnerResponse> AddPartnerAsync(
        //    AddPartnerRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a partner in the Business Center. You need to be an Admin of the Business Center.
        /// </summary>
        /// <param name="request">Request parameters for deleting a partner</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response confirming the partner was deleted</returns>
        //Task<DeletePartnerResponse> DeletePartnerAsync(
        //    DeletePartnerRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Cancel the sharing of assets with a partner. You need to be an Admin of the Business Center.
        /// </summary>
        /// <param name="request">Request parameters for canceling asset sharing</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response confirming the asset sharing was canceled</returns>
        //Task<CancelAssetSharingResponse> CancelAssetSharingAsync(
        //    CancelAssetSharingRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get assets that you have shared with a partner, or assets that are shared by a partner. You need to be an Admin of the Business Center.
        /// </summary>
        /// <param name="request">Request parameters for getting partner assets</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the list of partner assets</returns>
        Task<GetPartnerAssetsResponse> GetPartnerAssetsAsync(
            GetPartnerAssetsRequest request,
            CancellationToken cancellationToken = default);
    }
}
