﻿using AutoMapper;
using TikTok.AdAccounts;
using TikTok.Assets;
using TikTok.BusinessApplications;
using TikTok.BusinessCenters;
using TikTok.Entities;
using TikTok.Transactions;
using TikTok.BalanceBusinessCenters;
using TikTok.BalanceAdAccounts;
using TikTok.RecordTransactionAdAccounts;
using TikTok.RecordTransactionBcs;
using TikTok.JobManagement;
using TikTok.Campaigns;
using TikTok.CostProfiles;
using TikTok.ReportIntegratedBcs;
using TikTok.ReportIntegratedCampaigns;
using TikTok.ReportIntegratedAdAccounts;
using TikTok.ReportIntegratedAdGroups;
using TikTok.ReportIntegratedAds;
using TikTok.Entities.Raws;
using TikTok.FactBalances;
using TikTok.DimBusinessCenters;
using TikTok.DimAdAccounts;
using TikTok.DimDates;
using TikTok.FactCampaigns.Dtos;
using TikTok.DimCampaigns;
using TikTok.Entities.AdAccounts;
using TikTok.Application.Contracts.Rules;
using TikTok.Domain.Entities.Rules;
using TikTok.Customers;
using TikTok.Application.Contracts.LatestNotifications;
using TikTok.ResourcePermissions;
using TikTok.Entities.Permissions;
using TikTok.Users;
using Volo.Abp.Identity;
using TikTok.Rules;
using TikTok.UserAccessManagement;
using Tsp.Zalo;
using TikTok.Entities.Dim;
using TikTok.DimProducts;
using TikTok.DimStores;
using TikTok.Domain.Entities.DataWarehouse.Fact;
using TikTok.FactGmvMaxCampaigns.Dtos;
using TikTok.FactDailySpends.Dtos;
using TikTok.FactGmvMaxProducts.Dtos;

namespace TikTok;

public class TikTokApplicationAutoMapperProfile : Profile
{
    public TikTokApplicationAutoMapperProfile()
    {
        // BusinessCenter mappings
        CreateMap<RawBusinessCenterEntity, BusinessCenterDto>();
        CreateMap<CreateBusinessCenterDto, RawBusinessCenterEntity>();
        CreateMap<UpdateBusinessCenterDto, RawBusinessCenterEntity>();

        // AdAccount mappings
        CreateMap<RawAdAccountEntity, AdAccountDto>().IgnoreAllNonExisting();
        CreateMap<CreateAdAccountDto, RawAdAccountEntity>();
        CreateMap<UpdateAdAccountDto, RawAdAccountEntity>();

        // AdAccountSupporter mappings
        CreateMap<AdAccountSupporterEntity, AdAccountSupporterDto>();

        // TODO: AdGroup mappings
        //CreateMap<RawAdGroupEntity, AdGroupDto>();
        //CreateMap<CreateAdGroupDto, RawAdGroupEntity>();
        //CreateMap<UpdateAdGroupDto, RawAdGroupEntity>();

        // Asset mappings
        CreateMap<RawAssetEntity, AssetDto>();
        CreateMap<CreateAssetDto, RawAssetEntity>();
        CreateMap<UpdateAssetDto, RawAssetEntity>();

        // Transaction mappings
        CreateMap<RawTransactionEntity, TransactionDto>();
        CreateMap<CreateTransactionDto, RawTransactionEntity>();
        CreateMap<UpdateTransactionDto, RawTransactionEntity>();

        // BusinessApplication mappings
        CreateMap<BusinessApplicationEntity, BusinessApplicationDto>().ForMember(x => x.BackupId, op => op.MapFrom(o => o.Id));
        CreateMap<CreateBusinessApplicationDto, BusinessApplicationEntity>();
        CreateMap<UpdateBusinessApplicationDto, BusinessApplicationEntity>();

        // BalanceBusinessCenter mappings
        CreateMap<RawBalanceBusinessCenterEntity, BalanceBusinessCenterDto>();
        CreateMap<CreateBalanceBusinessCenterDto, RawBalanceBusinessCenterEntity>();
        CreateMap<UpdateBalanceBusinessCenterDto, RawBalanceBusinessCenterEntity>();

        // BalanceAdvertiserAccount mappings
        CreateMap<RawBalanceAdAccountEntity, BalanceAdAccountDto>();
        CreateMap<CreateBalanceAdAccountDto, RawBalanceAdAccountEntity>();
        CreateMap<UpdateBalanceAdAccountDto, RawBalanceAdAccountEntity>();
        CreateMap<BudgetFrequencyRestriction, BudgetFrequencyRestrictionDto>();
        CreateMap<BudgetAmountRestriction, BudgetAmountRestrictionDto>();
        CreateMap<MinTransferableAmount, MinTransferableAmountDto>();
        CreateMap<BudgetFrequencyRestrictionDto, BudgetFrequencyRestriction>();
        CreateMap<BudgetAmountRestrictionDto, BudgetAmountRestriction>();
        CreateMap<MinTransferableAmountDto, MinTransferableAmount>();
        CreateMap<BalanceAdAccountStatisticsDto, BalanceAdAccountStatisticsDto>();

        // LatestBalanceAdAccount mappings
        CreateMap<RawLatestBalanceAdAccountEntity, LatestBalanceAdAccountDto>();
        CreateMap<BudgetFrequencyRestriction, BudgetFrequencyRestrictionDto>();
        CreateMap<BudgetAmountRestriction, BudgetAmountRestrictionDto>();
        CreateMap<MinTransferableAmount, MinTransferableAmountDto>();

        // LatestBalanceBusinessCenter mappings
        CreateMap<RawLatestBalanceBusinessCenterEntity, LatestBalanceBusinessCenterDto>();

        // RecordTransactionAdAccount mappings
        CreateMap<RawRecordTransactionAdAccountEntity, RecordTransactionAdAccountDto>();
        CreateMap<CreateRecordTransactionAdAccountDto, RawRecordTransactionAdAccountEntity>();
        CreateMap<UpdateRecordTransactionAdAccountDto, RawRecordTransactionAdAccountEntity>();

        // RecordTransactionBc mappings
        CreateMap<RawRecordTransactionBcEntity, RecordTransactionBcDto>();
        CreateMap<CreateRecordTransactionBcDto, RawRecordTransactionBcEntity>();
        CreateMap<UpdateRecordTransactionBcDto, RawRecordTransactionBcEntity>();

        // JobManagement mappings
        CreateMap<JobConfigurationEntity, JobConfigurationDto>();
        CreateMap<WorkerInfoEntity, WorkerInfoDto>();
        CreateMap<JobSystemStatusDto, JobSystemStatusDto>();
        CreateMap<JobTypeConfigurationEntity, JobTypeConfigurationDto>();

        // Campaign mappings
        CreateMap<RawCampaignEntity, CampaignDto>();
        CreateMap<CreateCampaignDto, RawCampaignEntity>();
        CreateMap<UpdateCampaignDto, RawCampaignEntity>();

        // CostProfile mappings
        CreateMap<RawCostProfileEntity, CostProfileDto>();
        CreateMap<CreateCostProfileDto, RawCostProfileEntity>();
        CreateMap<UpdateCostProfileDto, RawCostProfileEntity>();
        CreateMap<CostProfileDto, UpdateCostProfileDto>();

        // ReportIntegratedBc mappings
        CreateMap<RawReportIntegratedBcEntity, ReportIntegratedBcDto>();
        CreateMap<CreateReportIntegratedBcDto, RawReportIntegratedBcEntity>();
        CreateMap<UpdateReportIntegratedBcDto, RawReportIntegratedBcEntity>();

        // ReportIntegratedCampaign mappings
        CreateMap<RawReportIntegratedCampaignEntity, ReportIntegratedCampaignDto>();
        CreateMap<CreateReportIntegratedCampaignDto, RawReportIntegratedCampaignEntity>();
        CreateMap<UpdateReportIntegratedCampaignDto, RawReportIntegratedCampaignEntity>();

        // ReportIntegratedAdAccount mappings
        CreateMap<RawReportIntegratedAdAccountEntity, ReportIntegratedAdAccountDto>();
        CreateMap<CreateReportIntegratedAdAccountDto, RawReportIntegratedAdAccountEntity>();
        CreateMap<UpdateReportIntegratedAdAccountDto, RawReportIntegratedAdAccountEntity>();

        // ReportIntegratedAdGroup mappings
        CreateMap<RawReportIntegratedAdGroupEntity, ReportIntegratedAdGroupDto>();
        CreateMap<CreateReportIntegratedAdGroupDto, RawReportIntegratedAdGroupEntity>();
        CreateMap<UpdateReportIntegratedAdGroupDto, RawReportIntegratedAdGroupEntity>();

        // ReportIntegratedAd mappings
        CreateMap<RawReportIntegratedAdEntity, ReportIntegratedAdDto>();
        CreateMap<CreateReportIntegratedAdDto, RawReportIntegratedAdEntity>();
        CreateMap<UpdateReportIntegratedAdDto, RawReportIntegratedAdEntity>();
        // Có thể thêm các mapping khác ở đây

        // FactBalance mappings
        CreateMap<FactBalanceEntity, FactBalanceDto>();

        // DimBusinessCenter mappings
        CreateMap<DimBusinessCenterEntity, DimBusinessCenterDto>();

        // DimAdAccount mappings
        CreateMap<DimAdAccountEntity, DimAdAccountDto>();

        // DimDate mappings
        CreateMap<DimDateEntity, DimDateDto>();

        // DimCampaign mappings
        CreateMap<DimCampaignEntity, DimCampaignDto>();

        // DimProduct mappings
        CreateMap<DimProductEntity, DimProductDto>();

        // DimStore mappings
        CreateMap<DimStoreEntity, DimStoreDto>();

        // FactCampaign mappings
        CreateMap<FactCampaignEntity, FactCampaignDto>();

        // FactGmvMaxCampaign mappings
        CreateMap<FactGmvMaxCampaignEntity, FactGmvMaxCampaignDto>();

        // FactGmvMaxProduct mappings
        CreateMap<FactGmvMaxProductEntity, FactGmvMaxProductDto>();

        // FactDailySpend mappings
        CreateMap<FactDailySpendEntity, FactDailySpendDto>().IgnoreAllNonExisting();
        CreateMap<CreateUpdateFactDailySpendDto, FactDailySpendEntity>().ReverseMap();

        // Rule mappings
        CreateMap<RuleEntity, RuleDto>().ReverseMap();
        CreateMap<CreateOrUpdateRuleDto, RuleEntity>().ReverseMap();
        CreateMap<RuleAdAccountEntity, RuleAdAccountDto>().ReverseMap();

        // Customer mappings
        CreateMap<CustomerEntity, CustomerDto>();
        CreateMap<CreateCustomerDto, CustomerEntity>();
        CreateMap<UpdateCustomerDto, CustomerEntity>();
        CreateMap<CustomerDto, UpdateCustomerDto>();

        // Customer AdAccount mappings (One-to-Many relationship)
        CreateMap<TikTok.Entities.CustomerAdAccountEntity, TikTok.Customers.CustomerAdAccountDto>();
        CreateMap<TikTok.Customers.CreateCustomerAdAccountDto, TikTok.Entities.CustomerAdAccountEntity>();
        CreateMap<TikTok.Customers.UpdateCustomerAdAccountDto, TikTok.Entities.CustomerAdAccountEntity>();
        CreateMap<TikTok.Customers.CustomerAdAccountDto, TikTok.Customers.UpdateCustomerAdAccountDto>();

        // Customer AdAccount Flat mappings
        CreateMap<TikTok.Entities.CustomerAdAccountEntity, TikTok.Customers.CustomerAdAccountFlatDto>();

        // AdAccount Search Result mappings
        CreateMap<RawAdAccountEntity, AdAccountSearchResultDto>();

        // LatestNotification mappings
        CreateMap<LatestNotificationEntity, LatestNotificationDto>();

        // ResourcePermission mappings
        CreateMap<ResourcePermissionEntity, ResourcePermissionDto>();
        CreateMap<CreateResourcePermissionDto, ResourcePermissionEntity>();
        CreateMap<UpdateResourcePermissionDto, ResourcePermissionEntity>();

        // User mappings
        CreateMap<IdentityUser, UserDto>();

        // UserAccessManagement mappings
        CreateMap<UserDto, UserAccessDto>()
            .ForMember(dest => dest.UserId, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.AdAccountsCount, opt => opt.Ignore())
            .ForMember(dest => dest.AssignedAdAccountIds, opt => opt.Ignore())
            .ForMember(dest => dest.RoleName, opt => opt.Ignore())
            .ForMember(dest => dest.RoleNames, opt => opt.Ignore())
            .ForMember(dest => dest.LastLoginTime, opt => opt.Ignore());
    }
}