/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Upload Music Models

    /// <summary>
    /// Request for uploading a piece of music
    /// </summary>
    public class MusicUploadRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The scenario where the piece of music is used
        /// Enum values: CREATIVE_ASSET, CAROUSEL_ADS, CATALOG_CAROUSEL
        /// Default value: CREATIVE_ASSET
        /// </summary>
        [JsonPropertyName("music_scene")]
        public string? MusicScene { get; set; }

        /// <summary>
        /// The way you upload your music
        /// Enum values: UPLOAD_BY_FILE, UPLOAD_BY_FILE_ID
        /// </summary>
        [JsonPropertyName("upload_type")]
        public string? UploadType { get; set; }

        /// <summary>
        /// The type of operation you want to perform on a piece of music
        /// Enum values: ADD_TO_LIKED, ADD_TO_HISTORY, REMOVE_FROM_LIKED
        /// </summary>
        [JsonPropertyName("material_action")]
        public string? MaterialAction { get; set; }

        /// <summary>
        /// Music MD5 (used for server verification)
        /// Required when upload_type is UPLOAD_BY_FILE or not specified
        /// </summary>
        [JsonPropertyName("music_signature")]
        public string? MusicSignature { get; set; }

        /// <summary>
        /// Music name
        /// Length limit: 1-100 characters
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }

        /// <summary>
        /// The file_id of the file that you want to upload
        /// Required when upload_type is UPLOAD_BY_FILE_ID
        /// </summary>
        [JsonPropertyName("file_id")]
        public string? FileId { get; set; }

        /// <summary>
        /// Material ID for the piece of music
        /// Required when music_scene is CAROUSEL_ADS or CATALOG_CAROUSEL and material_action is passed
        /// </summary>
        [JsonPropertyName("material_id")]
        public string? MaterialId { get; set; }
    }

    /// <summary>
    /// Response for uploading a piece of music
    /// </summary>
    public class MusicUploadResponse
    {
        /// <summary>
        /// Music ID
        /// </summary>
        [JsonPropertyName("music_id")]
        public string MusicId { get; set; } = string.Empty;

        /// <summary>
        /// Material ID
        /// </summary>
        [JsonPropertyName("material_id")]
        public string MaterialId { get; set; } = string.Empty;

        /// <summary>
        /// Music source. USER means the advertiser's uploaded music; SYSTEM means the system music
        /// </summary>
        [JsonPropertyName("sources")]
        public List<string> Sources { get; set; } = new();

        /// <summary>
        /// Author of the piece of music (returned only for music that can be used in Carousel Ads)
        /// </summary>
        [JsonPropertyName("author")]
        public string? Author { get; set; }

        /// <summary>
        /// Whether the piece of music is added to the Liked list (returned only for music that can be used in Carousel Ads)
        /// </summary>
        [JsonPropertyName("liked")]
        public bool? Liked { get; set; }

        /// <summary>
        /// URL of the cover for the piece of music (returned only for music that can be used in Carousel Ads)
        /// </summary>
        [JsonPropertyName("cover_url")]
        public string? CoverUrl { get; set; }

        /// <summary>
        /// Music link. Effective duration: 12h
        /// </summary>
        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// Music duration (in seconds). Only returned for system music
        /// </summary>
        [JsonPropertyName("duration")]
        public float? Duration { get; set; }

        /// <summary>
        /// Music style. Only returned for system music
        /// </summary>
        [JsonPropertyName("style")]
        public string? Style { get; set; }

        /// <summary>
        /// Music file MD5
        /// </summary>
        [JsonPropertyName("signature")]
        public string Signature { get; set; } = string.Empty;

        /// <summary>
        /// File Name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Copyright information
        /// Enum values: MUSIC_FORBID_VIDEO_ALLOW, MUSIC_FORBID_VIDEO_FORBID
        /// </summary>
        [JsonPropertyName("copyright")]
        public string Copyright { get; set; } = string.Empty;

        /// <summary>
        /// Creation time. Only returned for advertiser's music. UTC Time. Format: 2020-06-10T07:39:14Z
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Modified time. Only returned for advertiser's music. UTC Time. Format: 2020-06-10T07:39:14Z
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string? ModifyTime { get; set; }
    }

    #endregion

    #region Get Music List Models

    /// <summary>
    /// Request for getting the music list
    /// </summary>
    public class MusicGetRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The scenario where the piece of music is used
        /// Enum values: CREATIVE_ASSET, CAROUSEL_ADS, CATALOG_CAROUSEL
        /// Default value: CREATIVE_ASSET
        /// </summary>
        [JsonPropertyName("music_scene")]
        public string? MusicScene { get; set; }

        /// <summary>
        /// The type of music search that you want to perform
        /// Enum values: SEARCH_BY_KEYWORD, SEARCH_BY_RECOMMEND, SEARCH_BY_LIKED, SEARCH_BY_HISTORY, SEARCH_BY_MUSIC_ID, SEARCH_BY_SOURCE
        /// </summary>
        [JsonPropertyName("search_type")]
        public string? SearchType { get; set; }

        /// <summary>
        /// Filter condition
        /// </summary>
        [JsonPropertyName("filtering")]
        public MusicGetFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number. Default value: 1. Value Range: ≥ 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 1,000. Value Range: 1-1,000
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filter condition for music get request
    /// </summary>
    public class MusicGetFiltering
    {
        /// <summary>
        /// The keyword for music search (required when search_type is SEARCH_BY_KEYWORD)
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }

        /// <summary>
        /// A list of image URLs to get recommended music for (required when music_scene is CAROUSEL_ADS and search_type is SEARCH_BY_RECOMMEND)
        /// </summary>
        [JsonPropertyName("image_urls")]
        public List<string>? ImageUrls { get; set; }

        /// <summary>
        /// Music IDs (required when search_type is SEARCH_BY_MUSIC_ID)
        /// </summary>
        [JsonPropertyName("music_ids")]
        public List<string>? MusicIds { get; set; }

        /// <summary>
        /// Catalog ID (required when music_scene is CATALOG_CAROUSEL and search_type is SEARCH_BY_RECOMMEND)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }

        /// <summary>
        /// ID of the Business Center that has the permission to access the catalog
        /// </summary>
        [JsonPropertyName("catalog_authorized_bc_id")]
        public string? CatalogAuthorizedBcId { get; set; }

        /// <summary>
        /// Product SPU IDs
        /// </summary>
        [JsonPropertyName("item_group_ids")]
        public List<string>? ItemGroupIds { get; set; }

        /// <summary>
        /// The ID of the product set
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string? ProductSetId { get; set; }

        /// <summary>
        /// IDs of the SKUs
        /// </summary>
        [JsonPropertyName("sku_ids")]
        public List<string>? SkuIds { get; set; }

        /// <summary>
        /// The index that specifies the additional images to be used in the VSA Carousel Ad
        /// </summary>
        [JsonPropertyName("carousel_image_index")]
        public int? CarouselImageIndex { get; set; }

        /// <summary>
        /// Material IDs (not supported when music_scene is CAROUSEL_ADS or CATALOG_CAROUSEL)
        /// </summary>
        [JsonPropertyName("material_ids")]
        public List<string>? MaterialIds { get; set; }

        /// <summary>
        /// Music style (not supported when music_scene is CAROUSEL_ADS or CATALOG_CAROUSEL)
        /// </summary>
        [JsonPropertyName("styles")]
        public List<string>? Styles { get; set; }

        /// <summary>
        /// Music source. USER represents user uploaded music, and SYSTEM represents system music
        /// </summary>
        [JsonPropertyName("sources")]
        public List<string>? Sources { get; set; }
    }

    /// <summary>
    /// Response for getting the music list
    /// </summary>
    public class MusicGetResponse
    {
        /// <summary>
        /// Music data
        /// </summary>
        [JsonPropertyName("musics")]
        public List<MusicData> Musics { get; set; } = new();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Music information for Music API
    /// </summary>
    public class MusicData
    {
        /// <summary>
        /// Music ID
        /// </summary>
        [JsonPropertyName("music_id")]
        public string MusicId { get; set; } = string.Empty;

        /// <summary>
        /// Material ID
        /// </summary>
        [JsonPropertyName("material_id")]
        public string MaterialId { get; set; } = string.Empty;

        /// <summary>
        /// Music source. USER means the advertiser's uploaded music, and SYSTEM means the system music
        /// </summary>
        [JsonPropertyName("sources")]
        public List<string> Sources { get; set; } = new();

        /// <summary>
        /// Author of the piece of music (returned only for music that can be used in Carousel Ads)
        /// </summary>
        [JsonPropertyName("author")]
        public string? Author { get; set; }

        /// <summary>
        /// Whether the piece of music is added to the Liked list (returned only for music that can be used in Carousel Ads)
        /// </summary>
        [JsonPropertyName("liked")]
        public bool? Liked { get; set; }

        /// <summary>
        /// URL of the cover for the piece of music (returned only for music that can be used in Carousel Ads)
        /// </summary>
        [JsonPropertyName("cover_url")]
        public string? CoverUrl { get; set; }

        /// <summary>
        /// Music link. Effective duration: 12h
        /// </summary>
        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// Video Duration (Seconds). Only returned for system music
        /// </summary>
        [JsonPropertyName("duration")]
        public float? Duration { get; set; }

        /// <summary>
        /// Music style. Only returned for system music
        /// </summary>
        [JsonPropertyName("style")]
        public string? Style { get; set; }

        /// <summary>
        /// Music file MD5
        /// </summary>
        [JsonPropertyName("signature")]
        public string Signature { get; set; } = string.Empty;

        /// <summary>
        /// Music Name
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// File Name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Copyright information
        /// Enum values: MUSIC_FORBID_VIDEO_ALLOW, MUSIC_FORBID_VIDEO_FORBID
        /// </summary>
        [JsonPropertyName("copyright")]
        public string Copyright { get; set; } = string.Empty;

        /// <summary>
        /// Creation time. Only returned for advertiser's music. UTC Time. Format: 2020-06-10T07:39:14Z
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Modified time. Only returned for advertiser's music. UTC Time. Format: 2020-06-10T07:39:14Z
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string? ModifyTime { get; set; }
    }

    #endregion

    #region Enums and Constants

    /// <summary>
    /// Music scene options
    /// </summary>
    public static class MusicScene
    {
        /// <summary>
        /// Creative Tools
        /// </summary>
        public const string CreativeAsset = "CREATIVE_ASSET";

        /// <summary>
        /// Non-Shopping Ads - Carousel format
        /// </summary>
        public const string CarouselAds = "CAROUSEL_ADS";

        /// <summary>
        /// Shopping Ads (VSA) - Carousel format
        /// </summary>
        public const string CatalogCarousel = "CATALOG_CAROUSEL";
    }

    /// <summary>
    /// Upload type options
    /// </summary>
    public static class MusicUploadType
    {
        /// <summary>
        /// To upload a piece of music via file
        /// </summary>
        public const string UploadByFile = "UPLOAD_BY_FILE";

        /// <summary>
        /// To upload a piece of music via file ID
        /// </summary>
        public const string UploadByFileId = "UPLOAD_BY_FILE_ID";
    }

    /// <summary>
    /// Material action options
    /// </summary>
    public static class MaterialAction
    {
        /// <summary>
        /// To add a piece of music to the Liked list
        /// </summary>
        public const string AddToLiked = "ADD_TO_LIKED";

        /// <summary>
        /// To add a piece of music to the History list
        /// </summary>
        public const string AddToHistory = "ADD_TO_HISTORY";

        /// <summary>
        /// To remove a piece of music from the Liked list
        /// </summary>
        public const string RemoveFromLiked = "REMOVE_FROM_LIKED";
    }

    /// <summary>
    /// Search type options
    /// </summary>
    public static class MusicSearchType
    {
        /// <summary>
        /// To fuzzy search for music via keyword
        /// </summary>
        public const string SearchByKeyword = "SEARCH_BY_KEYWORD";

        /// <summary>
        /// To get recommended music
        /// </summary>
        public const string SearchByRecommend = "SEARCH_BY_RECOMMEND";

        /// <summary>
        /// To search the Liked list for music
        /// </summary>
        public const string SearchByLiked = "SEARCH_BY_LIKED";

        /// <summary>
        /// To search the History list for music
        /// </summary>
        public const string SearchByHistory = "SEARCH_BY_HISTORY";

        /// <summary>
        /// To search for music via music IDs
        /// </summary>
        public const string SearchByMusicId = "SEARCH_BY_MUSIC_ID";

        /// <summary>
        /// To search for music by music source
        /// </summary>
        public const string SearchBySource = "SEARCH_BY_SOURCE";
    }

    /// <summary>
    /// Music source options
    /// </summary>
    public static class MusicSource
    {
        /// <summary>
        /// User uploaded music
        /// </summary>
        public const string User = "USER";

        /// <summary>
        /// System music
        /// </summary>
        public const string System = "SYSTEM";
    }

    /// <summary>
    /// Copyright options
    /// </summary>
    public static class MusicCopyright
    {
        /// <summary>
        /// Music is not allowed to download, but the generated video is allowed to download
        /// </summary>
        public const string MusicForbidVideoAllow = "MUSIC_FORBID_VIDEO_ALLOW";

        /// <summary>
        /// Both music and the generated video are not allowed to download
        /// </summary>
        public const string MusicForbidVideoForbid = "MUSIC_FORBID_VIDEO_FORBID";
    }

    #endregion
}
