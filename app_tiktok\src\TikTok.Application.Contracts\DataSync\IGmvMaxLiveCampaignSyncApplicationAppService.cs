using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service interface cho việc đồng bộ dữ liệu báo cáo GMV Max Live Campaign
    /// </summary>
    public interface IGmvMaxLiveCampaignSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Campaign theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> bắ<PERSON> đầu (tùy chọn)</param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kế<PERSON> thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxLiveCampaignSyncResult> SyncGmvMaxLiveCampaignAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxLiveCampaignSyncResult> SyncAllGmvMaxLiveCampaignForAllBcsAsync();
    }
}
