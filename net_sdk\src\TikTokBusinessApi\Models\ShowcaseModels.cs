/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Get Identities Models

    /// <summary>
    /// Response for getting identities with Showcase permission under an ad account
    /// </summary>
    public class ShowcaseIdentitiesResponse
    {
        /// <summary>
        /// The list of identities with Showcase permission
        /// </summary>
        [JsonPropertyName("identity_list")]
        public List<ShowcaseIdentity> IdentityList { get; set; } = new List<ShowcaseIdentity>();
    }

    /// <summary>
    /// Identity with Showcase permission
    /// </summary>
    public class ShowcaseIdentity
    {
        /// <summary>
        /// The ID of the identity that the Showcase is bound to
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string IdentityId { get; set; } = string.Empty;

        /// <summary>
        /// Identity type. Enum values: TT_USER (TikTok User), BC_AUTH_TT (TikTok Account User in Business Center)
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string IdentityType { get; set; } = string.Empty;

        /// <summary>
        /// Returned when identity_type is BC_AUTH_TT. ID of the Business Center that a Showcase, which is bound to a TikTok Account User in Business Center identity, is associated with
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }
    }

    #endregion

    #region Get Regions Models

    /// <summary>
    /// Response for getting the available regions for a Showcase via identity
    /// </summary>
    public class ShowcaseRegionsResponse
    {
        /// <summary>
        /// The list of country or region codes that the Showcase (identity) can target
        /// </summary>
        [JsonPropertyName("region_codes")]
        public List<string> RegionCodes { get; set; } = new List<string>();
    }

    #endregion

    #region Get Products Models

    /// <summary>
    /// Request for getting the available products in a Showcase
    /// </summary>
    public class ShowcaseProductsRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the identity that is bound to a Showcase and the identity needs to have permission of the Showcase (Required)
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string IdentityId { get; set; } = string.Empty;

        /// <summary>
        /// Identity type. Enum values: TT_USER (TikTok User), BC_AUTH_TT (TikTok Account User in Business Center) (Required)
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string IdentityType { get; set; } = string.Empty;

        /// <summary>
        /// Required when identity_type is BC_AUTH_TT. ID of the Business Center that a TikTok Account User in Business Center identity is associated with
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// The list of country or region codes that you want to use products in the Showcase to target (Required)
        /// </summary>
        [JsonPropertyName("region_codes")]
        public List<string> RegionCodes { get; set; } = new List<string>();

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public ShowcaseProductsFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number. Default value: 1. Value range: ≥1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10. Value range: [1, 1000]
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering conditions for Showcase products
    /// </summary>
    public class ShowcaseProductsFiltering
    {
        /// <summary>
        /// Product SPU IDs. Max size: 10
        /// </summary>
        [JsonPropertyName("item_group_ids")]
        public List<string>? ItemGroupIds { get; set; }
    }

    /// <summary>
    /// Response for getting the available products in a Showcase
    /// </summary>
    public class ShowcaseProductsResponse
    {
        /// <summary>
        /// The list of products in the Showcase that you can use to target the specified countries or regions
        /// </summary>
        [JsonPropertyName("showcase_products")]
        public List<ShowcaseProductInfo> ShowcaseProducts { get; set; } = new List<ShowcaseProductInfo>();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Product in a Showcase
    /// </summary>
    public class ShowcaseProductInfo
    {
        /// <summary>
        /// SPU ID of the product
        /// </summary>
        [JsonPropertyName("item_group_id")]
        public string ItemGroupId { get; set; } = string.Empty;

        /// <summary>
        /// The title of the product
        /// </summary>
        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// The URL of the product image
        /// </summary>
        [JsonPropertyName("product_image_url")]
        public string ProductImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// The minimum price of the product. If you specify one fixed price for the product, the values of min_price and max_price will be the same
        /// </summary>
        [JsonPropertyName("min_price")]
        public string MinPrice { get; set; } = string.Empty;

        /// <summary>
        /// The maximum price of the product. If you specify one fixed price for the product, the values of min_price and max_price will be the same
        /// </summary>
        [JsonPropertyName("max_price")]
        public string MaxPrice { get; set; } = string.Empty;

        /// <summary>
        /// The code of the currency in which the prices (min_price and max_price) are specified
        /// </summary>
        [JsonPropertyName("currency")]
        public string Currency { get; set; } = string.Empty;

        /// <summary>
        /// The category of the product
        /// </summary>
        [JsonPropertyName("category")]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// The status of the product. Enum values: AVAILABLE (The product is available for use in ads), NOT_AVAILABLE (The product is not available for use in ads)
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the catalog that the product (item_group_id) belongs to. Note: Starting June 30th, 2024, this field will no longer be returned
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }

        /// <summary>
        /// The ID of the store that the product (item_group_id) belongs to. Note that the only supported store type is TikTok Shop
        /// </summary>
        [JsonPropertyName("store_id")]
        public string StoreId { get; set; } = string.Empty;
    }

    #endregion
}
