/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response data for token info API
    /// </summary>
    public class TokenInfoResponse
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Scope of permissions for the specified access token
        /// </summary>
        [JsonPropertyName("scope")]
        public string? Scope { get; set; }

        /// <summary>
        /// Application specific unique ID of the TikTok Account
        /// </summary>
        [JsonPropertyName("creator_id")]
        public string? CreatorId { get; set; }

        /// <summary>
        /// Initializes a new instance of the TokenInfoResponse class
        /// </summary>
        public TokenInfoResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the TokenInfoResponse class with parameters
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="scope">Permission scope</param>
        /// <param name="creatorId">Creator ID</param>
        public TokenInfoResponse(string? appId = null, string? scope = null, string? creatorId = null)
        {
            AppId = appId;
            Scope = scope;
            CreatorId = creatorId;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"TokenInfoResponse(AppId={AppId}, Scope={Scope}, CreatorId={CreatorId})";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not TokenInfoResponse other)
                return false;

            return AppId == other.AppId &&
                   Scope == other.Scope &&
                   CreatorId == other.CreatorId;
        }

        /// <summary>
        /// Serves as the default hash function
        /// </summary>
        /// <returns>A hash code for the current object</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AppId, Scope, CreatorId);
        }
    }
}
