/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Creative Reports operations
    /// </summary>
    public interface ICreativeReportsApi
    {
        /// <summary>
        /// Run a basic report on creative assets
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="materialType">Material type. Enum values: VIDEO, IMAGE, INSTANT_PAGE</param>
        /// <param name="lifetime">Use to specify whether to query all data. If true, you do not need to specify start_date and end_date</param>
        /// <param name="startDate">Start time, closed interval. Format: 2020-01-01 (advertiser time zone). Required when lifetime is false</param>
        /// <param name="endDate">End time, closed interval. Format: 2020-01-01 (advertiser time zone). Required when lifetime is false</param>
        /// <param name="infoFields">Information you want to get about creatives. Default: [material_id, video_id, image_id, page_id]</param>
        /// <param name="metricsFields">The metrics or dimension data that you need. Default: [impressions, spend]</param>
        /// <param name="filtering">Filtering criteria</param>
        /// <param name="sortField">Field to sort by. Support sorting according to the creation time of the material and all the index data</param>
        /// <param name="sortType">Sorting order. Enum values: ASC, DESC. Default: DESC</param>
        /// <param name="page">Current number of pages. Default: 1, range: ≥ 1</param>
        /// <param name="pageSize">Page size. Default: 10, range: 1-1000</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing creative reports data</returns>
        Task<Response<CreativeReportsResponse>> GetCreativeReportsAsync(
            string accessToken,
            string advertiserId,
            string materialType,
            bool lifetime = false,
            string? startDate = null,
            string? endDate = null,
            List<string>? infoFields = null,
            List<string>? metricsFields = null,
            CreativeReportsFiltering? filtering = null,
            string? sortField = null,
            string? sortType = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default);
    }
}
