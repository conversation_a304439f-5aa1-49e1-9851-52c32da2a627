﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.BusinessApplications
{
    public interface IBusinessApplicationCache : IScopedDependency
    {
        public Task<BusinessApplicationDto?> GetById(Guid id);

        public Task<BusinessApplicationDto?> GetByBcIdAsync(string bcId);

        public Task<List<BusinessApplicationDto>> GetAllActiveAsync();

        public Task CleanCache();
    }
}
