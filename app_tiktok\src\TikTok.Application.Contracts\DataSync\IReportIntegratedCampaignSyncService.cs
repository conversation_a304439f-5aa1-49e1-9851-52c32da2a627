using System;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu báo cáo tích hợp Campaign
    /// </summary>
    public interface IReportIntegratedCampaignSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ báo cáo tích hợp Campaign theo BC ID 
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu (tùy chọn)</param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kế<PERSON> thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<ReportIntegratedCampaignSyncResult> SyncReportIntegratedCampaignAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// Đồng bộ báo cáo tích hợp Campaign cho tất cả Business Centers với khoảng thời gian mặc định
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<ReportIntegratedCampaignSyncResult> SyncAllReportIntegratedCampaignForAllBcsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu báo cáo tích hợp Campaign
    /// </summary>
    public class ReportIntegratedCampaignSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số Campaign đã đồng bộ
        /// </summary>
        public int CampaignCount { get; set; }

        /// <summary>
        /// Số ngày đã đồng bộ
        /// </summary>
        public int DayCount { get; set; }
    }
}