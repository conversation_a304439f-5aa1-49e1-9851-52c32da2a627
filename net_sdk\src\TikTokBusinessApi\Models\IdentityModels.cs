/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Create Identity Models

    /// <summary>
    /// Request for creating an identity
    /// </summary>
    public class IdentityCreateRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Display name (Required)
        /// The maximum length is 100 characters.
        /// </summary>
        [JsonPropertyName("display_name")]
        [Required]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the avatar image for the identity.
        /// The width and height ratio must be 1:1.
        /// If this field is not specified, a default image will be used as the avatar.
        /// </summary>
        [JsonPropertyName("image_uri")]
        public string? ImageUri { get; set; }
    }

    /// <summary>
    /// Response for creating an identity
    /// </summary>
    public class IdentityCreateResponse
    {
        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string IdentityId { get; set; } = string.Empty;
    }

    #endregion

    #region Delete Identity Models

    /// <summary>
    /// Request for deleting an identity
    /// </summary>
    public class IdentityDeleteRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Identity ID (Required)
        /// </summary>
        [JsonPropertyName("identity_id")]
        [Required]
        public string IdentityId { get; set; } = string.Empty;

        /// <summary>
        /// Identity type (Required)
        /// Currently, this field only supports CUSTOMIZED_USER.
        /// </summary>
        [JsonPropertyName("identity_type")]
        [Required]
        public string IdentityType { get; set; } = string.Empty;
    }

    #endregion

    #region Get Identity List Models

    /// <summary>
    /// Request for getting identity list
    /// </summary>
    public class IdentityGetRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Identity type
        /// Enum values: CUSTOMIZED_USER, AUTH_CODE, TT_USER, BC_AUTH_TT
        /// If this field is not specified, all identities will be returned.
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// Required when identity_type is BC_AUTH_TT.
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with.
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// Filtering conditions
        /// This field is valid only when identity_type = CUSTOMIZED_USER or when identity_type is not specified.
        /// </summary>
        [JsonPropertyName("filtering")]
        public IdentityGetFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number (Default: 1)
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size (Value range: 1-100, Default: 20)
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering for identity get requests
    /// </summary>
    public class IdentityGetFiltering
    {
        /// <summary>
        /// Keyword to filter by
        /// When identity_type = CUSTOMIZED_USER, you can use fuzzy matching by specifying display name to this field.
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }
    }

    /// <summary>
    /// Response for getting identity list
    /// </summary>
    public class IdentityGetResponse
    {
        /// <summary>
        /// List of identities
        /// </summary>
        [JsonPropertyName("identity_list")]
        public List<IdentityInfo> IdentityList { get; set; } = new();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    #endregion

    #region Get Identity Info Models

    /// <summary>
    /// Request for getting identity info
    /// </summary>
    public class IdentityInfoRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Identity ID (Required)
        /// </summary>
        [JsonPropertyName("identity_id")]
        [Required]
        public string IdentityId { get; set; } = string.Empty;

        /// <summary>
        /// Identity type (Required)
        /// Enum values: CUSTOMIZED_USER, AUTH_CODE, TT_USER, BC_AUTH_TT
        /// </summary>
        [JsonPropertyName("identity_type")]
        [Required]
        public string IdentityType { get; set; } = string.Empty;

        /// <summary>
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with.
        /// Required when identity_type is BC_AUTH_TT.
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }
    }

    /// <summary>
    /// Response for getting identity info
    /// </summary>
    public class IdentityInfoResponse
    {
        /// <summary>
        /// Identity information
        /// </summary>
        [JsonPropertyName("identity_info")]
        public IdentityInfo IdentityInfo { get; set; } = new();
    }

    #endregion

    #region Common Identity Models

    /// <summary>
    /// Identity information
    /// </summary>
    public class IdentityInfo
    {
        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string IdentityId { get; set; } = string.Empty;

        /// <summary>
        /// Identity type
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string IdentityType { get; set; } = string.Empty;

        /// <summary>
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// Whether the BC_AUTH_TT or TT_USER identity can create or edit videos
        /// </summary>
        [JsonPropertyName("can_push_video")]
        public bool? CanPushVideo { get; set; }

        /// <summary>
        /// Whether the BC_AUTH_TT or TT_USER identity can get all videos under the TikTok account
        /// </summary>
        [JsonPropertyName("can_pull_video")]
        public bool? CanPullVideo { get; set; }

        /// <summary>
        /// Whether the BC_AUTH_TT or TT_USER identity can access the live room
        /// </summary>
        [JsonPropertyName("can_use_live_ads")]
        public bool? CanUseLiveAds { get; set; }

        /// <summary>
        /// Whether the BC_AUTH_TT or TT_USER identity can manage direct messages
        /// </summary>
        [JsonPropertyName("can_manage_message")]
        public bool? CanManageMessage { get; set; }

        /// <summary>
        /// Display name
        /// </summary>
        [JsonPropertyName("display_name")]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Availability of the identity. Only valid for TT_USER and BC_AUTH_TT type of identities.
        /// Enum values: AVAILABLE, NO_VALID_BIND_ACCOUNT, SCOPE_UNAVAILABLE, IS_PRIVATE_ACCOUNT, NOT_BUSINESS_ACCOUNT
        /// </summary>
        [JsonPropertyName("available_status")]
        public string? AvailableStatus { get; set; }

        /// <summary>
        /// Profile image URL
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string? ProfileImage { get; set; }

        /// <summary>
        /// Profile image URL (used in identity info response)
        /// </summary>
        [JsonPropertyName("profile_image_url")]
        public string? ProfileImageUrl { get; set; }
    }

    #endregion

    #region Get Identity Videos Models

    /// <summary>
    /// Request for getting posts under an identity
    /// </summary>
    public class IdentityVideosRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Identity ID (Required)
        /// </summary>
        [JsonPropertyName("identity_id")]
        [Required]
        public string IdentityId { get; set; } = string.Empty;

        /// <summary>
        /// Identity type (Required)
        /// Enum values: AUTH_CODE, TT_USER, BC_AUTH_TT
        /// </summary>
        [JsonPropertyName("identity_type")]
        [Required]
        public string IdentityType { get; set; } = string.Empty;

        /// <summary>
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with.
        /// Required when identity_type is BC_AUTH_TT.
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// The type of TikTok posts that you want to retrieve.
        /// Enum values: VIDEO (video post), CAROUSEL (photo post)
        /// Default value: VIDEO
        /// </summary>
        [JsonPropertyName("item_type")]
        public string? ItemType { get; set; }

        /// <summary>
        /// Valid only when identity_type is set to AUTH_CODE.
        /// The text or TikTok post ID as a keyword to search TikTok posts by.
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }

        /// <summary>
        /// Cursor for pagination
        /// </summary>
        [JsonPropertyName("cursor")]
        public string? Cursor { get; set; }

        /// <summary>
        /// Number of TikTok posts you want to get.
        /// Value range: 1-20.
        /// </summary>
        [JsonPropertyName("count")]
        public int? Count { get; set; }
    }

    /// <summary>
    /// Response for getting posts under an identity
    /// </summary>
    public class IdentityVideosResponse
    {
        /// <summary>
        /// Timestamp cursor or cursor for pagination
        /// </summary>
        [JsonPropertyName("cursor")]
        public string Cursor { get; set; } = string.Empty;

        /// <summary>
        /// Whether more data is available
        /// </summary>
        [JsonPropertyName("has_more")]
        public bool HasMore { get; set; }

        /// <summary>
        /// List of posts
        /// </summary>
        [JsonPropertyName("video_list")]
        public List<VideoInfo> VideoList { get; set; } = new();
    }

    #endregion

    #region Get Identity Live Videos Models

    /// <summary>
    /// Request for getting live videos under an identity
    /// </summary>
    public class IdentityLiveVideosRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Identity ID (Required)
        /// </summary>
        [JsonPropertyName("identity_id")]
        [Required]
        public string IdentityId { get; set; } = string.Empty;

        /// <summary>
        /// Identity type (Required)
        /// Enum values: TT_USER, BC_AUTH_TT
        /// </summary>
        [JsonPropertyName("identity_type")]
        [Required]
        public string IdentityType { get; set; } = string.Empty;

        /// <summary>
        /// Required when identity_type is BC_AUTH_TT.
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with.
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// Timestamp cursor. Live videos before the timestamp will be returned in reverse-chronological order.
        /// In the initial request, you can set it to 0.
        /// </summary>
        [JsonPropertyName("cursor")]
        public int? Cursor { get; set; }
    }

    /// <summary>
    /// Response for getting live videos under an identity
    /// </summary>
    public class IdentityLiveVideosResponse
    {
        /// <summary>
        /// Timestamp cursor. The time value of the last item returned according to the current request.
        /// </summary>
        [JsonPropertyName("cursor")]
        public string Cursor { get; set; } = string.Empty;

        /// <summary>
        /// Whether more data is available
        /// </summary>
        [JsonPropertyName("has_more")]
        public bool HasMore { get; set; }

        /// <summary>
        /// List of live videos
        /// </summary>
        [JsonPropertyName("live_list")]
        public List<LiveVideoInfo> LiveList { get; set; } = new();
    }

    #endregion

    #region Get Music Authorization Models

    /// <summary>
    /// Request for getting music authorization info of a video
    /// </summary>
    public class MusicAuthorizationRequest
    {
        /// <summary>
        /// Ad account ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// TikTok post ID (Required)
        /// </summary>
        [JsonPropertyName("item_id")]
        [Required]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// Identity ID (Required)
        /// </summary>
        [JsonPropertyName("identity_id")]
        [Required]
        public string IdentityId { get; set; } = string.Empty;

        /// <summary>
        /// Identity type (Required)
        /// Enum values: AUTH_CODE, TT_USER, BC_AUTH_TT
        /// </summary>
        [JsonPropertyName("identity_type")]
        [Required]
        public string IdentityType { get; set; } = string.Empty;

        /// <summary>
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with.
        /// Required when identity_type is BC_AUTH_TT.
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// List of targeting locations (Required)
        /// You must use the same set of locations as when you create the ad.
        /// </summary>
        [JsonPropertyName("locations")]
        [Required]
        public List<string> Locations { get; set; } = new();

        /// <summary>
        /// Ad deployment start time (UTC) (Required)
        /// Format: "YYYY-MM-DD HH:MM:SS"
        /// </summary>
        [JsonPropertyName("start_time")]
        [Required]
        public string StartTime { get; set; } = string.Empty;

        /// <summary>
        /// Ad deployment end time (UTC) (Required)
        /// Format: "YYYY-MM-DD HH:MM:SS"
        /// </summary>
        [JsonPropertyName("end_time")]
        [Required]
        public string EndTime { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting music authorization info
    /// </summary>
    public class MusicAuthorizationResponse
    {
        /// <summary>
        /// Music authorization information for TikTok posts
        /// </summary>
        [JsonPropertyName("music_authorization")]
        public List<MusicAuthorizationInfo> MusicAuthorization { get; set; } = new();
    }

    #endregion

    #region Get Video Info Models

    /// <summary>
    /// Request for getting info about TikTok posts
    /// </summary>
    public class VideoInfoRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Identity type (Required)
        /// Enum values: AUTH_CODE, TT_USER, BC_AUTH_TT
        /// </summary>
        [JsonPropertyName("identity_type")]
        [Required]
        public string IdentityType { get; set; } = string.Empty;

        /// <summary>
        /// Identity ID (Required)
        /// </summary>
        [JsonPropertyName("identity_id")]
        [Required]
        public string IdentityId { get; set; } = string.Empty;

        /// <summary>
        /// Required when identity_type is BC_AUTH_TT.
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with.
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// Either item_id or item_ids must be specified.
        /// The TikTok post ID that is associated with the provided Identity ID.
        /// </summary>
        [JsonPropertyName("item_id")]
        public string? ItemId { get; set; }

        /// <summary>
        /// Either item_id or item_ids must be specified.
        /// A list of TikTok post IDs that are associated with the provided Identity ID.
        /// Max size: 20.
        /// </summary>
        [JsonPropertyName("item_ids")]
        public List<string>? ItemIds { get; set; }

        /// <summary>
        /// The type of TikTok posts that you want to retrieve.
        /// Enum values: VIDEO (video post), CAROUSEL (photo post)
        /// Default value: VIDEO
        /// </summary>
        [JsonPropertyName("item_type")]
        public string? ItemType { get; set; }
    }

    /// <summary>
    /// Response for getting info about TikTok posts
    /// </summary>
    public class VideoInfoResponse
    {
        /// <summary>
        /// Returned when item_id is passed in the request.
        /// Information about the TikTok post.
        /// </summary>
        [JsonPropertyName("video_detail")]
        public VideoDetail? VideoDetail { get; set; }

        /// <summary>
        /// Returned when item_ids is passed in the request.
        /// Information about the TikTok posts.
        /// </summary>
        [JsonPropertyName("video_details")]
        public List<VideoDetail>? VideoDetails { get; set; }
    }

    #endregion
}
