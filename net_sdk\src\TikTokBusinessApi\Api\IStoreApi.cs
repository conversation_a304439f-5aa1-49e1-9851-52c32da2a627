/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Store operations
    /// </summary>
    public interface IStoreApi
    {
        /// <summary>
        /// Get the list of available first-party stores (TikTok Shops) under an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="storeId">ID of the first-party store (TikTok Shop) to filter by</param>
        /// <param name="storeType">Store type to filter by. Enum values: TIKTOK_SHOP</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing available stores</returns>
        Task<Response<StoreListResponse>> GetStoreListAsync(
            string accessToken,
            string advertiserId,
            string? storeId = null,
            string? storeType = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get products within a first-party store (TikTok Shop)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="storeId">TikTok Shop ID</param>
        /// <param name="filtering">Filtering conditions</param>
        /// <param name="advertiserId">Advertiser ID (required when ad_creation_eligible is passed)</param>
        /// <param name="sortField">Field to sort by. Enum values: min_price, historical_sales</param>
        /// <param name="sortType">Sorting order. Enum values: ASC, DESC</param>
        /// <param name="page">Current number of pages. Default value: 1</param>
        /// <param name="pageSize">Page size. Default value: 10. Value range: 1-100</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing store products</returns>
        Task<Response<StoreProductsResponse>> GetStoreProductsAsync(
            string accessToken,
            string bcId,
            string storeId,
            StoreProductFiltering? filtering = null,
            string? advertiserId = null,
            string? sortField = null,
            string? sortType = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default);
    }
}
