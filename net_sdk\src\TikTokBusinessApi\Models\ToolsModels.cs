/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    // ========== Search Targeting Models ==========

    /// <summary>
    /// Request body for searching location targeting tags
    /// </summary>
    public class SearchTargetingRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign objective
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string ObjectiveType { get; set; } = string.Empty;

        /// <summary>
        /// Promotion type
        /// </summary>
        [JsonPropertyName("promotion_type")]
        public string? PromotionType { get; set; }

        /// <summary>
        /// The apps where you want to deliver your ads
        /// </summary>
        [JsonPropertyName("placements")]
        public List<string> Placements { get; set; } = new();

        /// <summary>
        /// The type of search you want to perform
        /// </summary>
        [JsonPropertyName("search_type")]
        public string SearchType { get; set; } = string.Empty;

        /// <summary>
        /// The keywords that you use to search for targeting tags
        /// </summary>
        [JsonPropertyName("keywords")]
        public List<string> Keywords { get; set; } = new();

        /// <summary>
        /// The types of locations that you want to filter the results by
        /// </summary>
        [JsonPropertyName("geo_types")]
        public List<string>? GeoTypes { get; set; }

        /// <summary>
        /// The codes of targeted countries or regions that you want to filter the results by
        /// </summary>
        [JsonPropertyName("region_codes")]
        public List<string>? RegionCodes { get; set; }

        /// <summary>
        /// Device operating system that you want to target
        /// </summary>
        [JsonPropertyName("operating_system")]
        public string? OperatingSystem { get; set; }

        /// <summary>
        /// Brand safety type
        /// </summary>
        [JsonPropertyName("brand_safety_type")]
        public string? BrandSafetyType { get; set; }

        /// <summary>
        /// Brand safety partner
        /// </summary>
        [JsonPropertyName("brand_safety_partner")]
        public string? BrandSafetyPartner { get; set; }
    }

    /// <summary>
    /// Response for searching location targeting tags
    /// </summary>
    public class SearchTargetingResponse
    {
        /// <summary>
        /// Information about the targeting tags that are matched by the specified keyword or keywords
        /// </summary>
        [JsonPropertyName("targeting_tag_list")]
        public List<TargetingTag> TargetingTagList { get; set; } = new();

        /// <summary>
        /// Information about all the parent targeting tags
        /// </summary>
        [JsonPropertyName("parent_tags")]
        public List<TargetingTag> ParentTags { get; set; } = new();
    }

    /// <summary>
    /// Targeting tag information
    /// </summary>
    public class TargetingTag
    {
        /// <summary>
        /// The keyword that is used to search for targeting tags
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }

        /// <summary>
        /// The targeting type that the targeting tag is used for
        /// </summary>
        [JsonPropertyName("targeting_type")]
        public string TargetingType { get; set; } = string.Empty;

        /// <summary>
        /// The name of the targeting tag
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Information about the status of the targeting tag
        /// </summary>
        [JsonPropertyName("status_info")]
        public TargetingTagStatusInfo StatusInfo { get; set; } = new();

        /// <summary>
        /// Information about the targeting tag that is used for location targeting
        /// </summary>
        [JsonPropertyName("geo")]
        public TargetingTagGeo? Geo { get; set; }

        /// <summary>
        /// Information about the targeting tag that is used for Internet service provider targeting
        /// </summary>
        [JsonPropertyName("isp")]
        public TargetingTagIsp? Isp { get; set; }
    }

    /// <summary>
    /// Status information for targeting tag
    /// </summary>
    public class TargetingTagStatusInfo
    {
        /// <summary>
        /// The status of the targeting tag
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// The reason why the targeting tag cannot be used for targeting
        /// </summary>
        [JsonPropertyName("reason")]
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Geographic information for targeting tag
    /// </summary>
    public class TargetingTagGeo
    {
        /// <summary>
        /// Location ID, zip code ID, or postal code ID
        /// </summary>
        [JsonPropertyName("geo_id")]
        public string GeoId { get; set; } = string.Empty;

        /// <summary>
        /// The type of location that the geo_id corresponds to
        /// </summary>
        [JsonPropertyName("geo_type")]
        public string GeoType { get; set; } = string.Empty;

        /// <summary>
        /// The description of the targeted location
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// The code of the country or region that the targeted location belongs to
        /// </summary>
        [JsonPropertyName("region_code")]
        public string RegionCode { get; set; } = string.Empty;

        /// <summary>
        /// ID of the parent location
        /// </summary>
        [JsonPropertyName("parent_id")]
        public string ParentId { get; set; } = string.Empty;
    }

    /// <summary>
    /// ISP information for targeting tag
    /// </summary>
    public class TargetingTagIsp
    {
        /// <summary>
        /// The ID of the Internet service provider that you can target
        /// </summary>
        [JsonPropertyName("isp_id")]
        public string IspId { get; set; } = string.Empty;

        /// <summary>
        /// The location ID that the isp_id can be used together with
        /// </summary>
        [JsonPropertyName("location_id")]
        public string LocationId { get; set; } = string.Empty;

        /// <summary>
        /// The code of the region or country that the isp_id belongs to
        /// </summary>
        [JsonPropertyName("region_code")]
        public string RegionCode { get; set; } = string.Empty;
    }

    // ========== Get Targeting Info Models ==========

    /// <summary>
    /// Request body for getting targeting tag information by ID
    /// </summary>
    public class GetTargetingInfoRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The targeting type that the specified targeting_ids is used for
        /// </summary>
        [JsonPropertyName("scene")]
        public string? Scene { get; set; }

        /// <summary>
        /// Targeting tag IDs
        /// </summary>
        [JsonPropertyName("targeting_ids")]
        public List<string> TargetingIds { get; set; } = new();

        /// <summary>
        /// Campaign objective
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string? ObjectiveType { get; set; }

        /// <summary>
        /// Promotion type
        /// </summary>
        [JsonPropertyName("promotion_type")]
        public string? PromotionType { get; set; }

        /// <summary>
        /// The apps where you want to deliver your ads
        /// </summary>
        [JsonPropertyName("placements")]
        public List<string>? Placements { get; set; }

        /// <summary>
        /// Device operating system that you want to target
        /// </summary>
        [JsonPropertyName("operating_system")]
        public string? OperatingSystem { get; set; }

        /// <summary>
        /// Brand safety type
        /// </summary>
        [JsonPropertyName("brand_safety_type")]
        public string? BrandSafetyType { get; set; }

        /// <summary>
        /// Brand safety partner
        /// </summary>
        [JsonPropertyName("brand_safety_partner")]
        public string? BrandSafetyPartner { get; set; }
    }

    /// <summary>
    /// Response for getting targeting tag information by ID
    /// </summary>
    public class GetTargetingInfoResponse
    {
        /// <summary>
        /// Information about the targeting tags that are matched by the specified targeting tag IDs
        /// </summary>
        [JsonPropertyName("targeting_tag_list")]
        public List<TargetingTag> TargetingTagList { get; set; } = new();

        /// <summary>
        /// Information about all the parent targeting tags
        /// </summary>
        [JsonPropertyName("parent_tags")]
        public List<TargetingTag> ParentTags { get; set; } = new();
    }

    // ========== Get Region Models ==========

    /// <summary>
    /// Response for getting available locations by different settings
    /// </summary>
    public class GetRegionResponse
    {
        /// <summary>
        /// List of country or region codes
        /// </summary>
        [JsonPropertyName("region_list")]
        public List<string> RegionList { get; set; } = new();

        /// <summary>
        /// Information about the available locations
        /// </summary>
        [JsonPropertyName("region_info")]
        public List<RegionInfo> RegionInfo { get; set; } = new();
    }

    /// <summary>
    /// Information about available location
    /// </summary>
    public class RegionInfo
    {
        /// <summary>
        /// Location ID
        /// </summary>
        [JsonPropertyName("location_id")]
        public string LocationId { get; set; } = string.Empty;

        /// <summary>
        /// Location name
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// ID of the parent location
        /// </summary>
        [JsonPropertyName("parent_id")]
        public string ParentId { get; set; } = string.Empty;

        /// <summary>
        /// Country or region code
        /// </summary>
        [JsonPropertyName("region_code")]
        public string RegionCode { get; set; } = string.Empty;

        /// <summary>
        /// List of children locations
        /// </summary>
        [JsonPropertyName("next_level_ids")]
        public List<string> NextLevelIds { get; set; } = new();

        /// <summary>
        /// Location type
        /// </summary>
        [JsonPropertyName("area_type")]
        public string AreaType { get; set; } = string.Empty;

        /// <summary>
        /// Location level
        /// </summary>
        [JsonPropertyName("level")]
        public string Level { get; set; } = string.Empty;

        /// <summary>
        /// Whether the location supports ads to be delivered to people under 18 years of age
        /// </summary>
        [JsonPropertyName("support_below_18")]
        public bool SupportBelow18 { get; set; }
    }

    // ========== Search Region Models ==========

    /// <summary>
    /// Response for getting available locations by advertiser ID
    /// </summary>
    public class SearchRegionResponse
    {
        /// <summary>
        /// List of all available regions
        /// </summary>
        [JsonPropertyName("region_list")]
        public List<SearchRegionInfo> RegionList { get; set; } = new();
    }

    /// <summary>
    /// Information about available region by advertiser ID
    /// </summary>
    public class SearchRegionInfo
    {
        /// <summary>
        /// Region ID
        /// </summary>
        [JsonPropertyName("region_id")]
        public string RegionId { get; set; } = string.Empty;

        /// <summary>
        /// Region name
        /// </summary>
        [JsonPropertyName("region_name")]
        public string RegionName { get; set; } = string.Empty;

        /// <summary>
        /// Region level
        /// </summary>
        [JsonPropertyName("region_level")]
        public string RegionLevel { get; set; } = string.Empty;

        /// <summary>
        /// Country code for the country of the region
        /// </summary>
        [JsonPropertyName("country_code")]
        public string CountryCode { get; set; } = string.Empty;

        /// <summary>
        /// The upper-level region id
        /// </summary>
        [JsonPropertyName("parent_id")]
        public string? ParentId { get; set; }

        /// <summary>
        /// Location type
        /// </summary>
        [JsonPropertyName("area_type")]
        public string AreaType { get; set; } = string.Empty;

        /// <summary>
        /// Whether the location supports ads to be delivered to people under 18 years of age
        /// </summary>
        [JsonPropertyName("support_below_18")]
        public bool SupportBelow18 { get; set; }
    }

    // ========== Get Language Models ==========

    /// <summary>
    /// Response for getting languages
    /// </summary>
    public class GetLanguageResponse
    {
        /// <summary>
        /// Language list
        /// </summary>
        [JsonPropertyName("languages")]
        public List<LanguageInfo> Languages { get; set; } = new();
    }

    /// <summary>
    /// Language information
    /// </summary>
    public class LanguageInfo
    {
        /// <summary>
        /// Language code
        /// </summary>
        [JsonPropertyName("code")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Language name
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;
    }

    // ========== Search Interest and Behavior Models ==========

    /// <summary>
    /// Request for searching targeting categories and hashtags for interests and behaviors
    /// </summary>
    public class SearchInterestAndBehaviorRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The primary targeting type
        /// </summary>
        [JsonPropertyName("targeting_type")]
        public string TargetingType { get; set; } = string.Empty;

        /// <summary>
        /// The secondary targeting types
        /// </summary>
        [JsonPropertyName("sub_targeting_types")]
        public List<string>? SubTargetingTypes { get; set; }

        /// <summary>
        /// A list of seed keywords that you provide to search for targeting categories or hashtags
        /// </summary>
        [JsonPropertyName("search_keywords")]
        public List<string>? SearchKeywords { get; set; }

        /// <summary>
        /// The language of the seed keywords
        /// </summary>
        [JsonPropertyName("language")]
        public string? Language { get; set; }

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public InterestBehaviorFiltering? Filtering { get; set; }
    }

    /// <summary>
    /// Filtering conditions for interest and behavior search
    /// </summary>
    public class InterestBehaviorFiltering
    {
        /// <summary>
        /// Special ad categories
        /// </summary>
        [JsonPropertyName("special_industries")]
        public List<string>? SpecialIndustries { get; set; }
    }

    /// <summary>
    /// Response for searching targeting categories and hashtags for interests and behaviors
    /// </summary>
    public class SearchInterestAndBehaviorResponse
    {
        /// <summary>
        /// The list of general interest categories
        /// </summary>
        [JsonPropertyName("general_interest")]
        public InterestCategoryResult? GeneralInterest { get; set; }

        /// <summary>
        /// The list of additional interest categories
        /// </summary>
        [JsonPropertyName("additional_interest")]
        public InterestCategoryResult? AdditionalInterest { get; set; }

        /// <summary>
        /// The list of purchase intention categories
        /// </summary>
        [JsonPropertyName("purchase_intention")]
        public InterestCategoryResult? PurchaseIntention { get; set; }

        /// <summary>
        /// The list of video interactions categories
        /// </summary>
        [JsonPropertyName("video_interaction")]
        public InterestCategoryResult? VideoInteraction { get; set; }

        /// <summary>
        /// The list of creator interactions categories
        /// </summary>
        [JsonPropertyName("creator_interaction")]
        public InterestCategoryResult? CreatorInteraction { get; set; }

        /// <summary>
        /// The list of hashtags or hashtag bundles related to hashtag interactions
        /// </summary>
        [JsonPropertyName("hashtag_interaction")]
        public InterestCategoryResult? HashtagInteraction { get; set; }
    }

    /// <summary>
    /// Interest category result containing list and search results
    /// </summary>
    public class InterestCategoryResult
    {
        /// <summary>
        /// The list of all available categories
        /// </summary>
        [JsonPropertyName("list_result")]
        public List<InterestCategoryItem>? ListResult { get; set; }

        /// <summary>
        /// The list of categories that are generated based on the specified search_keywords
        /// </summary>
        [JsonPropertyName("search_result")]
        public Dictionary<string, List<InterestCategoryItem>>? SearchResult { get; set; }
    }

    /// <summary>
    /// Interest category item
    /// </summary>
    public class InterestCategoryItem
    {
        /// <summary>
        /// The secondary targeting type
        /// </summary>
        [JsonPropertyName("sub_targeting_type")]
        public string SubTargetingType { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the category
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// The name of the category
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The special ad categories that the category can be used together with
        /// </summary>
        [JsonPropertyName("supported_special_industries")]
        public List<string> SupportedSpecialIndustries { get; set; } = new();

        /// <summary>
        /// The level of the category
        /// </summary>
        [JsonPropertyName("level")]
        public int? Level { get; set; }

        /// <summary>
        /// The IDs of the children categories for the current category
        /// </summary>
        [JsonPropertyName("children_ids")]
        public List<string>? ChildrenIds { get; set; }

        /// <summary>
        /// Hashtag type
        /// </summary>
        [JsonPropertyName("hashtag_type")]
        public string? HashtagType { get; set; }
    }

    // ========== Get Interest Category Models ==========

    /// <summary>
    /// Response for getting general interest categories
    /// </summary>
    public class GetInterestCategoryResponse
    {
        /// <summary>
        /// Interest category list
        /// </summary>
        [JsonPropertyName("interest_categories")]
        public List<InterestCategory> InterestCategories { get; set; } = new();
    }

    /// <summary>
    /// Interest category information
    /// </summary>
    public class InterestCategory
    {
        /// <summary>
        /// Interest category ID
        /// </summary>
        [JsonPropertyName("interest_category_id")]
        public string InterestCategoryId { get; set; } = string.Empty;

        /// <summary>
        /// Interest category name
        /// </summary>
        [JsonPropertyName("interest_category_name")]
        public string InterestCategoryName { get; set; } = string.Empty;

        /// <summary>
        /// Level
        /// </summary>
        [JsonPropertyName("level")]
        public int Level { get; set; }

        /// <summary>
        /// Subcategory ID list
        /// </summary>
        [JsonPropertyName("sub_category_ids")]
        public List<string> SubCategoryIds { get; set; } = new();

        /// <summary>
        /// Special ad categories
        /// </summary>
        [JsonPropertyName("special_industries")]
        public List<string> SpecialIndustries { get; set; } = new();

        /// <summary>
        /// Available placement for this interest category
        /// </summary>
        [JsonPropertyName("placements")]
        public List<string> Placements { get; set; } = new();
    }

    // ========== Recommend Interest Keyword Models ==========

    /// <summary>
    /// Response for searching additional interest categories
    /// </summary>
    public class RecommendInterestKeywordResponse
    {
        /// <summary>
        /// A list of additional interest categories
        /// </summary>
        [JsonPropertyName("recommended_keywords")]
        public List<RecommendedKeyword> RecommendedKeywords { get; set; } = new();
    }

    /// <summary>
    /// Recommended keyword information
    /// </summary>
    public class RecommendedKeyword
    {
        /// <summary>
        /// The specific seed keyword for which the additional interest category is generated
        /// </summary>
        [JsonPropertyName("input_keyword")]
        public string? InputKeyword { get; set; }

        /// <summary>
        /// Additional interest category
        /// </summary>
        [JsonPropertyName("keyword")]
        public string Keyword { get; set; } = string.Empty;

        /// <summary>
        /// ID of the additional interest category
        /// </summary>
        [JsonPropertyName("keyword_id")]
        public string KeywordId { get; set; } = string.Empty;

        /// <summary>
        /// Language of the additional interest category
        /// </summary>
        [JsonPropertyName("language")]
        public string Language { get; set; } = string.Empty;

        /// <summary>
        /// Whether the additional interest category will be effective
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;
    }

    // ========== Get Interest Keyword Models ==========

    /// <summary>
    /// Query information for getting additional interest category
    /// </summary>
    public class InterestKeywordQuery
    {
        /// <summary>
        /// Additional interest category ID
        /// </summary>
        [JsonPropertyName("keyword_id")]
        public string KeywordId { get; set; } = string.Empty;

        /// <summary>
        /// Additional interest category language
        /// </summary>
        [JsonPropertyName("language")]
        public string? Language { get; set; }
    }

    /// <summary>
    /// Filtering conditions for getting interest keywords
    /// </summary>
    public class InterestKeywordFiltering
    {
        /// <summary>
        /// The audience type that you want to filter by
        /// </summary>
        [JsonPropertyName("audience_type")]
        public string? AudienceType { get; set; }
    }

    /// <summary>
    /// Response for getting additional interest categories by ID
    /// </summary>
    public class GetInterestKeywordResponse
    {
        /// <summary>
        /// List of additional interest categories
        /// </summary>
        [JsonPropertyName("keywords")]
        public List<InterestKeywordInfo> Keywords { get; set; } = new();
    }

    /// <summary>
    /// Interest keyword information
    /// </summary>
    public class InterestKeywordInfo
    {
        /// <summary>
        /// Additional interest category
        /// </summary>
        [JsonPropertyName("keyword")]
        public string Keyword { get; set; } = string.Empty;

        /// <summary>
        /// Additional interest category ID
        /// </summary>
        [JsonPropertyName("keyword_id")]
        public string KeywordId { get; set; } = string.Empty;

        /// <summary>
        /// Whether an additional interest category will be effective
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;
    }

    // ========== Get Action Category Models ==========

    /// <summary>
    /// Response for getting action categories
    /// </summary>
    public class GetActionCategoryResponse
    {
        /// <summary>
        /// Action categories list
        /// </summary>
        [JsonPropertyName("action_categories")]
        public List<ActionCategory> ActionCategories { get; set; } = new();
    }

    /// <summary>
    /// Action category information
    /// </summary>
    public class ActionCategory
    {
        /// <summary>
        /// Action description
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Action category ID
        /// </summary>
        [JsonPropertyName("action_category_id")]
        public string ActionCategoryId { get; set; } = string.Empty;

        /// <summary>
        /// Level
        /// </summary>
        [JsonPropertyName("level")]
        public int Level { get; set; }

        /// <summary>
        /// Subcategory ID list
        /// </summary>
        [JsonPropertyName("sub_category_ids")]
        public List<string> SubCategoryIds { get; set; } = new();

        /// <summary>
        /// Action category name
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Special ad categories
        /// </summary>
        [JsonPropertyName("special_industries")]
        public List<string> SpecialIndustries { get; set; } = new();

        /// <summary>
        /// Action scene
        /// </summary>
        [JsonPropertyName("action_scene")]
        public string ActionScene { get; set; } = string.Empty;
    }

    // ========== Recommend Hashtag Models ==========

    /// <summary>
    /// Response for searching targeting hashtags
    /// </summary>
    public class RecommendHashtagResponse
    {
        /// <summary>
        /// Recommended hashtags
        /// </summary>
        [JsonPropertyName("recommend_keywords")]
        public List<RecommendedHashtag> RecommendKeywords { get; set; } = new();
    }

    /// <summary>
    /// Recommended hashtag information
    /// </summary>
    public class RecommendedHashtag
    {
        /// <summary>
        /// The specific keyword for which the recommended hashtag is generated
        /// </summary>
        [JsonPropertyName("input_keyword")]
        public string? InputKeyword { get; set; }

        /// <summary>
        /// Recommended hashtag
        /// </summary>
        [JsonPropertyName("keyword")]
        public string Keyword { get; set; } = string.Empty;

        /// <summary>
        /// Hashtag keyword ID
        /// </summary>
        [JsonPropertyName("keyword_id")]
        public string KeywordId { get; set; } = string.Empty;

        /// <summary>
        /// Keyword status
        /// </summary>
        [JsonPropertyName("keyword_status")]
        public string KeywordStatus { get; set; } = string.Empty;
    }

    // ========== Get Hashtag Models ==========

    /// <summary>
    /// Response for getting targeting hashtags by ID
    /// </summary>
    public class GetHashtagResponse
    {
        /// <summary>
        /// Keyword information
        /// </summary>
        [JsonPropertyName("keywords_status")]
        public List<HashtagStatus> KeywordsStatus { get; set; } = new();
    }

    /// <summary>
    /// Hashtag status information
    /// </summary>
    public class HashtagStatus
    {
        /// <summary>
        /// Keyword
        /// </summary>
        [JsonPropertyName("keyword")]
        public string Keyword { get; set; } = string.Empty;

        /// <summary>
        /// Keyword ID
        /// </summary>
        [JsonPropertyName("keyword_id")]
        public string KeywordId { get; set; } = string.Empty;

        /// <summary>
        /// Keyword status
        /// </summary>
        [JsonPropertyName("keyword_status")]
        public string KeywordStatus { get; set; } = string.Empty;
    }

    // ========== Recommend Targeting Category Models ==========

    /// <summary>
    /// Request for getting recommended interest and action categories
    /// </summary>
    public class RecommendTargetingCategoryRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// List of country or region codes
        /// </summary>
        [JsonPropertyName("region_codes")]
        public List<string> RegionCodes { get; set; } = new();

        /// <summary>
        /// ID of the mobile app that you want to promote
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }
    }

    /// <summary>
    /// Response for getting recommended interest and action categories
    /// </summary>
    public class RecommendTargetingCategoryResponse
    {
        /// <summary>
        /// Recommended interest categories
        /// </summary>
        [JsonPropertyName("interest_categories")]
        public List<RecommendedInterestCategory> InterestCategories { get; set; } = new();

        /// <summary>
        /// Recommended action categories
        /// </summary>
        [JsonPropertyName("action_categories")]
        public List<RecommendedActionCategory> ActionCategories { get; set; } = new();
    }

    /// <summary>
    /// Recommended interest category information
    /// </summary>
    public class RecommendedInterestCategory
    {
        /// <summary>
        /// Interest category ID
        /// </summary>
        [JsonPropertyName("interest_category_id")]
        public string InterestCategoryId { get; set; } = string.Empty;

        /// <summary>
        /// Interest category name
        /// </summary>
        [JsonPropertyName("interest_category_name")]
        public string InterestCategoryName { get; set; } = string.Empty;

        /// <summary>
        /// List of placements that the interest categories can be used
        /// </summary>
        [JsonPropertyName("placements")]
        public List<string> Placements { get; set; } = new();

        /// <summary>
        /// Special ad categories
        /// </summary>
        [JsonPropertyName("special_industries")]
        public List<string> SpecialIndustries { get; set; } = new();
    }

    /// <summary>
    /// Recommended action category information
    /// </summary>
    public class RecommendedActionCategory
    {
        /// <summary>
        /// Action category ID
        /// </summary>
        [JsonPropertyName("action_category_id")]
        public string ActionCategoryId { get; set; } = string.Empty;

        /// <summary>
        /// Action category name
        /// </summary>
        [JsonPropertyName("action_category_name")]
        public string ActionCategoryName { get; set; } = string.Empty;

        /// <summary>
        /// Action category description
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Action category scene
        /// </summary>
        [JsonPropertyName("scene")]
        public string Scene { get; set; } = string.Empty;

        /// <summary>
        /// Special ad categories
        /// </summary>
        [JsonPropertyName("special_industries")]
        public List<string> SpecialIndustries { get; set; } = new();
    }

    // ========== Get OS Version Models ==========

    /// <summary>
    /// Response for getting OS versions
    /// </summary>
    public class GetOsVersionResponse
    {
        /// <summary>
        /// OS versions list
        /// </summary>
        [JsonPropertyName("os_versions")]
        public List<OsVersion> OsVersions { get; set; } = new();
    }

    /// <summary>
    /// OS version information
    /// </summary>
    public class OsVersion
    {
        /// <summary>
        /// OS versions ID
        /// </summary>
        [JsonPropertyName("os_id")]
        public string OsId { get; set; } = string.Empty;

        /// <summary>
        /// OS type
        /// </summary>
        [JsonPropertyName("os_type")]
        public string OsType { get; set; } = string.Empty;

        /// <summary>
        /// OS version
        /// </summary>
        [JsonPropertyName("version")]
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// OS version name
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;
    }

    // ========== Get Device Model Models ==========

    /// <summary>
    /// Response for getting device models
    /// </summary>
    public class GetDeviceModelResponse
    {
        /// <summary>
        /// The list of device model objects
        /// </summary>
        [JsonPropertyName("device_models")]
        public List<DeviceModel> DeviceModels { get; set; } = new();
    }

    /// <summary>
    /// Device model information
    /// </summary>
    public class DeviceModel
    {
        /// <summary>
        /// Device model ID
        /// </summary>
        [JsonPropertyName("device_model_id")]
        public string DeviceModelId { get; set; } = string.Empty;

        /// <summary>
        /// Device model name
        /// </summary>
        [JsonPropertyName("device_model_name")]
        public string DeviceModelName { get; set; } = string.Empty;

        /// <summary>
        /// List of children device model IDs
        /// </summary>
        [JsonPropertyName("child_device_ids")]
        public List<string> ChildDeviceIds { get; set; } = new();

        /// <summary>
        /// Whether the device model is active or not
        /// </summary>
        [JsonPropertyName("is_active")]
        public bool IsActive { get; set; }

        /// <summary>
        /// Device model level
        /// </summary>
        [JsonPropertyName("level")]
        public string Level { get; set; } = string.Empty;

        /// <summary>
        /// Operating system type
        /// </summary>
        [JsonPropertyName("os_type")]
        public string OsType { get; set; } = string.Empty;
    }

    // ========== Get Carrier Models ==========

    /// <summary>
    /// Response for getting carriers
    /// </summary>
    public class GetCarrierResponse
    {
        /// <summary>
        /// List of countries or locations
        /// </summary>
        [JsonPropertyName("countries")]
        public List<CarrierCountry> Countries { get; set; } = new();
    }

    /// <summary>
    /// Carrier country information
    /// </summary>
    public class CarrierCountry
    {
        /// <summary>
        /// Country or location code
        /// </summary>
        [JsonPropertyName("country_code")]
        public string CountryCode { get; set; } = string.Empty;

        /// <summary>
        /// List of carriers in the country or location
        /// </summary>
        [JsonPropertyName("carriers")]
        public List<Carrier> Carriers { get; set; } = new();
    }

    /// <summary>
    /// Carrier information
    /// </summary>
    public class Carrier
    {
        /// <summary>
        /// Carrier ID
        /// </summary>
        [JsonPropertyName("carrier_id")]
        public string CarrierId { get; set; } = string.Empty;

        /// <summary>
        /// Whether the carrier can be used for targeting
        /// </summary>
        [JsonPropertyName("in_use")]
        public bool InUse { get; set; }

        /// <summary>
        /// Carrier name
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// List of HNI (MCC/MNC) IDs
        /// </summary>
        [JsonPropertyName("value")]
        public List<HniValue> Value { get; set; } = new();
    }

    /// <summary>
    /// HNI value information
    /// </summary>
    public class HniValue
    {
        /// <summary>
        /// HNI (MCC/MNC) ID
        /// </summary>
        [JsonPropertyName("hni_id")]
        public string HniId { get; set; } = string.Empty;

        /// <summary>
        /// Whether the HNI ID can be used for targeting
        /// </summary>
        [JsonPropertyName("in_use")]
        public bool InUse { get; set; }
    }

    // ========== Get Targeting List Models ==========

    /// <summary>
    /// Response for getting targeting tags (ISP)
    /// </summary>
    public class GetTargetingListResponse
    {
        /// <summary>
        /// Information about the targeting tags
        /// </summary>
        [JsonPropertyName("targeting_tag_list")]
        public List<TargetingTagInfo> TargetingTagList { get; set; } = new();

        /// <summary>
        /// Information about all the parent targeting tags
        /// </summary>
        [JsonPropertyName("parent_tags")]
        public List<TargetingTagInfo> ParentTags { get; set; } = new();
    }

    /// <summary>
    /// Targeting tag information for ISP
    /// </summary>
    public class TargetingTagInfo
    {
        /// <summary>
        /// The targeting type that the targeting tag is used for
        /// </summary>
        [JsonPropertyName("targeting_type")]
        public string TargetingType { get; set; } = string.Empty;

        /// <summary>
        /// The name of the targeting tag
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Information about the status of the targeting tag
        /// </summary>
        [JsonPropertyName("status_info")]
        public TargetingTagStatusInfo StatusInfo { get; set; } = new();

        /// <summary>
        /// Information about the targeting tag that is used for Internet service provider targeting
        /// </summary>
        [JsonPropertyName("isp")]
        public TargetingTagIsp? Isp { get; set; }

        /// <summary>
        /// Information about the targeting tag that is used for location targeting
        /// </summary>
        [JsonPropertyName("geo")]
        public TargetingTagGeo? Geo { get; set; }
    }

    // ========== Get Contextual Tag Models ==========

    /// <summary>
    /// Response for getting available contextual tags
    /// </summary>
    public class GetContextualTagResponse
    {
        /// <summary>
        /// Contextual tag list
        /// </summary>
        [JsonPropertyName("contextual_tag_list")]
        public List<ContextualTag> ContextualTagList { get; set; } = new();
    }

    /// <summary>
    /// Contextual tag information
    /// </summary>
    public class ContextualTag
    {
        /// <summary>
        /// Contextual tag ID
        /// </summary>
        [JsonPropertyName("contextual_tag_id")]
        public string ContextualTagId { get; set; } = string.Empty;

        /// <summary>
        /// Contextual tag name
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Contextual tag category
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Content lineup types
        /// </summary>
        [JsonPropertyName("content_lineup_type")]
        public string? ContentLineupType { get; set; }

        /// <summary>
        /// Contextual tag status
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Contextual tag description
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// The advertising objectives that the contextual tag supports
        /// </summary>
        [JsonPropertyName("objective_types")]
        public List<string> ObjectiveTypes { get; set; } = new();

        /// <summary>
        /// The country or region codes that the contextual tag supports
        /// </summary>
        [JsonPropertyName("region_codes")]
        public List<string> RegionCodes { get; set; } = new();
    }

    // ========== Get Contextual Tag Info Models ==========

    /// <summary>
    /// Response for getting details of contextual tags
    /// </summary>
    public class GetContextualTagInfoResponse
    {
        /// <summary>
        /// Contextual tag list
        /// </summary>
        [JsonPropertyName("contextual_tag_list")]
        public List<ContextualTag> ContextualTagList { get; set; } = new();
    }

    // ========== Phone Region Code Models ==========

    /// <summary>
    /// Response for getting region calling codes and region codes for phone numbers
    /// </summary>
    public class PhoneRegionCodeResponse
    {
        /// <summary>
        /// Information about the region codes, region names, and region calling codes for phone numbers
        /// </summary>
        [JsonPropertyName("phone_region_code_infos")]
        public List<PhoneRegionCodeInfo> PhoneRegionCodeInfos { get; set; } = new();
    }

    /// <summary>
    /// Information about phone region codes
    /// </summary>
    public class PhoneRegionCodeInfo
    {
        /// <summary>
        /// The name of the region of a phone number
        /// </summary>
        [JsonPropertyName("phone_region_name")]
        public string PhoneRegionName { get; set; } = string.Empty;

        /// <summary>
        /// The region code for a phone number
        /// </summary>
        [JsonPropertyName("phone_region_code")]
        public string PhoneRegionCode { get; set; } = string.Empty;

        /// <summary>
        /// The region calling code for a phone number
        /// </summary>
        [JsonPropertyName("phone_region_calling_code")]
        public string PhoneRegionCallingCode { get; set; } = string.Empty;
    }

    // ========== Timezone Models ==========

    /// <summary>
    /// Response for getting time zones
    /// </summary>
    public class TimezoneResponse
    {
        /// <summary>
        /// Time Zone list
        /// </summary>
        [JsonPropertyName("timezones")]
        public List<TimezoneInfo> Timezones { get; set; } = new();
    }

    /// <summary>
    /// Information about time zones
    /// </summary>
    public class TimezoneInfo
    {
        /// <summary>
        /// Time zone
        /// </summary>
        [JsonPropertyName("timezone")]
        public string Timezone { get; set; } = string.Empty;

        /// <summary>
        /// Offset from GMT
        /// </summary>
        [JsonPropertyName("gmt_offset")]
        public string GmtOffset { get; set; } = string.Empty;
    }

    // ========== Open URL Models ==========

    /// <summary>
    /// Response for getting a TikTok in-app link
    /// </summary>
    public class OpenUrlResponse
    {
        /// <summary>
        /// TikTok internal link
        /// </summary>
        [JsonPropertyName("open_url")]
        public string OpenUrl { get; set; } = string.Empty;

        /// <summary>
        /// Countries and/or regions that the sticker (special effect) can be used
        /// </summary>
        [JsonPropertyName("supported_regions")]
        public List<string>? SupportedRegions { get; set; }
    }

    // ========== Content Exclusion Models ==========

    /// <summary>
    /// Response for getting available content exclusion categories
    /// </summary>
    public class ContentExclusionResponse
    {
        /// <summary>
        /// A list of content exclusion categories
        /// </summary>
        [JsonPropertyName("excluded_category_list")]
        public List<ContentExclusionCategory> ExcludedCategoryList { get; set; } = new();

        /// <summary>
        /// A list of vertical categories containing sensitive content
        /// </summary>
        [JsonPropertyName("vertical_sensitivity_list")]
        public List<ContentExclusionCategory> VerticalSensitivityList { get; set; } = new();
    }

    /// <summary>
    /// Response for getting details of content exclusion categories
    /// </summary>
    public class ContentExclusionInfoResponse
    {
        /// <summary>
        /// A list of content categories or vertical categories
        /// </summary>
        [JsonPropertyName("content_exclusion_list")]
        public List<ContentExclusionCategory> ContentExclusionList { get; set; } = new();
    }

    /// <summary>
    /// Information about content exclusion categories
    /// </summary>
    public class ContentExclusionCategory
    {
        /// <summary>
        /// Category ID
        /// </summary>
        [JsonPropertyName("category_id")]
        public string CategoryId { get; set; } = string.Empty;

        /// <summary>
        /// Category name
        /// </summary>
        [JsonPropertyName("category_name")]
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// The country or region codes that the category supports
        /// </summary>
        [JsonPropertyName("supported_regions")]
        public List<string> SupportedRegions { get; set; } = new();

        /// <summary>
        /// Category description
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Category type
        /// </summary>
        [JsonPropertyName("category_type")]
        public string CategoryType { get; set; } = string.Empty;
    }

    // ========== Bid Recommendation Models ==========

    /// <summary>
    /// Request body for getting a suggested bid
    /// </summary>
    public class BidRecommendRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// Campaign objective
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string ObjectiveType { get; set; } = string.Empty;

        /// <summary>
        /// IDs of the locations that you want to target
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string> LocationIds { get; set; } = new();

        /// <summary>
        /// Conversion event for the ad group
        /// </summary>
        [JsonPropertyName("external_action")]
        public string? ExternalAction { get; set; }
    }

    /// <summary>
    /// Response for getting a suggested bid
    /// </summary>
    public class BidRecommendResponse
    {
        /// <summary>
        /// Recommended bid value
        /// </summary>
        [JsonPropertyName("bid")]
        public decimal Bid { get; set; }
    }

    // ========== VBO Status Models ==========

    /// <summary>
    /// Response for checking Value-Based Optimization eligibility
    /// </summary>
    public class VboStatusResponse
    {
        /// <summary>
        /// The status of VBO for Web or VBO IAP for App for the settings in the request
        /// </summary>
        [JsonPropertyName("vo_status")]
        public string VoStatus { get; set; } = string.Empty;

        /// <summary>
        /// The status of the Cost Cap bidding strategy for the settings in the request when VBO for Web or VBO IAP for App is enabled
        /// </summary>
        [JsonPropertyName("vo_min")]
        public string VoMin { get; set; } = string.Empty;

        /// <summary>
        /// The status of the bidding strategy Minimum ROAS for the settings in the request when VBO for Web or VBO IAP for App is enabled
        /// </summary>
        [JsonPropertyName("vo_min_roas")]
        public string VoMinRoas { get; set; } = string.Empty;

        /// <summary>
        /// The status of the bidding strategy Highest Value for the settings in the request when VBO for Web or VBO IAP for App is enabled
        /// </summary>
        [JsonPropertyName("vo_highest_value")]
        public string VoHighestValue { get; set; } = string.Empty;

        /// <summary>
        /// The status of the Day 7 bidding strategy Minimum ROAS (Day 7 ROAS) for the settings in the request when VBO IAA for App is enabled
        /// </summary>
        [JsonPropertyName("vo_iaa_min_roas")]
        public string? VoIaaMinRoas { get; set; }

        /// <summary>
        /// The status of the Day 7 bidding strategy Highest Value (Day 7 Highest Value) for the settings in the request when VBO IAA for App is enabled
        /// </summary>
        [JsonPropertyName("vo_iaa_highest_value")]
        public string? VoIaaHighestValue { get; set; }

        /// <summary>
        /// The status of the Day 0 bidding strategy Minimum ROAS (Day 0 ROAS) for the settings in the request when VBO IAA for App is enabled
        /// </summary>
        [JsonPropertyName("vo_iaa_min_roas_zero_day")]
        public string? VoIaaMinRoasZeroDay { get; set; }

        /// <summary>
        /// The status of the Day 0 bidding strategy Highest Value (Day 0 Highest Value) for the settings in the request when VBO IAA for App is enabled
        /// </summary>
        [JsonPropertyName("vo_iaa_highest_value_zero_day")]
        public string? VoIaaHighestValueZeroDay { get; set; }

        /// <summary>
        /// The status of the Day 7 bidding strategy Minimum ROAS (Day 7 ROAS) for the settings in the request when VBO IAP for App is enabled
        /// </summary>
        [JsonPropertyName("roas_status_day7")]
        public string? RoasStatusDay7 { get; set; }

        /// <summary>
        /// The status of the Day 7 bidding strategy Highest Value (Day 7 Highest Value) for the settings in the request when VBO IAP for App is enabled
        /// </summary>
        [JsonPropertyName("highest_value_status_day7")]
        public string? HighestValueStatusDay7 { get; set; }

        /// <summary>
        /// The status of the Day 0 bidding strategy Minimum ROAS (Day 0 ROAS) for the settings in the request when VBO IAP for App is enabled
        /// </summary>
        [JsonPropertyName("roas_status_day0")]
        public string? RoasStatusDay0 { get; set; }

        /// <summary>
        /// The status of the Day 0 bidding strategy Highest Value (Day 0 Highest Value) for the settings in the request when VBO IAP for App is enabled
        /// </summary>
        [JsonPropertyName("highest_value_status_day0")]
        public string? HighestValueStatusDay0 { get; set; }
    }

    // ========== Brand Safety Partner Models ==========

    /// <summary>
    /// Response for getting the authorization status of a Brand Safety partner
    /// </summary>
    public class BrandSafetyPartnerStatusResponse
    {
        /// <summary>
        /// Authorization status
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;
    }

    // ========== URL Validation Models ==========

    /// <summary>
    /// Response for getting the verification results of a URL
    /// </summary>
    public class UrlValidationResponse
    {
        /// <summary>
        /// Information about the URL
        /// </summary>
        [JsonPropertyName("url_info")]
        public UrlInfo UrlInfo { get; set; } = new();
    }

    /// <summary>
    /// Information about the URL
    /// </summary>
    public class UrlInfo
    {
        /// <summary>
        /// Information about the verification results for the URL
        /// </summary>
        [JsonPropertyName("validate_info")]
        public ValidateInfo ValidateInfo { get; set; } = new();
    }

    /// <summary>
    /// Information about the verification results for the URL
    /// </summary>
    public class ValidateInfo
    {
        /// <summary>
        /// Whether the URL is a custom URL scheme
        /// </summary>
        [JsonPropertyName("is_scheme_link")]
        public bool IsSchemeLink { get; set; }

        /// <summary>
        /// Whether the URL is a valid URL (Apple's universal link, Android App Link, or custom URL scheme)
        /// </summary>
        [JsonPropertyName("is_valid_url")]
        public bool IsValidUrl { get; set; }

        /// <summary>
        /// Whether the URL is a valid custom URL scheme
        /// </summary>
        [JsonPropertyName("is_valid_scheme")]
        public bool IsValidScheme { get; set; }
    }

}
