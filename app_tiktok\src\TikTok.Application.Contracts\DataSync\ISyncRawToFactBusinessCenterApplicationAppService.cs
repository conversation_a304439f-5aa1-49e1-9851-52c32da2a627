using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service interface cho việc đồng bộ dữ liệu từ Raw sang Fact
    /// </summary>
    public interface ISyncRawToFactBusinessCenterApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ Business Centers từ Raw sang Fact
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncBusinessCentersAsync();

        /// <summary>
        /// Đồng bộ Business Centers từ Raw sang Fact theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncBusinessCentersAsync(string bcId);

        /// <summary>
        /// Đồng bộ Ad Accounts từ Raw sang Fact
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncAdAccountsAsync();

        /// <summary>
        /// Đồng bộ Ad Accounts từ Raw sang Fact theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncAdAccountsAsync(string bcId);

        /// <summary>
        /// Đồng bộ tất cả dữ liệu từ Raw sang Fact
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncAllAsync();

        /// <summary>
        /// Đồng bộ tất cả dữ liệu từ Raw sang Fact theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncAllAsync(string bcId);
    }
}
