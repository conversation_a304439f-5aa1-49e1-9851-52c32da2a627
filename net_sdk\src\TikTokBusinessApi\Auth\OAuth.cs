/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Net.Http;

namespace TikTokBusinessApi.Auth
{
    /// <summary>
    /// OAuth authentication implementation
    /// </summary>
    public class OAuth : IAuthentication
    {
        /// <summary>
        /// Access token for OAuth authentication
        /// </summary>
        public string? AccessToken { get; set; }

        /// <summary>
        /// Initializes a new instance of the OAuth class
        /// </summary>
        public OAuth()
        {
        }

        /// <summary>
        /// Initializes a new instance of the OAuth class with an access token
        /// </summary>
        /// <param name="accessToken">Access token</param>
        public OAuth(string accessToken)
        {
            AccessToken = accessToken;
        }

        /// <summary>
        /// Applies OAuth authentication to the HTTP request
        /// </summary>
        /// <param name="request">HTTP request message</param>
        public void ApplyToRequest(HttpRequestMessage request)
        {
            if (!string.IsNullOrEmpty(AccessToken))
            {
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", AccessToken);
            }
        }
    }
}
