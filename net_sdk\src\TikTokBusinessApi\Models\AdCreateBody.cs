/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for creating an ad
    /// </summary>
    public class AdCreateBody
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        [Required]
        public string AdgroupId { get; set; } = string.Empty;

        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// List of creatives for the ad
        /// </summary>
        [JsonPropertyName("creatives")]
        [Required]
        public List<AdcreateCreatives> Creatives { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AdCreateBody class
        /// </summary>
        public AdCreateBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdCreateBody class with parameters
        /// </summary>
        /// <param name="adgroupId">Ad group ID</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="creatives">List of creatives</param>
        public AdCreateBody(string adgroupId, string advertiserId, List<AdcreateCreatives> creatives)
        {
            AdgroupId = adgroupId;
            AdvertiserId = advertiserId;
            Creatives = creatives;
        }

        /// <summary>
        /// Sets the ad group ID and returns this instance for method chaining
        /// </summary>
        /// <param name="adgroupId">Ad group ID</param>
        /// <returns>This instance</returns>
        public AdCreateBody SetAdgroupId(string adgroupId)
        {
            AdgroupId = adgroupId;
            return this;
        }

        /// <summary>
        /// Sets the advertiser ID and returns this instance for method chaining
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>This instance</returns>
        public AdCreateBody SetAdvertiserId(string advertiserId)
        {
            AdvertiserId = advertiserId;
            return this;
        }

        /// <summary>
        /// Sets the creatives and returns this instance for method chaining
        /// </summary>
        /// <param name="creatives">List of creatives</param>
        /// <returns>This instance</returns>
        public AdCreateBody SetCreatives(List<AdcreateCreatives> creatives)
        {
            Creatives = creatives;
            return this;
        }

        /// <summary>
        /// Adds a creative to the list and returns this instance for method chaining
        /// </summary>
        /// <param name="creative">Creative to add</param>
        /// <returns>This instance</returns>
        public AdCreateBody AddCreative(AdcreateCreatives creative)
        {
            Creatives.Add(creative);
            return this;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCreateBody(AdgroupId={AdgroupId}, AdvertiserId={AdvertiserId}, Creatives={Creatives?.Count ?? 0} items)";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not AdCreateBody other)
                return false;

            return AdgroupId == other.AdgroupId &&
                   AdvertiserId == other.AdvertiserId &&
                   Creatives.SequenceEqual(other.Creatives);
        }

        /// <summary>
        /// Serves as the default hash function
        /// </summary>
        /// <returns>A hash code for the current object</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AdgroupId, AdvertiserId, Creatives);
        }
    }
}
