/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Playable Ads operations
    /// </summary>
    public class PlayableAdsApi : IPlayableAdsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<PlayableAdsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the PlayableAdsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public PlayableAdsApi(IApiClient apiClient, ILogger<PlayableAdsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Upload a playable creative
        /// </summary>
        /// <param name="request">Playable upload request</param>
        /// <param name="playablePackage">Playable Ad asset package (ZIP file, max 5MB)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing playable ID</returns>
        //public async Task<PlayableUploadResponse> UploadPlayableAsync(
        //    PlayableUploadRequest request,
        //    byte[]? playablePackage = null,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //    // Validate upload type and required parameters
        //    if (request.UploadType == "UPLOAD_BY_FILE" && playablePackage == null)
        //        throw new ArgumentException("Playable package is required when upload_type is UPLOAD_BY_FILE", nameof(playablePackage));
        //    if (request.UploadType == "UPLOAD_BY_FILE_ID" && string.IsNullOrWhiteSpace(request.FileId))
        //        throw new ArgumentException("File ID is required when upload_type is UPLOAD_BY_FILE_ID", nameof(request));

        //    _logger?.LogInformation("Uploading playable creative for advertiser {AdvertiserId} with upload type {UploadType}", 
        //        request.AdvertiserId, request.UploadType);

        //    object body;
        //    if (request.UploadType == "UPLOAD_BY_FILE" && playablePackage != null)
        //    {
        //        // Create multipart form data content for file upload
        //        var content = new MultipartFormDataContent();
        //        content.Add(new StringContent(request.AdvertiserId), "advertiser_id");
        //        content.Add(new StringContent(request.UploadType ?? "UPLOAD_BY_FILE"), "upload_type");
        //        content.Add(new ByteArrayContent(playablePackage), "playable_package", "playable.zip");
        //        body = content;
        //    }
        //    else
        //    {
        //        // Use JSON body for FILE_ID uploads
        //        body = request;
        //    }

        //    return await _apiClient.CallApiAsync<PlayableUploadResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{PlayableAdsEndpoints.UploadPlayable}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Check the validation and audit status of the uploaded playable creative
        /// </summary>
        /// <param name="request">Playable validate request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing playable status and details</returns>
        //public async Task<PlayableValidateResponse> ValidatePlayableAsync(
        //    PlayableValidateRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.PlayableId))
        //        throw new ArgumentException("Playable ID cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Validating playable creative {PlayableId} for advertiser {AdvertiserId}", 
        //        request.PlayableId, request.AdvertiserId);

        //    var queryParams = new Dictionary<string, string>
        //    {
        //        ["advertiser_id"] = request.AdvertiserId,
        //        ["playable_id"] = request.PlayableId
        //    };

        //    return await _apiClient.CallApiAsync<PlayableValidateResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{PlayableAdsEndpoints.ValidatePlayable}",
        //        HttpMethod.Get,
        //        queryParams: queryParams,
        //        body: null,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Save a playable creative
        /// </summary>
        /// <param name="request">Playable save request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing saved playable details</returns>
        //public async Task<PlayableSaveResponse> SavePlayableAsync(
        //    PlayableSaveRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.PlayableId))
        //        throw new ArgumentException("Playable ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.PlayableName))
        //        throw new ArgumentException("Playable name cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Saving playable creative {PlayableId} with name {PlayableName} for advertiser {AdvertiserId}", 
        //        request.PlayableId, request.PlayableName, request.AdvertiserId);

        //    return await _apiClient.CallApiAsync<PlayableSaveResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{PlayableAdsEndpoints.SavePlayable}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get a list of playable creatives
        /// </summary>
        /// <param name="request">Playable get request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of playable creatives</returns>
        public async Task<PlayableGetResponse> GetPlayablesAsync(
            PlayableGetRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            // Validate pagination parameters
            if (request.Page.HasValue && request.Page.Value < 1)
                throw new ArgumentException("Page must be greater than or equal to 1", nameof(request));
            if (request.PageSize.HasValue && (request.PageSize.Value < 1 || request.PageSize.Value > 100))
                throw new ArgumentException("Page size must be between 1 and 100", nameof(request));

            _logger?.LogInformation("Getting playable creatives for advertiser {AdvertiserId}", request.AdvertiserId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId
            };

            if (!string.IsNullOrWhiteSpace(request.PlayableId))
                queryParams["playable_id"] = request.PlayableId;
            if (!string.IsNullOrWhiteSpace(request.PlayableName))
                queryParams["playable_name"] = request.PlayableName;
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();
            if (!string.IsNullOrWhiteSpace(request.PlayableUrl))
                queryParams["playable_url"] = request.PlayableUrl;
            if (!string.IsNullOrWhiteSpace(request.Status))
                queryParams["status"] = request.Status;
            if (!string.IsNullOrWhiteSpace(request.OperationStatus))
                queryParams["operation_status"] = request.OperationStatus;

            return await _apiClient.CallApiAsync<PlayableGetResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{PlayableAdsEndpoints.GetPlayables}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Delete a playable creative
        /// </summary>
        /// <param name="request">Playable delete request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deleted playable ID</returns>
        //public async Task<PlayableDeleteResponse> DeletePlayableAsync(
        //    PlayableDeleteRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.PlayableId))
        //        throw new ArgumentException("Playable ID cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Deleting playable creative {PlayableId} for advertiser {AdvertiserId}",
        //        request.PlayableId, request.AdvertiserId);

        //    return await _apiClient.CallApiAsync<PlayableDeleteResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{PlayableAdsEndpoints.DeletePlayable}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}
    }
}
