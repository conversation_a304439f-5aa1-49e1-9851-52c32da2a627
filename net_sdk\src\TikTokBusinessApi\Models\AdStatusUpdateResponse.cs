/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response data for ad status update requests
    /// </summary>
    public class AdStatusUpdateResponse
    {
        /// <summary>
        /// List of updated ad IDs
        /// </summary>
        [JsonPropertyName("ad_ids")]
        public List<string>? AdIds { get; set; }

        /// <summary>
        /// List of updated ACO ad IDs
        /// </summary>
        [JsonPropertyName("aco_ad_ids")]
        public List<string>? AcoAdIds { get; set; }

        /// <summary>
        /// The operation that was executed. Enum values: DELETE, DISABLE, ENABLE
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// List of errors that occurred during status update
        /// </summary>
        [JsonPropertyName("errors")]
        public List<AdOperationError>? Errors { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdStatusUpdateResponse class
        /// </summary>
        public AdStatusUpdateResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdStatusUpdateResponse class with parameters
        /// </summary>
        /// <param name="adIds">List of updated ad IDs</param>
        /// <param name="acoAdIds">List of updated ACO ad IDs</param>
        /// <param name="status">Operation status</param>
        public AdStatusUpdateResponse(List<string>? adIds = null, List<string>? acoAdIds = null, string? status = null)
        {
            AdIds = adIds;
            AcoAdIds = acoAdIds;
            Status = status;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdStatusUpdateResponse(AdIds={AdIds?.Count ?? 0} items, AcoAdIds={AcoAdIds?.Count ?? 0} items, Status={Status})";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not AdStatusUpdateResponse other)
                return false;

            return Equals(AdIds, other.AdIds) &&
                   Equals(AcoAdIds, other.AcoAdIds) &&
                   Status == other.Status;
        }

        /// <summary>
        /// Returns the hash code for this instance
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AdIds, AcoAdIds, Status);
        }
    }
}
