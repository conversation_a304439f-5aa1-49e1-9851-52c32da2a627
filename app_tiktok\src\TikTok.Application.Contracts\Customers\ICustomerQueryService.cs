using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Enums;
using Volo.Abp.DependencyInjection;

namespace TikTok.Customers
{
    /// <summary>
    /// Service interface để truy vấn dữ liệu khách hàng cho các Activity
    /// </summary>
    public interface ICustomerQueryService : IScopedDependency
    {
        /// <summary>
        /// Lấy danh sách khách hàng theo AdvertiserIds
        /// </summary>
        /// <param name="advertiserIds">Danh sách AdvertiserIds</param>
        /// <param name="includeAdAccounts">C<PERSON> bao gồm thông tin AdAccounts không</param>
        /// <returns>Danh sách CustomerAdvertiserDto</returns>
        Task<List<CustomerAdvertiserDto>> GetCustomersByAdvertiserIdsAsync(
            List<string> advertiserIds, 
            bool includeAdAccounts = true);

        /// <summary>
        /// L<PERSON><PERSON> danh sách khách hàng theo Business Center ID
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="includeAdAccounts">Có bao gồm thông tin AdAccounts không</param>
        /// <returns>Danh sách CustomerAdvertiserDto</returns>
        Task<List<CustomerAdvertiserDto>> GetCustomersByBcIdAsync(
            string bcId, 
            bool includeAdAccounts = true);

        /// <summary>
        /// Lấy tất cả khách hàng
        /// </summary>
        /// <param name="includeAdAccounts">Có bao gồm thông tin AdAccounts không</param>
        /// <returns>Danh sách CustomerAdvertiserDto</returns>
        Task<List<CustomerAdvertiserDto>> GetAllCustomersAsync(bool includeAdAccounts = true);

        /// <summary>
        /// Lấy thông tin khách hàng theo CustomerId
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="includeAdAccounts">Có bao gồm thông tin AdAccounts không</param>
        /// <returns>CustomerAdvertiserDto</returns>
        Task<CustomerAdvertiserDto?> GetCustomerByCustomerIdAsync(
            string customerId, 
            bool includeAdAccounts = true);
    }
}
