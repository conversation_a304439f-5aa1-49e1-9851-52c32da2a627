/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using TikTokBusinessApi.Auth;
using TikTokBusinessApi.Core;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating TikTok authentication usage
    /// </summary>
    public class AuthenticationExample
    {
        /// <summary>
        /// Example 1: Basic configuration with automatic authentication
        /// </summary>
        public static async Task BasicAuthenticationExample()
        {
            // Create configuration with app_id and access_token
            var config = Configuration.CreateWithCredentials(
                appId: "your_app_id_here",
                accessToken: "your_access_token_here"
            );

            // Create API client - TikTok authentication is automatically configured
            var apiClient = config.CreateApiClient();

            // When you make API calls, app_id and access_token are automatically injected
            // Example: This would automatically include app_id and access_token in the request body
            var response = await apiClient.CallApiAsync<Dictionary<string, object>>(
                path: "/tt_user/token_info/get/",
                method: HttpMethod.Post,
                queryParams: null,
                body: new { /* your other parameters */ },
                headerParams: null,
                authNames: new string[] { "tiktok" }
            );

            Console.WriteLine("API call completed with automatic authentication");
        }

        /// <summary>
        /// Example 2: Dynamic access token management
        /// </summary>
        public static async Task DynamicTokenExample()
        {
            // Create configuration with just app_id initially
            var config = Configuration.CreateWithCredentials(
                appId: "your_app_id_here",
                accessToken: null // No initial token
            );

            var apiClient = config.CreateApiClient();

            // Set access token dynamically (e.g., after user login)
            await apiClient.SetAccessTokenAsync("user_specific_access_token");

            // Make API call - app_id and the new access_token are automatically included
            var response = await apiClient.CallApiAsync<Dictionary<string, object>>(
                path: "/tt_user/token_info/get/",
                method: HttpMethod.Post,
                queryParams: null,
                body: null,
                headerParams: null,
                authNames: new string[] { "tiktok" }
            );

            // Change access token for different user
            await apiClient.SetAccessTokenAsync("another_user_access_token");

            // Subsequent calls use the new token
            var response2 = await apiClient.CallApiAsync<Dictionary<string, object>>(
                path: "/tt_user/token_info/get/",
                method: HttpMethod.Post,
                queryParams: null,
                body: null,
                headerParams: null,
                authNames: new string[] { "tiktok" }
            );

            Console.WriteLine("Dynamic token management completed");
        }

        /// <summary>
        /// Example 3: Environment-specific authentication
        /// </summary>
        public static async Task EnvironmentSpecificExample()
        {
            // Production environment
            var prodConfig = Configuration.CreateForProduction(
                appId: "prod_app_id",
                accessToken: "prod_access_token"
            );

            // Sandbox environment
            var sandboxConfig = Configuration.CreateForSandbox(
                appId: "sandbox_app_id", 
                accessToken: "sandbox_access_token"
            );

            // Both clients automatically handle authentication
            var prodClient = prodConfig.CreateApiClient();
            var sandboxClient = sandboxConfig.CreateApiClient();

            Console.WriteLine("Environment-specific authentication configured");
        }

        /// <summary>
        /// Example 4: Manual authentication configuration
        /// </summary>
        public static async Task ManualAuthenticationExample()
        {
            var apiClient = new ApiClient();

            // Manually configure TikTok authentication
            var tikTokAuth = TikTokAuthentication.WithCredentials(
                appId: "your_app_id",
                accessToken: "your_access_token"
            );

            // apiClient.AddAuthentication("tiktok", tikTokAuth); // Method not implemented

            // API calls with authentication context
            var authContext = AuthenticationContext.Default();
            var response = await apiClient.CallApiAsync<Dictionary<string, object>>(
                path: "/tt_user/token_info/get/",
                method: HttpMethod.Post,
                authenticationContext: authContext
            );

            Console.WriteLine("Manual authentication configuration completed");
        }

        /// <summary>
        /// Example 5: Authentication validation
        /// </summary>
        public static async Task AuthenticationValidationExample()
        {
            var tikTokAuth = TikTokAuthentication.WithAppId("your_app_id");

            // Validate authentication (will fail because access_token is missing)
            var validationResult = await tikTokAuth.ValidateAsync(requireAccessToken: true);
            
            if (!validationResult.IsValid)
            {
                Console.WriteLine("Authentication validation failed:");
                foreach (var error in validationResult.Errors)
                {
                    Console.WriteLine($"- {error}");
                }
            }

            // Set access token and validate again
            await tikTokAuth.SetAccessTokenAsync("valid_access_token");
            var validationResult2 = await tikTokAuth.ValidateAsync(requireAccessToken: true);
            
            if (validationResult2.IsValid)
            {
                Console.WriteLine("Authentication validation passed");
            }
        }

        /// <summary>
        /// Example 6: Request body merging demonstration
        /// </summary>
        public static async Task RequestBodyMergingExample()
        {
            var config = Configuration.CreateWithCredentials(
                appId: "your_app_id",
                accessToken: "your_access_token"
            );

            var apiClient = config.CreateApiClient();

            // Your business logic parameters
            var requestData = new
            {
                advertiser_id = "123456789",
                filtering = new
                {
                    campaign_ids = new[] { "campaign_1", "campaign_2" }
                },
                page_info = new
                {
                    page = 1,
                    page_size = 10
                }
            };

            // When this call is made, the final request body will automatically include:
            // {
            //   "app_id": "your_app_id",
            //   "access_token": "your_access_token", 
            //   "advertiser_id": "123456789",
            //   "filtering": { "campaign_ids": ["campaign_1", "campaign_2"] },
            //   "page_info": { "page": 1, "page_size": 10 }
            // }
            var response = await apiClient.CallApiAsync<Dictionary<string, object>>(
                path: "/campaign/get/",
                method: HttpMethod.Post,
                queryParams: null,
                body: requestData,
                headerParams: null,
                authNames: new string[] { "tiktok" }
            );

            Console.WriteLine("Request body merging completed automatically");
        }
    }
}
