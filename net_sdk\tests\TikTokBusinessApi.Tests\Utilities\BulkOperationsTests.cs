/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using TikTokBusinessApi.Utilities;
using Xunit;

namespace TikTokBusinessApi.Tests.Utilities
{
    public class BulkOperationsTests
    {
        [Fact]
        public void Batch_WithValidInput_ReturnsBatches()
        {
            // Arrange
            var items = Enumerable.Range(1, 10).ToList();
            var batchSize = 3;

            // Act
            var batches = items.Batch(batchSize).ToList();

            // Assert
            Assert.Equal(4, batches.Count);
            Assert.Equal(3, batches[0].Count());
            Assert.Equal(3, batches[1].Count());
            Assert.Equal(3, batches[2].Count());
            Assert.Equal(1, batches[3].Count());
        }

        [Fact]
        public void Batch_WithNullSource_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => BulkOperations.Batch<int>(null!, 5));
        }

        [Fact]
        public void Batch_WithZeroBatchSize_ThrowsArgumentException()
        {
            // Arrange
            var items = new List<int> { 1, 2, 3 };

            // Act & Assert
            Assert.Throws<ArgumentException>(() => items.Batch(0));
        }

        [Fact]
        public void Batch_WithNegativeBatchSize_ThrowsArgumentException()
        {
            // Arrange
            var items = new List<int> { 1, 2, 3 };

            // Act & Assert
            Assert.Throws<ArgumentException>(() => items.Batch(-1));
        }

        [Fact]
        public void Batch_WithEmptySource_ReturnsEmptyResult()
        {
            // Arrange
            var items = new List<int>();

            // Act
            var batches = items.Batch(5).ToList();

            // Assert
            Assert.Empty(batches);
        }

        [Fact]
        public async Task ExecuteBulkOperationAsync_WithValidInput_ReturnsSuccessfulResult()
        {
            // Arrange
            var items = new List<string> { "item1", "item2", "item3" };
            var operation = new Func<IEnumerable<string>, CancellationToken, Task<string>>(
                (batch, ct) => Task.FromResult($"Processed {batch.Count()} items"));

            // Act
            var result = await BulkOperations.ExecuteBulkOperationAsync(items, operation, batchSize: 2);

            // Assert
            Assert.Equal(2, result.SuccessfulOperations.Count);
            Assert.Empty(result.FailedOperations);
            Assert.True(result.IsFullySuccessful);
            Assert.Equal(100.0, result.SuccessRate);
        }

        [Fact]
        public async Task ExecuteBulkOperationAsync_WithNullItems_ThrowsArgumentNullException()
        {
            // Arrange
            var operation = new Func<IEnumerable<string>, CancellationToken, Task<string>>(
                (batch, ct) => Task.FromResult("result"));

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() =>
                BulkOperations.ExecuteBulkOperationAsync<string, string>(null!, operation));
        }

        [Fact]
        public async Task ExecuteBulkOperationAsync_WithFailingOperation_ReturnsFailedResult()
        {
            // Arrange
            var items = new List<string> { "item1", "item2" };
            var operation = new Func<IEnumerable<string>, CancellationToken, Task<string>>(
                (batch, ct) => throw new InvalidOperationException("Test error"));

            // Act
            var result = await BulkOperations.ExecuteBulkOperationAsync(items, operation, batchSize: 1, maxRetries: 0);

            // Assert
            Assert.Empty(result.SuccessfulOperations);
            Assert.Equal(2, result.FailedOperations.Count);
            Assert.False(result.IsFullySuccessful);
            Assert.Equal(0.0, result.SuccessRate);
            Assert.Equal("Test error", result.FailedOperations[0].Error);
        }

        [Fact]
        public async Task ExecuteParallelBulkOperationAsync_WithValidInput_ReturnsSuccessfulResult()
        {
            // Arrange
            var items = new List<string> { "item1", "item2", "item3", "item4" };
            var operation = new Func<IEnumerable<string>, CancellationToken, Task<string>>(
                async (batch, ct) =>
                {
                    await Task.Delay(10, ct); // Simulate work
                    return $"Processed {batch.Count()} items";
                });

            // Act
            var result = await BulkOperations.ExecuteParallelBulkOperationAsync(items, operation, batchSize: 2, maxConcurrency: 2);

            // Assert
            Assert.Equal(2, result.SuccessfulOperations.Count);
            Assert.Empty(result.FailedOperations);
            Assert.True(result.IsFullySuccessful);
        }

        [Fact]
        public void BulkOperationResult_Properties_CalculateCorrectly()
        {
            // Arrange
            var result = new BulkOperationResult<string>();
            result.SuccessfulOperations.AddRange(new[] { "success1", "success2", "success3" });
            result.FailedOperations.Add(new BulkOperationError<string> { Items = new List<string> { "failed1" } });

            // Act & Assert
            Assert.Equal(4, result.TotalOperations);
            Assert.Equal(75.0, result.SuccessRate);
            Assert.False(result.IsFullySuccessful);
            Assert.True(result.HasAnySuccess);
            Assert.True(result.HasAnyFailure);
        }

        [Fact]
        public void BulkOperationResult_EmptyResult_CalculatesCorrectly()
        {
            // Arrange
            var result = new BulkOperationResult<string>();

            // Act & Assert
            Assert.Equal(0, result.TotalOperations);
            Assert.Equal(0.0, result.SuccessRate);
            Assert.False(result.IsFullySuccessful);
            Assert.False(result.HasAnySuccess);
            Assert.False(result.HasAnyFailure);
        }

        [Fact]
        public void BulkOperationError_Properties_SetCorrectly()
        {
            // Arrange
            var exception = new InvalidOperationException("Test exception");
            var items = new List<string> { "item1", "item2" };

            // Act
            var error = new BulkOperationError<string>
            {
                Items = items,
                Error = "Test error",
                Exception = exception,
                RetryCount = 3
            };

            // Assert
            Assert.Equal(items, error.Items);
            Assert.Equal("Test error", error.Error);
            Assert.Equal(exception, error.Exception);
            Assert.Equal(3, error.RetryCount);
            Assert.True(error.Timestamp <= DateTimeOffset.UtcNow);
        }
    }
}
