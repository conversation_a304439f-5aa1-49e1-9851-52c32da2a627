/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Upload File Models

    /// <summary>
    /// Request model for uploading a file
    /// </summary>
    public class FileUploadRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The upload type. Enum values: FILE, URL
        /// </summary>
        [Required]
        [JsonPropertyName("upload_type")]
        public string UploadType { get; set; } = string.Empty;

        /// <summary>
        /// The file type. Enum values: image, music, video, playable
        /// </summary>
        [Required]
        [JsonPropertyName("content_type")]
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// The URL of the file that you want to upload. Required when upload_type is URL
        /// </summary>
        [JsonPropertyName("url")]
        public string? Url { get; set; }

        /// <summary>
        /// The MD5 value of the file, used as a checksum. Required when upload_type is FILE
        /// </summary>
        [JsonPropertyName("signature")]
        public string? Signature { get; set; }

        /// <summary>
        /// The file name. Character limit is 100
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }
    }

    /// <summary>
    /// Response model for file upload
    /// </summary>
    public class FileUploadResponse
    {
        /// <summary>
        /// The ID of the file that you have uploaded
        /// </summary>
        [JsonPropertyName("file_id")]
        public string FileId { get; set; } = string.Empty;

        /// <summary>
        /// The MD5 value of the file
        /// </summary>
        [JsonPropertyName("signature")]
        public string Signature { get; set; } = string.Empty;

        /// <summary>
        /// The file size in bytes
        /// </summary>
        [JsonPropertyName("file_size")]
        public long FileSize { get; set; }

        /// <summary>
        /// The time when the file was uploaded in the format of YYYY-MM-DD HH:MM:SS(UTC+0)
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;
    }

    #endregion

    #region Chunk Upload Models

    /// <summary>
    /// Request model for starting a chunk upload task
    /// </summary>
    public class ChunkUploadStartRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The size of the file that you want to upload, in Bytes
        /// </summary>
        [Required]
        [JsonPropertyName("size")]
        public long Size { get; set; }

        /// <summary>
        /// The content type of the file. Enum values: image, video, music
        /// </summary>
        [Required]
        [JsonPropertyName("content_type")]
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// The file name. The maximum length is 100 characters
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }
    }

    /// <summary>
    /// Response model for starting a chunk upload task
    /// </summary>
    public class ChunkUploadStartResponse
    {
        /// <summary>
        /// The upload task ID. It will be needed in all of the following requests for the same task
        /// </summary>
        [JsonPropertyName("upload_id")]
        public string UploadId { get; set; } = string.Empty;

        /// <summary>
        /// The file name. The maximum length is 100 characters
        /// </summary>
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// The starting offset of the first chunk
        /// </summary>
        [JsonPropertyName("start_offset")]
        public long StartOffset { get; set; }

        /// <summary>
        /// The ending offset of the first chunk
        /// </summary>
        [JsonPropertyName("end_offset")]
        public long EndOffset { get; set; }
    }

    /// <summary>
    /// Request model for transferring a file chunk
    /// </summary>
    public class ChunkTransferRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the upload task
        /// </summary>
        [Required]
        [JsonPropertyName("upload_id")]
        public string UploadId { get; set; } = string.Empty;

        /// <summary>
        /// The MD5 of the chunk, used as a checksum to verify data integrity
        /// </summary>
        [Required]
        [JsonPropertyName("signature")]
        public string Signature { get; set; } = string.Empty;

        /// <summary>
        /// The starting offset of the current chunk
        /// </summary>
        [Required]
        [JsonPropertyName("start_offset")]
        public long StartOffset { get; set; }
    }

    /// <summary>
    /// Response model for transferring a file chunk
    /// </summary>
    public class ChunkTransferResponse
    {
        /// <summary>
        /// The starting offset of the uploaded chunk
        /// </summary>
        [JsonPropertyName("start_offset")]
        public long StartOffset { get; set; }

        /// <summary>
        /// The ending offset of the uploaded chunk
        /// </summary>
        [JsonPropertyName("end_offset")]
        public long EndOffset { get; set; }
    }

    /// <summary>
    /// Request model for finishing a chunk upload task
    /// </summary>
    public class ChunkUploadFinishRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the upload task
        /// </summary>
        [Required]
        [JsonPropertyName("upload_id")]
        public string UploadId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for finishing a chunk upload task
    /// </summary>
    public class ChunkUploadFinishResponse
    {
        /// <summary>
        /// The ID of the file that you have uploaded
        /// </summary>
        [JsonPropertyName("file_id")]
        public string FileId { get; set; } = string.Empty;

        /// <summary>
        /// The name of the file that you have uploaded
        /// </summary>
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// The time when the file was uploaded in the format of YYYY-MM-DD HH:MM:SS(UTC+0)
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;

        /// <summary>
        /// The file size in bytes
        /// </summary>
        [JsonPropertyName("size")]
        public long Size { get; set; }
    }

    #endregion

    #region File Name Check Models

    /// <summary>
    /// File information for checking file names
    /// </summary>
    public class FileNameCheckInfo
    {
        /// <summary>
        /// File name
        /// </summary>
        [Required]
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// File type. Enum values: VIDEO (video), IMAGE (image). Default value: VIDEO
        /// </summary>
        [JsonPropertyName("file_type")]
        public string FileType { get; set; } = "VIDEO";
    }

    /// <summary>
    /// Request model for checking file names
    /// </summary>
    public class FileNameCheckRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Information about the file names to check. Max size: 20
        /// </summary>
        [JsonPropertyName("files")]
        public List<FileNameCheckInfo>? Files { get; set; }

        /// <summary>
        /// File name (for single file check)
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }

        /// <summary>
        /// File type (for single file check). Enum values: VIDEO (video), IMAGE (image). Default value: VIDEO
        /// </summary>
        [JsonPropertyName("file_type")]
        public string? FileType { get; set; }
    }

    /// <summary>
    /// Result for batch file name check
    /// </summary>
    public class FileNameCheckResult
    {
        /// <summary>
        /// File name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Whether the file name is duplicate
        /// </summary>
        [JsonPropertyName("duplicate")]
        public bool Duplicate { get; set; }

        /// <summary>
        /// The material ID of the image or video that already uses the file name
        /// </summary>
        [JsonPropertyName("duplicate_material_id")]
        public string? DuplicateMaterialId { get; set; }
    }

    /// <summary>
    /// Response model for checking file names
    /// </summary>
    public class FileNameCheckResponse
    {
        /// <summary>
        /// Whether the file name is duplicate (for single file check)
        /// </summary>
        [JsonPropertyName("duplicate")]
        public bool? Duplicate { get; set; }

        /// <summary>
        /// The material ID of the image or video that already uses the file name (for single file check)
        /// </summary>
        [JsonPropertyName("duplicate_material_id")]
        public string? DuplicateMaterialId { get; set; }

        /// <summary>
        /// The results for the list of file names checked (for batch check)
        /// </summary>
        [JsonPropertyName("batch_results")]
        public List<FileNameCheckResult>? BatchResults { get; set; }
    }

    #endregion
}
