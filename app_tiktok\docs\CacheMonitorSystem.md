# Cache Monitor System

## Overview

The Cache Monitor System provides a centralized interface for monitoring and managing all caches in the TikTok application. It's designed to be extensible, allowing new cache services to be easily integrated without modifying the monitor itself.

## Features

-   **Centralized Cache Monitoring**: View all active caches in the system
-   **Cache Information Display**: Detailed information about each cache including status, key count, and timestamps
-   **Cache Clearing**: Clear individual caches or all caches at once
-   **Permission-Based Access**: Role-based access control for cache operations
-   **Real-time Statistics**: Summary statistics showing total caches, active caches, error caches, and total keys
-   **Extensible Architecture**: Easy to add new cache services without modifying the monitor

## Accessing the Cache Monitor

Navigate to: `/cache-monitor`

**Required Permissions:**

-   `CacheMonitor.Default` - View cache monitor
-   `CacheMonitor.Clear` - Clear individual caches
-   `CacheMonitor.ClearAll` - Clear all caches

## How to Use

### Viewing Cache Information

1. Access the cache monitor page
2. View the summary statistics at the top:
    - Total Caches
    - Active Caches
    - Error Caches
    - Total Cache Keys
3. Browse the cache list table showing:
    - Cache Name
    - Status (Active/Error/Warning)
    - Active Keys Count
    - Description
    - Actions

### Viewing Detailed Cache Information

1. Click the info button (ℹ️) next to any cache
2. A modal will display detailed information including:
    - Cache details (name, status, active keys, description)
    - Timestamps (created, last accessed, last modified)

### Clearing Caches

#### Clear Individual Cache

1. Click the "Clear" button next to the cache you want to clear
2. Confirm the action in the confirmation dialog
3. The cache will be cleared and the page will refresh

#### Clear All Caches

1. Click the "Clear All Cache" button in the header
2. Confirm the action in the confirmation dialog
3. All caches will be cleared and the page will refresh

### Refreshing Cache Data

Click the "Refresh" button to reload the cache information from the server.

## Adding New Cache Services

To add a new cache service to the monitor, follow these steps:

### 1. Implement the ICacheService Interface

Create a new cache service class that implements `ICacheService`:

```csharp
public class MyNewCacheService : ICacheService
{
    private readonly IDistributedCache _cache;

    public string CacheName => "MyNewCache";

    public MyNewCacheService(IDistributedCache cache)
    {
        _cache = cache;
    }

    public async Task<List<string>> GetCacheKeysAsync()
    {
        // Implementation to get all cache keys
        return new List<string>();
    }

    public async Task<bool> ClearCacheAsync()
    {
        // Implementation to clear all cache entries
        return true;
    }

    public async Task<bool> ClearCacheByKeyAsync(string cacheKey)
    {
        // Implementation to clear specific cache key
        await _cache.RemoveAsync(cacheKey);
        return true;
    }

    public async Task<CacheInfoDto> GetCacheInfoAsync()
    {
        var keys = await GetCacheKeysAsync();
        return new CacheInfoDto
        {
            CacheName = CacheName,
            Status = "Active",
            ActiveKeysCount = keys.Count,
            Description = "Description of my new cache",
            CacheKeys = keys,
            CreatedAt = DateTime.UtcNow,
            LastAccessedAt = DateTime.UtcNow,
            LastModifiedAt = DateTime.UtcNow
        };
    }
}
```

### 2. Register the Service

The service will be automatically registered due to the `IScopedDependency` interface. No additional registration is needed.

### 3. Test the Integration

1. Build and run the application
2. Navigate to the cache monitor
3. Verify your new cache appears in the list
4. Test the cache operations (view info, clear cache)

## Technical Architecture

### Core Components

#### ICacheService Interface

```csharp
public interface ICacheService : IScopedDependency
{
    string CacheName { get; }
    Task<List<string>> GetCacheKeysAsync();
    Task<bool> ClearCacheAsync();
    Task<bool> ClearCacheByKeyAsync(string cacheKey);
    Task<CacheInfoDto> GetCacheInfoAsync();
}
```

#### ICacheMonitorService Interface

```csharp
public interface ICacheMonitorService
{
    Task<List<CacheInfoDto>> GetAllCacheInfoAsync();
    Task<CacheInfoDto> GetCacheInfoByNameAsync(string cacheName);
    Task<ClearCacheResultDto> ClearAllCacheAsync();
    Task<CacheClearResultDto> ClearCacheByNameAsync(string cacheName);
}
```

#### CacheMonitorService Implementation

The service automatically discovers all registered `ICacheService` implementations and provides centralized operations.

### Data Transfer Objects

-   **CacheInfoDto**: Contains detailed information about a cache
-   **ClearCacheRequestDto**: Request model for clearing specific caches
-   **CacheClearResultDto**: Result of clearing a single cache
-   **ClearCacheResultDto**: Result of clearing all caches

### API Endpoints

-   `GET /api/cache-monitor` - Get all cache information
-   `GET /api/cache-monitor/{cacheName}` - Get specific cache information
-   `POST /api/cache-monitor/clear` - Clear specific cache
-   `POST /api/cache-monitor/clear-all` - Clear all caches

### Permissions

-   `CacheMonitor.Default` - Access to cache monitor
-   `CacheMonitor.Clear` - Clear individual caches
-   `CacheMonitor.ClearAll` - Clear all caches

### Frontend Implementation

The cache monitor uses Razor Pages with:

-   Bootstrap modals for confirmation dialogs
-   AJAX calls to API endpoints
-   Toast notifications for success/error messages
-   Responsive design for mobile compatibility

## Best Practices

### Cache Service Implementation

1. **Error Handling**: Always handle exceptions gracefully in cache operations
2. **Performance**: Implement efficient key retrieval methods
3. **Status Reporting**: Provide accurate status information
4. **Descriptions**: Use clear, descriptive cache names and descriptions

### Cache Key Management

1. **Naming Convention**: Use consistent naming patterns for cache keys
2. **Key Discovery**: Implement efficient methods to discover all keys
3. **Key Validation**: Validate cache keys before operations

### Security Considerations

1. **Permission Checks**: Always verify user permissions before cache operations
2. **Input Validation**: Validate all input parameters
3. **Error Messages**: Don't expose sensitive information in error messages

## Troubleshooting

### Common Issues

1. **Cache Not Appearing**: Ensure the cache service implements `ICacheService` and inherits from `IScopedDependency`
2. **Permission Errors**: Verify the user has the required permissions
3. **Clear Operations Failing**: Check if the cache service properly implements clear methods
4. **Performance Issues**: Optimize key retrieval methods for large caches

### Debugging

1. Check application logs for cache-related errors
2. Verify cache service registration in dependency injection
3. Test cache operations individually
4. Monitor cache performance metrics

## Future Enhancements

Potential improvements for the cache monitor system:

1. **Cache Statistics**: Add more detailed statistics (hit rates, miss rates, etc.)
2. **Cache Expiration**: Show expiration times for cache entries
3. **Bulk Operations**: Support for bulk cache operations
4. **Cache History**: Track cache operation history
5. **Performance Monitoring**: Real-time performance metrics
6. **Cache Warming**: Pre-populate frequently accessed caches
7. **Export Functionality**: Export cache information to various formats

## Support

For issues or questions about the cache monitor system:

1. Check the application logs
2. Review this documentation
3. Contact the development team
4. Create an issue in the project repository
