# FactTransactionEntity

## Thông tin bảng dữ liệu

-   **Table**: `FactTransactions`
-   **TablePrefix**: `Fact_`
-   **TableName**: `Fact_FactTransactions`
-   **DbSchema**: `TikTok`

## Mô tả

Fact table lưu trữ thông tin tất cả các giao dịch tài chính từ 3 nguồn dữ liệu khác nhau. **Type field bắt buộc** để ETL biết lưu dữ liệu từ nguồn nào.

## Cấu trúc bảng

| Tên Field            | Kiểu dữ liệu | Bắt buộc | Độ dài | <PERSON>ô tả                                                      |
| -------------------- | ------------ | -------- | ------ | ---------------------------------------------------------- |
| Id                   | Guid         | ✓        | -      | ID duy nhất của bản ghi (Primary Key)                      |
| DimDateId            | int          | ✓        | -      | Khóa ngoại liên kết với Dim_Date (YYYYMMDD)                |
| DimAdAccountId       | Guid?        | ✗        | -      | Khóa ngoại liên kết với Dim_AdAccount (null nếu Type = BC) |
| DimBusinessCenterId  | Guid         | ✓        | -      | Khóa ngoại liên kết với Dim_BusinessCenter                 |
| DimTransactionTypeId | Guid         | ✓        | -      | Khóa ngoại liên kết với Dim_Transaction                    |
| **Type**             | **string**   | **✓**    | **20** | **"BC", "AdAccount", "General" - BẮT BUỘC cho ETL**        |
| EntityId             | string       | ✓        | 100    | ID của entity (BcId hoặc AdvertiserId)                     |
| Amount               | decimal      | ✓        | 18,2   | Số tiền giao dịch                                          |
| Currency             | string       | ✓        | 10     | Tiền tệ                                                    |
| FundsType            | string       | ✓        | 50     | Loại quỹ (CASH, CREDIT)                                    |
| TransferType         | string       | ✗        | 50     | Loại chuyển khoản (chỉ AdAccount)                          |
| InvoiceId            | string       | ✗        | 100    | ID hóa đơn                                                 |
| InvoiceSerialNumber  | string       | ✗        | 100    | Số sê-ri hóa đơn                                           |
| Timezone             | string       | ✓        | 20     | Múi giờ giao dịch                                          |
| Date                 | DateTime     | ✓        | -      | Thời gian giao dịch (UTC)                                  |
| CreationTime         | DateTime     | ✓        | -      | Thời gian tạo bản ghi                                      |

## Type Values và ETL Mapping

### Type = "BC" (từ RawRecordTransactionBcEntity)

-   **EntityId** ← tự động lookup BcId từ context
-   **Amount** ← RawRecordTransactionBcEntity.Amount
-   **Currency** ← RawRecordTransactionBcEntity.Currency
-   **FundsType** ← RawRecordTransactionBcEntity.FundsType
-   **Date** ← RawRecordTransactionBcEntity.Date

### Type = "AdAccount" (từ RawRecordTransactionAdAccountEntity)

-   **EntityId** ← RawRecordTransactionAdAccountEntity.AdvertiserId
-   **Amount** ← RawRecordTransactionAdAccountEntity.Amount
-   **Currency** ← RawRecordTransactionAdAccountEntity.Currency
-   **FundsType** ← RawRecordTransactionAdAccountEntity.FundsType
-   **TransferType** ← RawRecordTransactionAdAccountEntity.TransferType
-   **Date** ← RawRecordTransactionAdAccountEntity.Date

### Type = "General" (từ RawTransactionEntity)

-   **EntityId** ← RawTransactionEntity.BcId
-   **Amount** ← RawTransactionEntity.Amount
-   **Currency** ← RawTransactionEntity.Currency
-   **InvoiceId** ← RawTransactionEntity.InvoiceId
-   **InvoiceSerialNumber** ← RawTransactionEntity.SerialNumber
-   **Date** ← RawTransactionEntity.CreateTime

## Grain Definition

-   **Mỗi dòng đại diện cho**: 1 giao dịch tài chính từ 1 trong 3 nguồn
-   **Unique Key**: (EntityId, Type, Date)

## Business Questions

```sql
-- Tổng giao dịch theo loại
SELECT Type, FundsType, COUNT(*), SUM(Amount)
FROM Fact_GiaoDich
GROUP BY Type, FundsType

-- Giao dịch BC vs AdAccount
SELECT Type, SUM(CASE WHEN Amount > 0 THEN Amount END) as Inflow,
             SUM(CASE WHEN Amount < 0 THEN ABS(Amount) END) as Outflow
FROM FactTransactions GROUP BY Type
```
