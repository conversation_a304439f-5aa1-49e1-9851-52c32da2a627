# RawGmvMaxLiveCampaignReportEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawGmvMaxLiveCampaignReports`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawGmvMaxLiveCampaignReports`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho báo cáo cấp chiến dịch của LIVE GMV Max Campaign. Lưu trữ thông tin tổng quan về hiệu suất chiến dịch LIVE GMV Max bao gồm cấu hình chiến dịch, thông tin TikTok account, ngân sách, mục tiêu tối ưu và các metrics hiệu suất LIVE theo thời gian.

## Bảng dữ liệu: RawGmvMaxLiveCampaignReports

| Tên Field                       | Kiểu dữ liệu     | <PERSON><PERSON><PERSON> buộ<PERSON> | <PERSON><PERSON> dà<PERSON> | <PERSON><PERSON> tả                                                                    |
| ------------------------------- | ---------------- | -------- | ------ | ------------------------------------------------------------------------ |
| Id                              | Guid             | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                   |
| BcId                            | string           | ✅       | 100    | ID Business Center                                                       |
| AdvertiserId                    | string           | ✅       | 100    | ID nhà quảng cáo                                                         |
| StoreId                         | string           | ✅       | 100    | ID TikTok Shop                                                           |
| CampaignId                      | string           | ✅       | 100    | ID chiến dịch LIVE GMV Max                                               |
| CampaignName                    | string           | ❌       | 500    | Tên chiến dịch                                                           |
| OperationStatus                 | string           | ❌       | 20     | Trạng thái ON/OFF chiến dịch (ENABLE, DISABLE)                          |
| TtAccountName                   | string           | ❌       | 200    | Tên TikTok account                                                       |
| TtAccountProfileImageUrl        | string           | ❌       | 1000   | URL hình đại diện TikTok account                                         |
| IdentityId                      | string           | ❌       | 100    | Identity ID (TikTok account ID)                                          |
| BidType                         | string           | ❌       | 20     | Chế độ tối ưu (CUSTOM: Target ROI, NO_BID: Maximum delivery)             |
| ScheduleType                    | string           | ❌       | 50     | Loại lịch trình (Continuously)                                          |
| ScheduleStartTime               | DateTime?        | ❌       | -      | Thời gian bắt đầu chiến dịch (UTC)                                       |
| ScheduleEndTime                 | DateTime?        | ❌       | -      | Thời gian kết thúc chiến dịch (UTC)                                      |
| TargetRoiBudget                 | decimal?         | ❌       | -      | Ngân sách Target ROI                                                     |
| MaxDeliveryBudget               | decimal?         | ❌       | -      | Ngân sách Maximum delivery                                               |
| RoasBid                         | decimal?         | ❌       | -      | Mục tiêu ROI                                                             |
| Cost                            | decimal?         | ❌       | -      | Chi phí quảng cáo (theo đơn vị tiền tệ của ad account)                   |
| NetCost                         | decimal?         | ❌       | -      | Chi phí thực tế (trừ ad credit hoặc coupon)                              |
| Orders                          | int?             | ❌       | -      | Số lượng đơn hàng SKU LIVE cá nhân                                       |
| CostPerOrder                    | decimal?         | ❌       | -      | Chi phí trung bình mỗi đơn hàng LIVE                                     |
| GrossRevenue                    | decimal?         | ❌       | -      | Tổng doanh thu gộp từ đơn hàng LIVE của TikTok Shop                      |
| ROI                             | decimal?         | ❌       | -      | Tỷ suất lợi nhuận từ đơn hàng LIVE                                       |
| LiveViews                       | long?            | ❌       | -      | Số lượt xem LIVE                                                         |
| CostPerLiveView                 | decimal?         | ❌       | -      | Chi phí trung bình mỗi lượt xem LIVE                                     |
| TenSecondLiveViews              | long?            | ❌       | -      | Số lượt xem LIVE ít nhất 10 giây                                        |
| CostPerTenSecondLiveView        | decimal?         | ❌       | -      | Chi phí trung bình mỗi lượt xem LIVE 10 giây                            |
| LiveFollows                     | int?             | ❌       | -      | Số lượt follow profile trong quá trình LIVE                             |
| Currency                        | string           | ✅       | 10     | Tiền tệ - Mã tiền tệ, ví dụ: USD                                         |
| Date                            | DateTime         | ✅       | -      | Ngày giờ tổng hợp báo cáo, UTC format yyyy-MM-dd HH:00:00 (theo giờ)    |
| CreationTime                    | DateTime         | ✅       | -      | Thời gian tạo bản ghi (Audit)                                           |
| CreatorId                       | Guid?            | ❌       | -      | ID người tạo (Audit)                                                     |
| LastModificationTime            | DateTime?        | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                          |
| LastModifierId                  | Guid?            | ❌       | -      | ID người sửa đổi cuối (Audit)                                           |

## Cấu trúc dữ liệu

### Thông tin nhận dạng
-   **BcId**: ID Business Center quản lý
-   **AdvertiserId**: ID tài khoản nhà quảng cáo
-   **StoreId**: ID TikTok Shop liên kết
-   **CampaignId**: ID duy nhất của chiến dịch LIVE GMV Max

### Thông tin chiến dịch
-   **CampaignName**: Tên hiển thị của chiến dịch
-   **OperationStatus**: Trạng thái hoạt động (ENABLE/DISABLE)
-   **ScheduleType**: Loại lịch trình chạy
-   **ScheduleStartTime/ScheduleEndTime**: Thời gian bắt đầu và kết thúc

### Thông tin TikTok Account
-   **TtAccountName**: Tên tài khoản TikTok
-   **TtAccountProfileImageUrl**: URL hình đại diện
-   **IdentityId**: ID định danh TikTok account

### Cấu hình tối ưu hóa
-   **BidType**: Chế độ tối ưu (CUSTOM/NO_BID)
-   **TargetRoiBudget**: Ngân sách cho Target ROI
-   **MaxDeliveryBudget**: Ngân sách cho Maximum delivery
-   **RoasBid**: Mục tiêu ROI

### Metrics hiệu suất tài chính
-   **Cost**: Chi phí quảng cáo
-   **NetCost**: Chi phí thực tế sau chiết khấu
-   **Orders**: Số lượng đơn hàng LIVE
-   **CostPerOrder**: Chi phí trung bình mỗi đơn hàng
-   **GrossRevenue**: Tổng doanh thu gộp
-   **ROI**: Tỷ suất lợi nhuận

### Metrics hiệu suất LIVE
-   **LiveViews**: Tổng lượt xem LIVE
-   **CostPerLiveView**: Chi phí mỗi lượt xem
-   **TenSecondLiveViews**: Lượt xem ít nhất 10 giây
-   **CostPerTenSecondLiveView**: Chi phí mỗi lượt xem 10 giây
-   **LiveFollows**: Số lượt follow trong LIVE

### Thông tin báo cáo
-   **Currency**: Mã tiền tệ
-   **Date**: Thời điểm tổng hợp báo cáo

## Phương thức đặc biệt

### HasChanged()

```csharp
public bool HasChanged(RawGmvMaxLiveCampaignReportEntity other)
```

So sánh entity hiện tại với entity khác để phát hiện thay đổi. Bao gồm tất cả các trường thông tin campaign, TikTok account, cấu hình tối ưu hóa và metrics hiệu suất.

## Đặc điểm chính của LIVE GMV Max Campaign Report

### Live Stream Performance Tracking
-   **Real-time Metrics**: Theo dõi hiệu suất LIVE real-time
-   **Engagement Metrics**: Đo lường tương tác trong LIVE stream
-   **View Duration**: Phân tích thời gian xem LIVE

### ROI Optimization
-   **Target ROI**: Tối ưu hóa theo mục tiêu ROI
-   **Budget Control**: Kiểm soát ngân sách tự động
-   **Cost Efficiency**: Theo dõi hiệu quả chi phí

### TikTok Account Integration
-   **Identity Management**: Quản lý danh tính TikTok
-   **Profile Tracking**: Theo dõi thông tin profile
-   **Authorization**: Xử lý quyền truy cập account

## Mục đích sử dụng

-   Báo cáo hiệu suất tổng quan chiến dịch LIVE GMV Max
-   Theo dõi ROI và hiệu quả tài chính
-   Phân tích metrics LIVE stream chi tiết
-   Quản lý ngân sách và tối ưu hóa chi phí
-   Tích hợp với TikTok ecosystem cho LIVE commerce
-   Hỗ trợ ra quyết định tối ưu hóa campaign

## Quan hệ với các entity khác

-   **RawGmvMaxCampaignsEntity**: Many-to-One, thuộc về chiến dịch GMV Max
-   **RawGmvMaxLiveDetailLivestreamReportEntity**: One-to-Many, chi tiết từng phòng LIVE

## Use Cases thực tế

### Live Commerce Campaign Analysis
```
Phân tích hiệu suất chiến dịch LIVE bán hàng:
- Theo dõi Cost và NetCost để đánh giá hiệu quả chi phí
- Phân tích ROI để tối ưu hóa lợi nhuận
- Theo dõi LiveViews và TenSecondLiveViews để đánh giá engagement
```

### ROI Optimization
```
Tối ưu hóa ROI cho chiến dịch LIVE:
- So sánh TargetRoiBudget với MaxDeliveryBudget
- Điều chỉnh RoasBid dựa trên ROI thực tế
- Tối ưu hóa CostPerOrder và CostPerLiveView
```