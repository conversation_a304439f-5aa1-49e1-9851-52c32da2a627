/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Information about the video post
    /// </summary>
    public class VideoPostInfo
    {
        /// <summary>
        /// Video caption/description - which can contain #hashtags and @mentions
        /// </summary>
        [JsonPropertyName("caption")]
        public string? Caption { get; set; }

        /// <summary>
        /// Whether to enable the Brand Organic Content toggle for the video
        /// </summary>
        [JsonPropertyName("is_brand_organic")]
        public bool? IsBrandOrganic { get; set; }

        /// <summary>
        /// Whether to enable the Branded Content toggle for the video
        /// </summary>
        [JsonPropertyName("is_branded_content")]
        public bool? IsBrandedContent { get; set; }

        /// <summary>
        /// Valid only when is_branded_content is true - the invite link for a TTO Creator Marketplace campaign
        /// </summary>
        [JsonPropertyName("tto_invite_link")]
        public string? TtoInviteLink { get; set; }

        /// <summary>
        /// Whether to disable the "Allow comments" setting for the published video post
        /// </summary>
        [JsonPropertyName("disable_comment")]
        public bool? DisableComment { get; set; }

        /// <summary>
        /// Whether to disable the "Allow Duet" setting for the published video post
        /// </summary>
        [JsonPropertyName("disable_duet")]
        public bool? DisableDuet { get; set; }

        /// <summary>
        /// Whether to disable the "Allow Stitch" setting for the published video post
        /// </summary>
        [JsonPropertyName("disable_stitch")]
        public bool? DisableStitch { get; set; }

        /// <summary>
        /// Setting to choose a frame of the published video as the cover photo, in the format of a timestamp in ms
        /// </summary>
        [JsonPropertyName("thumbnail_offset")]
        public int? ThumbnailOffset { get; set; }

        /// <summary>
        /// Whether to enable the AI-generated content toggle for the video post
        /// </summary>
        [JsonPropertyName("is_ai_generated")]
        public bool? IsAiGenerated { get; set; }

        /// <summary>
        /// Whether to upload the post as a draft
        /// </summary>
        [JsonPropertyName("upload_to_draft")]
        public bool? UploadToDraft { get; set; }

        /// <summary>
        /// Whether to publish the video as an "Only show in ads" video
        /// </summary>
        [JsonPropertyName("is_ads_only")]
        public bool? IsAdsOnly { get; set; }
    }
}
