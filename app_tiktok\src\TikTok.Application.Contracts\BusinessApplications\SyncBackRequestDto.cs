using System;
using System.Collections.Generic;
using TikTok.Enums;

namespace TikTok.BusinessApplications
{
    /// <summary>
    /// DTO cho request cài đặt đồng bộ lùi
    /// </summary>
    public class SyncBackRequestDto
    {
        /// <summary>
        /// ID của Business Center
        /// </summary>
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Ngày bắt đầu đồng bộ
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// <PERSON><PERSON>y kết thúc đồng bộ
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Danh sách các loại lệnh cần đồng bộ
        /// </summary>
        public List<CommandType> CommandTypes { get; set; } = new List<CommandType>();
    }
}
