using Microsoft.AspNetCore.Diagnostics;

namespace TikTok.Permissions;

public static class TikTokPermissions
{
    public const string GroupName = "TikTok";

    //Add your own permission names. Example:
    //public const string MyPermission1 = GroupName + ".MyPermission1";

    public static class BusinessCenters
    {
        public const string Default = GroupName + ".BusinessCenters";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class BalanceBusinessCenters
    {
        public const string Default = GroupName + ".BalanceBusinessCenters";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class AdAccounts
    {
        public const string Default = GroupName + ".AdAccounts";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
        public const string AssignSupport = Default + ".AssignSupport";
        /// <summary>
        /// For Admin
        /// </summary>
        public const string Supervise = Default + ".Supervise";
    }

    public static class AdAccountSupporters
    {
        public const string Default = GroupName + ".AdAccountSupporters";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class Assets
    {
        public const string Default = GroupName + ".Assets";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class Transactions
    {
        public const string Default = GroupName + ".Transactions";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
        public const string Sync = Default + ".Sync";
    }

    public static class BusinessApplications
    {
        public const string Default = GroupName + ".BusinessApplications";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class BalanceAdAccounts
    {
        public const string Default = GroupName + ".BalanceAdAccounts";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class RecordTransactionAdAccounts
    {
        public const string Default = GroupName + ".RecordTransactionAdAccounts";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class RecordTransactionBcs
    {
        public const string Default = GroupName + ".RecordTransactionBcs";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class JobManagement
    {
        public const string Default = GroupName + ".JobManagement";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class Campaigns
    {
        public const string Default = GroupName + ".Campaigns";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class CostProfiles
    {
        public const string Default = GroupName + ".CostProfiles";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
        public const string Sync = Default + ".Sync";
    }

    public static class ReportIntegratedBcs
    {
        public const string Default = GroupName + ".ReportIntegratedBcs";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class ReportIntegratedCampaigns
    {
        public const string Default = GroupName + ".ReportIntegratedCampaigns";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class ReportIntegratedAdAccounts
    {
        public const string Default = GroupName + ".ReportIntegratedAdAccounts";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class ReportIntegratedAdGroups
    {
        public const string Default = GroupName + ".ReportIntegratedAdGroups";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class ReportIntegratedAds
    {
        public const string Default = GroupName + ".ReportIntegratedAds";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
    }

    public static class FactBalances
    {
        public const string Default = GroupName + ".FactBalances";
    }

    public static class Customers
    {
        public const string Default = GroupName + ".Customers";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
        public const string Import = Default + ".Import";
    }

    public static class SystemCache
    {
        public const string Default = GroupName + ".SystemCache";
        public const string Monitor = Default + ".Monitor";
        public const string Clear = Default + ".Clear";
        public const string ClearAll = Default + ".ClearAll";
    }

    public static class NotificationRules
    {
        public const string Default = GroupName + ".NotificationRules";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
        public const string ViewAll = Default + ".ViewAll";
        public const string UseCommonRule = Default + ".UseCommonRule";
        public const string ManageRule = Default + ".ManageRule";
    }

    public static class SupportManagement
    {
        public const string Default = GroupName + ".SupportManagement";
        public const string Import = Default + ".Import";
    }

    public static class AdAccountPermissionManagement
    {
        public const string Default = GroupName + ".AdAccountPermissionManagement";
    }

    public static class FactGmvMax
    {
        public const string Default = GroupName + ".FactGmvMax";
    }
    public static class FactGmvMaxCampaigns
    {
        public const string Default = FactGmvMax.Default + ".FactGmvMaxCampaigns";
        public const string ViewSpending = Default + ".ViewSpending";      // Xem chi tiêu của tài khoản quảng cáo
        public const string ViewMetrics = Default + ".ViewMetrics";        // Xem chỉ số quảng cáo
        public const string ViewAll = Default + ".ViewAll";                // Xem tất cả thông tin
    }

    public static class FactGmvMaxProducts
    {
        public const string Default = FactGmvMax.Default + ".FactGmvMaxProducts";
        public const string ViewSpending = Default + ".ViewSpending";      // Xem chi tiêu của tài khoản quảng cáo
        public const string ViewMetrics = Default + ".ViewMetrics";        // Xem chỉ số quảng cáo
        public const string ViewAll = Default + ".ViewAll";                // Xem tất cả thông tin
    }
}
