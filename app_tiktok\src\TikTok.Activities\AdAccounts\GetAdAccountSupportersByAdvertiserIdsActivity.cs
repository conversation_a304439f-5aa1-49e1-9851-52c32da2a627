using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using TikTok.AdAccounts;
using TikTok.Entities.AdAccounts;
using Volo.Abp.Domain.Repositories;

namespace TikTok.Activities.AdAccounts
{
    [Activity(Category = "Tài khoản quảng cáo", DisplayName = "Lấy danh sách người hỗ trợ theo danh sách tài khoản quảng cáo", Outcomes = new[] { OutcomeNames.Done })]
    public class GetAdAccountSupportersByAdvertiserIdsActivity : UnitOfWorkActivity
    {
        private readonly IAdAccountSupporterQueryService _adAccountSupporterQueryService;

        public GetAdAccountSupportersByAdvertiserIdsActivity(
            IAdAccountSupporterQueryService adAccountSupporterQueryService)
        {
            _adAccountSupporterQueryService = adAccountSupporterQueryService;
        }

        [ActivityInput(Hint = "AdvertiserIds", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public List<string> AdvertiserIds { get; set; }

        [ActivityInput(Hint = "Business Center ID", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public string BcId { get; set; }

        [ActivityInput(Hint = "Có bao gồm người hỗ trợ không hoạt động không", DefaultValue = false, SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public bool IncludeInactive { get; set; } = false;

        [ActivityOutput(Hint = "Danh sách người hỗ trợ")]
        public List<AdAccountSupporterListDto> Output { get; set; } = new();

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            // Sử dụng service để lấy dữ liệu người hỗ trợ
            if (!string.IsNullOrEmpty(BcId))
            {
                if (AdvertiserIds != null && AdvertiserIds.Any())
                {
                    // Lấy theo cả BcId và AdvertiserIds
                    Output = await _adAccountSupporterQueryService.GetSupportersByBcIdAndAdvertiserIdsAsync(
                        BcId, AdvertiserIds, IncludeInactive);
                }
                else
                {
                    // Lấy theo BcId
                    Output = await _adAccountSupporterQueryService.GetSupportersByBcIdAsync(BcId, IncludeInactive);
                }
            }
            else
            {
                // Lấy theo AdvertiserIds hoặc tất cả
                Output = await _adAccountSupporterQueryService.GetSupportersByAdvertiserIdsAsync(AdvertiserIds, IncludeInactive);
            }

            return Done();
        }
    }
}
