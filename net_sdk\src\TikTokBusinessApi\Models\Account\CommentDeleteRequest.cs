/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for deleting an owned comment on an owned video
    /// </summary>
    public class CommentDeleteRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for owned comment on an owned TikTok video to delete
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string CommentId { get; set; } = string.Empty;
    }
}
