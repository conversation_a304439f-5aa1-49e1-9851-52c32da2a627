# DateTime API Documentation

## Tổng quan

DateTime API cung cấp các endpoint để xử lý thời gian và timezone một cách thông minh, hỗ trợ cả hai dạng timezone:

-   Format UTC: "UTC+07:00", "UTC-05:00", "UTC+10:30"
-   Format Timezone ID: "Asia/Ho_Chi_Minh", "America/New_York", "Europe/London"

## Base URL

```
/api/date-time
```

## Endpoints

### 1. <PERSON><PERSON><PERSON> thời gian hiện tại theo timezone

**GET** `/api/date-time/now`

**Query Parameters:**

-   `timezone` (optional): Timezone (ví dụ: "Asia/Ho_Chi_Minh" hoặc "UTC+07:00")

**Response:**

```json
{
    "value": "2024-01-15T17:30:45.123"
}
```

**Ví dụ:**

```bash
GET /api/date-time/now?timezone=Asia/Ho_Chi_Minh
GET /api/date-time/now?timezone=UTC+07:00
```

### 1.1. <PERSON><PERSON><PERSON> thời gian hiện tại theo timezone với thông tin timezone

**GET** `/api/date-time/now-with-timezone`

**Query Parameters:**

-   `timezone` (optional): Timezone (ví dụ: "Asia/Ho_Chi_Minh" hoặc "UTC+07:00")

**Response:**

```json
{
    "dateTime": "2024-01-15T17:30:45.123",
    "timezone": "Asia/Ho_Chi_Minh",
    "timezoneOffset": "07:00:00"
}
```

**Ví dụ:**

```bash
GET /api/date-time/now-with-timezone?timezone=Asia/Ho_Chi_Minh
GET /api/date-time/now-with-timezone?timezone=UTC+07:00
```

### 2. Lấy ngày hiện tại theo timezone (chỉ ngày tháng năm)

**GET** `/api/date-time/date-now`

**Query Parameters:**

-   `timezone` (optional): Timezone (ví dụ: "Asia/Ho_Chi_Minh" hoặc "UTC+07:00")

**Response:**

```json
{
    "value": "2024-01-15T00:00:00"
}
```

**Ví dụ:**

```bash
GET /api/date-time/date-now?timezone=Asia/Ho_Chi_Minh
```

### 2.1. Lấy ngày hiện tại theo timezone với thông tin timezone

**GET** `/api/date-time/date-now-with-timezone`

**Query Parameters:**

-   `timezone` (optional): Timezone (ví dụ: "Asia/Ho_Chi_Minh" hoặc "UTC+07:00")

**Response:**

```json
{
    "dateTime": "2024-01-15T00:00:00",
    "timezone": "Asia/Ho_Chi_Minh",
    "timezoneOffset": "07:00:00"
}
```

**Ví dụ:**

```bash
GET /api/date-time/date-now-with-timezone?timezone=Asia/Ho_Chi_Minh
```

### 3. Lấy khoảng ngày hiện tại theo timezone

**GET** `/api/date-time/range-now`

**Query Parameters:**

-   `timezone` (optional): Timezone (ví dụ: "Asia/Ho_Chi_Minh" hoặc "UTC+07:00")

**Response:**

```json
{
    "startDateTime": "2024-01-14T23:59:59",
    "endDateTime": "2024-01-15T23:59:59"
}
```

### 3.1. Lấy khoảng ngày hiện tại theo timezone với thông tin timezone

**GET** `/api/date-time/range-now-with-timezone`

**Query Parameters:**

-   `timezone` (optional): Timezone (ví dụ: "Asia/Ho_Chi_Minh" hoặc "UTC+07:00")

**Response:**

```json
{
    "startDateTime": "2024-01-14T23:59:59",
    "endDateTime": "2024-01-15T23:59:59",
    "timezone": "Asia/Ho_Chi_Minh",
    "timezoneOffset": "07:00:00"
}
```

### 4. Lấy thời gian hiện tại UTC

**GET** `/api/date-time/utc-now`

**Response:**

```json
{
    "value": "2024-01-15T10:30:45.123Z"
}
```

### 4.1. Lấy thời gian hiện tại UTC với thông tin timezone UTC

**GET** `/api/date-time/utc-now-with-timezone`

**Response:**

```json
{
    "dateTime": "2024-01-15T10:30:45.123Z",
    "timezone": "UTC",
    "timezoneOffset": "00:00:00"
}
```

### 5. Lấy ngày hiện tại UTC (chỉ ngày tháng năm)

**GET** `/api/date-time/date-utc-now`

**Response:**

```json
{
    "value": "2024-01-15T00:00:00Z"
}
```

### 5.1. Lấy ngày hiện tại UTC với thông tin timezone UTC

**GET** `/api/date-time/date-utc-now-with-timezone`

**Response:**

```json
{
    "dateTime": "2024-01-15T00:00:00Z",
    "timezone": "UTC",
    "timezoneOffset": "00:00:00"
}
```

### 6. Lấy khoảng ngày hiện tại UTC

**GET** `/api/date-time/range-utc-now`

**Response:**

```json
{
    "startDateTime": "2024-01-14T23:59:59Z",
    "endDateTime": "2024-01-15T23:59:59Z"
}
```

### 6.1. Lấy khoảng ngày hiện tại UTC với thông tin timezone UTC

**GET** `/api/date-time/range-utc-now-with-timezone`

**Response:**

```json
{
    "startDateTime": "2024-01-14T23:59:59Z",
    "endDateTime": "2024-01-15T23:59:59Z",
    "timezone": "UTC",
    "timezoneOffset": "00:00:00"
}
```

### 7. Chuyển đổi từ UTC sang timezone

**POST** `/api/date-time/convert-from-utc`

**Request Body:**

```json
{
    "utcDateTime": "2024-01-15T10:30:00Z",
    "timezone": "Asia/Ho_Chi_Minh"
}
```

**Response:**

```json
{
    "value": "2024-01-15T17:30:00"
}
```

### 8. Chuyển đổi từ timezone sang UTC

**POST** `/api/date-time/convert-to-utc`

**Request Body:**

```json
{
    "localDateTime": "2024-01-15T17:30:00",
    "timezone": "Asia/Ho_Chi_Minh"
}
```

**Response:**

```json
{
    "value": "2024-01-15T10:30:00Z"
}
```

### 9. Kiểm tra timezone có hợp lệ không

**GET** `/api/date-time/validate-timezone`

**Query Parameters:**

-   `timezone`: Timezone cần kiểm tra

**Response:**

```json
{
    "value": true
}
```

**Ví dụ:**

```bash
GET /api/date-time/validate-timezone?timezone=Asia/Ho_Chi_Minh
GET /api/date-time/validate-timezone?timezone=UTC+07:00
GET /api/date-time/validate-timezone?timezone=Invalid/Timezone
```

### 10. Lấy offset của timezone so với UTC

**GET** `/api/date-time/timezone-offset`

**Query Parameters:**

-   `timezone`: Timezone cần lấy offset

**Response:**

```json
{
    "value": "07:00:00"
}
```

**Ví dụ:**

```bash
GET /api/date-time/timezone-offset?timezone=Asia/Ho_Chi_Minh
GET /api/date-time/timezone-offset?timezone=UTC-05:00
```

### 11. Lấy khoảng ngày từ ngày được chỉ định đến hiện tại

**POST** `/api/date-time/date-range-from-date`

**Request Body:**

```json
{
    "startDate": "2024-01-01T00:00:00",
    "timezone": "Asia/Ho_Chi_Minh"
}
```

**Response:**

```json
{
    "startDateTime": "2024-01-01T00:00:00",
    "endDateTime": "2024-01-15T23:59:59"
}
```

### 12. Chuyển đổi DateTime sang UTC với thông tin timezone UTC

**POST** `/api/date-time/convert-to-utc-with-timezone`

**Request Body:**

```json
{
    "localDateTime": "2024-01-15T17:30:00",
    "timezone": "Asia/Ho_Chi_Minh"
}
```

**Response:**

```json
{
    "utcDateTime": "2024-01-15T10:30:00Z",
    "utcTimezone": "UTC",
    "utcOffset": "00:00:00"
}
```

## Error Responses

### 400 Bad Request

Khi timezone không hợp lệ:

```json
{
    "error": {
        "message": "Timezone không hợp lệ: Invalid/Timezone"
    }
}
```

## Các timezone phổ biến

### Format UTC

-   `UTC+07:00` - Việt Nam, Thái Lan
-   `UTC+08:00` - Singapore, Malaysia, Philippines
-   `UTC-05:00` - Eastern Time (US & Canada)
-   `UTC-08:00` - Pacific Time (US & Canada)
-   `UTC+00:00` - UTC/GMT
-   `UTC+01:00` - Central European Time
-   `UTC+09:00` - Japan Standard Time

### Format Timezone ID

-   `Asia/Ho_Chi_Minh` - Việt Nam
-   `Asia/Bangkok` - Thái Lan
-   `Asia/Singapore` - Singapore
-   `Asia/Tokyo` - Nhật Bản
-   `America/New_York` - New York
-   `America/Los_Angeles` - Los Angeles
-   `Europe/London` - London
-   `Europe/Paris` - Paris

## Lưu ý

-   Tất cả các endpoint đều hỗ trợ cả hai format timezone
-   Nếu không truyền timezone, hệ thống sẽ sử dụng timezone mặc định của hệ thống (TimeZoneInfo.Local.Id)
-   Nếu timezone không hợp lệ, hệ thống sẽ fallback về timezone của hệ thống
-   Các endpoint trả về ngày (date-now, date-utc-now) chỉ trả về ngày tháng năm, không có giờ phút giây
-   Các endpoint chuyển đổi timezone sẽ validate timezone trước khi thực hiện chuyển đổi
-   Endpoint convert-to-utc-with-timezone trả về thông tin đầy đủ về UTC time và timezone UTC
-   UtcDateTimeDto luôn có utcTimezone = "UTC" và utcOffset = "00:00:00"
-   Các endpoint với suffix "-with-timezone" trả về thông tin đầy đủ về DateTime và timezone tương ứng
-   DateTimeWithTimezoneDto chứa DateTime, timezone và timezoneOffset
-   DateRangeWithTimezoneDto chứa startDateTime, endDateTime, timezone và timezoneOffset
