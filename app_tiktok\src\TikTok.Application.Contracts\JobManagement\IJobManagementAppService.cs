using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.JobManagement;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.JobManagement
{
    /// <summary>
    /// Service interface cho quản lý job
    /// </summary>
    public interface IJobManagementAppService : IApplicationService
    {
        /// <summary>
        /// Lấy cấu hình job hiện tại
        /// </summary>
        /// <returns>Cấu hình job</returns>
        Task<JobConfigurationDto> GetCurrentConfigurationAsync();

        /// <summary>
        /// Cập nhật cấu hình job
        /// </summary>
        /// <param name="input">Thông tin cấu hình</param>
        /// <returns>Cấu hình đã cập nhật</returns>
        Task<JobConfigurationDto> UpdateConfigurationAsync(JobConfigurationDto input);

        /// <summary>
        /// C<PERSON><PERSON> hình lại hệ thống job
        /// </summary>
        /// <param name="id">ID của cấu hình</param>
        /// <returns>Kết quả</returns>
        Task<bool> ReConfigurationAsync(Guid id);

        /// <summary>
        /// Bật/tắt hoạt động đồng bộ
        /// </summary>
        /// <param name="isActive">Trạng thái hoạt động</param>
        /// <returns>Kết quả</returns>
        Task<bool> ToggleSyncActivityAsync(bool isActive);

        /// <summary>
        /// Lấy danh sách worker
        /// </summary>
        /// <returns>Danh sách worker</returns>
        Task<List<WorkerInfoDto>> GetWorkersAsync();

        /// <summary>
        /// Hủy worker
        /// </summary>
        /// <param name="workerId">ID của worker</param>
        /// <returns>Kết quả</returns>
        Task<bool> CancelWorkerAsync(string workerId);

        /// <summary>
        /// Lấy thống kê hệ thống
        /// </summary>
        /// <returns>Thống kê</returns>
        Task<JobSystemStatisticsDto> GetSystemStatisticsAsync();

        /// <summary>
        /// Lấy trạng thái hệ thống
        /// </summary>
        /// <returns>Trạng thái hệ thống</returns>
        Task<JobSystemStatusDto> GetSystemStatusAsync();

        /// <summary>
        /// Lấy danh sách job đang chờ xử lý
        /// </summary>
        /// <returns>Danh sách job đang chờ</returns>
        Task<List<PendingJobDto>> GetPendingJobsAsync();

        /// <summary>
        /// Lấy hoạt động gần đây
        /// </summary>
        /// <returns>Danh sách hoạt động gần đây</returns>
        Task<List<RecentActivityDto>> GetRecentActivityAsync();
    }
}