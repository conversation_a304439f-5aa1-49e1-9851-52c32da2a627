﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace TikTok.DimDates
{
    public class DimDateDto: AuditedEntityDto<int>
    {

        /// <summary>
        /// Ng<PERSON>y đầy đủ
        /// </summary>
        public DateTime FullDate { get; set; }

        /// <summary>
        /// Năm
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// Tháng (1-12)
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// Quý (1-4)
        /// </summary>
        public int Quarter { get; set; }

        /// <summary>
        /// Tuần trong năm (1-53)
        /// </summary>
        public int Week { get; set; }

        /// <summary>
        /// Thứ trong tuần (1=CN, 2=T2,...)
        /// </summary>
        public int DayOfWeek { get; set; }

        /// <summary>
        /// Tên tháng (January, February,...)
        /// </summary>
        public string MonthName { get; set; }

        /// <summary>
        /// Tên tháng tiếng Việt (Tháng 1, Tháng 2,...)
        /// </summary>
        public string MonthNameVi { get; set; }

        /// <summary>
        /// Tên quý (Q1, Q2, Q3, Q4)
        /// </summary>
        public string QuarterName { get; set; }

        /// <summary>
        /// Năm-Tháng (YYYY-MM)
        /// </summary>
        public string YearMonth { get; set; }

        /// <summary>
        /// Năm-Quý (YYYY-Q1)
        /// </summary>
        public string YearQuarter { get; set; }

        /// <summary>
        /// Có phải cuối tuần không
        /// </summary>
        public bool IsWeekend { get; set; }

        /// <summary>
        /// Format DD/MM/YYYY cho display
        /// </summary>
        public string DateFormat_DDMMYYYY { get; set; }
    }
}
