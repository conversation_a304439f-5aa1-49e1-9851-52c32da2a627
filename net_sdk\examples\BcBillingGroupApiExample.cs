/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the BC Billing Group API
    /// </summary>
    public class BcBillingGroupApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public BcBillingGroupApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example of creating a billing group
        /// </summary>
        public async Task CreateBillingGroupExample()
        {
            try
            {
                Console.WriteLine("Creating billing group...");

                var createRequest = new CreateBillingGroupRequest
                {
                    BcId = "your_bc_id_here",
                    BillingGroupName = "My Billing Group",
                    AdvertiserIds = new List<string> { "advertiser_id_1", "advertiser_id_2" },
                    BillingGroupEmails = new List<string> { "<EMAIL>" },
                    IsPrimary = false,
                    BillingGroupType = "AUCTION"
                };

                var response = await _client.BcBillingGroup.CreateBillingGroupAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    createRequest);

                if (response.Data?.Data?.BillingGroupId != null)
                {
                    Console.WriteLine($"Billing group created successfully!");
                    Console.WriteLine($"Billing Group ID: {response.Data.Data.BillingGroupId}");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of updating a billing group
        /// </summary>
        public async Task UpdateBillingGroupExample()
        {
            try
            {
                Console.WriteLine("Updating billing group...");

                var updateRequest = new UpdateBillingGroupRequest
                {
                    BcId = "your_bc_id_here",
                    BillingGroupId = "your_billing_group_id_here",
                    NewBillingGroupName = "Updated Billing Group Name",
                    NewBillingGroupEmails = new List<string> { "<EMAIL>" },
                    AddAdvertiserIds = new List<string> { "new_advertiser_id" },
                    DeleteAdvertiserIds = new List<string> { "old_advertiser_id" },
                    IsPrimary = true
                };

                var response = await _client.BcBillingGroup.UpdateBillingGroupAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    updateRequest);

                Console.WriteLine("Billing group updated successfully!");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting billing groups
        /// </summary>
        public async Task GetBillingGroupsExample()
        {
            try
            {
                Console.WriteLine("Getting billing groups...");

                var getRequest = new GetBillingGroupsRequest
                {
                    BcId = "your_bc_id_here",
                    Filtering = new GetBillingGroupsFiltering
                    {
                        Status = "VALID",
                        BillingGroupType = "AUCTION"
                    },
                    Page = 1,
                    PageSize = 10
                };

                var response = await _client.BcBillingGroup.GetBillingGroupsAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    getRequest);

                if (response.Data?.Data?.List != null)
                {
                    Console.WriteLine($"Found {response.Data.Data.List.Count} billing groups:");
                    
                    foreach (var billingGroup in response.Data.Data.List)
                    {
                        Console.WriteLine($"- ID: {billingGroup.BillingGroupId}");
                        Console.WriteLine($"  Name: {billingGroup.BillingGroupName}");
                        Console.WriteLine($"  Status: {billingGroup.Status}");
                        Console.WriteLine($"  Is Primary: {billingGroup.IsPrimary}");
                        Console.WriteLine($"  Type: {billingGroup.BillingGroupType}");
                        
                        if (billingGroup.Advertisers != null)
                        {
                            Console.WriteLine($"  Advertisers: {billingGroup.Advertisers.Count}");
                        }
                        
                        Console.WriteLine();
                    }

                    if (response.Data.Data.PageInfo != null)
                    {
                        Console.WriteLine($"Page: {response.Data.Data.PageInfo.Page}/{response.Data.Data.PageInfo.TotalPage}");
                        Console.WriteLine($"Total: {response.Data.Data.PageInfo.TotalNumber}");
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting billing group advertisers
        /// </summary>
        public async Task GetBillingGroupAdvertisersExample()
        {
            try
            {
                Console.WriteLine("Getting billing group advertisers...");

                var getRequest = new GetBillingGroupAdvertisersRequest
                {
                    BcId = "your_bc_id_here",
                    BillingGroupId = "your_billing_group_id_here",
                    Page = 1,
                    PageSize = 20
                };

                var response = await _client.BcBillingGroup.GetBillingGroupAdvertisersAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    getRequest);

                if (response.Data?.Data?.List != null)
                {
                    Console.WriteLine($"Found {response.Data.Data.List.Count} advertisers in billing group:");
                    
                    foreach (var advertiser in response.Data.Data.List)
                    {
                        Console.WriteLine($"- Advertiser ID: {advertiser.AdvertiserId}");
                    }

                    if (response.Data.Data.PageInfo != null)
                    {
                        Console.WriteLine($"Page: {response.Data.Data.PageInfo.Page}/{response.Data.Data.PageInfo.TotalPage}");
                        Console.WriteLine($"Total: {response.Data.Data.PageInfo.TotalNumber}");
                    }
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Run all examples
        /// </summary>
        public async Task RunAllExamples()
        {
            await CreateBillingGroupExample();
            Console.WriteLine();
            
            await UpdateBillingGroupExample();
            Console.WriteLine();
            
            await GetBillingGroupsExample();
            Console.WriteLine();
            
            await GetBillingGroupAdvertisersExample();
        }
    }
}
