/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTokBusinessApi.Api;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating GMV Max Campaign report API usage
    /// 
    /// Note: The report API returns dynamic data where dimensions and metrics 
    /// depend on what was requested. Therefore, we use Dictionary<string, object>
    /// to store this data flexibly. Helper methods are provided to safely access
    /// values from these dictionaries.
    /// </summary>
    public class GMVMaxReportApiExample
    {
        private readonly IGMVMaxApi _gmvMaxApi;
        private readonly ILogger<GMVMaxReportApiExample> _logger;

        public GMVMaxReportApiExample(IGMVMaxApi gmvMaxApi, ILogger<GMVMaxReportApiExample> logger)
        {
            _gmvMaxApi = gmvMaxApi ?? throw new ArgumentNullException(nameof(gmvMaxApi));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Example: Get basic GMV Max Campaign report
        /// </summary>
        public async Task GetBasicReportExample()
        {
            try
            {
                var advertiserId = "YOUR_ADVERTISER_ID";
                var storeIds = new List<string> { "YOUR_STORE_ID" };
                var startDate = "2024-01-01";
                var endDate = "2024-01-31";

                // Basic metrics for Product GMV Max Campaign
                var metrics = new List<string>
                {
                    "cost",
                    "orders",
                    "gross_revenue",
                    "roi"
                };

                // Basic dimensions
                var dimensions = new List<string>
                {
                    "advertiser_id",
                    "campaign_id"
                };

                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = advertiserId,
                    StoreIds = storeIds,
                    StartDate = startDate,
                    EndDate = endDate,
                    Metrics = metrics,
                    Dimensions = dimensions
                };

                var response = await _gmvMaxApi.GetReportAsync(request);

                if (response.Data != null)
                {
                    _logger.LogInformation("Report retrieved successfully with {Count} items", response.Data.List.Count);

                    foreach (var item in response.Data.List)
                    {
                        var campaignId = GetDimensionValue(item.Dimensions, "campaign_id");
                        var cost = GetMetricValue(item.Metrics, "cost");
                        var revenue = GetMetricValue(item.Metrics, "gross_revenue");
                        var roi = GetMetricValue(item.Metrics, "roi");

                        _logger.LogInformation("Campaign ID: {CampaignId}, Cost: {Cost}, Revenue: {Revenue}, ROI: {ROI}",
                            campaignId, cost, revenue, roi);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting basic GMV Max report");
            }
        }

        /// <summary>
        /// Example: Get detailed report with filtering and daily breakdown
        /// </summary>
        public async Task GetDetailedReportWithFilteringExample()
        {
            try
            {
                var advertiserId = "YOUR_ADVERTISER_ID";
                var storeIds = new List<string> { "YOUR_STORE_ID" };
                var startDate = "2024-01-01";
                var endDate = "2024-01-07"; // Max 30 days for daily breakdown

                // Comprehensive metrics
                var metrics = new List<string>
                {
                    "cost",
                    "net_cost",
                    "orders",
                    "cost_per_order",
                    "gross_revenue",
                    "roi",
                    "product_impressions",
                    "product_clicks",
                    "product_click_rate"
                };

                // Dimensions with daily breakdown
                var dimensions = new List<string>
                {
                    "advertiser_id",
                    "campaign_id",
                    "stat_time_day"
                };

                // Apply filtering
                var filtering = new GMVMaxReportFiltering
                {
                    GMVMaxPromotionTypes = new List<string> { "PRODUCT" },
                    CampaignStatuses = new List<string> { "STATUS_DELIVERY_OK" }
                };

                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = advertiserId,
                    StoreIds = storeIds,
                    StartDate = startDate,
                    EndDate = endDate,
                    Metrics = metrics,
                    Dimensions = dimensions,
                    EnableTotalMetrics = true,
                    Filtering = filtering,
                    SortField = "cost",
                    SortType = "DESC",
                    Page = 1,
                    PageSize = 100
                };

                var response = await _gmvMaxApi.GetReportAsync(request);

                if (response.Data != null)
                {
                    _logger.LogInformation("Detailed report retrieved successfully with {Count} items", response.Data.List.Count);

                    // Log total metrics if available
                    if (response.Data.TotalMetrics != null)
                    {
                        var totalCost = response.Data.TotalMetrics.ContainsKey("cost") ? response.Data.TotalMetrics["cost"]?.ToString() : "N/A";
                        var totalRevenue = response.Data.TotalMetrics.ContainsKey("gross_revenue") ? response.Data.TotalMetrics["gross_revenue"]?.ToString() : "N/A";
                        var totalRoi = response.Data.TotalMetrics.ContainsKey("roi") ? response.Data.TotalMetrics["roi"]?.ToString() : "N/A";

                        _logger.LogInformation("Total Metrics - Cost: {Cost}, Revenue: {Revenue}, ROI: {ROI}",
                            totalCost, totalRevenue, totalRoi);
                    }

                    // Log individual daily data
                    foreach (var item in response.Data.List)
                    {
                        var date = item.Dimensions?.ContainsKey("stat_time_day") == true ? item.Dimensions["stat_time_day"]?.ToString() : "N/A";
                        var campaignId = item.Dimensions?.ContainsKey("campaign_id") == true ? item.Dimensions["campaign_id"]?.ToString() : "N/A";
                        var cost = item.Metrics?.ContainsKey("cost") == true ? item.Metrics["cost"]?.ToString() : "N/A";
                        var orders = item.Metrics?.ContainsKey("orders") == true ? item.Metrics["orders"]?.ToString() : "N/A";

                        _logger.LogInformation("Date: {Date}, Campaign: {CampaignId}, Cost: {Cost}, Orders: {Orders}",
                            date, campaignId, cost, orders);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting detailed GMV Max report");
            }
        }

        /// <summary>
        /// Example: Get LIVE GMV Max Campaign report
        /// </summary>
        public async Task GetLiveGMVMaxReportExample()
        {
            try
            {
                var advertiserId = "YOUR_ADVERTISER_ID";
                var storeIds = new List<string> { "YOUR_STORE_ID" };
                var startDate = "2024-01-01";
                var endDate = "2024-01-31";

                // LIVE-specific metrics
                var metrics = new List<string>
                {
                    "cost",
                    "net_cost",
                    "orders",
                    "cost_per_order",
                    "gross_revenue",
                    "roi",
                    "live_views",
                    "cost_per_live_view",
                    "10_second_live_views",
                    "cost_per_10_second_live_view",
                    "live_follows"
                };

                // Dimensions for LIVE campaigns
                var dimensions = new List<string>
                {
                    "advertiser_id",
                    "campaign_id",
                    "room_id"
                };

                // Filter for LIVE campaigns
                var filtering = new GMVMaxReportFiltering
                {
                    GMVMaxPromotionTypes = new List<string> { "LIVE" },
                    CampaignStatuses = new List<string> { "STATUS_DELIVERY_OK" }
                };

                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = advertiserId,
                    StoreIds = storeIds,
                    StartDate = startDate,
                    EndDate = endDate,
                    Metrics = metrics,
                    Dimensions = dimensions,
                    EnableTotalMetrics = true,
                    Filtering = filtering,
                    SortField = "live_views",
                    SortType = "DESC"
                };

                var response = await _gmvMaxApi.GetReportAsync(request);

                if (response.Data != null)
                {
                    _logger.LogInformation("LIVE GMV Max report retrieved successfully with {Count} items", response.Data.List.Count);

                    foreach (var item in response.Data.List)
                    {
                        var roomId = item.Dimensions?.ContainsKey("room_id") == true ? item.Dimensions["room_id"]?.ToString() : "N/A";
                        var liveViews = item.Metrics?.ContainsKey("live_views") == true ? item.Metrics["live_views"]?.ToString() : "N/A";
                        var revenue = item.Metrics?.ContainsKey("gross_revenue") == true ? item.Metrics["gross_revenue"]?.ToString() : "N/A";
                        var follows = item.Metrics?.ContainsKey("live_follows") == true ? item.Metrics["live_follows"]?.ToString() : "N/A";

                        _logger.LogInformation("Room ID: {RoomId}, Live Views: {LiveViews}, Revenue: {Revenue}, Follows: {Follows}",
                            roomId, liveViews, revenue, follows);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting LIVE GMV Max report");
            }
        }

        /// <summary>
        /// Example: Get product-level report
        /// </summary>
        public async Task GetProductLevelReportExample()
        {
            try
            {
                var advertiserId = "YOUR_ADVERTISER_ID";
                var storeIds = new List<string> { "YOUR_STORE_ID" };
                var startDate = "2024-01-01";
                var endDate = "2024-01-31";

                // Product-focused metrics
                var metrics = new List<string>
                {
                    "cost",
                    "orders",
                    "gross_revenue",
                    "roi",
                    "product_impressions",
                    "product_clicks",
                    "product_click_rate"
                };

                // Product-level dimensions
                var dimensions = new List<string>
                {
                    "advertiser_id",
                    "campaign_id",
                    "item_group_id",
                    "item_id"
                };

                // Filter for specific product SPU IDs
                var filtering = new GMVMaxReportFiltering
                {
                    GMVMaxPromotionTypes = new List<string> { "PRODUCT" },
                    ItemGroupIds = new List<string> { "YOUR_SPU_ID_1", "YOUR_SPU_ID_2" }
                };

                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = advertiserId,
                    StoreIds = storeIds,
                    StartDate = startDate,
                    EndDate = endDate,
                    Metrics = metrics,
                    Dimensions = dimensions,
                    EnableTotalMetrics = false,
                    Filtering = filtering,
                    SortField = "gross_revenue",
                    SortType = "DESC"
                };

                var response = await _gmvMaxApi.GetReportAsync(request);

                if (response.Data != null)
                {
                    _logger.LogInformation("Product-level report retrieved successfully with {Count} items", response.Data.List.Count);

                    foreach (var item in response.Data.List)
                    {
                        var spuId = item.Dimensions?.ContainsKey("item_group_id") == true ? item.Dimensions["item_group_id"]?.ToString() : "N/A";
                        var itemId = item.Dimensions?.ContainsKey("item_id") == true ? item.Dimensions["item_id"]?.ToString() : "N/A";
                        var revenue = item.Metrics?.ContainsKey("gross_revenue") == true ? item.Metrics["gross_revenue"]?.ToString() : "N/A";
                        var orders = item.Metrics?.ContainsKey("orders") == true ? item.Metrics["orders"]?.ToString() : "N/A";

                        _logger.LogInformation("SPU ID: {SpuId}, Item ID: {ItemId}, Revenue: {Revenue}, Orders: {Orders}",
                            spuId, itemId, revenue, orders);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product-level GMV Max report");
            }
        }

        /// <summary>
        /// Example: Get hourly breakdown report (max 1 day range)
        /// </summary>
        public async Task GetHourlyBreakdownReportExample()
        {
            try
            {
                var advertiserId = "YOUR_ADVERTISER_ID";
                var storeIds = new List<string> { "YOUR_STORE_ID" };
                var startDate = "2024-01-15";
                var endDate = "2024-01-15"; // Same day for hourly breakdown

                // Basic metrics
                var metrics = new List<string>
                {
                    "cost",
                    "orders",
                    "gross_revenue",
                    "product_impressions",
                    "product_clicks"
                };

                // Hourly breakdown dimensions
                var dimensions = new List<string>
                {
                    "advertiser_id",
                    "campaign_id",
                    "stat_time_hour"
                };

                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = advertiserId,
                    StoreIds = storeIds,
                    StartDate = startDate,
                    EndDate = endDate,
                    Metrics = metrics,
                    Dimensions = dimensions,
                    EnableTotalMetrics = true,
                    SortField = "stat_time_hour",
                    SortType = "ASC"
                };

                var response = await _gmvMaxApi.GetReportAsync(request);

                if (response.Data != null)
                {
                    _logger.LogInformation("Hourly breakdown report retrieved successfully with {Count} items", response.Data.List.Count);

                    foreach (var item in response.Data.List)
                    {
                        var hour = item.Dimensions?.ContainsKey("stat_time_hour") == true ? item.Dimensions["stat_time_hour"]?.ToString() : "N/A";
                        var cost = item.Metrics?.ContainsKey("cost") == true ? item.Metrics["cost"]?.ToString() : "N/A";
                        var orders = item.Metrics?.ContainsKey("orders") == true ? item.Metrics["orders"]?.ToString() : "N/A";
                        var impressions = item.Metrics?.ContainsKey("product_impressions") == true ? item.Metrics["product_impressions"]?.ToString() : "N/A";

                        _logger.LogInformation("Hour: {Hour}, Cost: {Cost}, Orders: {Orders}, Impressions: {Impressions}",
                            hour, cost, orders, impressions);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting hourly breakdown GMV Max report");
            }
        }

        /// <summary>
        /// Example: Demonstrate working with Dictionary data using helper methods
        /// </summary>
        public async Task GetSimpleReportWithHelpersExample()
        {
            try
            {
                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = "YOUR_ADVERTISER_ID",
                    StoreIds = new List<string> { "YOUR_STORE_ID" },
                    StartDate = "2024-01-01",
                    EndDate = "2024-01-31",
                    Metrics = new List<string> { "cost", "orders", "gross_revenue" },
                    Dimensions = new List<string> { "advertiser_id", "campaign_id" },
                    EnableTotalMetrics = true
                };

                var response = await _gmvMaxApi.GetReportAsync(request);

                if (response.Data != null)
                {
                    _logger.LogInformation("Report retrieved with {Count} items", response.Data.List.Count);

                    // Example of working with total metrics
                    if (response.Data.TotalMetrics != null)
                    {
                        var totalCost = GetMetricNumericValue(response.Data.TotalMetrics, "cost");
                        var totalRevenue = GetMetricNumericValue(response.Data.TotalMetrics, "gross_revenue");
                        var totalOrders = GetMetricNumericValue(response.Data.TotalMetrics, "orders");

                        _logger.LogInformation("Totals - Cost: ${Cost:F2}, Revenue: ${Revenue:F2}, Orders: {Orders}",
                            totalCost, totalRevenue, totalOrders);
                    }

                    // Example of iterating through items with helper methods
                    foreach (var item in response.Data.List.Take(5)) // Show first 5 items only
                    {
                        var campaignId = GetDimensionValue(item.Dimensions, "campaign_id");
                        var cost = GetMetricNumericValue(item.Metrics, "cost");
                        var revenue = GetMetricNumericValue(item.Metrics, "gross_revenue");
                        var orders = GetMetricNumericValue(item.Metrics, "orders");

                        // Calculate ROI if both cost and revenue are available
                        var roi = cost > 0 ? (revenue / cost - 1) * 100 : 0;

                        _logger.LogInformation("Campaign {CampaignId}: Cost=${Cost:F2}, Revenue=${Revenue:F2}, Orders={Orders}, ROI={ROI:F1}%",
                            campaignId, cost, revenue, orders, roi);
                    }

                    // Example of checking for specific metrics availability
                    var firstItem = response.Data.List.FirstOrDefault();
                    if (firstItem?.Metrics != null)
                    {
                        var availableMetrics = string.Join(", ", firstItem.Metrics.Keys);
                        _logger.LogInformation("Available metrics in response: {Metrics}", availableMetrics);
                    }

                    if (firstItem?.Dimensions != null)
                    {
                        var availableDimensions = string.Join(", ", firstItem.Dimensions.Keys);
                        _logger.LogInformation("Available dimensions in response: {Dimensions}", availableDimensions);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting simple GMV Max report with helpers");
            }
        }

        /// <summary>
        /// Helper method to safely get string value from dimensions dictionary
        /// </summary>
        /// <param name="dimensions">Dimensions dictionary</param>
        /// <param name="key">Key to look for</param>
        /// <param name="defaultValue">Default value if key not found</param>
        /// <returns>String value or default</returns>
        private string GetDimensionValue(Dictionary<string, object>? dimensions, string key, string defaultValue = "N/A")
        {
            return dimensions?.ContainsKey(key) == true ? dimensions[key]?.ToString() ?? defaultValue : defaultValue;
        }

        /// <summary>
        /// Helper method to safely get string value from metrics dictionary
        /// </summary>
        /// <param name="metrics">Metrics dictionary</param>
        /// <param name="key">Key to look for</param>
        /// <param name="defaultValue">Default value if key not found</param>
        /// <returns>String value or default</returns>
        private string GetMetricValue(Dictionary<string, object>? metrics, string key, string defaultValue = "N/A")
        {
            return metrics?.ContainsKey(key) == true ? metrics[key]?.ToString() ?? defaultValue : defaultValue;
        }

        /// <summary>
        /// Helper method to safely get numeric value from metrics dictionary
        /// </summary>
        /// <param name="metrics">Metrics dictionary</param>
        /// <param name="key">Key to look for</param>
        /// <param name="defaultValue">Default value if key not found</param>
        /// <returns>Decimal value or default</returns>
        private decimal GetMetricNumericValue(Dictionary<string, object>? metrics, string key, decimal defaultValue = 0)
        {
            if (metrics?.ContainsKey(key) == true && metrics[key] != null)
            {
                var value = metrics[key].ToString();
                if (decimal.TryParse(value, out decimal result))
                {
                    return result;
                }
            }
            return defaultValue;
        }
    }
}