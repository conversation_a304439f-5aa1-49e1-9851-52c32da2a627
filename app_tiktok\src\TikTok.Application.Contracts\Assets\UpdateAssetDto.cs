using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;

namespace TikTok.Assets
{
    /// <summary>
    /// DTO để cập nhật tài sản
    /// </summary>
    public class UpdateAssetDto
    {
        /// <summary>
        /// ID duy nhất của tài sản
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AssetId { get; set; }

        /// <summary>
        /// Tên của tài sản
        /// </summary>
        [Required]
        [StringLength(255)]
        public string AssetName { get; set; }

        /// <summary>
        /// Loại tài sản
        /// </summary>
        [Required]
        public AssetType AssetType { get; set; }

        /// <summary>
        /// ID của Business Center sở hữu tài sản
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// Loại tài khoản quảng cáo
        /// </summary>
        public AdAccountType? AdvertiserAccountType { get; set; }

        /// <summary>
        /// Quyền của người dùng Business Center đối với tài khoản quảng cáo
        /// </summary>
        public AdvertiserRole? AdvertiserRole { get; set; }

        /// <summary>
        /// Quyền của người dùng Business Center đối với danh mục
        /// </summary>
        public CatalogRole? CatalogRole { get; set; }

        /// <summary>
        /// Cho biết liệu danh mục có thể được sử dụng trong quảng cáo
        /// </summary>
        public AdCreationEligible? AdCreationEligible { get; set; }

        /// <summary>
        /// Quyền của người dùng Business Center đối với TikTok Shop
        /// </summary>
        public StoreRole? StoreRole { get; set; }

        /// <summary>
        /// Quyền của người dùng Business Center đối với tài khoản TikTok
        /// </summary>
        public List<TtAccountRole>? TtAccountRoles { get; set; }

        /// <summary>
        /// Tên của Business Center sở hữu tài sản này
        /// </summary>
        [StringLength(255)]
        public string? OwnerBcName { get; set; }

        /// <summary>
        /// Cờ đánh dấu đã bị xóa khỏi Business Center
        /// </summary>
        public bool IsRemoved { get; set; }

        /// <summary>
        /// Thời gian xóa khỏi Business Center (UTC)
        /// </summary>
        public DateTime? RemovedAt { get; set; }

        /// <summary>
        /// Loại quan hệ giữa Business Center và tài sản
        /// </summary>
        public RelationType? RelationType { get; set; }

        /// <summary>
        /// Trạng thái quan hệ giữa tài khoản Business Center và tài sản
        /// Trường này sẽ là null khi asset_type là CATALOG, LEAD, hoặc TT_ACCOUNT
        /// </summary>
        public RelationStatus? RelationStatus { get; set; }

        /// <summary>
        /// Trạng thái tài khoản quảng cáo (sử dụng enum AdAccountStatus)
        /// </summary>
        public AdAccountStatus? AdvertiserStatus { get; set; }
    }
}