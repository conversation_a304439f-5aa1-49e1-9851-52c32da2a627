## Bảng mô tả trường dữ liệu

| Trường | Loại | Mô tả | Chi tiết |
| **advertiser_name** | string | Tên tài khoản nhà quảng cáo | Hỗ trợ ở cấp <PERSON> quả<PERSON> cáo, <PERSON><PERSON><PERSON> dịch, Nhóm quảng cáo và Quảng cáo. |
| **advertiser_id** | string | ID tài khoản nhà quảng cáo | Hỗ trợ ở cấp <PERSON>h<PERSON> quảng cáo, Chiến dịch, Nhóm quảng cáo và Quảng cáo. |
| **campaign_name** | string | Tên chiến dịch | Hỗ trợ ở cấp <PERSON>ến dịch, Nhóm quảng cáo và Quảng cáo. |
| **campaign_id** | string | ID chiến dịch | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. |
| **objective_type** | string | <PERSON>ục tiêu quảng cáo | Hỗ trợ ở cấp <PERSON> dị<PERSON>, Nhóm quảng cáo và Quảng cáo. |
| **split_test** | string | Trạng thái thử nghiệm A/B | Hỗ trợ ở cấp Chiến dịch, Nhóm quảng cáo và Quảng cáo. |
| **campaign_budget** | string | Ngân sách chiến dịch | Hỗ trợ ở cấp Chiến dịch, Nhóm quảng cáo và Quảng cáo. |
| **campaign_dedicate_type** | string | Loại chiến dịch | Chiến dịch chuyên dụng iOS14 hoặc chiến dịch thông thường. Hỗ trợ ở cấp Chiến dịch, Nhóm quảng cáo và Quảng cáo. |
| **app_promotion_type** | string | Loại quảng cáo ứng dụng | Hỗ trợ ở cấp Chiến dịch, Nhóm quảng cáo và Quảng cáo. Các giá trị Enum: `APP_INSTALL`, `APP_RETARGETING`. `APP_INSTALL` và `APP_RETARGETING` sẽ được trả về khi `objective_type` là `APP_PROMOTION`. Ngược lại, `UNSET` sẽ được trả về. |
| **adgroup_name** | string | Tên nhóm quảng cáo | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. |
| **adgroup_id** | string | ID nhóm quảng cáo | Hỗ trợ ở cấp Quảng cáo. |
| **placement_type** | string | Loại vị trí đặt | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. |
| **promotion_type** | string | Loại quảng cáo | Có thể là ứng dụng, trang web, hoặc khác. Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo trong cả báo cáo đồng bộ và không đồng bộ. |
| **opt_status** | string | Tối ưu hóa quảng cáo tự động | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. |
| **adgroup_download_url** | string | URL tải xuống/URL trang web | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. |
| **profile_image** | string | Hình ảnh hồ sơ | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. |
| **dpa_target_audience_type** | string | Loại đối tượng mục tiêu cho DPA | Đối tượng mà các sản phẩm DPA nhắm đến. Hỗ trợ ở cấp Nhóm quảng cáo hoặc Quảng cáo trong cả báo cáo đồng bộ và không đồng bộ. |
| **budget** | string | Ngân sách nhóm quảng cáo | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. |
| **smart_target** | string | Mục tiêu tối ưu hóa | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. |
| **pricing_category** | string | Sự kiện thanh toán **(Sẽ bị loại bỏ)** | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. Nếu bạn muốn truy xuất sự kiện thanh toán của quảng cáo, hãy sử dụng chỉ số mới `billing_event`. |
| **billing_event** | string | Sự kiện thanh toán | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. Ví dụ: `"Clicks"`, `"Impression"`. |
| **bid_strategy** | string | Chiến lược giá thầu | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. |
| **bid** | string | Giá thầu | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. |
| **bid_secondary_goal** | string | Giá thầu cho mục tiêu phụ | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. |
| **aeo_type** | string | Loại tối ưu hóa sự kiện ứng dụng | Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. (Đã hỗ trợ ở cấp Nhóm quảng cáo và sẽ được hỗ trợ ở cấp Quảng cáo) |
| **ad_name** | string | Tên quảng cáo | Hỗ trợ ở cấp Quảng cáo. |
| **ad_id** | string | ID quảng cáo | Hỗ trợ ở cấp Quảng cáo. |
| **ad_text** | string | Tiêu đề quảng cáo | Hỗ trợ ở cấp Quảng cáo. |
| **call_to_action** | string | Lời kêu gọi hành động | Hỗ trợ ở cấp Quảng cáo. |
| **ad_profile_image** | string | Hình ảnh hồ sơ (cấp Quảng cáo) | Hỗ trợ ở cấp Quảng cáo. |
| **ad_url** | string | URL (cấp Quảng cáo) | Hỗ trợ ở cấp Quảng cáo. |
| **tt_app_id** | string | ID ứng dụng TikTok | ID ứng dụng TikTok mà bạn đã sử dụng khi tạo Nhóm quảng cáo. Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. Được trả về nếu loại quảng cáo của một Nhóm quảng cáo là Ứng dụng. |
| **tt_app_name** | string | Tên ứng dụng TikTok | Tên ứng dụng TikTok của bạn. Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. Được trả về nếu loại quảng cáo của một Nhóm quảng cáo là Ứng dụng. |
| **mobile_app_id** | string | ID ứng dụng di động | ID ứng dụng di động. Ví dụ: App Store: https://apps.apple.com/us/app/angry-birds/id**343200656**; Google Play: https://play.google.com/store/apps/details?id=**com.rovio.angrybirds**. Hỗ trợ ở cấp Nhóm quảng cáo và Quảng cáo. Được trả về nếu loại quảng cáo của một Nhóm quảng cáo là Ứng dụng. |
| **image_mode** | string | Định dạng | Hỗ trợ ở cấp Quảng cáo. |
| **currency** | string | Tiền tệ | Mã tiền tệ, ví dụ: USD. Lưu ý rằng nếu bạn muốn sử dụng `currency` làm chỉ số, thì trường `dimensions` trong yêu cầu của bạn phải bao gồm `adgroup_id`/`ad_id`/`campaign_id`/`advertiser_id`. |
| **is_aco\*\* | boolean | Quảng cáo tự động/Smart Creative | `True` nếu là quảng cáo tự động hoặc quảng cáo Smart Creative. | Hỗ trợ ở cấp `AUCTION_ADGROUP`. |
| \*\*is_smart_creative\*\* | boolean | Quảng cáo Smart Creative | `True` nếu là quảng cáo Smart Creative. | Hỗ trợ ở cấp `AUCTION_AD`. |
