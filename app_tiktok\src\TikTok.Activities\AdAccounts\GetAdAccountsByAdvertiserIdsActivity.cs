using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using TikTok.AdAccounts;
using TikTok.Repositories;
using Volo.Abp.ObjectMapping;

namespace TikTok.Activities.AdAccounts
{
    [Activity(Category = "Tài khoản quảng cáo", DisplayName = "Lấy thông tin tài khoản quảng cáo theo danh sách AdvertiserIds", Outcomes = new[] { OutcomeNames.Done })]
    public class GetAdAccountsByAdvertiserIdsActivity : UnitOfWorkActivity
    {
        private readonly IAdAccountRepository _adAccountRepository;
        private readonly IObjectMapper _objectMapper;

        public GetAdAccountsByAdvertiserIdsActivity(
            IAdAccountRepository adAccountRepository,
            IObjectMapper objectMapper)
        {
            _adAccountRepository = adAccountRepository;
            _objectMapper = objectMapper;
        }

        [ActivityInput(Hint = "AdvertiserIds", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public List<string> AdvertiserIds { get; set; }

        [ActivityOutput(Hint = "Output")]
        public List<AdAccountDto> Output { get; set; } = new();

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            if (AdvertiserIds == null || !AdvertiserIds.Any())
            {
                Output = new List<AdAccountDto>();
                return Done();
            }

            var entities = await _adAccountRepository.GetListByAdvertiserIdsAsync(AdvertiserIds);
            Output = _objectMapper.Map<List<TikTok.Entities.RawAdAccountEntity>, List<AdAccountDto>>(entities);

            return Done();
        }
    }
}