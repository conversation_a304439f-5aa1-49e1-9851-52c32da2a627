/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Ad Group operations
    /// </summary>
    public interface IAdGroupApi
    {
        /// <summary>
        /// Get ad groups with optional filtering and pagination
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="fields">Fields to return (optional)</param>
        /// <param name="filtering">Filtering criteria (optional)</param>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10, max: 1000)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad group get response containing list of ad groups and pagination info</returns>
        Task<AdGroupGetResponse> GetAdGroupsAsync(
            string advertiserId,
            List<string>? fields = null,
            AdGroupFiltering? filtering = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get ad group quota information for an advertiser
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad group quota response containing total and used quota</returns>
        Task<AdGroupQuotaResponse> GetAdGroupQuotaAsync(
            string advertiserId,
            CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Estimate audience size for a targeting configuration
        // /// </summary>
        // /// <param name="request">Audience size estimation request</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Audience size estimation response</returns>
        // Task<AudienceSizeEstimateResponse> EstimateAudienceSizeAsync(
        //     AudienceSizeEstimateRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Create new ad groups
        /// </summary>
        /// <param name="request">Ad group creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad group create response containing created ad group IDs and details</returns>
        //Task<AdGroupCreateResponse> CreateAdGroupsAsync(
        //    AdGroupCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update existing ad groups
        /// </summary>
        /// <param name="request">Ad group update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad group update response containing updated ad group IDs and details</returns>
        //Task<AdGroupUpdateResponse> UpdateAdGroupsAsync(
        //    AdGroupUpdateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update ad group status (enable, disable, or delete)
        /// </summary>
        /// <param name="request">Ad group status update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad group status update response containing updated ad group IDs and status</returns>
        //Task<AdGroupStatusUpdateResponse> UpdateAdGroupStatusAsync(
        //    AdGroupStatusUpdateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update ad group budgets
        /// </summary>
        /// <param name="request">Ad group budget update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad group budget update response containing updated ad group IDs and budget details</returns>
        //Task<AdGroupBudgetUpdateResponse> UpdateAdGroupBudgetAsync(
        //    AdGroupBudgetUpdateRequest request,
        //    CancellationToken cancellationToken = default);
    }
}
