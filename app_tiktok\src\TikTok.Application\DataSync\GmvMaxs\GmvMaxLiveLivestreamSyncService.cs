using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Repositories;
using TikTok.TikTokApiClients;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu báo cáo GMV Max Live Detail Livestream
    /// </summary>
    public class GmvMaxLiveLivestreamSyncService : BaseSyncService, IGmvMaxLiveLivestreamSyncService
    {
        private readonly IRawGmvMaxLiveDetailLivestreamReportRepository _gmvMaxLiveDetailLivestreamReportRepository;
        private readonly IAdAccountRepository _adAccountRepository;
        private readonly IRawGmvMaxCampaignsRepository _gmvMaxCampaignsRepository;
        private readonly ITikTokApiClientService _tikTokApiClientService;

        public GmvMaxLiveLivestreamSyncService(
            IServiceProvider serviceProvider,
            IRawGmvMaxLiveDetailLivestreamReportRepository gmvMaxLiveDetailLivestreamReportRepository,
            ILogger<GmvMaxLiveLivestreamSyncService> logger,
            IAdAccountRepository adAccountRepository,
            IRawGmvMaxCampaignsRepository gmvMaxCampaignsRepository,
            ITikTokApiClientService tikTokApiClientService) : base(serviceProvider, logger)
        {
            _gmvMaxLiveDetailLivestreamReportRepository = gmvMaxLiveDetailLivestreamReportRepository;
            _adAccountRepository = adAccountRepository;
            _gmvMaxCampaignsRepository = gmvMaxCampaignsRepository;
            _tikTokApiClientService = tikTokApiClientService;
        }

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Detail Livestream cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxLiveLivestreamSyncResult> SyncAllGmvMaxLiveLivestreamForAllBcsAsync()
        {
            var result = new GmvMaxLiveLivestreamSyncResult
            {
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo GMV Max Live Detail Livestream cho tất cả BC");

                var allBcs = await _businessApplicationCache.GetAllActiveAsync();
                var bcIds = allBcs.Select(x => x.BcId).Distinct().ToList();

                var totalResult = new GmvMaxLiveLivestreamSyncResult
                {
                };

                foreach (var bcId in bcIds)
                {
                    var bcResult = await SyncGmvMaxLiveLivestreamAsync(bcId);
                    totalResult.TotalSynced += bcResult.TotalSynced;
                    totalResult.NewRecords += bcResult.NewRecords;
                    totalResult.UpdatedRecords += bcResult.UpdatedRecords;
                    totalResult.DayCount += bcResult.DayCount;
                    totalResult.CampaignCount += bcResult.CampaignCount;
                    totalResult.LivestreamCount += bcResult.LivestreamCount;
                    totalResult.StoreCount += bcResult.StoreCount;
                    totalResult.BcCount++;
                }

                result = totalResult;

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo GMV Max Live Detail Livestream cho tất cả BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}, Campaign: {CampaignCount}, Livestream: {LivestreamCount}, Store: {StoreCount}, Ngày: {DayCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount, result.CampaignCount, result.LivestreamCount, result.StoreCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo GMV Max Live Detail Livestream cho tất cả BC");
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo GMV Max Live Detail Livestream: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo GMV Max Live Detail Livestream cho tất cả BC");
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Detail Livestream theo BC ID và khoảng thời gian
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate">Ngày bắt đầu (tùy chọn)</param>
        /// <param name="endDate">Ngày kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxLiveLivestreamSyncResult> SyncGmvMaxLiveLivestreamAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var result = new GmvMaxLiveLivestreamSyncResult
            {
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo GMV Max Live Detail Livestream cho BC: {BcId}", bcId);

                // Lấy danh sách AdvertiserIds từ bcId
                var advertiserIds = await _adAccountRepository.GetByBcIdAndStatusAsync(bcId, Enums.AdAccountStatus.STATUS_ENABLE);
                if (!advertiserIds.Any())
                {
                    _logger.LogWarning("Không tìm thấy Advertiser nào cho Business Center: {BcId}", bcId);
                    result.ErrorMessage = $"Không tìm thấy Advertiser nào cho Business Center: {bcId}";
                    return result;
                }

                // Lấy danh sách Campaigns từ bcId
                var camps = new List<RawGmvMaxCampaignsEntity>();
                foreach (var ad in advertiserIds)
                {
                    var campaigns = await _gmvMaxCampaignsRepository.GetByAdvertiserIdAndOperationStatusAsync(ad, "ENABLE");
                    camps.AddRange(campaigns);
                }
                if (!camps.Any())
                {
                    _logger.LogWarning("Không tìm thấy Campaign nào cho Business Center: {BcId}", bcId);
                    result.ErrorMessage = $"Không tìm thấy Campaign nào cho Business Center: {bcId}";
                    return result;
                }

                result.CampaignCount = camps.Count;
                result.StoreCount = camps.Select(x => x.StoreId).Distinct().Count();
                string bcTimezone = DateTimeService.UTC_TIMEZONE;

                var bc = await _businessCenterCache.GetByBcIdAsync(bcId);
                if (bc != null && !string.IsNullOrEmpty(bc.Timezone))
                {
                    bcTimezone = bc.Timezone;
                }
                // Xác định khoảng thời gian đồng bộ
                (DateTime StartDate, DateTime EndDate) dateRange;
                if (startDate.HasValue && endDate.HasValue)
                {
                    if(startDate.Value > endDate.Value)
                    {
                        throw new UserFriendlyException("Ngày bắt đầu phải nhỏ hơn ngày kết thúc");
                    }
                    dateRange = (startDate.Value, endDate.Value);
                }
                else
                {
                    dateRange = await GetDateRangeForSyncAsync(bcId, bcTimezone);
                }
                result.DayCount = (int)(dateRange.EndDate - dateRange.StartDate).TotalDays + 1;

                _logger.LogDebug("Đồng bộ báo cáo GMV Max Live Detail Livestream cho BC: {BcId}, Advertisers: {AdvertiserCount}, Campaigns: {CampaignCount}, Store: {StoreCount}, Ngày: {DayCount}",
                    bcId, advertiserIds.Count, result.CampaignCount, result.StoreCount, result.DayCount);

                // Tạo TikTok client từ Singleton
                using var tikTokClient = await _tikTokApiClientService.GetOrCreateClientAsync(bcId);

                // Duyệt từng nhà quảng cáo
                foreach (var advertiserId in advertiserIds)
                {
                    (DateTime from, DateTime to) rangeDateFiltering;
                    if (startDate.HasValue && endDate.HasValue)
                    {
                        if(startDate.Value > endDate.Value)
                        {
                            throw new UserFriendlyException("Ngày bắt đầu phải nhỏ hơn ngày kết thúc");
                        }
                        rangeDateFiltering = (startDate.Value, endDate.Value);
                    }
                    else
                    {
                        rangeDateFiltering = await GetRangeDateFiltering(bcId, advertiserId, bcTimezone);
                    }

                    var currentDate = rangeDateFiltering.from.Date;
                    var end = rangeDateFiltering.to.Date;

                    while (currentDate <= end)
                    {
                        var pageEnd = currentDate.AddDays(30);
                        var daysDifference = (end - currentDate).TotalDays;
                        if (daysDifference < 30)
                        {
                            pageEnd = end;
                        }
                        _logger.LogDebug("Đồng bộ dữ liệu báo cáo chi tiết cấp livestream GMV Max Live Campaign cho Advertiser: {AdvertiserId}, Ngày: {Date}", advertiserId, currentDate.ToString("yyyy-MM-dd"));

                        var apiResponse = await GetSyncGmvMaxLiveLivestreamFromApiAsync(tikTokClient, bcId, advertiserId, currentDate, pageEnd);
                        if (apiResponse != null && apiResponse.Any())
                        {
                            await ProcessReportDataAsync(bcId, advertiserId, bcTimezone, apiResponse, result);
                        }
                        else
                        {
                            _logger.LogDebug("Không có dữ liệu báo cáo chi tiết cấp livestream GMV Max Live Campaign cho Advertiser: {AdvertiserId}, Ngày: {Date}", advertiserId, currentDate.ToString("yyyy-MM-dd"));
                        }
                        if (pageEnd == end)
                        {
                            break;
                        }
                        currentDate = currentDate.AddDays(29);
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo GMV Max Live Detail Livestream cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi Business khi đồng bộ báo cáo GMV Max Live Detail Livestream cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo GMV Max Live Detail Livestream: {ex.Message}";
                _logger.LogError(ex, "Lỗi exception khi đồng bộ báo cáo GMV Max Live Detail Livestream cho BC: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Lấy khoảng thời gian để đồng bộ dữ liệu
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Khoảng thời gian đồng bộ</returns>
        private async Task<(DateTime StartDate, DateTime EndDate)> GetDateRangeForSyncAsync(string bcId, string timezone)
        {
            // TODO: Implement logic xác định khoảng thời gian đồng bộ
            // Có thể dựa trên:
            // 1. Dữ liệu đã có trong database
            // 2. Cấu hình đồng bộ của BC
            // 3. Thời gian mặc định (ví dụ: 7 ngày gần nhất)
            DateTime currentDateInTimezone = _dateTimeService.GetDateNow(timezone);
            DateTime endDate = currentDateInTimezone;

            var startDate = currentDateInTimezone; // Mặc định đồng bộ 7 ngày gần nhất

            return (startDate, endDate);
        }

        /// <summary>
        /// Lấy khoảng thời gian để đồng bộ dữ liệu theo advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="timezone">Timezone</param>
        /// <returns>Khoảng thời gian đồng bộ</returns>
        private async Task<(DateTime startDate, DateTime endDate)> GetRangeDateFiltering(string bcId, string advertiserId, string timezone)
        {
            // Lấy ngày hiện tại theo timezone của BC
            DateTime currentDateInTimezone = _dateTimeService.GetDateNow(timezone);
            DateTime endDate = currentDateInTimezone;

            // Lấy dữ liệu báo cáo chi tiết cấp livestream GMV Max Live Campaign mới nhất
            var latestReport = await GetLatestReportByBcIdAsync(bcId, advertiserId);

            DateTime startDate = currentDateInTimezone;
            if (latestReport == null)
            {
                // Nếu chưa có dữ liệu trong DB thì lấy khoảng 7 ngày từ ngày hiện tại
                startDate = currentDateInTimezone.AddDays(-7);
                _logger.LogDebug("Chưa có dữ liệu báo cáo chi tiết cấp livestream GMV Max Live Campaign trong DB cho BC: {BcId}, Advertiser: {AdvertiserId}. Lấy dữ liệu 7 ngày từ {StartDate} đến {EndDate}",
                    bcId, advertiserId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            }
            else
            {
                // Nếu có dữ liệu trong DB thì lấy từ ngày trong DB đến ngày hiện tại
                // Convert từ UTC (trong DB) sang timezone của BC để so sánh
                var latestReportDateInTimezone = _dateTimeService.ConvertFromUtc(latestReport.Date, timezone).Date;
                startDate = latestReportDateInTimezone;
                _logger.LogDebug("Có dữ liệu báo cáo chi tiết cấp livestream GMV Max Live Campaign trong DB cho BC: {BcId}, Advertiser: {AdvertiserId}. Lấy từ {StartDate} đến {EndDate}",
                    bcId, advertiserId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            }

            return (startDate, endDate);
        }

        /// <summary>
        /// Lấy báo cáo mới nhất theo BC ID và advertiserId
        /// </summary>
        /// <param name="bcId">BC ID</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>Báo cáo mới nhất</returns>
        private async Task<RawGmvMaxLiveDetailLivestreamReportEntity?> GetLatestReportByBcIdAsync(string bcId, string advertiserId)
        {
            var reports = await _gmvMaxLiveDetailLivestreamReportRepository.GetListAsync(
                bcId: bcId,
                advertiserId: advertiserId,
                sorting: "Date DESC",
                maxResultCount: 1);
            
            return reports.FirstOrDefault();
        }

        /// <summary>
        /// Lấy dữ liệu báo cáo chi tiết cấp livestream GMV Max Live Campaign từ TikTok API
        /// Lưu ý: Khi lấy báo cáo theo giờ, startDate và endDate phải cùng một ngày
        /// </summary>
        private async Task<List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)>> GetSyncGmvMaxLiveLivestreamFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, string advertiserId, DateTime startDate, DateTime endDate)
        {

            var records = new List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)>();

            // Lấy danh sách StoreId và CampaignId từ GMV Max Campaigns theo AdvertiserId
            var gmvMaxCampaigns = await _gmvMaxCampaignsRepository.GetByAdvertiserIdAndOperationStatusAsync(advertiserId, "ENABLE");
            var campaignStoreMapping = gmvMaxCampaigns
                .Where(x => !string.IsNullOrEmpty(x.StoreId) && !string.IsNullOrEmpty(x.CampaignId))
                .GroupBy(x => new { x.StoreId, x.CampaignId })
                .Select(g => new { g.Key.StoreId, g.Key.CampaignId })
                .ToList();

            if (!campaignStoreMapping.Any())
            {
                _logger.LogDebug("Không có Campaign và Store ID nào cho Advertiser: {AdvertiserId}", advertiserId);
                return records;
            }

            _logger.LogDebug("Tìm thấy {Count} Campaign-Store mapping cho Advertiser: {AdvertiserId}", campaignStoreMapping.Count, advertiserId);

            // Duyệt từng Campaign-Store mapping vì API chỉ hỗ trợ 1 StoreId mỗi lần gọi
            foreach (var mapping in campaignStoreMapping)
            {
                var pageRecords = await GetReportFromApiByAdvertiserStoreAndCampaignAsync(tikTokClient, advertiserId, mapping.StoreId, mapping.CampaignId, startDate, endDate);
                records.AddRange(pageRecords);
            }

            return records;
        }

        /// <summary>
        /// Lấy báo cáo từ API theo advertiser, store và campaign (Campaign Report-Livestream)
        /// </summary>
        private async Task<List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)>> GetReportFromApiByAdvertiserStoreAndCampaignAsync(TikTokBusinessApiClient tikTokClient, string advertiserId, string storeId, string campaignId, DateTime startDate, DateTime endDate)
        {
            var records = new List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)>();
            var page = 1;
            var pageSize = PAGE_SIZE_SYNC_REPORT;

            while (true)
            {
                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = advertiserId,
                    StoreIds = new List<string> { storeId },
                    StartDate = startDate.ToString("yyyy-MM-dd"),
                    EndDate = endDate.ToString("yyyy-MM-dd"),
                    Dimensions = new List<string> { "room_id", "stat_time_day" },
                    Metrics = new List<string>
                    {
                        "live_name", "live_status", "live_launched_time", "live_duration", 
                        "cost", "net_cost", "orders", "cost_per_order", "gross_revenue", "roi", 
                        "live_views", "cost_per_live_view", "10_second_live_views",
                        "cost_per_10_second_live_view", "live_follows"
                    },
                    Filtering = new GMVMaxReportFiltering
                    {
                        CampaignIds = new List<string> { campaignId }
                    },
                    Page = page,
                    PageSize = pageSize
                };

                var response = await tikTokClient.GMVMax.GetReportAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu báo cáo chi tiết cấp livestream GMV Max Live Campaign: {response.Message}");
                }

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break;
                }

                // Thêm AdvertiserId, StoreId và CampaignId vào mỗi item
                foreach (var item in response.Data.List)
                {
                    records.Add((item, advertiserId, storeId, campaignId));
                }

                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            return records;
        }

        /// <summary>
        /// Xử lý dữ liệu báo cáo từ API
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserId">ID nhà quảng cáo</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportDataList">Danh sách dữ liệu báo cáo</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task ProcessReportDataAsync(string bcId, string advertiserId, string bcTimezone, List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)> reportDataList, GmvMaxLiveLivestreamSyncResult result)
        {
            // sử dụng UnitOfWork để đảm bảo tính toàn vẹn dữ liệu cho mỗi 200 bản ghi
            if (reportDataList == null || !reportDataList.Any())
            {
                _logger.LogDebug("Không có dữ liệu báo cáo chi tiết cấp livestream GMV Max Live Campaign để xử lý cho {Total} bản ghi", reportDataList?.Count ?? 0);
                return;
            }

            var pageSize = PAGE_SIZE_HANDLE_SAVE_TO_DATABASE;
            var totalPages = (int)Math.Ceiling((double)reportDataList.Count / pageSize);
            _logger.LogDebug("Bắt đầu xử lý {TotalRecords} bản ghi báo cáo chi tiết cấp livestream GMV Max Live Campaign cho {TotalPages} trang", reportDataList.Count, totalPages);

            for (int page = 0; page < totalPages; page++)
            {
                var pageData = reportDataList.Skip(page * pageSize).Take(pageSize).ToList();
                if (pageData.Any())
                {
                    try
                    {
                        await ProcessPageDataAsync(bcId, bcTimezone, pageData, result);
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý dữ liệu báo cáo chi tiết cấp livestream GMV Max Live Campaign cho {Total} bản ghi, Trang: {Page}", pageData.Count, page + 1);
                        result.ErrorMessage += ex.Message + Environment.NewLine;
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        continue; // Bỏ qua lỗi và tiếp tục với trang tiếp theo
                    }
                }
            }
        }

        private async Task ProcessPageDataAsync(string bcId, string bcTimezone, List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)> pageData, GmvMaxLiveLivestreamSyncResult result)
        {
            // use unit of work
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var mappedEntities = await MapListReportDataToEntitiesAsync(bcId, bcTimezone, pageData);

                var existingEntities = await GetExistingEntitiesAsync(bcId, mappedEntities);

                var insertedEntities = new List<RawGmvMaxLiveDetailLivestreamReportEntity>();
                var updatedEntities = new List<RawGmvMaxLiveDetailLivestreamReportEntity>();

                foreach (var mappedEntity in mappedEntities)
                {
                                    // So sánh theo CampaignId, RoomId và Date với độ chính xác đến giờ
                var currentEntity = existingEntities.FirstOrDefault(x => x.CampaignId == mappedEntity.CampaignId && x.RoomId == mappedEntity.RoomId && x.Date == mappedEntity.Date);
                    if (currentEntity == null)
                    {
                        insertedEntities.Add(mappedEntity);
                        result.NewRecords++;
                        result.TotalSynced++;
                    }
                    else
                    {
                        // Cập nhật nếu có thay đổi
                        if (currentEntity.HasChanged(mappedEntity))
                        {
                            // Cập nhật từng field thủ công
                            currentEntity.LiveName = mappedEntity.LiveName;
                            currentEntity.LiveStatus = mappedEntity.LiveStatus;
                            currentEntity.LiveLaunchedTime = mappedEntity.LiveLaunchedTime;
                            currentEntity.LiveDuration = mappedEntity.LiveDuration;
                            currentEntity.Cost = mappedEntity.Cost;
                            currentEntity.NetCost = mappedEntity.NetCost;
                            currentEntity.Orders = mappedEntity.Orders;
                            currentEntity.CostPerOrder = mappedEntity.CostPerOrder;
                            currentEntity.GrossRevenue = mappedEntity.GrossRevenue;
                            currentEntity.ROI = mappedEntity.ROI;
                            currentEntity.LiveViews = mappedEntity.LiveViews;
                            currentEntity.CostPerLiveView = mappedEntity.CostPerLiveView;
                            currentEntity.TenSecondLiveViews = mappedEntity.TenSecondLiveViews;
                            currentEntity.CostPerTenSecondLiveView = mappedEntity.CostPerTenSecondLiveView;
                            currentEntity.LiveFollows = mappedEntity.LiveFollows;
                            currentEntity.Currency = mappedEntity.Currency;
                            
                            updatedEntities.Add(currentEntity);
                            result.UpdatedRecords++;
                            result.TotalSynced++;
                        }
                    }
                }

                // Thêm các bản ghi mới vào kho dữ liệu
                if (insertedEntities.Any())
                {
                    await _gmvMaxLiveDetailLivestreamReportRepository.InsertManyAsync(insertedEntities);
                }
                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _gmvMaxLiveDetailLivestreamReportRepository.UpdateManyAsync(updatedEntities);
                }

                result.DayCount += pageData.Count;
                result.CampaignCount += pageData.Select(x => x.campaignId).Distinct().Count();
                result.StoreCount += pageData.Select(x => x.storeId).Distinct().Count();
                result.LivestreamCount += pageData.Select(x => x.reportItem.Dimensions?.GetValueOrDefault("livestream_id")?.ToString()).Distinct().Count();

                await uow.CompleteAsync();
            }
        }

        /// <summary>
        /// Lấy các entity hiện có trong database
        /// </summary>
        private async Task<List<RawGmvMaxLiveDetailLivestreamReportEntity>> GetExistingEntitiesAsync(string bcId, List<RawGmvMaxLiveDetailLivestreamReportEntity> mappedEntities)
        {
            if (!mappedEntities.Any())
                return new List<RawGmvMaxLiveDetailLivestreamReportEntity>();

            var campaignIds = mappedEntities.Select(x => x.CampaignId).Distinct().ToList();
            var advertiserIds = mappedEntities.Select(x => x.AdvertiserId).Distinct().ToList();
            var roomIds = mappedEntities.Select(x => x.RoomId).Distinct().ToList();

            var minDate = mappedEntities.Min(x => x.Date);
            var maxDate = mappedEntities.Max(x => x.Date);

            var query = await _gmvMaxLiveDetailLivestreamReportRepository.GetQueryableAsync();
            return query.Where(x => x.BcId == bcId &&
                                    campaignIds.Contains(x.CampaignId) &&
                                    advertiserIds.Contains(x.AdvertiserId) &&
                                    roomIds.Contains(x.RoomId) &&
                                    x.Date >= minDate && x.Date <= maxDate).ToList();
        }

        private Task<List<RawGmvMaxLiveDetailLivestreamReportEntity>> MapListReportDataToEntitiesAsync(string bcId, string bcTimezone, List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)> reportDataList)
        {
            var entities = new List<RawGmvMaxLiveDetailLivestreamReportEntity>();
            foreach (var (reportItem, advertiserId, storeId, campaignId) in reportDataList)
            {
                var entity = MapReportDataToEntity(bcId, bcTimezone, reportItem, advertiserId, storeId, campaignId);
                if (entity != null)
                {
                    entities.Add(entity);
                }
            }
            return Task.FromResult(entities);
        }

        /// <summary>
        /// Map dữ liệu báo cáo từ API sang entity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportData">Dữ liệu báo cáo</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="storeId">ID của Store</param>
        /// <param name="campaignId">ID của Campaign</param>
        /// <returns>RawGmvMaxLiveDetailLivestreamReportEntity</returns>
        private RawGmvMaxLiveDetailLivestreamReportEntity? MapReportDataToEntity(string bcId, string bcTimezone, GMVMaxReportItem reportData, string advertiserId, string storeId, string campaignId)
        {
            var dateTimeStr = reportData.Dimensions?.GetValueOrDefault("stat_time_day")?.ToString();

            if (string.IsNullOrEmpty(dateTimeStr) || !DateTime.TryParse(dateTimeStr, out var reportDateTime))
            {
                _logger.LogWarning("Ngày báo cáo chi tiết cấp livestream GMV Max Live Campaign không hợp lệ: {DateTime} cho BC: {BcId}", dateTimeStr, bcId);
                return null;
            }

            // Convert datetime từ timezone của campaign sang UTC
            var reportDateTimeUtc = _dateTimeService.ConvertToUtc(reportDateTime, bcTimezone);

            var roomId = reportData.Dimensions?.GetValueOrDefault("livestream_id")?.ToString();
            if (string.IsNullOrEmpty(roomId))
            {
                _logger.LogWarning("Livestream ID không hợp lệ cho BC: {BcId}", bcId);
                return null;
            }

            var entity = new RawGmvMaxLiveDetailLivestreamReportEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdvertiserId = advertiserId,
                StoreId = storeId,
                CampaignId = campaignId,
                RoomId = roomId,
                Date = reportDateTimeUtc,  // Lưu datetime UTC với ngày
                Currency = "USD" // Default currency
            };

            if (reportData.Metrics != null)
            {
                // Map các metrics từ API response
                entity.LiveName = GetStringValue(reportData.Metrics, "livestream_title");
                
                // Map livestream status
                var livestreamStatusStr = GetStringValue(reportData.Metrics, "livestream_status");
                if (!string.IsNullOrEmpty(livestreamStatusStr) && Enum.TryParse<TikTok.Enums.LiveStatus>(livestreamStatusStr, true, out var livestreamStatus))
                {
                    entity.LiveStatus = livestreamStatus;
                }

                // Map livestream times
                var livestreamStartTimeStr = GetStringValue(reportData.Metrics, "livestream_start_time");
                if (!string.IsNullOrEmpty(livestreamStartTimeStr) && DateTime.TryParse(livestreamStartTimeStr, out var livestreamStartTime))
                {
                    entity.LiveLaunchedTime = livestreamStartTime;
                }

                entity.LiveDuration = GetStringValue(reportData.Metrics, "livestream_duration");
                entity.Cost = GetDecimalValue(reportData.Metrics, "cost");
                entity.NetCost = GetDecimalValue(reportData.Metrics, "net_cost");
                entity.Orders = GetIntValue(reportData.Metrics, "orders");
                entity.CostPerOrder = GetDecimalValue(reportData.Metrics, "cost_per_order");
                entity.GrossRevenue = GetDecimalValue(reportData.Metrics, "gross_revenue");
                entity.ROI = GetDecimalValue(reportData.Metrics, "roi");
                entity.LiveViews = GetLongValue(reportData.Metrics, "live_views");
                entity.CostPerLiveView = GetDecimalValue(reportData.Metrics, "cost_per_live_view");
                entity.TenSecondLiveViews = GetLongValue(reportData.Metrics, "ten_second_live_views");
                entity.CostPerTenSecondLiveView = GetDecimalValue(reportData.Metrics, "cost_per_ten_second_live_view");
                entity.LiveFollows = GetIntValue(reportData.Metrics, "live_follows");

                var currency = GetStringValue(reportData.Metrics, "currency");
                if (!string.IsNullOrEmpty(currency))
                {
                    entity.Currency = currency;
                }
            }

            return entity;
        }
    }
}
