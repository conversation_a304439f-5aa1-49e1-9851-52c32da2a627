/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Inventory Estimate Models

    /// <summary>
    /// Request model for getting inventory estimates for Reach & Frequency ads
    /// </summary>
    public class RFInventoryEstimateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Audience targeting rules
        /// </summary>
        [JsonPropertyName("audience_info")]
        public RFAudienceInfo AudienceInfo { get; set; } = new();

        /// <summary>
        /// Start time (UTC+0): format "YYYY-MM-DD HH:MM:SS"
        /// </summary>
        [JsonPropertyName("schedule_start_time")]
        public string ScheduleStartTime { get; set; } = string.Empty;

        /// <summary>
        /// End time (UTC+0): format "YYYY-MM-DD HH:MM:SS"
        /// </summary>
        [JsonPropertyName("schedule_end_time")]
        public string ScheduleEndTime { get; set; } = string.Empty;

        /// <summary>
        /// Frequency
        /// </summary>
        [JsonPropertyName("frequency")]
        public int Frequency { get; set; }

        /// <summary>
        /// Frequency cycle (in days)
        /// </summary>
        [JsonPropertyName("frequency_schedule")]
        public int FrequencySchedule { get; set; }

        /// <summary>
        /// The advertising objective
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string ObjectiveType { get; set; } = "RF_REACH";

        /// <summary>
        /// The target video view duration
        /// </summary>
        [JsonPropertyName("cpv_video_duration")]
        public string? CpvVideoDuration { get; set; }

        /// <summary>
        /// Feed type option
        /// </summary>
        [JsonPropertyName("feed_type")]
        public string? FeedType { get; set; }

        /// <summary>
        /// Buying type
        /// </summary>
        [JsonPropertyName("rf_purchased_type")]
        public string RfPurchasedType { get; set; } = string.Empty;

        /// <summary>
        /// Budget (required when rf_purchased_type = FIXED_BUDGET)
        /// </summary>
        [JsonPropertyName("budget")]
        public float? Budget { get; set; }

        /// <summary>
        /// Impressions to be purchased, in the unit of 1,000 (required when rf_purchased_type = FIXED_SHOW)
        /// </summary>
        [JsonPropertyName("purchased_impression")]
        public int? PurchasedImpression { get; set; }

        /// <summary>
        /// Number of users reached to be purchased, in the unit of 1,000 (required when rf_purchased_type = FIXED_REACH)
        /// </summary>
        [JsonPropertyName("purchased_reach")]
        public int? PurchasedReach { get; set; }

        /// <summary>
        /// Campaign type for TikTok Pulse
        /// </summary>
        [JsonPropertyName("rf_campaign_type")]
        public string? RfCampaignType { get; set; }
    }

    /// <summary>
    /// Audience targeting rules for Reach & Frequency
    /// </summary>
    public class RFAudienceInfo
    {
        /// <summary>
        /// IDs of the audiences that you want to target
        /// </summary>
        [JsonPropertyName("audience_ids")]
        public List<string>? AudienceIds { get; set; }

        /// <summary>
        /// IDs of the audiences that you want to exclude
        /// </summary>
        [JsonPropertyName("excluded_audience_ids")]
        public List<string>? ExcludedAudienceIds { get; set; }

        /// <summary>
        /// Target age range
        /// </summary>
        [JsonPropertyName("age_groups")]
        public List<string>? AgeGroups { get; set; }

        /// <summary>
        /// Target gender
        /// </summary>
        [JsonPropertyName("gender")]
        public string? Gender { get; set; }

        /// <summary>
        /// Target language
        /// </summary>
        [JsonPropertyName("languages")]
        public List<string>? Languages { get; set; }

        /// <summary>
        /// IDs of the targeted locations
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string> LocationIds { get; set; } = new();

        /// <summary>
        /// Operating system
        /// </summary>
        [JsonPropertyName("operating_systems")]
        public List<string>? OperatingSystems { get; set; }

        /// <summary>
        /// Network type
        /// </summary>
        [JsonPropertyName("network_types")]
        public List<string>? NetworkTypes { get; set; }

        /// <summary>
        /// IDs of the device models that you want to target
        /// </summary>
        [JsonPropertyName("device_model_ids")]
        public List<string>? DeviceModelIds { get; set; }

        /// <summary>
        /// Targeting device price range
        /// </summary>
        [JsonPropertyName("device_price_ranges")]
        public List<int>? DevicePriceRanges { get; set; }

        /// <summary>
        /// Carrier IDs
        /// </summary>
        [JsonPropertyName("carrier_ids")]
        public List<string>? CarrierIds { get; set; }

        /// <summary>
        /// Interest classification
        /// </summary>
        [JsonPropertyName("interest_category_ids")]
        public List<string>? InterestCategoryIds { get; set; }

        /// <summary>
        /// Brand safety type for Reach and Frequency ads
        /// </summary>
        [JsonPropertyName("brand_safety_type")]
        public string? BrandSafetyType { get; set; }

        /// <summary>
        /// Contextual tag IDs
        /// </summary>
        [JsonPropertyName("contextual_tag_ids")]
        public List<string>? ContextualTagIds { get; set; }
    }

    /// <summary>
    /// Response model for inventory estimate
    /// </summary>
    public class RFInventoryEstimateResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string RequestId { get; set; } = string.Empty;

        /// <summary>
        /// Returned data
        /// </summary>
        [JsonPropertyName("data")]
        public RFInventoryEstimateData? Data { get; set; }
    }

    /// <summary>
    /// Inventory estimate data
    /// </summary>
    public class RFInventoryEstimateData
    {
        /// <summary>
        /// Estimated inventory result
        /// </summary>
        [JsonPropertyName("default_result")]
        public RFEstimateResult? DefaultResult { get; set; }

        /// <summary>
        /// Estimated inventory results for different budgets
        /// </summary>
        [JsonPropertyName("results")]
        public List<RFEstimateResult>? Results { get; set; }
    }

    /// <summary>
    /// Estimate result
    /// </summary>
    public class RFEstimateResult
    {
        /// <summary>
        /// Basic information
        /// </summary>
        [JsonPropertyName("base_info")]
        public RFBaseInfo? BaseInfo { get; set; }

        /// <summary>
        /// Daily spend
        /// </summary>
        [JsonPropertyName("daily_cost")]
        public List<RFDailyCost>? DailyCost { get; set; }

        /// <summary>
        /// Distribution of impressions per person
        /// </summary>
        [JsonPropertyName("frequency_per_person")]
        public List<RFFrequencyPerPerson>? FrequencyPerPerson { get; set; }
    }

    /// <summary>
    /// Basic information for RF estimate
    /// </summary>
    public class RFBaseInfo
    {
        /// <summary>
        /// Total budget
        /// </summary>
        [JsonPropertyName("budget")]
        public float Budget { get; set; }

        /// <summary>
        /// CPM (Cost per mille)
        /// </summary>
        [JsonPropertyName("cpm")]
        public float Cpm { get; set; }

        /// <summary>
        /// Total impressions, in the unit of 1,000
        /// </summary>
        [JsonPropertyName("impression")]
        public int Impression { get; set; }

        /// <summary>
        /// Number of users reached, in the unit of 1,000
        /// </summary>
        [JsonPropertyName("reach")]
        public int Reach { get; set; }

        /// <summary>
        /// Number of total bookable reach, in the unit of 1,000
        /// </summary>
        [JsonPropertyName("max_reach")]
        public int? MaxReach { get; set; }

        /// <summary>
        /// Average frequency
        /// </summary>
        [JsonPropertyName("average_frequency")]
        public float AverageFrequency { get; set; }
    }

    /// <summary>
    /// Daily cost information
    /// </summary>
    public class RFDailyCost
    {
        /// <summary>
        /// Spend for the day
        /// </summary>
        [JsonPropertyName("cost")]
        public float Cost { get; set; }

        /// <summary>
        /// Date in format "YYYY-MM-DD"
        /// </summary>
        [JsonPropertyName("date")]
        public string Date { get; set; } = string.Empty;
    }

    /// <summary>
    /// Frequency per person information
    /// </summary>
    public class RFFrequencyPerPerson
    {
        /// <summary>
        /// Impressions per person
        /// </summary>
        [JsonPropertyName("frequency")]
        public int Frequency { get; set; }

        /// <summary>
        /// The percentage of users for the frequency
        /// </summary>
        [JsonPropertyName("percentage")]
        public float Percentage { get; set; }
    }

    #endregion

    #region RF Ad Group Models

    /// <summary>
    /// Request model for creating a Reach & Frequency ad group
    /// </summary>
    public class RFAdGroupCreateRequest
    {
        /// <summary>
        /// Request ID with which you can create ad groups with duplicate names
        /// </summary>
        [JsonPropertyName("request_id")]
        public string RequestId { get; set; } = string.Empty;

        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The campaign the ad group belongs to
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// Whether sharing to third-party platforms is disabled for ads in this ad group
        /// </summary>
        [JsonPropertyName("share_disabled")]
        public bool? ShareDisabled { get; set; }

        /// <summary>
        /// Name of the ad group
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string AdGroupName { get; set; } = string.Empty;

        /// <summary>
        /// Promotion type
        /// </summary>
        [JsonPropertyName("promotion_type")]
        public string PromotionType { get; set; } = string.Empty;

        /// <summary>
        /// Conversion event for the ad group
        /// </summary>
        [JsonPropertyName("optimization_event")]
        public string? OptimizationEvent { get; set; }

        /// <summary>
        /// ID of the promoted mobile application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Whether to prohibit users from commenting on your ads on TikTok
        /// </summary>
        [JsonPropertyName("comment_disabled")]
        public bool? CommentDisabled { get; set; }

        /// <summary>
        /// IDs of the audiences that you want to target
        /// </summary>
        [JsonPropertyName("audience_ids")]
        public List<string>? AudienceIds { get; set; }

        /// <summary>
        /// IDs of the audiences that you want to exclude
        /// </summary>
        [JsonPropertyName("excluded_audience_ids")]
        public List<string>? ExcludedAudienceIds { get; set; }

        /// <summary>
        /// Age groups you want to target
        /// </summary>
        [JsonPropertyName("age_groups")]
        public List<string>? AgeGroups { get; set; }

        /// <summary>
        /// Audience gender
        /// </summary>
        [JsonPropertyName("gender")]
        public string? Gender { get; set; }

        /// <summary>
        /// Codes of the languages that you want to target
        /// </summary>
        [JsonPropertyName("languages")]
        public List<string>? Languages { get; set; }

        /// <summary>
        /// IDs of the targeted locations
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string> LocationIds { get; set; } = new();

        /// <summary>
        /// Whether the promoted content is HFSS foods
        /// </summary>
        [JsonPropertyName("is_hfss")]
        public bool? IsHfss { get; set; }

        /// <summary>
        /// Device operating systems that you want to target
        /// </summary>
        [JsonPropertyName("operating_systems")]
        public List<string>? OperatingSystems { get; set; }

        /// <summary>
        /// Device connection types that you want to target
        /// </summary>
        [JsonPropertyName("network_types")]
        public List<string>? NetworkTypes { get; set; }

        /// <summary>
        /// IDs of the device models that you want to target
        /// </summary>
        [JsonPropertyName("device_model_ids")]
        public List<string>? DeviceModelIds { get; set; }

        /// <summary>
        /// Targeting device price range
        /// </summary>
        [JsonPropertyName("device_price_ranges")]
        public List<int>? DevicePriceRanges { get; set; }

        /// <summary>
        /// Carriers that you want to target
        /// </summary>
        [JsonPropertyName("carrier_ids")]
        public List<string>? CarrierIds { get; set; }

        /// <summary>
        /// List of interest categories that you want to target
        /// </summary>
        [JsonPropertyName("interest_category_ids")]
        public List<string>? InterestCategoryIds { get; set; }

        /// <summary>
        /// Buying type
        /// </summary>
        [JsonPropertyName("rf_purchased_type")]
        public string RfPurchasedType { get; set; } = string.Empty;

        /// <summary>
        /// Budget
        /// </summary>
        [JsonPropertyName("budget")]
        public float Budget { get; set; }

        /// <summary>
        /// Impressions to be purchased, in the unit of 1,000
        /// </summary>
        [JsonPropertyName("purchased_impression")]
        public int PurchasedImpression { get; set; }

        /// <summary>
        /// Purchased user reach, in the unit of 1,000
        /// </summary>
        [JsonPropertyName("purchased_reach")]
        public int PurchasedReach { get; set; }

        /// <summary>
        /// Schedule start time (UTC+0), in the format of YYYY-MM-DD HH:MM:SS
        /// </summary>
        [JsonPropertyName("schedule_start_time")]
        public string ScheduleStartTime { get; set; } = string.Empty;

        /// <summary>
        /// Schedule end time (UTC+0), in the format of YYYY-MM-DD HH:MM:SS
        /// </summary>
        [JsonPropertyName("schedule_end_time")]
        public string ScheduleEndTime { get; set; } = string.Empty;

        /// <summary>
        /// Frequency
        /// </summary>
        [JsonPropertyName("frequency")]
        public int Frequency { get; set; }

        /// <summary>
        /// Frequency schedule
        /// </summary>
        [JsonPropertyName("frequency_schedule")]
        public int FrequencySchedule { get; set; }

        /// <summary>
        /// The measurable results you'd like to drive with your ads
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string OptimizationGoal { get; set; } = string.Empty;

        /// <summary>
        /// The target video view duration
        /// </summary>
        [JsonPropertyName("cpv_video_duration")]
        public string? CpvVideoDuration { get; set; }

        /// <summary>
        /// Brand safety type for Reach and Frequency ads
        /// </summary>
        [JsonPropertyName("brand_safety_type")]
        public string? BrandSafetyType { get; set; }

        /// <summary>
        /// Brand safety partner for Reach and Frequency ads
        /// </summary>
        [JsonPropertyName("brand_safety_partner")]
        public string? BrandSafetyPartner { get; set; }

        /// <summary>
        /// Content exclusion category IDs
        /// </summary>
        [JsonPropertyName("category_exclusion_ids")]
        public List<string>? CategoryExclusionIds { get; set; }

        /// <summary>
        /// Whether users can download your video ads on TikTok
        /// </summary>
        [JsonPropertyName("video_download_disabled")]
        public bool? VideoDownloadDisabled { get; set; }

        /// <summary>
        /// Feed type option
        /// </summary>
        [JsonPropertyName("feed_type")]
        public string? FeedType { get; set; }

        /// <summary>
        /// The strategy for sequencing and scheduling your ad delivery
        /// </summary>
        [JsonPropertyName("delivery_mode")]
        public string? DeliveryMode { get; set; }

        /// <summary>
        /// Ad delivery information
        /// </summary>
        [JsonPropertyName("schedule_infos")]
        public List<RFScheduleInfo>? ScheduleInfos { get; set; }

        /// <summary>
        /// Contextual tag IDs
        /// </summary>
        [JsonPropertyName("contextual_tag_ids")]
        public List<string>? ContextualTagIds { get; set; }
    }

    /// <summary>
    /// Schedule information for RF ad group
    /// </summary>
    public class RFScheduleInfo
    {
        /// <summary>
        /// The details of the scheduled delivery that you want to set for the ad
        /// </summary>
        [JsonPropertyName("schedules")]
        public List<RFSchedule>? Schedules { get; set; }

        /// <summary>
        /// The delivery order for an ad within the ad group
        /// </summary>
        [JsonPropertyName("expected_orders")]
        public List<int>? ExpectedOrders { get; set; }
    }

    /// <summary>
    /// Schedule details for RF ad group
    /// </summary>
    public class RFSchedule
    {
        /// <summary>
        /// Ad delivery start time, in the format of "YYYY-MM-DD HH:MM:SS"
        /// </summary>
        [JsonPropertyName("start_time")]
        public string StartTime { get; set; } = string.Empty;

        /// <summary>
        /// Ad delivery end time, in the format of "YYYY-MM-DD HH:MM:SS"
        /// </summary>
        [JsonPropertyName("end_time")]
        public string EndTime { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for RF ad group creation
    /// </summary>
    public class RFAdGroupCreateResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string RequestId { get; set; } = string.Empty;

        /// <summary>
        /// Returned data
        /// </summary>
        [JsonPropertyName("data")]
        public RFAdGroupData? Data { get; set; }
    }

    /// <summary>
    /// RF ad group data
    /// </summary>
    public class RFAdGroupData
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string AdGroupId { get; set; } = string.Empty;

        /// <summary>
        /// Whether sharing to third-party platforms is disabled
        /// </summary>
        [JsonPropertyName("share_disabled")]
        public bool? ShareDisabled { get; set; }

        /// <summary>
        /// Name of the ad group
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string AdGroupName { get; set; } = string.Empty;

        /// <summary>
        /// Promotion type
        /// </summary>
        [JsonPropertyName("promotion_type")]
        public string? PromotionType { get; set; }

        /// <summary>
        /// Conversion event for the ad group
        /// </summary>
        [JsonPropertyName("optimization_event")]
        public string? OptimizationEvent { get; set; }

        /// <summary>
        /// ID of the promoted mobile application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Whether to prohibit users from commenting on your ads
        /// </summary>
        [JsonPropertyName("comment_disabled")]
        public bool? CommentDisabled { get; set; }

        /// <summary>
        /// IDs of the audiences that you want to target
        /// </summary>
        [JsonPropertyName("audience_ids")]
        public List<string>? AudienceIds { get; set; }

        /// <summary>
        /// IDs of the audiences that you want to exclude
        /// </summary>
        [JsonPropertyName("excluded_audience_ids")]
        public List<string>? ExcludedAudienceIds { get; set; }

        /// <summary>
        /// Age groups you want to target
        /// </summary>
        [JsonPropertyName("age_groups")]
        public List<string>? AgeGroups { get; set; }

        /// <summary>
        /// Audience gender
        /// </summary>
        [JsonPropertyName("gender")]
        public string? Gender { get; set; }

        /// <summary>
        /// Codes of the languages that you want to target
        /// </summary>
        [JsonPropertyName("languages")]
        public List<string>? Languages { get; set; }

        /// <summary>
        /// IDs of the targeted locations
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string>? LocationIds { get; set; }

        /// <summary>
        /// Whether the promoted content is HFSS foods
        /// </summary>
        [JsonPropertyName("is_hfss")]
        public bool? IsHfss { get; set; }

        /// <summary>
        /// Device operating systems that you want to target
        /// </summary>
        [JsonPropertyName("operating_systems")]
        public List<string>? OperatingSystems { get; set; }

        /// <summary>
        /// Device connection types that you want to target
        /// </summary>
        [JsonPropertyName("network_types")]
        public List<string>? NetworkTypes { get; set; }

        /// <summary>
        /// IDs of the device models that you want to target
        /// </summary>
        [JsonPropertyName("device_model_ids")]
        public List<string>? DeviceModelIds { get; set; }

        /// <summary>
        /// Targeting device price range
        /// </summary>
        [JsonPropertyName("device_price_ranges")]
        public List<int>? DevicePriceRanges { get; set; }

        /// <summary>
        /// Carriers that you want to target
        /// </summary>
        [JsonPropertyName("carrier_ids")]
        public List<string>? CarrierIds { get; set; }

        /// <summary>
        /// List of interest categories that you want to target
        /// </summary>
        [JsonPropertyName("interest_category_ids")]
        public List<string>? InterestCategoryIds { get; set; }

        /// <summary>
        /// Buying type
        /// </summary>
        [JsonPropertyName("rf_purchased_type")]
        public string? RfPurchasedType { get; set; }

        /// <summary>
        /// Budget
        /// </summary>
        [JsonPropertyName("budget")]
        public float? Budget { get; set; }

        /// <summary>
        /// Impressions to be purchased, in the unit of 1,000
        /// </summary>
        [JsonPropertyName("purchased_impression")]
        public int? PurchasedImpression { get; set; }

        /// <summary>
        /// Purchased user reach, in the unit of 1,000
        /// </summary>
        [JsonPropertyName("purchased_reach")]
        public int? PurchasedReach { get; set; }

        /// <summary>
        /// Schedule start time
        /// </summary>
        [JsonPropertyName("schedule_start_time")]
        public string? ScheduleStartTime { get; set; }

        /// <summary>
        /// Schedule end time
        /// </summary>
        [JsonPropertyName("schedule_end_time")]
        public string? ScheduleEndTime { get; set; }

        /// <summary>
        /// Frequency
        /// </summary>
        [JsonPropertyName("frequency")]
        public int? Frequency { get; set; }

        /// <summary>
        /// Frequency schedule
        /// </summary>
        [JsonPropertyName("frequency_schedule")]
        public int? FrequencySchedule { get; set; }

        /// <summary>
        /// The measurable results you'd like to drive with your ads
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string? OptimizationGoal { get; set; }

        /// <summary>
        /// The target video view duration
        /// </summary>
        [JsonPropertyName("cpv_video_duration")]
        public string? CpvVideoDuration { get; set; }

        /// <summary>
        /// Brand safety type for Reach and Frequency ads
        /// </summary>
        [JsonPropertyName("brand_safety_type")]
        public string? BrandSafetyType { get; set; }

        /// <summary>
        /// Brand safety partner for Reach and Frequency ads
        /// </summary>
        [JsonPropertyName("brand_safety_partner")]
        public string? BrandSafetyPartner { get; set; }

        /// <summary>
        /// Content exclusion category IDs
        /// </summary>
        [JsonPropertyName("category_exclusion_ids")]
        public List<string>? CategoryExclusionIds { get; set; }

        /// <summary>
        /// Whether users can download your video ads on TikTok
        /// </summary>
        [JsonPropertyName("video_download_disabled")]
        public bool? VideoDownloadDisabled { get; set; }

        /// <summary>
        /// Feed type option
        /// </summary>
        [JsonPropertyName("feed_type")]
        public string? FeedType { get; set; }

        /// <summary>
        /// The strategy for sequencing and scheduling your ad delivery
        /// </summary>
        [JsonPropertyName("delivery_mode")]
        public string? DeliveryMode { get; set; }

        /// <summary>
        /// Ad delivery information
        /// </summary>
        [JsonPropertyName("schedule_infos")]
        public List<RFScheduleInfoResponse>? ScheduleInfos { get; set; }

        /// <summary>
        /// System notice
        /// </summary>
        [JsonPropertyName("notice")]
        public string? Notice { get; set; }

        /// <summary>
        /// Contextual tag IDs
        /// </summary>
        [JsonPropertyName("contextual_tag_ids")]
        public List<string>? ContextualTagIds { get; set; }
    }

    /// <summary>
    /// Schedule information response for RF ad group
    /// </summary>
    public class RFScheduleInfoResponse
    {
        /// <summary>
        /// The details of the scheduled delivery for the ad
        /// </summary>
        [JsonPropertyName("schedules")]
        public List<RFSchedule>? Schedules { get; set; }

        /// <summary>
        /// The delivery order for an ad within the ad group
        /// </summary>
        [JsonPropertyName("expected_orders")]
        public List<int>? ExpectedOrders { get; set; }

        /// <summary>
        /// Whether the ad delivery information is in draft mode
        /// </summary>
        [JsonPropertyName("is_draft")]
        public bool? IsDraft { get; set; }

        /// <summary>
        /// Schedule ID
        /// </summary>
        [JsonPropertyName("schedule_id")]
        public string? ScheduleId { get; set; }
    }

    #endregion

    #region RF Ad Group Update Models

    /// <summary>
    /// Request model for updating a Reach & Frequency ad group
    /// </summary>
    public class RFAdGroupUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the ad group to be modified
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string AdGroupId { get; set; } = string.Empty;

        /// <summary>
        /// Whether sharing to third-party platforms is disabled for ads in this ad group
        /// </summary>
        [JsonPropertyName("share_disabled")]
        public bool? ShareDisabled { get; set; }

        /// <summary>
        /// Name of the ad group
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdGroupName { get; set; }

        /// <summary>
        /// Whether to prohibit users from posting comments to your ads on TikTok
        /// </summary>
        [JsonPropertyName("comment_disabled")]
        public bool? CommentDisabled { get; set; }

        /// <summary>
        /// IDs of the audiences that you want to target
        /// </summary>
        [JsonPropertyName("audience_ids")]
        public List<string>? AudienceIds { get; set; }

        /// <summary>
        /// IDs of the audiences that you want to exclude
        /// </summary>
        [JsonPropertyName("excluded_audience_ids")]
        public List<string>? ExcludedAudienceIds { get; set; }

        /// <summary>
        /// Audience age range
        /// </summary>
        [JsonPropertyName("age_groups")]
        public List<string>? AgeGroups { get; set; }

        /// <summary>
        /// Target gender
        /// </summary>
        [JsonPropertyName("gender")]
        public string? Gender { get; set; }

        /// <summary>
        /// Audience language
        /// </summary>
        [JsonPropertyName("languages")]
        public List<string>? Languages { get; set; }

        /// <summary>
        /// IDs of the targeted locations
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string>? LocationIds { get; set; }

        /// <summary>
        /// Whether the promoted content is HFSS foods
        /// </summary>
        [JsonPropertyName("is_hfss")]
        public bool? IsHfss { get; set; }

        /// <summary>
        /// Operating system
        /// </summary>
        [JsonPropertyName("operating_systems")]
        public List<string>? OperatingSystems { get; set; }

        /// <summary>
        /// Device connection types that you want to target
        /// </summary>
        [JsonPropertyName("network_types")]
        public List<string>? NetworkTypes { get; set; }

        /// <summary>
        /// IDs of the device models that you want to target
        /// </summary>
        [JsonPropertyName("device_model_ids")]
        public List<string>? DeviceModelIds { get; set; }

        /// <summary>
        /// Targeting device price range
        /// </summary>
        [JsonPropertyName("device_price_ranges")]
        public List<int>? DevicePriceRanges { get; set; }

        /// <summary>
        /// Carriers that you want to target
        /// </summary>
        [JsonPropertyName("carrier_ids")]
        public List<string>? CarrierIds { get; set; }

        /// <summary>
        /// Interest classification
        /// </summary>
        [JsonPropertyName("interest_category_ids")]
        public List<string>? InterestCategoryIds { get; set; }

        /// <summary>
        /// Buying type
        /// </summary>
        [JsonPropertyName("rf_purchased_type")]
        public string? RfPurchasedType { get; set; }

        /// <summary>
        /// Budget
        /// </summary>
        [JsonPropertyName("budget")]
        public float? Budget { get; set; }

        /// <summary>
        /// Impressions to be purchased, in the unit of 1,000
        /// </summary>
        [JsonPropertyName("purchased_impression")]
        public int? PurchasedImpression { get; set; }

        /// <summary>
        /// Number of users reached to be purchased, in the unit of 1,000
        /// </summary>
        [JsonPropertyName("purchased_reach")]
        public int? PurchasedReach { get; set; }

        /// <summary>
        /// Schedule start time (UTC+0), in the format of YYYY-MM-DD HH:MM:SS
        /// </summary>
        [JsonPropertyName("schedule_start_time")]
        public string? ScheduleStartTime { get; set; }

        /// <summary>
        /// Schedule end time (UTC+0), in the format of YYYY-MM-DD HH:MM:SS
        /// </summary>
        [JsonPropertyName("schedule_end_time")]
        public string? ScheduleEndTime { get; set; }

        /// <summary>
        /// Frequency
        /// </summary>
        [JsonPropertyName("frequency")]
        public int? Frequency { get; set; }

        /// <summary>
        /// Frequency schedule
        /// </summary>
        [JsonPropertyName("frequency_schedule")]
        public int? FrequencySchedule { get; set; }

        /// <summary>
        /// Feed type option
        /// </summary>
        [JsonPropertyName("feed_type")]
        public string? FeedType { get; set; }

        /// <summary>
        /// The strategy for sequencing and scheduling your ad delivery
        /// </summary>
        [JsonPropertyName("delivery_mode")]
        public string? DeliveryMode { get; set; }

        /// <summary>
        /// Ad delivery information
        /// </summary>
        [JsonPropertyName("schedule_infos")]
        public List<RFScheduleInfo>? ScheduleInfos { get; set; }

        /// <summary>
        /// Contextual tag IDs
        /// </summary>
        [JsonPropertyName("contextual_tag_ids")]
        public List<string>? ContextualTagIds { get; set; }

        /// <summary>
        /// Brand safety type
        /// </summary>
        [JsonPropertyName("brand_safety_type")]
        public string? BrandSafetyType { get; set; }

        /// <summary>
        /// Content exclusion category IDs
        /// </summary>
        [JsonPropertyName("category_exclusion_ids")]
        public List<string>? CategoryExclusionIds { get; set; }
    }

    #endregion

    #region RF Order Cancel Models

    /// <summary>
    /// Request model for canceling R&F ad order
    /// </summary>
    public class RFOrderCancelRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the ad group to be withdrawn
        /// </summary>
        [JsonPropertyName("adgroup_ids")]
        public List<string> AdGroupIds { get; set; } = new();
    }

    /// <summary>
    /// Response model for RF order cancellation
    /// </summary>
    public class RFOrderCancelResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string RequestId { get; set; } = string.Empty;

        /// <summary>
        /// Returned data
        /// </summary>
        [JsonPropertyName("data")]
        public RFOrderCancelData? Data { get; set; }
    }

    /// <summary>
    /// RF order cancel data
    /// </summary>
    public class RFOrderCancelData
    {
        /// <summary>
        /// ID of the ad group whose withdrawal request has failed
        /// </summary>
        [JsonPropertyName("fail_adgroup_ids")]
        public List<string>? FailAdGroupIds { get; set; }
    }

    #endregion

    #region RF Estimated Info Models

    /// <summary>
    /// Request model for getting estimated info of R&F ad groups
    /// </summary>
    public class RFEstimatedInfoRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group IDs
        /// </summary>
        [JsonPropertyName("adgroup_ids")]
        public List<string> AdGroupIds { get; set; } = new();
    }

    /// <summary>
    /// Response model for RF estimated info
    /// </summary>
    public class RFEstimatedInfoResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string RequestId { get; set; } = string.Empty;

        /// <summary>
        /// Returned data
        /// </summary>
        [JsonPropertyName("data")]
        public RFEstimatedInfoData? Data { get; set; }
    }

    /// <summary>
    /// RF estimated info data
    /// </summary>
    public class RFEstimatedInfoData
    {
        /// <summary>
        /// Estimated information
        /// </summary>
        [JsonPropertyName("estimated_info")]
        public List<RFEstimatedInfo>? EstimatedInfo { get; set; }
    }

    /// <summary>
    /// RF estimated information
    /// </summary>
    public class RFEstimatedInfo
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string AdGroupId { get; set; } = string.Empty;

        /// <summary>
        /// Basic information
        /// </summary>
        [JsonPropertyName("base_info")]
        public RFBaseInfo? BaseInfo { get; set; }

        /// <summary>
        /// Daily cost
        /// </summary>
        [JsonPropertyName("daily_cost")]
        public List<RFDailyCost>? DailyCost { get; set; }

        /// <summary>
        /// Distribution of impressions per capita
        /// </summary>
        [JsonPropertyName("frequency_per_person")]
        public List<RFFrequencyPerPerson>? FrequencyPerPerson { get; set; }
    }

    #endregion

    #region RF Contract Query Models

    /// <summary>
    /// Request model for querying contracts
    /// </summary>
    public class RFContractQueryRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// A certain date within the contract validity, format: "2020-09-18"
        /// </summary>
        [JsonPropertyName("included_date")]
        public string IncludedDate { get; set; } = string.Empty;

        /// <summary>
        /// Use this field to specify whether you want to query TikTok Pulse contract
        /// </summary>
        [JsonPropertyName("rf_campaign_type")]
        public string? RfCampaignType { get; set; }
    }

    /// <summary>
    /// Response model for contract query
    /// </summary>
    public class RFContractQueryResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string RequestId { get; set; } = string.Empty;

        /// <summary>
        /// Returned data
        /// </summary>
        [JsonPropertyName("data")]
        public RFContractData? Data { get; set; }
    }

    /// <summary>
    /// RF contract data
    /// </summary>
    public class RFContractData
    {
        /// <summary>
        /// Whether valid contracts can be found
        /// </summary>
        [JsonPropertyName("has_valid_contract")]
        public bool HasValidContract { get; set; }

        /// <summary>
        /// Start date of TikTok Pulse contract
        /// </summary>
        [JsonPropertyName("pulse_start_date")]
        public string? PulseStartDate { get; set; }

        /// <summary>
        /// End date of TikTok Pulse contract
        /// </summary>
        [JsonPropertyName("pulse_end_date")]
        public string? PulseEndDate { get; set; }

        /// <summary>
        /// Start date of Reach and Frequency contract
        /// </summary>
        [JsonPropertyName("start_date")]
        public string? StartDate { get; set; }

        /// <summary>
        /// End date of Reach and Frequency contract
        /// </summary>
        [JsonPropertyName("end_date")]
        public string? EndDate { get; set; }
    }

    #endregion

    #region RF Time Zone Models

    /// <summary>
    /// Request model for getting R&F time zones
    /// </summary>
    public class RFTimeZoneRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Country codes to be queried
        /// </summary>
        [JsonPropertyName("region_codes")]
        public List<string> RegionCodes { get; set; } = new();
    }

    /// <summary>
    /// Response model for RF time zones
    /// </summary>
    public class RFTimeZoneResponse
    {
        /// <summary>
        /// Return code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Return message
        /// </summary>
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Request ID
        /// </summary>
        [JsonPropertyName("request_id")]
        public string RequestId { get; set; } = string.Empty;

        /// <summary>
        /// Returned data
        /// </summary>
        [JsonPropertyName("data")]
        public RFTimeZoneData? Data { get; set; }
    }

    /// <summary>
    /// RF time zone data
    /// </summary>
    public class RFTimeZoneData
    {
        /// <summary>
        /// Time zone information
        /// </summary>
        [JsonPropertyName("timezone_info")]
        public List<RFTimeZoneInfo>? TimeZoneInfo { get; set; }
    }

    /// <summary>
    /// RF time zone information
    /// </summary>
    public class RFTimeZoneInfo
    {
        /// <summary>
        /// Region code
        /// </summary>
        [JsonPropertyName("region_code")]
        public string RegionCode { get; set; } = string.Empty;

        /// <summary>
        /// Supported targeting time zones
        /// </summary>
        [JsonPropertyName("delivery_timezone")]
        public List<string> DeliveryTimeZone { get; set; } = new();
    }

    #endregion
}
