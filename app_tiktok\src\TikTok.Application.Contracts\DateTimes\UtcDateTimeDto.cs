using System;

namespace TikTok.DateTimes;

/// <summary>
/// DTO chứa thông tin DateTime UTC và timezone UTC
/// </summary>
public class UtcDateTimeDto
{
    /// <summary>
    /// DateTime UTC
    /// </summary>
    public System.DateTime UtcDateTime { get; set; }

    /// <summary>
    /// Timezone UTC (luôn là "UTC")
    /// </summary>
    public string UtcTimezone { get; set; }

    /// <summary>
    /// Offset của UTC (luôn là "00:00:00")
    /// </summary>
    public TimeSpan UtcOffset { get; set; }

    public UtcDateTimeDto(System.DateTime utcDateTime)
    {
        UtcDateTime = utcDateTime;
        UtcTimezone = "UTC";
        UtcOffset = TimeSpan.Zero;
    }

    public UtcDateTimeDto(System.DateTime utcDateTime, string utcTimezone, TimeSpan utcOffset)
    {
        UtcDateTime = utcDateTime;
        UtcTimezone = utcTimezone;
        UtcOffset = utcOffset;
    }
} 