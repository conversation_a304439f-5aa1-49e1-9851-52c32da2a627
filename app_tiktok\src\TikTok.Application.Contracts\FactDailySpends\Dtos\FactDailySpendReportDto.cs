using System;
using System.Collections.Generic;
using TikTok.DimAdAccounts;
using TikTok.DimBusinessCenters;
using TikTok.DimDates;

namespace TikTok.FactDailySpends.Dtos
{
    public class FactDailySpendReportDto
    {
        public DateTime From { get; set; }
        public DateTime To { get; set; }
        public List<FactDailySpendDto> FactDailySpends { get; set; }
        public List<DimDateDto> DimDates { get; set; }
        public List<DimAdAccountDto> DimAdAccounts { get; set; }
        public List<DimBusinessCenterDto> DimBusinessCenters { get; set; }
    }
}