using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Assets;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.ObjectMapping;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu tài sản (Assets)
    /// </summary>
    public class AssetSyncService : BaseSyncService, IAssetSyncService
    {
        private readonly IAssetRepository _assetRepository;
        private readonly IRepository<RawAssetEntity, Guid> _assetEntityRepository;
        private readonly IObjectMapper _objectMapper;

        public AssetSyncService(
            IServiceProvider serviceProvider,
            IAssetRepository assetRepository,
            IRepository<RawAssetEntity, Guid> assetEntityRepository,
            IObjectMapper objectMapper,
            ILogger<AssetSyncService> logger) : base(serviceProvider, logger)
        {
            _assetRepository = assetRepository;
            _assetEntityRepository = assetEntityRepository;
            _objectMapper = objectMapper;
        }

        /// <summary>
        /// Đồng bộ tài sản theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AssetSyncResult> SyncAssetsAsync(string bcId)
        {
            var result = new AssetSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tài sản cho BC: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // 3. Đồng bộ tất cả loại tài sản
                // var assetTypes = new[] { "ADVERTISER", "CATALOG", "TIKTOK_SHOP", "PIXEL", "LEAD", "TT_ACCOUNT" };
                // TODO: Chỉ đồng bộ ADVERTISER vì các loại tài sản chưa cần thiết
                var assetTypes = new[] { "ADVERTISER" };

                var totalResult = new AssetSyncResult();

                foreach (var assetType in assetTypes)
                {
                    var assetTypeResult = await SyncAssetsByTypeFromApiAsync(tikTokClient, bcId, assetType);
                    totalResult.TotalSynced += assetTypeResult.TotalSynced;
                    totalResult.NewRecords += assetTypeResult.NewRecords;
                    totalResult.UpdatedRecords += assetTypeResult.UpdatedRecords;
                    totalResult.AssetTypeCount++;
                    totalResult.SyncedAssets.AddRange(assetTypeResult.SyncedAssets);
                }

                result = totalResult;
                result.BcCount = 1;

                _logger.LogDebug("Hoàn thành đồng bộ tài sản cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Loại tài sản: {AssetTypes}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.AssetTypeCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ tài sản cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tài sản: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tài sản cho BC: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ tài sản theo loại tài sản và BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="assetType">Loại tài sản</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AssetSyncResult> SyncAssetsByTypeAsync(string bcId, string assetType)
        {
            var result = new AssetSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tài sản loại {AssetType} cho BC: {BcId}", assetType, bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // 3. Đồng bộ tài sản theo loại
                result = await SyncAssetsByTypeFromApiAsync(tikTokClient, bcId, assetType);
                result.BcCount = 1;
                result.AssetTypeCount = 1;

                _logger.LogDebug("Hoàn thành đồng bộ tài sản loại {AssetType} cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    assetType, bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ tài sản loại {AssetType} cho BC: {BcId}", assetType, bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tài sản loại {assetType}: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tài sản loại {AssetType} cho BC: {BcId}", assetType, bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả tài sản cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AssetSyncResult> SyncAllAssetsForAllBcsAsync()
        {
            var result = new AssetSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả tài sản cho tất cả Business Centers");

                // 1. Lấy tất cả Business Centers có ứng dụng active
                var allBusinessApplications = await _businessApplicationCache.GetAllActiveAsync();
                var bcGroups = allBusinessApplications.GroupBy(x => x.BcId);

                if (!bcGroups.Any())
                {
                    result.ErrorMessage = "Không tìm thấy ứng dụng Business active nào";
                    _logger.LogWarning(result.ErrorMessage);
                    return result;
                }

                // 2. Đồng bộ cho từng BC
                foreach (var bcGroup in bcGroups)
                {
                    var bcId = bcGroup.Key;
                    var activeApplication = bcGroup.FirstOrDefault(x => x.IsActive && !string.IsNullOrEmpty(x.AccessToken));

                    if (activeApplication == null)
                    {
                        _logger.LogWarning("Bỏ qua BC {BcId} - không có ứng dụng active hoặc access token", bcId);
                        continue;
                    }

                    try
                    {
                        var bcResult = await SyncAssetsAsync(bcId);
                        result.TotalSynced += bcResult.TotalSynced;
                        result.NewRecords += bcResult.NewRecords;
                        result.UpdatedRecords += bcResult.UpdatedRecords;
                        result.BcCount++;
                        result.AssetTypeCount += bcResult.AssetTypeCount;
                        result.SyncedAssets.AddRange(bcResult.SyncedAssets);
                    }
                    catch (BusinessException bex)
                    {
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        result.ErrorMessage += bex.Message + Environment.NewLine;
                        _logger.LogError(bex, "Lỗi khi đồng bộ tài sản cho BC: {BcId}", bcId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ tài sản cho BC: {BcId}", bcId);
                        // Tiếp tục với BC tiếp theo
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ tất cả tài sản cho tất cả Business Centers. BC: {BcCount}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    result.BcCount, result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tất cả tài sản: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả tài sản cho tất cả Business Centers");
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ tài sản theo loại từ API
        /// </summary>
        /// <param name="client">TikTok client</param>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="assetType">Loại tài sản</param>
        /// <returns>Kết quả đồng bộ</returns>
        private async Task<AssetSyncResult> SyncAssetsByTypeFromApiAsync(TikTokBusinessApiClient client, string bcId, string assetType)
        {
            var result = new AssetSyncResult();
            var page = 1;

            // Lấy danh sách asset hiện tại trong DB cho BC này
            var existingAssets = await _assetRepository.GetByBcIdAsync(bcId, true);
            var existingAssetIds = existingAssets.Select(x => x.AssetId).ToHashSet();
            var apiAssetIds = new HashSet<string>();

            while (true)
            {
                // 1. Gọi API để lấy danh sách tài sản
                var request = new GetAssetsAsAdminRequest
                {
                    BcId = bcId,
                    AssetType = assetType,
                    Page = page,
                    PageSize = TikTokSyncConst.PAGE_SIZE
                };

                var response = await client.BcAssets.GetAssetsAsAdminAsync(request);

                // Kiểm tra response code
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Không thể lấy được danh sách tài sản: {response.Message}");
                }

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break; // Không còn dữ liệu
                }

                // Thu thập Asset IDs từ API
                foreach (var asset in response.Data.List)
                {
                    if (!string.IsNullOrEmpty(asset.AssetId))
                    {
                        apiAssetIds.Add(asset.AssetId);
                    }
                }

                // 2. Xử lý từng tài sản trong trang
                _logger.LogDebug("Đồng bộ tài sản cho trang dữ liệu BC: {BcId}, Trang: {Page}, Tổng: {Total}", bcId, page, response.Data.List.Count);
                var pageResult = await SyncAssetsForPage(bcId, response.Data.List, result, page);
                result.SyncedAssets.AddRange(pageResult);
                _logger.LogDebug("Đã đồng bộ tài sản cho trang dữ liệu BC: {BcId}, Trang: {Page}, Tổng: {Total}", bcId, page, response.Data.List.Count);

                // 3. Kiểm tra có trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            // Xử lý soft delete cho các asset đã bị xóa
            await HandleSoftDeleteForAssets(bcId, existingAssetIds, apiAssetIds, result);

            return result;
        }

        /// <summary>
        /// Xử lý soft delete cho các asset đã bị xóa
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="existingAssetIds">Danh sách Asset ID hiện có trong DB</param>
        /// <param name="apiAssetIds">Danh sách Asset ID từ API</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task HandleSoftDeleteForAssets(string bcId, HashSet<string> existingAssetIds, HashSet<string> apiAssetIds, AssetSyncResult result)
        {
            // Tìm các asset đã bị xóa (có trong DB nhưng không có trong API)
            var removedAssetIds = existingAssetIds.Except(apiAssetIds).ToList();

            if (removedAssetIds.Any())
            {
                using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);
                try
                {
                    var removedAssets = await _assetRepository.GetManyByAssetIdsAsync(removedAssetIds, true);

                    foreach (var removedAsset in removedAssets)
                    {
                        removedAsset.IsRemoved = true;
                        removedAsset.RemovedAt = DateTime.UtcNow;
                    }

                    if (removedAssets.Any())
                    {
                        await _assetEntityRepository.UpdateManyAsync(removedAssets);
                        result.UpdatedRecords += removedAssets.Count;
                        _logger.LogDebug("Đánh dấu xóa {RemovedCount} tài sản cho BC: {BcId}", removedAssets.Count, bcId);
                    }

                    await uow.CompleteAsync();
                }
                catch (Exception ex)
                {
                    await uow.RollbackAsync();
                    _logger.LogError(ex, "Lỗi khi xử lý soft delete tài sản cho BC: {BcId}", bcId);
                    throw;
                }
            }
        }

        /// <summary>
        /// Đồng bộ tài sản cho một trang dữ liệu
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="assets">Danh sách tài sản từ API</param>
        /// <param name="result">Kết quả đồng bộ</param>
        /// <param name="page">Số trang</param>
        /// <returns>Danh sách AssetDto đã đồng bộ</returns>
        private async Task<List<AssetDto>> SyncAssetsForPage(string bcId, List<AdminAssetInfo> assets, AssetSyncResult result, int page)
        {
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false);

            try
            {
                var newAssets = new List<RawAssetEntity>();
                var updatedAssets = new List<RawAssetEntity>();
                var syncedAssetDtos = new List<AssetDto>();

                var adminAssets = MapApiAssetsToEntities(assets, bcId);

                var adminAssetIds = adminAssets.Select(x => x.AssetId).ToList();

                var existingAssets = await _assetRepository.GetManyByAssetIdsAsync(adminAssetIds, true);

                foreach (var adminAsset in adminAssets)
                {
                    if (string.IsNullOrEmpty(adminAsset.AssetId))
                    {
                        continue; // Bỏ qua nếu không có Asset ID
                    }

                    // 1. Tìm tài sản hiện có trong database
                    var existingAsset = existingAssets.FirstOrDefault(x => x.AssetId == adminAsset.AssetId);

                    if (existingAsset == null)
                    {
                        // 2. Tạo mới nếu chưa tồn tại
                        newAssets.Add(adminAsset);
                        result.NewRecords++;

                    }
                    else
                    {
                        // Kiểm tra xem có thay đổi không sử dụng HasIsChanged
                        if (existingAsset.HasIsChanged(adminAsset))
                        {
                            // Cập nhật sử dụng UpdateFrom
                            existingAsset.UpdateFrom(adminAsset);
                            // Đảm bảo cờ xóa được reset
                            existingAsset.IsRemoved = false;
                            existingAsset.RemovedAt = null;
                            updatedAssets.Add(existingAsset);
                            result.UpdatedRecords++;
                        }

                    }

                    result.TotalSynced++;
                }

                if (newAssets.Any())
                {
                    await _assetEntityRepository.InsertManyAsync(newAssets);
                    syncedAssetDtos.AddRange(_objectMapper.Map<RawAssetEntity[], AssetDto[]>(newAssets.ToArray()));

                }

                if (updatedAssets.Any())
                {
                    await _assetEntityRepository.UpdateManyAsync(updatedAssets);
                    syncedAssetDtos.AddRange(_objectMapper.Map<RawAssetEntity[], AssetDto[]>(updatedAssets.ToArray()));
                }

                await uow.CompleteAsync();

                return syncedAssetDtos;
            }
            catch (Exception ex)
            {
                await uow.RollbackAsync();
                _logger.LogError(ex, "Lỗi khi đồng bộ tài sản cho trang dữ liệu BC: {BcId}", bcId);
                throw;
            }
        }

        /// <summary>
        /// Map dữ liệu từ API sang Entity
        /// </summary>
        /// <param name="apiAsset">Dữ liệu từ API</param>
        /// <param name="bcId">Business Center ID</param>
        /// <returns>Entity</returns>
        private RawAssetEntity MapApiAssetToEntity(AdminAssetInfo apiAsset, string bcId)
        {
            return new RawAssetEntity
            {
                AssetId = apiAsset.AssetId ?? string.Empty,
                AssetName = apiAsset.AssetName ?? string.Empty,
                AssetType = MapAssetType(apiAsset.AssetType),
                BcId = bcId,
                AdvertiserAccountType = MapAdAccountType(apiAsset.AdvertiserAccountType),
                AdvertiserRole = MapAdvertiserRole(apiAsset.AdvertiserRole),
                CatalogRole = MapCatalogRole(apiAsset.CatalogRole),
                AdCreationEligible = MapAdCreationEligible(apiAsset.AdCreationEligible),
                StoreRole = MapStoreRole(apiAsset.StoreRole),
                TtAccountRoles = MapTtAccountRoles(apiAsset.TtAccountRoles),
                OwnerBcName = apiAsset.OwnerBcName,
                RelationType = MapRelationType(apiAsset.RelationType),
                RelationStatus = MapRelationStatus(apiAsset.RelationStatus),
                AdvertiserStatus = MapAdAccountStatus(apiAsset.AdvertiserStatus),
                IsRemoved = false,
                RemovedAt = null
            };
        }

        // Map danh sách dữ liệu từ API sang Entity
        private List<RawAssetEntity> MapApiAssetsToEntities(List<AdminAssetInfo> apiAssets, string bcId)
        {
            return apiAssets.Select(x => MapApiAssetToEntity(x, bcId)).ToList();
        }


        /// <summary>
        /// Map Asset Type từ API sang enum
        /// </summary>
        /// <param name="assetType">Asset type từ API</param>
        /// <returns>AssetType enum</returns>
        private AssetType MapAssetType(string? assetType)
        {
            return Enum.TryParse(assetType, true, out AssetType result) ? result : AssetType.ADVERTISER;
        }

        /// <summary>
        /// Map Ad Account Type từ API sang enum
        /// </summary>
        /// <param name="advertiserAccountType">Advertiser account type từ API</param>
        /// <returns>AdAccountType enum</returns>
        private AdAccountType? MapAdAccountType(string? advertiserAccountType)
        {
            return Enum.TryParse(advertiserAccountType, true, out AdAccountType result) ? result : null;
        }

        /// <summary>
        /// Map Advertiser Role từ API sang enum
        /// </summary>
        /// <param name="advertiserRole">Advertiser role từ API</param>
        /// <returns>AdvertiserRole enum</returns>
        private AdvertiserRole? MapAdvertiserRole(string? advertiserRole)
        {
            return Enum.TryParse(advertiserRole, true, out AdvertiserRole result) ? result : null;
        }

        /// <summary>
        /// Map Catalog Role từ API sang enum
        /// </summary>
        /// <param name="catalogRole">Catalog role từ API</param>
        /// <returns>CatalogRole enum</returns>
        private CatalogRole? MapCatalogRole(string? catalogRole)
        {
            return Enum.TryParse(catalogRole, true, out CatalogRole result) ? result : null;
        }

        /// <summary>
        /// Map Ad Creation Eligible từ API sang enum
        /// </summary>
        /// <param name="adCreationEligible">Ad creation eligible từ API</param>
        /// <returns>AdCreationEligible enum</returns>
        private AdCreationEligible? MapAdCreationEligible(string? adCreationEligible)
        {
            return Enum.TryParse(adCreationEligible, true, out AdCreationEligible result) ? result : null;
        }

        /// <summary>
        /// Map Store Role từ API sang enum
        /// </summary>
        /// <param name="storeRole">Store role từ API</param>
        /// <returns>StoreRole enum</returns>
        private StoreRole? MapStoreRole(string? storeRole)
        {
            return Enum.TryParse(storeRole, true, out StoreRole result) ? result : null;
        }

        /// <summary>
        /// Map TT Account Roles từ API sang enum list
        /// </summary>
        /// <param name="ttAccountRoles">TT account roles từ API</param>
        /// <returns>List TtAccountRole enum</returns>
        private List<TtAccountRole> MapTtAccountRoles(List<string>? ttAccountRoles)
        {
            if (ttAccountRoles == null || !ttAccountRoles.Any())
                return new List<TtAccountRole>();

            var result = new List<TtAccountRole>();
            foreach (var role in ttAccountRoles)
            {
                var mappedRole = Enum.TryParse(role, true, out TtAccountRole parsedRole) ? parsedRole : TtAccountRole.POST;
                result.Add(mappedRole);
            }
            return result;
        }

        /// <summary>
        /// Map Relation Type từ API sang enum
        /// </summary>
        /// <param name="relationType">Relation type từ API</param>
        /// <returns>RelationType enum</returns>
        private RelationType? MapRelationType(string? relationType)
        {
            return Enum.TryParse(relationType, true, out RelationType result) ? result : null;
        }

        /// <summary>
        /// Map Relation Status từ API sang enum
        /// </summary>
        /// <param name="relationStatus">Relation status từ API</param>
        /// <returns>RelationStatus enum</returns>
        private RelationStatus? MapRelationStatus(string? relationStatus)
        {
            return Enum.TryParse(relationStatus, true, out RelationStatus result) ? result : null;
        }

        /// <summary>
        /// Map Ad Account Status từ API sang enum
        /// </summary>
        /// <param name="advertiserStatus">Advertiser status từ API</param>
        /// <returns>AdAccountStatus enum</returns>
        private AdAccountStatus? MapAdAccountStatus(string? advertiserStatus)
        {
            return Enum.TryParse(advertiserStatus, true, out AdAccountStatus result) ? result : null;
        }
    }
}