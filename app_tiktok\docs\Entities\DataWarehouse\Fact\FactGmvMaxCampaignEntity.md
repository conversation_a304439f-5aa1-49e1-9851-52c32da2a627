# FactGmvMaxCampaignEntity

## Thông tin bảng dữ liệu

-   **Table**: `Fact_GmvMaxCampaign`
-   **TablePrefix**: `Fact_`
-   **TableName**: `Fact_GmvMaxCampaign`
-   **DbSchema**: `null`

## Mô tả

Fact table cho GMV Max Campaign - Tổng hợp hiệu suất chiến dịch GMV Max. Phục vụ dashboard với các metrics: Revenue, ROI, Orders, Cost, ACOS, TACOS theo yêu cầu GMV Max Dashboard UI.

**Nguồn dữ liệu**: Đ<PERSON><PERSON>c xây dựng từ các bảng dữ liệu thô:

-   `RawGmvMaxCampaignsEntity` - Thông tin cấu hình chiến dịch
-   `RawGmvMaxProductCampaignReportEntity` - Báo cáo hiệu suất Product GMV Max Campaign
-   `RawGmvMaxLiveCampaignReportEntity` - <PERSON><PERSON><PERSON> c<PERSON>o hiệu suất LIVE GMV Max Campaign

**Composite Key**: `StoreId` + `CampaignId` + `AdvertiserId` + `Date` (UTC format yyyy-MM-dd 00:00:00)

## Bảng dữ liệu: Fact_GmvMaxCampaign

| Tên Field                   | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                                                                  | Nguồn dữ liệu thô                                                                                                                                       |
| --------------------------- | ------------ | -------- | ------ | -------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Id                          | Guid         | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                                  | -                                                                                                                                                       |
| DimDateId                   | int          | ✅       | -      | Foreign Key đến Dim_Date                                                               | -                                                                                                                                                       |
| DimBusinessCenterId         | Guid         | ✅       | -      | Foreign Key đến Dim_BusinessCenter                                                     | -                                                                                                                                                       |
| DimAdAccountId              | Guid         | ✅       | -      | Foreign Key đến Dim_AdAccount                                                          | -                                                                                                                                                       |
| DimCampaignId               | Guid         | ✅       | -      | Foreign Key đến Dim_Campaign                                                           | -                                                                                                                                                       |
| DimStoreId                  | Guid         | ✅       | -      | Foreign Key đến Dim_Store (TikTok Shop)                                                | -                                                                                                                                                       |
| CampaignId                  | string       | ✅       | 100    | Business Key - Campaign ID từ TikTok                                                   | RawGmvMaxCampaignsEntity.CampaignId, RawGmvMaxProductCampaignReportEntity.CampaignId, RawGmvMaxLiveCampaignReportEntity.CampaignId                      |
| StoreId                     | string       | ✅       | 100    | Business Key - Store ID từ TikTok Shop                                                 | RawGmvMaxCampaignsEntity.StoreId, RawGmvMaxProductCampaignReportEntity.StoreId, RawGmvMaxLiveCampaignReportEntity.StoreId                               |
| BcId                        | string       | ✅       | 100    | Business Key - Business Center ID từ TikTok                                            | RawGmvMaxCampaignsEntity.BcId, RawGmvMaxProductCampaignReportEntity.BcId, RawGmvMaxLiveCampaignReportEntity.BcId                                        |
| AdvertiserId                | string       | ✅       | 100    | Business Key - Advertiser ID từ TikTok                                                 | RawGmvMaxCampaignsEntity.AdvertiserId, RawGmvMaxProductCampaignReportEntity.AdvertiserId, RawGmvMaxLiveCampaignReportEntity.AdvertiserId                |
| CampaignName                | string       | ❌       | 500    | Tên chiến dịch                                                                         | RawGmvMaxCampaignsEntity.CampaignName, RawGmvMaxProductCampaignReportEntity.CampaignName, RawGmvMaxLiveCampaignReportEntity.CampaignName                |
| ShoppingAdsType             | string       | ✅       | 20     | Loại GMV Max Campaign (PRODUCT, LIVE)                                                  | RawGmvMaxCampaignsEntity.ShoppingAdsType (enum -> string)                                                                                               |
| OperationStatus             | string       | ❌       | 20     | Trạng thái hoạt động (ENABLE, DISABLE)                                                 | RawGmvMaxCampaignsEntity.OperationStatus, RawGmvMaxProductCampaignReportEntity.OperationStatus, RawGmvMaxLiveCampaignReportEntity.OperationStatus       |
| BidType                     | string       | ❌       | 20     | Loại đấu giá (CUSTOM: Target ROI, NO_BID: Maximum delivery)                            | RawGmvMaxProductCampaignReportEntity.BidType, RawGmvMaxLiveCampaignReportEntity.BidType                                                                 |
| RoasBid                     | decimal?     | ❌       | -      | Mục tiêu ROAS                                                                          | RawGmvMaxCampaignsEntity.RoasBid, RawGmvMaxProductCampaignReportEntity.RoasBid, RawGmvMaxLiveCampaignReportEntity.RoasBid                               |
| RoasBidVND                  | decimal?     | ❌       | -      | Mục tiêu ROAS (VND)                                                                    | Calculated                                                                                                                                              |
| RoasBidUSD                  | decimal?     | ❌       | -      | Mục tiêu ROAS (USD)                                                                    | Calculated                                                                                                                                              |
| TargetRoiBudget             | decimal?     | ❌       | -      | Ngân sách Target ROI                                                                   | RawGmvMaxProductCampaignReportEntity.TargetRoiBudget, RawGmvMaxLiveCampaignReportEntity.TargetRoiBudget                                                 |
| TargetRoiBudgetVND          | decimal?     | ❌       | -      | Ngân sách Target ROI (VND)                                                             | Calculated                                                                                                                                              |
| TargetRoiBudgetUSD          | decimal?     | ❌       | -      | Ngân sách Target ROI (USD)                                                             | Calculated                                                                                                                                              |
| MaxDeliveryBudget           | decimal?     | ❌       | -      | Ngân sách Maximum delivery                                                             | RawGmvMaxProductCampaignReportEntity.MaxDeliveryBudget, RawGmvMaxLiveCampaignReportEntity.MaxDeliveryBudget                                             |
| MaxDeliveryBudgetVND        | decimal?     | ❌       | -      | Ngân sách Maximum delivery (VND)                                                       | Calculated                                                                                                                                              |
| MaxDeliveryBudgetUSD        | decimal?     | ❌       | -      | Ngân sách Maximum delivery (USD)                                                       | Calculated                                                                                                                                              |
| Cost                        | decimal      | ✅       | -      | Chi phí quảng cáo (Cost)                                                               | RawGmvMaxProductCampaignReportEntity.Cost, RawGmvMaxLiveCampaignReportEntity.Cost                                                                       |
| CostVND                     | decimal?     | ❌       | -      | Chi phí quảng cáo (Cost) (VND)                                                         | Calculated                                                                                                                                              |
| CostUSD                     | decimal?     | ❌       | -      | Chi phí quảng cáo (Cost) (USD)                                                         | Calculated                                                                                                                                              |
| NetCost                     | decimal?     | ❌       | -      | Chi phí thực tế (Net Cost)                                                             | RawGmvMaxProductCampaignReportEntity.NetCost, RawGmvMaxLiveCampaignReportEntity.NetCost                                                                 |
| NetCostVND                  | decimal?     | ❌       | -      | Chi phí thực tế (Net Cost) (VND)                                                       | Calculated                                                                                                                                              |
| NetCostUSD                  | decimal?     | ❌       | -      | Chi phí thực tế (Net Cost) (USD)                                                       | Calculated                                                                                                                                              |
| Orders                      | int          | ✅       | -      | Số lượng đơn hàng (Orders)                                                             | RawGmvMaxProductCampaignReportEntity.Orders, RawGmvMaxLiveCampaignReportEntity.Orders                                                                   |
| CostPerOrder                | decimal?     | ❌       | -      | Chi phí trung bình mỗi đơn hàng (CPO)                                                  | RawGmvMaxProductCampaignReportEntity.CostPerOrder, RawGmvMaxLiveCampaignReportEntity.CostPerOrder                                                       |
| CostPerOrderVND             | decimal?     | ❌       | -      | Chi phí trung bình mỗi đơn hàng (CPO) (VND)                                            | Calculated                                                                                                                                              |
| CostPerOrderUSD             | decimal?     | ❌       | -      | Chi phí trung bình mỗi đơn hàng (CPO) (USD)                                            | Calculated                                                                                                                                              |
| GrossRevenue                | decimal      | ✅       | -      | Tổng doanh thu (Gross Revenue) - Bao gồm cả doanh thu từ quảng cáo và doanh thu hữu cơ | RawGmvMaxProductCampaignReportEntity.GrossRevenue, RawGmvMaxLiveCampaignReportEntity.GrossRevenue                                                       |
| GrossRevenueVND             | decimal?     | ❌       | -      | Tổng doanh thu (Gross Revenue) (VND)                                                   | Calculated                                                                                                                                              |
| GrossRevenueUSD             | decimal?     | ❌       | -      | Tổng doanh thu (Gross Revenue) (USD)                                                   | Calculated                                                                                                                                              |
| ROAS                        | decimal?     | ❌       | -      | ROAS (Return on Ad Spend) - Hiệu quả quảng cáo                                         | RawGmvMaxProductCampaignReportEntity.ROI, RawGmvMaxLiveCampaignReportEntity.ROI                                                                         |
| TACOS                       | decimal?     | ❌       | -      | TACOS (True ACOS) - Hiệu quả quảng cáo so với tổng doanh thu                           | Calculated: Cost / GrossRevenue                                                                                                                         |
| TtAccountName               | string       | ❌       | 200    | Tên TikTok account (chỉ có cho LIVE campaigns)                                         | RawGmvMaxLiveCampaignReportEntity.TtAccountName                                                                                                         |
| TtAccountProfileImageUrl    | string       | ❌       | 1000   | URL hình đại diện TikTok account (chỉ có cho LIVE campaigns)                           | RawGmvMaxLiveCampaignReportEntity.TtAccountProfileImageUrl                                                                                              |
| IdentityId                  | string       | ❌       | 100    | Identity ID (TikTok account ID) (chỉ có cho LIVE campaigns)                            | RawGmvMaxLiveCampaignReportEntity.IdentityId                                                                                                            |
| LiveViews                   | long?        | ❌       | -      | Số lượt xem LIVE (chỉ có cho LIVE campaigns)                                           | RawGmvMaxLiveCampaignReportEntity.LiveViews                                                                                                             |
| CostPerLiveView             | decimal?     | ❌       | -      | Chi phí trung bình mỗi lượt xem LIVE (chỉ có cho LIVE campaigns)                       | RawGmvMaxLiveCampaignReportEntity.CostPerLiveView                                                                                                       |
| CostPerLiveViewVND          | decimal?     | ❌       | -      | Chi phí trung bình mỗi lượt xem LIVE (VND) (chỉ có cho LIVE campaigns)                 | Calculated                                                                                                                                              |
| CostPerLiveViewUSD          | decimal?     | ❌       | -      | Chi phí trung bình mỗi lượt xem LIVE (USD) (chỉ có cho LIVE campaigns)                 | Calculated                                                                                                                                              |
| TenSecondLiveViews          | long?        | ❌       | -      | Số lượt xem LIVE ít nhất 10 giây (chỉ có cho LIVE campaigns)                           | RawGmvMaxLiveCampaignReportEntity.TenSecondLiveViews                                                                                                    |
| CostPerTenSecondLiveView    | decimal?     | ❌       | -      | Chi phí trung bình mỗi lượt xem LIVE 10 giây (chỉ có cho LIVE campaigns)               | RawGmvMaxLiveCampaignReportEntity.CostPerTenSecondLiveView                                                                                              |
| CostPerTenSecondLiveViewVND | decimal?     | ❌       | -      | Chi phí trung bình mỗi lượt xem LIVE 10 giây (VND) (chỉ có cho LIVE campaigns)         | Calculated                                                                                                                                              |
| CostPerTenSecondLiveViewUSD | decimal?     | ❌       | -      | Chi phí trung bình mỗi lượt xem LIVE 10 giây (USD) (chỉ có cho LIVE campaigns)         | Calculated                                                                                                                                              |
| LiveFollows                 | int?         | ❌       | -      | Số lượt follow profile trong quá trình LIVE (chỉ có cho LIVE campaigns)                | RawGmvMaxLiveCampaignReportEntity.LiveFollows                                                                                                           |
| ScheduleType                | string       | ❌       | 50     | Loại lịch trình (Continuously)                                                         | RawGmvMaxProductCampaignReportEntity.ScheduleType, RawGmvMaxLiveCampaignReportEntity.ScheduleType                                                       |
| ScheduleStartTime           | DateTime?    | ❌       | -      | Thời gian bắt đầu chiến dịch (UTC)                                                     | RawGmvMaxCampaignsEntity.ScheduleStartTime, RawGmvMaxProductCampaignReportEntity.ScheduleStartTime, RawGmvMaxLiveCampaignReportEntity.ScheduleStartTime |
| ScheduleEndTime             | DateTime?    | ❌       | -      | Thời gian kết thúc chiến dịch (UTC)                                                    | RawGmvMaxCampaignsEntity.ScheduleEndTime, RawGmvMaxProductCampaignReportEntity.ScheduleEndTime, RawGmvMaxLiveCampaignReportEntity.ScheduleEndTime       |
| Currency                    | string       | ✅       | 10     | Tiền tệ                                                                                | RawGmvMaxProductCampaignReportEntity.Currency, RawGmvMaxLiveCampaignReportEntity.Currency                                                               |
| Date                        | DateTime     | ✅       | -      | Ngày báo cáo UTC format yyyy-MM-dd 00:00:00 (theo ngày)                                | RawGmvMaxProductCampaignReportEntity.Date, RawGmvMaxLiveCampaignReportEntity.Date (converted to date only)                                              |
| CreationTime                | DateTime     | ✅       | -      | Thời gian tạo bản ghi (AuditedEntity)                                                  | -                                                                                                                                                       |
| CreatorId                   | Guid?        | ❌       | -      | ID người tạo (AuditedEntity)                                                           | -                                                                                                                                                       |
| LastModificationTime        | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (AuditedEntity)                                                 | -                                                                                                                                                       |
| LastModifierId              | Guid?        | ❌       | -      | ID người sửa đổi cuối (AuditedEntity)                                                  | -                                                                                                                                                       |

## Cấu trúc dữ liệu

### Foreign Keys

-   **DimDateId**: Liên kết với Dim_Date để phân tích theo thời gian
-   **DimBusinessCenterId**: Liên kết với Dim_BusinessCenter để phân tích theo tổ chức
-   **DimAdAccountId**: Liên kết với Dim_AdAccount để phân tích theo tài khoản
-   **DimCampaignId**: Liên kết với Dim_Campaign để phân tích theo chiến dịch
-   **DimStoreId**: Liên kết với Dim_Store để phân tích theo TikTok Shop

### Business Keys

-   **CampaignId**: ID chiến dịch từ TikTok API
-   **StoreId**: ID cửa hàng từ TikTok Shop
-   **BcId**: ID Business Center từ TikTok
-   **AdvertiserId**: ID Advertiser từ TikTok

### Metrics chính cho Dashboard

#### 1. Dashboard Overview

-   **Total Revenue**: SUM(GrossRevenue)
-   **Average ROAS**: AVG(ROAS)
-   **No. Campaign**: COUNT(DISTINCT CampaignId)
-   **No. Orders**: SUM(Orders)
-   **Spent Ads Estimate**: SUM(Cost)
-   **Total Ads Spent**: SUM(NetCost)

#### 2. Report Charts (7 ngày gần nhất)

-   **Cost vs last 7 days**: So sánh Cost theo ngày
-   **Orders (SKU) vs last 7 days**: So sánh Orders theo ngày
-   **Cost per order vs last 7 days**: So sánh CostPerOrder theo ngày
-   **Gross revenue vs last 7 days**: So sánh GrossRevenue theo ngày
-   **ROAS vs last 7 days**: So sánh ROAS theo ngày

#### 3. Overview Analytics

-   **SHOP**: StoreName từ Dim_Store
-   **CAMPAIGN**: CampaignName từ Dim_Campaign
-   **BID TYPE**: BidType
-   **SCHEDULE TIME**: ScheduleStartTime/ScheduleEndTime từ Dim_Campaign
-   **STATUS**: OperationStatus
-   **ORDERS**: Orders
-   **TOTAL ADS SPENT**: Cost
-   **REVENUE**: GrossRevenue
-   **ADS REVENUE**: AdsRevenue
-   **ROAS**: ROAS

#### 4. Advanced Filter

-   **Orders**: Lọc theo Orders với operators (=, ≥, ≤, Between)
-   **ROAS**: Lọc theo ROAS với operators (=, ≥, ≤, Between)
-   **Cost**: Lọc theo Cost với operators (=, ≥, ≤, Between)
-   **Gross Revenue**: Lọc theo GrossRevenue với operators (=, ≥, ≤, Between)

#### 5. Views theo vai trò

##### View Nhà bán hàng

-   Tập trung vào **ROAS (Return On Ad Spend)**
-   Hiển thị: ROAS, Orders, GrossRevenue, Cost

##### View Quản lý Ads

-   Hiển thị thêm **TACOS (True ACOS)**
-   **TACOS** = `Cost / GrossRevenue`

### Công thức tính toán

#### ROAS (Return on Ad Spend) - Hiệu quả quảng cáo

```
ROAS = GrossRevenue / Cost
```

**Nguồn**: RawGmvMaxProductCampaignReportEntity.ROI, RawGmvMaxLiveCampaignReportEntity.ROI

#### TACOS (True ACOS) - Hiệu quả quảng cáo so với tổng doanh thu

```
TACOS = Cost / GrossRevenue
```

**Tính toán**: Được tính trong ETL process

#### CPO (Cost Per Order)

```
CPO = Cost / Orders
```

**Nguồn**: RawGmvMaxProductCampaignReportEntity.CostPerOrder, RawGmvMaxLiveCampaignReportEntity.CostPerOrder

#### LIVE Campaign Specific Metrics

```
CostPerLiveView = Cost / LiveViews
CostPerTenSecondLiveView = Cost / TenSecondLiveViews
```

**Nguồn**: RawGmvMaxLiveCampaignReportEntity.CostPerLiveView, RawGmvMaxLiveCampaignReportEntity.CostPerTenSecondLiveView

## Navigation Properties

-   **Date**: Liên kết với Dim_DateEntity
-   **BusinessCenter**: Liên kết với Dim_BusinessCenterEntity
-   **AdAccount**: Liên kết với Dim_AdAccountEntity
-   **Campaign**: Liên kết với Dim_CampaignEntity
-   **Store**: Liên kết với Dim_StoreEntity

## ETL Process và Data Mapping

### Nguồn dữ liệu chính

1. **RawGmvMaxCampaignsEntity**: Thông tin cấu hình chiến dịch

    - CampaignId, StoreId, BcId, AdvertiserId
    - CampaignName, ShoppingAdsType, OperationStatus
    - ScheduleStartTime, ScheduleEndTime, RoasBid

2. **RawGmvMaxProductCampaignReportEntity**: Báo cáo hiệu suất Product campaigns

    - Cost, NetCost, Orders, CostPerOrder, GrossRevenue, ROI
    - BidType, TargetRoiBudget, MaxDeliveryBudget, RoasBid
    - ScheduleType, ScheduleStartTime, ScheduleEndTime

3. **RawGmvMaxLiveCampaignReportEntity**: Báo cáo hiệu suất LIVE campaigns
    - Cost, NetCost, Orders, CostPerOrder, GrossRevenue, ROI
    - BidType, TargetRoiBudget, MaxDeliveryBudget, RoasBid
    - TtAccountName, TtAccountProfileImageUrl, IdentityId
    - LiveViews, CostPerLiveView, TenSecondLiveViews, CostPerTenSecondLiveView, LiveFollows

### Logic ETL

1. **Data Aggregation**: Tổng hợp dữ liệu theo composite key (StoreId + CampaignId + AdvertiserId + Date)
2. **Campaign Type Detection**: Xác định loại campaign (PRODUCT/LIVE) từ ShoppingAdsType
3. **Field Mapping**: Map các trường từ raw data sang fact table
4. **Calculated Fields**: Tính toán ROAS, ACOS, TACOS
5. **Date Normalization**: Convert datetime sang date only (yyyy-MM-dd 00:00:00)

### Lưu ý quan trọng về TACOS

-   **GrossRevenue đã bao gồm organic revenue**: TACOS = ACOS = Cost / GrossRevenue
-   **Không cần thay đổi**: Vì GrossRevenue từ raw data đã là tổng doanh thu (ads + organic)

## Mục đích sử dụng

-   Dashboard tổng quan GMV Max Campaign
-   Báo cáo hiệu suất theo thời gian (ngày/tuần/tháng)
-   Phân tích ROI và ROAS
-   So sánh hiệu suất giữa các chiến dịch
-   Quản lý ngân sách và chi phí
-   Tối ưu hóa chiến dịch GMV Max
-   Hỗ trợ ra quyết định kinh doanh

## Quan hệ với các entity khác

-   **Dim_DateEntity**: Many-to-One, phân tích theo thời gian
-   **Dim_BusinessCenterEntity**: Many-to-One, phân tích theo tổ chức
-   **Dim_AdAccountEntity**: Many-to-One, phân tích theo tài khoản
-   **Dim_CampaignEntity**: Many-to-One, phân tích theo chiến dịch
-   **Dim_StoreEntity**: Many-to-One, phân tích theo TikTok Shop
-   **FactGmvMaxProductEntity**: One-to-Many, drill-down theo sản phẩm

## Use Cases thực tế

### Dashboard Overview

```sql
-- Tổng quan hiệu suất GMV Max
SELECT
    SUM(GrossRevenue) as TotalRevenue,
    AVG(ROAS) as AverageROAS,
    COUNT(DISTINCT CampaignId) as CampaignCount,
    SUM(Orders) as TotalOrders,
    SUM(Cost) as TotalCost
FROM Fact_GmvMaxCampaign
WHERE DimDateId >= @Last7Days
```

### Campaign Performance Analysis

```sql
-- Phân tích hiệu suất chiến dịch
SELECT
    c.CampaignName,
    s.StoreName,
    f.Orders,
    f.Cost,
    f.GrossRevenue,
    f.ROAS,
    f.TACOS
FROM Fact_GmvMaxCampaign f
JOIN Dim_Campaign c ON f.DimCampaignId = c.DimCampaignId
JOIN Dim_Store s ON f.DimStoreId = s.DimStoreId
WHERE f.DimDateId >= @StartDate AND f.DimDateId <= @EndDate
ORDER BY f.ROAS DESC
```

### Advanced Filtering

```sql
-- Lọc theo ROAS và Orders
SELECT *
FROM Fact_GmvMaxCampaign
WHERE ROAS >= @MinROAS
  AND ROAS <= @MaxROAS
  AND Orders >= @MinOrders
  AND Orders <= @MaxOrders
```

### Time Series Analysis

```sql
-- Phân tích xu hướng theo thời gian
SELECT
    d.Date,
    SUM(f.Cost) as DailyCost,
    SUM(f.Orders) as DailyOrders,
    AVG(f.ROAS) as AverageROAS
FROM Fact_GmvMaxCampaign f
JOIN Dim_Date d ON f.DimDateId = d.DimDateId
WHERE d.Date >= @Last7Days
GROUP BY d.Date
ORDER BY d.Date
```
