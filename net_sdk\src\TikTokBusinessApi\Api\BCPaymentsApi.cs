/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;
using TikTokBusinessApi.Serialization;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API BC Payments operations
    /// </summary>
    public class BCPaymentsApi : IBCPaymentsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<BCPaymentsApi>? _logger;
        private readonly JsonSerializerOptions _jsonOptions;


        /// <summary>
        /// Initializes a new instance of the BCPaymentsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public BCPaymentsApi(IApiClient apiClient, ILogger<BCPaymentsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
            _jsonOptions = JsonSerializerOptionsExtensions.CreateDefault();

        }

        /// <summary>
        /// Process a payment (recharge money to or deduct money from an ad account in a Business Center, or increase/decrease the credit balance of a Business Center)
        /// </summary>
        /// <param name="request">Payment processing request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing transaction information</returns>
        //public async Task<ProcessPaymentResponse> ProcessPaymentAsync(
        //    ProcessPaymentRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.BcId))
        //        throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.TransferType))
        //        throw new ArgumentException("Transfer type cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Processing payment for BC {BcId}", request.BcId);

        //    return await _apiClient.CallApiAsync<ProcessPaymentResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCPaymentsEndpoints.ProcessPayment}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get the balance and budget of ad accounts in the Business Center
        /// </summary>
        /// <param name="request">Advertiser balance request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing advertiser account balances</returns>
        public async Task<GetAdvertiserBalanceResponse> GetAdvertiserBalanceAsync(
            GetAdvertiserBalanceRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting advertiser balance for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            if (request.Fields != null && request.Fields.Count > 0)
                queryParams["fields"] = string.Join(",", request.Fields);
            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering, _jsonOptions);
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetAdvertiserBalanceResponse>(
                $"{BCPaymentsEndpoints.GetAdvertiserBalance}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the balance of a Business Center
        /// </summary>
        /// <param name="request">BC balance request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing BC balance information</returns>
        public async Task<GetBcBalanceResponse> GetBcBalanceAsync(
            GetBcBalanceRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting BC balance for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            return await _apiClient.CallApiAsync<GetBcBalanceResponse>(
                BCPaymentsEndpoints.GetBCBalance,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the transaction records of a BC or ad accounts within the Business Center
        /// </summary>
        /// <param name="request">Account transactions request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing transaction records</returns>
        public async Task<GetBcAccountTransactionResponse> GetBcAccountTransactionAsync(
            GetBcAccountTransactionRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting account transactions for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            if (!string.IsNullOrWhiteSpace(request.TransactionLevel))
                queryParams["transaction_level"] = request.TransactionLevel;
            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering, _jsonOptions);
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetBcAccountTransactionResponse>(
                BCPaymentsEndpoints.GetAccountTransactions,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the transaction records of ad accounts in the Business Center
        /// </summary>
        /// <param name="request">Advertiser transactions request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing advertiser transaction records</returns>
        public async Task<GetAdvertiserTransactionResponse> GetAdvertiserTransactionAsync(
            GetAdvertiserTransactionRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting advertiser transactions for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering, _jsonOptions);
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetAdvertiserTransactionResponse>(
                BCPaymentsEndpoints.GetAdvertiserTransactions,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the transaction records of a Business Center
        /// </summary>
        /// <param name="request">BC transactions request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing BC transaction records</returns>
        public async Task<GetBcTransactionResponse> GetBcTransactionAsync(
            GetBcTransactionRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting BC transactions for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering, _jsonOptions);
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetBcTransactionResponse>(
                BCPaymentsEndpoints.GetBCTransactions,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the budget change history of an ad account within a Business Center
        /// </summary>
        /// <param name="request">Budget change history request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing budget change history</returns>
        public async Task<GetBudgetChangelogResponse> GetBudgetChangelogAsync(
            GetBudgetChangelogRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));
            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting budget change history for BC {BcId}, Advertiser {AdvertiserId}", request.BcId, request.AdvertiserId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["advertiser_id"] = request.AdvertiserId
            };

            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering, _jsonOptions);
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetBudgetChangelogResponse>(
                BCPaymentsEndpoints.GetBudgetChangeHistory,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the cost records at the ad account and Business Center levels
        /// </summary>
        /// <param name="request">Cost records request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing cost records</returns>
        public async Task<GetCostRecordsResponse> GetCostRecordsAsync(
            GetCostRecordsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting cost records for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering, _jsonOptions);
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetCostRecordsResponse>(
                BCPaymentsEndpoints.GetCostRecords,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
