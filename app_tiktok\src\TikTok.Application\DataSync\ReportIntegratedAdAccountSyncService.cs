using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu báo cáo tích hợp AdAccount
    /// </summary>
    public class ReportIntegratedAdAccountSyncService : BaseSyncService, IReportIntegratedAdAccountSyncService
    {
        private readonly IRawReportIntegratedAdAccountRepository _reportIntegratedAdAccountRepository;
        private readonly IRepository<RawReportIntegratedAdAccountEntity, Guid> _reportIntegratedAdAccountEntityRepository;
        private readonly IAdAccountRepository _adAccountRepository;

        public ReportIntegratedAdAccountSyncService(
            IServiceProvider serviceProvider,
            IRawReportIntegratedAdAccountRepository reportIntegratedAdAccountRepository,
            IRepository<RawReportIntegratedAdAccountEntity, Guid> reportIntegratedAdAccountEntityRepository,
            ILogger<ReportIntegratedAdAccountSyncService> logger,
            IAdAccountRepository adAccountRepository) : base(serviceProvider, logger)
        {
            _reportIntegratedAdAccountRepository = reportIntegratedAdAccountRepository;
            _reportIntegratedAdAccountEntityRepository = reportIntegratedAdAccountEntityRepository;
            _adAccountRepository = adAccountRepository;
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdAccount cho tất cả Business Centers
        /// </summary>
        /// <param name="startDate">Ngày bắt đầu (yyyy-MM-dd)</param>
        /// <param name="endDate">Ngày kết thúc (yyyy-MM-dd)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedAdAccountSyncResult> SyncAllReportIntegratedAdAccountForAllBcsAsync()
        {
            var result = new ReportIntegratedAdAccountSyncResult
            {
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp AdAccount cho tất cả BC");

                var allBcs = await _businessApplicationCache.GetAllActiveAsync();
                var bcIds = allBcs.Select(x => x.BcId).Distinct().ToList();

                var totalResult = new ReportIntegratedAdAccountSyncResult
                {
                };

                foreach (var bcId in bcIds)
                {
                    var bcResult = await SyncReportIntegratedAdAccountAsync(bcId);
                    totalResult.TotalSynced += bcResult.TotalSynced;
                    totalResult.NewRecords += bcResult.NewRecords;
                    totalResult.UpdatedRecords += bcResult.UpdatedRecords;
                    totalResult.DayCount += bcResult.DayCount;
                    totalResult.AdAccountCount += bcResult.AdAccountCount;
                    totalResult.BcCount++;
                }

                result = totalResult;

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo tích hợp AdAccount cho tất cả BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}, AdAccount: {AdAccountCount}, Bản ghi: {RecordCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount, result.AdAccountCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp AdAccount cho tất cả BC");
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo tích hợp AdAccount: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp AdAccount cho tất cả BC");
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdAccount theo BC ID và khoảng thời gian
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate">Ngày bắt đầu (yyyy-MM-dd)</param>
        /// <param name="endDate">Ngày kết thúc (yyyy-MM-dd)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedAdAccountSyncResult> SyncReportIntegratedAdAccountAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var result = new ReportIntegratedAdAccountSyncResult
            {
                BcCount = 1
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp AdAccount cho BC: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                string bcTimezone = DateTimeService.UTC_TIMEZONE;

                var bc = await _businessCenterCache.GetByBcIdAsync(bcId);
                if (bc != null && !string.IsNullOrEmpty(bc.Timezone))
                {
                    bcTimezone = bc.Timezone;
                }

                (DateTime startDate, DateTime endDate) rangeDateFiltering;
                if (startDate.HasValue && endDate.HasValue)
                {
                    rangeDateFiltering = (startDate.Value, endDate.Value);
                }
                else
                {
                    rangeDateFiltering = await GetRangeDateFiltering(bcId, bcTimezone);
                }

                var adAccountIds = await _adAccountRepository.GetByBcIdAsync(bcId);

                
                var currentDate = rangeDateFiltering.startDate.Date;
                var end = rangeDateFiltering.endDate.Date;

                while (currentDate <= end)
                {
                    _logger.LogDebug("Đồng bộ dữ liệu báo cáo cho ngày: {Date}", currentDate.ToString("yyyy-MM-dd"));

                    // Một lần chỉ xử lý tối đa 5 tài khoản quảng cáo
                    int pageSize = 5;
                    var totalPages = (int)Math.Ceiling((double)adAccountIds.Count / pageSize);
                    for (int page = 0; page < totalPages; page++)
                    {
                        var adAccountIdsChunk = adAccountIds.Skip(page * pageSize).Take(pageSize).ToList();
                        var apiResponse = await GetSyncReportIntegratedAdAccountFromApiAsync(tikTokClient, bcId, adAccountIdsChunk, currentDate, currentDate.AddDays(29));
                        if (apiResponse != null && apiResponse.Any())
                        {
                            await ProcessReportDataAsync(bcId, adAccountIdsChunk, bcTimezone, apiResponse, result);
                        }
                        else
                        {
                            _logger.LogDebug("Không có dữ liệu báo cáo cho BC: {BcId}, Ngày: {Date}", bcId, currentDate.ToString("yyyy-MM-dd"));
                        }
                    }

                    // Chuyển sang ngày tiếp theo
                    currentDate = currentDate.AddDays(29);
                }

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo tích hợp AdAccount cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, AdAccount: {AdAccountCount}, Bản ghi: {RecordCount}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.AdAccountCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp AdAccount cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo tích hợp AdAccount: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp AdAccount cho BC: {BcId}", bcId);
            }

            return result;
        }

        private async Task<(DateTime startDate, DateTime endDate)> GetRangeDateFiltering(string bcId, string timezone)
        {
            // Lấy ngày hiện tại theo timezone của BC
            DateTime currentDateInTimezone = _dateTimeService.GetDateNow(timezone);
            DateTime endDate = currentDateInTimezone;

            // Lấy dữ liệu báo cáo tích hợp ad account mới nhất (theo giờ)
            var latestReport = await _reportIntegratedAdAccountRepository.GetLatestByBcIdAsync(bcId);

            DateTime startDate;
            if (latestReport == null)
            {
                // Nếu chưa có dữ liệu trong DB thì lấy khoảng 1 tuần từ ngày hiện tại
                startDate = currentDateInTimezone.AddDays(-LAST_SYNC_DAYS);
                _logger.LogDebug("Chưa có dữ liệu báo cáo trong DB cho BC: {BcId}. Lấy dữ liệu 1 tuần từ {StartDate} đến {EndDate}",
                    bcId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            }
            else
            {
                // Nếu có dữ liệu trong DB thì lấy từ ngày trong DB đến ngày hiện tại
                // Convert từ UTC (trong DB) sang timezone của BC để so sánh
                var latestReportDateInTimezone = _dateTimeService.ConvertFromUtc(latestReport.Date, timezone).Date;
                startDate = latestReportDateInTimezone;
                _logger.LogDebug("Có dữ liệu báo cáo trong DB cho BC: {BcId}. Lấy từ {StartDate} đến {EndDate}",
                    bcId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            }

            return (startDate, endDate);
        }

        /// <summary>
        /// Lấy dữ liệu báo cáo tích hợp AdAccount từ TikTok API
        /// Lưu ý: Khi lấy báo cáo theo giờ, startDate và endDate phải cùng một ngày
        /// </summary>
        private async Task<List<ReportDataItem>> GetSyncReportIntegratedAdAccountFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, List<string> adAccountIds, DateTime startDate, DateTime endDate)
        {
            // Validation: Đảm bảo startDate và endDate cùng ngày cho báo cáo theo giờ
            if (startDate.Date != endDate.Date)
            {
                throw new BusinessException(TikTokApiCodes.InvalidParameters.ToString(), $"Khi lấy báo cáo theo giờ, startDate và endDate phải cùng một ngày. StartDate: {startDate:yyyy-MM-dd}, EndDate: {endDate:yyyy-MM-dd}");
            }

            var records = new List<ReportDataItem>();
            var page = 1;
            const int pageSize = PAGE_SIZE_SYNC_REPORT;

            while (true)
            {
                var request = new SynchronousReportRequest
                {
                    AdvertiserIds = adAccountIds,
                    ServiceType = "AUCTION",
                    ReportType = "BASIC",
                    DataLevel = "AUCTION_ADVERTISER",
                    Dimensions = new List<string> { "advertiser_id", "stat_time_day" },
                    Metrics = new List<string>
                    {
                        "spend", "billed_cost", "impressions", "clicks", "ctr", "cpm", "cpc", "reach", "frequency",
                        "advertiser_id", "advertiser_name", "currency", "timezone"
                    },
                    StartDate = startDate.ToString("yyyy-MM-dd"),
                    EndDate = endDate.ToString("yyyy-MM-dd"),
                    Page = page,
                    PageSize = pageSize
                };

                var response = await tikTokClient.Reporting.GetSynchronousReportAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu báo cáo tích hợp AdAccount: {response.Message}");
                }

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break;
                }

                records.AddRange(response.Data.List);

                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            return records;
        }

        /// <summary>
        /// Xử lý dữ liệu báo cáo từ API
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="adAccountIds">Danh sách ID tài khoản quảng cáo</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportDataList">Danh sách dữ liệu báo cáo</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task ProcessReportDataAsync(string bcId, List<string> adAccountIds, string bcTimezone, List<ReportDataItem> reportDataList, ReportIntegratedAdAccountSyncResult result)
        {
            // sử dụng UnitOfWork để đảm bảo tính toàn vẹn dữ liệu cho mỗi 200 bản ghi
            if (reportDataList == null || !reportDataList.Any())
            {
                _logger.LogDebug("Không có dữ liệu báo cáo để xử lý cho {Total} AdAccount", reportDataList.Count);
                return;
            }

            var pageSize = PAGE_SIZE_HANDLE_SAVE_TO_DATABASE;
            var totalPages = (int)Math.Ceiling((double)reportDataList.Count / pageSize);
            _logger.LogDebug("Bắt đầu xử lý {TotalRecords} bản ghi báo cáo cho {TotalPages} trang", reportDataList.Count, totalPages);

            for (int page = 0; page < totalPages; page++)
            {
                var pageData = reportDataList.Skip(page * pageSize).Take(pageSize).ToList();
                if (pageData.Any())
                {
                    try
                    {
                        await ProcessPageDataAsync(bcId, bcTimezone, pageData, result);
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý dữ liệu báo cáo cho {Total} AdAccount, Trang: {Page}", pageData.Count, page + 1);
                        result.ErrorMessage += ex.Message + Environment.NewLine;
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        continue; // Bỏ qua lỗi và tiếp tục với trang tiếp theo
                    }
                }
            }
        }

        private async Task ProcessPageDataAsync(string bcId, string bcTimezone, List<ReportDataItem> pageData, ReportIntegratedAdAccountSyncResult result)
        {
            // use unit of work
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var mappedEntities = await MapListReportDataToEntitiesAsync(bcId, bcTimezone, pageData);

                var existingEntities = await _reportIntegratedAdAccountRepository.GetByBcIdAndDateRangeAsync(bcId, mappedEntities.Min(x => x.Date), mappedEntities.Max(x => x.Date));

                var insertedEntities = new List<RawReportIntegratedAdAccountEntity>();
                var updatedEntities = new List<RawReportIntegratedAdAccountEntity>();

                foreach (var mappedEntity in mappedEntities)
                {
                    // So sánh theo AdvertiserId và Date với độ chính xác đến giờ
                    var currentEntity = existingEntities.FirstOrDefault(x => x.AdvertiserId == mappedEntity.AdvertiserId && x.Date == mappedEntity.Date);
                    if (currentEntity == null)
                    {
                        insertedEntities.Add(mappedEntity);
                        result.NewRecords++;
                        result.TotalSynced++;
                    }
                    else
                    {
                        // Cập nhật nếu có thay đổi
                        if (currentEntity.HasChanged(mappedEntity))
                        {
                            UpdateEntityFromNewData(currentEntity, mappedEntity);
                            updatedEntities.Add(currentEntity);
                            result.UpdatedRecords++;
                            result.TotalSynced++;
                        }
                    }
                }

                // Thêm các bản ghi mới vào kho dữ liệu
                if (insertedEntities.Any())
                {
                    await _reportIntegratedAdAccountEntityRepository.InsertManyAsync(insertedEntities);
                }
                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _reportIntegratedAdAccountEntityRepository.UpdateManyAsync(updatedEntities);
                }

                result.DayCount += pageData.Count;
                result.AdAccountCount += pageData.Select(x => x.Dimensions?.GetValueOrDefault("advertiser_id")?.ToString()).Distinct().Count();

                await uow.CompleteAsync();
            }
        }

        private async Task<List<RawReportIntegratedAdAccountEntity>> MapListReportDataToEntitiesAsync(string bcId, string bcTimezone, List<ReportDataItem> reportDataList)
        {
            var entities = new List<RawReportIntegratedAdAccountEntity>();
            foreach (var reportData in reportDataList)
            {
                var entity = await MapReportDataToEntityAsync(bcId, bcTimezone, reportData);
                if (entity != null)
                {
                    entities.Add(entity);
                }
            }
            return entities;
        }

        /// <summary>
        /// Map dữ liệu báo cáo từ API sang entity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportData">Dữ liệu báo cáo</param>
        /// <returns>RawReportIntegratedAdAccountEntity</returns>
        private async Task<RawReportIntegratedAdAccountEntity?> MapReportDataToEntityAsync(string bcId, string bcTimezone, ReportDataItem reportData)
        {
            var dateTimeStr = reportData.Dimensions?.GetValueOrDefault("stat_time_day")?.ToString();

            if (string.IsNullOrEmpty(dateTimeStr) || !DateTime.TryParse(dateTimeStr, out var reportDateTime))
            {
                _logger.LogWarning("Ngày giờ báo cáo không hợp lệ: {DateTime} cho BC: {BcId}", dateTimeStr, bcId);
                return null;
            }
            var adTimezone = GetStringValue(reportData.Metrics, "timezone") ?? bcTimezone;
            // Convert datetime từ timezone của BC sang UTC
            var reportDateTimeUtc = _dateTimeService.ConvertToUtc(reportDateTime, adTimezone);

            var advertiserId = reportData.Dimensions?.GetValueOrDefault("advertiser_id")?.ToString();
            if (string.IsNullOrEmpty(advertiserId))
            {
                _logger.LogWarning("Advertiser ID không hợp lệ cho BC: {BcId}", bcId);
                return null;
            }

            var entity = new RawReportIntegratedAdAccountEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdvertiserId = advertiserId,
                Date = reportDateTimeUtc  // Lưu datetime UTC với giờ
            };

            if (reportData.Metrics != null)
            {
                // Map các metrics từ API response
                entity.AdvertiserId = GetStringValue(reportData.Metrics, "advertiser_id") ?? string.Empty;
                entity.Spend = GetDecimalValue(reportData.Metrics, "spend");
                entity.BilledCost = GetDecimalValue(reportData.Metrics, "billed_cost");
                entity.Impressions = GetLongValue(reportData.Metrics, "impressions");
                entity.Clicks = GetLongValue(reportData.Metrics, "clicks");
                entity.Ctr = GetDecimalValue(reportData.Metrics, "ctr");
                entity.Cpm = GetDecimalValue(reportData.Metrics, "cpm");
                entity.Cpc = GetDecimalValue(reportData.Metrics, "cpc");
                entity.Reach = GetLongValue(reportData.Metrics, "reach");
                entity.Frequency = GetDecimalValue(reportData.Metrics, "frequency");
                entity.Currency = GetStringValue(reportData.Metrics, "currency") ?? "USD";
                entity.AdvertiserName = GetStringValue(reportData.Metrics, "advertiser_name") ?? string.Empty;
                entity.CashSpend = GetDecimalValue(reportData.Metrics, "cash_spend");
                entity.VoucherSpend = GetDecimalValue(reportData.Metrics, "voucher_spend");
            }

            return entity;
        }

        /// <summary>
        /// Cập nhật entity từ dữ liệu mới
        /// </summary>
        /// <param name="existingEntity">Entity hiện tại</param>
        /// <param name="newEntity">Entity mới</param>
        private void UpdateEntityFromNewData(RawReportIntegratedAdAccountEntity existingEntity, RawReportIntegratedAdAccountEntity newEntity)
        {
            existingEntity.AdvertiserName = newEntity.AdvertiserName;
            existingEntity.Spend = newEntity.Spend;
            existingEntity.BilledCost = newEntity.BilledCost;
            existingEntity.Impressions = newEntity.Impressions;
            existingEntity.Clicks = newEntity.Clicks;
            existingEntity.Ctr = newEntity.Ctr;
            existingEntity.Cpm = newEntity.Cpm;
            existingEntity.Cpc = newEntity.Cpc;
            existingEntity.Reach = newEntity.Reach;
            existingEntity.Frequency = newEntity.Frequency;
            existingEntity.Currency = newEntity.Currency;
        }
    }
}