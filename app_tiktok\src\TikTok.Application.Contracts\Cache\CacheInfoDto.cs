using System;
using System.Collections.Generic;

namespace TikTok.Cache
{
    /// <summary>
    /// DTO chứa thông tin chi tiết về cache
    /// </summary>
    public class CacheInfoDto
    {
        /// <summary>
        /// Tên cache
        /// </summary>
        public string CacheName { get; set; } = string.Empty;

        /// <summary>
        /// Số lượng cache keys đang hoạt động
        /// </summary>
        public int ActiveKeysCount { get; set; }

        /// <summary>
        /// Danh sách cache keys
        /// </summary>
        public List<string> CacheKeys { get; set; } = new List<string>();

        /// <summary>
        /// Thời gian cache được tạo
        /// </summary>
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// Thời gian cache được truy cập lần cuối
        /// </summary>
        public DateTime? LastAccessedAt { get; set; }

        /// <summary>
        /// Thời gian cache được chỉnh sửa lần cuối
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }

        /// <summary>
        /// Trạng thái cache (Active, Error, etc.)
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Mô tả thêm về cache
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }
}
