/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.IO;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API BC Assets operations
    /// </summary>
    public interface IBCAssetsApi
    {
        /// <summary>
        /// Create an ad account in the Business Center
        /// </summary>
        /// <param name="request">Create advertiser request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created advertiser ID</returns>
        //Task<CreateAdvertiserResponse> CreateAdvertiserAsync(
        //    CreateAdvertiserRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update an ad account in the Business Center
        /// </summary>
        /// <param name="request">Update advertiser request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing update results</returns>
        //Task<UpdateAdvertiserResponse> UpdateAdvertiserAsync(
        //    UpdateAdvertiserRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Disable an ad account in the Business Center
        /// </summary>
        /// <param name="request">Disable advertiser request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing disable results</returns>
        //Task<DisableAdvertiserResponse> DisableAdvertiserAsync(
        //    DisableAdvertiserRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Upload a business certificate
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="imageFile">Certificate image file stream</param>
        /// <param name="fileName">File name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the uploaded image ID</returns>
        //Task<UploadBusinessCertificateResponse> UploadBusinessCertificateAsync(
        //    string bcId,
        //    Stream imageFile,
        //    string fileName,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Check UnionPay verification requirement for a business license
        /// </summary>
        /// <param name="request">Check UnionPay verification request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating if UnionPay verification is required</returns>
        //Task<CheckUnionPayVerificationResponse> CheckUnionPayVerificationAsync(
        //    CheckUnionPayVerificationRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Submit UnionPay verification for an ad account
        /// </summary>
        /// <param name="request">Submit UnionPay verification request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing submission status</returns>
        //Task<SubmitUnionPayVerificationResponse> SubmitUnionPayVerificationAsync(
        //    SubmitUnionPayVerificationRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get UnionPay verification status for an ad account
        /// </summary>
        /// <param name="request">Get UnionPay status request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing verification status</returns>
        Task<GetUnionPayStatusResponse> GetUnionPayStatusAsync(
            GetUnionPayStatusRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get assets that you or a particular user has access to
        /// </summary>
        /// <param name="request">Get assets request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of assets</returns>
        Task<TikTokApiResponse<GetAssetsResponse>> GetAssetsAsync(
            GetAssetsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get additional information about assets in a Business Center (Admin only)
        /// </summary>
        /// <param name="request">Get assets as admin request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing detailed asset information</returns>
        Task<TikTokApiResponse<GetAssetsAsAdminResponse>> GetAssetsAsAdminAsync(
            GetAssetsAsAdminRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Share assets with other Business Centers
        /// </summary>
        /// <param name="request">Share assets request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing sharing results</returns>
        //Task<ShareAssetsResponse> ShareAssetsAsync(
        //    ShareAssetsRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Unshare assets from other Business Centers
        /// </summary>
        /// <param name="request">Unshare assets request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing unsharing results</returns>
        //Task<UnshareAssetsResponse> UnshareAssetsAsync(
        //    UnshareAssetsRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get shared assets
        /// </summary>
        /// <param name="request">Get shared assets request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing shared assets</returns>
        Task<GetSharedAssetsResponse> GetSharedAssetsAsync(
            GetSharedAssetsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete assets from a Business Center
        /// </summary>
        /// <param name="request">Delete assets request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deletion results</returns>
        //Task<DeleteAssetsResponse> DeleteAssetsAsync(
        //    DeleteAssetsRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get asset sharing history
        /// </summary>
        /// <param name="request">Get asset sharing history request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing sharing history</returns>
        Task<GetAssetSharingHistoryResponse> GetAssetSharingHistoryAsync(
            GetAssetSharingHistoryRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create an Asset Group
        /// </summary>
        /// <param name="request">Create asset group request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created asset group ID</returns>
        //Task<CreateAssetGroupResponse> CreateAssetGroupAsync(
        //    CreateAssetGroupRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get Asset Groups
        /// </summary>
        /// <param name="request">List asset groups request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing asset groups</returns>
        Task<ListAssetGroupsResponse> GetAssetGroupsAsync(
            ListAssetGroupsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get details of an Asset Group
        /// </summary>
        /// <param name="request">Get asset group request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing asset group details</returns>
        Task<GetAssetGroupResponse> GetAssetGroupAsync(
            GetAssetGroupRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Update an Asset Group
        /// </summary>
        /// <param name="request">Update asset group request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<UpdateAssetGroupResponse> UpdateAssetGroupAsync(
        //    UpdateAssetGroupRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// List all Asset Groups
        /// </summary>
        /// <param name="request">List asset groups request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing asset groups</returns>
        Task<ListAssetGroupsResponse> ListAssetGroupsAsync(
            ListAssetGroupsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete Asset Groups
        /// </summary>
        /// <param name="request">Delete asset groups request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<DeleteAssetGroupsResponse> DeleteAssetGroupsAsync(
        //    DeleteAssetGroupsRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get advertiser qualification information
        /// </summary>
        /// <param name="request">Get qualifications request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing qualification information</returns>
        Task<GetQualificationsResponse> GetAdvertiserQualificationAsync(
            GetQualificationsRequest request,
            CancellationToken cancellationToken = default);
    }
}
