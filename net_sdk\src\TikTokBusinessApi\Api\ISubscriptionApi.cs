/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Subscription operations
    /// </summary>
    public interface ISubscriptionApi
    {
        // /// <summary>
        // /// Create a subscription to ad account suspension status, leads, ad group, ad, 
        // /// TikTok Creator Marketplace (TCM) Spark Ads authorization, or TTO campaign video linking
        // /// </summary>
        // /// <param name="body">Request body containing subscription details</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing the subscription ID</returns>
        // Task<SubscriptionCreateResponse> CreateSubscriptionAsync(
        //     SubscriptionCreateBody body,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the subscription details of a developer app
        /// </summary>
        /// <param name="request">Request parameters for getting subscription details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing subscription information</returns>
        Task<SubscriptionGetResponse> GetSubscriptionsAsync(
            SubscriptionGetRequest request,
            CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Cancel a subscription
        // /// </summary>
        // /// <param name="body">Request body containing subscription cancellation details</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing the cancelled subscription ID</returns>
        // Task<SubscriptionCancelResponse> CancelSubscriptionAsync(
        //     SubscriptionCancelBody body,
        //     CancellationToken cancellationToken = default);
    }
}
