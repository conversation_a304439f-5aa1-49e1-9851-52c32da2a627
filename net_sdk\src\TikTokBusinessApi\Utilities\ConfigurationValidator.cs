/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Core;

namespace TikTokBusinessApi.Utilities
{
    /// <summary>
    /// Configuration validation utility to help developers catch issues early
    /// </summary>
    public class ConfigurationValidator
    {
        private readonly ILogger<ConfigurationValidator>? _logger;
        private readonly List<IValidationRule> _rules;

        /// <summary>
        /// Initializes a new instance of the ConfigurationValidator class
        /// </summary>
        /// <param name="logger">Logger instance (optional)</param>
        public ConfigurationValidator(ILogger<ConfigurationValidator>? logger = null)
        {
            _logger = logger;
            _rules = new List<IValidationRule>
            {
                new BaseUrlValidationRule(),
                new AppIdValidationRule(),
                new AccessTokenValidationRule(),
                new TimeoutValidationRule(),
                new RetryConfigurationValidationRule(),
                new RateLimitValidationRule(),
                new CacheConfigurationValidationRule()
            };
        }

        /// <summary>
        /// Add a custom validation rule
        /// </summary>
        /// <param name="rule">Validation rule to add</param>
        public void AddRule(IValidationRule rule)
        {
            if (rule == null)
                throw new ArgumentNullException(nameof(rule));

            _rules.Add(rule);
            _logger?.LogDebug("Added custom validation rule: {RuleName}", rule.GetType().Name);
        }

        /// <summary>
        /// Validate TikTok Business API configuration
        /// </summary>
        /// <param name="configuration">Configuration to validate</param>
        /// <returns>Validation result</returns>
        public ValidationResult ValidateConfiguration(Configuration configuration)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            var result = new ValidationResult();
            var context = new ValidationContext(configuration);

            _logger?.LogDebug("Starting configuration validation with {RuleCount} rules", _rules.Count);

            foreach (var rule in _rules)
            {
                try
                {
                    var ruleResult = rule.Validate(context);
                    result.AddResult(ruleResult);

                    _logger?.LogDebug("Rule {RuleName} completed with {IssueCount} issues", 
                        rule.GetType().Name, ruleResult.Issues.Count);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error executing validation rule: {RuleName}", rule.GetType().Name);
                    result.AddIssue(ValidationSeverity.Error, $"Validation rule {rule.GetType().Name} failed: {ex.Message}");
                }
            }

            _logger?.LogInformation("Configuration validation completed. Issues: {ErrorCount} errors, {WarningCount} warnings, {InfoCount} info", 
                result.ErrorCount, result.WarningCount, result.InfoCount);

            return result;
        }

        /// <summary>
        /// Validate authentication configuration
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="accessToken">Access token (optional)</param>
        /// <returns>Validation result</returns>
        public ValidationResult ValidateAuthentication(string appId, string? accessToken = null)
        {
            var result = new ValidationResult();

            // Validate App ID
            if (string.IsNullOrWhiteSpace(appId))
            {
                result.AddIssue(ValidationSeverity.Error, "App ID is required");
            }
            else if (!IsValidAppId(appId))
            {
                result.AddIssue(ValidationSeverity.Warning, "App ID format appears invalid");
            }

            // Validate Access Token if provided
            if (!string.IsNullOrWhiteSpace(accessToken))
            {
                if (!IsValidAccessToken(accessToken))
                {
                    result.AddIssue(ValidationSeverity.Warning, "Access token format appears invalid");
                }
            }
            else
            {
                result.AddIssue(ValidationSeverity.Info, "Access token not provided - will need to be set before making API calls");
            }

            return result;
        }

        /// <summary>
        /// Validate HTTP client configuration
        /// </summary>
        /// <param name="httpClient">HTTP client to validate</param>
        /// <returns>Validation result</returns>
        public ValidationResult ValidateHttpClient(HttpClient httpClient)
        {
            if (httpClient == null)
                throw new ArgumentNullException(nameof(httpClient));

            var result = new ValidationResult();

            // Check timeout
            if (httpClient.Timeout == TimeSpan.Zero)
            {
                result.AddIssue(ValidationSeverity.Warning, "HTTP client timeout is set to zero (infinite)");
            }
            else if (httpClient.Timeout < TimeSpan.FromSeconds(30))
            {
                result.AddIssue(ValidationSeverity.Warning, "HTTP client timeout is very short, may cause timeouts for slow API calls");
            }
            else if (httpClient.Timeout > TimeSpan.FromMinutes(10))
            {
                result.AddIssue(ValidationSeverity.Info, "HTTP client timeout is very long");
            }

            // Check base address
            if (httpClient.BaseAddress == null)
            {
                result.AddIssue(ValidationSeverity.Info, "HTTP client base address is not set");
            }
            else if (!httpClient.BaseAddress.Scheme.Equals("https", StringComparison.OrdinalIgnoreCase))
            {
                result.AddIssue(ValidationSeverity.Error, "HTTP client base address should use HTTPS");
            }

            // Check default headers
            if (!httpClient.DefaultRequestHeaders.Contains("User-Agent"))
            {
                result.AddIssue(ValidationSeverity.Info, "HTTP client does not have a User-Agent header set");
            }

            return result;
        }

        private bool IsValidAppId(string appId)
        {
            // TikTok App IDs are typically numeric strings
            return Regex.IsMatch(appId, @"^\d+$") && appId.Length >= 10;
        }

        private bool IsValidAccessToken(string accessToken)
        {
            // Basic validation for access token format
            return !string.IsNullOrWhiteSpace(accessToken) && 
                   accessToken.Length >= 20 && 
                   !accessToken.Contains(" ");
        }
    }

    /// <summary>
    /// Interface for validation rules
    /// </summary>
    public interface IValidationRule
    {
        /// <summary>
        /// Validate configuration
        /// </summary>
        /// <param name="context">Validation context</param>
        /// <returns>Validation result</returns>
        ValidationResult Validate(ValidationContext context);
    }

    /// <summary>
    /// Validation context containing configuration and additional data
    /// </summary>
    public class ValidationContext
    {
        /// <summary>
        /// Configuration being validated
        /// </summary>
        public Configuration Configuration { get; }

        /// <summary>
        /// Additional context data
        /// </summary>
        public Dictionary<string, object> Data { get; } = new();

        /// <summary>
        /// Initializes a new instance of the ValidationContext class
        /// </summary>
        /// <param name="configuration">Configuration to validate</param>
        public ValidationContext(Configuration configuration)
        {
            Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }
    }

    /// <summary>
    /// Validation result containing issues and summary
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// List of validation issues
        /// </summary>
        public List<ValidationIssue> Issues { get; } = new();

        /// <summary>
        /// Whether the validation passed (no errors)
        /// </summary>
        public bool IsValid => !Issues.Any(i => i.Severity == ValidationSeverity.Error);

        /// <summary>
        /// Number of error issues
        /// </summary>
        public int ErrorCount => Issues.Count(i => i.Severity == ValidationSeverity.Error);

        /// <summary>
        /// Number of warning issues
        /// </summary>
        public int WarningCount => Issues.Count(i => i.Severity == ValidationSeverity.Warning);

        /// <summary>
        /// Number of info issues
        /// </summary>
        public int InfoCount => Issues.Count(i => i.Severity == ValidationSeverity.Info);

        /// <summary>
        /// Add a validation issue
        /// </summary>
        /// <param name="severity">Issue severity</param>
        /// <param name="message">Issue message</param>
        /// <param name="property">Property name (optional)</param>
        public void AddIssue(ValidationSeverity severity, string message, string? property = null)
        {
            Issues.Add(new ValidationIssue
            {
                Severity = severity,
                Message = message,
                Property = property
            });
        }

        /// <summary>
        /// Add results from another validation result
        /// </summary>
        /// <param name="other">Other validation result</param>
        public void AddResult(ValidationResult other)
        {
            if (other != null)
            {
                Issues.AddRange(other.Issues);
            }
        }

        /// <summary>
        /// Get formatted summary of validation results
        /// </summary>
        /// <returns>Formatted summary string</returns>
        public string GetSummary()
        {
            if (IsValid && Issues.Count == 0)
                return "Configuration validation passed with no issues.";

            var summary = $"Configuration validation completed with {Issues.Count} issue(s): ";
            summary += $"{ErrorCount} error(s), {WarningCount} warning(s), {InfoCount} info.";

            if (!IsValid)
                summary += " Configuration is invalid due to errors.";

            return summary;
        }
    }

    /// <summary>
    /// Individual validation issue
    /// </summary>
    public class ValidationIssue
    {
        /// <summary>
        /// Severity of the issue
        /// </summary>
        public ValidationSeverity Severity { get; set; }

        /// <summary>
        /// Issue message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Property name related to the issue (optional)
        /// </summary>
        public string? Property { get; set; }

        /// <summary>
        /// Timestamp when the issue was detected
        /// </summary>
        public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.UtcNow;
    }

    /// <summary>
    /// Validation issue severity levels
    /// </summary>
    public enum ValidationSeverity
    {
        /// <summary>
        /// Informational message
        /// </summary>
        Info,

        /// <summary>
        /// Warning that should be addressed
        /// </summary>
        Warning,

        /// <summary>
        /// Error that prevents proper operation
        /// </summary>
        Error
    }

    // Built-in validation rules

    /// <summary>
    /// Validates base URL configuration
    /// </summary>
    internal class BaseUrlValidationRule : IValidationRule
    {
        public ValidationResult Validate(ValidationContext context)
        {
            var result = new ValidationResult();
            var config = context.Configuration;

            if (string.IsNullOrWhiteSpace(config.BaseUrl))
            {
                result.AddIssue(ValidationSeverity.Error, "Base URL is required", nameof(config.BaseUrl));
            }
            else
            {
                if (!Uri.TryCreate(config.BaseUrl, UriKind.Absolute, out var uri))
                {
                    result.AddIssue(ValidationSeverity.Error, "Base URL is not a valid URI", nameof(config.BaseUrl));
                }
                else
                {
                    if (!uri.Scheme.Equals("https", StringComparison.OrdinalIgnoreCase))
                    {
                        result.AddIssue(ValidationSeverity.Error, "Base URL must use HTTPS", nameof(config.BaseUrl));
                    }

                    if (!uri.Host.Contains("tiktokv.com") && !uri.Host.Contains("bytedance.com"))
                    {
                        result.AddIssue(ValidationSeverity.Warning, "Base URL does not appear to be a TikTok API endpoint", nameof(config.BaseUrl));
                    }
                }
            }

            return result;
        }
    }

    /// <summary>
    /// Validates App ID configuration
    /// </summary>
    internal class AppIdValidationRule : IValidationRule
    {
        public ValidationResult Validate(ValidationContext context)
        {
            var result = new ValidationResult();
            var config = context.Configuration;

            if (string.IsNullOrWhiteSpace(config.AppId))
            {
                result.AddIssue(ValidationSeverity.Error, "App ID is required", nameof(config.AppId));
            }
            else
            {
                if (!Regex.IsMatch(config.AppId, @"^\d+$"))
                {
                    result.AddIssue(ValidationSeverity.Warning, "App ID should be numeric", nameof(config.AppId));
                }

                if (config.AppId.Length < 10)
                {
                    result.AddIssue(ValidationSeverity.Warning, "App ID appears to be too short", nameof(config.AppId));
                }
            }

            return result;
        }
    }

    /// <summary>
    /// Validates access token configuration
    /// </summary>
    internal class AccessTokenValidationRule : IValidationRule
    {
        public ValidationResult Validate(ValidationContext context)
        {
            var result = new ValidationResult();
            var config = context.Configuration;

            if (string.IsNullOrWhiteSpace(config.AccessToken))
            {
                result.AddIssue(ValidationSeverity.Info, "Access token not configured - will need to be provided per request", nameof(config.AccessToken));
            }
            else
            {
                if (config.AccessToken.Length < 20)
                {
                    result.AddIssue(ValidationSeverity.Warning, "Access token appears to be too short", nameof(config.AccessToken));
                }

                if (config.AccessToken.Contains(" "))
                {
                    result.AddIssue(ValidationSeverity.Error, "Access token should not contain spaces", nameof(config.AccessToken));
                }
            }

            return result;
        }
    }

    /// <summary>
    /// Validates timeout configuration
    /// </summary>
    internal class TimeoutValidationRule : IValidationRule
    {
        public ValidationResult Validate(ValidationContext context)
        {
            var result = new ValidationResult();
            var config = context.Configuration;

            if (config.Timeout <= TimeSpan.Zero)
            {
                result.AddIssue(ValidationSeverity.Warning, "Timeout is set to zero or negative value", nameof(config.Timeout));
            }
            else if (config.Timeout < TimeSpan.FromSeconds(30))
            {
                result.AddIssue(ValidationSeverity.Warning, "Timeout is very short, may cause issues with slow API calls", nameof(config.Timeout));
            }
            else if (config.Timeout > TimeSpan.FromMinutes(10))
            {
                result.AddIssue(ValidationSeverity.Info, "Timeout is very long", nameof(config.Timeout));
            }

            return result;
        }
    }

    /// <summary>
    /// Validates retry configuration
    /// </summary>
    internal class RetryConfigurationValidationRule : IValidationRule
    {
        public ValidationResult Validate(ValidationContext context)
        {
            var result = new ValidationResult();
            var config = context.Configuration;

            if (config.MaxRetries < 0)
            {
                result.AddIssue(ValidationSeverity.Error, "Max retries cannot be negative", nameof(config.MaxRetries));
            }
            else if (config.MaxRetries > 10)
            {
                result.AddIssue(ValidationSeverity.Warning, "Max retries is very high, may cause long delays", nameof(config.MaxRetries));
            }

            if (config.RetryDelay <= TimeSpan.Zero)
            {
                result.AddIssue(ValidationSeverity.Warning, "Retry delay should be positive", nameof(config.RetryDelay));
            }
            else if (config.RetryDelay > TimeSpan.FromMinutes(1))
            {
                result.AddIssue(ValidationSeverity.Warning, "Retry delay is very long", nameof(config.RetryDelay));
            }

            return result;
        }
    }

    /// <summary>
    /// Validates rate limiting configuration
    /// </summary>
    internal class RateLimitValidationRule : IValidationRule
    {
        public ValidationResult Validate(ValidationContext context)
        {
            var result = new ValidationResult();
            var config = context.Configuration;

            if (config.RateLimitPerSecond <= 0)
            {
                result.AddIssue(ValidationSeverity.Info, "Rate limiting is disabled", nameof(config.RateLimitPerSecond));
            }
            else if (config.RateLimitPerSecond > 100)
            {
                result.AddIssue(ValidationSeverity.Warning, "Rate limit is very high, may exceed API limits", nameof(config.RateLimitPerSecond));
            }

            return result;
        }
    }

    /// <summary>
    /// Validates cache configuration
    /// </summary>
    internal class CacheConfigurationValidationRule : IValidationRule
    {
        public ValidationResult Validate(ValidationContext context)
        {
            var result = new ValidationResult();
            var config = context.Configuration;

            if (config.EnableCaching)
            {
                if (config.CacheTtl <= TimeSpan.Zero)
                {
                    result.AddIssue(ValidationSeverity.Warning, "Cache TTL should be positive when caching is enabled", nameof(config.CacheTtl));
                }
                else if (config.CacheTtl > TimeSpan.FromHours(24))
                {
                    result.AddIssue(ValidationSeverity.Info, "Cache TTL is very long, data may become stale", nameof(config.CacheTtl));
                }
            }

            return result;
        }
    }
}
