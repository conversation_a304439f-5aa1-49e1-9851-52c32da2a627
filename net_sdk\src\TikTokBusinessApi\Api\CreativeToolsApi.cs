/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Creative Tools operations
    /// </summary>
    public class CreativeToolsApi : ICreativeToolsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<CreativeToolsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the CreativeToolsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public CreativeToolsApi(IApiClient apiClient, ILogger<CreativeToolsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get the status of an asynchronous creative tool task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="taskId">Task ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task status</returns>
        public async Task<Response<TaskStatusResponse>> GetTaskStatusAsync(
            string accessToken,
            string advertiserId,
            string taskId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrWhiteSpace(taskId))
                throw new ArgumentException("Task ID cannot be null or empty", nameof(taskId));

            _logger?.LogInformation("Getting task status for advertiser {AdvertiserId}, task {TaskId}", advertiserId, taskId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["task_id"] = taskId
            };

            return await _apiClient.CallApiAsync<Response<TaskStatusResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.GetTaskStatus}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Edit an image
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Image edit request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing edited image information</returns>
        //public async Task<Response<ImageEditResponse>> EditImageAsync(
        //    string accessToken,
        //    ImageEditRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Editing image {ImageId} for advertiser {AdvertiserId}", request.ImageId, request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<ImageEditResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.EditImage}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Preview an ad
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Ad preview request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing preview information</returns>
        //public async Task<Response<AdPreviewResponse>> PreviewAdAsync(
        //    string accessToken,
        //    AdPreviewRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Creating ad preview for advertiser {AdvertiserId}", request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken,
        //        ["Content-Type"] = "application/json"
        //    };

        //    return await _apiClient.CallApiAsync<Response<AdPreviewResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.PreviewAd}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Create a Smart Video Soundtrack task (Deprecated)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Video soundtrack creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task ID</returns>
        //public async Task<Response<TaskCreateResponse>> CreateVideoSoundtrackTaskAsync(
        //    string accessToken,
        //    VideoSoundtrackCreateRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Creating video soundtrack task for advertiser {AdvertiserId}", request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken,
        //        ["Content-Type"] = "application/json"
        //    };

        //    return await _apiClient.CallApiAsync<Response<TaskCreateResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.CreateVideoSoundtrackTask}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Create a Quick Optimization task (Deprecated)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Quick optimization creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task ID</returns>
        //public async Task<Response<TaskCreateResponse>> CreateQuickOptimizationTaskAsync(
        //    string accessToken,
        //    QuickOptimizationCreateRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Creating quick optimization task for advertiser {AdvertiserId}", request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken,
        //        ["Content-Type"] = "application/json"
        //    };

        //    return await _apiClient.CallApiAsync<Response<TaskCreateResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.CreateQuickOptimizationTask}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Create a Smart Video task (Deprecated)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Smart video creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task ID</returns>
        //public async Task<Response<TaskCreateResponse>> CreateSmartVideoTaskAsync(
        //    string accessToken,
        //    SmartVideoCreateRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Creating smart video task for advertiser {AdvertiserId}", request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken,
        //        ["Content-Type"] = "application/json"
        //    };

        //    return await _apiClient.CallApiAsync<Response<TaskCreateResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.CreateSmartVideoTask}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Delete creative assets
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Creative asset deletion request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deletion results</returns>
        //public async Task<Response<CreativeAssetDeleteResponse>> DeleteCreativeAssetsAsync(
        //    string accessToken,
        //    CreativeAssetDeleteRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Deleting creative assets for advertiser {AdvertiserId}", request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken,
        //        ["Content-Type"] = "application/json"
        //    };

        //    return await _apiClient.CallApiAsync<Response<CreativeAssetDeleteResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.DeleteCreativeAssets}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get Smart Text recommendations
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Smart text generation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing text recommendations</returns>
        public async Task<Response<SmartTextGenerateResponse>> GetSmartTextRecommendationsAsync(
            string accessToken,
            SmartTextGenerateRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogInformation("Getting Smart Text recommendations for advertiser {AdvertiserId}", request.AdvertiserId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<SmartTextGenerateResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.GetSmartTextRecommendations}",
                HttpMethod.Post,
                queryParams: null,
                body: request,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Send Smart Text feedback
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Smart text feedback request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<object>> SendSmartTextFeedbackAsync(
        //    string accessToken,
        //    SmartTextFeedbackRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Sending Smart Text feedback for advertiser {AdvertiserId}", request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.SendSmartTextFeedback}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get recommended CTAs
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="newVersion">Whether to use the new version</param>
        /// <param name="assetType">Asset type</param>
        /// <param name="contentType">Content type</param>
        /// <param name="objectiveType">Objective type</param>
        /// <param name="promotionType">Promotion type</param>
        /// <param name="language">Language</param>
        /// <param name="appId">App ID</param>
        /// <param name="placements">Placements</param>
        /// <param name="regionCodes">Region codes</param>
        /// <param name="optimizationGoal">Optimization goal</param>
        /// <param name="adTexts">Ad texts</param>
        /// <param name="landingPageUrl">Landing page URL</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing CTA recommendations</returns>
        public async Task<Response<CtaRecommendResponse>> GetRecommendedCTAsAsync(
            string accessToken,
            string advertiserId,
            bool? newVersion = null,
            string? assetType = null,
            string? contentType = null,
            string? objectiveType = null,
            string? promotionType = null,
            string? language = null,
            string? appId = null,
            List<string>? placements = null,
            List<string>? regionCodes = null,
            string? optimizationGoal = null,
            List<string>? adTexts = null,
            string? landingPageUrl = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogInformation("Getting recommended CTAs for advertiser {AdvertiserId}", advertiserId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId
            };

            if (newVersion.HasValue)
                queryParams["new_version"] = newVersion.Value.ToString().ToLower();
            if (!string.IsNullOrEmpty(assetType))
                queryParams["asset_type"] = assetType;
            if (!string.IsNullOrEmpty(contentType))
                queryParams["content_type"] = contentType;
            if (!string.IsNullOrEmpty(objectiveType))
                queryParams["objective_type"] = objectiveType;
            if (!string.IsNullOrEmpty(promotionType))
                queryParams["promotion_type"] = promotionType;
            if (!string.IsNullOrEmpty(language))
                queryParams["language"] = language;
            if (!string.IsNullOrEmpty(appId))
                queryParams["app_id"] = appId;
            if (placements != null && placements.Count > 0)
                queryParams["placements"] = string.Join(",", placements);
            if (regionCodes != null && regionCodes.Count > 0)
                queryParams["region_codes"] = string.Join(",", regionCodes);
            if (!string.IsNullOrEmpty(optimizationGoal))
                queryParams["optimization_goal"] = optimizationGoal;
            if (adTexts != null && adTexts.Count > 0)
                queryParams["ad_texts"] = string.Join(",", adTexts);
            if (!string.IsNullOrEmpty(landingPageUrl))
                queryParams["landing_page_url"] = landingPageUrl;

            return await _apiClient.CallApiAsync<Response<CtaRecommendResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.GetRecommendedCTAs}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Create a Smart Fix task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Smart Fix creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task results</returns>
        //public async Task<Response<SmartFixCreateResponse>> CreateSmartFixTaskAsync(
        //    string accessToken,
        //    SmartFixCreateRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Creating Smart Fix task for advertiser {AdvertiserId}", request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<SmartFixCreateResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.CreateSmartFixTask}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get the results of a Smart Fix task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="taskId">Fix task ID</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing Smart Fix results</returns>
        public async Task<Response<SmartFixResultsResponse>> GetSmartFixTaskResultsAsync(
            string accessToken,
            string taskId,
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(taskId))
                throw new ArgumentException("Task ID cannot be null or empty", nameof(taskId));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogInformation("Getting Smart Fix task results for task {TaskId}", taskId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["task_id"] = taskId,
                ["advertiser_id"] = advertiserId
            };

            return await _apiClient.CallApiAsync<Response<SmartFixResultsResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.GetSmartFixTaskResults}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get Creative Fatigue Detection results
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Creative fatigue request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing fatigue detection results</returns>
        public async Task<Response<CreativeFatigueResponse>> GetCreativeFatigueResultsAsync(
            string accessToken,
            CreativeFatigueRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogInformation("Getting Creative Fatigue results for advertiser {AdvertiserId}, ad {AdId}", request.AdvertiserId, request.AdId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken,
                ["Content-Type"] = "application/json"
            };

            return await _apiClient.CallApiAsync<Response<CreativeFatigueResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeToolsEndpoints.GetCreativeFatigueResults}",
                HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
