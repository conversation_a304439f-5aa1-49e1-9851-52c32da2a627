/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using TikTokBusinessApi.Core;

namespace TikTokBusinessApi.Models
{
    #region Process Payment (Transfer) Models

    /// <summary>
    /// Request model for processing a payment (transfer) via /bc/transfer/
    /// </summary>
    public class ProcessPaymentRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Transfer level. Enum values: ADVERTISER (default), BC
        /// </summary>
        [JsonPropertyName("transfer_level")]
        public string? TransferLevel { get; set; }

        /// <summary>
        /// Ad account ID. Required when transfer_level is ADVERTISER or not specified
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Transfer type. Enum values: RECHAR<PERSON>, REFUND
        /// </summary>
        [JsonPropertyName("transfer_type")]
        public string TransferType { get; set; } = string.Empty;

        /// <summary>
        /// Cash or credit amount to process, rounded to two decimal places
        /// </summary>
        [JsonPropertyName("cash_amount")]
        public decimal? CashAmount { get; set; }

        /// <summary>
        /// Voucher amount to process, rounded to two decimal places
        /// </summary>
        [JsonPropertyName("grant_amount")]
        public decimal? GrantAmount { get; set; }

        /// <summary>
        /// Request ID for idempotency. Valid only when transfer_level is BC
        /// </summary>
        [JsonPropertyName("request_id")]
        public string? RequestId { get; set; }
    }

    /// <summary>
    /// Transaction information in process payment response
    /// </summary>
    public class TransactionInfo
    {
        /// <summary>
        /// Transaction ID of the transfer
        /// </summary>
        [JsonPropertyName("transaction_id")]
        public string? TransactionId { get; set; }

        /// <summary>
        /// Transaction type. Enum values: CASH, GRANT, CREDIT
        /// </summary>
        [JsonPropertyName("transaction_type")]
        public string? TransactionType { get; set; }
    }

    /// <summary>
    /// Response data for process payment
    /// </summary>
    public class ProcessPaymentData
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string? BcId { get; set; }

        /// <summary>
        /// Ad account ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Details of the transfers
        /// </summary>
        [JsonPropertyName("transaction_infos")]
        public List<TransactionInfo>? TransactionInfos { get; set; }
    }

    /// <summary>
    /// Response for process payment
    /// </summary>
    public class ProcessPaymentResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public ProcessPaymentData? Data { get; set; }
    }

    #endregion

    #region Get Advertiser Balance Models

    /// <summary>
    /// Filtering conditions for advertiser balance request
    /// </summary>
    public class AdvertiserBalanceFiltering
    {
        /// <summary>
        /// Keywords, you can search for ad account name or ad account ID
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }

        /// <summary>
        /// Ad Account display status
        /// </summary>
        [JsonPropertyName("advertiser_status")]
        public List<string>? AdvertiserStatus { get; set; }
    }

    /// <summary>
    /// Request model for getting advertiser balance
    /// </summary>
    public class GetAdvertiserBalanceRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Additional fields to return in the response
        /// </summary>
        [JsonPropertyName("fields")]
        public List<string>? Fields { get; set; }

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public AdvertiserBalanceFiltering? Filtering { get; set; }

        /// <summary>
        /// Current number of pages. Default value: 1. Value range: ≥ 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10. Value range: 1-50
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Budget frequency restriction details
    /// </summary>
    public class BudgetFrequencyRestriction
    {
        /// <summary>
        /// The maximum number of updates allowed
        /// </summary>
        [JsonPropertyName("total_count")]
        public int? TotalCount { get; set; }

        /// <summary>
        /// The number of updates that have been made
        /// </summary>
        [JsonPropertyName("used_count")]
        public int? UsedCount { get; set; }

        /// <summary>
        /// The remaining number of updates allowed
        /// </summary>
        [JsonPropertyName("remaining_count")]
        public int? RemainingCount { get; set; }

        /// <summary>
        /// The time when the restriction starts
        /// </summary>
        [JsonPropertyName("effective_start_time")]
        public string? EffectiveStartTime { get; set; }

        /// <summary>
        /// The time when the restriction ends
        /// </summary>
        [JsonPropertyName("effective_end_time")]
        public string? EffectiveEndTime { get; set; }
    }

    /// <summary>
    /// Budget amount restriction details
    /// </summary>
    public class BudgetAmountRestriction
    {
        /// <summary>
        /// The minimum amount that you can change for the budget
        /// </summary>
        [JsonPropertyName("minimum_amount")]
        public string? MinimumAmount { get; set; }
    }

    /// <summary>
    /// Minimum transferable amount details
    /// </summary>
    public class MinTransferableAmount
    {
        /// <summary>
        /// The minimum cash amount that you can transfer to the ad account
        /// </summary>
        [JsonPropertyName("cash_amount")]
        public string? CashAmount { get; set; }

        /// <summary>
        /// The minimum voucher amount that you can transfer to the ad account
        /// </summary>
        [JsonPropertyName("grant_amount")]
        public string? GrantAmount { get; set; }

        /// <summary>
        /// The minimum credit amount that you can transfer to the ad account
        /// </summary>
        [JsonPropertyName("credit_amount")]
        public string? CreditAmount { get; set; }
    }

    /// <summary>
    /// Advertiser account balance information
    /// </summary>
    public class AdvertiserAccountBalance
    {
        /// <summary>
        /// Advertiser account ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Advertiser account name
        /// </summary>
        [JsonPropertyName("advertiser_name")]
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// Advertiser account display status
        /// </summary>
        [JsonPropertyName("advertiser_status")]
        public string? AdvertiserStatus { get; set; }

        /// <summary>
        /// Advertiser account type
        /// </summary>
        [JsonPropertyName("advertiser_type")]
        public string? AdvertiserType { get; set; }

        /// <summary>
        /// Advertiser account time zone
        /// </summary>
        [JsonPropertyName("timezone")]
        public string? Timezone { get; set; }

        /// <summary>
        /// Advertiser account currency
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// Advertiser account opening days
        /// </summary>
        [JsonPropertyName("account_open_days")]
        public int? AccountOpenDays { get; set; }

        /// <summary>
        /// Balance hit the line indicator
        /// </summary>
        [JsonPropertyName("balance_reminder")]
        public bool? BalanceReminder { get; set; }

        /// <summary>
        /// Advertiser account company name
        /// </summary>
        [JsonPropertyName("company")]
        public string? Company { get; set; }

        /// <summary>
        /// Advertiser account contact name
        /// </summary>
        [JsonPropertyName("contact_name")]
        public string? ContactName { get; set; }

        /// <summary>
        /// Advertiser account contact email
        /// </summary>
        [JsonPropertyName("contact_email")]
        public string? ContactEmail { get; set; }

        /// <summary>
        /// Advertiser account opening time (UTC+0)
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Advertiser account total balance, kept to two decimal places
        /// </summary>
        [JsonPropertyName("account_balance")]
        public decimal? AccountBalance { get; set; }

        /// <summary>
        /// Advertiser account valid account balance, kept to two decimal places
        /// </summary>
        [JsonPropertyName("valid_account_balance")]
        public decimal? ValidAccountBalance { get; set; }

        /// <summary>
        /// Advertiser account frozen balance, kept to two decimal places
        /// </summary>
        [JsonPropertyName("frozen_balance")]
        public decimal? FrozenBalance { get; set; }

        /// <summary>
        /// Advertiser account taxes, kept to two decimal places
        /// </summary>
        [JsonPropertyName("tax")]
        public decimal? Tax { get; set; }

        /// <summary>
        /// Advertiser account cash balance, kept to two decimal places
        /// </summary>
        [JsonPropertyName("cash_balance")]
        public decimal? CashBalance { get; set; }

        /// <summary>
        /// Advertiser account valid cash balance, kept to two decimal places
        /// </summary>
        [JsonPropertyName("valid_cash_balance")]
        public decimal? ValidCashBalance { get; set; }

        /// <summary>
        /// Advertiser account coupon/voucher balance, kept to two decimal places
        /// </summary>
        [JsonPropertyName("grant_balance")]
        public decimal? GrantBalance { get; set; }

        /// <summary>
        /// Advertiser account valid coupon/voucher balance, kept to two decimal places
        /// </summary>
        [JsonPropertyName("valid_grant_balance")]
        public decimal? ValidGrantBalance { get; set; }

        /// <summary>
        /// Amount that you can transfer from the advertiser account
        /// </summary>
        [JsonPropertyName("transferable_amount")]
        public decimal? TransferableAmount { get; set; }

        /// <summary>
        /// Budget mode of the ad account
        /// </summary>
        [JsonPropertyName("budget_mode")]
        public string? BudgetMode { get; set; }

        /// <summary>
        /// Budget amount based on budget mode
        /// </summary>
        [JsonPropertyName("budget")]
        public decimal? Budget { get; set; }

        /// <summary>
        /// Spent budget of the ad account
        /// </summary>
        [JsonPropertyName("budget_cost")]
        public decimal? BudgetCost { get; set; }

        /// <summary>
        /// The remaining budget
        /// </summary>
        [JsonPropertyName("budget_remaining")]
        public decimal? BudgetRemaining { get; set; }

        /// <summary>
        /// Restrictions on the number of budget changes allowed for the current day
        /// </summary>
        [JsonPropertyName("budget_frequency_restriction")]
        public BudgetFrequencyRestriction? BudgetFrequencyRestriction { get; set; }

        /// <summary>
        /// Restrictions on the minimum amount that can be changed for the budget
        /// </summary>
        [JsonPropertyName("budget_amount_restriction")]
        public BudgetAmountRestriction? BudgetAmountRestriction { get; set; }

        /// <summary>
        /// Details of the minimal amount that you can transfer to the ad account
        /// </summary>
        [JsonPropertyName("min_transferable_amount")]
        public MinTransferableAmount? MinTransferableAmount { get; set; }
    }

    /// <summary>
    /// Response data for get advertiser balance
    /// </summary>
    public class GetAdvertiserBalanceData
    {
        /// <summary>
        /// Advertiser account balance list
        /// </summary>
        [JsonPropertyName("advertiser_account_list")]
        public List<AdvertiserAccountBalance>? AdvertiserAccountList { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Response for get advertiser balance
    /// </summary>
    public class GetAdvertiserBalanceResponse : TikTokApiResponse<GetAdvertiserBalanceData>
    {
    }

    #endregion

    #region Get BC Balance Models

    /// <summary>
    /// Request model for getting BC balance
    /// </summary>
    public class GetBcBalanceRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Business Center balance information
    /// </summary>
    public class BcBalanceData
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string? BcId { get; set; }

        /// <summary>
        /// The currency of the Business Center
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// Business Center total balance, rounded to two decimal places
        /// </summary>
        [JsonPropertyName("account_balance")]
        public decimal? AccountBalance { get; set; }

        /// <summary>
        /// Business Center valid account balance, rounded to two decimal places
        /// </summary>
        [JsonPropertyName("valid_account_balance")]
        public decimal? ValidAccountBalance { get; set; }

        /// <summary>
        /// Business Center freeze balance, rounded to two decimal places
        /// </summary>
        [JsonPropertyName("frozen_balance")]
        public decimal? FrozenBalance { get; set; }

        /// <summary>
        /// Business Center tax, rounded to two decimal places
        /// </summary>
        [JsonPropertyName("tax")]
        public decimal? Tax { get; set; }

        /// <summary>
        /// Business Center cash balance, rounded to two decimal places
        /// </summary>
        [JsonPropertyName("cash_balance")]
        public decimal? CashBalance { get; set; }

        /// <summary>
        /// Business Center valid cash balance, rounded to two decimal places
        /// </summary>
        [JsonPropertyName("valid_cash_balance")]
        public decimal? ValidCashBalance { get; set; }

        /// <summary>
        /// Business Center coupon/voucher balance, rounded to two decimal places
        /// </summary>
        [JsonPropertyName("grant_balance")]
        public decimal? GrantBalance { get; set; }

        /// <summary>
        /// Business Center valid coupon/voucher balance, rounded to two decimal places
        /// </summary>
        [JsonPropertyName("valid_grant_balance")]
        public decimal? ValidGrantBalance { get; set; }
    }

    /// <summary>
    /// Response for get BC balance
    /// </summary>
    public class GetBcBalanceResponse: TikTokApiResponse<BcBalanceData>
    {
    }

    #endregion

    #region Transaction Models

    /// <summary>
    /// Filtering conditions for transaction requests
    /// </summary>
    public class TransactionFiltering
    {
        /// <summary>
        /// Transaction types
        /// </summary>
        [JsonPropertyName("transaction_types")]
        public List<string>? TransactionTypes { get; set; }

        /// <summary>
        /// Owner of ad accounts
        /// </summary>
        [JsonPropertyName("owner_of_account")]
        public List<string>? OwnerOfAccount { get; set; }

        /// <summary>
        /// A list of ad account IDs
        /// </summary>
        [JsonPropertyName("account_ids")]
        public List<string>? AccountIds { get; set; }

        /// <summary>
        /// Ad account name
        /// </summary>
        [JsonPropertyName("account_name")]
        public string? AccountName { get; set; }

        /// <summary>
        /// Query start time
        /// </summary>
        [JsonPropertyName("start_time")]
        public string? StartTime { get; set; }

        /// <summary>
        /// Query end time
        /// </summary>
        [JsonPropertyName("end_time")]
        public string? EndTime { get; set; }

        /// <summary>
        /// Billing types
        /// </summary>
        [JsonPropertyName("billing_types")]
        public List<string>? BillingTypes { get; set; }

        /// <summary>
        /// Fund type
        /// </summary>
        [JsonPropertyName("funds_type")]
        public List<string>? FundsType { get; set; }

        /// <summary>
        /// Whether to summarize by account
        /// </summary>
        [JsonPropertyName("summary_by_account")]
        public bool? SummaryByAccount { get; set; }

        /// <summary>
        /// Search keywords
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }

        /// <summary>
        /// Transaction record search start date
        /// </summary>
        [JsonPropertyName("start_date")]
        public string? StartDate { get; set; }

        /// <summary>
        /// Transaction record search end date
        /// </summary>
        [JsonPropertyName("end_date")]
        public string? EndDate { get; set; }

        /// <summary>
        /// Transfer type
        /// </summary>
        [JsonPropertyName("transfer_type")]
        public string? TransferType { get; set; }
    }

    /// <summary>
    /// Transaction record information
    /// </summary>
    public class TransactionRecord
    {
        /// <summary>
        /// Transaction ID, the unique identifier of the transaction
        /// </summary>
        [JsonPropertyName("transaction_id")]
        public string? TransactionId { get; set; }

        /// <summary>
        /// The ID of the Payment Portfolio associated with the transaction
        /// </summary>
        [JsonPropertyName("payment_portfolio_id")]
        public string? PaymentPortfolioId { get; set; }

        /// <summary>
        /// The name of the Payment Portfolio associated with the transaction
        /// </summary>
        [JsonPropertyName("payment_portfolio_name")]
        public string? PaymentPortfolioName { get; set; }

        /// <summary>
        /// The ID of the ad account associated with the transaction
        /// </summary>
        [JsonPropertyName("account_id")]
        public string? AccountId { get; set; }

        /// <summary>
        /// The name of the ad account associated with the transaction
        /// </summary>
        [JsonPropertyName("account_name")]
        public string? AccountName { get; set; }

        /// <summary>
        /// Ad Account ID (for advertiser transaction records)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Ad Account Name (for advertiser transaction records)
        /// </summary>
        [JsonPropertyName("advertiser_name")]
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string? BcId { get; set; }

        /// <summary>
        /// Business Center name
        /// </summary>
        [JsonPropertyName("bc_name")]
        public string? BcName { get; set; }

        /// <summary>
        /// The total amount of the transaction
        /// </summary>
        [JsonPropertyName("amount")]
        public decimal? Amount { get; set; }

        /// <summary>
        /// The subtotal amount (the amount excluding taxes) of the transaction
        /// </summary>
        [JsonPropertyName("subtotal")]
        public decimal? Subtotal { get; set; }

        /// <summary>
        /// The amount of tax included in the transaction
        /// </summary>
        [JsonPropertyName("tax_amount")]
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// The currency code in which the amounts are calculated
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// Amount type
        /// </summary>
        [JsonPropertyName("amount_type")]
        public string? AmountType { get; set; }

        /// <summary>
        /// Transaction type
        /// </summary>
        [JsonPropertyName("transaction_type")]
        public string? TransactionType { get; set; }

        /// <summary>
        /// Billing type
        /// </summary>
        [JsonPropertyName("billing_type")]
        public string? BillingType { get; set; }

        /// <summary>
        /// Type of funding
        /// </summary>
        [JsonPropertyName("funds_type")]
        public string? FundsType { get; set; }

        /// <summary>
        /// Transfer type
        /// </summary>
        [JsonPropertyName("transfer_type")]
        public string? TransferType { get; set; }

        /// <summary>
        /// The time zone where the transaction occurred
        /// </summary>
        [JsonPropertyName("timezone")]
        public string? Timezone { get; set; }

        /// <summary>
        /// The time when the transaction record was generated
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Trading time
        /// </summary>
        [JsonPropertyName("date")]
        public string? Date { get; set; }

        /// <summary>
        /// Invoice ID
        /// </summary>
        [JsonPropertyName("invoice_id")]
        public string? InvoiceId { get; set; }

        /// <summary>
        /// Invoice serial number
        /// </summary>
        [JsonPropertyName("serial_number")]
        public string? SerialNumber { get; set; }
    }

    #endregion

    #region Get BC/Account Transaction Models

    /// <summary>
    /// Request model for getting BC or account transaction records
    /// </summary>
    public class GetBcAccountTransactionRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// The transaction level that the transaction records will be retrieved at
        /// </summary>
        [JsonPropertyName("transaction_level")]
        public string? TransactionLevel { get; set; }

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public TransactionFiltering? Filtering { get; set; }

        /// <summary>
        /// Current number of pages
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Response data for get BC/account transaction
    /// </summary>
    public class GetBcAccountTransactionData
    {
        /// <summary>
        /// The list of transaction records
        /// </summary>
        [JsonPropertyName("transaction_list")]
        public List<TransactionRecord>? TransactionList { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Response for get BC/account transaction
    /// </summary>
    public class GetBcAccountTransactionResponse: TikTokApiResponse<GetBcAccountTransactionData>
    {

    }

    #endregion

    #region Get Advertiser Transaction Models

    /// <summary>
    /// Request model for getting advertiser transaction records
    /// </summary>
    public class GetAdvertiserTransactionRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public AdvertiserTransactionFiltering? Filtering { get; set; }

        /// <summary>
        /// Current number of pages
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering conditions for advertiser transaction records requests
    /// </summary>
    public class AdvertiserTransactionFiltering
    {
        /// <summary>
        /// Transfer type
        /// </summary>
        [JsonPropertyName("transfer_type")]
        public string? TransferType { get; set; }

        /// <summary>
        /// Type of funding
        /// </summary>
        [JsonPropertyName("funds_type")]
        public List<string>? FundsType { get; set; }


        /// <summary>
        /// Whether to summarize by account. false means no summary; true means summary. Default value: false.
        /// </summary>
        [JsonPropertyName("summary_by_account")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Search keywords, you can search for ad account name or ad account ID.
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Date { get; set; }

        /// <summary>
        /// Query start date
        /// </summary>
        [JsonPropertyName("start_date")]
        public string? StartDate { get; set; }

        /// <summary>
        /// Query end date
        /// </summary>
        [JsonPropertyName("end_date")]
        public string? EndDate { get; set; }
    }

    /// <summary>
    /// Response data for get advertiser transaction
    /// </summary>
    public class GetAdvertiserTransactionData
    {
        /// <summary>
        /// Ad Account transaction record
        /// </summary>
        [JsonPropertyName("transaction_list")]
        public List<AdvertiserTransactionRecord>? TransactionList { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    public class AdvertiserTransactionRecord
    {
        /// <summary>
        /// Advertiser account ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Advertiser account name
        /// </summary>
        [JsonPropertyName("advertiser_name")]
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// Transaction amount, kept to two decimal places.
        /// </summary>
        [JsonPropertyName("amount")]
        public decimal? Amount { get; set; }

        /// <summary>
        /// Advertiser account currency
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }


        /// <summary>
        /// Trading time, (UTC+0) format：2020-10-12 00:00:00.
        /// </summary>
        [JsonPropertyName("date")]
        public string? Date { get; set; }
        /// <summary>
        /// Type of funding. Enum values：FUNDS_TYPE_CASH(cash), FUNDS_TYPE_GRANT(coupon/voucher) .
        /// </summary>
        [JsonPropertyName("funds_type")]
        public string? FundsType { get; set; }

        /// <summary>
        /// Billing type. Enum values: TRANS_TYPE_TRANSFER(transfer), TRANS_TYPE_TAX(consumption), TRANS_TYPE_COST(tax).
        /// </summary>
        [JsonPropertyName("transfer_type")]
        public string TransferType { get; set; } = string.Empty;

        /// <summary>
        /// Advertiser account time zone
        /// </summary>
        [JsonPropertyName("timezone")]
        public string? Timezone { get; set; }
    }

    /// <summary>
    /// Response for get advertiser transaction
    /// </summary>
    public class GetAdvertiserTransactionResponse: TikTokApiResponse<GetAdvertiserTransactionData>
    {
    }

    #endregion

    #region Get BC Transaction Models

    /// <summary>
    /// Transaction summary information
    /// </summary>
    public class TransactionSummary
    {
        /// <summary>
        /// Total amount of money charged to the account
        /// </summary>
        [JsonPropertyName("amount_charged")]
        public decimal? AmountCharged { get; set; }

        /// <summary>
        /// Total amount of money transferred out
        /// </summary>
        [JsonPropertyName("amount_paid")]
        public decimal? AmountPaid { get; set; }

        /// <summary>
        /// Total cost amount for the Business Center
        /// </summary>
        [JsonPropertyName("amount")]
        public decimal? Amount { get; set; }

        /// <summary>
        /// Cash cost amount for the Business Center
        /// </summary>
        [JsonPropertyName("cash_amount")]
        public decimal? CashAmount { get; set; }

        /// <summary>
        /// Ad credit cost amount for the Business Center
        /// </summary>
        [JsonPropertyName("grant_amount")]
        public decimal? GrantAmount { get; set; }

        /// <summary>
        /// Estimated tax amount for the Business Center
        /// </summary>
        [JsonPropertyName("tax_amount")]
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// Currency
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }
    }

    /// <summary>
    /// BC transaction record
    /// </summary>
    public class BcTransactionRecord
    {
        /// <summary>
        /// Transaction date
        /// </summary>
        [JsonPropertyName("date")]
        public string? Date { get; set; }

        /// <summary>
        /// Transaction amount
        /// </summary>
        [JsonPropertyName("amount")]
        public decimal? Amount { get; set; }

        /// <summary>
        /// Timezone
        /// </summary>
        [JsonPropertyName("timezone")]
        public string? Timezone { get; set; }

        /// <summary>
        /// Currency used in the transaction
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// Fund type
        /// </summary>
        [JsonPropertyName("funds_type")]
        public string? FundsType { get; set; }

        /// <summary>
        /// Invoice ID
        /// </summary>
        [JsonPropertyName("invoice_id")]
        public string? InvoiceId { get; set; }

        /// <summary>
        /// Invoice serial number
        /// </summary>
        [JsonPropertyName("invoice_serial_number")]
        public string? InvoiceSerialNumber { get; set; }
    }

    /// <summary>
    /// Request model for getting BC transaction records
    /// </summary>
    public class GetBcTransactionRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public TransactionFiltering? Filtering { get; set; }

        /// <summary>
        /// Page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Response data for get BC transaction
    /// </summary>
    public class GetBcTransactionData
    {
        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }

        /// <summary>
        /// List of transaction records
        /// </summary>
        [JsonPropertyName("list")]
        public List<BcTransactionRecord>? List { get; set; }

        /// <summary>
        /// Sum of transactions
        /// </summary>
        [JsonPropertyName("transaction_summary")]
        public TransactionSummary? TransactionSummary { get; set; }
    }

    /// <summary>
    /// Response for get BC transaction
    /// </summary>
    public class GetBcTransactionResponse: TikTokApiResponse<GetBcTransactionData>
    {
    }

    #endregion

    #region Budget Changelog Models

    /// <summary>
    /// Filtering conditions for budget changelog request
    /// </summary>
    public class BudgetChangelogFiltering
    {
        /// <summary>
        /// Query start date
        /// </summary>
        [JsonPropertyName("start_date")]
        public string? StartDate { get; set; }

        /// <summary>
        /// Query end date
        /// </summary>
        [JsonPropertyName("end_date")]
        public string? EndDate { get; set; }
    }

    /// <summary>
    /// Request model for getting budget changelog
    /// </summary>
    public class GetBudgetChangelogRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Ad account ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public BudgetChangelogFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Budget change record
    /// </summary>
    public class BudgetChangelogRecord
    {
        /// <summary>
        /// The time when the budget change occurred
        /// </summary>
        [JsonPropertyName("operation_time")]
        public string? OperationTime { get; set; }

        /// <summary>
        /// Change type
        /// </summary>
        [JsonPropertyName("activity_type")]
        public string? ActivityType { get; set; }

        /// <summary>
        /// The previous budget amount of the ad account before the change
        /// </summary>
        [JsonPropertyName("previous_budget")]
        public decimal? PreviousBudget { get; set; }

        /// <summary>
        /// The previous budget mode of the ad account
        /// </summary>
        [JsonPropertyName("previous_budget_mode")]
        public string? PreviousBudgetMode { get; set; }

        /// <summary>
        /// The current budget amount of the ad account after the change
        /// </summary>
        [JsonPropertyName("current_budget")]
        public decimal? CurrentBudget { get; set; }

        /// <summary>
        /// The current budget mode of the ad account
        /// </summary>
        [JsonPropertyName("current_budget_mode")]
        public string? CurrentBudgetMode { get; set; }

        /// <summary>
        /// The currency for the current budget
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// The user ID of the operator
        /// </summary>
        [JsonPropertyName("operator_id")]
        public string? OperatorId { get; set; }

        /// <summary>
        /// The user name of the operator
        /// </summary>
        [JsonPropertyName("operator_name")]
        public string? OperatorName { get; set; }
    }

    /// <summary>
    /// Response data for get budget changelog
    /// </summary>
    public class GetBudgetChangelogData
    {
        /// <summary>
        /// The budget change history details
        /// </summary>
        [JsonPropertyName("changelog_list")]
        public List<BudgetChangelogRecord>? ChangelogList { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Response for get budget changelog
    /// </summary>
    public class GetBudgetChangelogResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        [JsonPropertyName("data")]
        public GetBudgetChangelogData? Data { get; set; }
    }

    #endregion

    #region Cost Records Models

    /// <summary>
    /// Filtering conditions for cost records request
    /// </summary>
    public class CostRecordsFiltering
    {
        /// <summary>
        /// Search keyword
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }

        /// <summary>
        /// Query start date
        /// </summary>
        [JsonPropertyName("start_date")]
        public string? StartDate { get; set; }

        /// <summary>
        /// Query end date
        /// </summary>
        [JsonPropertyName("end_date")]
        public string? EndDate { get; set; }
    }

    /// <summary>
    /// Request model for getting cost records
    /// </summary>
    public class GetCostRecordsRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public CostRecordsFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Cost record for an advertiser
    /// </summary>
    public class CostRecord
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Advertiser name
        /// </summary>
        [JsonPropertyName("advertiser_name")]
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// Total cost amount for the advertiser
        /// </summary>
        [JsonPropertyName("amount")]
        public decimal? Amount { get; set; }

        /// <summary>
        /// Cash cost amount for the advertiser
        /// </summary>
        [JsonPropertyName("cash_amount")]
        public decimal? CashAmount { get; set; }

        /// <summary>
        /// Ad credit cost amount for the advertiser
        /// </summary>
        [JsonPropertyName("grant_amount")]
        public decimal? GrantAmount { get; set; }

        /// <summary>
        /// Estimated tax amount for the advertiser
        /// </summary>
        [JsonPropertyName("tax_amount")]
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// The currency of the advertiser
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }
    }

    /// <summary>
    /// Response data for get cost records
    /// </summary>
    public class GetCostRecordsData
    {
        /// <summary>
        /// The list of cost records for each advertiser
        /// </summary>
        [JsonPropertyName("cost_list")]
        public List<CostRecord>? CostList { get; set; }

        /// <summary>
        /// A summary of cost records for the Business Center
        /// </summary>
        [JsonPropertyName("transaction_summary")]
        public TransactionSummary? TransactionSummary { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Response for get cost records
    /// </summary>
    public class GetCostRecordsResponse: TikTokApiResponse<GetCostRecordsData>
    {
    }

    #endregion
}
