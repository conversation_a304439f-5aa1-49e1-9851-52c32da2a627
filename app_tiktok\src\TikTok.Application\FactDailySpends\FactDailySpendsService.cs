﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.FactDailySpends.Dtos;
using TikTok.Repositories;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.FactDailySpends
{
    public class FactDailySpendsService : CrudAppService<FactDailySpendEntity, FactDailySpendDto, Guid, GetFactDailySpendDto, GetFactDailySpendInput, GetFactDailySpendInput>, IFactDailySpendsService
    {
        private readonly IFactDailySpendRepository _factDailySpendRepository;
        private readonly IRepository<DimDateEntity, int> _dimDateRepository;
        private readonly IRepository<DimBusinessCenterEntity, Guid> _dimBusinessCenterRepository;
        private readonly IRepository<DimAdAccountEntity, Guid> _dimAdAccountRepository;

        public FactDailySpendsService(
            IFactDailySpendRepository repository,
            IRepository<DimDateEntity, int> dimDateRepository,
            IRepository<DimBusinessCenterEntity, Guid> dimBusinessCenterRepository,
            IRepository<DimAdAccountEntity, Guid> dimAdAccountRepository) : base(repository)
        {
            _factDailySpendRepository = repository;
            _dimDateRepository = dimDateRepository;
            _dimBusinessCenterRepository = dimBusinessCenterRepository;
            _dimAdAccountRepository = dimAdAccountRepository;
        }

        public override async Task<PagedResultDto<FactDailySpendDto>> GetListAsync(GetFactDailySpendDto input)
        {
            var result = await _factDailySpendRepository.GetListAsync(
                from: input.StartDate ?? DateTime.Now.AddDays(-1),
                to: input.EndDate ?? DateTime.MaxValue,
                DimAdAccountId: input.DimAdAccountId,
                DimBusinessCenterId: input.DimBusinessCenterId,
                AdvertiserId: input.AdvertiserId,
                AdvertiserName: input.SearchKeyword,
                BcId: input.BcId,
                totalAmount: input.MinAmount != null && input.MaxAmount != null ? Tuple.Create(input.MinAmount.Value, input.MaxAmount.Value) : null,
                currency: input.Currency);

            return new PagedResultDto<FactDailySpendDto>
            {
                TotalCount = result.Count(),
                Items = result.Select(x => ObjectMapper.Map<FactDailySpendEntity, FactDailySpendDto>(x)).ToArray()
            };
        }

        public async Task<FactDailySpendReportDto> GetDailySpendReportAsync(DateTime from, DateTime to)
        {
            var result = new FactDailySpendReportDto()
            {
                From = from,
                To = to,
                FactDailySpends = [],
                DimDates = [],
                DimAdAccounts = [],
                DimBusinessCenters = []
            };

            var factDailySpends = await this.GetListAsync(new GetFactDailySpendDto()
            {
                StartDate = from,
                EndDate = to
            });
            result.FactDailySpends = factDailySpends.Items.ToList();

            if (result.FactDailySpends.Any())
            {
                var uniqueDimDateIds = result.FactDailySpends
                    .Select(f => f.DimDateId)
                    .Distinct()
                    .ToList();

                if (uniqueDimDateIds.Any())
                {
                    var dimDates = await _dimDateRepository.GetListAsync(d => uniqueDimDateIds.Contains(d.Id));
                    result.DimDates = ObjectMapper.Map<List<DimDateEntity>, List<DimDates.DimDateDto>>(dimDates);
                }

                var uniqueDimBusinessCenterIds = result.FactDailySpends
                    .Select(f => f.DimBusinessCenterId)
                    .Distinct()
                    .ToList();

                if (uniqueDimBusinessCenterIds.Any())
                {
                    var dimBusinessCenters = await _dimBusinessCenterRepository.GetListAsync(b => uniqueDimBusinessCenterIds.Contains(b.Id));
                    result.DimBusinessCenters = ObjectMapper.Map<List<DimBusinessCenterEntity>, List<DimBusinessCenters.DimBusinessCenterDto>>(dimBusinessCenters);
                }

                var uniqueDimAdAccountIds = result.FactDailySpends
                    .Select(f => f.DimAdAccountId)
                    .Distinct()
                    .ToList();

                if (uniqueDimAdAccountIds.Any())
                {
                    var dimAdAccounts = await _dimAdAccountRepository.GetListAsync(a => uniqueDimAdAccountIds.Contains(a.Id));
                    result.DimAdAccounts = ObjectMapper.Map<List<DimAdAccountEntity>, List<DimAdAccounts.DimAdAccountDto>>(dimAdAccounts);
                }
            }

            return result;
        }
    }
}
