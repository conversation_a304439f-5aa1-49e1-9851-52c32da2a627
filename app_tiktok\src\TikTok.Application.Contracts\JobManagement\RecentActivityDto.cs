using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.JobManagement
{
    /// <summary>
    /// DTO cho hoạt động gần đây
    /// </summary>
    public class RecentActivityDto : EntityDto<Guid>
    {
        /// <summary>
        /// ID của job
        /// </summary>
        public string JobId { get; set; }

        /// <summary>
        /// Loại command
        /// </summary>
        public string CommandType { get; set; }

        /// <summary>
        /// ID của worker
        /// </summary>
        public string WorkerId { get; set; }

        /// <summary>
        /// Trạng thái
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Thời gian tạo
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// Thời gian bắt đầu
        /// </summary>
        public DateTime? StartedTime { get; set; }

        /// <summary>
        /// Thời gian hoàn thành
        /// </summary>
        public DateTime? CompletedTime { get; set; }

        /// <summary>
        /// Thời gian xử lý (giây)
        /// </summary>
        public int? ProcessingTimeSeconds { get; set; }

        /// <summary>
        /// Tên Business Application
        /// </summary>
        public string BusinessApplicationName { get; set; }

        /// <summary>
        /// Thông báo lỗi (nếu có)
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}