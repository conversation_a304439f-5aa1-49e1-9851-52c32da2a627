using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu ngân sách
    /// </summary>
    public interface IBalanceSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ ngân sách Business Center theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BalanceSyncResult> SyncBcBalanceAsync(string bcId);

        /// <summary>
        /// Đồng bộ ngân sách AdAccount theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BalanceSyncResult> SyncAdAccountBalanceAsync(string bcId);

        /// <summary>
        /// Đồng bộ tất cả ngân sách (BC và AdAccount) theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BalanceSyncResult> SyncAllBalancesAsync(string bcId);

        /// <summary>
        /// Đồng bộ tất cả ngân sách cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BalanceSyncResult> SyncAllBalancesForAllBcsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu ngân sách
    /// </summary>
    public class BalanceSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số AdAccount đã đồng bộ
        /// </summary>
        public int AdAccountCount { get; set; }
    }
}