/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Subscription endpoints
    /// </summary>
    public static class SubscriptionEndpoints
    {
        /// <summary>
        /// Create a subscription endpoint (v1.3)
        /// </summary>
        public const string Subscribe = "/open_api/v1.3/subscription/subscribe/";

        /// <summary>
        /// Get subscription details endpoint (v1.3)
        /// </summary>
        public const string GetSubscriptions = "/open_api/v1.3/subscription/get/";

        /// <summary>
        /// Cancel a subscription endpoint (v1.3)
        /// </summary>
        public const string Unsubscribe = "/open_api/v1.3/subscription/unsubscribe/";
    }
}
