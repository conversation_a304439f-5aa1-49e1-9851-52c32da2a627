using System;

namespace TikTok.Dashboard
{
    /// <summary>
    /// DTO cho thống kê overview dashboard
    /// </summary>
    public class DashboardOverviewDto
    {
        /// <summary>
        /// Số lượng trung tâm kinh doanh
        /// </summary>
        public long BusinessCenterCount { get; set; }

        /// <summary>
        /// Số lượng tài khoản quảng cáo
        /// </summary>
        public long AdAccountCount { get; set; }

        /// <summary>
        /// Số lượng chiến dịch
        /// </summary>
        public long CampaignCount { get; set; }

        /// <summary>
        /// Số lượng khách hàng
        /// </summary>
        public long CustomerCount { get; set; }

        /// <summary>
        /// Thời gian cập nhật cuối cùng
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }
}
