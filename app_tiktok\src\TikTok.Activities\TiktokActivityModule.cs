﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tsp.Zalo.Activities;
using UI.Activities;
using Volo.Abp.AutoMapper;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.BlobStoring;
using Volo.Abp.Modularity;

namespace TikTok.Activities
{
    [DependsOn(
      typeof(TikTokDomainModule),
      typeof(TikTokApplicationContractsModule),
      typeof(AbpAutoMapperModule),
      typeof(UIActivitiesWebModule),
      typeof(ZaloActivitiesModule)
    )]
    public class TiktokActivityModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<TiktokActivityModule>();
            });
        }
    }
}
