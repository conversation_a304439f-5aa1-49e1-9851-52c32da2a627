using System;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.BusinessApplications
{
    /// <summary>
    /// DTO cho ứng dụng Business
    /// </summary>
    public class BusinessApplicationDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID duy nhất của ứng dụng Business
        /// </summary>
        public string ApplicationId { get; set; }

        /// <summary>
        /// Khóa bí mật của ứng dụng
        /// </summary>
        public string Secret { get; set; }

        /// <summary>
        /// Token truy cập để thực hiện các API calls
        /// </summary>
        public string? AccessToken { get; set; }

        /// <summary>
        /// Thời gian tạo token truy cập
        /// </summary>
        public DateTime? AccessTokenCreatedAt { get; set; }

        /// <summary>
        /// G<PERSON> ch<PERSON> bổ sung về ứng dụng
        /// </summary>
        public string? Comment { get; set; }

        /// <summary>
        /// Trạng thái hoạt động của ứng dụng
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Trạng thái xử lý hiện tại của ứng dụng
        /// </summary>
        public BusinessApplicationStatus Status { get; set; }

        /// <summary>
        /// Dữ liệu bổ sung được lưu trữ dưới dạng JSON
        /// </summary>
        public string? Payload { get; set; }

        /// <summary>
        /// ID của Business Center mà ứng dụng này thuộc về
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// Tên của Business Center mà ứng dụng này thuộc về
        /// </summary>
        public string? BcName { get; set; }

        public Guid BackupId { get; set; }
    }
} 