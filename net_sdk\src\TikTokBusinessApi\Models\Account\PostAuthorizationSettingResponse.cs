/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for post authorization setting
    /// </summary>
    public class PostAuthorizationSettingResponse
    {
        /// <summary>
        /// The ID of the TikTok post (returned only when is_ad_promotable is set to true)
        /// </summary>
        [JsonPropertyName("item_id")]
        public string? ItemId { get; set; }

        /// <summary>
        /// The authorization code for the TikTok post (returned only when is_ad_promotable is set to true)
        /// </summary>
        [JsonPropertyName("auth_code")]
        public string? AuthCode { get; set; }

        /// <summary>
        /// The time when the authorization code becomes valid (returned only when is_ad_promotable is set to true)
        /// </summary>
        [JsonPropertyName("auth_code_start_time")]
        public string? AuthCodeStartTime { get; set; }

        /// <summary>
        /// The time when the authorization code expires (returned only when is_ad_promotable is set to true)
        /// </summary>
        [JsonPropertyName("auth_code_end_time")]
        public string? AuthCodeEndTime { get; set; }

        /// <summary>
        /// The authorization validity period in days (returned only when is_ad_promotable is set to true)
        /// </summary>
        [JsonPropertyName("authorization_days")]
        public int? AuthorizationDays { get; set; }
    }
}
