/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Enums and Constants

    /// <summary>
    /// Object type for negative keywords operations
    /// </summary>
    public static class NegativeKeywordObjectType
    {
        /// <summary>
        /// Campaign level negative keywords
        /// </summary>
        public const string Campaign = "CAMPAIGN";

        /// <summary>
        /// Ad group level negative keywords
        /// </summary>
        public const string AdGroup = "ADGROUP";
    }

    /// <summary>
    /// Match type for negative keywords
    /// </summary>
    public static class NegativeKeywordMatchType
    {
        /// <summary>
        /// Exact match
        /// </summary>
        public const string PreciseWord = "PRECISE_WORD";

        /// <summary>
        /// Phrase match
        /// </summary>
        public const string PhraseWord = "PHRASE_WORD";

        /// <summary>
        /// Broad match
        /// </summary>
        public const string BroadWord = "BROAD_WORD";
    }

    #endregion

    #region Request Models

    /// <summary>
    /// Request model for getting negative keywords
    /// </summary>
    public class GetNegativeKeywordsRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Object type (CAMPAIGN or ADGROUP)
        /// </summary>
        [Required]
        [JsonPropertyName("object_type")]
        public string ObjectType { get; set; } = string.Empty;

        /// <summary>
        /// Object ID (campaign ID or ad group ID)
        /// </summary>
        [Required]
        [JsonPropertyName("object_id")]
        public string ObjectId { get; set; } = string.Empty;

        /// <summary>
        /// Page number (default: 1, minimum: 1)
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size (default: 10, range: 1-50)
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Request model for creating negative keywords
    /// </summary>
    public class CreateNegativeKeywordsRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Object type (CAMPAIGN or ADGROUP)
        /// </summary>
        [Required]
        [JsonPropertyName("object_type")]
        public string ObjectType { get; set; } = string.Empty;

        /// <summary>
        /// Object IDs (campaign IDs or ad group IDs, max size: 50)
        /// </summary>
        [Required]
        [JsonPropertyName("object_ids")]
        public List<string> ObjectIds { get; set; } = new();

        /// <summary>
        /// Whether to replace all existing negative keywords (default: false)
        /// </summary>
        [JsonPropertyName("replace")]
        public bool? Replace { get; set; }

        /// <summary>
        /// List of negative keywords to create (max size: 1,000)
        /// </summary>
        [Required]
        [JsonPropertyName("keywords")]
        public List<NegativeKeywordCreateBody> Keywords { get; set; } = new();
    }

    /// <summary>
    /// Request model for updating a negative keyword
    /// </summary>
    public class UpdateNegativeKeywordRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Object type (CAMPAIGN or ADGROUP)
        /// </summary>
        [Required]
        [JsonPropertyName("object_type")]
        public string ObjectType { get; set; } = string.Empty;

        /// <summary>
        /// Object ID (campaign ID or ad group ID)
        /// </summary>
        [Required]
        [JsonPropertyName("object_id")]
        public string ObjectId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the negative keyword to update
        /// </summary>
        [Required]
        [JsonPropertyName("old_keyword_id")]
        public string OldKeywordId { get; set; } = string.Empty;

        /// <summary>
        /// Updated keyword information
        /// </summary>
        [Required]
        [JsonPropertyName("keyword")]
        public NegativeKeywordUpdateBody Keyword { get; set; } = new();
    }

    /// <summary>
    /// Request model for deleting negative keywords
    /// </summary>
    public class DeleteNegativeKeywordsRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Object type (CAMPAIGN or ADGROUP)
        /// </summary>
        [Required]
        [JsonPropertyName("object_type")]
        public string ObjectType { get; set; } = string.Empty;

        /// <summary>
        /// Object ID (campaign ID or ad group ID)
        /// </summary>
        [Required]
        [JsonPropertyName("object_id")]
        public string ObjectId { get; set; } = string.Empty;

        /// <summary>
        /// IDs of negative keywords to delete (max size: 1,000)
        /// </summary>
        [Required]
        [JsonPropertyName("keyword_ids")]
        public List<string> KeywordIds { get; set; } = new();
    }

    /// <summary>
    /// Request model for downloading negative keywords
    /// </summary>
    public class DownloadNegativeKeywordsRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [Required]
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Object type (CAMPAIGN or ADGROUP)
        /// </summary>
        [Required]
        [JsonPropertyName("object_type")]
        public string ObjectType { get; set; } = string.Empty;

        /// <summary>
        /// Object ID (campaign ID or ad group ID)
        /// </summary>
        [Required]
        [JsonPropertyName("object_id")]
        public string ObjectId { get; set; } = string.Empty;
    }

    #endregion

    #region Body Models

    /// <summary>
    /// Body model for creating a negative keyword
    /// </summary>
    public class NegativeKeywordCreateBody
    {
        /// <summary>
        /// Name of the negative keyword (max 80 characters)
        /// </summary>
        [Required]
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Match type for the negative keyword (default: BROAD_WORD)
        /// </summary>
        [JsonPropertyName("match_type")]
        public string? MatchType { get; set; }
    }

    /// <summary>
    /// Body model for updating a negative keyword
    /// </summary>
    public class NegativeKeywordUpdateBody
    {
        /// <summary>
        /// New name for the negative keyword (max 80 characters)
        /// </summary>
        [Required]
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Match type for the negative keyword (default: BROAD_WORD)
        /// </summary>
        [JsonPropertyName("match_type")]
        public string? MatchType { get; set; }
    }

    #endregion

    #region Response Models

    /// <summary>
    /// Response model for getting negative keywords
    /// </summary>
    public class GetNegativeKeywordsResponse
    {
        /// <summary>
        /// List of negative keywords
        /// </summary>
        [JsonPropertyName("keywords")]
        public List<NegativeKeywordInfo> Keywords { get; set; } = new();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Response model for creating negative keywords
    /// </summary>
    public class CreateNegativeKeywordsResponse
    {
        // Empty response body as per API documentation
    }

    /// <summary>
    /// Response model for updating a negative keyword
    /// </summary>
    public class UpdateNegativeKeywordResponse
    {
        /// <summary>
        /// ID of the updated negative keyword
        /// </summary>
        [JsonPropertyName("new_keyword_id")]
        public string? NewKeywordId { get; set; }
    }

    /// <summary>
    /// Response model for deleting negative keywords
    /// </summary>
    public class DeleteNegativeKeywordsResponse
    {
        // Empty response body as per API documentation
    }

    #endregion

    #region Info Models

    /// <summary>
    /// Information about a negative keyword
    /// </summary>
    public class NegativeKeywordInfo
    {
        /// <summary>
        /// ID of the negative keyword
        /// </summary>
        [JsonPropertyName("keyword_id")]
        public string? KeywordId { get; set; }

        /// <summary>
        /// Name of the negative keyword
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Match type for the negative keyword
        /// </summary>
        [JsonPropertyName("match_type")]
        public string? MatchType { get; set; }
    }

    #endregion
}
