/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Enums

    /// <summary>
    /// Sort field options for mentioned posts
    /// </summary>
    public enum MentionSortField
    {
        /// <summary>
        /// Sort by create time
        /// </summary>
        CREATE_TIME,

        /// <summary>
        /// Sort by likes
        /// </summary>
        LIKES,

        /// <summary>
        /// Sort by comments
        /// </summary>
        COMMENTS,

        /// <summary>
        /// Sort by shares
        /// </summary>
        SHARES
    }

    /// <summary>
    /// Sort type options
    /// </summary>
    public enum MentionSortType
    {
        /// <summary>
        /// Ascending order
        /// </summary>
        ASC,

        /// <summary>
        /// Descending order
        /// </summary>
        DESC
    }

    /// <summary>
    /// Comment sort field options
    /// </summary>
    public enum CommentSortField
    {
        /// <summary>
        /// Sort by video likes
        /// </summary>
        VIDEO_LIKES,

        /// <summary>
        /// Sort by comment create time
        /// </summary>
        COMMENT_CREATE_TIME,

        /// <summary>
        /// Sort by comment likes
        /// </summary>
        COMMENT_LIKES
    }

    /// <summary>
    /// Comment type options
    /// </summary>
    public enum CommentType
    {
        /// <summary>
        /// Top-level comment
        /// </summary>
        COMMENT,

        /// <summary>
        /// Comment reply
        /// </summary>
        REPLY
    }

    #endregion

    #region Request Models

    /// <summary>
    /// Request for getting mentioned posts
    /// </summary>
    public class GetMentionedPostsRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        [Required]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// Requested fields
        /// </summary>
        [JsonPropertyName("fields")]
        public List<string>? Fields { get; set; }

        /// <summary>
        /// The field to sort the result by
        /// </summary>
        [JsonPropertyName("sort_field")]
        public MentionSortField? SortField { get; set; }

        /// <summary>
        /// Sorting order
        /// </summary>
        [JsonPropertyName("sort_type")]
        public MentionSortType? SortType { get; set; }

        /// <summary>
        /// The lookback period for the results, measured in days (1-90)
        /// </summary>
        [JsonPropertyName("number_of_days")]
        public int? NumberOfDays { get; set; }

        /// <summary>
        /// The regions of mentioned posts to filter by
        /// </summary>
        [JsonPropertyName("regions")]
        public List<string>? Regions { get; set; }

        /// <summary>
        /// Cursor for pagination
        /// </summary>
        [JsonPropertyName("cursor")]
        public int? Cursor { get; set; }

        /// <summary>
        /// The maximum number of posts to be returned for each page (1-100)
        /// </summary>
        [JsonPropertyName("max_count")]
        public int? MaxCount { get; set; }
    }

    /// <summary>
    /// Request for getting mentioned post details
    /// </summary>
    public class GetMentionedPostDetailsRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        [Required]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the post
        /// </summary>
        [JsonPropertyName("item_id")]
        [Required]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// Requested fields
        /// </summary>
        [JsonPropertyName("fields")]
        public List<string>? Fields { get; set; }
    }

    /// <summary>
    /// Request for getting top keywords
    /// </summary>
    public class GetTopKeywordsRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        [Required]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// The regions of mentioned posts to filter by
        /// </summary>
        [JsonPropertyName("regions")]
        public List<string>? Regions { get; set; }
    }

    /// <summary>
    /// Request for getting top hashtags
    /// </summary>
    public class GetTopHashtagsRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        [Required]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// The regions of mentioned posts to filter by
        /// </summary>
        [JsonPropertyName("regions")]
        public List<string>? Regions { get; set; }
    }

    /// <summary>
    /// Request for getting brand hashtag content
    /// </summary>
    public class GetBrandHashtagContentRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        [Required]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// Requested fields
        /// </summary>
        [JsonPropertyName("fields")]
        public List<string>? Fields { get; set; }

        /// <summary>
        /// The field to sort the result by
        /// </summary>
        [JsonPropertyName("sort_field")]
        public MentionSortField? SortField { get; set; }

        /// <summary>
        /// Sorting order
        /// </summary>
        [JsonPropertyName("sort_order")]
        public MentionSortType? SortOrder { get; set; }

        /// <summary>
        /// The lookback period for the results, measured in days (1-90)
        /// </summary>
        [JsonPropertyName("number_of_days")]
        public int? NumberOfDays { get; set; }

        /// <summary>
        /// The regions of posts to filter by
        /// </summary>
        [JsonPropertyName("regions")]
        public List<string>? Regions { get; set; }

        /// <summary>
        /// Cursor for pagination
        /// </summary>
        [JsonPropertyName("cursor")]
        public int? Cursor { get; set; }

        /// <summary>
        /// The maximum number of hashtags to be returned for each page
        /// </summary>
        [JsonPropertyName("max_count")]
        public int? MaxCount { get; set; }
    }

    /// <summary>
    /// Request for getting valid brand hashtags
    /// </summary>
    public class GetValidBrandHashtagsRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        [Required]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// The username (handle) of the Business Account
        /// </summary>
        [JsonPropertyName("username")]
        [Required]
        public string Username { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for enabling brand hashtags
    /// </summary>
    public class EnableBrandHashtagsRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        [Required]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// The username (handle) of the Business Account
        /// </summary>
        [JsonPropertyName("username")]
        [Required]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// A list of hashtags to enable for the brand (max 10)
        /// </summary>
        [JsonPropertyName("hashtags")]
        [Required]
        public List<string> Hashtags { get; set; } = new();
    }

    /// <summary>
    /// Request for getting enabled hashtags
    /// </summary>
    public class GetEnabledHashtagsRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        [Required]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// The username (handle) of the Business Account
        /// </summary>
        [JsonPropertyName("username")]
        [Required]
        public string Username { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for deleting enabled hashtags
    /// </summary>
    public class DeleteEnabledHashtagsRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        [Required]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// The username (handle) of the Business Account
        /// </summary>
        [JsonPropertyName("username")]
        [Required]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// A hashtag to remove
        /// </summary>
        [JsonPropertyName("hashtag")]
        [Required]
        public string Hashtag { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for getting mentions in comments
    /// </summary>
    public class GetMentionsInCommentsRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        [Required]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// Requested fields
        /// </summary>
        [JsonPropertyName("fields")]
        public List<string>? Fields { get; set; }

        /// <summary>
        /// The field to sort the result by
        /// </summary>
        [JsonPropertyName("sort_field")]
        public CommentSortField? SortField { get; set; }

        /// <summary>
        /// Sorting order
        /// </summary>
        [JsonPropertyName("sort_type")]
        public MentionSortType? SortType { get; set; }

        /// <summary>
        /// The lookback period for the results, measured in days (1-90)
        /// </summary>
        [JsonPropertyName("number_of_days")]
        public int? NumberOfDays { get; set; }

        /// <summary>
        /// The regions of posts to filter by
        /// </summary>
        [JsonPropertyName("regions")]
        public List<string>? Regions { get; set; }

        /// <summary>
        /// Cursor for pagination
        /// </summary>
        [JsonPropertyName("cursor")]
        public int? Cursor { get; set; }

        /// <summary>
        /// The maximum number of comments to be returned for each page (1-100)
        /// </summary>
        [JsonPropertyName("max_count")]
        public int? MaxCount { get; set; }
    }

    /// <summary>
    /// Request for getting comment mention details
    /// </summary>
    public class GetCommentMentionDetailsRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        [Required]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the comment or reply that mentions a business
        /// </summary>
        [JsonPropertyName("comment_id")]
        [Required]
        public string CommentId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the post on which the comment was published
        /// </summary>
        [JsonPropertyName("item_id")]
        [Required]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// Requested fields
        /// </summary>
        [JsonPropertyName("fields")]
        public List<string>? Fields { get; set; }
    }

    #endregion

    #region Response Models

    /// <summary>
    /// Video/Post information for mentions
    /// </summary>
    public class MentionVideo
    {
        /// <summary>
        /// Unique identifier for the post
        /// </summary>
        [JsonPropertyName("item_id")]
        public string? ItemId { get; set; }

        /// <summary>
        /// Unix/epoch date-time when the post was posted
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// The generic URL of the post
        /// </summary>
        [JsonPropertyName("video_link")]
        public string? VideoLink { get; set; }

        /// <summary>
        /// Post description
        /// </summary>
        [JsonPropertyName("caption")]
        public string? Caption { get; set; }

        /// <summary>
        /// Total number of likes on the post
        /// </summary>
        [JsonPropertyName("likes")]
        public int? Likes { get; set; }

        /// <summary>
        /// Total number of comments on the post
        /// </summary>
        [JsonPropertyName("comments")]
        public int? Comments { get; set; }

        /// <summary>
        /// Total number of times the post has been shared
        /// </summary>
        [JsonPropertyName("shares")]
        public int? Shares { get; set; }

        /// <summary>
        /// The URL of the post thumbnail
        /// </summary>
        [JsonPropertyName("thumbnail_url")]
        public string? ThumbnailUrl { get; set; }

        /// <summary>
        /// Total number of views the post has received
        /// </summary>
        [JsonPropertyName("views")]
        public int? Views { get; set; }

        /// <summary>
        /// Total number of unique viewers who viewed the post
        /// </summary>
        [JsonPropertyName("reach")]
        public int? Reach { get; set; }

        /// <summary>
        /// The handle name of the creator who published the post
        /// </summary>
        [JsonPropertyName("creator_handle_name")]
        public string? CreatorHandleName { get; set; }

        /// <summary>
        /// A list of brand hashtags in the post caption that match the hashtags configured for the business
        /// </summary>
        [JsonPropertyName("matched_hashtags")]
        public List<string>? MatchedHashtags { get; set; }
    }

    /// <summary>
    /// Response for getting mentioned posts
    /// </summary>
    public class GetMentionedPostsResponse
    {
        /// <summary>
        /// Information about the mentioned posts
        /// </summary>
        [JsonPropertyName("videos")]
        public List<MentionVideo> Videos { get; set; } = new();

        /// <summary>
        /// Cursor for the next page of results
        /// </summary>
        [JsonPropertyName("cursor")]
        public int? Cursor { get; set; }

        /// <summary>
        /// Whether an additional page of data is available
        /// </summary>
        [JsonPropertyName("has_more")]
        public bool HasMore { get; set; }
    }

    /// <summary>
    /// Response for getting mentioned post details
    /// </summary>
    public class GetMentionedPostDetailsResponse
    {
        /// <summary>
        /// Information about the mentioned post
        /// </summary>
        [JsonPropertyName("video")]
        public MentionVideo? Video { get; set; }
    }

    /// <summary>
    /// Keyword result item
    /// </summary>
    public class KeywordResult
    {
        /// <summary>
        /// A word or phrase that appears frequently in the captions of mentioned posts
        /// </summary>
        [JsonPropertyName("word")]
        public string? Word { get; set; }

        /// <summary>
        /// The frequency of the word or phrase
        /// </summary>
        [JsonPropertyName("count")]
        public int Count { get; set; }
    }

    /// <summary>
    /// Response for getting top keywords
    /// </summary>
    public class GetTopKeywordsResponse
    {
        /// <summary>
        /// The results of the top 20 keywords in mentioned posts
        /// </summary>
        [JsonPropertyName("results")]
        public List<KeywordResult> Results { get; set; } = new();
    }

    /// <summary>
    /// Hashtag result item
    /// </summary>
    public class HashtagResult
    {
        /// <summary>
        /// A hashtag that appears frequently in the captions of mentioned posts
        /// </summary>
        [JsonPropertyName("hashtag")]
        public string? Hashtag { get; set; }

        /// <summary>
        /// The frequency of the hashtag
        /// </summary>
        [JsonPropertyName("count")]
        public int Count { get; set; }
    }

    /// <summary>
    /// Response for getting top hashtags
    /// </summary>
    public class GetTopHashtagsResponse
    {
        /// <summary>
        /// The results of the top 20 hashtags in the mentioned posts
        /// </summary>
        [JsonPropertyName("results")]
        public List<HashtagResult> Results { get; set; } = new();
    }

    /// <summary>
    /// Response for getting brand hashtag content
    /// </summary>
    public class GetBrandHashtagContentResponse
    {
        /// <summary>
        /// Information about the posts
        /// </summary>
        [JsonPropertyName("videos")]
        public List<MentionVideo> Videos { get; set; } = new();

        /// <summary>
        /// Cursor for the next page of results
        /// </summary>
        [JsonPropertyName("cursor")]
        public int? Cursor { get; set; }

        /// <summary>
        /// Whether an additional page of data is available
        /// </summary>
        [JsonPropertyName("has_more")]
        public bool HasMore { get; set; }
    }

    /// <summary>
    /// Brand hashtag item
    /// </summary>
    public class BrandHashtag
    {
        /// <summary>
        /// A valid hashtag that can be enabled for the Business Account
        /// </summary>
        [JsonPropertyName("hashtag")]
        public string? Hashtag { get; set; }

        /// <summary>
        /// The total number of posts containing the hashtag in the post caption in the last 90 days
        /// </summary>
        [JsonPropertyName("post_count")]
        public int? PostCount { get; set; }

        /// <summary>
        /// The total number of likes on posts containing the hashtag in the post caption in the last 90 days
        /// </summary>
        [JsonPropertyName("like_count")]
        public int? LikeCount { get; set; }

        /// <summary>
        /// The rank of the hashtag among all the valid hashtags for the Business Account
        /// </summary>
        [JsonPropertyName("mention_post_rank")]
        public int? MentionPostRank { get; set; }

        /// <summary>
        /// The date when the hashtag was enabled for the brand
        /// </summary>
        [JsonPropertyName("create_date")]
        public string? CreateDate { get; set; }
    }

    /// <summary>
    /// Response for getting valid brand hashtags
    /// </summary>
    public class GetValidBrandHashtagsResponse
    {
        /// <summary>
        /// The list of valid hashtags that can be enabled for the Business Account
        /// </summary>
        [JsonPropertyName("hashtag_list")]
        public List<BrandHashtag> HashtagList { get; set; } = new();
    }

    /// <summary>
    /// Response for enabling brand hashtags
    /// </summary>
    public class EnableBrandHashtagsResponse
    {
        /// <summary>
        /// The list of hashtags enabled for the Business Account
        /// </summary>
        [JsonPropertyName("hashtag_list")]
        public List<BrandHashtag> HashtagList { get; set; } = new();
    }

    /// <summary>
    /// Response for getting enabled hashtags
    /// </summary>
    public class GetEnabledHashtagsResponse
    {
        /// <summary>
        /// The list of hashtags enabled for the Business Account
        /// </summary>
        [JsonPropertyName("hashtag_list")]
        public List<BrandHashtag> HashtagList { get; set; } = new();
    }

    /// <summary>
    /// Response for deleting enabled hashtags
    /// </summary>
    public class DeleteEnabledHashtagsResponse
    {
        // Empty response body according to documentation
    }

    /// <summary>
    /// Comment information for mentions
    /// </summary>
    public class MentionComment
    {
        /// <summary>
        /// Unique identifier for the post
        /// </summary>
        [JsonPropertyName("item_id")]
        public string? ItemId { get; set; }

        /// <summary>
        /// The generic URL of the post
        /// </summary>
        [JsonPropertyName("video_link")]
        public string? VideoLink { get; set; }

        /// <summary>
        /// Post description
        /// </summary>
        [JsonPropertyName("caption")]
        public string? Caption { get; set; }

        /// <summary>
        /// Total number of likes on the post
        /// </summary>
        [JsonPropertyName("video_likes")]
        public int? VideoLikes { get; set; }

        /// <summary>
        /// The URL of the post thumbnail
        /// </summary>
        [JsonPropertyName("thumbnail_url")]
        public string? ThumbnailUrl { get; set; }

        /// <summary>
        /// The handle name of the user who made the comment
        /// </summary>
        [JsonPropertyName("commenter_display_name")]
        public string? CommenterDisplayName { get; set; }

        /// <summary>
        /// The handle name of the user who made the comment (from webhook)
        /// </summary>
        [JsonPropertyName("commenter_handle_name")]
        public string? CommenterHandleName { get; set; }

        /// <summary>
        /// Unique identifier for the comment
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string? CommentId { get; set; }

        /// <summary>
        /// The type of comment
        /// </summary>
        [JsonPropertyName("comment_type")]
        public string? CommentType { get; set; }

        /// <summary>
        /// The comment text that mentions the business's handle
        /// </summary>
        [JsonPropertyName("comment_text")]
        public string? CommentText { get; set; }

        /// <summary>
        /// Unix/epoch date-time when the comment was posted
        /// </summary>
        [JsonPropertyName("comment_create_time")]
        public string? CommentCreateTime { get; set; }

        /// <summary>
        /// Total number of likes for the comment
        /// </summary>
        [JsonPropertyName("comment_likes")]
        public int? CommentLikes { get; set; }
    }

    /// <summary>
    /// Response for getting mentions in comments
    /// </summary>
    public class GetMentionsInCommentsResponse
    {
        /// <summary>
        /// Information about the mentions in comments
        /// </summary>
        [JsonPropertyName("comments")]
        public List<MentionComment> Comments { get; set; } = new();

        /// <summary>
        /// Cursor for the next page of results
        /// </summary>
        [JsonPropertyName("cursor")]
        public int? Cursor { get; set; }

        /// <summary>
        /// Whether an additional page of data is available
        /// </summary>
        [JsonPropertyName("has_more")]
        public bool HasMore { get; set; }
    }

    /// <summary>
    /// Response for getting comment mention details
    /// </summary>
    public class GetCommentMentionDetailsResponse
    {
        /// <summary>
        /// Information about the mention in the comment
        /// </summary>
        [JsonPropertyName("comment")]
        public MentionComment? Comment { get; set; }
    }

    #endregion
}
