using System;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu báo cáo tích hợp AdAccount
    /// </summary>
    public interface IReportIntegratedAdAccountSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdAccount theo BC ID 
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> bắ<PERSON> đầu (tùy chọn)</param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<ReportIntegratedAdAccountSyncResult> SyncReportIntegratedAdAccountAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdAccount cho tất cả Business Centers với khoảng thời gian mặc định
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<ReportIntegratedAdAccountSyncResult> SyncAllReportIntegratedAdAccountForAllBcsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu báo cáo tích hợp AdAccount
    /// </summary>
    public class ReportIntegratedAdAccountSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số AdAccount đã đồng bộ
        /// </summary>
        public int AdAccountCount { get; set; }

        /// <summary>
        /// Số ngày đã đồng bộ
        /// </summary>
        public int DayCount { get; set; }

    }
} 