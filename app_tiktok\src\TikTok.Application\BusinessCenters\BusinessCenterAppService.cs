using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using TikTok.BusinessCenters;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.BusinessCenters
{
    /// <summary>
    /// Service quản lý trung tâm kinh doanh
    /// </summary>
    public class BusinessCenterAppService :
        CrudAppService<
            RawBusinessCenterEntity,                  // The business center entity
            BusinessCenterDto,                     // Used to show business centers
            Guid,                                  // Primary key of the business center entity
            GetBusinessCenterListDto,              // Used for paging/sorting
            CreateBusinessCenterDto,               // Used to create a new business center
            UpdateBusinessCenterDto>,              // Used to update a business center
        IBusinessCenterAppService
    {
        private readonly IBusinessCenterRepository _businessCenterRepository;

        public BusinessCenterAppService(IBusinessCenterRepository businessCenterRepository)
            : base(businessCenterRepository)
        {
            _businessCenterRepository = businessCenterRepository;

            GetPolicyName = TikTokPermissions.BusinessCenters.Default;
            GetListPolicyName = TikTokPermissions.BusinessCenters.Default;
            CreatePolicyName = TikTokPermissions.BusinessCenters.Create;
            UpdatePolicyName = TikTokPermissions.BusinessCenters.Edit;
            DeletePolicyName = TikTokPermissions.BusinessCenters.Delete;
        }

        protected override async Task<IQueryable<RawBusinessCenterEntity>> CreateFilteredQueryAsync(GetBusinessCenterListDto input)
        {
            // Sử dụng repository để lấy danh sách với filter
            var entities = await _businessCenterRepository.GetListAsync(
                sorting: input.Sorting,
                maxResultCount: input.MaxResultCount,
                skipCount: input.SkipCount,
                filter: input.Filter,
                bcId: input.BcId,
                name: input.Name,
                company: input.Company,
                currency: input.Currency,
                registeredArea: input.RegisteredArea,
                status: input.Status,
                timezone: input.Timezone,
                type: input.Type,
                userRole: input.UserRole,
                extUserFinanceRole: input.ExtUserFinanceRole,
                creationTimeFrom: input.CreationTimeFrom,
                creationTimeTo: input.CreationTimeTo
            );

            return entities.AsQueryable();
        }

        public async Task<BusinessCenterDto> GetByBcIdAsync(string bcId)
        {
            var entity = await _businessCenterRepository.GetByBcIdAsync(bcId);
            return ObjectMapper.Map<RawBusinessCenterEntity, BusinessCenterDto>(entity);
        }

        public async Task<bool> IsBcIdExistsAsync(string bcId)
        {
            return await _businessCenterRepository.IsBcIdExistsAsync(bcId);
        }

        public override Task<BusinessCenterDto> CreateAsync(CreateBusinessCenterDto input)
        {
            // Kiểm tra xem BC ID đã tồn tại chưa
            if (input.BcId != null && input.BcId.Length > 0)
            {
                var exists = _businessCenterRepository.IsBcIdExistsAsync(input.BcId).Result;
                if (exists)
                {
                    throw new BusinessException(HttpStatusCode.Conflict.ToString())
                        .WithData("bcId", input.BcId);
                }
            }

            return base.CreateAsync(input);
        }
    }
}