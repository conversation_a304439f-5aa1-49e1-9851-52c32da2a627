/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TikTokBusinessApi.Core.Resilience
{
    /// <summary>
    /// Default implementation of request throttler
    /// </summary>
    public class DefaultRequestThrottler : IRequestThrottler
    {
        private readonly RequestThrottlerOptions _options;
        private readonly ILogger<DefaultRequestThrottler>? _logger;
        private readonly ConcurrentQueue<RequestMetric> _requestMetrics = new();
        private readonly SemaphoreSlim _concurrencySemaphore;
        private readonly object _lock = new();
        
        private long _totalRequests;
        private long _throttledRequests;
        private double _currentRequestsPerSecond;
        private TimeSpan _averageResponseTime = TimeSpan.Zero;

        /// <summary>
        /// Initializes a new instance of the DefaultRequestThrottler class
        /// </summary>
        /// <param name="options">Request throttler options</param>
        /// <param name="logger">Logger instance</param>
        public DefaultRequestThrottler(RequestThrottlerOptions? options = null, ILogger<DefaultRequestThrottler>? logger = null)
        {
            _options = options ?? new RequestThrottlerOptions();
            _logger = logger;
            _concurrencySemaphore = new SemaphoreSlim(_options.MaxConcurrentRequests, _options.MaxConcurrentRequests);
        }

        /// <summary>
        /// Throttles a request based on current load and policies
        /// </summary>
        /// <param name="requestContext">Context information about the request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task that completes when request can proceed</returns>
        public async Task ThrottleAsync(RequestContext requestContext, CancellationToken cancellationToken = default)
        {
            var decision = ShouldThrottle(requestContext);
            
            if (!decision.ShouldThrottle)
            {
                // Wait for concurrency slot
                await _concurrencySemaphore.WaitAsync(cancellationToken);
                _logger?.LogDebug("Request {RequestId} allowed to proceed", requestContext.RequestId);
                return;
            }

            Interlocked.Increment(ref _throttledRequests);
            _logger?.LogDebug("Request {RequestId} throttled: {Reason}, delay: {Delay}ms", 
                requestContext.RequestId, decision.Reason, decision.Delay.TotalMilliseconds);

            if (decision.Delay > TimeSpan.Zero)
            {
                await Task.Delay(decision.Delay, cancellationToken);
            }

            // After delay, wait for concurrency slot
            await _concurrencySemaphore.WaitAsync(cancellationToken);
        }

        /// <summary>
        /// Checks if a request should be throttled without waiting
        /// </summary>
        /// <param name="requestContext">Context information about the request</param>
        /// <returns>Throttle decision</returns>
        public ThrottleDecision ShouldThrottle(RequestContext requestContext)
        {
            UpdateMetrics();

            // Check rate limit
            if (_currentRequestsPerSecond > _options.MaxRequestsPerSecond)
            {
                var delay = CalculateRateLimitDelay();
                return ThrottleDecision.Throttle(delay, "Rate limit exceeded");
            }

            // Check concurrency limit
            if (_concurrencySemaphore.CurrentCount <= 0)
            {
                var delay = TimeSpan.FromMilliseconds(100); // Small delay for concurrency
                return ThrottleDecision.Throttle(delay, "Concurrency limit exceeded");
            }

            // Check adaptive throttling based on response times
            if (_options.EnableAdaptiveThrottling && _averageResponseTime > _options.TargetResponseTime)
            {
                var delay = CalculateAdaptiveDelay();
                return ThrottleDecision.Throttle(delay, "Adaptive throttling due to high response times");
            }

            return ThrottleDecision.Allow();
        }

        /// <summary>
        /// Updates throttling metrics after a request completes
        /// </summary>
        /// <param name="requestContext">Context information about the request</param>
        /// <param name="responseTime">Time taken to complete the request</param>
        /// <param name="success">Whether the request was successful</param>
        public void RecordRequestCompletion(RequestContext requestContext, TimeSpan responseTime, bool success)
        {
            // Release concurrency slot
            _concurrencySemaphore.Release();

            // Record metrics
            var metric = new RequestMetric
            {
                Timestamp = DateTimeOffset.UtcNow,
                ResponseTime = responseTime,
                Success = success,
                RequestId = requestContext.RequestId
            };

            _requestMetrics.Enqueue(metric);
            Interlocked.Increment(ref _totalRequests);

            _logger?.LogDebug("Request {RequestId} completed in {ResponseTime}ms (success: {Success})", 
                requestContext.RequestId, responseTime.TotalMilliseconds, success);

            UpdateMetrics();
        }

        /// <summary>
        /// Gets current throttling metrics
        /// </summary>
        public ThrottlingMetrics GetMetrics()
        {
            UpdateMetrics();

            return new ThrottlingMetrics
            {
                TotalRequests = _totalRequests,
                ThrottledRequests = _throttledRequests,
                CurrentRequestsPerSecond = _currentRequestsPerSecond,
                AverageResponseTime = _averageResponseTime,
                QueueLength = Math.Max(0, _options.MaxConcurrentRequests - _concurrencySemaphore.CurrentCount)
            };
        }

        /// <summary>
        /// Resets throttling state
        /// </summary>
        public void Reset()
        {
            lock (_lock)
            {
                _requestMetrics.Clear();
                _totalRequests = 0;
                _throttledRequests = 0;
                _currentRequestsPerSecond = 0;
                _averageResponseTime = TimeSpan.Zero;
                
                // Reset semaphore
                while (_concurrencySemaphore.CurrentCount < _options.MaxConcurrentRequests)
                {
                    _concurrencySemaphore.Release();
                }

                _logger?.LogDebug("Request throttler reset");
            }
        }

        private void UpdateMetrics()
        {
            lock (_lock)
            {
                var now = DateTimeOffset.UtcNow;
                var oneSecondAgo = now.AddSeconds(-1);
                var oneMinuteAgo = now.AddMinutes(-1);

                // Clean up old metrics
                while (_requestMetrics.TryPeek(out var oldMetric) && oldMetric.Timestamp < oneMinuteAgo)
                {
                    _requestMetrics.TryDequeue(out _);
                }

                var recentMetrics = _requestMetrics.Where(m => m.Timestamp >= oneSecondAgo).ToList();
                var allMetrics = _requestMetrics.ToList();

                // Calculate requests per second
                _currentRequestsPerSecond = recentMetrics.Count;

                // Calculate average response time
                if (allMetrics.Any())
                {
                    _averageResponseTime = TimeSpan.FromMilliseconds(
                        allMetrics.Average(m => m.ResponseTime.TotalMilliseconds));
                }
            }
        }

        private TimeSpan CalculateRateLimitDelay()
        {
            var excessRate = _currentRequestsPerSecond - _options.MaxRequestsPerSecond;
            var delayMs = Math.Min(excessRate * 100, _options.MaxThrottleDelay.TotalMilliseconds);
            return TimeSpan.FromMilliseconds(delayMs);
        }

        private TimeSpan CalculateAdaptiveDelay()
        {
            var responseTimeRatio = _averageResponseTime.TotalMilliseconds / _options.TargetResponseTime.TotalMilliseconds;
            var delayMs = Math.Min((responseTimeRatio - 1) * 500, _options.MaxThrottleDelay.TotalMilliseconds);
            return TimeSpan.FromMilliseconds(Math.Max(0, delayMs));
        }

        /// <summary>
        /// Disposes the request throttler
        /// </summary>
        public void Dispose()
        {
            _concurrencySemaphore?.Dispose();
        }

        private class RequestMetric
        {
            public DateTimeOffset Timestamp { get; set; }
            public TimeSpan ResponseTime { get; set; }
            public bool Success { get; set; }
            public string RequestId { get; set; } = string.Empty;
        }
    }

    /// <summary>
    /// No-operation request throttler that allows all requests
    /// </summary>
    public class NoOpRequestThrottler : IRequestThrottler
    {
        /// <summary>
        /// Always returns immediately
        /// </summary>
        public Task ThrottleAsync(RequestContext requestContext, CancellationToken cancellationToken = default) 
            => Task.CompletedTask;

        /// <summary>
        /// Always allows requests
        /// </summary>
        public ThrottleDecision ShouldThrottle(RequestContext requestContext) => ThrottleDecision.Allow();

        /// <summary>
        /// No-op completion recording
        /// </summary>
        public void RecordRequestCompletion(RequestContext requestContext, TimeSpan responseTime, bool success) { }

        /// <summary>
        /// Returns empty metrics
        /// </summary>
        public ThrottlingMetrics GetMetrics() => new();

        /// <summary>
        /// No-op reset
        /// </summary>
        public void Reset() { }
    }
}
