/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.IO;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Music operations
    /// </summary>
    public interface IMusicApi
    {
        // /// <summary>
        // /// Upload a piece of music and get the music ID, to add music to the Liked list or History list, or to remove music from the Liked list
        // /// </summary>
        // /// <param name="request">Music upload request parameters</param>
        // /// <param name="fileStream">Music file stream (required when upload_type is UPLOAD_BY_FILE or not specified)</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing music upload information</returns>
        // Task<MusicUploadResponse> UploadMusicAsync(
        //     MusicUploadRequest request,
        //     Stream? fileStream = null,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the list of available music that can be used for video creation or Carousel Ads creation
        /// </summary>
        /// <param name="request">Music get request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing music list information</returns>
        Task<MusicGetResponse> GetMusicListAsync(
            MusicGetRequest request,
            CancellationToken cancellationToken = default);
    }
}
