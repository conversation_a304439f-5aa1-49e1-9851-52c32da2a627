/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for updating webhook configuration
    /// </summary>
    public class WebhookUpdateRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Webhook URL endpoint
        /// </summary>
        [JsonPropertyName("webhook_url")]
        public string? WebhookUrl { get; set; }

        /// <summary>
        /// List of event types to subscribe to
        /// </summary>
        [JsonPropertyName("event_types")]
        public List<string>? EventTypes { get; set; }

        /// <summary>
        /// Webhook secret for verification
        /// </summary>
        [JsonPropertyName("webhook_secret")]
        public string? WebhookSecret { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"WebhookUpdateRequest(BusinessId={BusinessId}, WebhookUrl={WebhookUrl})";
        }
    }

    /// <summary>
    /// Response data for webhook list API
    /// </summary>
    public class WebhookListResponse
    {
        /// <summary>
        /// List of webhook configurations
        /// </summary>
        [JsonPropertyName("webhooks")]
        public List<WebhookData>? Webhooks { get; set; }

        /// <summary>
        /// Total count of webhooks
        /// </summary>
        [JsonPropertyName("total_count")]
        public int? TotalCount { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"WebhookListResponse(Webhooks={Webhooks?.Count}, TotalCount={TotalCount})";
        }
    }

    /// <summary>
    /// Webhook configuration data
    /// </summary>
    public class WebhookData
    {
        /// <summary>
        /// Webhook ID
        /// </summary>
        [JsonPropertyName("webhook_id")]
        public string? WebhookId { get; set; }

        /// <summary>
        /// Business ID
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Webhook URL endpoint
        /// </summary>
        [JsonPropertyName("webhook_url")]
        public string? WebhookUrl { get; set; }

        /// <summary>
        /// List of subscribed event types
        /// </summary>
        [JsonPropertyName("event_types")]
        public List<string>? EventTypes { get; set; }

        /// <summary>
        /// Webhook status
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Date when webhook was created
        /// </summary>
        [JsonPropertyName("created_time")]
        public string? CreatedTime { get; set; }

        /// <summary>
        /// Date when webhook was last updated
        /// </summary>
        [JsonPropertyName("updated_time")]
        public string? UpdatedTime { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"WebhookData(WebhookId={WebhookId}, WebhookUrl={WebhookUrl}, Status={Status})";
        }
    }

    /// <summary>
    /// Request body for deleting a webhook
    /// </summary>
    public class WebhookDeleteRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Webhook ID to delete
        /// </summary>
        [JsonPropertyName("webhook_id")]
        public string? WebhookId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"WebhookDeleteRequest(BusinessId={BusinessId}, WebhookId={WebhookId})";
        }
    }

    /// <summary>
    /// Webhook event types enumeration
    /// </summary>
    public static class WebhookEventType
    {
        public const string CommentCreated = "comment.created";
        public const string CommentDeleted = "comment.deleted";
        public const string VideoPublished = "video.published";
        public const string VideoDeleted = "video.deleted";
        public const string PhotoPublished = "photo.published";
        public const string PhotoDeleted = "photo.deleted";
        public const string LiveStarted = "live.started";
        public const string LiveEnded = "live.ended";
        public const string FollowerGained = "follower.gained";
        public const string FollowerLost = "follower.lost";
    }

    /// <summary>
    /// Webhook status enumeration
    /// </summary>
    public static class WebhookStatus
    {
        public const string Active = "ACTIVE";
        public const string Inactive = "INACTIVE";
        public const string Pending = "PENDING";
        public const string Failed = "FAILED";
    }

    /// <summary>
    /// Response for successful operations that don't return specific data
    /// </summary>
    public class SuccessResponse
    {
        /// <summary>
        /// Operation status
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Success message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"SuccessResponse(Status={Status}, Message={Message})";
        }
    }
}
