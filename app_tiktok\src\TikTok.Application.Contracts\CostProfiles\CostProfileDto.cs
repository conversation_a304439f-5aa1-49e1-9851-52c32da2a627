using System;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.CostProfiles
{
    /// <summary>
    /// DTO cho hồ sơ chi phí của tài khoản quảng cáo
    /// </summary>
    public class CostProfileDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID nhà quảng cáo
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// Tên nhà quảng cáo
        /// </summary>
        public string AdvertiserName { get; set; }

        /// <summary>
        /// Tổng số tiền chi phí cho nhà quảng cáo
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Số tiền chi phí tiền mặt cho nhà quảng cáo
        /// </summary>
        public decimal CashAmount { get; set; }

        /// <summary>
        /// Số tiền chi phí tín dụng quảng cáo cho nhà quảng cáo
        /// </summary>
        public decimal GrantAmount { get; set; }

        /// <summary>
        /// Số tiền thuế ước tính cho nhà quảng cáo
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// Tiền tệ của nhà quảng cáo
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// ID của Business Center
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// Thời gian đồng bộ dữ liệu (UTC) - lưu đầy đủ thời gian
        /// </summary>
        public DateTime Date { get; set; }
    }
}