﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace TikTok.DimBusinessCenters
{
    public class DimBusinessCenterDto: AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID duy nhất của Business Center (Business Key)
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// Tên Business Center
        /// </summary>
        public string BcName { get; set; }

        /// <summary>
        /// Tên công ty
        /// </summary>
        public string Company { get; set; }

        /// <summary>
        /// Đơn vị tiền tệ
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Khu vực đăng ký
        /// </summary>
        public string RegisteredArea { get; set; }

        /// <summary>
        /// Trạng thái Business Center
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Múi giờ
        /// </summary>
        public string Timezone { get; set; }

        /// <summary>
        /// Loại Business Center
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Vai trò người dùng
        /// </summary>
        public string UserRole { get; set; }

        /// <summary>
        /// Vai trò tài chính mở rộng
        /// </summary>
        public string? ExtUserFinanceRole { get; set; }

        /// <summary>
        /// Record hiện tại (SCD Type 2)
        /// </summary>
        public bool IsCurrent { get; set; }

        /// <summary>
        /// Ngày hiệu lực
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Ngày hết hiệu lực
        /// </summary>
        public DateTime? ExpirationDate { get; set; }

        /// <summary>
        /// Phiên bản bản ghi (SCD2)
        /// </summary>
        public int RowVersion { get; set; }
    }
}
