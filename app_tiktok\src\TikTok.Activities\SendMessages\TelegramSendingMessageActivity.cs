﻿using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using TikTok.Activities.BaseMessages;
using TikTok.Application.Contracts.MessageProviders;
using TikTok.BaseMessages;
namespace TikTok.Activities.SendMessages
{
    [Activity(Category = "Messages", DisplayName = "Thực hiện gửi thông báo Telegram", Outcomes = new[] { OutcomeNames.Done })]
    public class TelegramSendingMessageActivity : UnitOfWorkActivity
    {
        private readonly IEnumerable<IMessageProvider> _messageProviders;
        private readonly ILogger<TelegramSendingMessageActivity> _logger;
        public TelegramSendingMessageActivity(IEnumerable<IMessageProvider> messageProvider
            , ILogger<TelegramSendingMessageActivity> logger)
        {
            _messageProviders = messageProvider;
            _logger = logger;
        }

        [ActivityInput(Name = "Message Input", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public BaseMessageActivityOutput Input { get; set; }

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            if (Input == null)
            {
                // Add debugging to see what we're actually receiving
                _logger.LogDebug("Context input type: {Type}", context.Input?.GetType().Name);
                _logger.LogDebug("Context input: {Input}", JsonConvert.SerializeObject(context.Input));

                if (context.Input is BaseMessageActivityOutput baseOutput)
                {
                    // Convert BaseMessageActivityOutput to SendingMessageActivityInput
                    Input = new SendingMessageActivityInput
                    {
                        OwnerId = baseOutput.OwnerId,
                        PhoneNumber = baseOutput.PhoneNumber,
                        Content = baseOutput.Content
                    };
                    _logger.LogDebug("Successfully converted BaseMessageActivityOutput to SendingMessageActivityInput");
                }
                else
                {
                    // Try direct cast as fallback
                    Input = context.Input as SendingMessageActivityInput;
                    if (Input == null)
                    {
                        _logger.LogError("Failed to convert context.Input to SendingMessageActivityInput. Type: {Type}", context.Input?.GetType().Name);
                    }
                }
            }

            if (_messageProviders.Any())
            {
                // Get TelegramMessageProvider from _messageProviders
                var telegramProvider = _messageProviders.FirstOrDefault(p =>
                    p.Name.Equals("Telegram", StringComparison.OrdinalIgnoreCase));

                if (telegramProvider != null && telegramProvider.IsEnabled)
                {
                    var message = Input.Content;
                    _logger.LogDebug("Sending Telegram message: {Message}", message);
                    // await telegramProvider.SendMessageAsync(message);

                    return Done();
                }
                else
                {
                    // Log warning if Telegram provider is not found or disabled
                    return Done();
                }
            }

            return Done();
        }
    }
}
