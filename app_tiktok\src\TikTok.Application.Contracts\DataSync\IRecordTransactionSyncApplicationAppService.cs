using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// Application App Service interface cho việc đồng bộ dữ liệu bản ghi giao dịch BC và AdAccount
    /// </summary>
    public interface IRecordTransactionSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch BC theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<RecordTransactionSyncResult> SyncBcTransactionsAsync(string bcId);

        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch AdAccount theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<RecordTransactionSyncResult> SyncAdAccountTransactionsAsync(string bcId);

        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<RecordTransactionSyncResult> SyncAllRecordTransactionsAsync();
    }
}