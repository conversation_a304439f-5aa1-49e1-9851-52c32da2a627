using System.Collections.Generic;
using TikTok.Enums;

namespace TikTok.Application.Contracts.MessageProviders
{
    /// <summary>
    /// DTO cho việc gửi thông báo
    /// </summary>
    public class SendNotificationDto
    {
        /// <summary>
        /// ID Business Center
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// Danh sách AdvertiserIds
        /// </summary>
        public List<string> AdvertiserIds { get; set; }

        /// <summary>
        /// Danh sách AdvertiserIds
        /// </summary>
        public List<string> ObjectIds { get; set; }

        /// <summary>
        /// Dữ liệu payload
        /// </summary>
        public string Payload { get; set; }

        /// <summary>
        /// Loại thông báo
        /// </summary>
        public NotificationType NotificationType { get; set; }

        /// <summary>
        /// Constructor mặc định
        /// </summary>
        public SendNotificationDto()
        {
            AdvertiserIds = new List<string>();
            ObjectIds = new List<string>();
        }

        /// <summary>
        /// Constructor với tham số cơ bản
        /// </summary>
        /// <param name="bcId">ID Business Center</param>
        /// <param name="advertiserIds">Danh sách AdvertiserIds</param>
        /// <param name="notificationType">Loại thông báo</param>
        public SendNotificationDto(string bcId, List<string> advertiserIds, NotificationType notificationType)
        {
            BcId = bcId;
            AdvertiserIds = advertiserIds ?? new List<string>();
            NotificationType = notificationType;
        }

        /// <summary>
        /// Tạo SendNotificationDto cho thông báo số dư
        /// </summary>
        /// <param name="bcId">ID Business Center</param>
        /// <param name="advertiserIds">Danh sách AdvertiserIds</param>
        /// <param name="payload">Dữ liệu payload</param>
        /// <returns>SendNotificationDto cho thông báo số dư</returns>
        public static SendNotificationDto CreateBalanceNotification(string bcId, List<string> advertiserIds, string payload = null)
        {
            return new SendNotificationDto
            {
                BcId = bcId,
                AdvertiserIds = advertiserIds ?? new List<string>(),
                NotificationType = NotificationType.Balance,
                Payload = payload
            };
        }

        /// <summary>
        /// Tạo SendNotificationDto cho thông báo giao dịch mới
        /// </summary>
        /// <param name="bcId">ID Business Center</param>
        /// <param name="transactionIds">Danh sách transactionIds</param>
        /// <param name="payload">Dữ liệu payload</param>
        /// <returns>SendNotificationDto cho thông báo giao dịch</returns>
        public static SendNotificationDto CreateTransactionNotification(string bcId, List<string> transactionIds, string payload = null)
        {
            return new SendNotificationDto
            {
                BcId = bcId,
                ObjectIds = transactionIds ?? new List<string>(),
                NotificationType = NotificationType.Transaction,
                Payload = payload
            };
        }
    }
}
