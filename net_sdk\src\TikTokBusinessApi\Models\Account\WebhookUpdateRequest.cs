/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for creating or updating webhook configurations
    /// </summary>
    public class WebhookUpdateRequest
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// The webhook URL that will receive the webhook events
        /// </summary>
        [JsonPropertyName("webhook_url")]
        public string WebhookUrl { get; set; } = string.Empty;

        /// <summary>
        /// List of webhook event types that you want to subscribe to
        /// Enum values: ADVERTISER_ACCOUNT_CREATION, ADVERTISER_ACCOUNT_UPDATE, ADVERTISER_ACCOUNT_DELETION, 
        /// CAMPAIGN_CREATION, CAMPAIGN_UPDATE, CAMPAIGN_DELETION, ADGROUP_CREATION, ADGROUP_UPDATE, ADGROUP_DELETION,
        /// AD_CREATION, AD_UPDATE, AD_DELETION, COMMENT_CREATION, COMMENT_UPDATE, COMMENT_DELETION
        /// </summary>
        [JsonPropertyName("event_types")]
        public List<string> EventTypes { get; set; } = new List<string>();
    }
}
