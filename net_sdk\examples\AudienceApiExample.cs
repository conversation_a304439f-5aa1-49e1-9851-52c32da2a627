/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the AudienceApi
    /// </summary>
    public class AudienceApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public AudienceApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example of uploading an audience file
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="filePath">Path to the CSV file containing audience data</param>
        /// <returns>Task representing the async operation</returns>
        public async Task UploadAudienceFileExample(string advertiserId, string filePath)
        {
            try
            {
                Console.WriteLine("=== Upload Audience File Example ===");

                // Calculate MD5 hash of the file
                string fileSignature;
                using (var md5 = MD5.Create())
                {
                    using var stream = File.OpenRead(filePath);
                    var hash = md5.ComputeHash(stream);
                    fileSignature = Convert.ToHexString(hash).ToLower();
                }

                var request = new AudienceFileUploadRequest
                {
                    AdvertiserId = advertiserId,
                    FileSignature = fileSignature,
                    CalculateType = "EMAIL_SHA256" // Assuming email addresses are SHA256 hashed
                };

                using var fileStream = File.OpenRead(filePath);
                var fileName = Path.GetFileName(filePath);

                Console.WriteLine($"Uploading file: {fileName}");
                Console.WriteLine($"File signature: {fileSignature}");

                var response = await _client.Audience.UploadAudienceFileAsync(
                    _client.AccessToken,
                    request,
                    fileStream,
                    fileName);

                if (response.Data != null)
                {
                    Console.WriteLine($"File uploaded successfully!");
                    Console.WriteLine($"File path: {response.Data.FilePath}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of creating an audience from uploaded files
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="audienceName">Name for the new audience</param>
        /// <param name="filePaths">List of file paths from upload response</param>
        /// <returns>Task representing the async operation</returns>
        public async Task CreateAudienceExample(string advertiserId, string audienceName, List<string> filePaths)
        {
            try
            {
                Console.WriteLine("=== Create Audience Example ===");

                var request = new AudienceCreateRequest
                {
                    AdvertiserId = advertiserId,
                    CustomAudienceName = audienceName,
                    CalculateType = "EMAIL_SHA256",
                    FilePaths = filePaths,
                    RetentionInDays = 180, // Optional: audience retention period
                    AudienceEnhancement = true // Optional: enable audience enhancement
                };

                Console.WriteLine($"Creating audience: {audienceName}");
                Console.WriteLine($"File paths: {string.Join(", ", filePaths)}");

                var response = await _client.Audience.CreateAudienceAsync(
                    _client.AccessToken,
                    request);

                if (response.Data != null)
                {
                    Console.WriteLine($"Audience created successfully!");
                    Console.WriteLine($"Audience ID: {response.Data.CustomAudienceId}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting the list of audiences
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task GetAudienceListExample(string advertiserId)
        {
            try
            {
                Console.WriteLine("=== Get Audience List Example ===");

                var request = new AudienceListRequest
                {
                    AdvertiserId = advertiserId,
                    Page = 1,
                    PageSize = 20
                };

                Console.WriteLine($"Getting audiences for advertiser: {advertiserId}");

                var response = await _client.Audience.GetAudienceListAsync(
                    _client.AccessToken,
                    request);

                if (response.Data?.List != null)
                {
                    Console.WriteLine($"Found {response.Data.List.Count} audiences:");
                    
                    foreach (var audience in response.Data.List)
                    {
                        Console.WriteLine($"- ID: {audience.CustomAudienceId}");
                        Console.WriteLine($"  Name: {audience.CustomAudienceName}");
                        Console.WriteLine($"  Type: {audience.AudienceType}");
                        Console.WriteLine($"  Size: {audience.AudienceSize:N0}");
                        Console.WriteLine($"  Status: {audience.AudienceStatus}");
                        Console.WriteLine($"  Created: {audience.CreateTime}");
                        Console.WriteLine($"  Is Creator: {audience.IsCreator}");
                        Console.WriteLine();
                    }

                    if (response.Data.PageInfo != null)
                    {
                        Console.WriteLine($"Page: {response.Data.PageInfo.Page}");
                        Console.WriteLine($"Total Count: {response.Data.PageInfo.TotalNumber}");
                    }
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting detailed audience information
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="audienceIds">List of audience IDs to get details for</param>
        /// <returns>Task representing the async operation</returns>
        public async Task GetAudienceDetailsExample(string advertiserId, List<string> audienceIds)
        {
            try
            {
                Console.WriteLine("=== Get Audience Details Example ===");

                var request = new AudienceGetRequest
                {
                    AdvertiserId = advertiserId,
                    CustomAudienceIds = audienceIds
                };

                Console.WriteLine($"Getting details for audiences: {string.Join(", ", audienceIds)}");

                var response = await _client.Audience.GetAudienceAsync(
                    _client.AccessToken,
                    request);

                if (response.Data?.List != null)
                {
                    Console.WriteLine($"Found details for {response.Data.List.Count} audiences:");
                    
                    foreach (var audience in response.Data.List)
                    {
                        Console.WriteLine($"- ID: {audience.CustomAudienceId}");
                        Console.WriteLine($"  Name: {audience.CustomAudienceName}");
                        Console.WriteLine($"  Type: {audience.AudienceType}");
                        Console.WriteLine($"  Sub Type: {audience.AudienceSubType}");
                        Console.WriteLine($"  Size: {audience.AudienceSize:N0}");
                        Console.WriteLine($"  Status: {audience.AudienceStatus}");
                        Console.WriteLine($"  Created: {audience.CreateTime}");
                        Console.WriteLine($"  Expires: {audience.ExpireTime}");
                        Console.WriteLine($"  Is Creator: {audience.IsCreator}");

                        if (audience.ModifyRecords != null && audience.ModifyRecords.Count > 0)
                        {
                            Console.WriteLine($"  Modification Records:");
                            foreach (var record in audience.ModifyRecords)
                            {
                                Console.WriteLine($"    - {record.ModifyTime}: {record.OperationType} ({record.OperationStatus})");
                            }
                        }
                        Console.WriteLine();
                    }
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of sharing audiences with other advertisers
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="audienceIds">List of audience IDs to share</param>
        /// <param name="sharedAdvertiserIds">List of advertiser IDs to share with</param>
        /// <returns>Task representing the async operation</returns>
        public async Task ShareAudienceExample(string advertiserId, List<string> audienceIds, List<string> sharedAdvertiserIds)
        {
            try
            {
                Console.WriteLine("=== Share Audience Example ===");

                var request = new AudienceShareRequest
                {
                    AdvertiserId = advertiserId,
                    CustomAudienceIds = audienceIds,
                    SharedAdvertiserIds = sharedAdvertiserIds
                };

                Console.WriteLine($"Sharing audiences {string.Join(", ", audienceIds)} with advertisers {string.Join(", ", sharedAdvertiserIds)}");

                var response = await _client.Audience.ShareAudienceAsync(
                    _client.AccessToken,
                    request);

                if (response.Data != null)
                {
                    Console.WriteLine($"Audience sharing completed!");
                    Console.WriteLine($"Success: {response.Data.Success}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of applying audiences to ad groups
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="audienceId">Audience ID to apply</param>
        /// <param name="adGroupIds">List of ad group IDs to apply the audience to</param>
        /// <returns>Task representing the async operation</returns>
        public async Task ApplyAudienceExample(string advertiserId, string audienceId, List<string> adGroupIds)
        {
            try
            {
                Console.WriteLine("=== Apply Audience Example ===");

                var request = new AudienceApplyRequest
                {
                    AdvertiserId = advertiserId,
                    CustomAudienceId = audienceId,
                    AdgroupIds = adGroupIds,
                    ActionMode = "APPLY", // or "DISCONNECT" to remove
                    UsageMode = "INCLUDE" // Optional: INCLUDE or EXCLUDE
                };

                Console.WriteLine($"Applying audience {audienceId} to ad groups {string.Join(", ", adGroupIds)}");

                var response = await _client.Audience.ApplyAudienceAsync(
                    _client.AccessToken,
                    request);

                if (response.Data != null)
                {
                    Console.WriteLine($"Audience application completed!");
                    Console.WriteLine($"Success: {response.Data.Success}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of deleting audiences
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="audienceIds">List of audience IDs to delete</param>
        /// <returns>Task representing the async operation</returns>
        public async Task DeleteAudienceExample(string advertiserId, List<string> audienceIds)
        {
            try
            {
                Console.WriteLine("=== Delete Audience Example ===");

                var request = new AudienceDeleteRequest
                {
                    AdvertiserId = advertiserId,
                    CustomAudienceIds = audienceIds
                };

                Console.WriteLine($"Deleting audiences: {string.Join(", ", audienceIds)}");

                var response = await _client.Audience.DeleteAudienceAsync(
                    _client.AccessToken,
                    request);

                if (response.Data != null)
                {
                    Console.WriteLine($"Audience deletion completed!");
                    Console.WriteLine($"Success: {response.Data.Success}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Utility method to calculate MD5 hash of a file
        /// </summary>
        /// <param name="filePath">Path to the file</param>
        /// <returns>MD5 hash as lowercase hex string</returns>
        public static string CalculateFileMD5(string filePath)
        {
            using var md5 = MD5.Create();
            using var stream = File.OpenRead(filePath);
            var hash = md5.ComputeHash(stream);
            return Convert.ToHexString(hash).ToLower();
        }

        /// <summary>
        /// Utility method to hash email addresses with SHA256
        /// </summary>
        /// <param name="email">Email address to hash</param>
        /// <returns>SHA256 hash as lowercase hex string</returns>
        public static string HashEmailSHA256(string email)
        {
            using var sha256 = SHA256.Create();
            var bytes = Encoding.UTF8.GetBytes(email.ToLower().Trim());
            var hash = sha256.ComputeHash(bytes);
            return Convert.ToHexString(hash).ToLower();
        }
    }

    /// <summary>
    /// Program entry point for running the examples
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Replace with your actual access token and advertiser ID
            const string accessToken = "your-access-token";
            const string advertiserId = "your-advertiser-id";
            
            var example = new AudienceApiExample(accessToken);
            
            try
            {
                Console.WriteLine("=== TikTok Business API - Audience Examples ===\n");
                
                // Example 1: Upload audience file
                // await example.UploadAudienceFileExample(advertiserId, "path/to/your/audience.csv");
                
                // Example 2: Create audience from uploaded files
                // var filePaths = new List<string> { "file_path_from_upload_response" };
                // await example.CreateAudienceExample(advertiserId, "My Custom Audience", filePaths);
                
                // Example 3: Get audience list
                await example.GetAudienceListExample(advertiserId);
                
                // Example 4: Get audience details
                // var audienceIds = new List<string> { "audience_id_1", "audience_id_2" };
                // await example.GetAudienceDetailsExample(advertiserId, audienceIds);
                
                // Example 5: Share audiences
                // var audienceIds = new List<string> { "audience_id_1" };
                // var sharedAdvertiserIds = new List<string> { "shared_advertiser_id" };
                // await example.ShareAudienceExample(advertiserId, audienceIds, sharedAdvertiserIds);
                
                // Example 6: Apply audience to ad groups
                // var adGroupIds = new List<string> { "adgroup_id_1", "adgroup_id_2" };
                // await example.ApplyAudienceExample(advertiserId, "audience_id", adGroupIds);
                
                // Example 7: Delete audiences
                // var audienceIds = new List<string> { "audience_id_1", "audience_id_2" };
                // await example.DeleteAudienceExample(advertiserId, audienceIds);
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Example execution failed: {ex.Message}");
            }
        }
    }
}
