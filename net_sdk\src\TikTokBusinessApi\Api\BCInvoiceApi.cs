/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API BC Invoice operations
    /// </summary>
    public class BCInvoiceApi : IBCInvoiceApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<BCInvoiceApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the BCInvoiceApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public BCInvoiceApi(IApiClient apiClient, ILogger<BCInvoiceApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get invoices of a Business Center account
        /// </summary>
        /// <param name="request">Get invoices request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing invoice information</returns>
        public async Task<GetInvoicesResponse> GetInvoicesAsync(
            GetInvoicesRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BcId))
                throw new ArgumentException("BcId is required", nameof(request));

            if (request.InvoiceTypes == null || request.InvoiceTypes.Count == 0)
                throw new ArgumentException("InvoiceTypes is required", nameof(request));

            _logger?.LogDebug("Getting invoices for BC ID: {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["invoice_types"] = string.Join(",", request.InvoiceTypes)
            };

            if (!string.IsNullOrEmpty(request.InvoiceId))
                queryParams["invoice_id"] = request.InvoiceId;

            if (!string.IsNullOrEmpty(request.InvoiceTitle))
                queryParams["invoice_title"] = request.InvoiceTitle;

            if (request.PayStatuses != null && request.PayStatuses.Count > 0)
                queryParams["pay_statuses"] = string.Join(",", request.PayStatuses);

            if (!string.IsNullOrEmpty(request.StartTime))
                queryParams["start_time"] = request.StartTime;

            if (!string.IsNullOrEmpty(request.EndTime))
                queryParams["end_time"] = request.EndTime;

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();

            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetInvoicesResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCInvoiceEndpoints.GetInvoices}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get total unpaid amount of a Business Center account
        /// </summary>
        /// <param name="request">Get unpaid amount request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing unpaid amount information</returns>
        public async Task<GetUnpaidAmountResponse> GetUnpaidAmountAsync(
            GetUnpaidAmountRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BcId))
                throw new ArgumentException("BcId is required", nameof(request));

            if (string.IsNullOrEmpty(request.InvoiceType))
                throw new ArgumentException("InvoiceType is required", nameof(request));

            _logger?.LogDebug("Getting unpaid amount for BC ID: {BcId}, Invoice Type: {InvoiceType}", 
                request.BcId, request.InvoiceType);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["invoice_type"] = request.InvoiceType
            };

            return await _apiClient.CallApiAsync<GetUnpaidAmountResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCInvoiceEndpoints.GetUnpaidAmount}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Download an individual invoice synchronously
        /// </summary>
        /// <param name="request">Download invoice request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>PDF file stream as byte array</returns>
        public async Task<byte[]> DownloadInvoiceAsync(
            DownloadInvoiceRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BcId))
                throw new ArgumentException("BcId is required", nameof(request));

            if (string.IsNullOrEmpty(request.InvoiceId))
                throw new ArgumentException("InvoiceId is required", nameof(request));

            _logger?.LogDebug("Downloading invoice for BC ID: {BcId}, Invoice ID: {InvoiceId}", 
                request.BcId, request.InvoiceId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["invoice_id"] = request.InvoiceId
            };

            return await _apiClient.CallApiRawAsync(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCInvoiceEndpoints.DownloadInvoice}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authenticationContext: null,
                cancellationToken: cancellationToken);
        }

        // /// <summary>
        // /// Create an asynchronous download task
        // /// </summary>
        // /// <param name="request">Create download task request</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing task ID</returns>
        // public async Task<CreateDownloadTaskResponse> CreateDownloadTaskAsync(
        //     CreateDownloadTaskRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.BcId))
        //         throw new ArgumentException("BcId is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.DownloadType))
        //         throw new ArgumentException("DownloadType is required", nameof(request));

        //     _logger?.LogDebug("Creating download task for BC ID: {BcId}, Download Type: {DownloadType}", 
        //         request.BcId, request.DownloadType);

        //     return await _apiClient.CallApiAsync<CreateDownloadTaskResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCInvoiceEndpoints.CreateDownloadTask}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get asynchronous download task (BILLING_REPORT)
        /// </summary>
        /// <param name="request">Get download task request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing download task information</returns>
        public async Task<GetDownloadTaskResponse> GetDownloadTaskAsync(
            GetDownloadTaskRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BcId))
                throw new ArgumentException("BcId is required", nameof(request));

            if (string.IsNullOrEmpty(request.TaskId))
                throw new ArgumentException("TaskId is required", nameof(request));

            _logger?.LogDebug("Getting download task for BC ID: {BcId}, Task ID: {TaskId}", 
                request.BcId, request.TaskId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["task_id"] = request.TaskId
            };

            return await _apiClient.CallApiAsync<GetDownloadTaskResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCInvoiceEndpoints.GetDownloadTask}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get asynchronous download task list (INVOICE_LIST and INVOICE_BATCH)
        /// </summary>
        /// <param name="request">Get download task list request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing download task list</returns>
        public async Task<GetDownloadTaskListResponse> GetDownloadTaskListAsync(
            GetDownloadTaskListRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BcId))
                throw new ArgumentException("BcId is required", nameof(request));

            _logger?.LogDebug("Getting download task list for BC ID: {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();

            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetDownloadTaskListResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCInvoiceEndpoints.GetDownloadTaskList}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
