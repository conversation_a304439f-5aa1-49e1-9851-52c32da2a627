using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.BalanceBusinessCenters;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.BalanceBusinessCenters
{
    /// <summary>
    /// Interface cho LatestBalanceBusinessCenter App Service
    /// Chỉ cung cấp các thao tác đọc dữ liệu mới nhất
    /// </summary>
    public interface ILatestBalanceBusinessCenterAppService :
        IReadOnlyAppService<
            LatestBalanceBusinessCenterDto,
            Guid,
            GetLatestBalanceBusinessCenterListDto>
    {
        /// <summary>
        /// Lấy Business Center mới nhất theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Business Center mới nhất</returns>
        Task<LatestBalanceBusinessCenterDto> GetByBcIdAsync(string bcId);

        /// <summary>
        /// Lấy danh sách Business Center mới nhất theo tiền tệ
        /// </summary>
        /// <param name="currency">Tiền tệ</param>
        /// <returns>Danh sách Business Center mới nhất</returns>
        Task<PagedResultDto<LatestBalanceBusinessCenterDto>> GetByCurrencyAsync(string currency);

        /// <summary>
        /// Lấy danh sách Business Center mới nhất theo khoảng thời gian đồng bộ
        /// </summary>
        /// <param name="fromDate">Từ ngày</param>
        /// <param name="toDate">Đến ngày</param>
        /// <returns>Danh sách Business Center mới nhất</returns>
        Task<PagedResultDto<LatestBalanceBusinessCenterDto>> GetBySyncDateRangeAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// Lấy thống kê tổng quan về Business Center mới nhất
        /// </summary>
        /// <returns>Thống kê tổng quan</returns>
        Task<LatestBalanceBusinessCenterStatisticsDto> GetStatisticsAsync();

        /// <summary>
        /// Lấy danh sách Business Center mới nhất theo nhiều BC ID
        /// </summary>
        /// <param name="bcIds">Danh sách BC ID</param>
        /// <returns>Danh sách Business Center mới nhất</returns>
        Task<List<LatestBalanceBusinessCenterDto>> GetByManyBcIdsAsync(IEnumerable<string> bcIds);
    }
}
