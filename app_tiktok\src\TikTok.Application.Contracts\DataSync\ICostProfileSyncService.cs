using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu hồ sơ chi phí
    /// </summary>
    public interface ICostProfileSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ hồ sơ chi phí theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<CostProfileSyncResult> SyncCostProfilesAsync(string bcId);

        /// <summary>
        /// Đồng bộ hồ sơ chi phí cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<CostProfileSyncResult> SyncAllCostProfilesAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu hồ sơ chi phí
    /// </summary>
    public class CostProfileSyncResult : SyncResultBase
    {

        /// <summary>
        /// Số AdAccount đã đồng bộ
        /// </summary>
        public int AdAccountCount { get; set; }
    }
}