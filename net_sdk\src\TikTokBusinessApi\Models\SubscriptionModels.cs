/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for creating a subscription
    /// </summary>
    public class SubscriptionCreateBody
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// Secret of your developer application
        /// </summary>
        [JsonPropertyName("secret")]
        public string Secret { get; set; } = string.Empty;

        /// <summary>
        /// The object that you want to subscribe to
        /// </summary>
        [JsonPropertyName("subscribe_entity")]
        public string SubscribeEntity { get; set; } = string.Empty;

        /// <summary>
        /// The callback URL
        /// </summary>
        [JsonPropertyName("callback_url")]
        public string CallbackUrl { get; set; } = string.Empty;

        /// <summary>
        /// Details about the subscription
        /// </summary>
        [JsonPropertyName("subscription_detail")]
        public SubscriptionDetail SubscriptionDetail { get; set; } = new SubscriptionDetail();
    }

    /// <summary>
    /// Details about the subscription
    /// </summary>
    public class SubscriptionDetail
    {
        /// <summary>
        /// The authorized access token
        /// </summary>
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// The source of the leads to subscribe to (valid only when subscribe_entity is LEAD)
        /// </summary>
        [JsonPropertyName("lead_source")]
        public string? LeadSource { get; set; }

        /// <summary>
        /// Business Center ID (conditional for AD_ACCOUNT_SUSPENSION)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string? BcId { get; set; }

        /// <summary>
        /// A list of advertiser IDs (conditional for AD_ACCOUNT_SUSPENSION)
        /// </summary>
        [JsonPropertyName("advertiser_ids")]
        public List<string>? AdvertiserIds { get; set; }

        /// <summary>
        /// Advertiser ID (conditional for AD_GROUP, AD, LEAD, CREATIVE_FATIGUE)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// ID of the form library (conditional for LEAD)
        /// </summary>
        [JsonPropertyName("library_id")]
        public string? LibraryId { get; set; }

        /// <summary>
        /// Page ID of the Instant Form (valid for LEAD with INSTANT_FORM)
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }

        /// <summary>
        /// Ad group ID (conditional for AD_GROUP or CREATIVE_FATIGUE)
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdgroupId { get; set; }

        /// <summary>
        /// Ad ID (conditional for AD or CREATIVE_FATIGUE)
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// TikTok Creator Marketplace account ID (required for TCM_SPARK_ADS)
        /// </summary>
        [JsonPropertyName("tcm_account_id")]
        public string? TcmAccountId { get; set; }

        /// <summary>
        /// The ID of the video uploaded to a TCM Workflow 2.0 order (required for TCM_SPARK_ADS)
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// The ID of a TTO Creator Marketplace account (required for TCM_VIDEOS)
        /// </summary>
        [JsonPropertyName("tto_tcm_account_id")]
        public string? TtoTcmAccountId { get; set; }
    }

    /// <summary>
    /// Response for creating a subscription
    /// </summary>
    public class SubscriptionCreateResponse
    {
        /// <summary>
        /// The subscription ID
        /// </summary>
        [JsonPropertyName("subscription_id")]
        public string SubscriptionId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request parameters for getting subscription details
    /// </summary>
    public class SubscriptionGetRequest
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// Secret of your developer application
        /// </summary>
        [JsonPropertyName("secret")]
        public string Secret { get; set; } = string.Empty;

        /// <summary>
        /// The object that you want to subscribe to (optional filter)
        /// </summary>
        [JsonPropertyName("subscribe_entity")]
        public string? SubscribeEntity { get; set; }

        /// <summary>
        /// The current page number (default: 1)
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// The page size (default: 10, range: 1-1000)
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Response for getting subscription details
    /// </summary>
    public class SubscriptionGetResponse
    {
        /// <summary>
        /// Subscription information
        /// </summary>
        [JsonPropertyName("subscriptions")]
        public List<SubscriptionInfo> Subscriptions { get; set; } = new List<SubscriptionInfo>();

        /// <summary>
        /// The page information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new PageInfo();
    }

    /// <summary>
    /// Subscription information
    /// </summary>
    public class SubscriptionInfo
    {
        /// <summary>
        /// The App ID
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// The subscription ID
        /// </summary>
        [JsonPropertyName("subscription_id")]
        public string SubscriptionId { get; set; } = string.Empty;

        /// <summary>
        /// The object that you want to subscribe to
        /// </summary>
        [JsonPropertyName("subscribe_entity")]
        public string SubscribeEntity { get; set; } = string.Empty;

        /// <summary>
        /// The callback URL
        /// </summary>
        [JsonPropertyName("callback_url")]
        public string CallbackUrl { get; set; } = string.Empty;

        /// <summary>
        /// Details about the subscription
        /// </summary>
        [JsonPropertyName("subscription_detail")]
        public SubscriptionDetail SubscriptionDetail { get; set; } = new SubscriptionDetail();
    }

    /// <summary>
    /// Request body for canceling a subscription
    /// </summary>
    public class SubscriptionCancelBody
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// Secret of your developer application
        /// </summary>
        [JsonPropertyName("secret")]
        public string Secret { get; set; } = string.Empty;

        /// <summary>
        /// The subscription ID
        /// </summary>
        [JsonPropertyName("subscription_id")]
        public string SubscriptionId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for canceling a subscription
    /// </summary>
    public class SubscriptionCancelResponse
    {
        /// <summary>
        /// The subscription ID
        /// </summary>
        [JsonPropertyName("subscription_id")]
        public string SubscriptionId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Subscription entity types
    /// </summary>
    public static class SubscriptionEntityTypes
    {
        /// <summary>
        /// The suspension statuses of ad accounts
        /// </summary>
        public const string AdAccountSuspension = "AD_ACCOUNT_SUSPENSION";

        /// <summary>
        /// Leads
        /// </summary>
        public const string Lead = "LEAD";

        /// <summary>
        /// The review status of an ad group
        /// </summary>
        public const string AdGroup = "AD_GROUP";

        /// <summary>
        /// The review status of an ad
        /// </summary>
        public const string Ad = "AD";

        /// <summary>
        /// The status of Spark Ads authorization for a video uploaded to a TCM Workflow 2.0 order
        /// </summary>
        public const string TcmSparkAds = "TCM_SPARK_ADS";

        /// <summary>
        /// The linking of a TikTok video to a TTO campaign
        /// </summary>
        public const string TcmVideos = "TCM_VIDEOS";

        /// <summary>
        /// The fatigue status of an ad, ads within an ad group, or ads within an advertiser account
        /// </summary>
        public const string CreativeFatigue = "CREATIVE_FATIGUE";
    }

    /// <summary>
    /// Lead source types
    /// </summary>
    public static class LeadSourceTypes
    {
        /// <summary>
        /// Leads generated through Instant Forms
        /// </summary>
        public const string InstantForm = "INSTANT_FORM";

        /// <summary>
        /// Leads generated from direct messages of the associated Business Account
        /// </summary>
        public const string DirectMessage = "DIRECT_MESSAGE";
    }
}
