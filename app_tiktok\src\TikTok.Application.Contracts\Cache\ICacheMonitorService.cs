using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.Cache
{
    /// <summary>
    /// Interface cho service monitor cache
    /// </summary>
    public interface ICacheMonitorService : IScopedDependency
    {
        /// <summary>
        /// L<PERSON>y danh sách tất cả cache services
        /// </summary>
        /// <returns>Danh sách cache services</returns>
        Task<List<CacheInfoDto>> GetAllCacheInfoAsync();

        /// <summary>
        /// Lấy thông tin cache theo tên
        /// </summary>
        /// <param name="cacheName">Tên cache</param>
        /// <returns>Thông tin cache</returns>
        Task<CacheInfoDto?> GetCacheInfoByNameAsync(string cacheName);

        /// <summary>
        /// Clear tất cả cache
        /// </summary>
        /// <returns>Kết quả clear cache</returns>
        Task<ClearCacheResultDto> ClearAllCacheAsync();

        /// <summary>
        /// Clear cache theo tên
        /// </summary>
        /// <param name="cacheName">Tên cache</param>
        /// <param name="cacheKey">Cache key cụ thể (optional)</param>
        /// <returns>Kết quả clear cache</returns>
        Task<CacheClearResultDto> ClearCacheByNameAsync(string cacheName, string? cacheKey = null);
    }
}
