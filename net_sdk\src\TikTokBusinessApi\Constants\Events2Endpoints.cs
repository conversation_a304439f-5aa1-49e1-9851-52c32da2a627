/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Events 2.0 endpoints
    /// </summary>
    public static class Events2Endpoints
    {
        /// <summary>
        /// Report App, Web, Offline, or CRM events endpoint (v1.3)
        /// This is the unified endpoint for Events API 2.0 that supports all event types
        /// </summary>
        public const string TrackEvents = "/event/track/";
    }
}
