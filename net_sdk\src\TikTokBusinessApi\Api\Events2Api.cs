/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Events 2.0 operations
    /// Events API 2.0 provides a unified endpoint for reporting App, Web, Offline, and CRM events
    /// </summary>
    public class Events2Api : IEvents2Api
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<Events2Api>? _logger;

        /// <summary>
        /// Initializes a new instance of the Events2Api class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public Events2Api(IApiClient apiClient, ILogger<Events2Api>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Report App, Web, Offline, or CRM events using Events API 2.0
        /// This is the unified endpoint that supports all event types through a single API call
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Events 2.0 track request containing event source, source ID, and event data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure with detailed error information</returns>
        //public async Task<Response<Events2TrackResponse>> TrackEventsAsync(
        //    string accessToken,
        //    Events2TrackRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.EventSource))
        //        throw new ArgumentException("Event source cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.EventSourceId))
        //        throw new ArgumentException("Event source ID cannot be null or empty", nameof(request));
        //    if (request.Data == null || request.Data.Count == 0)
        //        throw new ArgumentException("Event data cannot be null or empty", nameof(request));
        //    if (request.Data.Count > 1000)
        //        throw new ArgumentException("Cannot report more than 1,000 events in a single request", nameof(request));

        //    _logger?.LogInformation("Tracking {Count} {EventSource} events for source ID {EventSourceId}", 
        //        request.Data.Count, request.EventSource, request.EventSourceId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<Events2TrackResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{Events2Endpoints.TrackEvents}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: (string[]?)null,
        //        cancellationToken: cancellationToken);
        //}

        #region Convenience Methods for Specific Event Types

        /// <summary>
        /// Report web events using Events API 2.0
        /// Convenience method for reporting web events measured by Pixel Code
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="pixelCode">Pixel Code for measuring web events</param>
        /// <param name="eventData">Web event data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<Events2TrackResponse>> TrackWebEventsAsync(
        //    string accessToken,
        //    string pixelCode,
        //    Events2EventData[] eventData,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(pixelCode))
        //        throw new ArgumentException("Pixel code cannot be null or empty", nameof(pixelCode));
        //    if (eventData == null || eventData.Length == 0)
        //        throw new ArgumentException("Event data cannot be null or empty", nameof(eventData));

        //    var request = new Events2TrackRequest
        //    {
        //        EventSource = Events2EventSource.Web,
        //        EventSourceId = pixelCode,
        //        Data = eventData.ToList()
        //    };

        //    return await TrackEventsAsync(accessToken, request, cancellationToken);
        //}

        /// <summary>
        /// Report app events using Events API 2.0
        /// Convenience method for reporting app events measured by TikTok App ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="tikTokAppId">TikTok App ID for measuring app events</param>
        /// <param name="eventData">App event data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<Events2TrackResponse>> TrackAppEventsAsync(
        //    string accessToken,
        //    string tikTokAppId,
        //    Events2EventData[] eventData,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(tikTokAppId))
        //        throw new ArgumentException("TikTok App ID cannot be null or empty", nameof(tikTokAppId));
        //    if (eventData == null || eventData.Length == 0)
        //        throw new ArgumentException("Event data cannot be null or empty", nameof(eventData));

        //    _logger?.LogInformation("Tracking app events - Note: This is an allowlist-only feature");

        //    var request = new Events2TrackRequest
        //    {
        //        EventSource = Events2EventSource.App,
        //        EventSourceId = tikTokAppId,
        //        Data = eventData.ToList()
        //    };

        //    return await TrackEventsAsync(accessToken, request, cancellationToken);
        //}

        /// <summary>
        /// Report offline events using Events API 2.0
        /// Convenience method for reporting offline events measured by Offline Event Set ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="offlineEventSetId">Offline Event Set ID for measuring offline events</param>
        /// <param name="eventData">Offline event data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<Events2TrackResponse>> TrackOfflineEventsAsync(
        //    string accessToken,
        //    string offlineEventSetId,
        //    Events2EventData[] eventData,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(offlineEventSetId))
        //        throw new ArgumentException("Offline Event Set ID cannot be null or empty", nameof(offlineEventSetId));
        //    if (eventData == null || eventData.Length == 0)
        //        throw new ArgumentException("Event data cannot be null or empty", nameof(eventData));

        //    var request = new Events2TrackRequest
        //    {
        //        EventSource = Events2EventSource.Offline,
        //        EventSourceId = offlineEventSetId,
        //        Data = eventData.ToList()
        //    };

        //    return await TrackEventsAsync(accessToken, request, cancellationToken);
        //}

        /// <summary>
        /// Report CRM events using Events API 2.0
        /// Convenience method for reporting CRM events measured by CRM Event Set ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="crmEventSetId">CRM Event Set ID for measuring CRM events</param>
        /// <param name="eventData">CRM event data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<Events2TrackResponse>> TrackCrmEventsAsync(
        //    string accessToken,
        //    string crmEventSetId,
        //    Events2EventData[] eventData,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(crmEventSetId))
        //        throw new ArgumentException("CRM Event Set ID cannot be null or empty", nameof(crmEventSetId));
        //    if (eventData == null || eventData.Length == 0)
        //        throw new ArgumentException("Event data cannot be null or empty", nameof(eventData));

        //    var request = new Events2TrackRequest
        //    {
        //        EventSource = Events2EventSource.Crm,
        //        EventSourceId = crmEventSetId,
        //        Data = eventData.ToList()
        //    };

        //    return await TrackEventsAsync(accessToken, request, cancellationToken);
        //}

        #endregion
    }
}
