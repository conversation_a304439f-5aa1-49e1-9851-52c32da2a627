/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for publish status
    /// </summary>
    public class PublishStatusResponse
    {
        /// <summary>
        /// The publishing status of the TikTok post
        /// Enum values: PROCESSING_DOWNLOAD, PUBLISH_COMPLETE, FAILED, SEND_TO_USER_INBOX
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// The unique identifiers for the published TikTok posts (returned only when status is PUBLISH_COMPLETE)
        /// </summary>
        [JsonPropertyName("post_ids")]
        public List<string>? PostIds { get; set; }

        /// <summary>
        /// The reason for the publishing failure (returned only when status is FAILED)
        /// </summary>
        [JsonPropertyName("reason")]
        public string? Reason { get; set; }
    }
}
