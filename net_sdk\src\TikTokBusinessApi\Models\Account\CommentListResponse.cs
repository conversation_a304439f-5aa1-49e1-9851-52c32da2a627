/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for comment list
    /// </summary>
    public class CommentListResponse
    {
        /// <summary>
        /// List of current page of comments created on the specified video
        /// </summary>
        [JsonPropertyName("comments")]
        public List<CommentInfo> Comments { get; set; } = new();

        /// <summary>
        /// Cursor for the next page of results
        /// </summary>
        [JsonPropertyName("cursor")]
        public int Cursor { get; set; }

        /// <summary>
        /// Whether an additional page of data is available
        /// </summary>
        [JsonPropertyName("has_more")]
        public bool HasMore { get; set; }
    }

    /// <summary>
    /// Comment information
    /// </summary>
    public class CommentInfo
    {
        /// <summary>
        /// Unique identifier for the comment or comment reply
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string CommentId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for the owned video that the comment was created on
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Developer application and TikTok account scoped unique identifier for the user that made the comment
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// A globally unique identifier assigned to each user commenting
        /// </summary>
        [JsonPropertyName("unique_identifier")]
        public string? UniqueIdentifier { get; set; }

        /// <summary>
        /// Unix/Epoch date-time when the comment was created
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;

        /// <summary>
        /// Text content of the comment
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; } = string.Empty;

        /// <summary>
        /// Number of likes the comment has received
        /// </summary>
        [JsonPropertyName("likes")]
        public int Likes { get; set; }

        /// <summary>
        /// Number of replies the comment has received
        /// </summary>
        [JsonPropertyName("replies")]
        public int Replies { get; set; }

        /// <summary>
        /// Whether the user who posted the video made the comment
        /// </summary>
        [JsonPropertyName("owner")]
        public bool Owner { get; set; }

        /// <summary>
        /// Whether the user who posted the video has liked the comment
        /// </summary>
        [JsonPropertyName("liked")]
        public bool Liked { get; set; }

        /// <summary>
        /// Whether the user who posted the video has pinned the comment
        /// </summary>
        [JsonPropertyName("pinned")]
        public bool Pinned { get; set; }

        /// <summary>
        /// The visibility status of the comment
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// The username of the TikTok user who made the comment
        /// </summary>
        [JsonPropertyName("username")]
        public string? Username { get; set; }

        /// <summary>
        /// The display name of the TikTok user who made the comment
        /// </summary>
        [JsonPropertyName("display_name")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// Temporary URL for profile image of the TikTok user who posted the comment
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string? ProfileImage { get; set; }

        /// <summary>
        /// Returned only for comment replies. The ID of the comment to which the reply is directed
        /// </summary>
        [JsonPropertyName("parent_comment_id")]
        public string? ParentCommentId { get; set; }

        /// <summary>
        /// Returned only for top-level comments when include_replies is set to true
        /// </summary>
        [JsonPropertyName("reply_list")]
        public List<CommentReply>? ReplyList { get; set; }
    }

    /// <summary>
    /// Comment reply information
    /// </summary>
    public class CommentReply
    {
        /// <summary>
        /// Unique identifier for the owned video that the comment reply was created on
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for the comment reply
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string CommentId { get; set; } = string.Empty;

        /// <summary>
        /// Unix/Epoch date-time when the comment reply was created
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;

        /// <summary>
        /// Text content of the comment reply
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; } = string.Empty;

        /// <summary>
        /// The visibility status of the comment reply
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Whether this comment reply is liked by the user who posted the video
        /// </summary>
        [JsonPropertyName("liked")]
        public bool Liked { get; set; }

        /// <summary>
        /// Number of likes the comment reply has received
        /// </summary>
        [JsonPropertyName("likes")]
        public int Likes { get; set; }

        /// <summary>
        /// Whether the comment reply is made by the user who posted the video
        /// </summary>
        [JsonPropertyName("owner")]
        public bool Owner { get; set; }

        /// <summary>
        /// Developer application and TikTok account scoped unique identifier for the user who made the comment reply
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// A globally unique identifier assigned to each user commenting
        /// </summary>
        [JsonPropertyName("unique_identifier")]
        public string? UniqueIdentifier { get; set; }

        /// <summary>
        /// The username of the TikTok user who posted the comment reply
        /// </summary>
        [JsonPropertyName("username")]
        public string? Username { get; set; }

        /// <summary>
        /// The display name of the TikTok user who posted the comment reply
        /// </summary>
        [JsonPropertyName("display_name")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// Temporary URL for profile image of the TikTok user who posted the comment reply
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string? ProfileImage { get; set; }

        /// <summary>
        /// The ID of the parent comment to which the comment reply is directed
        /// </summary>
        [JsonPropertyName("parent_comment_id")]
        public string ParentCommentId { get; set; } = string.Empty;
    }
}
