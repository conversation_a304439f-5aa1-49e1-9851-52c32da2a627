﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<OutputType>Library</OutputType>
		<IsPackable>true</IsPackable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Elsa.Core" Version="2.14.1" />
		<PackageReference Include="Tsp.Zalo.Activities" Version="1.0.0-prerelease-5916" />
		<PackageReference Include="Volo.Abp.AutoMapper" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.BlobStoring" Version="8.1.4" />
		<PackageReference Include="Volo.Abp.Core" Version="8.1.4" />
		<PackageReference Include="Tsp.UI.Activities" Version="1.0.5" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\TikTok.Application.Contracts\TikTok.Application.Contracts.csproj" />
		<ProjectReference Include="..\TikTok.Domain\TikTok.Domain.csproj" />
	</ItemGroup>
</Project>
