/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for confirming terms
    /// </summary>
    public class TermConfirmBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Term type
        /// </summary>
        [JsonPropertyName("term_type")]
        public string? TermType { get; set; }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="obj">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not TermConfirmBody other)
                return false;

            return AdvertiserId == other.AdvertiserId &&
                   TermType == other.TermType;
        }

        /// <summary>
        /// Returns the hash code for this instance
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AdvertiserId, TermType);
        }

        /// <summary>
        /// Returns the string representation of the object
        /// </summary>
        /// <returns>String representation of the object</returns>
        public override string ToString()
        {
            return $"TermConfirmBody {{ AdvertiserId = {AdvertiserId}, TermType = {TermType} }}";
        }
    }
}
