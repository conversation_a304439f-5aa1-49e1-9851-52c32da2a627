using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using System.Text.Json.Serialization;
using TikTok.Application.Contracts.LatestNotifications;
using TikTok.Application.Contracts.Rules;
using TikTok.AdAccounts;
using TikTok.BalanceAdAccounts;
using TikTok.Customers;
using TikTok.Domain.Entities.Rules;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.Rules;
using TikTok.Transactions;
using Volo.Abp.ObjectMapping;

namespace TikTok.Activities.Notifications
{
    [Activity(Category = "Thông báo", DisplayName = "Chuẩn bị thông báo số dư", Outcomes = new[] { OutcomeNames.Done })]
    public class PrepareBalanceNotificationActivity : UnitOfWorkActivity
    {
        private readonly ILatestBalanceAdAccountRepository _latestBalanceAdAccountRepository;
        private readonly ITransactionRepository _transactionRepository;
        private readonly ILatestNotificationRepository _latestNotificationRepository;
        private readonly IRuleCache _ruleCache;
        private readonly IRuleRepository _ruleRepository;
        private readonly IRuleAdAccountService _ruleAdAccountService;
        private readonly ICustomerQueryService _customerQueryService;
        private readonly IAdAccountSupporterQueryService _adAccountSupporterQueryService;
        private readonly IObjectMapper _objectMapper;

        public PrepareBalanceNotificationActivity(
            ILatestBalanceAdAccountRepository latestBalanceAdAccountRepository,
            ITransactionRepository transactionRepository,
            ILatestNotificationRepository latestNotificationRepository,
            IRuleCache ruleCache,
            IRuleRepository ruleRepository,
            IRuleAdAccountService ruleAdAccountService,
            ICustomerQueryService customerQueryService,
            IAdAccountSupporterQueryService adAccountSupporterQueryService,
            IObjectMapper objectMapper)
        {
            _latestBalanceAdAccountRepository = latestBalanceAdAccountRepository;
            _transactionRepository = transactionRepository;
            _latestNotificationRepository = latestNotificationRepository;
            _ruleCache = ruleCache;
            _ruleRepository = ruleRepository;
            _ruleAdAccountService = ruleAdAccountService;
            _customerQueryService = customerQueryService;
            _adAccountSupporterQueryService = adAccountSupporterQueryService;
            _objectMapper = objectMapper;
        }

        [ActivityInput(Hint = "Business Center ID", SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public string BcId { get; set; }

        [ActivityInput(Hint = "Danh sách Advertiser IDs", SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public List<string> AdvertiserIds { get; set; } = new List<string>();

        [ActivityInput(Hint = "Cấu hình lấy dữ liệu - Lấy danh sách quy tắc từ cache", DefaultValue = true, SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public bool ShouldGetRulesList { get; set; } = true;

        [ActivityInput(Hint = "Cấu hình lấy dữ liệu - Lấy danh sách quy tắc gắn theo tài khoản quảng cáo", DefaultValue = true, SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public bool ShouldGetRuleAdAccountList { get; set; } = false;

        [ActivityInput(Hint = "Cấu hình lấy dữ liệu - Lấy danh sách số dư mới nhất", DefaultValue = true, SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public bool ShouldGetLatestBalances { get; set; } = false;

        [ActivityInput(Hint = "Cấu hình lấy dữ liệu - Lấy danh sách giao dịch mới nhất", DefaultValue = true, SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public bool ShouldGetLatestTransactions { get; set; } = false;

        [ActivityInput(Hint = "Cấu hình lấy dữ liệu - Lấy danh sách thông báo mới nhất", DefaultValue = true, SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public bool ShouldGetLatestNotifications { get; set; } = false;

        [ActivityInput(Hint = "Cấu hình lấy dữ liệu - Lấy danh sách khách hàng", DefaultValue = false, SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public bool ShouldGetCustomers { get; set; } = false;

        [ActivityInput(Hint = "Cấu hình lấy dữ liệu - Lấy danh sách người hỗ trợ", DefaultValue = false, SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public bool ShouldGetSupporters { get; set; } = false;

        [ActivityInput(Hint = "Cấu hình performance - Sử dụng cache cho quy tắc", DefaultValue = true, SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public bool UseRuleCache { get; set; } = true;

        [ActivityInput(Hint = "Cấu hình performance - Giới hạn số lượng giao dịch lấy về (0 = không giới hạn)", DefaultValue = 0, SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public int TransactionLimit { get; set; } = 0;

        [ActivityInput(Hint = "Cấu hình performance - Chỉ lấy giao dịch trong khoảng thời gian (số ngày gần đây, 0 = không giới hạn)", DefaultValue = 0, SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public int TransactionDaysLimit { get; set; } = 0;

        [ActivityOutput(Hint = "Danh sách quy tắc từ cache")]
        public List<RuleDto> RulesList { get; set; } = new List<RuleDto>();

        [ActivityOutput(Hint = "Danh sách quy tắc gắn theo tài khoản quảng cáo")]
        public List<RuleAdAccountFlatDto> RuleAdAccountList { get; set; } = new List<RuleAdAccountFlatDto>();

        [ActivityOutput(Hint = "Danh sách số dư mới nhất")]
        public List<BalanceAdAccountDto> LatestBalances { get; set; } = new List<BalanceAdAccountDto>();

        [ActivityOutput(Hint = "Danh sách giao dịch mới nhất")]
        public List<TransactionDto> LatestTransactions { get; set; } = new List<TransactionDto>();

        [ActivityOutput(Hint = "Danh sách thông báo mới nhất")]
        public List<LatestNotificationDto> LatestNotifications { get; set; } = new List<LatestNotificationDto>();

        [ActivityOutput(Hint = "Danh sách khách hàng")]
        public List<CustomerAdvertiserDto> Customers { get; set; } = new List<CustomerAdvertiserDto>();

        [ActivityOutput(Hint = "Danh sách người hỗ trợ")]
        public List<AdAccountSupporterListDto> Supporters { get; set; } = new List<AdAccountSupporterListDto>();

        [ActivityOutput(Hint = "Thông tin về dữ liệu đã được lấy")]
        public DataRetrievalInfo DataRetrievalInfo { get; set; } = new DataRetrievalInfo();

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            if (string.IsNullOrEmpty(BcId))
            {
                throw new InvalidOperationException("BcId is required.");
            }

            var startTime = DateTime.UtcNow;
            var retrievalInfo = new DataRetrievalInfo
            {
                StartTime = startTime,
                BcId = BcId,
                AdvertiserIdsCount = AdvertiserIds?.Count ?? 0,
                RetrievedDataTypes = new List<string>()
            };

            try
            {
                // 1. Lấy danh sách quy tắc từ cache (nếu được cấu hình)
                if (ShouldGetRulesList)
                {
                    await GetRulesListAsync();
                    retrievalInfo.RetrievedDataTypes.Add("RulesList");
                }

                // 2. Lấy danh sách quy tắc gắn theo tài khoản quảng cáo (nếu được cấu hình)
                if (ShouldGetRuleAdAccountList)
                {
                    await GetRuleAdAccountListAsync();
                    retrievalInfo.RetrievedDataTypes.Add("RuleAdAccountList");
                }

                // 3. Lấy danh sách số dư mới nhất (nếu được cấu hình)
                if (ShouldGetLatestBalances)
                {
                    await GetLatestBalancesAsync();
                    retrievalInfo.RetrievedDataTypes.Add("LatestBalances");
                }

                // 4. Lấy danh sách giao dịch mới nhất (nếu được cấu hình)
                if (ShouldGetLatestTransactions)
                {
                    await GetLatestTransactionsAsync();
                    retrievalInfo.RetrievedDataTypes.Add("LatestTransactions");
                }

                // 5. Lấy danh sách thông báo mới nhất (nếu được cấu hình)
                if (ShouldGetLatestNotifications)
                {
                    await GetLatestNotificationsAsync();
                    retrievalInfo.RetrievedDataTypes.Add("LatestNotifications");
                }

                // 6. Lấy danh sách khách hàng (nếu được cấu hình)
                if (ShouldGetCustomers)
                {
                    await GetCustomersAsync();
                    retrievalInfo.RetrievedDataTypes.Add("Customers");
                }

                // 7. Lấy danh sách người hỗ trợ (nếu được cấu hình)
                if (ShouldGetSupporters)
                {
                    await GetSupportersAsync();
                    retrievalInfo.RetrievedDataTypes.Add("Supporters");
                }

                retrievalInfo.EndTime = DateTime.UtcNow;
                retrievalInfo.Duration = retrievalInfo.EndTime - retrievalInfo.StartTime;
                retrievalInfo.Success = true;
                DataRetrievalInfo = retrievalInfo;

                return Done();
            }
            catch (Exception ex)
            {
                retrievalInfo.EndTime = DateTime.UtcNow;
                retrievalInfo.Duration = retrievalInfo.EndTime - retrievalInfo.StartTime;
                retrievalInfo.Success = false;
                retrievalInfo.ErrorMessage = ex.Message;
                DataRetrievalInfo = retrievalInfo;
                throw;
            }
        }

        private async Task GetRulesListAsync()
        {
            if (UseRuleCache)
            {
                // Lấy tất cả quy tắc từ cache
                RulesList = await _ruleCache.GetAllAsync();
            }
            else
            {
                var rules = await _ruleRepository.GetListAsync();
                RulesList = _objectMapper.Map<List<RuleEntity>, List<RuleDto>>(rules);
            }
        }

        private async Task GetRuleAdAccountListAsync()
        {
            RuleAdAccountList = await _ruleAdAccountService.GetRulesByBcIdAndAdvertiserIdsAsync(BcId, AdvertiserIds);
        }

        private async Task GetLatestBalancesAsync()
        {
            var queryable = (await _latestBalanceAdAccountRepository.GetQueryableAsync())
                .Where(x => x.BcId == BcId);

            if (AdvertiserIds != null && AdvertiserIds.Any())
            {
                queryable = queryable.Where(x => AdvertiserIds.Contains(x.AdvertiserId));
            }

            LatestBalances = _objectMapper.Map<List<RawLatestBalanceAdAccountEntity>, List<BalanceAdAccountDto>>(queryable.ToList());
        }

        private async Task GetLatestTransactionsAsync()
        {
            var queryable = (await _transactionRepository.GetQueryableAsync())
                .Where(x => x.BcId == BcId);

            if (AdvertiserIds != null && AdvertiserIds.Any())
            {
                queryable = queryable.Where(x => x.AccountId != null && AdvertiserIds.Contains(x.AccountId));
            }

            // Áp dụng giới hạn thời gian nếu được cấu hình
            if (TransactionDaysLimit > 0)
            {
                var cutoffDate = DateTime.UtcNow.AddDays(-TransactionDaysLimit);
                queryable = queryable.Where(x => x.CreateTime >= cutoffDate);
            }

            // Lấy giao dịch cuối cùng cho mỗi tài khoản quảng cáo
            var latestTransactions = queryable
                .GroupBy(x => x.AccountId)
                .Select(g => g.OrderByDescending(x => x.CreateTime).First())
                .ToList();

            // Áp dụng giới hạn số lượng nếu được cấu hình
            if (TransactionLimit > 0 && latestTransactions.Count > TransactionLimit)
            {
                latestTransactions = latestTransactions.Take(TransactionLimit).ToList();
            }

            LatestTransactions = _objectMapper.Map<List<RawTransactionEntity>, List<TransactionDto>>(latestTransactions);
        }

        private async Task GetLatestNotificationsAsync()
        {
            if (AdvertiserIds == null || AdvertiserIds.Count == 0)
            {
                LatestNotifications = new List<LatestNotificationDto>();
                return;
            }

            // Lấy thông báo theo Type và danh sách AdvertiserIds
            var notifications = await _latestNotificationRepository.GetByTypeAndObjectIdsAsync(NotificationType.Balance, AdvertiserIds);

            // Map entities to DTOs using AutoMapper
            LatestNotifications = _objectMapper.Map<List<LatestNotificationEntity>, List<LatestNotificationDto>>(notifications) ?? new List<LatestNotificationDto>();
        }

        private async Task GetCustomersAsync()
        {
            if (AdvertiserIds != null && AdvertiserIds.Any())
            {
                // Lấy khách hàng theo AdvertiserIds
                Customers = await _customerQueryService.GetCustomersByAdvertiserIdsAsync(AdvertiserIds);
            }
            else
            {
                // Lấy tất cả khách hàng theo BcId
                Customers = await _customerQueryService.GetCustomersByBcIdAsync(BcId);
            }
        }

        private async Task GetSupportersAsync()
        {
            if (AdvertiserIds != null && AdvertiserIds.Any())
            {
                // Lấy người hỗ trợ theo BcId và AdvertiserIds
                Supporters = await _adAccountSupporterQueryService.GetSupportersByBcIdAndAdvertiserIdsAsync(BcId, AdvertiserIds, false);
            }
            else
            {
                // Lấy tất cả người hỗ trợ theo BcId
                Supporters = await _adAccountSupporterQueryService.GetSupportersByBcIdAsync(BcId, false);
            }
        }
    }

    /// <summary>
    /// Thông tin về việc lấy dữ liệu
    /// </summary>
    public class DataRetrievalInfo
    {
        [JsonPropertyName("startTime")]
        public DateTime StartTime { get; set; }

        [JsonPropertyName("endTime")]
        public DateTime? EndTime { get; set; }

        [JsonPropertyName("duration")]
        public TimeSpan? Duration { get; set; }

        [JsonPropertyName("bcId")]
        public string BcId { get; set; }

        [JsonPropertyName("advertiserIdsCount")]
        public int AdvertiserIdsCount { get; set; }

        [JsonPropertyName("retrievedDataTypes")]
        public List<string> RetrievedDataTypes { get; set; } = new List<string>();

        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("errorMessage")]
        public string ErrorMessage { get; set; }

        [JsonPropertyName("performanceMetrics")]
        public Dictionary<string, object> PerformanceMetrics { get; set; } = new Dictionary<string, object>();
    }
}