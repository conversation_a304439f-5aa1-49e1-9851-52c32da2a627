/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Threading;
using System.Threading.Tasks;

namespace TikTokBusinessApi.Core.Resilience
{
    /// <summary>
    /// Circuit breaker states
    /// </summary>
    public enum CircuitBreakerState
    {
        /// <summary>
        /// Circuit is closed, requests are allowed through
        /// </summary>
        Closed,

        /// <summary>
        /// Circuit is open, requests are blocked
        /// </summary>
        Open,

        /// <summary>
        /// Circuit is half-open, testing if service has recovered
        /// </summary>
        HalfOpen
    }

    /// <summary>
    /// Interface for circuit breaker implementations
    /// </summary>
    public interface ICircuitBreaker
    {
        /// <summary>
        /// Current state of the circuit breaker
        /// </summary>
        CircuitBreakerState State { get; }

        /// <summary>
        /// Executes an operation through the circuit breaker
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="operation">Operation to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        /// <exception cref="CircuitBreakerOpenException">Thrown when circuit is open</exception>
        Task<T> ExecuteAsync<T>(Func<CancellationToken, Task<T>> operation, CancellationToken cancellationToken = default);

        /// <summary>
        /// Manually opens the circuit breaker
        /// </summary>
        void Open();

        /// <summary>
        /// Manually closes the circuit breaker
        /// </summary>
        void Close();

        /// <summary>
        /// Resets the circuit breaker to closed state
        /// </summary>
        void Reset();

        /// <summary>
        /// Gets the current circuit breaker metrics
        /// </summary>
        CircuitBreakerMetrics GetMetrics();

        /// <summary>
        /// Event raised when circuit breaker state changes
        /// </summary>
        event EventHandler<CircuitBreakerStateChangedEventArgs> StateChanged;
    }

    /// <summary>
    /// Circuit breaker metrics
    /// </summary>
    public class CircuitBreakerMetrics
    {
        /// <summary>
        /// Total number of requests processed
        /// </summary>
        public long TotalRequests { get; set; }

        /// <summary>
        /// Number of successful requests
        /// </summary>
        public long SuccessfulRequests { get; set; }

        /// <summary>
        /// Number of failed requests
        /// </summary>
        public long FailedRequests { get; set; }

        /// <summary>
        /// Current failure rate (0.0 to 1.0)
        /// </summary>
        public double FailureRate => TotalRequests > 0 ? (double)FailedRequests / TotalRequests : 0.0;

        /// <summary>
        /// Time when circuit was last opened
        /// </summary>
        public DateTimeOffset? LastOpenedAt { get; set; }

        /// <summary>
        /// Time when circuit was last closed
        /// </summary>
        public DateTimeOffset? LastClosedAt { get; set; }

        /// <summary>
        /// Duration the circuit has been in current state
        /// </summary>
        public TimeSpan TimeInCurrentState { get; set; }
    }

    /// <summary>
    /// Event arguments for circuit breaker state changes
    /// </summary>
    public class CircuitBreakerStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// Previous state
        /// </summary>
        public CircuitBreakerState PreviousState { get; set; }

        /// <summary>
        /// New state
        /// </summary>
        public CircuitBreakerState NewState { get; set; }

        /// <summary>
        /// Reason for state change
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Exception that triggered the state change (if any)
        /// </summary>
        public Exception? Exception { get; set; }
    }

    /// <summary>
    /// Circuit breaker configuration options
    /// </summary>
    public class CircuitBreakerOptions
    {
        /// <summary>
        /// Failure threshold to open the circuit (default: 0.5 = 50%)
        /// </summary>
        public double FailureThreshold { get; set; } = 0.5;

        /// <summary>
        /// Minimum number of requests before circuit can open (default: 10)
        /// </summary>
        public int MinimumThroughput { get; set; } = 10;

        /// <summary>
        /// Duration to keep circuit open before trying half-open (default: 30 seconds)
        /// </summary>
        public TimeSpan OpenTimeout { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// Time window for calculating failure rate (default: 1 minute)
        /// </summary>
        public TimeSpan SamplingDuration { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// Number of successful requests needed in half-open state to close circuit (default: 3)
        /// </summary>
        public int SuccessThreshold { get; set; } = 3;

        /// <summary>
        /// Custom predicate to determine if an exception should count as a failure
        /// </summary>
        public Func<Exception, bool>? IsFailureException { get; set; }
    }
}
