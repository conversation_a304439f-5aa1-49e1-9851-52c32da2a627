/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Music operations
    /// </summary>
    public class MusicApi : IMusicApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<MusicApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the MusicApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public MusicApi(IApiClient apiClient, ILogger<MusicApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        // /// <summary>
        // /// Upload a piece of music and get the music ID, to add music to the Liked list or History list, or to remove music from the Liked list
        // /// </summary>
        // /// <param name="request">Music upload request parameters</param>
        // /// <param name="fileStream">Music file stream (required when upload_type is UPLOAD_BY_FILE or not specified)</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing music upload information</returns>
        // public async Task<MusicUploadResponse> UploadMusicAsync(
        //     MusicUploadRequest request,
        //     Stream? fileStream = null,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     // Validate file stream requirement
        //     var uploadType = request.UploadType ?? MusicUploadType.UploadByFile;
        //     var isFileUpload = uploadType == MusicUploadType.UploadByFile && string.IsNullOrWhiteSpace(request.MaterialAction);

        //     if (isFileUpload && fileStream == null)
        //         throw new ArgumentException("File stream is required when upload_type is UPLOAD_BY_FILE or not specified", nameof(fileStream));

        //     if (isFileUpload && string.IsNullOrWhiteSpace(request.MusicSignature))
        //         throw new ArgumentException("Music signature is required when upload_type is UPLOAD_BY_FILE or not specified", nameof(request));

        //     _logger?.LogInformation("Uploading music for advertiser {AdvertiserId} with upload type {UploadType}", 
        //         request.AdvertiserId, uploadType);

        //     object body;
        //     if (isFileUpload && fileStream != null)
        //     {
        //         // Create multipart form data content for file upload
        //         var content = new MultipartFormDataContent();
        //         content.Add(new StringContent(request.AdvertiserId), "advertiser_id");
                
        //         if (!string.IsNullOrWhiteSpace(request.MusicScene))
        //             content.Add(new StringContent(request.MusicScene), "music_scene");
        //         if (!string.IsNullOrWhiteSpace(request.UploadType))
        //             content.Add(new StringContent(request.UploadType), "upload_type");
        //         if (!string.IsNullOrWhiteSpace(request.MaterialAction))
        //             content.Add(new StringContent(request.MaterialAction), "material_action");
        //         if (!string.IsNullOrWhiteSpace(request.MusicSignature))
        //             content.Add(new StringContent(request.MusicSignature), "music_signature");
        //         if (!string.IsNullOrWhiteSpace(request.FileName))
        //             content.Add(new StringContent(request.FileName), "file_name");
        //         if (!string.IsNullOrWhiteSpace(request.MaterialId))
        //             content.Add(new StringContent(request.MaterialId), "material_id");
                
        //         content.Add(new StreamContent(fileStream), "music_file", request.FileName ?? "music");
        //         body = content;
        //     }
        //     else
        //     {
        //         // Use JSON body for FILE_ID uploads or material actions
        //         body = request;
        //     }

        //     return await _apiClient.CallApiAsync<MusicUploadResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MusicEndpoints.UploadMusic}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get the list of available music that can be used for video creation or Carousel Ads creation
        /// </summary>
        /// <param name="request">Music get request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing music list information</returns>
        public async Task<MusicGetResponse> GetMusicListAsync(
            MusicGetRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting music list for advertiser {AdvertiserId} with scene {MusicScene}", 
                request.AdvertiserId, request.MusicScene ?? "CREATIVE_ASSET");

            // Build query parameters
            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId
            };

            if (!string.IsNullOrWhiteSpace(request.MusicScene))
                queryParams["music_scene"] = request.MusicScene;
            if (!string.IsNullOrWhiteSpace(request.SearchType))
                queryParams["search_type"] = request.SearchType;
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            // Add filtering parameters if provided
            if (request.Filtering != null)
            {
                var filtering = request.Filtering;
                
                if (!string.IsNullOrWhiteSpace(filtering.Keyword))
                    queryParams["filtering.keyword"] = filtering.Keyword;
                if (filtering.ImageUrls?.Any() == true)
                    queryParams["filtering.image_urls"] = JsonSerializer.Serialize(filtering.ImageUrls);
                if (filtering.MusicIds?.Any() == true)
                    queryParams["filtering.music_ids"] = JsonSerializer.Serialize(filtering.MusicIds);
                if (!string.IsNullOrWhiteSpace(filtering.CatalogId))
                    queryParams["filtering.catalog_id"] = filtering.CatalogId;
                if (!string.IsNullOrWhiteSpace(filtering.CatalogAuthorizedBcId))
                    queryParams["filtering.catalog_authorized_bc_id"] = filtering.CatalogAuthorizedBcId;
                if (filtering.ItemGroupIds?.Any() == true)
                    queryParams["filtering.item_group_ids"] = JsonSerializer.Serialize(filtering.ItemGroupIds);
                if (!string.IsNullOrWhiteSpace(filtering.ProductSetId))
                    queryParams["filtering.product_set_id"] = filtering.ProductSetId;
                if (filtering.SkuIds?.Any() == true)
                    queryParams["filtering.sku_ids"] = JsonSerializer.Serialize(filtering.SkuIds);
                if (filtering.CarouselImageIndex.HasValue)
                    queryParams["filtering.carousel_image_index"] = filtering.CarouselImageIndex.Value.ToString();
                if (filtering.MaterialIds?.Any() == true)
                    queryParams["filtering.material_ids"] = JsonSerializer.Serialize(filtering.MaterialIds);
                if (filtering.Styles?.Any() == true)
                    queryParams["filtering.styles"] = JsonSerializer.Serialize(filtering.Styles);
                if (filtering.Sources?.Any() == true)
                    queryParams["filtering.sources"] = JsonSerializer.Serialize(filtering.Sources);
            }

            return await _apiClient.CallApiAsync<MusicGetResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{MusicEndpoints.GetMusicList}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
