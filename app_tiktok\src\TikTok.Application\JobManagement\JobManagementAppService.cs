using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.JobManagement;
using TikTok.Permissions;
using TikTok.Repositories;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;

namespace TikTok.JobManagement
{
    /// <summary>
    /// Service implementation cho quản lý job
    /// </summary>
    [Authorize(TikTokPermissions.JobManagement.Default)]
    public class JobManagementAppService : ApplicationService, IJobManagementAppService
    {
        private readonly IJobConfigurationRepository _jobConfigurationRepository;
        private readonly IWorkerInfoRepository _workerInfoRepository;
        private readonly IJobRepository _jobRepository;
        private readonly IGuidGenerator _guidGenerator;
        private readonly IJobConfigurationCacheService  _jobConfigurationCacheService;
        private readonly IJobConfigurationStore _jobConfigurationStore;
        private readonly IJobActivityService _jobActivityService;

        public JobManagementAppService(
            IJobConfigurationRepository jobConfigurationRepository,
            IWorkerInfoRepository workerInfoRepository,
            IJobRepository jobRepository,
            IGuidGenerator guidGenerator,
            IJobActivityService jobActivityService,
            IJobConfigurationCacheService jobConfigurationCacheService,
            IJobConfigurationStore jobConfigurationStore)
        {
            _jobConfigurationRepository = jobConfigurationRepository;
            _workerInfoRepository = workerInfoRepository;
            _jobRepository = jobRepository;
            _guidGenerator = guidGenerator;
            _jobActivityService = jobActivityService;
            _jobConfigurationCacheService = jobConfigurationCacheService;
            _jobConfigurationStore = jobConfigurationStore;
        }

        /// <summary>
        /// Lấy cấu hình job hiện tại
        /// </summary>
        /// <returns>Cấu hình job</returns>
        public async Task<JobConfigurationDto> GetCurrentConfigurationAsync()
        {
            var configuration = await _jobConfigurationRepository.GetCurrentConfigurationAsync();
            if (configuration == null)
            {
                throw new UserFriendlyException("Configuration not found");
            }

            return ObjectMapper.Map<JobConfigurationEntity, JobConfigurationDto>(configuration);
        }

        /// <summary>
        /// Cập nhật cấu hình job
        /// </summary>
        /// <param name="input">Thông tin cấu hình</param>
        /// <returns>Cấu hình đã cập nhật</returns>
        [Authorize(TikTokPermissions.JobManagement.Edit)]
        public async Task<JobConfigurationDto> UpdateConfigurationAsync(JobConfigurationDto input)
        {
            var configuration = await _jobConfigurationRepository.GetCurrentConfigurationAsync();
            if (configuration == null)
            {
                configuration = new JobConfigurationEntity(_guidGenerator.Create());
            }

            // Cập nhật thông tin
            configuration.Name = input.Name;
            configuration.Description = input.Description;
            configuration.IsActive = input.IsActive;
            configuration.ManagerJobCron = input.ManagerJobCron;
            configuration.RegisterJobCron = input.RegisterJobCron;
            configuration.WorkerTimeoutMinutes = input.WorkerTimeoutMinutes;
            configuration.MaxWorkers = input.MaxWorkers;

            if (configuration.Id == Guid.Empty)
            {
                await _jobConfigurationRepository.InsertAsync(configuration);
            }
            else
            {
                await _jobConfigurationRepository.UpdateAsync(configuration);
            }

            await _jobConfigurationCacheService.ClearConfigurationCacheAsync();

            return ObjectMapper.Map<JobConfigurationEntity, JobConfigurationDto>(configuration);
        }

        /// <summary>
        /// Bật/tắt hoạt động đồng bộ
        /// </summary>
        /// <param name="isActive">Trạng thái hoạt động</param>
        /// <returns>Kết quả</returns>
        [Authorize(TikTokPermissions.JobManagement.Edit)]
        public async Task<bool> ToggleSyncActivityAsync(bool isActive)
        {
            var configuration = await _jobConfigurationRepository.GetCurrentConfigurationAsync();
            if (configuration == null)
            {
                throw new InvalidOperationException("No configuration found");
            }

            configuration.IsActive = isActive;
            await _jobConfigurationRepository.UpdateAsync(configuration);

            // Clear cache sau khi thay đổi trạng thái
            await _jobConfigurationCacheService.ClearConfigurationCacheAsync();

            if (isActive)
            {
                await _jobConfigurationStore.StartJobSystemAsync();
            }
            else
            {
                await _jobConfigurationStore.StopJobSystemAsync();
            }

            return configuration.IsActive;
        }

        /// <summary>
        /// Lấy danh sách worker
        /// </summary>
        /// <returns>Danh sách worker</returns>
        public async Task<List<WorkerInfoDto>> GetWorkersAsync()
        {
            var workers = await _workerInfoRepository.GetListAsync();
            return ObjectMapper.Map<List<WorkerInfoEntity>, List<WorkerInfoDto>>(workers);
        }

        /// <summary>
        /// Hủy worker
        /// </summary>
        /// <param name="workerId">ID của worker</param>
        /// <returns>Kết quả</returns>
        [Authorize(TikTokPermissions.JobManagement.Edit)]
        public async Task<bool> CancelWorkerAsync(string workerId)
        {
            var worker = await _workerInfoRepository.GetByWorkerIdAsync(workerId);
            if (worker == null)
            {
                return false;
            }

            // Cập nhật trạng thái worker thành Cancelled
            worker.Status = WorkerStatus.Cancelled;
            worker.CompletedAt = DateTime.UtcNow;
            worker.ErrorMessage = "Worker cancelled by user";
            await _workerInfoRepository.UpdateAsync(worker);

            return true;
        }

        /// <summary>
        /// Lấy thống kê hệ thống
        /// </summary>
        /// <returns>Thống kê</returns>
        public async Task<JobSystemStatisticsDto> GetSystemStatisticsAsync()
        {
            var configuration = await _jobConfigurationCacheService.GetCurrentConfigurationWithCacheAsync();
            var workers = await _workerInfoRepository.GetListAsync();
            var allJobs = await _jobRepository.GetListAsync();
            var today = DateTime.UtcNow.Date;

            // Thống kê jobs
            var totalJobs = allJobs.Count;
            var pendingJobs = allJobs.Count(j => j.Status == JobStatus.Pending);
            var inProcessJobs = allJobs.Count(j => j.Status == JobStatus.InProcess);
            var completedJobs = allJobs.Count(j => j.Status == JobStatus.Completed);
            var errorJobs = allJobs.Count(j => j.Status == JobStatus.Error);
            var cancelledJobs = allJobs.Count(j => j.Status == JobStatus.Cancelled);

            // Jobs hoàn thành hôm nay
            var completedJobsToday = allJobs.Count(j => j.Status == JobStatus.Completed &&
                j.CompletedAt.HasValue && j.CompletedAt.Value.Date == today);

            // Tính tỷ lệ thành công
            var processedJobs = allJobs.Where(j => j.Status == JobStatus.Completed || j.Status == JobStatus.Error).ToList();
            var successRate = processedJobs.Any() ? (double)completedJobs / processedJobs.Count * 100 : 0;

            // Tính thời gian xử lý trung bình
            var jobsWithProcessingTime = allJobs.Where(j => j.StartedAt.HasValue && j.CompletedAt.HasValue).ToList();
            var avgProcessingTime = jobsWithProcessingTime.Any()
                ? jobsWithProcessingTime.Average(j => (j.CompletedAt.Value - j.StartedAt.Value).TotalSeconds)
                : 0;

            var lastManagerJobRun = await _jobActivityService.GetLastManagerJobRunAsync();
            var lastRegisterJobRun = await _jobActivityService.GetLastRegisterJobRunAsync();

            return new JobSystemStatisticsDto
            {
                TotalWorkers = workers.Count,
                ActiveWorkers = workers.Count(w => w.Status == WorkerStatus.Working),
                IdleWorkers = workers.Count(w => w.Status == WorkerStatus.Idle),
                ErrorWorkers = workers.Count(w => w.Status == WorkerStatus.Error),
                TimeoutWorkers = workers.Count(w => w.Status == WorkerStatus.Timeout),
                PendingJobs = pendingJobs,
                CompletedJobsToday = completedJobsToday,
                TotalJobs = totalJobs,
                CompletedJobs = completedJobs,
                FailedJobs = errorJobs,
                SuccessRate = Math.Round(successRate, 1),
                AvgProcessingTime = Math.Round(avgProcessingTime, 1),
                LastManagerJobRun = lastManagerJobRun,
                LastRegisterJobRun = lastRegisterJobRun,
                IsSystemActive = configuration?.IsActive ?? false
            };
        }

        /// <summary>
        /// Lấy trạng thái hệ thống
        /// </summary>
        /// <returns>Trạng thái hệ thống</returns>
        public async Task<JobSystemStatusDto> GetSystemStatusAsync()
        {
            var configuration = await _jobConfigurationCacheService.GetCurrentConfigurationWithCacheAsync();
            var workers = await _workerInfoRepository.GetListAsync();
            var allJobs = await _jobRepository.GetListAsync();
            var today = DateTime.UtcNow.Date;

            // Đếm job hoàn thành hôm nay
            var completedJobsToday = allJobs.Count(j => j.Status == JobStatus.Completed &&
                j.CompletedAt.HasValue && j.CompletedAt.Value.Date == today);

            // Đếm job lỗi hôm nay
            var failedJobsToday = allJobs.Count(j => j.Status == JobStatus.Error &&
                j.CompletedAt.HasValue && j.CompletedAt.Value.Date == today);

            // Đếm pending jobs
            var pendingJobs = allJobs.Count(j => j.Status == JobStatus.Pending);

            return new JobSystemStatusDto
            {
                IsActive = configuration?.IsActive ?? false,
                ActiveWorkers = workers.Count(w => w.Status == WorkerStatus.Working),
                TotalWorkers = workers.Count,
                PendingJobs = pendingJobs,
                CompletedJobsToday = completedJobsToday,
                FailedJobsToday = failedJobsToday,
                LastUpdated = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Cấu hình lại hệ thống job
        /// </summary>
        /// <param name="id">ID của cấu hình</param>
        /// <returns>Kết quả</returns>
        [Authorize(TikTokPermissions.JobManagement.Edit)]
        public async Task<bool> ReConfigurationAsync(Guid id)
        {
            try
            {
                await _jobConfigurationStore.ReConfigurationAsync(id);
                return true;
            }
            catch (Exception ex)
            {
                // Log error using base class logger
                throw;
            }
        }

        /// <summary>
        /// Lấy danh sách job đang chờ xử lý
        /// </summary>
        /// <returns>Danh sách job đang chờ</returns>
        public async Task<List<PendingJobDto>> GetPendingJobsAsync()
        {
            var pendingJobs = await _jobRepository.GetPendingJobsAsync(20);

            return pendingJobs.Select(job => new PendingJobDto
            {
                Id = job.Id,
                JobId = job.Id.ToString("N")[..8],
                CommandType = job.CommandType.ToString(),
                Priority = job.Priority.ToString(),
                CreatedTime = job.CreationTime,
                BusinessApplicationName = job.BusinessApplicationId?.ToString() ?? "N/A",
                Status = job.Status.ToString(),
                BusinessApplicationId = job.BusinessApplicationId
            }).ToList();
        }

        /// <summary>
        /// Lấy hoạt động gần đây
        /// </summary>
        /// <returns>Danh sách hoạt động gần đây</returns>
        public async Task<List<RecentActivityDto>> GetRecentActivityAsync()
        {
            var recentJobs = await _jobRepository.GetRecentJobsAsync(20);

            return recentJobs.Select(job => new RecentActivityDto
            {
                Id = job.Id,
                JobId = job.Id.ToString("N")[..8],
                CommandType = job.CommandType.ToString(),
                WorkerId = job.WorkerId ?? "N/A",
                Status = job.Status.ToString(),
                CreatedTime = job.CreationTime,
                StartedTime = job.StartedAt,
                CompletedTime = job.CompletedAt,
                ProcessingTimeSeconds = job.StartedAt.HasValue && job.CompletedAt.HasValue
                    ? (int)(job.CompletedAt.Value - job.StartedAt.Value).TotalSeconds
                    : (int?)null,
                BusinessApplicationName = job.BusinessApplicationId?.ToString() ?? "N/A",
                ErrorMessage = job.ErrorMessage
            }).ToList();
        }
    }
}