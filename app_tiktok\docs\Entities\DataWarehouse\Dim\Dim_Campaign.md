# DimCampaignEntity

## Thông tin bảng dữ liệu

-   **Table**: `Dim_Campaign`
-   **TablePrefix**: `Dim_`
-   **TableName**: `Dim_Campaign`
-   **DbSchema**: `TikTok`

## Mô tả

Dimension table lưu trữ thông tin chiến dịch quảng cáo. **Chỉ chứa những trường cơ bản có thật trong RawCampaignEntity**.

## Cấu trúc bảng

| Tên Field       | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                     |
| --------------- | ------------ | -------- | ------ | ----------------------------------------- |
| Id              | Guid         | ✓        | -      | Kh<PERSON>a ch<PERSON>h surrogate key (Identity)       |
| CampaignId      | string       | ✓        | 100    | ID duy nhất của chiến dịch (Business Key) |
| DimAdAccountId  | Guid         | ✓        | -      | Khóa ngoại liên kết với Dim_AdAccount     |
| AdvertiserId    | string       | ✓        | 100    | ID tài khoản quảng cáo                    |
| CampaignName    | string       | ✓        | 255    | Tên chiến dịch                            |
| ObjectiveType   | string       | ✓        | 100    | Mục tiêu quảng cáo                        |
| CampaignType    | string       | ✓        | 50     | Loại chiến dịch                           |
| BudgetMode      | string       | ✓        | 50     | Chế độ ngân sách                          |
| Budget          | decimal      | ✗        | 18,2   | Ngân sách chiến dịch                      |
| OperationStatus | string       | ✓        | 20     | Trạng thái hoạt động                      |
| CreateTime      | DateTime     | ✗        | -      | Thời gian tạo chiến dịch (UTC)            |
| ModifyTime      | DateTime     | ✗        | -      | Thời gian sửa đổi chiến dịch (UTC)        |
| IsCurrent       | bool         | ✓        | -      | Record hiện tại (SCD Type 2)              |
| EffectiveDate   | DateTime     | ✓        | -      | Ngày hiệu lực                             |
| ExpirationDate  | DateTime     | ✗        | -      | Ngày hết hiệu lực                         |
| CreationTime    | DateTime     | ✓        | -      | Thời gian tạo bản ghi                     |

## ETL Mapping từ RawCampaignEntity

-   **CampaignId** ← RawCampaignEntity.CampaignId
-   **AdvertiserId** ← RawCampaignEntity.AdvertiserId
-   **CampaignName** ← RawCampaignEntity.CampaignName
-   **ObjectiveType** ← RawCampaignEntity.ObjectiveType
-   **CampaignType** ← RawCampaignEntity.CampaignType
-   **BudgetMode** ← RawCampaignEntity.BudgetMode
-   **Budget** ← RawCampaignEntity.Budget
-   **OperationStatus** ← RawCampaignEntity.OperationStatus
-   **CreateTime** ← RawCampaignEntity.CreateTime
-   **ModifyTime** ← RawCampaignEntity.ModifyTime

## Grain Definition

-   **Mỗi dòng đại diện cho**: 1 phiên bản chiến dịch
-   **Unique Key**: Id
-   **Business Key**: CampaignId

## SCD Type 2 - Tracked Changes

-   Budget changes
-   OperationStatus changes
-   BudgetMode changes

## Business Questions

```sql
-- Chiến dịch theo mục tiêu
SELECT ObjectiveType, COUNT(*) FROM DimCampaigns WHERE IsCurrent = 1 GROUP BY ObjectiveType

-- Chiến dịch theo trạng thái
SELECT OperationStatus, COUNT(*) FROM DimCampaigns WHERE IsCurrent = 1 GROUP BY OperationStatus
```
