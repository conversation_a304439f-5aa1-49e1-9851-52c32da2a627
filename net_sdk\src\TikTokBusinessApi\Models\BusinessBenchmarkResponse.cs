/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response data for business benchmark API
    /// </summary>
    public class BusinessBenchmarkResponse
    {
        /// <summary>
        /// Business category
        /// </summary>
        [JsonPropertyName("business_category")]
        public string? BusinessCategory { get; set; }

        /// <summary>
        /// Average number of likes on owned videos across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_likes")]
        public double? AverageLikes { get; set; }

        /// <summary>
        /// Average number of comments on owned videos across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_comments")]
        public double? AverageComments { get; set; }

        /// <summary>
        /// Average number of shares on owned videos across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_shares")]
        public double? AverageShares { get; set; }

        /// <summary>
        /// Average number of videos across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_video_count")]
        public double? AverageVideoCount { get; set; }

        /// <summary>
        /// Average number of followers across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_follower_count")]
        public double? AverageFollowerCount { get; set; }

        /// <summary>
        /// Average increase in followers over the last 30 days across Business Accounts in business category
        /// </summary>
        [JsonPropertyName("average_follower_growth")]
        public double? AverageFollowerGrowth { get; set; }

        /// <summary>
        /// Average engagement rate across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_engagement_rate")]
        public double? AverageEngagementRate { get; set; }

        /// <summary>
        /// Average video views across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_video_views")]
        public double? AverageVideoViews { get; set; }

        /// <summary>
        /// Initializes a new instance of the BusinessBenchmarkResponse class
        /// </summary>
        public BusinessBenchmarkResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"BusinessBenchmarkResponse(BusinessCategory={BusinessCategory}, AverageFollowerCount={AverageFollowerCount}, AverageEngagementRate={AverageEngagementRate})";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not BusinessBenchmarkResponse other)
                return false;

            return BusinessCategory == other.BusinessCategory &&
                   AverageLikes == other.AverageLikes &&
                   AverageComments == other.AverageComments &&
                   AverageShares == other.AverageShares &&
                   AverageVideoCount == other.AverageVideoCount &&
                   AverageFollowerCount == other.AverageFollowerCount &&
                   AverageFollowerGrowth == other.AverageFollowerGrowth &&
                   AverageEngagementRate == other.AverageEngagementRate &&
                   AverageVideoViews == other.AverageVideoViews;
        }

        /// <summary>
        /// Serves as the default hash function
        /// </summary>
        /// <returns>A hash code for the current object</returns>
        public override int GetHashCode()
        {
            var hash = new HashCode();
            hash.Add(BusinessCategory);
            hash.Add(AverageLikes);
            hash.Add(AverageComments);
            hash.Add(AverageShares);
            hash.Add(AverageVideoCount);
            hash.Add(AverageFollowerCount);
            hash.Add(AverageFollowerGrowth);
            hash.Add(AverageEngagementRate);
            hash.Add(AverageVideoViews);
            return hash.ToHashCode();
        }
    }

    /// <summary>
    /// Business category enumeration
    /// </summary>
    public static class BusinessCategory
    {
        public const string ArtAndCrafts = "ART_AND_CRAFTS";
        public const string AutomotiveAndTransportation = "AUTOMOTIVE_AND_TRANSPORTATION";
        public const string Baby = "BABY";
        public const string Beauty = "BEAUTY";
        public const string ClothingAndAccessories = "CLOTHING_AND_ACCESSORIES";
        public const string EducationAndTraining = "EDUCATION_AND_TRAINING";
        public const string Electronics = "ELECTRONICS";
        public const string FinanceAndInvesting = "FINANCE_AND_INVESTING";
        public const string FoodAndBeverage = "FOOD_AND_BEVERAGE";
        public const string Gaming = "GAMING";
        public const string HealthAndWellness = "HEALTH_AND_WELLNESS";
        public const string HomeFurnitureAndAppliances = "HOME_FURNITURE_AND_APPLIANCES";
        public const string MachineryAndEquipment = "MACHINERY_AND_EQUIPMENT";
        public const string MediaAndEntertainment = "MEDIA_AND_ENTERTAINMENT";
        public const string PersonalBlog = "PERSONAL_BLOG";
        public const string Pets = "PETS";
        public const string ProfessionalServices = "PROFESSIONAL_SERVICES";
        public const string PublicAdministration = "PUBLIC_ADMINISTRATION";
        public const string RealEstate = "REAL_ESTATE";
        public const string RestaurantsAndBars = "RESTAURANTS_AND_BARS";
        public const string ShoppingAndRetail = "SHOPPING_AND_RETAIL";
        public const string SoftwareAndApps = "SOFTWARE_AND_APPS";
        public const string SportsFitnessAndOutdoors = "SPORTS_FITNESS_AND_OUTDOORS";
        public const string TravelAndTourism = "TRAVEL_AND_TOURISM";
        public const string Others = "OTHERS";
    }
}
