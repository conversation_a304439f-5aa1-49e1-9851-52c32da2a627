/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Get Pages Models

    /// <summary>
    /// Request model for getting pages
    /// </summary>
    public class PageGetRequest
    {
        /// <summary>
        /// Either advertiser_id or library_id has to be set. To get pages under an ad account, you must specify the advertiser ID.
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Either advertiser_id or library_id has to be set. To get pages under a Business Center, you must specify the ID of the form library that contains the page and leads.
        /// </summary>
        [JsonPropertyName("library_id")]
        public string? LibraryId { get; set; }

        /// <summary>
        /// Current number of pages. Default value: 1. Value range: ≥ 1.
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10. Value range: 1-100.
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Page status. Enum values: EDITED (draft), PUBLISHED (ready).
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Page title. This field will be used to filter the page that contains the words in the title.
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// Filter for pages updated in the specified time range.
        /// </summary>
        [JsonPropertyName("update_time_range")]
        public PageUpdateTimeRange? UpdateTimeRange { get; set; }

        /// <summary>
        /// Page type. Enum values: LEAD_GEN(Instant Form), STORE_FRONT(Storefront Page), APP_PROFILE_PAGE (App Profile Page), TIKTOK_INSTANT_PAGE (Custom Page or Custom TikTok Instant Page), SHOP_ADS_PLP (Shop Ads Product List Page), SHOP_ADS_PDP (Shop Ads Product Detail Page), POP_UP_FORM(Direct Message Page). Default value: LEAD_GEN.
        /// </summary>
        [JsonPropertyName("business_type")]
        public string? BusinessType { get; set; }

        /// <summary>
        /// Page types. If it is passed concurrently with business_type, the value of business_type will be ignored.
        /// </summary>
        [JsonPropertyName("business_types")]
        public List<string>? BusinessTypes { get; set; }
    }

    /// <summary>
    /// Update time range filter for pages
    /// </summary>
    public class PageUpdateTimeRange
    {
        /// <summary>
        /// Required when update_time_range is passed. Start time in the format of YYYY-MM-DD HH:MM:SS (UTC time).
        /// </summary>
        [JsonPropertyName("start")]
        public string? Start { get; set; }

        /// <summary>
        /// Required when update_time_range is passed. End time in the format of YYYY-MM-DD HH:MM:SS (UTC time).
        /// </summary>
        [JsonPropertyName("end")]
        public string? End { get; set; }
    }

    /// <summary>
    /// Response model for getting pages
    /// </summary>
    public class PageGetResponse
    {
        /// <summary>
        /// Page list
        /// </summary>
        [JsonPropertyName("list")]
        public List<PageData>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Page information
    /// </summary>
    public class PageData
    {
        /// <summary>
        /// Page ID
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }

        /// <summary>
        /// Page status. Enum values: EDITED (draft), PUBLISHED (ready).
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// The creation time of the page in the format of YYYY-MM-DD HH:MM:SS (UTC time).
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// The time the page was last updated in the format of YYYY-MM-DD HH:MM:SS (UTC time).
        /// </summary>
        [JsonPropertyName("update_time")]
        public string? UpdateTime { get; set; }

        /// <summary>
        /// The publishing time of the page in the format of YYYY-MM-DD HH:MM:SS (UTC time).
        /// </summary>
        [JsonPropertyName("publish_time")]
        public string? PublishTime { get; set; }

        /// <summary>
        /// Title
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// Preview link of the page. The link is generated after the page is ready, and will be empty when the page is only a draft.
        /// </summary>
        [JsonPropertyName("preview_url")]
        public string? PreviewUrl { get; set; }

        /// <summary>
        /// Page preview thumbnail image
        /// </summary>
        [JsonPropertyName("thumbnail")]
        public string? Thumbnail { get; set; }

        /// <summary>
        /// Returned for all App Profile Pages and TikTok Instant Pages where an App is specified. The ID of the App that is specified in the page.
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Returned only for TikTok Instant Pages. Whether the TikTok Instant Page contains Custom Product Page.
        /// </summary>
        [JsonPropertyName("has_cpp")]
        public bool? HasCpp { get; set; }

        /// <summary>
        /// Returned when your business_type is LEAD_GEN(Instant Form), APP_PROFILE_PAGE (App Profile Page), or TIKTOK_INSTANT_PAGE (Custom Page or Custom TikTok Instant Page). Destination URLs.
        /// </summary>
        [JsonPropertyName("destination_urls")]
        public List<string>? DestinationUrls { get; set; }

        /// <summary>
        /// The type of instant messaging app associated with the TikTok Instant Page. Enum values: MESSENGER, WHATSAPP, IM_URL.
        /// </summary>
        [JsonPropertyName("messaging_app_type")]
        public string? MessagingAppType { get; set; }

        /// <summary>
        /// The ID of the instant messaging app account associated with the TikTok Instant Page.
        /// </summary>
        [JsonPropertyName("messaging_app_account_id")]
        public string? MessagingAppAccountId { get; set; }

        /// <summary>
        /// Whether the page has been transferred to Business Center. Enum values: UNSET, TRANSFERRED
        /// </summary>
        [JsonPropertyName("transfer_status")]
        public string? TransferStatus { get; set; }

        /// <summary>
        /// Page template ID
        /// </summary>
        [JsonPropertyName("template_id")]
        public string? TemplateId { get; set; }

        /// <summary>
        /// ID of the user who created the page
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// Whether Storefront Page has been associated with some ad (Storefront Page cannot be reused, and each page can only be associated with one ad).
        /// </summary>
        [JsonPropertyName("is_associated")]
        public bool? IsAssociated { get; set; }

        /// <summary>
        /// Duplicate source page ID
        /// </summary>
        [JsonPropertyName("duplicate_id")]
        public string? DuplicateId { get; set; }
    }

    #endregion

    #region TIP SDK Access Token Models

    /// <summary>
    /// Request model for creating TIP SDK access token
    /// </summary>
    public class TipSdkAccessTokenCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for creating TIP SDK access token
    /// </summary>
    public class TipSdkAccessTokenCreateResponse
    {
        /// <summary>
        /// The temporary TIP Editor SDK access token. The token is valid for 24 hours.
        /// </summary>
        [JsonPropertyName("tip_sdk_access_token")]
        public string? TipSdkAccessToken { get; set; }
    }

    /// <summary>
    /// Request model for validating TIP SDK access token
    /// </summary>
    public class TipSdkAccessTokenValidateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// A TIP Editor SDK access token
        /// </summary>
        [JsonPropertyName("tip_sdk_access_token")]
        public string TipSdkAccessToken { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for validating TIP SDK access token
    /// </summary>
    public class TipSdkAccessTokenValidateResponse
    {
        /// <summary>
        /// Whether the access token is valid. Supported values: true, false.
        /// </summary>
        [JsonPropertyName("is_valid")]
        public bool? IsValid { get; set; }
    }

    /// <summary>
    /// Request model for renewing TIP SDK access token
    /// </summary>
    public class TipSdkAccessTokenRenewRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// A TIP Editor SDK access token
        /// </summary>
        [JsonPropertyName("tip_sdk_access_token")]
        public string TipSdkAccessToken { get; set; } = string.Empty;
    }

    #endregion
}
