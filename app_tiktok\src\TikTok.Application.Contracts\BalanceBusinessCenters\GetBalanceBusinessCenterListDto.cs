using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.BalanceBusinessCenters
{
    /// <summary>
    /// DTO cho việc tìm kiếm và phân trang ngân sách Business Center
    /// </summary>
    public class GetBalanceBusinessCenterListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Từ khóa tìm kiếm (tìm theo BC ID, tiền tệ)
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// ID của Business Center
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// Tên của Business Center
        /// </summary>
        public string? BcName { get; set; }

        /// <summary>
        /// Tiền tệ của Business Center
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Số dư tài khoản tối thiểu
        /// </summary>
        public decimal? MinAccountBalance { get; set; }

        /// <summary>
        /// Số dư tài khoản tối đa
        /// </summary>
        public decimal? MaxAccountBalance { get; set; }

        /// <summary>
        /// Số dư tiền mặt tối thiểu
        /// </summary>
        public decimal? MinCashBalance { get; set; }

        /// <summary>
        /// Số dư tiền mặt tối đa
        /// </summary>
        public decimal? MaxCashBalance { get; set; }



        /// <summary>
        /// Thời gian nhận ngân sách từ
        /// </summary>
        public DateTime? DateFrom { get; set; }

        /// <summary>
        /// Thời gian nhận ngân sách đến
        /// </summary>
        public DateTime? DateTo { get; set; }

        /// <summary>
        /// Múi giờ của ngân sách
        /// </summary>
        public string? Timezone { get; set; }
    }
}