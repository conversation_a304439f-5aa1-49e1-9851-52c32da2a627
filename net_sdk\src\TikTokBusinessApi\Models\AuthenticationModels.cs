/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Text.Json.Serialization;
using TikTokBusinessApi.Core;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request model for obtaining a long-term access token via /oauth2/access_token/
    /// </summary>
    public class LongTermAccessTokenRequest
    {
        /// <summary>
        /// ID of your developer app
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// Secret of your developer app
        /// </summary>
        [JsonPropertyName("secret")]
        public string Secret { get; set; } = string.Empty;

        /// <summary>
        /// The authorization code generated by your Advertiser authorization URL
        /// </summary>
        [JsonPropertyName("auth_code")]
        public string AuthCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for long-term access token
    /// </summary>
    public class LongTermAccessTokenResponse : TikTokApiResponse<LongTermAccessTokenData>
    {

    }

    /// <summary>
    /// Data model for long-term access token response
    /// </summary>
    public class LongTermAccessTokenData
    {
        /// <summary>
        /// The long-term access token for permission verification
        /// </summary>
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// The list of ad accounts that the token can access
        /// </summary>
        [JsonPropertyName("advertiser_ids")]
        public string[] AdvertiserIds { get; set; } = Array.Empty<string>();

        /// <summary>
        /// The scope of permissions granted to the access token
        /// </summary>
        [JsonPropertyName("scope")]
        public long[] Scope { get; set; } = Array.Empty<long>();
    }

    /// <summary>
    /// Request model for obtaining access token via /oauth/token/
    /// </summary>
    public class AccessTokenRequest
    {
        /// <summary>
        /// ID of your developer app
        /// </summary>
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Secret of your developer app
        /// </summary>
        [JsonPropertyName("client_secret")]
        public string ClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// The authorization code generated by your Advertiser authorization URL
        /// </summary>
        [JsonPropertyName("code")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// The way of generating the access token. Must be "authorization_code"
        /// </summary>
        [JsonPropertyName("grant_type")]
        public string GrantType { get; set; } = "authorization_code";
    }

    /// <summary>
    /// Response model for access token via /oauth/token/
    /// </summary>
    public class AccessTokenResponse
    {
        /// <summary>
        /// Response code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// The log ID of a request, which uniquely identifies the request
        /// </summary>
        [JsonPropertyName("request_id")]
        public string RequestId { get; set; } = string.Empty;

        /// <summary>
        /// The long-term access token for permission verification
        /// </summary>
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// Token type. Enum values: Bearer
        /// </summary>
        [JsonPropertyName("token_type")]
        public string TokenType { get; set; } = string.Empty;

        /// <summary>
        /// The list of ad accounts that the token can access
        /// </summary>
        [JsonPropertyName("advertiser_ids")]
        public string[] AdvertiserIds { get; set; } = Array.Empty<string>();

        /// <summary>
        /// The endpoints that the token can access
        /// </summary>
        [JsonPropertyName("scope")]
        public long[] Scope { get; set; } = Array.Empty<long>();
    }

    /// <summary>
    /// Request model for revoking a long-term access token
    /// </summary>
    public class RevokeLongTermTokenRequest
    {
        /// <summary>
        /// ID of your developer app
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// Secret of your developer app
        /// </summary>
        [JsonPropertyName("secret")]
        public string Secret { get; set; } = string.Empty;

        /// <summary>
        /// The long-term access token that you want to revoke
        /// </summary>
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for revoking a long-term access token
    /// </summary>
    public class RevokeLongTermTokenResponse: TikTokApiResponse<RevokeLongTermTokenData>
    {
    }

    /// <summary>
    /// Data model for revoke long-term token response
    /// </summary>
    public class RevokeLongTermTokenData
    {
        /// <summary>
        /// ID of your developer app
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// The ID list of ad accounts that the token can no longer access
        /// </summary>
        [JsonPropertyName("advertiser_ids")]
        public string[] AdvertiserIds { get; set; } = Array.Empty<string>();
    }

    /// <summary>
    /// Request model for obtaining a short-term access token
    /// </summary>
    public class ShortTermAccessTokenRequest
    {
        /// <summary>
        /// ID of your developer app
        /// </summary>
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Secret of your developer app
        /// </summary>
        [JsonPropertyName("client_secret")]
        public string ClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// The way of generating the access token. Must be "authorization_code"
        /// </summary>
        [JsonPropertyName("grant_type")]
        public string GrantType { get; set; } = "authorization_code";

        /// <summary>
        /// The authorization code generated by your TikTok account holder authorization URL
        /// </summary>
        [JsonPropertyName("auth_code")]
        public string AuthCode { get; set; } = string.Empty;

        /// <summary>
        /// The redirect URL which the client will be directed to
        /// </summary>
        [JsonPropertyName("redirect_uri")]
        public string RedirectUri { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for short-term access token
    /// </summary>
    public class ShortTermAccessTokenResponse
    {
        /// <summary>
        /// Response code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// The log ID of the request, which uniquely identifies a request
        /// </summary>
        [JsonPropertyName("request_id")]
        public string RequestId { get; set; } = string.Empty;

        /// <summary>
        /// Returned data
        /// </summary>
        [JsonPropertyName("data")]
        public ShortTermAccessTokenData? Data { get; set; }
    }

    /// <summary>
    /// Data model for short-term access token response
    /// </summary>
    public class ShortTermAccessTokenData
    {
        /// <summary>
        /// The short-term token for permission verification
        /// </summary>
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// The type of token. Enum value: Bearer
        /// </summary>
        [JsonPropertyName("token_type")]
        public string TokenType { get; set; } = string.Empty;

        /// <summary>
        /// The scope of permissions granted to the access token
        /// </summary>
        [JsonPropertyName("scope")]
        public string Scope { get; set; } = string.Empty;

        /// <summary>
        /// The time remaining until the access token expires, measured in seconds
        /// </summary>
        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }

        /// <summary>
        /// The refresh token, which can be used to renew the access token
        /// </summary>
        [JsonPropertyName("refresh_token")]
        public string RefreshToken { get; set; } = string.Empty;

        /// <summary>
        /// The time remaining until the refresh token expires, measured in seconds
        /// </summary>
        [JsonPropertyName("refresh_token_expires_in")]
        public int RefreshTokenExpiresIn { get; set; }

        /// <summary>
        /// Application specific unique ID of the TikTok account
        /// </summary>
        [JsonPropertyName("open_id")]
        public string OpenId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request model for refreshing a short-term access token
    /// </summary>
    public class RefreshShortTermTokenRequest
    {
        /// <summary>
        /// ID of your developer app
        /// </summary>
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Secret of your developer app
        /// </summary>
        [JsonPropertyName("client_secret")]
        public string ClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// The way of generating the access token. Must be "refresh_token"
        /// </summary>
        [JsonPropertyName("grant_type")]
        public string GrantType { get; set; } = "refresh_token";

        /// <summary>
        /// Refresh token to renew an existing access token
        /// </summary>
        [JsonPropertyName("refresh_token")]
        public string RefreshToken { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for refreshing a short-term access token
    /// </summary>
    public class RefreshShortTermTokenResponse
    {
        /// <summary>
        /// Response code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// The log ID of the request, which uniquely identifies a request
        /// </summary>
        [JsonPropertyName("request_id")]
        public string RequestId { get; set; } = string.Empty;

        /// <summary>
        /// Returned data
        /// </summary>
        [JsonPropertyName("data")]
        public ShortTermAccessTokenData? Data { get; set; }
    }

    /// <summary>
    /// Request model for revoking a short-term access token
    /// </summary>
    public class RevokeShortTermTokenRequest
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Secret of your developer application
        /// </summary>
        [JsonPropertyName("client_secret")]
        public string ClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// The TikTok account access token that you want to revoke
        /// </summary>
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for revoking a short-term access token
    /// </summary>
    public class RevokeShortTermTokenResponse
    {
        /// <summary>
        /// Response code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// The log ID of a request, which uniquely identifies the request
        /// </summary>
        [JsonPropertyName("request_id")]
        public string RequestId { get; set; } = string.Empty;

        /// <summary>
        /// Returned data
        /// </summary>
        [JsonPropertyName("data")]
        public object? Data { get; set; }
    }
}