/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Mentions endpoints
    /// </summary>
    public static class MentionsEndpoints
    {
        /// <summary>
        /// Get mentioned posts endpoint (v1.3)
        /// </summary>
        public const string GetMentionedPosts = "/business/mention/video/list/";

        /// <summary>
        /// Get the details of a mentioned post from mentions webhook endpoint (v1.3)
        /// </summary>
        public const string GetMentionedPostDetails = "/business/mention/video/get/";

        /// <summary>
        /// Get top keywords in mentioned posts endpoint (v1.3)
        /// </summary>
        public const string GetTopKeywords = "/business/mention/top_word/list/";

        /// <summary>
        /// Get top hashtags in mentioned posts endpoint (v1.3)
        /// </summary>
        public const string GetTopHashtags = "/business/mention/top_hashtag/list/";

        /// <summary>
        /// Get mention content in brand hashtags endpoint (v1.3)
        /// </summary>
        public const string GetBrandHashtagContent = "/business/mention/hashtag/video/list/";

        /// <summary>
        /// Get valid brand mention hashtags endpoint (v1.3)
        /// </summary>
        public const string GetValidBrandHashtags = "/business/mention/hashtag/verify/list/";

        /// <summary>
        /// Enable brand mention hashtags for a Business Account endpoint (v1.3)
        /// </summary>
        public const string EnableBrandHashtags = "/business/mention/hashtag/add/";

        /// <summary>
        /// Get enabled hashtags for a Business Account endpoint (v1.3)
        /// </summary>
        public const string GetEnabledHashtags = "/business/mention/hashtag/manage/list/";

        /// <summary>
        /// Delete enabled hashtags for a Business Account endpoint (v1.3)
        /// </summary>
        public const string DeleteEnabledHashtags = "/business/mention/hashtag/remove/";

        /// <summary>
        /// Get mentions in comments endpoint (v1.3)
        /// </summary>
        public const string GetMentionsInComments = "/business/mention/comment/list/";

        /// <summary>
        /// Get the details of a mention in a comment from mentions webhook endpoint (v1.3)
        /// </summary>
        public const string GetCommentMentionDetails = "/business/mention/comment/get/";
    }
}
