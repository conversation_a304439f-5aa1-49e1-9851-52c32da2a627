using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.ReportIntegratedAdAccounts;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.ReportIntegratedAdAccounts
{
    /// <summary>
    /// Interface service cho báo cáo tích hợp AdAccount
    /// </summary>
    public interface IReportIntegratedAdAccountAppService : 
        ICrudAppService<
            ReportIntegratedAdAccountDto,
            Guid,
            GetReportIntegratedAdAccountListDto,
            CreateReportIntegratedAdAccountDto,
            UpdateReportIntegratedAdAccountDto>
    {
        /// <summary>
        /// L<PERSON>y danh sách theo Advertiser IDs và khoảng thời gian
        /// </summary>
        /// <param name="advertiserIds">Danh sách ID tài khoản nhà quảng cáo</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> bắt đầ<PERSON></param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kết thúc</param>
        /// <returns>Danh sách báo cáo tích hợp AdAccount</returns>
        Task<List<ReportIntegratedAdAccountDto>> GetByAdvertiserIdsAndDateRangeAsync(
            List<string> advertiserIds,
            DateTime startDate,
            DateTime endDate);

        /// <summary>
        /// Lấy báo cáo theo Advertiser ID và ngày
        /// </summary>
        /// <param name="advertiserId">ID tài khoản nhà quảng cáo</param>
        /// <param name="date">Ngày báo cáo</param>
        /// <returns>Báo cáo tích hợp AdAccount</returns>
        Task<ReportIntegratedAdAccountDto?> GetByAdvertiserIdAndDateAsync(
            string advertiserId,
            DateTime date);

        /// <summary>
        /// Lấy danh sách báo cáo theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID tài khoản nhà quảng cáo</param>
        /// <returns>Danh sách báo cáo tích hợp AdAccount</returns>
        Task<List<ReportIntegratedAdAccountDto>> GetByAdvertiserIdAsync(string advertiserId);
    }
} 