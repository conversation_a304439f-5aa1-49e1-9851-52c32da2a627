using System;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu báo cáo GMV Max Live Detail Livestream
    /// </summary>
    public interface IGmvMaxLiveLivestreamSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Detail Livestream theo BC ID 
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu (tùy chọn)</param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxLiveLivestreamSyncResult> SyncGmvMaxLiveLivestreamAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Live Detail Livestream cho tất cả Business Centers với khoảng thời gian mặc định
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<GmvMaxLiveLivestreamSyncResult> SyncAllGmvMaxLiveLivestreamForAllBcsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu báo cáo GMV Max Live Detail Livestream
    /// </summary>
    public class GmvMaxLiveLivestreamSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số Campaign đã đồng bộ
        /// </summary>
        public int CampaignCount { get; set; }

        /// <summary>
        /// Số ngày đã đồng bộ
        /// </summary>
        public int DayCount { get; set; }

        /// <summary>
        /// Số Livestream đã đồng bộ
        /// </summary>
        public int LivestreamCount { get; set; }

        /// <summary>
        /// Số Store đã đồng bộ
        /// </summary>
        public int StoreCount { get; set; }
    }
}
