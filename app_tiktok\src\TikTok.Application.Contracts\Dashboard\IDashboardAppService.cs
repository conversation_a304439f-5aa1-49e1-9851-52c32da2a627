using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.Dashboard
{
    /// <summary>
    /// Interface cho Dashboard service
    /// </summary>
    public interface IDashboardAppService : IApplicationService
    {
        /// <summary>
        /// <PERSON><PERSON><PERSON> thống kê overview cho dashboard
        /// </summary>
        /// <returns>Thống kê overview</returns>
        Task<DashboardOverviewDto> GetOverviewStatisticsAsync();
    }
}
