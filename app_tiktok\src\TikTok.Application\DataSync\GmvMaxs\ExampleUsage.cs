using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// <PERSON><PERSON> dụ sử dụng GMV Max Sync Service
    /// </summary>
    public class GmvMaxSyncExample : ITransientDependency
    {
        private readonly IGmvMaxSyncService _gmvMaxSyncService;
        private readonly ILogger<GmvMaxSyncExample> _logger;

        public GmvMaxSyncExample(
            IGmvMaxSyncService gmvMaxSyncService,
            ILogger<GmvMaxSyncExample> logger)
        {
            _gmvMaxSyncService = gmvMaxSyncService;
            _logger = logger;
        }

        /// <summary>
        /// Ví dụ đồng bộ GMV Max cho một Business Center
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        public async Task ExampleSyncSingleBcAsync(string bcId)
        {
            _logger.LogDebug("Bắt đầu ví dụ đồng bộ GMV Max cho BC: {BcId}", bcId);

            try
            {
                var result = await _gmvMaxSyncService.SyncGmvMaxAsync(bcId);

                if (result.IsSuccess)
                {
                    _logger.LogDebug("Đồng bộ thành công!");
                    _logger.LogDebug("Thời gian thực hiện: {Duration}", result.Duration);
                    _logger.LogDebug("Số Advertiser: {AdvertiserCount}", result.AdvertiserCount);
                    _logger.LogDebug("Tổng bản ghi: {TotalSynced}", result.TotalSynced);

                    // Chi tiết từng loại đồng bộ
                    if (result.CampaignSyncResult != null)
                    {
                        _logger.LogDebug("Campaign - Mới: {New}, Cập nhật: {Updated}", 
                            result.CampaignSyncResult.NewRecords, result.CampaignSyncResult.UpdatedRecords);
                    }

                    if (result.ProductCampaignSyncResult != null)
                    {
                        _logger.LogDebug("Product Campaign Report - Mới: {New}, Cập nhật: {Updated}", 
                            result.ProductCampaignSyncResult.NewRecords, result.ProductCampaignSyncResult.UpdatedRecords);
                    }

                    if (result.ProductDetailProductSyncResult != null)
                    {
                        _logger.LogDebug("Product Detail Product Report - Mới: {New}, Cập nhật: {Updated}", 
                            result.ProductDetailProductSyncResult.NewRecords, result.ProductDetailProductSyncResult.UpdatedRecords);
                    }

                    if (result.ProductCreativeSyncResult != null)
                    {
                        _logger.LogDebug("Product Creative Report - Mới: {New}, Cập nhật: {Updated}", 
                            result.ProductCreativeSyncResult.NewRecords, result.ProductCreativeSyncResult.UpdatedRecords);
                    }
                }
                else
                {
                    _logger.LogError("Đồng bộ thất bại: {ErrorMessage}", result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi thực hiện ví dụ đồng bộ GMV Max");
            }
        }

        /// <summary>
        /// Ví dụ đồng bộ GMV Max cho tất cả Business Centers
        /// </summary>
        public async Task ExampleSyncAllBcsAsync()
        {
            _logger.LogDebug("Bắt đầu ví dụ đồng bộ GMV Max cho tất cả BC");

            try
            {
                var result = await _gmvMaxSyncService.SyncAllGmvMaxAsync();

                if (result.IsSuccess)
                {
                    _logger.LogDebug("Đồng bộ tất cả BC thành công!");
                    _logger.LogDebug("Thời gian thực hiện: {Duration}", result.Duration);
                    _logger.LogDebug("Số BC: {BcCount}", result.BcCount);
                    _logger.LogDebug("Số Advertiser: {AdvertiserCount}", result.AdvertiserCount);
                    _logger.LogDebug("Tổng bản ghi: {TotalSynced}", result.TotalSynced);

                    // Chi tiết từng loại đồng bộ
                    if (result.CampaignSyncResult != null)
                    {
                        _logger.LogDebug("Campaign - Mới: {New}, Cập nhật: {Updated}", 
                            result.CampaignSyncResult.NewRecords, result.CampaignSyncResult.UpdatedRecords);
                    }

                    if (result.ProductCampaignSyncResult != null)
                    {
                        _logger.LogDebug("Product Campaign Report - Mới: {New}, Cập nhật: {Updated}", 
                            result.ProductCampaignSyncResult.NewRecords, result.ProductCampaignSyncResult.UpdatedRecords);
                    }

                    if (result.ProductDetailProductSyncResult != null)
                    {
                        _logger.LogDebug("Product Detail Product Report - Mới: {New}, Cập nhật: {Updated}", 
                            result.ProductDetailProductSyncResult.NewRecords, result.ProductDetailProductSyncResult.UpdatedRecords);
                    }

                    if (result.ProductCreativeSyncResult != null)
                    {
                        _logger.LogDebug("Product Creative Report - Mới: {New}, Cập nhật: {Updated}", 
                            result.ProductCreativeSyncResult.NewRecords, result.ProductCreativeSyncResult.UpdatedRecords);
                    }
                }
                else
                {
                    _logger.LogError("Đồng bộ tất cả BC thất bại: {ErrorMessage}", result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi thực hiện ví dụ đồng bộ GMV Max cho tất cả BC");
            }
        }

        /// <summary>
        /// Ví dụ sử dụng với parameters tùy chỉnh
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        public async Task ExampleWithCustomParametersAsync(string bcId)
        {
            _logger.LogDebug("Bắt đầu ví dụ đồng bộ GMV Max với parameters tùy chỉnh cho BC: {BcId}", bcId);

            try
            {
                // Tạo danh sách Advertiser tùy chỉnh
                var advertisers = new List<GmvMaxAdvertiserDto>
                {
                    new GmvMaxAdvertiserDto
                    {
                        AdvertiserId = "ADV001",
                        Name = "Advertiser 1",
                        Timezone = "Asia/Ho_Chi_Minh",
                        Currency = "VND"
                    },
                    new GmvMaxAdvertiserDto
                    {
                        AdvertiserId = "ADV002",
                        Name = "Advertiser 2",
                        Timezone = "Asia/Ho_Chi_Minh",
                        Currency = "VND"
                    }
                };

                // Tạo parameters
                var parameters = new GmvMaxSyncParameters
                {
                    BcId = bcId,
                    TikTokClient = null, // Sẽ được tạo trong service
                    Advertisers = advertisers
                };

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi thực hiện ví dụ đồng bộ GMV Max với parameters");
            }
        }

        /// <summary>
        /// Ví dụ sử dụng trong background job
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        public async Task ExampleBackgroundJobAsync(string bcId)
        {
            _logger.LogDebug("Background job: Bắt đầu đồng bộ GMV Max cho BC: {BcId}", bcId);

            var startTime = DateTime.UtcNow;
            var result = await _gmvMaxSyncService.SyncGmvMaxAsync(bcId);
            var endTime = DateTime.UtcNow;

            _logger.LogDebug("Background job: Hoàn thành đồng bộ GMV Max cho BC: {BcId}", bcId);
            _logger.LogDebug("Background job: Thời gian thực hiện: {Duration}", endTime - startTime);
            _logger.LogDebug("Background job: Kết quả: {Success}, Tổng bản ghi: {TotalSynced}", 
                result.IsSuccess, result.TotalSynced);

            if (!result.IsSuccess)
            {
                _logger.LogError("Background job: Lỗi đồng bộ GMV Max: {ErrorMessage}", result.ErrorMessage);
            }
        }
    }
} 