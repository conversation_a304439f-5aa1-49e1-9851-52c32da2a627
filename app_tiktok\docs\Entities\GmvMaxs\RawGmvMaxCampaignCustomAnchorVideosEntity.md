# RawGmvMaxCampaignCustomAnchorVideosEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawGmvMaxCampaignCustomAnchorVideos`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawGmvMaxCampaignCustomAnchorVideos`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho danh sách video anchor tù<PERSON> chỉnh (Custom Anchor Video List) của GMV Max Campaign. Chứa thông tin về các bài đăng TikTok tùy chỉnh với liên kết sản phẩm tùy chỉnh, đ<PERSON><PERSON><PERSON> sử dụng làm video anchor cho chiế<PERSON> dịch GMV Max.

## Bảng dữ liệu: RawGmvMaxCampaignCustomAnchorVideos

| Tên Field                | Kiểu dữ liệu  | Bắt buộc | Đ<PERSON> dài | <PERSON><PERSON> tả                                                            |
| ------------------------ | ------------- | -------- | ------ | ---------------------------------------------------------------- |
| Id                       | Guid          | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                            |
| CampaignId               | string        | ✅       | 100    | ID của GMV Max Campaign (Foreign Key)                            |
| ItemId                   | string        | ✅       | 100    | ID của bài đăng TikTok tùy chỉnh                                 |
| SpuIdList                | List<string>? | ❌       | -      | Danh sách Product SPU ID được liên kết với bài đăng (JSON array) |
| IdentityId               | string        | ❌       | 100    | ID danh tính liên kết với bài đăng                               |
| IdentityType             | string        | ❌       | 50     | Loại danh tính (AUTH_CODE, TT_USER, BC_AUTH_TT, TTS_TT)          |
| IdentityAuthorizedBcId   | string        | ❌       | 100    | ID Business Center được ủy quyền                                 |
| IdentityAuthorizedShopId | string        | ❌       | 100    | ID TikTok Shop được ủy quyền                                     |
| IdentityStoreId          | string        | ❌       | 100    | ID TikTok Shop của danh tính                                     |
| CreationTime             | DateTime      | ✅       | -      | Thời gian tạo bản ghi (Audit)                                    |
| CreatorId                | Guid?         | ❌       | -      | ID người tạo (Audit)                                             |
| LastModificationTime     | DateTime?     | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                   |
| LastModifierId           | Guid?         | ❌       | -      | ID người sửa đổi cuối (Audit)                                    |

## Cấu trúc dữ liệu

### Thông tin liên kết

-   **CampaignId**: Liên kết với GMV Max Campaign chính
-   **ItemId**: ID duy nhất của bài đăng TikTok tùy chỉnh
-   **SpuIdList**: Danh sách các sản phẩm SPU được liên kết tùy chỉnh

### Thông tin danh tính

-   **IdentityId**: ID của danh tính TikTok liên kết
-   **IdentityType**: Loại danh tính (AUTH_CODE, TT_USER, BC_AUTH_TT, TTS_TT)
-   **IdentityAuthorizedBcId**: Business Center được ủy quyền truy cập
-   **IdentityAuthorizedShopId**: TikTok Shop được ủy quyền truy cập
-   **IdentityStoreId**: TikTok Shop thuộc về danh tính

## Navigation Properties

-   **Campaign**: Liên kết tới RawGmvMaxCampaignsEntity

## Đặc điểm chính

### Video Anchor tùy chỉnh

Custom Anchor Videos là các bài đăng TikTok đặc biệt được chọn để làm video mẫu (anchor) cho chiến dịch GMV Max. Những video này:

-   **Tùy chỉnh sản phẩm**: Có thể liên kết với các sản phẩm SPU cụ thể thay vì sử dụng toàn bộ catalog
-   **Định hướng nội dung**: Được sử dụng làm mẫu cho AI tạo nội dung tương tự
-   **Kiểm soát chất lượng**: Đảm bảo nội dung quảng cáo phù hợp với thương hiệu

### Liên kết danh tính

-   **AUTH_CODE**: Ủy quyền thông qua mã xác thực
-   **TT_USER**: Người dùng TikTok trực tiếp
-   **BC_AUTH_TT**: Business Center được ủy quyền truy cập TikTok
-   **TTS_TT**: TikTok Shop được liên kết với TikTok

## Mục đích sử dụng

-   Lưu trữ danh sách video anchor tùy chỉnh cho GMV Max Campaign
-   Quản lý liên kết giữa video anchor và sản phẩm cụ thể
-   Theo dõi ủy quyền và danh tính của video anchor
-   Hỗ trợ AI tạo nội dung dựa trên video mẫu
-   Kiểm soát chất lượng nội dung quảng cáo
-   Tích hợp với TikTok Business API cho Custom Anchor Video management

## Quan hệ với các entity khác

-   **RawGmvMaxCampaignsEntity**: Mối quan hệ Many-to-One, một campaign có thể có nhiều custom anchor videos
-   **SpuIdList**: Liên kết với danh sách sản phẩm trong TikTok Shop catalog
