# RawGmvMaxLiveDetailLivestreamReportEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawGmvMaxLiveDetailLivestreamReports`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawGmvMaxLiveDetailLivestreamReports`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho báo cáo chi tiết cấp livestream của LIVE GMV Max Campaign. Lưu trữ thông tin chi tiết về hiệu suất từng phòng livestream trong chiến dịch LIVE GMV Max, bao gồm thông tin livestream, trạng thái và các metrics hiệu suất LIVE theo thời gian.

## Bảng dữ liệu: RawGmvMaxLiveDetailLivestreamReports

| Tên Field                       | Kiểu dữ liệu     | <PERSON><PERSON><PERSON> buộ<PERSON> | <PERSON><PERSON> dài | <PERSON><PERSON> tả                                                                    |
| ------------------------------- | ---------------- | -------- | ------ | ------------------------------------------------------------------------ |
| Id                              | Guid             | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                   |
| BcId                            | string           | ✅       | 100    | ID Business Center                                                       |
| AdvertiserId                    | string           | ✅       | 100    | ID nhà quảng cáo                                                         |
| StoreId                         | string           | ✅       | 100    | ID TikTok Shop                                                           |
| CampaignId                      | string           | ✅       | 100    | ID chiến dịch LIVE GMV Max                                               |
| RoomId                          | string           | ✅       | 100    | ID phòng livestream                                                      |
| LiveName                        | string           | ❌       | 500    | Tên livestream                                                           |
| LiveStatus                      | LiveStatus?      | ❌       | -      | Trạng thái livestream (ONGOING: Đang diễn ra, END: Đã kết thúc)         |
| LiveLaunchedTime                | DateTime?        | ❌       | -      | Thời gian bắt đầu livestream (theo time zone của ad account)            |
| LiveDuration                    | string           | ❌       | 50     | Thời lượng livestream (ví dụ: "14h 1m")                                 |
| Cost                            | decimal?         | ❌       | -      | Chi phí quảng cáo cho livestream này                                     |
| NetCost                         | decimal?         | ❌       | -      | Chi phí thực tế (trừ ad credit hoặc coupon)                              |
| Orders                          | int?             | ❌       | -      | Số lượng đơn hàng SKU LIVE từ phòng livestream này                       |
| CostPerOrder                    | decimal?         | ❌       | -      | Chi phí trung bình mỗi đơn hàng LIVE                                     |
| GrossRevenue                    | decimal?         | ❌       | -      | Tổng doanh thu gộp từ đơn hàng LIVE của phòng livestream này             |
| ROI                             | decimal?         | ❌       | -      | Tỷ suất lợi nhuận từ đơn hàng LIVE của phòng livestream này              |
| LiveViews                       | long?            | ❌       | -      | Số lượt xem livestream này                                               |
| CostPerLiveView                 | decimal?         | ❌       | -      | Chi phí trung bình mỗi lượt xem livestream này                           |
| TenSecondLiveViews              | long?            | ❌       | -      | Số lượt xem livestream này ít nhất 10 giây                              |
| CostPerTenSecondLiveView        | decimal?         | ❌       | -      | Chi phí trung bình mỗi lượt xem livestream 10 giây                       |
| LiveFollows                     | int?             | ❌       | -      | Số lượt follow profile trong livestream này                              |
| Currency                        | string           | ✅       | 10     | Tiền tệ - Mã tiền tệ, ví dụ: USD                                         |
| Date                            | DateTime         | ✅       | -      | Ngày giờ tổng hợp báo cáo, UTC format yyyy-MM-dd HH:00:00 (theo giờ)    |
| CreationTime                    | DateTime         | ✅       | -      | Thời gian tạo bản ghi (Audit)                                           |
| CreatorId                       | Guid?            | ❌       | -      | ID người tạo (Audit)                                                     |
| LastModificationTime            | DateTime?        | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                          |
| LastModifierId                  | Guid?            | ❌       | -      | ID người sửa đổi cuối (Audit)                                           |

## Enums liên quan

### LiveStatus

-   **ONGOING**: Livestream đang diễn ra
-   **END**: Livestream đã kết thúc

## Cấu trúc dữ liệu

### Thông tin nhận dạng
-   **BcId**: ID Business Center quản lý
-   **AdvertiserId**: ID tài khoản nhà quảng cáo
-   **StoreId**: ID TikTok Shop liên kết
-   **CampaignId**: ID chiến dịch LIVE GMV Max
-   **RoomId**: ID duy nhất của phòng livestream

### Thông tin livestream
-   **LiveName**: Tên hiển thị của livestream
-   **LiveStatus**: Trạng thái hiện tại (đang diễn ra/đã kết thúc)
-   **LiveLaunchedTime**: Thời gian bắt đầu phát
-   **LiveDuration**: Thời lượng livestream (format: "Xh Ym")

### Metrics hiệu suất tài chính
-   **Cost**: Chi phí quảng cáo cho livestream này
-   **NetCost**: Chi phí thực tế sau chiết khấu
-   **Orders**: Số đơn hàng từ livestream này
-   **CostPerOrder**: Chi phí trung bình mỗi đơn hàng
-   **GrossRevenue**: Doanh thu gộp từ livestream
-   **ROI**: Tỷ suất lợi nhuận

### Metrics hiệu suất engagement
-   **LiveViews**: Tổng lượt xem livestream
-   **CostPerLiveView**: Chi phí mỗi lượt xem
-   **TenSecondLiveViews**: Lượt xem ít nhất 10 giây
-   **CostPerTenSecondLiveView**: Chi phí mỗi lượt xem chất lượng
-   **LiveFollows**: Lượt follow được trong livestream

### Thông tin báo cáo
-   **Currency**: Mã tiền tệ
-   **Date**: Thời điểm tổng hợp báo cáo

## Phương thức đặc biệt

### HasChanged()

```csharp
public bool HasChanged(RawGmvMaxLiveDetailLivestreamReportEntity other)
```

So sánh entity hiện tại với entity khác để phát hiện thay đổi. Bao gồm tất cả các trường nhận dạng, thông tin livestream và metrics hiệu suất.

## Đặc điểm chính của Live Detail Livestream Report

### Individual Room Performance
-   **Room-Level Tracking**: Theo dõi hiệu suất từng phòng LIVE
-   **Real-time Status**: Cập nhật trạng thái LIVE real-time
-   **Duration Analysis**: Phân tích thời lượng và timing

### Granular Metrics
-   **View Quality**: Phân biệt lượt xem thường và chất lượng (10s+)
-   **Cost Breakdown**: Chi phí chi tiết cho từng livestream
-   **ROI per Stream**: Tính toán ROI riêng biệt cho mỗi livestream

### Engagement Tracking
-   **Follow Conversion**: Theo dõi lượt follow trong LIVE
-   **Order Attribution**: Đơn hàng được gán cho livestream cụ thể
-   **Revenue Attribution**: Doanh thu từ từng phòng LIVE

## Mục đích sử dụng

-   Báo cáo hiệu suất chi tiết từng phòng livestream
-   Phân tích ROI cấp độ livestream riêng lẻ
-   So sánh hiệu suất giữa các phòng LIVE khác nhau
-   Tối ưu hóa timing và thời lượng livestream
-   Theo dõi chất lượng engagement trong LIVE
-   Hỗ trợ quyết định đầu tư vào livestream

## Quan hệ với các entity khác

-   **RawGmvMaxLiveCampaignReportEntity**: Many-to-One, thuộc về báo cáo campaign
-   **RawGmvMaxCampaignsEntity**: Many-to-One, thuộc về chiến dịch GMV Max

## Use Cases thực tế

### Live Room Performance Analysis
```
Phân tích hiệu suất từng phòng LIVE:
- So sánh ROI giữa các phòng livestream khác nhau
- Phân tích LiveViews vs TenSecondLiveViews để đánh giá chất lượng nội dung
- Theo dõi CostPerOrder để tối ưu hóa hiệu quả chi phí
```

### Live Schedule Optimization
```
Tối ưu hóa lịch trình phát LIVE:
- Phân tích LiveLaunchedTime để tìm thời điểm tối ưu
- So sánh LiveDuration với hiệu suất để xác định thời lượng lý tưởng
- Theo dõi LiveStatus để quản lý vòng đời livestream
```

### Streamer Performance Evaluation
```
Đánh giá hiệu suất streamer:
- Phân tích LiveFollows để đo lường sức hút cá nhân
- So sánh Orders và GrossRevenue giữa các streamer
- Tối ưu hóa CostPerLiveView cho từng streamer
```