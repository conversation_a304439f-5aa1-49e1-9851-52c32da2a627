﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Threading;
using TikTok.DataSync;

namespace TikTok.BackgroundJobs
{
    public class SyncRawToFactBusinessCenterWorker : AsyncPeriodicBackgroundWorkerBase
    {
        private readonly ILogger<SyncRawToFactBusinessCenterWorker> _logger;

        public SyncRawToFactBusinessCenterWorker(
            AbpAsyncTimer timer,
            IServiceScopeFactory serviceScopeFactory,
            ILogger<SyncRawToFactBusinessCenterWorker> logger) : base(timer, serviceScopeFactory)
        {
            Timer.Period = 1800000; //10 minutes
            _logger = logger;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            try
            {
                _logger.LogDebug("Starting sync from Raw to Dim entities...");

                using var scope = workerContext.ServiceProvider.CreateScope();
                var syncService = scope.ServiceProvider.GetRequiredService<ISyncRawToFactBusinessCenterService>();

                // Sync all data using the service
                var result = await syncService.SyncAllAsync();

                _logger.LogDebug("Completed sync from Raw to Dim entities. Total synced: {TotalSynced}, BC: {BcCount}, AdAccounts: {AdAccountCount}", 
                    result.TotalSynced, result.BusinessCenterCount, result.AdAccountCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during sync from Raw to Dim entities");
                throw;
            }
        }

    }
}
