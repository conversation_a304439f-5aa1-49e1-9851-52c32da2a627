using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// Application Service interface cho việc đồng bộ tối ưu dữ liệu GMV Max
    /// </summary>
    public interface IGmvMaxSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ tất cả dữ liệu GMV Max cho một Business Center
        /// Bao gồm: Chiến dịch, báo cáo chiến dịch, báo c<PERSON>o sản phẩm, báo cáo sáng tạo
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ tổng hợp</returns>
        Task<GmvMaxSyncResult> SyncGmvMaxAsync(string bcId);

        /// <summary>
        /// Đồng bộ tất cả dữ liệu GMV Max cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ tổng hợp</returns>
        Task<GmvMaxSyncResult> SyncAllGmvMaxAsync();
    }
} 