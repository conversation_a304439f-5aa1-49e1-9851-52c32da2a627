using System;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.JobManagement
{
    /// <summary>
    /// DTO cho thông tin worker
    /// </summary>
    public class WorkerInfoDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID của worker (Hangfire job ID)
        /// </summary>
        public string WorkerId { get; set; }

        /// <summary>
        /// Tên worker
        /// </summary>
        public string WorkerName { get; set; }

        /// <summary>
        /// Trạng thái của worker
        /// </summary>
        public WorkerStatus Status { get; set; }

        /// <summary>
        /// Thời gian bắt đầu làm việc
        /// </summary>
        public DateTime? StartedAt { get; set; }

        /// <summary>
        /// Thời gian kết thúc làm việc
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// Thời gian timeout
        /// </summary>
        public DateTime? TimeoutAt { get; set; }

        /// <summary>
        /// ID của Business Application đang được xử lý
        /// </summary>
        public Guid? BusinessApplicationId { get; set; }

        /// <summary>
        /// Thông tin lỗi nếu có
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Kết quả xử lý
        /// </summary>
        public string? Result { get; set; }

        /// <summary>
        /// Thời gian hoạt động cuối cùng
        /// </summary>
        public DateTime? LastHeartbeat { get; set; }
    }
}