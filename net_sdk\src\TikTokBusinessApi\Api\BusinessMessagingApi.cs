/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Business Messaging operations
    /// </summary>
    public class BusinessMessagingApi : IBusinessMessagingApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<BusinessMessagingApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the BusinessMessagingApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public BusinessMessagingApi(IApiClient apiClient, ILogger<BusinessMessagingApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Send a message to a conversation
        /// </summary>
        /// <param name="request">Message send request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing message ID</returns>
        //public async Task<MessageSendResponse> SendMessageAsync(
        //    MessageSendRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.BusinessId))
        //        throw new ArgumentException("Business ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.RecipientType))
        //        throw new ArgumentException("Recipient type cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.Recipient))
        //        throw new ArgumentException("Recipient cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.MessageType))
        //        throw new ArgumentException("Message type cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Sending message of type {MessageType} to {Recipient}", 
        //        request.MessageType, request.Recipient);

        //    return await _apiClient.CallApiAsync<MessageSendResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.SendMessage}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get a list of conversations
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok Business Account</param>
        /// <param name="conversationType">Conversation type (STRANGER or SINGLE)</param>
        /// <param name="limit">The maximum number of conversations to return (1-100, default: 100)</param>
        /// <param name="cursor">Cursor for pagination (default: 0)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of conversations</returns>
        public async Task<ConversationListResponse> GetConversationsAsync(
            string businessId,
            string conversationType,
            int limit = 100,
            int cursor = 0,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(businessId))
                throw new ArgumentException("Business ID cannot be null or empty", nameof(businessId));
            if (string.IsNullOrWhiteSpace(conversationType))
                throw new ArgumentException("Conversation type cannot be null or empty", nameof(conversationType));
            if (limit < 1 || limit > 100)
                throw new ArgumentException("Limit must be between 1 and 100", nameof(limit));

            _logger?.LogInformation("Getting conversations for business {BusinessId} of type {ConversationType}", 
                businessId, conversationType);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = businessId,
                ["conversation_type"] = conversationType,
                ["limit"] = limit.ToString(),
                ["cursor"] = cursor.ToString()
            };

            return await _apiClient.CallApiAsync<ConversationListResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.GetConversations}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get a list of messages in a conversation
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok Business Account</param>
        /// <param name="conversationId">Conversation ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of messages and participants</returns>
        public async Task<MessageContentListResponse> GetMessagesAsync(
            string businessId,
            string conversationId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(businessId))
                throw new ArgumentException("Business ID cannot be null or empty", nameof(businessId));
            if (string.IsNullOrWhiteSpace(conversationId))
                throw new ArgumentException("Conversation ID cannot be null or empty", nameof(conversationId));

            _logger?.LogInformation("Getting messages for conversation {ConversationId}", conversationId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = businessId,
                ["conversation_id"] = conversationId
            };

            return await _apiClient.CallApiAsync<MessageContentListResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.GetMessages}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Upload an image for use in messages
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok Business Account</param>
        /// <param name="fileStream">Image file stream</param>
        /// <param name="fileName">Name of the file</param>
        /// <param name="mediaType">Media type (currently only IMAGE is supported)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing media ID</returns>
        //public async Task<MediaUploadResponse> UploadMediaAsync(
        //    string businessId,
        //    Stream fileStream,
        //    string fileName,
        //    string mediaType,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(businessId))
        //        throw new ArgumentException("Business ID cannot be null or empty", nameof(businessId));
        //    if (fileStream == null)
        //        throw new ArgumentNullException(nameof(fileStream));
        //    if (string.IsNullOrWhiteSpace(fileName))
        //        throw new ArgumentException("File name cannot be null or empty", nameof(fileName));
        //    if (string.IsNullOrWhiteSpace(mediaType))
        //        throw new ArgumentException("Media type cannot be null or empty", nameof(mediaType));

        //    _logger?.LogInformation("Uploading media file {FileName} for business {BusinessId}", fileName, businessId);

        //    // Create multipart form data content
        //    using var content = new MultipartFormDataContent();
        //    content.Add(new StringContent(businessId), "business_id");
        //    content.Add(new StringContent(mediaType), "media_type");
        //    content.Add(new StreamContent(fileStream), "file", fileName);

        //    return await _apiClient.CallApiAsync<MediaUploadResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.UploadMedia}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: content,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Download an image from a message
        /// </summary>
        /// <param name="request">Media download request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing download URL</returns>
        //public async Task<MediaDownloadResponse> DownloadMediaAsync(
        //    MediaDownloadRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.BusinessId))
        //        throw new ArgumentException("Business ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.ConversationId))
        //        throw new ArgumentException("Conversation ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.MessageId))
        //        throw new ArgumentException("Message ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.MediaId))
        //        throw new ArgumentException("Media ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.MediaType))
        //        throw new ArgumentException("Media type cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Downloading media {MediaId} from message {MessageId}", 
        //        request.MediaId, request.MessageId);

        //    return await _apiClient.CallApiAsync<MediaDownloadResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.DownloadMedia}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Create a Business Messaging Webhook configuration
        /// </summary>
        /// <param name="request">Webhook configuration request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing webhook configuration</returns>
        //public async Task<BusinessMessagingWebhookResponse> CreateWebhookAsync(
        //    BusinessMessagingWebhookRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AppId))
        //        throw new ArgumentException("App ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.Secret))
        //        throw new ArgumentException("Secret cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.EventType))
        //        throw new ArgumentException("Event type cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.CallbackUrl))
        //        throw new ArgumentException("Callback URL cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Creating webhook configuration for app {AppId} with event type {EventType}",
        //        request.AppId, request.EventType);

        //    return await _apiClient.CallApiAsync<BusinessMessagingWebhookResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.CreateWebhook}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get a Business Messaging Webhook configuration
        /// </summary>
        /// <param name="appId">ID of your developer application</param>
        /// <param name="secret">Secret of your developer application</param>
        /// <param name="eventType">The type of Webhook event (DIRECT_MESSAGE)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing webhook configuration</returns>
        public async Task<BusinessMessagingWebhookResponse> GetWebhookAsync(
            string appId,
            string secret,
            string eventType,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(appId))
                throw new ArgumentException("App ID cannot be null or empty", nameof(appId));
            if (string.IsNullOrWhiteSpace(secret))
                throw new ArgumentException("Secret cannot be null or empty", nameof(secret));
            if (string.IsNullOrWhiteSpace(eventType))
                throw new ArgumentException("Event type cannot be null or empty", nameof(eventType));

            _logger?.LogInformation("Getting webhook configuration for app {AppId} with event type {EventType}",
                appId, eventType);

            var queryParams = new Dictionary<string, string>
            {
                ["app_id"] = appId,
                ["secret"] = secret,
                ["event_type"] = eventType
            };

            return await _apiClient.CallApiAsync<BusinessMessagingWebhookResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.GetWebhook}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Delete a Business Messaging Webhook configuration
        /// </summary>
        /// <param name="request">Webhook delete request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deleted webhook configuration</returns>
        //public async Task<BusinessMessagingWebhookResponse> DeleteWebhookAsync(
        //    BusinessMessagingWebhookDeleteRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AppId))
        //        throw new ArgumentException("App ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.Secret))
        //        throw new ArgumentException("Secret cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.EventType))
        //        throw new ArgumentException("Event type cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Deleting webhook configuration for app {AppId} with event type {EventType}",
        //        request.AppId, request.EventType);

        //    return await _apiClient.CallApiAsync<BusinessMessagingWebhookResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.DeleteWebhook}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Create an automatic message for a Business Account
        /// </summary>
        /// <param name="request">Auto message create request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing auto message ID</returns>
        //public async Task<AutoMessageResponse> CreateAutoMessageAsync(
        //    AutoMessageCreateRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.BusinessId))
        //        throw new ArgumentException("Business ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AutoMessageType))
        //        throw new ArgumentException("Auto message type cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Creating auto message of type {AutoMessageType} for business {BusinessId}",
        //        request.AutoMessageType, request.BusinessId);

        //    return await _apiClient.CallApiAsync<AutoMessageResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.CreateAutoMessage}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Update the automatic message for a Business Account
        /// </summary>
        /// <param name="request">Auto message update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing auto message ID</returns>
        //public async Task<AutoMessageResponse> UpdateAutoMessageAsync(
        //    AutoMessageUpdateRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.BusinessId))
        //        throw new ArgumentException("Business ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AutoMessageId))
        //        throw new ArgumentException("Auto message ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AutoMessageType))
        //        throw new ArgumentException("Auto message type cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Updating auto message {AutoMessageId} for business {BusinessId}",
        //        request.AutoMessageId, request.BusinessId);

        //    return await _apiClient.CallApiAsync<AutoMessageResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.UpdateAutoMessage}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Turn on or turn off an automatic message for a Business Account
        /// </summary>
        /// <param name="request">Auto message status update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Empty response indicating success</returns>
        //public async Task UpdateAutoMessageStatusAsync(
        //    AutoMessageStatusUpdateRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.BusinessId))
        //        throw new ArgumentException("Business ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AutoMessageType))
        //        throw new ArgumentException("Auto message type cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.OperationStatus))
        //        throw new ArgumentException("Operation status cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Updating auto message status to {OperationStatus} for type {AutoMessageType} in business {BusinessId}",
        //        request.OperationStatus, request.AutoMessageType, request.BusinessId);

        //    await _apiClient.CallApiAsync<object>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.UpdateAutoMessageStatus}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get the automatic messages for a Business Account
        /// </summary>
        /// <param name="businessId">Application specific unique identifier for the TikTok Business Account</param>
        /// <param name="autoMessageType">The type of automatic message</param>
        /// <param name="autoMessageId">The ID of the automatic message (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing auto messages</returns>
        public async Task<AutoMessageGetResponse> GetAutoMessagesAsync(
            string businessId,
            string autoMessageType,
            string? autoMessageId = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(businessId))
                throw new ArgumentException("Business ID cannot be null or empty", nameof(businessId));
            if (string.IsNullOrWhiteSpace(autoMessageType))
                throw new ArgumentException("Auto message type cannot be null or empty", nameof(autoMessageType));

            _logger?.LogInformation("Getting auto messages of type {AutoMessageType} for business {BusinessId}",
                autoMessageType, businessId);

            var queryParams = new Dictionary<string, string>
            {
                ["business_id"] = businessId,
                ["auto_message_type"] = autoMessageType
            };

            if (!string.IsNullOrWhiteSpace(autoMessageId))
            {
                queryParams["auto_message_id"] = autoMessageId;
            }

            return await _apiClient.CallApiAsync<AutoMessageGetResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.GetAutoMessages}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Delete the automatic message for a Business Account
        /// </summary>
        /// <param name="request">Auto message delete request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Empty response indicating success</returns>
        //public async Task DeleteAutoMessageAsync(
        //    AutoMessageDeleteRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.BusinessId))
        //        throw new ArgumentException("Business ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AutoMessageType))
        //        throw new ArgumentException("Auto message type cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AutoMessageId))
        //        throw new ArgumentException("Auto message ID cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Deleting auto message {AutoMessageId} for business {BusinessId}",
        //        request.AutoMessageId, request.BusinessId);

        //    await _apiClient.CallApiAsync<object>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.DeleteAutoMessage}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Sort the automatic message for a Business Account
        /// </summary>
        /// <param name="request">Auto message sort request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Empty response indicating success</returns>
        //public async Task SortAutoMessagesAsync(
        //    AutoMessageSortRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.BusinessId))
        //        throw new ArgumentException("Business ID cannot be null or empty", nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.AutoMessageType))
        //        throw new ArgumentException("Auto message type cannot be null or empty", nameof(request));
        //    if (request.AutoMessageIds == null || request.AutoMessageIds.Count == 0)
        //        throw new ArgumentException("Auto message IDs cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Sorting auto messages for business {BusinessId}", request.BusinessId);

        //    await _apiClient.CallApiAsync<object>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BusinessMessagingEndpoints.SortAutoMessages}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}
    }
}
