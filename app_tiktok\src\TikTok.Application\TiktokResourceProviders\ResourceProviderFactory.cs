﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.ResourceProviders;

namespace TikTok.TiktokResourceProviders
{
    public class ResourceProviderFactory : IResourceProviderFactory
    {
        private readonly IEnumerable<IResourcePermissionProvider> _providers;
        public ResourceProviderFactory(IEnumerable<IResourcePermissionProvider> provider)
        {
            _providers = provider;
        }

        public IEnumerable<IResourcePermissionProvider> GetAll()
        {
            return _providers;
        }

        public IResourcePermissionProvider GetProvider(string resourceType)
        {
            return _providers.FirstOrDefault(x => x.ResourceType == resourceType);
        }
    }
}
