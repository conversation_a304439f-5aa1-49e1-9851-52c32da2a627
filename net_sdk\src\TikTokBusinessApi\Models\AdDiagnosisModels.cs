/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request model for ad diagnosis API
    /// </summary>
    public class AdDiagnosisRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public AdDiagnosisFiltering? Filtering { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisRequest class
        /// </summary>
        public AdDiagnosisRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisRequest class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="filtering">Filtering conditions</param>
        public AdDiagnosisRequest(string advertiserId, AdDiagnosisFiltering? filtering = null)
        {
            AdvertiserId = advertiserId;
            Filtering = filtering;
        }
    }

    /// <summary>
    /// Filtering conditions for ad diagnosis requests
    /// </summary>
    public class AdDiagnosisFiltering
    {
        /// <summary>
        /// IDs of the ad groups that you want to get diagnosis for. Max size: 20.
        /// </summary>
        [JsonPropertyName("adgroup_ids")]
        public List<string>? AdgroupIds { get; set; }

        /// <summary>
        /// The issue category or categories.
        /// Allowed values: CREATIVE, BID_AND_BUDGET, EVENT_TRACK
        /// If multiple values are specified, diagnoses belonging to any of the specified issue categories will be returned.
        /// If this field is not specified, diagnoses belonging to all issue categories will be returned.
        /// </summary>
        [JsonPropertyName("issue_category")]
        public List<string>? IssueCategory { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisFiltering class
        /// </summary>
        public AdDiagnosisFiltering()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisFiltering class with parameters
        /// </summary>
        /// <param name="adgroupIds">Ad group IDs to filter</param>
        /// <param name="issueCategory">Issue categories to filter</param>
        public AdDiagnosisFiltering(List<string>? adgroupIds = null, List<string>? issueCategory = null)
        {
            AdgroupIds = adgroupIds;
            IssueCategory = issueCategory;
        }
    }

    /// <summary>
    /// Response data for ad diagnosis requests
    /// </summary>
    public class AdDiagnosisResponse
    {
        /// <summary>
        /// List of ad groups with the corresponding diagnoses
        /// </summary>
        [JsonPropertyName("results")]
        public List<AdDiagnosisResult>? Results { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisResponse class
        /// </summary>
        public AdDiagnosisResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisResponse class with parameters
        /// </summary>
        /// <param name="results">List of diagnosis results</param>
        public AdDiagnosisResponse(List<AdDiagnosisResult>? results = null)
        {
            Results = results;
        }
    }

    /// <summary>
    /// Ad group diagnosis result
    /// </summary>
    public class AdDiagnosisResult
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdgroupId { get; set; }

        /// <summary>
        /// Ad group name
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdgroupName { get; set; }

        /// <summary>
        /// Diagnosis for the ad group
        /// </summary>
        [JsonPropertyName("diagnosis")]
        public AdDiagnosis? Diagnosis { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisResult class
        /// </summary>
        public AdDiagnosisResult()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisResult class with parameters
        /// </summary>
        /// <param name="adgroupId">Ad group ID</param>
        /// <param name="adgroupName">Ad group name</param>
        /// <param name="diagnosis">Diagnosis information</param>
        public AdDiagnosisResult(string? adgroupId = null, string? adgroupName = null, AdDiagnosis? diagnosis = null)
        {
            AdgroupId = adgroupId;
            AdgroupName = adgroupName;
            Diagnosis = diagnosis;
        }
    }

    /// <summary>
    /// Diagnosis information for an ad group
    /// </summary>
    public class AdDiagnosis
    {
        /// <summary>
        /// The date and time (UTC + 0 time) when the diagnosis was requested, in the format of "YYYY-MM-DD HH:MM:SS"
        /// </summary>
        [JsonPropertyName("diagnosis_time")]
        public string? DiagnosisTime { get; set; }

        /// <summary>
        /// Suggestions for the ad group
        /// </summary>
        [JsonPropertyName("suggestions")]
        public List<AdDiagnosisSuggestions>? Suggestions { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosis class
        /// </summary>
        public AdDiagnosis()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosis class with parameters
        /// </summary>
        /// <param name="diagnosisTime">Diagnosis time</param>
        /// <param name="suggestions">List of suggestions</param>
        public AdDiagnosis(string? diagnosisTime = null, List<AdDiagnosisSuggestions>? suggestions = null)
        {
            DiagnosisTime = diagnosisTime;
            Suggestions = suggestions;
        }
    }

    /// <summary>
    /// Suggestions for an ad group
    /// </summary>
    public class AdDiagnosisSuggestions
    {
        /// <summary>
        /// Suggestions for creatives in the ad group
        /// </summary>
        [JsonPropertyName("creative")]
        public List<AdDiagnosisCreativeSuggestion>? Creative { get; set; }

        /// <summary>
        /// Suggestions for the ad group regarding bidding and budget
        /// </summary>
        [JsonPropertyName("bid_and_budget")]
        public List<AdDiagnosisBidBudgetSuggestion>? BidAndBudget { get; set; }

        /// <summary>
        /// Suggestions for the ad group regarding event tracking
        /// </summary>
        [JsonPropertyName("event_track")]
        public List<AdDiagnosisEventTrackSuggestion>? EventTrack { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisSuggestions class
        /// </summary>
        public AdDiagnosisSuggestions()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisSuggestions class with parameters
        /// </summary>
        /// <param name="creative">Creative suggestions</param>
        /// <param name="bidAndBudget">Bid and budget suggestions</param>
        /// <param name="eventTrack">Event track suggestions</param>
        public AdDiagnosisSuggestions(List<AdDiagnosisCreativeSuggestion>? creative = null, 
            List<AdDiagnosisBidBudgetSuggestion>? bidAndBudget = null, 
            List<AdDiagnosisEventTrackSuggestion>? eventTrack = null)
        {
            Creative = creative;
            BidAndBudget = bidAndBudget;
            EventTrack = eventTrack;
        }
    }

    /// <summary>
    /// Creative suggestion for ad diagnosis
    /// </summary>
    public class AdDiagnosisCreativeSuggestion
    {
        /// <summary>
        /// The date and time (UTC + 0 time) when the issue was detected and the suggestion was generated
        /// </summary>
        [JsonPropertyName("suggestion_time")]
        public string? SuggestionTime { get; set; }

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("vid")]
        public string? Vid { get; set; }

        /// <summary>
        /// Ad name
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// The suggestion for the issue. Enum values: NOBGM, VIDEO_LENGTH, VIDEO_RESOLUTION
        /// </summary>
        [JsonPropertyName("issue_suggestion")]
        public string? IssueSuggestion { get; set; }

        /// <summary>
        /// Suggestion ID
        /// </summary>
        [JsonPropertyName("suggestion_id")]
        public string? SuggestionId { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisCreativeSuggestion class
        /// </summary>
        public AdDiagnosisCreativeSuggestion()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisCreativeSuggestion class with parameters
        /// </summary>
        /// <param name="suggestionTime">Suggestion time</param>
        /// <param name="vid">Video ID</param>
        /// <param name="name">Ad name</param>
        /// <param name="adId">Ad ID</param>
        /// <param name="issueSuggestion">Issue suggestion</param>
        /// <param name="suggestionId">Suggestion ID</param>
        public AdDiagnosisCreativeSuggestion(string? suggestionTime = null, string? vid = null, string? name = null,
            string? adId = null, string? issueSuggestion = null, string? suggestionId = null)
        {
            SuggestionTime = suggestionTime;
            Vid = vid;
            Name = name;
            AdId = adId;
            IssueSuggestion = issueSuggestion;
            SuggestionId = suggestionId;
        }
    }

    /// <summary>
    /// Bid and budget suggestion for ad diagnosis
    /// </summary>
    public class AdDiagnosisBidBudgetSuggestion
    {
        /// <summary>
        /// The date and time (UTC + 0 time) when the issue was detected and the suggestion was generated
        /// </summary>
        [JsonPropertyName("suggestion_time")]
        public string? SuggestionTime { get; set; }

        /// <summary>
        /// Suggestion ID
        /// </summary>
        [JsonPropertyName("suggestion_id")]
        public string? SuggestionId { get; set; }

        /// <summary>
        /// The suggestion for the issue. Enum values: SUGGEST_BID, SUGGEST_BUDGET, NOBID_SWITCH, BUDGET_EDR, BID_EDR
        /// </summary>
        [JsonPropertyName("issue_suggestion")]
        public string? IssueSuggestion { get; set; }

        /// <summary>
        /// Current bid value. Returned if issue_suggestion is SUGGEST_BID.
        /// </summary>
        [JsonPropertyName("bid")]
        public double? Bid { get; set; }

        /// <summary>
        /// Current budget. Returned if issue_suggestion is SUGGEST_BID, SUGGEST_BUDGET, or NOBID_SWITCH.
        /// </summary>
        [JsonPropertyName("budget")]
        public double? Budget { get; set; }

        /// <summary>
        /// Suggested bid value. Returned if issue_suggestion is SUGGEST_BID.
        /// </summary>
        [JsonPropertyName("suggest_bid")]
        public double? SuggestBid { get; set; }

        /// <summary>
        /// Suggested budget value. Returned if issue_suggestion is SUGGEST_BUDGET or NOBID_SWITCH.
        /// </summary>
        [JsonPropertyName("suggest_budget")]
        public double? SuggestBudget { get; set; }

        /// <summary>
        /// Estimated minimum cost. Returned if issue_suggestion is SUGGEST_BID.
        /// </summary>
        [JsonPropertyName("cost_floor")]
        public double? CostFloor { get; set; }

        /// <summary>
        /// Bid EDR recommendations for the ad group
        /// </summary>
        [JsonPropertyName("bid_edr_info")]
        public List<AdDiagnosisBidEdrInfo>? BidEdrInfo { get; set; }

        /// <summary>
        /// Budget EDR recommendations for the ad group
        /// </summary>
        [JsonPropertyName("budget_edr_info")]
        public List<AdDiagnosisBudgetEdrInfo>? BudgetEdrInfo { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisBidBudgetSuggestion class
        /// </summary>
        public AdDiagnosisBidBudgetSuggestion()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisBidBudgetSuggestion class with parameters
        /// </summary>
        /// <param name="suggestionTime">Suggestion time</param>
        /// <param name="suggestionId">Suggestion ID</param>
        /// <param name="issueSuggestion">Issue suggestion</param>
        /// <param name="bid">Current bid</param>
        /// <param name="budget">Current budget</param>
        /// <param name="suggestBid">Suggested bid</param>
        /// <param name="suggestBudget">Suggested budget</param>
        /// <param name="costFloor">Cost floor</param>
        /// <param name="bidEdrInfo">Bid EDR info</param>
        /// <param name="budgetEdrInfo">Budget EDR info</param>
        public AdDiagnosisBidBudgetSuggestion(string? suggestionTime = null, string? suggestionId = null,
            string? issueSuggestion = null, double? bid = null, double? budget = null, double? suggestBid = null,
            double? suggestBudget = null, double? costFloor = null, List<AdDiagnosisBidEdrInfo>? bidEdrInfo = null,
            List<AdDiagnosisBudgetEdrInfo>? budgetEdrInfo = null)
        {
            SuggestionTime = suggestionTime;
            SuggestionId = suggestionId;
            IssueSuggestion = issueSuggestion;
            Bid = bid;
            Budget = budget;
            SuggestBid = suggestBid;
            SuggestBudget = suggestBudget;
            CostFloor = costFloor;
            BidEdrInfo = bidEdrInfo;
            BudgetEdrInfo = budgetEdrInfo;
        }
    }

    /// <summary>
    /// Bid EDR information for ad diagnosis
    /// </summary>
    public class AdDiagnosisBidEdrInfo
    {
        /// <summary>
        /// Recommended bid
        /// </summary>
        [JsonPropertyName("recommended_bid")]
        public double? RecommendedBid { get; set; }

        /// <summary>
        /// Estimated bid increase ratio
        /// </summary>
        [JsonPropertyName("bid_increase_ratio")]
        public double? BidIncreaseRatio { get; set; }

        /// <summary>
        /// Estimated cost
        /// </summary>
        [JsonPropertyName("estimated_cost")]
        public double? EstimatedCost { get; set; }

        /// <summary>
        /// Estimated cost uplift
        /// </summary>
        [JsonPropertyName("cost_uplift")]
        public double? CostUplift { get; set; }

        /// <summary>
        /// Estimated cost uplift ratio
        /// </summary>
        [JsonPropertyName("cost_uplift_ratio")]
        public double? CostUpliftRatio { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisBidEdrInfo class
        /// </summary>
        public AdDiagnosisBidEdrInfo()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisBidEdrInfo class with parameters
        /// </summary>
        /// <param name="recommendedBid">Recommended bid</param>
        /// <param name="bidIncreaseRatio">Bid increase ratio</param>
        /// <param name="estimatedCost">Estimated cost</param>
        /// <param name="costUplift">Cost uplift</param>
        /// <param name="costUpliftRatio">Cost uplift ratio</param>
        public AdDiagnosisBidEdrInfo(double? recommendedBid = null, double? bidIncreaseRatio = null,
            double? estimatedCost = null, double? costUplift = null, double? costUpliftRatio = null)
        {
            RecommendedBid = recommendedBid;
            BidIncreaseRatio = bidIncreaseRatio;
            EstimatedCost = estimatedCost;
            CostUplift = costUplift;
            CostUpliftRatio = costUpliftRatio;
        }
    }

    /// <summary>
    /// Budget EDR information for ad diagnosis
    /// </summary>
    public class AdDiagnosisBudgetEdrInfo
    {
        /// <summary>
        /// Recommended budget
        /// </summary>
        [JsonPropertyName("recommended_budget")]
        public double? RecommendedBudget { get; set; }

        /// <summary>
        /// Estimated budget increase ratio
        /// </summary>
        [JsonPropertyName("budget_increase_ratio")]
        public double? BudgetIncreaseRatio { get; set; }

        /// <summary>
        /// Estimated conversion
        /// </summary>
        [JsonPropertyName("estimated_conversion")]
        public double? EstimatedConversion { get; set; }

        /// <summary>
        /// Estimated conversion uplift
        /// </summary>
        [JsonPropertyName("conversion_uplift")]
        public double? ConversionUplift { get; set; }

        /// <summary>
        /// Estimated conversion uplift ratio
        /// </summary>
        [JsonPropertyName("conversion_uplift_ratio")]
        public double? ConversionUpliftRatio { get; set; }

        /// <summary>
        /// Estimated CPA
        /// </summary>
        [JsonPropertyName("cpa")]
        public double? Cpa { get; set; }

        /// <summary>
        /// Estimated CPA uplift
        /// </summary>
        [JsonPropertyName("cpa_uplift")]
        public double? CpaUplift { get; set; }

        /// <summary>
        /// Estimated CPA uplift ratio
        /// </summary>
        [JsonPropertyName("cpa_uplift_ratio")]
        public double? CpaUpliftRatio { get; set; }

        /// <summary>
        /// Estimated impression
        /// </summary>
        [JsonPropertyName("impression")]
        public double? Impression { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisBudgetEdrInfo class
        /// </summary>
        public AdDiagnosisBudgetEdrInfo()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisBudgetEdrInfo class with parameters
        /// </summary>
        /// <param name="recommendedBudget">Recommended budget</param>
        /// <param name="budgetIncreaseRatio">Budget increase ratio</param>
        /// <param name="estimatedConversion">Estimated conversion</param>
        /// <param name="conversionUplift">Conversion uplift</param>
        /// <param name="conversionUpliftRatio">Conversion uplift ratio</param>
        /// <param name="cpa">CPA</param>
        /// <param name="cpaUplift">CPA uplift</param>
        /// <param name="cpaUpliftRatio">CPA uplift ratio</param>
        /// <param name="impression">Impression</param>
        public AdDiagnosisBudgetEdrInfo(double? recommendedBudget = null, double? budgetIncreaseRatio = null,
            double? estimatedConversion = null, double? conversionUplift = null, double? conversionUpliftRatio = null,
            double? cpa = null, double? cpaUplift = null, double? cpaUpliftRatio = null, double? impression = null)
        {
            RecommendedBudget = recommendedBudget;
            BudgetIncreaseRatio = budgetIncreaseRatio;
            EstimatedConversion = estimatedConversion;
            ConversionUplift = conversionUplift;
            ConversionUpliftRatio = conversionUpliftRatio;
            Cpa = cpa;
            CpaUplift = cpaUplift;
            CpaUpliftRatio = cpaUpliftRatio;
            Impression = impression;
        }
    }

    /// <summary>
    /// Event track suggestion for ad diagnosis
    /// </summary>
    public class AdDiagnosisEventTrackSuggestion
    {
        /// <summary>
        /// The date and time (UTC + 0 time) when the issue was detected and the suggestion was generated
        /// </summary>
        [JsonPropertyName("suggestion_time")]
        public string? SuggestionTime { get; set; }

        /// <summary>
        /// Suggestion ID
        /// </summary>
        [JsonPropertyName("suggestion_id")]
        public string? SuggestionId { get; set; }

        /// <summary>
        /// The suggestion for the issue. Enum values: PIXEL
        /// </summary>
        [JsonPropertyName("issue_suggestion")]
        public string? IssueSuggestion { get; set; }

        /// <summary>
        /// Pixel ID
        /// </summary>
        [JsonPropertyName("pixel_id")]
        public string? PixelId { get; set; }

        /// <summary>
        /// Pixel code
        /// </summary>
        [JsonPropertyName("pixel_code")]
        public string? PixelCode { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisEventTrackSuggestion class
        /// </summary>
        public AdDiagnosisEventTrackSuggestion()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisEventTrackSuggestion class with parameters
        /// </summary>
        /// <param name="suggestionTime">Suggestion time</param>
        /// <param name="suggestionId">Suggestion ID</param>
        /// <param name="issueSuggestion">Issue suggestion</param>
        /// <param name="pixelId">Pixel ID</param>
        /// <param name="pixelCode">Pixel code</param>
        public AdDiagnosisEventTrackSuggestion(string? suggestionTime = null, string? suggestionId = null,
            string? issueSuggestion = null, string? pixelId = null, string? pixelCode = null)
        {
            SuggestionTime = suggestionTime;
            SuggestionId = suggestionId;
            IssueSuggestion = issueSuggestion;
            PixelId = pixelId;
            PixelCode = pixelCode;
        }
    }
}
