/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TikTokBusinessApi.Utilities
{
    /// <summary>
    /// Rate limiting utility to help manage API rate limits
    /// </summary>
    public class RateLimiter : IDisposable
    {
        private readonly ConcurrentDictionary<string, TokenBucket> _buckets;
        private readonly ILogger<RateLimiter>? _logger;
        private readonly Timer _cleanupTimer;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the RateLimiter class
        /// </summary>
        /// <param name="logger">Logger instance (optional)</param>
        public RateLimiter(ILogger<RateLimiter>? logger = null)
        {
            _buckets = new ConcurrentDictionary<string, TokenBucket>();
            _logger = logger;
            
            // Clean up expired buckets every 5 minutes
            _cleanupTimer = new Timer(CleanupExpiredBuckets, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        }

        /// <summary>
        /// Wait for permission to make a request based on rate limiting rules
        /// </summary>
        /// <param name="key">Rate limiting key (e.g., "advertiser_123", "global")</param>
        /// <param name="maxRequests">Maximum number of requests allowed</param>
        /// <param name="timeWindow">Time window for the rate limit</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task that completes when permission is granted</returns>
        public async Task WaitForPermissionAsync(
            string key, 
            int maxRequests, 
            TimeSpan timeWindow, 
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Key cannot be null or empty", nameof(key));
            if (maxRequests <= 0)
                throw new ArgumentException("Max requests must be greater than 0", nameof(maxRequests));
            if (timeWindow <= TimeSpan.Zero)
                throw new ArgumentException("Time window must be greater than zero", nameof(timeWindow));

            var bucket = _buckets.GetOrAdd(key, _ => new TokenBucket(maxRequests, timeWindow, _logger));
            await bucket.WaitForTokenAsync(cancellationToken);
        }

        /// <summary>
        /// Check if a request is allowed without waiting
        /// </summary>
        /// <param name="key">Rate limiting key</param>
        /// <param name="maxRequests">Maximum number of requests allowed</param>
        /// <param name="timeWindow">Time window for the rate limit</param>
        /// <returns>True if request is allowed, false otherwise</returns>
        public bool IsRequestAllowed(string key, int maxRequests, TimeSpan timeWindow)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Key cannot be null or empty", nameof(key));
            if (maxRequests <= 0)
                throw new ArgumentException("Max requests must be greater than 0", nameof(maxRequests));
            if (timeWindow <= TimeSpan.Zero)
                throw new ArgumentException("Time window must be greater than zero", nameof(timeWindow));

            var bucket = _buckets.GetOrAdd(key, _ => new TokenBucket(maxRequests, timeWindow, _logger));
            return bucket.TryConsumeToken();
        }

        /// <summary>
        /// Get the current rate limit status for a key
        /// </summary>
        /// <param name="key">Rate limiting key</param>
        /// <returns>Rate limit status information</returns>
        public RateLimitStatus GetStatus(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Key cannot be null or empty", nameof(key));

            if (_buckets.TryGetValue(key, out var bucket))
            {
                return bucket.GetStatus();
            }

            return new RateLimitStatus
            {
                Key = key,
                AvailableTokens = 0,
                MaxTokens = 0,
                NextRefillTime = DateTimeOffset.UtcNow,
                IsThrottled = false
            };
        }

        /// <summary>
        /// Reset rate limiting for a specific key
        /// </summary>
        /// <param name="key">Rate limiting key to reset</param>
        public void Reset(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Key cannot be null or empty", nameof(key));

            if (_buckets.TryRemove(key, out var bucket))
            {
                bucket.Dispose();
                _logger?.LogDebug("Reset rate limiter for key: {Key}", key);
            }
        }

        /// <summary>
        /// Reset all rate limiting buckets
        /// </summary>
        public void ResetAll()
        {
            var keys = _buckets.Keys;
            foreach (var key in keys)
            {
                if (_buckets.TryRemove(key, out var bucket))
                {
                    bucket.Dispose();
                }
            }
            _logger?.LogDebug("Reset all rate limiters");
        }

        private void CleanupExpiredBuckets(object? state)
        {
            var cutoffTime = DateTimeOffset.UtcNow.AddMinutes(-10); // Remove buckets unused for 10 minutes
            var keysToRemove = new List<string>();

            foreach (var kvp in _buckets)
            {
                if (kvp.Value.LastUsed < cutoffTime)
                {
                    keysToRemove.Add(kvp.Key);
                }
            }

            foreach (var key in keysToRemove)
            {
                if (_buckets.TryRemove(key, out var bucket))
                {
                    bucket.Dispose();
                    _logger?.LogDebug("Cleaned up expired rate limiter bucket for key: {Key}", key);
                }
            }

            if (keysToRemove.Count > 0)
            {
                _logger?.LogDebug("Cleaned up {Count} expired rate limiter buckets", keysToRemove.Count);
            }
        }

        /// <summary>
        /// Dispose of the rate limiter and clean up resources
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _cleanupTimer?.Dispose();
                
                foreach (var bucket in _buckets.Values)
                {
                    bucket.Dispose();
                }
                _buckets.Clear();

                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Rate limit status information
    /// </summary>
    public class RateLimitStatus
    {
        /// <summary>
        /// Rate limiting key
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// Number of available tokens
        /// </summary>
        public int AvailableTokens { get; set; }

        /// <summary>
        /// Maximum number of tokens
        /// </summary>
        public int MaxTokens { get; set; }

        /// <summary>
        /// Time when tokens will be refilled
        /// </summary>
        public DateTimeOffset NextRefillTime { get; set; }

        /// <summary>
        /// Whether requests are currently being throttled
        /// </summary>
        public bool IsThrottled { get; set; }

        /// <summary>
        /// Estimated wait time until next request is allowed
        /// </summary>
        public TimeSpan EstimatedWaitTime { get; set; }
    }

    /// <summary>
    /// Token bucket implementation for rate limiting
    /// </summary>
    internal class TokenBucket : IDisposable
    {
        private readonly int _maxTokens;
        private readonly TimeSpan _refillInterval;
        private readonly ILogger? _logger;
        private readonly SemaphoreSlim _semaphore;
        private readonly object _lock = new();
        
        private int _availableTokens;
        private DateTimeOffset _lastRefill;
        private bool _disposed;

        /// <summary>
        /// Last time this bucket was used
        /// </summary>
        public DateTimeOffset LastUsed { get; private set; }

        public TokenBucket(int maxTokens, TimeSpan refillInterval, ILogger? logger = null)
        {
            _maxTokens = maxTokens;
            _refillInterval = refillInterval;
            _logger = logger;
            _availableTokens = maxTokens;
            _lastRefill = DateTimeOffset.UtcNow;
            LastUsed = DateTimeOffset.UtcNow;
            _semaphore = new SemaphoreSlim(maxTokens, maxTokens);
        }

        public async Task WaitForTokenAsync(CancellationToken cancellationToken = default)
        {
            LastUsed = DateTimeOffset.UtcNow;
            RefillTokens();
            
            await _semaphore.WaitAsync(cancellationToken);
            
            lock (_lock)
            {
                _availableTokens--;
            }
        }

        public bool TryConsumeToken()
        {
            LastUsed = DateTimeOffset.UtcNow;
            RefillTokens();
            
            if (_semaphore.Wait(0))
            {
                lock (_lock)
                {
                    _availableTokens--;
                }
                return true;
            }
            
            return false;
        }

        public RateLimitStatus GetStatus()
        {
            RefillTokens();
            
            lock (_lock)
            {
                var nextRefill = _lastRefill.Add(_refillInterval);
                var waitTime = _availableTokens <= 0 ? nextRefill - DateTimeOffset.UtcNow : TimeSpan.Zero;
                
                return new RateLimitStatus
                {
                    AvailableTokens = _availableTokens,
                    MaxTokens = _maxTokens,
                    NextRefillTime = nextRefill,
                    IsThrottled = _availableTokens <= 0,
                    EstimatedWaitTime = waitTime > TimeSpan.Zero ? waitTime : TimeSpan.Zero
                };
            }
        }

        private void RefillTokens()
        {
            var now = DateTimeOffset.UtcNow;
            
            lock (_lock)
            {
                if (now - _lastRefill >= _refillInterval)
                {
                    var tokensToAdd = _maxTokens - _availableTokens;
                    if (tokensToAdd > 0)
                    {
                        _availableTokens = _maxTokens;
                        _semaphore.Release(tokensToAdd);
                        _lastRefill = now;
                        
                        _logger?.LogDebug("Refilled {TokensAdded} tokens, total available: {AvailableTokens}", 
                            tokensToAdd, _availableTokens);
                    }
                }
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _semaphore?.Dispose();
                _disposed = true;
            }
        }
    }
}
