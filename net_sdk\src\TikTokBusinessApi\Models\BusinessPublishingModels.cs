/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for publishing a video
    /// </summary>
    public class VideoPublishRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Video file to be published
        /// </summary>
        [JsonPropertyName("video_file")]
        public string? VideoFile { get; set; }

        /// <summary>
        /// Video caption/description
        /// </summary>
        [JsonPropertyName("caption")]
        public string? Caption { get; set; }

        /// <summary>
        /// Whether comments are enabled
        /// </summary>
        [JsonPropertyName("comments_disabled")]
        public bool? CommentsDisabled { get; set; }

        /// <summary>
        /// Whether stitches are enabled
        /// </summary>
        [JsonPropertyName("stitch_disabled")]
        public bool? StitchDisabled { get; set; }

        /// <summary>
        /// Whether duets are enabled
        /// </summary>
        [JsonPropertyName("duet_disabled")]
        public bool? DuetDisabled { get; set; }

        /// <summary>
        /// Privacy level of the video
        /// </summary>
        [JsonPropertyName("privacy_level")]
        public string? PrivacyLevel { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"VideoPublishRequest(BusinessId={BusinessId}, Caption={Caption})";
        }
    }

    /// <summary>
    /// Request body for publishing a photo
    /// </summary>
    public class PhotoPublishRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Photo files to be published
        /// </summary>
        [JsonPropertyName("photo_files")]
        public List<string>? PhotoFiles { get; set; }

        /// <summary>
        /// Photo caption/description
        /// </summary>
        [JsonPropertyName("caption")]
        public string? Caption { get; set; }

        /// <summary>
        /// Whether comments are enabled
        /// </summary>
        [JsonPropertyName("comments_disabled")]
        public bool? CommentsDisabled { get; set; }

        /// <summary>
        /// Privacy level of the photo post
        /// </summary>
        [JsonPropertyName("privacy_level")]
        public string? PrivacyLevel { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"PhotoPublishRequest(BusinessId={BusinessId}, Caption={Caption})";
        }
    }

    /// <summary>
    /// Response data for publish status API
    /// </summary>
    public class PublishStatusResponse
    {
        /// <summary>
        /// Post ID
        /// </summary>
        [JsonPropertyName("post_id")]
        public string? PostId { get; set; }

        /// <summary>
        /// Publishing status
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Status message
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        /// <summary>
        /// Video/Photo URL if published successfully
        /// </summary>
        [JsonPropertyName("share_url")]
        public string? ShareUrl { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"PublishStatusResponse(PostId={PostId}, Status={Status})";
        }
    }

    /// <summary>
    /// Response data for hashtag suggestion API
    /// </summary>
    public class HashtagSuggestionResponse
    {
        /// <summary>
        /// List of suggested hashtags
        /// </summary>
        [JsonPropertyName("hashtags")]
        public List<HashtagData>? Hashtags { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"HashtagSuggestionResponse(Hashtags={Hashtags?.Count})";
        }
    }

    /// <summary>
    /// Hashtag data
    /// </summary>
    public class HashtagData
    {
        /// <summary>
        /// Hashtag text
        /// </summary>
        [JsonPropertyName("hashtag")]
        public string? Hashtag { get; set; }

        /// <summary>
        /// Hashtag popularity score
        /// </summary>
        [JsonPropertyName("score")]
        public float? Score { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"HashtagData(Hashtag={Hashtag}, Score={Score})";
        }
    }

    /// <summary>
    /// Request body for setting post authorization
    /// </summary>
    public class PostAuthorizeSettingRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Post ID
        /// </summary>
        [JsonPropertyName("post_id")]
        public string? PostId { get; set; }

        /// <summary>
        /// Whether to enable ad authorization
        /// </summary>
        [JsonPropertyName("authorize")]
        public bool? Authorize { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"PostAuthorizeSettingRequest(BusinessId={BusinessId}, PostId={PostId}, Authorize={Authorize})";
        }
    }

    /// <summary>
    /// Request body for authorizing a post
    /// </summary>
    public class PostAuthorizeRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Post ID
        /// </summary>
        [JsonPropertyName("post_id")]
        public string? PostId { get; set; }

        /// <summary>
        /// Authorization validity period in days
        /// </summary>
        [JsonPropertyName("validity_days")]
        public int? ValidityDays { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"PostAuthorizeRequest(BusinessId={BusinessId}, PostId={PostId}, ValidityDays={ValidityDays})";
        }
    }

    /// <summary>
    /// Response data for post authorization status API
    /// </summary>
    public class PostAuthorizeStatusResponse
    {
        /// <summary>
        /// Post ID
        /// </summary>
        [JsonPropertyName("post_id")]
        public string? PostId { get; set; }

        /// <summary>
        /// Authorization status
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Authorization code
        /// </summary>
        [JsonPropertyName("authorization_code")]
        public string? AuthorizationCode { get; set; }

        /// <summary>
        /// Authorization expiry date
        /// </summary>
        [JsonPropertyName("expiry_date")]
        public string? ExpiryDate { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"PostAuthorizeStatusResponse(PostId={PostId}, Status={Status})";
        }
    }

    /// <summary>
    /// Request body for deleting post authorization
    /// </summary>
    public class PostAuthorizeDeleteRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Post ID
        /// </summary>
        [JsonPropertyName("post_id")]
        public string? PostId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"PostAuthorizeDeleteRequest(BusinessId={BusinessId}, PostId={PostId})";
        }
    }

    /// <summary>
    /// Response data for video settings API
    /// </summary>
    public class VideoSettingsResponse
    {
        /// <summary>
        /// Default privacy setting for new posts
        /// </summary>
        [JsonPropertyName("default_privacy")]
        public string? DefaultPrivacy { get; set; }

        /// <summary>
        /// Whether comments are enabled by default
        /// </summary>
        [JsonPropertyName("comments_enabled")]
        public bool? CommentsEnabled { get; set; }

        /// <summary>
        /// Whether duets are enabled by default
        /// </summary>
        [JsonPropertyName("duets_enabled")]
        public bool? DuetsEnabled { get; set; }

        /// <summary>
        /// Whether stitches are enabled by default
        /// </summary>
        [JsonPropertyName("stitches_enabled")]
        public bool? StitchesEnabled { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"VideoSettingsResponse(DefaultPrivacy={DefaultPrivacy}, CommentsEnabled={CommentsEnabled})";
        }
    }
}
