/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API BC Partners endpoints
    /// </summary>
    public static class BCPartnersEndpoints
    {
        /// <summary>
        /// Get the partners of a BC endpoint (v1.3)
        /// </summary>
        public const string GetPartners = "/v1.3/bc/partner/get/";

        /// <summary>
        /// Add a partner to a BC endpoint (v1.3)
        /// </summary>
        public const string AddPartner = "/v1.3/bc/partner/add/";

        /// <summary>
        /// Delete a partner from a BC endpoint (v1.3)
        /// </summary>
        public const string DeletePartner = "/v1.3/bc/partner/delete/";

        /// <summary>
        /// Cancel the sharing of assets with a partner endpoint (v1.3)
        /// </summary>
        public const string CancelAssetSharing = "/v1.3/bc/partner/asset/delete/";

        /// <summary>
        /// Get the assets of a partner endpoint (v1.3)
        /// </summary>
        public const string GetPartnerAssets = "/v1.3/bc/partner/asset/get/";
    }
}
