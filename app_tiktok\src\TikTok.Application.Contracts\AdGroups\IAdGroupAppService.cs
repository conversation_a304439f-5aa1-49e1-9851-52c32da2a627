﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.AdGroups
{
    /// <summary>
    /// Service interface cho nhóm quảng cáo
    /// </summary>
    public interface IAdGroupAppService :
        ICrudAppService<
            AdGroupDto,
            Guid,
            GetAdGroupListDto,
            CreateAdGroupDto,
            UpdateAdGroupDto>
    {
        /// <summary>
        /// Lấy nhóm quảng cáo theo AdGroup ID
        /// </summary>
        /// <param name="adgroupId">ID của nhóm quảng cáo</param>
        /// <returns>Nhóm quảng cáo</returns>
        Task<AdGroupDto> GetByAdgroupIdAsync(string adgroupId);

        /// <summary>
        /// Lấy danh sách nhóm quảng cáo theo Campaign ID
        /// </summary>
        /// <param name="campaignId">ID của chiến dịch</param>
        /// <returns>Danh sách nhóm quảng cáo</returns>
        Task<PagedResultDto<AdGroupDto>> GetByCampaignIdAsync(string campaignId);

        /// <summary>
        /// Lấy danh sách nhóm quảng cáo theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của nhà quảng cáo</param>
        /// <returns>Danh sách nhóm quảng cáo</returns>
        Task<PagedResultDto<AdGroupDto>> GetByAdvertiserIdAsync(string advertiserId);

        /// <summary>
        /// Lấy danh sách nhóm quảng cáo theo danh sách Campaign IDs
        /// </summary>
        /// <param name="campaignIds">Danh sách ID chiến dịch</param>
        /// <returns>Danh sách nhóm quảng cáo</returns>
        Task<PagedResultDto<AdGroupDto>> GetByCampaignIdsAsync(List<string> campaignIds);

        /// <summary>
        /// Lấy danh sách nhóm quảng cáo theo danh sách AdGroup IDs
        /// </summary>
        /// <param name="adgroupIds">Danh sách ID nhóm quảng cáo</param>
        /// <returns>Danh sách nhóm quảng cáo</returns>
        Task<PagedResultDto<AdGroupDto>> GetByAdgroupIdsAsync(List<string> adgroupIds);

        /// <summary>
        /// Cập nhật trạng thái hoạt động của nhóm quảng cáo
        /// </summary>
        /// <param name="adgroupIds">Danh sách ID nhóm quảng cáo</param>
        /// <param name="operationStatus">Trạng thái hoạt động mới</param>
        /// <returns>Số lượng bản ghi được cập nhật</returns>
        Task<int> UpdateOperationStatusAsync(List<string> adgroupIds, string operationStatus);

        /// <summary>
        /// Lấy danh sách nhóm quảng cáo theo Store ID
        /// </summary>
        /// <param name="storeId">ID của TikTok Shop</param>
        /// <returns>Danh sách nhóm quảng cáo</returns>
        Task<PagedResultDto<AdGroupDto>> GetByStoreIdAsync(string storeId);

        /// <summary>
        /// Lấy danh sách nhóm quảng cáo theo Catalog ID
        /// </summary>
        /// <param name="catalogId">ID của catalog</param>
        /// <returns>Danh sách nhóm quảng cáo</returns>
        Task<PagedResultDto<AdGroupDto>> GetByCatalogIdAsync(string catalogId);

        /// <summary>
        /// Lấy danh sách nhóm quảng cáo theo Identity ID
        /// </summary>
        /// <param name="identityId">ID danh tính</param>
        /// <returns>Danh sách nhóm quảng cáo</returns>
        Task<PagedResultDto<AdGroupDto>> GetByIdentityIdAsync(string identityId);

        /// <summary>
        /// Lấy thống kê nhóm quảng cáo theo Campaign ID
        /// </summary>
        /// <param name="campaignId">ID của chiến dịch</param>
        /// <returns>Thống kê nhóm quảng cáo</returns>
        Task<AdGroupStatisticsDto> GetStatisticsByCampaignIdAsync(string campaignId);

        /// <summary>
        /// Lấy thống kê nhóm quảng cáo theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của nhà quảng cáo</param>
        /// <returns>Thống kê nhóm quảng cáo</returns>
        Task<AdGroupStatisticsDto> GetStatisticsByAdvertiserIdAsync(string advertiserId);

        /// <summary>
        /// Kiểm tra xem AdGroup ID có tồn tại hay không
        /// </summary>
        /// <param name="adgroupId">ID của nhóm quảng cáo</param>
        /// <returns>True nếu tồn tại, False nếu không</returns>
        Task<bool> ExistsAsync(string adgroupId);

        /// <summary>
        /// Lấy danh sách nhóm quảng cáo theo trạng thái hoạt động
        /// </summary>
        /// <param name="operationStatus">Trạng thái hoạt động</param>
        /// <param name="advertiserId">ID nhà quảng cáo (tùy chọn)</param>
        /// <returns>Danh sách nhóm quảng cáo</returns>
        Task<PagedResultDto<AdGroupDto>> GetByOperationStatusAsync(string operationStatus, string advertiserId = null);

        /// <summary>
        /// Lấy danh sách nhóm quảng cáo đang hoạt động
        /// </summary>
        /// <param name="advertiserId">ID nhà quảng cáo (tùy chọn)</param>
        /// <returns>Danh sách nhóm quảng cáo đang hoạt động</returns>
        Task<PagedResultDto<AdGroupDto>> GetActiveAdGroupsAsync(string advertiserId = null);

        /// <summary>
        /// Lấy danh sách nhóm quảng cáo bị tạm dừng
        /// </summary>
        /// <param name="advertiserId">ID nhà quảng cáo (tùy chọn)</param>
        /// <returns>Danh sách nhóm quảng cáo bị tạm dừng</returns>
        Task<PagedResultDto<AdGroupDto>> GetPausedAdGroupsAsync(string advertiserId = null);
    }

    /// <summary>
    /// DTO cho thống kê nhóm quảng cáo
    /// </summary>
    public class AdGroupStatisticsDto
    {
        /// <summary>
        /// Tổng số nhóm quảng cáo
        /// </summary>
        public int TotalAdGroups { get; set; }

        /// <summary>
        /// Số nhóm quảng cáo đang hoạt động
        /// </summary>
        public int ActiveAdGroups { get; set; }

        /// <summary>
        /// Số nhóm quảng cáo bị tạm dừng
        /// </summary>
        public int PausedAdGroups { get; set; }

        /// <summary>
        /// Số nhóm quảng cáo bị từ chối
        /// </summary>
        public int RejectedAdGroups { get; set; }

        /// <summary>
        /// Số nhóm quảng cáo đang chờ duyệt
        /// </summary>
        public int PendingAdGroups { get; set; }

        /// <summary>
        /// Tổng ngân sách của tất cả nhóm quảng cáo
        /// </summary>
        public decimal TotalBudget { get; set; }

        /// <summary>
        /// Ngân sách trung bình của nhóm quảng cáo
        /// </summary>
        public decimal AverageBudget { get; set; }

        /// <summary>
        /// Số nhóm quảng cáo có Smart Performance Campaign
        /// </summary>
        public int SmartPerformanceCampaignAdGroups { get; set; }

        /// <summary>
        /// Số nhóm quảng cáo có Search Result enabled
        /// </summary>
        public int SearchEnabledAdGroups { get; set; }

        /// <summary>
        /// Số nhóm quảng cáo Shopping Ads
        /// </summary>
        public int ShoppingAdsAdGroups { get; set; }

        /// <summary>
        /// Thời gian cập nhật thống kê
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Constructor mặc định
        /// </summary>
        public AdGroupStatisticsDto()
        {
            UpdatedAt = DateTime.UtcNow;
        }
    }
}