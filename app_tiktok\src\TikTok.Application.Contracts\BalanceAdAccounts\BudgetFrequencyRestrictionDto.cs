using System.ComponentModel.DataAnnotations;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// DTO cho các hạn chế về số lần thay đổi ngân sách được phép trong ngày hiện tại
    /// </summary>
    public class BudgetFrequencyRestrictionDto
    {
        /// <summary>
        /// Số lần thay đổi ngân sách tối đa trong ngày
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "MaxChangesPerDay phải lớn hơn hoặc bằng 0")]
        public int MaxChangesPerDay { get; set; }

        /// <summary>
        /// Số lần thay đổi ngân sách đã sử dụng trong ngày hiện tại
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "ChangesUsedToday phải lớn hơn hoặc bằng 0")]
        public int ChangesUsedToday { get; set; }

        /// <summary>
        /// Ngày cuối cùng thay đổi ngân sách
        /// </summary>
        public System.DateTime? LastChangeDate { get; set; }
    }
}