using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTok.Application.Contracts.MessageProviders;
using TikTok.BaseMessages;
using TikTok.Const;
using Volo.Abp.DependencyInjection;
using System.Collections.Generic;

namespace TikTok.Application.MessageProviders
{
    /// <summary>
    /// V<PERSON> dụ sử dụng NotificationService
    /// </summary>
    public class NotificationServiceExample : ITransientDependency
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger<NotificationServiceExample> _logger;

        public NotificationServiceExample(
            INotificationService notificationService,
            ILogger<NotificationServiceExample> logger)
        {
            _notificationService = notificationService;
            _logger = logger;
        }

        /// <summary>
        /// Ví dụ gửi thông báo với workflow mặc định
        /// </summary>
        public async Task SendDefaultNotificationExample()
        {
            var messageInput = new BaseMessageActivityInput()
            {
                AdAccountId = "*********",
                BcId = "bc_001",
                TextContent = "<PERSON><PERSON><PERSON> là thông báo mặc định",
                Name = "<PERSON>ệ thống",
                Phonenumber = "***********"
            };

            await _notificationService.SendNotificationAsync(messageInput);
        }

        /// <summary>
        /// Ví dụ gửi thông báo với workflow cụ thể
        /// </summary>
        public async Task SendSpecificWorkflowNotificationExample()
        {
            var messageInput = new BaseMessageActivityInput()
            {
                AdAccountId = "*********",
                BcId = "bc_001",
                TextContent = "Đây là thông báo Zalo",
                Name = "Hệ thống Zalo",
                OwnerId = "701495915639571502",
                Phonenumber = "***********"
            };

            // Gửi qua workflow Zalo
            await _notificationService.SendNotificationAsync(
                WorkflowConst.RequireSendMessageZaloWorkflow, 
                messageInput);
        }

        /// <summary>
        /// Ví dụ gửi thông báo với thông tin cơ bản
        /// </summary>
        public async Task SendSimpleNotificationExample()
        {
            // Gửi thông báo đơn giản với các tham số cơ bản
            await _notificationService.SendNotificationAsync(
                adAccountId: "*********",
                bcId: "bc_001",
                textContent: "Tài khoản quảng cáo đã được cập nhật thành công",
                phoneNumber: "***********",
                name: "Hệ thống TikTok"
            );
        }

        /// <summary>
        /// Ví dụ gửi thông báo với attachments
        /// </summary>
        public async Task SendNotificationWithAttachmentsExample()
        {
            var messageInput = new BaseMessageActivityInput()
            {
                AdAccountId = "*********",
                BcId = "bc_001",
                TextContent = "Báo cáo đã được tạo",
                Name = "Hệ thống Báo cáo",
                Attachments = new List<string>
                {
                    "https://example.com/report1.pdf",
                    "https://example.com/report2.xlsx"
                }
            };

            await _notificationService.SendNotificationAsync(messageInput);
        }
    }
} 