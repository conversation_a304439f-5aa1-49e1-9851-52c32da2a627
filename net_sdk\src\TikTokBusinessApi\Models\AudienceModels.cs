/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request parameters for uploading an audience file
    /// </summary>
    public class AudienceFileUploadRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The file's MD5, which is used for server-side verification
        /// </summary>
        [JsonPropertyName("file_signature")]
        [Required]
        public string FileSignature { get; set; } = string.Empty;

        /// <summary>
        /// Encryption type. The value for this field must be consistent with the actual file data
        /// </summary>
        [JsonPropertyName("calculate_type")]
        [Required]
        public string CalculateType { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the AudienceFileUploadRequest class
        /// </summary>
        public AudienceFileUploadRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AudienceFileUploadRequest class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="fileSignature">File MD5 signature</param>
        /// <param name="calculateType">Encryption type</param>
        public AudienceFileUploadRequest(string advertiserId, string fileSignature, string calculateType)
        {
            AdvertiserId = advertiserId;
            FileSignature = fileSignature;
            CalculateType = calculateType;
        }
    }

    /// <summary>
    /// Response data for audience file upload
    /// </summary>
    public class AudienceFileUploadResponse
    {
        /// <summary>
        /// Unique path to the file in the repository
        /// </summary>
        [JsonPropertyName("file_path")]
        public string? FilePath { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceFileUploadResponse class
        /// </summary>
        public AudienceFileUploadResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AudienceFileUploadResponse class with parameters
        /// </summary>
        /// <param name="filePath">File path</param>
        public AudienceFileUploadResponse(string? filePath = null)
        {
            FilePath = filePath;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AudienceFileUploadResponse(FilePath={FilePath})";
        }
    }

    /// <summary>
    /// Request parameters for creating an audience by file
    /// </summary>
    public class AudienceCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Audience name. Maximum of 128 characters
        /// </summary>
        [JsonPropertyName("custom_audience_name")]
        [Required]
        [StringLength(128, MinimumLength = 1)]
        public string CustomAudienceName { get; set; } = string.Empty;

        /// <summary>
        /// Encryption type
        /// </summary>
        [JsonPropertyName("calculate_type")]
        [Required]
        public string CalculateType { get; set; } = string.Empty;

        /// <summary>
        /// List of file paths
        /// </summary>
        [JsonPropertyName("file_paths")]
        [Required]
        public List<string> FilePaths { get; set; } = new();

        /// <summary>
        /// Audience sub type
        /// </summary>
        [JsonPropertyName("audience_sub_type")]
        public int? AudienceSubType { get; set; }

        /// <summary>
        /// Retention in days
        /// </summary>
        [JsonPropertyName("retention_in_days")]
        public int? RetentionInDays { get; set; }

        /// <summary>
        /// Audience enhancement
        /// </summary>
        [JsonPropertyName("audience_enhancement")]
        public bool? AudienceEnhancement { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceCreateRequest class
        /// </summary>
        public AudienceCreateRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AudienceCreateRequest class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="customAudienceName">Audience name</param>
        /// <param name="calculateType">Encryption type</param>
        /// <param name="filePaths">List of file paths</param>
        public AudienceCreateRequest(string advertiserId, string customAudienceName, string calculateType, List<string> filePaths)
        {
            AdvertiserId = advertiserId;
            CustomAudienceName = customAudienceName;
            CalculateType = calculateType;
            FilePaths = filePaths;
        }
    }

    /// <summary>
    /// Response data for audience creation
    /// </summary>
    public class AudienceCreateResponse
    {
        /// <summary>
        /// Custom audience ID
        /// </summary>
        [JsonPropertyName("custom_audience_id")]
        public string? CustomAudienceId { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceCreateResponse class
        /// </summary>
        public AudienceCreateResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AudienceCreateResponse class with parameters
        /// </summary>
        /// <param name="customAudienceId">Custom audience ID</param>
        public AudienceCreateResponse(string? customAudienceId = null)
        {
            CustomAudienceId = customAudienceId;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AudienceCreateResponse(CustomAudienceId={CustomAudienceId})";
        }
    }

    /// <summary>
    /// Request parameters for getting audience list
    /// </summary>
    public class AudienceListRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Page number for pagination
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size for pagination
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceListRequest class
        /// </summary>
        public AudienceListRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AudienceListRequest class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        public AudienceListRequest(string advertiserId, int? page = null, int? pageSize = null)
        {
            AdvertiserId = advertiserId;
            Page = page;
            PageSize = pageSize;
        }
    }

    /// <summary>
    /// Audience information
    /// </summary>
    public class AudienceInfo
    {
        /// <summary>
        /// Custom audience ID
        /// </summary>
        [JsonPropertyName("custom_audience_id")]
        public string? CustomAudienceId { get; set; }

        /// <summary>
        /// Audience name
        /// </summary>
        [JsonPropertyName("custom_audience_name")]
        public string? CustomAudienceName { get; set; }

        /// <summary>
        /// Audience type
        /// </summary>
        [JsonPropertyName("audience_type")]
        public string? AudienceType { get; set; }

        /// <summary>
        /// Audience size
        /// </summary>
        [JsonPropertyName("audience_size")]
        public long? AudienceSize { get; set; }

        /// <summary>
        /// Creation time
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Expiration time
        /// </summary>
        [JsonPropertyName("expire_time")]
        public string? ExpireTime { get; set; }

        /// <summary>
        /// Whether this advertiser is the creator of this audience
        /// </summary>
        [JsonPropertyName("is_creator")]
        public bool? IsCreator { get; set; }

        /// <summary>
        /// Audience status
        /// </summary>
        [JsonPropertyName("audience_status")]
        public string? AudienceStatus { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceInfo class
        /// </summary>
        public AudienceInfo()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AudienceInfo(Id={CustomAudienceId}, Name={CustomAudienceName}, Size={AudienceSize})";
        }
    }

    /// <summary>
    /// Response data for audience list
    /// </summary>
    public class AudienceListResponse
    {
        /// <summary>
        /// List of audiences
        /// </summary>
        [JsonPropertyName("list")]
        public List<AudienceInfo>? List { get; set; }

        /// <summary>
        /// Page information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceListResponse class
        /// </summary>
        public AudienceListResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AudienceListResponse class with parameters
        /// </summary>
        /// <param name="list">List of audiences</param>
        /// <param name="pageInfo">Page information</param>
        public AudienceListResponse(List<AudienceInfo>? list = null, PageInfo? pageInfo = null)
        {
            List = list;
            PageInfo = pageInfo;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AudienceListResponse(Count={List?.Count}, Page={PageInfo?.Page})";
        }
    }

    /// <summary>
    /// Request parameters for getting audience details
    /// </summary>
    public class AudienceGetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience IDs
        /// </summary>
        [JsonPropertyName("custom_audience_ids")]
        [Required]
        public List<string> CustomAudienceIds { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AudienceGetRequest class
        /// </summary>
        public AudienceGetRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AudienceGetRequest class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="customAudienceIds">Custom audience IDs</param>
        public AudienceGetRequest(string advertiserId, List<string> customAudienceIds)
        {
            AdvertiserId = advertiserId;
            CustomAudienceIds = customAudienceIds;
        }
    }

    /// <summary>
    /// Detailed audience information
    /// </summary>
    public class AudienceDetailInfo : AudienceInfo
    {
        /// <summary>
        /// Audience sub type
        /// </summary>
        [JsonPropertyName("audience_sub_type")]
        public int? AudienceSubType { get; set; }

        /// <summary>
        /// Modification records
        /// </summary>
        [JsonPropertyName("modify_records")]
        public List<AudienceModifyRecord>? ModifyRecords { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceDetailInfo class
        /// </summary>
        public AudienceDetailInfo()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AudienceDetailInfo(Id={CustomAudienceId}, Name={CustomAudienceName}, Status={AudienceStatus})";
        }
    }

    /// <summary>
    /// Audience modification record
    /// </summary>
    public class AudienceModifyRecord
    {
        /// <summary>
        /// Modification time
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string? ModifyTime { get; set; }

        /// <summary>
        /// Operation type
        /// </summary>
        [JsonPropertyName("operation_type")]
        public string? OperationType { get; set; }

        /// <summary>
        /// Operation status
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string? OperationStatus { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceModifyRecord class
        /// </summary>
        public AudienceModifyRecord()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AudienceModifyRecord(Type={OperationType}, Status={OperationStatus}, Time={ModifyTime})";
        }
    }

    /// <summary>
    /// Response data for audience details
    /// </summary>
    public class AudienceGetResponse
    {
        /// <summary>
        /// List of detailed audience information
        /// </summary>
        [JsonPropertyName("list")]
        public List<AudienceDetailInfo>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceGetResponse class
        /// </summary>
        public AudienceGetResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AudienceGetResponse class with parameters
        /// </summary>
        /// <param name="list">List of detailed audience information</param>
        public AudienceGetResponse(List<AudienceDetailInfo>? list = null)
        {
            List = list;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AudienceGetResponse(Count={List?.Count})";
        }
    }

    /// <summary>
    /// Request parameters for sharing audiences
    /// </summary>
    public class AudienceShareRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience IDs to share
        /// </summary>
        [JsonPropertyName("custom_audience_ids")]
        [Required]
        public List<string> CustomAudienceIds { get; set; } = new();

        /// <summary>
        /// Advertisers that you want to share audiences with
        /// </summary>
        [JsonPropertyName("shared_advertiser_ids")]
        [Required]
        public List<string> SharedAdvertiserIds { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AudienceShareRequest class
        /// </summary>
        public AudienceShareRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AudienceShareRequest class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="customAudienceIds">Custom audience IDs</param>
        /// <param name="sharedAdvertiserIds">Shared advertiser IDs</param>
        public AudienceShareRequest(string advertiserId, List<string> customAudienceIds, List<string> sharedAdvertiserIds)
        {
            AdvertiserId = advertiserId;
            CustomAudienceIds = customAudienceIds;
            SharedAdvertiserIds = sharedAdvertiserIds;
        }
    }

    /// <summary>
    /// Request parameters for deleting audiences
    /// </summary>
    public class AudienceDeleteRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience IDs to delete
        /// </summary>
        [JsonPropertyName("custom_audience_ids")]
        [Required]
        public List<string> CustomAudienceIds { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AudienceDeleteRequest class
        /// </summary>
        public AudienceDeleteRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AudienceDeleteRequest class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="customAudienceIds">Custom audience IDs</param>
        public AudienceDeleteRequest(string advertiserId, List<string> customAudienceIds)
        {
            AdvertiserId = advertiserId;
            CustomAudienceIds = customAudienceIds;
        }
    }

    /// <summary>
    /// Request parameters for applying audiences to ad groups
    /// </summary>
    public class AudienceApplyRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience ID
        /// </summary>
        [JsonPropertyName("custom_audience_id")]
        [Required]
        public string CustomAudienceId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group IDs
        /// </summary>
        [JsonPropertyName("adgroup_ids")]
        [Required]
        public List<string> AdgroupIds { get; set; } = new();

        /// <summary>
        /// Action mode (APPLY or DISCONNECT)
        /// </summary>
        [JsonPropertyName("action_mode")]
        [Required]
        public string ActionMode { get; set; } = string.Empty;

        /// <summary>
        /// Usage mode
        /// </summary>
        [JsonPropertyName("usage_mode")]
        public string? UsageMode { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceApplyRequest class
        /// </summary>
        public AudienceApplyRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AudienceApplyRequest class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="customAudienceId">Custom audience ID</param>
        /// <param name="adgroupIds">Ad group IDs</param>
        /// <param name="actionMode">Action mode</param>
        public AudienceApplyRequest(string advertiserId, string customAudienceId, List<string> adgroupIds, string actionMode)
        {
            AdvertiserId = advertiserId;
            CustomAudienceId = customAudienceId;
            AdgroupIds = adgroupIds;
            ActionMode = actionMode;
        }
    }

    /// <summary>
    /// Success response for audience operations
    /// </summary>
    public class AudienceSuccessResponse
    {
        /// <summary>
        /// Success status
        /// </summary>
        [JsonPropertyName("success")]
        public bool? Success { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceSuccessResponse class
        /// </summary>
        public AudienceSuccessResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AudienceSuccessResponse class with parameters
        /// </summary>
        /// <param name="success">Success status</param>
        public AudienceSuccessResponse(bool? success = null)
        {
            Success = success;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AudienceSuccessResponse(Success={Success})";
        }
    }

    /// <summary>
    /// Request parameters for creating an audience by rules
    /// </summary>
    public class AudienceCreateByRulesRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience name
        /// </summary>
        [JsonPropertyName("custom_audience_name")]
        [Required]
        public string CustomAudienceName { get; set; } = string.Empty;

        /// <summary>
        /// Rule definition for the audience
        /// </summary>
        [JsonPropertyName("rule")]
        [Required]
        public object Rule { get; set; } = new object();

        /// <summary>
        /// Initializes a new instance of the AudienceCreateByRulesRequest class
        /// </summary>
        public AudienceCreateByRulesRequest()
        {
        }
    }

    /// <summary>
    /// Request parameters for updating an audience
    /// </summary>
    public class AudienceUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience ID
        /// </summary>
        [JsonPropertyName("custom_audience_id")]
        [Required]
        public string CustomAudienceId { get; set; } = string.Empty;

        /// <summary>
        /// New custom audience name
        /// </summary>
        [JsonPropertyName("custom_audience_name")]
        public string? CustomAudienceName { get; set; }

        /// <summary>
        /// File paths to add to the audience
        /// </summary>
        [JsonPropertyName("file_paths")]
        public List<string>? FilePaths { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceUpdateRequest class
        /// </summary>
        public AudienceUpdateRequest()
        {
        }
    }

    /// <summary>
    /// Response data for audience update
    /// </summary>
    public class AudienceUpdateResponse
    {
        /// <summary>
        /// Custom audience ID
        /// </summary>
        [JsonPropertyName("custom_audience_id")]
        public string? CustomAudienceId { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceUpdateResponse class
        /// </summary>
        public AudienceUpdateResponse()
        {
        }
    }

    /// <summary>
    /// Response data for audience delete
    /// </summary>
    public class AudienceDeleteResponse
    {
        /// <summary>
        /// List of successfully deleted audience IDs
        /// </summary>
        [JsonPropertyName("custom_audience_ids")]
        public List<string>? CustomAudienceIds { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceDeleteResponse class
        /// </summary>
        public AudienceDeleteResponse()
        {
        }
    }

    /// <summary>
    /// Response data for audience share
    /// </summary>
    public class AudienceShareResponse
    {
        /// <summary>
        /// Success status
        /// </summary>
        [JsonPropertyName("success")]
        public bool? Success { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceShareResponse class
        /// </summary>
        public AudienceShareResponse()
        {
        }
    }

    /// <summary>
    /// Response data for audience apply
    /// </summary>
    public class AudienceApplyResponse
    {
        /// <summary>
        /// Success status
        /// </summary>
        [JsonPropertyName("success")]
        public bool? Success { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceApplyResponse class
        /// </summary>
        public AudienceApplyResponse()
        {
        }
    }

    /// <summary>
    /// Request parameters for getting audience share log
    /// </summary>
    public class AudienceShareLogRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience ID
        /// </summary>
        [JsonPropertyName("custom_audience_id")]
        [Required]
        public string CustomAudienceId { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the AudienceShareLogRequest class
        /// </summary>
        public AudienceShareLogRequest()
        {
        }
    }

    /// <summary>
    /// Response data for audience share log
    /// </summary>
    public class AudienceShareLogResponse
    {
        /// <summary>
        /// List of share log entries
        /// </summary>
        [JsonPropertyName("list")]
        public List<AudienceShareLogEntry>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceShareLogResponse class
        /// </summary>
        public AudienceShareLogResponse()
        {
        }
    }

    /// <summary>
    /// Share log entry information
    /// </summary>
    public class AudienceShareLogEntry
    {
        /// <summary>
        /// Shared advertiser ID
        /// </summary>
        [JsonPropertyName("shared_advertiser_id")]
        public string? SharedAdvertiserId { get; set; }

        /// <summary>
        /// Share time
        /// </summary>
        [JsonPropertyName("share_time")]
        public string? ShareTime { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceShareLogEntry class
        /// </summary>
        public AudienceShareLogEntry()
        {
        }
    }

    /// <summary>
    /// Request parameters for creating a lookalike audience
    /// </summary>
    public class AudienceLookalikeCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience name
        /// </summary>
        [JsonPropertyName("custom_audience_name")]
        [Required]
        public string CustomAudienceName { get; set; } = string.Empty;

        /// <summary>
        /// Seed audience ID
        /// </summary>
        [JsonPropertyName("seed_id")]
        [Required]
        public string SeedId { get; set; } = string.Empty;

        /// <summary>
        /// Lookalike type
        /// </summary>
        [JsonPropertyName("lookalike_type")]
        [Required]
        public string LookalikeType { get; set; } = string.Empty;

        /// <summary>
        /// Location IDs for targeting
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string>? LocationIds { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceLookalikeCreateRequest class
        /// </summary>
        public AudienceLookalikeCreateRequest()
        {
        }
    }

    /// <summary>
    /// Response data for lookalike audience creation
    /// </summary>
    public class AudienceLookalikeCreateResponse
    {
        /// <summary>
        /// Custom audience ID
        /// </summary>
        [JsonPropertyName("custom_audience_id")]
        public string? CustomAudienceId { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceLookalikeCreateResponse class
        /// </summary>
        public AudienceLookalikeCreateResponse()
        {
        }
    }

    /// <summary>
    /// Request parameters for updating a lookalike audience
    /// </summary>
    public class AudienceLookalikeUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience ID
        /// </summary>
        [JsonPropertyName("custom_audience_id")]
        [Required]
        public string CustomAudienceId { get; set; } = string.Empty;

        /// <summary>
        /// New custom audience name
        /// </summary>
        [JsonPropertyName("custom_audience_name")]
        public string? CustomAudienceName { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceLookalikeUpdateRequest class
        /// </summary>
        public AudienceLookalikeUpdateRequest()
        {
        }
    }

    /// <summary>
    /// Response data for lookalike audience update
    /// </summary>
    public class AudienceLookalikeUpdateResponse
    {
        /// <summary>
        /// Custom audience ID
        /// </summary>
        [JsonPropertyName("custom_audience_id")]
        public string? CustomAudienceId { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceLookalikeUpdateResponse class
        /// </summary>
        public AudienceLookalikeUpdateResponse()
        {
        }
    }

    // ===== RULE-BASED AUDIENCE MODELS =====

    /// <summary>
    /// Request parameters for creating an audience by rules
    /// </summary>
    public class AudienceRuleCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience name
        /// </summary>
        [JsonPropertyName("custom_audience_name")]
        [Required]
        [StringLength(128, MinimumLength = 1)]
        public string CustomAudienceName { get; set; } = string.Empty;

        /// <summary>
        /// Audience type
        /// </summary>
        [JsonPropertyName("audience_type")]
        [Required]
        public string AudienceType { get; set; } = string.Empty;

        /// <summary>
        /// Audience sub type
        /// </summary>
        [JsonPropertyName("audience_sub_type")]
        public string? AudienceSubType { get; set; }

        /// <summary>
        /// Number of days to retain the audience
        /// </summary>
        [JsonPropertyName("retention_in_days")]
        public int? RetentionInDays { get; set; }

        /// <summary>
        /// Whether to turn on the audience auto-refresh function
        /// </summary>
        [JsonPropertyName("is_auto_refresh")]
        public bool? IsAutoRefresh { get; set; }

        /// <summary>
        /// Identity ID (required for certain audience types)
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Identity type (required for certain audience types)
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// Rule specification for the audience
        /// </summary>
        [JsonPropertyName("rule_spec")]
        [Required]
        public AudienceRuleSpec RuleSpec { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AudienceRuleCreateRequest class
        /// </summary>
        public AudienceRuleCreateRequest()
        {
        }
    }

    /// <summary>
    /// Rule specification for audience creation
    /// </summary>
    public class AudienceRuleSpec
    {
        /// <summary>
        /// Inclusion rule set
        /// </summary>
        [JsonPropertyName("inclusion_rule_set")]
        [Required]
        public AudienceRuleSet InclusionRuleSet { get; set; } = new();

        /// <summary>
        /// Exclusion rule set
        /// </summary>
        [JsonPropertyName("exclusion_rule_set")]
        public AudienceRuleSet? ExclusionRuleSet { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceRuleSpec class
        /// </summary>
        public AudienceRuleSpec()
        {
        }
    }

    /// <summary>
    /// Rule set for audience inclusion or exclusion
    /// </summary>
    public class AudienceRuleSet
    {
        /// <summary>
        /// Operator between rules
        /// </summary>
        [JsonPropertyName("operator")]
        [Required]
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// List of rules
        /// </summary>
        [JsonPropertyName("rules")]
        [Required]
        public List<AudienceRule> Rules { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AudienceRuleSet class
        /// </summary>
        public AudienceRuleSet()
        {
        }
    }

    /// <summary>
    /// Individual audience rule
    /// </summary>
    public class AudienceRule
    {
        /// <summary>
        /// Event source IDs
        /// </summary>
        [JsonPropertyName("event_source_ids")]
        public List<string>? EventSourceIds { get; set; }

        /// <summary>
        /// Retention days (lookback window)
        /// </summary>
        [JsonPropertyName("retention_days")]
        [Required]
        public int RetentionDays { get; set; }

        /// <summary>
        /// Filter set for the rule
        /// </summary>
        [JsonPropertyName("filter_set")]
        [Required]
        public AudienceFilterSet FilterSet { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AudienceRule class
        /// </summary>
        public AudienceRule()
        {
        }
    }

    /// <summary>
    /// Filter set for audience rules
    /// </summary>
    public class AudienceFilterSet
    {
        /// <summary>
        /// Operator between filters
        /// </summary>
        [JsonPropertyName("operator")]
        [Required]
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// List of filters
        /// </summary>
        [JsonPropertyName("filters")]
        [Required]
        public List<AudienceFilter> Filters { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AudienceFilterSet class
        /// </summary>
        public AudienceFilterSet()
        {
        }
    }

    /// <summary>
    /// Individual audience filter
    /// </summary>
    public class AudienceFilter
    {
        /// <summary>
        /// Filter field
        /// </summary>
        [JsonPropertyName("field")]
        [Required]
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// Filter operator
        /// </summary>
        [JsonPropertyName("operator")]
        [Required]
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// Filter value
        /// </summary>
        [JsonPropertyName("value")]
        [Required]
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// Parameter filters for URL keywords or parameters
        /// </summary>
        [JsonPropertyName("parameter_filters")]
        public List<AudienceParameterFilter>? ParameterFilters { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceFilter class
        /// </summary>
        public AudienceFilter()
        {
        }
    }

    /// <summary>
    /// Parameter filter for URL keywords or parameters
    /// </summary>
    public class AudienceParameterFilter
    {
        /// <summary>
        /// Filter field
        /// </summary>
        [JsonPropertyName("field")]
        [Required]
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// Filter operator
        /// </summary>
        [JsonPropertyName("operator")]
        [Required]
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// Filter values
        /// </summary>
        [JsonPropertyName("values")]
        [Required]
        public List<string> Values { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AudienceParameterFilter class
        /// </summary>
        public AudienceParameterFilter()
        {
        }
    }

    /// <summary>
    /// Response data for rule-based audience creation
    /// </summary>
    public class AudienceRuleCreateResponse
    {
        /// <summary>
        /// Custom audience ID
        /// </summary>
        [JsonPropertyName("custom_audience_id")]
        public string? CustomAudienceId { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceRuleCreateResponse class
        /// </summary>
        public AudienceRuleCreateResponse()
        {
        }
    }

    // ===== STREAMING/SEGMENT API MODELS =====

    /// <summary>
    /// Request parameters for creating/deleting audience segments
    /// </summary>
    public class SegmentAudienceRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Action to perform (create or delete)
        /// </summary>
        [JsonPropertyName("action")]
        [Required]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience name (required when action is create)
        /// </summary>
        [JsonPropertyName("custom_audience_name")]
        public string? CustomAudienceName { get; set; }

        /// <summary>
        /// Audience ID to delete (required when action is delete)
        /// </summary>
        [JsonPropertyName("delete_audience_id")]
        public string? DeleteAudienceId { get; set; }

        /// <summary>
        /// Initializes a new instance of the SegmentAudienceRequest class
        /// </summary>
        public SegmentAudienceRequest()
        {
        }
    }

    /// <summary>
    /// Response data for segment audience operations
    /// </summary>
    public class SegmentAudienceResponse
    {
        /// <summary>
        /// Audience ID
        /// </summary>
        [JsonPropertyName("audience_id")]
        public string? AudienceId { get; set; }

        /// <summary>
        /// Initializes a new instance of the SegmentAudienceResponse class
        /// </summary>
        public SegmentAudienceResponse()
        {
        }
    }

    /// <summary>
    /// Request parameters for segment mapping operations
    /// </summary>
    public class SegmentMappingRequest
    {
        /// <summary>
        /// List of advertiser IDs
        /// </summary>
        [JsonPropertyName("advertiser_ids")]
        [Required]
        public List<string> AdvertiserIds { get; set; } = new();

        /// <summary>
        /// Action to perform (add or delete)
        /// </summary>
        [JsonPropertyName("action")]
        public string? Action { get; set; }

        /// <summary>
        /// ID schema/types
        /// </summary>
        [JsonPropertyName("id_schema")]
        [Required]
        public List<string> IdSchema { get; set; } = new();

        /// <summary>
        /// Batch data for mapping operations
        /// </summary>
        [JsonPropertyName("batch_data")]
        [Required]
        public List<List<SegmentMappingData>> BatchData { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the SegmentMappingRequest class
        /// </summary>
        public SegmentMappingRequest()
        {
        }
    }

    /// <summary>
    /// Segment mapping data
    /// </summary>
    public class SegmentMappingData
    {
        /// <summary>
        /// ID of a user or device
        /// </summary>
        [JsonPropertyName("id")]
        public string? Id { get; set; }

        /// <summary>
        /// Array of audience IDs
        /// </summary>
        [JsonPropertyName("audience_ids")]
        public List<string>? AudienceIds { get; set; }

        /// <summary>
        /// Initializes a new instance of the SegmentMappingData class
        /// </summary>
        public SegmentMappingData()
        {
        }
    }

    /// <summary>
    /// Response data for segment mapping operations
    /// </summary>
    public class SegmentMappingResponse
    {
        /// <summary>
        /// Empty data object for successful operations
        /// </summary>
        [JsonPropertyName("data")]
        public object? Data { get; set; }

        /// <summary>
        /// Initializes a new instance of the SegmentMappingResponse class
        /// </summary>
        public SegmentMappingResponse()
        {
        }
    }

    // ===== SAVED AUDIENCE MODELS =====

    /// <summary>
    /// Request parameters for creating a saved audience
    /// </summary>
    public class SavedAudienceCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Saved audience name
        /// </summary>
        [JsonPropertyName("saved_audience_name")]
        [Required]
        public string SavedAudienceName { get; set; } = string.Empty;

        /// <summary>
        /// Targeting specification
        /// </summary>
        [JsonPropertyName("targeting")]
        [Required]
        public object Targeting { get; set; } = new object();

        /// <summary>
        /// Initializes a new instance of the SavedAudienceCreateRequest class
        /// </summary>
        public SavedAudienceCreateRequest()
        {
        }
    }

    /// <summary>
    /// Response data for saved audience creation
    /// </summary>
    public class SavedAudienceCreateResponse
    {
        /// <summary>
        /// Saved audience ID
        /// </summary>
        [JsonPropertyName("saved_audience_id")]
        public string? SavedAudienceId { get; set; }

        /// <summary>
        /// Initializes a new instance of the SavedAudienceCreateResponse class
        /// </summary>
        public SavedAudienceCreateResponse()
        {
        }
    }

    /// <summary>
    /// Request parameters for deleting saved audiences
    /// </summary>
    public class SavedAudienceDeleteRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Saved audience IDs to delete
        /// </summary>
        [JsonPropertyName("saved_audience_ids")]
        [Required]
        public List<string> SavedAudienceIds { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the SavedAudienceDeleteRequest class
        /// </summary>
        public SavedAudienceDeleteRequest()
        {
        }
    }

    /// <summary>
    /// Request parameters for listing saved audiences
    /// </summary>
    public class SavedAudienceListRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Page number for pagination
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size for pagination
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Initializes a new instance of the SavedAudienceListRequest class
        /// </summary>
        public SavedAudienceListRequest()
        {
        }
    }

    /// <summary>
    /// Saved audience information
    /// </summary>
    public class SavedAudienceInfo
    {
        /// <summary>
        /// Saved audience ID
        /// </summary>
        [JsonPropertyName("saved_audience_id")]
        public string? SavedAudienceId { get; set; }

        /// <summary>
        /// Saved audience name
        /// </summary>
        [JsonPropertyName("saved_audience_name")]
        public string? SavedAudienceName { get; set; }

        /// <summary>
        /// Creation time
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Targeting specification
        /// </summary>
        [JsonPropertyName("targeting")]
        public object? Targeting { get; set; }

        /// <summary>
        /// Initializes a new instance of the SavedAudienceInfo class
        /// </summary>
        public SavedAudienceInfo()
        {
        }
    }

    /// <summary>
    /// Response data for saved audience list
    /// </summary>
    public class SavedAudienceListResponse
    {
        /// <summary>
        /// List of saved audiences
        /// </summary>
        [JsonPropertyName("list")]
        public List<SavedAudienceInfo>? List { get; set; }

        /// <summary>
        /// Page information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }

        /// <summary>
        /// Initializes a new instance of the SavedAudienceListResponse class
        /// </summary>
        public SavedAudienceListResponse()
        {
        }
    }

    // ===== SHARE CANCEL MODELS =====

    /// <summary>
    /// Request parameters for canceling audience sharing
    /// </summary>
    public class AudienceShareCancelRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience IDs to cancel sharing
        /// </summary>
        [JsonPropertyName("custom_audience_ids")]
        [Required]
        public List<string> CustomAudienceIds { get; set; } = new();

        /// <summary>
        /// Advertiser IDs to cancel sharing with
        /// </summary>
        [JsonPropertyName("shared_advertiser_ids")]
        [Required]
        public List<string> SharedAdvertiserIds { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AudienceShareCancelRequest class
        /// </summary>
        public AudienceShareCancelRequest()
        {
        }
    }

    /// <summary>
    /// Response data for audience share cancel
    /// </summary>
    public class AudienceShareCancelResponse
    {
        /// <summary>
        /// Success status
        /// </summary>
        [JsonPropertyName("success")]
        public bool? Success { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceShareCancelResponse class
        /// </summary>
        public AudienceShareCancelResponse()
        {
        }
    }

    // ===== APPLY LOG MODELS =====

    /// <summary>
    /// Request parameters for getting audience apply log
    /// </summary>
    public class AudienceApplyLogRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Custom audience ID
        /// </summary>
        [JsonPropertyName("custom_audience_id")]
        [Required]
        public string CustomAudienceId { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the AudienceApplyLogRequest class
        /// </summary>
        public AudienceApplyLogRequest()
        {
        }
    }

    /// <summary>
    /// Response data for audience apply log
    /// </summary>
    public class AudienceApplyLogResponse
    {
        /// <summary>
        /// List of apply log entries
        /// </summary>
        [JsonPropertyName("list")]
        public List<AudienceApplyLogEntry>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceApplyLogResponse class
        /// </summary>
        public AudienceApplyLogResponse()
        {
        }
    }

    /// <summary>
    /// Apply log entry information
    /// </summary>
    public class AudienceApplyLogEntry
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdgroupId { get; set; }

        /// <summary>
        /// Apply time
        /// </summary>
        [JsonPropertyName("apply_time")]
        public string? ApplyTime { get; set; }

        /// <summary>
        /// Action mode
        /// </summary>
        [JsonPropertyName("action_mode")]
        public string? ActionMode { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceApplyLogEntry class
        /// </summary>
        public AudienceApplyLogEntry()
        {
        }
    }

    // ===== PANGLE AUDIENCE MODELS =====

    /// <summary>
    /// Request parameters for getting Pangle audience package
    /// </summary>
    public class PangleAudiencePackageRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the PangleAudiencePackageRequest class
        /// </summary>
        public PangleAudiencePackageRequest()
        {
        }
    }

    /// <summary>
    /// Response data for Pangle audience package
    /// </summary>
    public class PangleAudiencePackageResponse
    {
        /// <summary>
        /// List of Pangle audience packages
        /// </summary>
        [JsonPropertyName("list")]
        public List<PangleAudiencePackageInfo>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the PangleAudiencePackageResponse class
        /// </summary>
        public PangleAudiencePackageResponse()
        {
        }
    }

    /// <summary>
    /// Pangle audience package information
    /// </summary>
    public class PangleAudiencePackageInfo
    {
        /// <summary>
        /// Package ID
        /// </summary>
        [JsonPropertyName("package_id")]
        public string? PackageId { get; set; }

        /// <summary>
        /// Package name
        /// </summary>
        [JsonPropertyName("package_name")]
        public string? PackageName { get; set; }

        /// <summary>
        /// Package description
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Initializes a new instance of the PangleAudiencePackageInfo class
        /// </summary>
        public PangleAudiencePackageInfo()
        {
        }
    }

    // ===== ENHANCED AUDIENCE DETAIL MODELS =====

    /// <summary>
    /// Enhanced audience detail information with additional fields from v1.3 API
    /// </summary>
    public class EnhancedAudienceDetailInfo : AudienceDetailInfo
    {
        /// <summary>
        /// Owner ID of the audience creator
        /// </summary>
        [JsonPropertyName("owner_id")]
        public string? OwnerId { get; set; }

        /// <summary>
        /// Lookalike audience-specific information
        /// </summary>
        [JsonPropertyName("lookalike_spec")]
        public List<LookalikeSpec>? LookalikeSpec { get; set; }

        /// <summary>
        /// Rule definition for rule-based audiences
        /// </summary>
        [JsonPropertyName("rule")]
        public string? Rule { get; set; }

        /// <summary>
        /// Whether the audience refreshes automatically
        /// </summary>
        [JsonPropertyName("is_auto_refresh")]
        public bool? IsAutoRefresh { get; set; }

        /// <summary>
        /// Error message when creating or modifying an audience
        /// </summary>
        [JsonPropertyName("error_msg")]
        public string? ErrorMsg { get; set; }

        /// <summary>
        /// Information about audience status
        /// </summary>
        [JsonPropertyName("msg")]
        public string? Msg { get; set; }

        /// <summary>
        /// Initializes a new instance of the EnhancedAudienceDetailInfo class
        /// </summary>
        public EnhancedAudienceDetailInfo()
        {
        }
    }

    /// <summary>
    /// Lookalike audience specification
    /// </summary>
    public class LookalikeSpec
    {
        /// <summary>
        /// Source (seed) audience ID
        /// </summary>
        [JsonPropertyName("source_audience_id")]
        public string? SourceAudienceId { get; set; }

        /// <summary>
        /// Whether to include the source audience in the new lookalike audience
        /// </summary>
        [JsonPropertyName("include_source")]
        public bool? IncludeSource { get; set; }

        /// <summary>
        /// Device operating systems
        /// </summary>
        [JsonPropertyName("mobile_os")]
        public string? MobileOs { get; set; }

        /// <summary>
        /// The apps where you want to deliver your ads
        /// </summary>
        [JsonPropertyName("placements")]
        public string? Placements { get; set; }

        /// <summary>
        /// IDs of the locations that you want to target
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string>? LocationIds { get; set; }

        /// <summary>
        /// Size of lookalike audience
        /// </summary>
        [JsonPropertyName("audience_size")]
        public string? AudienceSize { get; set; }

        /// <summary>
        /// Initializes a new instance of the LookalikeSpec class
        /// </summary>
        public LookalikeSpec()
        {
        }
    }

    /// <summary>
    /// Enhanced audience get response with additional fields
    /// </summary>
    public class EnhancedAudienceGetResponse
    {
        /// <summary>
        /// List of enhanced audience details
        /// </summary>
        [JsonPropertyName("list")]
        public List<EnhancedAudienceDetailWrapper>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the EnhancedAudienceGetResponse class
        /// </summary>
        public EnhancedAudienceGetResponse()
        {
        }
    }

    /// <summary>
    /// Wrapper for enhanced audience details with history
    /// </summary>
    public class EnhancedAudienceDetailWrapper
    {
        /// <summary>
        /// Audience details
        /// </summary>
        [JsonPropertyName("audience_details")]
        public EnhancedAudienceDetailInfo? AudienceDetails { get; set; }

        /// <summary>
        /// Audience modification history
        /// </summary>
        [JsonPropertyName("audience_history")]
        public List<AudienceModifyRecord>? AudienceHistory { get; set; }

        /// <summary>
        /// Initializes a new instance of the EnhancedAudienceDetailWrapper class
        /// </summary>
        public EnhancedAudienceDetailWrapper()
        {
        }
    }
}
