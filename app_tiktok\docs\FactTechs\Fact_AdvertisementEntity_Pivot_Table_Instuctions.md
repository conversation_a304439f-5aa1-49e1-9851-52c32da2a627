# Implementation Guide: Syncfusion Pivot Table for Fact Advertisement

## Overview
This guide provides step-by-step instructions to implement a Syncfusion Pivot Table for TikTok advertisement performance analysis using the Fact_QuangCao (Advertisement Facts) data model.

## Prerequisites
- ASP.NET Core application with ABP Framework
- Syncfusion ES5 license (Essential Studio)
- TikTok Business API data synchronization in place
- Fact_QuangCao table implemented following Star schema design

## Step 1: Setup Syncfusion Dependencies

Synfusion is already installed in Project. Don not need to config

## Step 2: Data Structure Preparation

### 2.1 Define Fact Advertisement Data Model
Create JavaScript object structure matching your Fact_QuangCao table:

```javascript
const factAdvertisementData = [
    {
        DateKey: ********,
        Date: "2025-07-01",
        Type: "BC|AdAccount|Campaign|AdGroup|Ad",
        EntityId: "unique_entity_id",
        EntityName: "display_name",
        BusinessCenter: "bc_name",
        AdAccount: "account_name",
        Campaign: "campaign_name",
        AdGroup: "adgroup_name",
        Ad: "ad_name",
        Spend: 1000000,
        Impressions: 500000,
        Clicks: 25000,
        Conversion: 750,
        TotalOnsiteShoppingValue: 3000000,
        Currency: "VND",
        CTR: 5.0,
        CPC: 40,
        ConversionRate: 3.0,
        ROAS: 3.0
    }
    // ... more data rows
];
```

### 2.2 Data Enhancement
Add calculated fields and formatting:

```javascript
factAdvertisementData.forEach(item => {
    // Calculate KPIs
    item.CPM = item.Impressions > 0 ? (item.Spend / item.Impressions * 1000) : 0;
    item.CostPerConversion = item.Conversion > 0 ? (item.Spend / item.Conversion) : 0;
    
    // Add date components
    const dateObj = new Date(item.Date);
    item.Year = dateObj.getFullYear();
    item.Month = dateObj.getMonth() + 1;
    item.WeekDay = dateObj.toLocaleDateString('vi-VN', { weekday: 'long' });
    item.IsWeekend = dateObj.getDay() === 0 || dateObj.getDay() === 6;
});
```

## Step 3: Create Pivot Table Component

### 3.1 HTML Container
```html
<div id="FactAdvertisementPivotTable"></div>
```

### 3.2 Initialize Pivot Table
```javascript
const pivotTableObj = new ej.pivotview.PivotView({
    dataSourceSettings: {
        // Data source
        dataSource: factAdvertisementData,
        
        // Row hierarchy - TikTok advertising structure
        rows: [
            { name: 'Type', caption: 'Cấp độ' },
            { name: 'BusinessCenter', caption: 'Trung tâm kinh doanh' },
            { name: 'AdAccount', caption: 'Tài khoản quảng cáo' },
            { name: 'Campaign', caption: 'Chiến dịch' },
            { name: 'AdGroup', caption: 'Nhóm quảng cáo' },
            { name: 'Ad', caption: 'Quảng cáo' }
        ],
        
        // Column grouping - Time analysis
        columns: [
            { name: 'Date', caption: 'Ngày' },
            { name: 'WeekDay', caption: 'Thứ' }
        ],
        
        // Metrics - Key performance indicators
        values: [
            { name: 'Spend', caption: 'Chi phí (VND)', type: 'Sum' },
            { name: 'Impressions', caption: 'Lượt hiển thị', type: 'Sum' },
            { name: 'Clicks', caption: 'Lượt nhấp', type: 'Sum' },
            { name: 'Conversion', caption: 'Chuyển đổi', type: 'Sum' },
            { name: 'TotalOnsiteShoppingValue', caption: 'Doanh thu (VND)', type: 'Sum' },
            { name: 'CTR', caption: 'Tỷ lệ nhấp (%)', type: 'Avg' },
            { name: 'ROAS', caption: 'ROAS', type: 'Avg' }
        ],
        
        // Filters
        filters: [
            { name: 'Currency', caption: 'Tiền tệ' },
            { name: 'IsWeekend', caption: 'Cuối tuần' }
        ],
        
        // Formatting
        formatSettings: [
            { name: 'Spend', format: 'N0', currency: 'VND' },
            { name: 'TotalOnsiteShoppingValue', format: 'N0', currency: 'VND' },
            { name: 'Impressions', format: 'N0' },
            { name: 'Clicks', format: 'N0' },
            { name: 'CTR', format: 'N2', suffix: '%' },
            { name: 'ROAS', format: 'N2' }
        ],
        
        // Enable features
        allowLabelFilter: true,
        allowValueFilter: true,
        enableSorting: true,
        expandAll: false
    },
    
    // Pivot table settings
    height: 700,
    width: '100%',
    showGroupingBar: true,
    showFieldList: true,
    allowExcelExport: true,
    allowPdfExport: true,
    showToolbar: true,
    
    // Toolbar options
    toolbar: [
        'New', 'Save', 'Grid', 'Chart', 'Export', 
        'SubTotal', 'GrandTotal', 'FieldList'
    ]
});

// Render to container
pivotTableObj.appendTo('#FactAdvertisementPivotTable');
```

## Step 4: Add Performance Features

### 4.1 Conditional Formatting for ROAS
```javascript
conditionalFormatSettings: [
    {
        measure: 'ROAS',
        value1: 3,
        conditions: 'GreaterThan',
        style: {
            backgroundColor: '#d4edda',
            color: '#155724'
        }
    },
    {
        measure: 'ROAS',
        value1: 2,
        value2: 3,
        conditions: 'Between',
        style: {
            backgroundColor: '#fff3cd',
            color: '#856404'
        }
    },
    {
        measure: 'ROAS',
        value1: 2,
        conditions: 'LessThan',
        style: {
            backgroundColor: '#f8d7da',
            color: '#721c24'
        }
    }
]
```

### 4.2 Export Functionality
```javascript
// Excel Export
function exportToExcel() {
    if (pivotTableObj) {
        pivotTableObj.excelExport();
    }
}

// PDF Export
function exportToPdf() {
    if (pivotTableObj) {
        pivotTableObj.pdfExport();
    }
}

// Chart View
function showChart() {
    if (pivotTableObj) {
        pivotTableObj.displayOption.view = 'Chart';
    }
}
```

## Step 5: Add Data Integration

### 5.1 API Integration (ASP.NET Core)
```csharp
[HttpGet]
public async Task<IActionResult> GetAdvertisementData(DateTime? fromDate, DateTime? toDate, string type = null)
{
    var data = await _factAdvertisementService.GetDataAsync(fromDate, toDate, type);
    return Json(data);
}
```

### 5.2 Dynamic Data Loading
```javascript
async function loadAdvertisementData(filters = {}) {
    try {
        showLoading();
        const response = await fetch('/api/fact-advertisement/data', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(filters)
        });
        
        const data = await response.json();
        
        // Update pivot table data
        pivotTableObj.dataSourceSettings.dataSource = data;
        pivotTableObj.refresh();
        
        hideLoading();
    } catch (error) {
        console.error('Error loading data:', error);
        hideLoading();
    }
}
```

## Step 6: Implement UI Controls

### 6.1 Filter Controls
```html
<div class="controls-section">
    <button onclick="exportToExcel()" class="btn btn-success">
        <i class="fas fa-file-excel"></i> Xuất Excel
    </button>
    <button onclick="exportToPdf()" class="btn btn-danger">
        <i class="fas fa-file-pdf"></i> Xuất PDF
    </button>
    <button onclick="refreshData()" class="btn btn-primary">
        <i class="fas fa-sync-alt"></i> Làm mới
    </button>
</div>
```

### 6.2 Level Filtering
```javascript
// Filter by advertising level
function filterByLevel(level) {
    const filterItem = {
        name: 'Type',
        type: 'Include',
        items: [level]
    };
    
    pivotTableObj.dataSourceSettings.filterSettings = [filterItem];
    pivotTableObj.refresh();
}
```

## Step 7: Performance Optimization

### 7.1 Virtual Scrolling (for large datasets)
```javascript
virtualScrollSettings: {
    allowVirtualScrolling: true
}
```

### 7.2 Data Compression
```javascript
// Enable data compression for large datasets
dataSourceSettings: {
    enableSorting: true,
    allowMemberFilter: true,
    compress: true
}
```

## Step 8: Testing and Validation

### 8.1 Data Validation
- Verify all metrics calculate correctly
- Test drill-down functionality through advertising hierarchy
- Validate export formats (Excel/PDF)
- Check responsive behavior on different screen sizes

### 8.2 Performance Testing
- Test with large datasets (10K+ rows)
- Verify loading times under 3 seconds
- Test concurrent user access
- Validate memory usage

## Step 9: Deployment Considerations

### 9.1 Production Settings
```javascript
// Disable development features in production
const isProduction = window.location.hostname !== 'localhost';

const pivotConfig = {
    showFieldList: !isProduction,
    allowConditionalFormatting: true,
    locale: 'vi-VN'
};
```

### 9.2 Security
- Implement proper authentication for data access
- Validate user permissions for different advertising levels
- Sanitize all input parameters
- Use HTTPS for all API calls

## Conclusion

This implementation provides a comprehensive TikTok advertising performance dashboard using Syncfusion Pivot Table with proper data hierarchy, real-time filtering, export capabilities, and performance optimization for production use.