/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Split Test operations
    /// </summary>
    public class SplitTestApi : ISplitTestApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<SplitTestApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the SplitTestApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public SplitTestApi(IApiClient apiClient, ILogger<SplitTestApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        // /// <summary>
        // /// Create a split test group
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing split test configuration</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing split test group ID</returns>
        // public async Task<Response<SplitTestCreateResponse>> CreateSplitTestAsync(
        //     string accessToken,
        //     SplitTestCreateBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Creating split test for advertiser {AdvertiserId}", body.AdvertiserId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<SplitTestCreateResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SplitTestEndpoints.CreateSplitTest}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <summary>
        // /// Update the start time and end time of a split test
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing updated times</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response indicating success</returns>
        // public async Task<Response<object>> UpdateSplitTestAsync(
        //     string accessToken,
        //     SplitTestUpdateBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Updating split test {SplitTestGroupId} for advertiser {AdvertiserId}", 
        //         body.SplitTestGroupId, body.AdvertiserId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<object>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SplitTestEndpoints.UpdateSplitTest}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <summary>
        // /// Stop an ad group level or campaign level split test
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing split test group ID</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response indicating success</returns>
        // public async Task<Response<object>> EndSplitTestAsync(
        //     string accessToken,
        //     SplitTestEndBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Ending split test {SplitTestGroupId} for advertiser {AdvertiserId}", 
        //         body.SplitTestGroupId, body.AdvertiserId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<object>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SplitTestEndpoints.EndSplitTest}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get test results, including p-values for each metric
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="splitTestGroupId">Split test group ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing split test results</returns>
        public async Task<Response<SplitTestResultResponse>> GetSplitTestResultAsync(
            string accessToken,
            string advertiserId,
            string splitTestGroupId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrWhiteSpace(splitTestGroupId))
                throw new ArgumentException("Split test group ID cannot be null or empty", nameof(splitTestGroupId));

            _logger?.LogInformation("Getting split test result {SplitTestGroupId} for advertiser {AdvertiserId}",
                splitTestGroupId, advertiserId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["split_test_group_id"] = splitTestGroupId
            };

            return await _apiClient.CallApiAsync<Response<SplitTestResultResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SplitTestEndpoints.GetSplitTestResult}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <summary>
        // /// Run the winning ad group in your ad group level split test
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing winning object ID</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response indicating success</returns>
        // public async Task<Response<object>> PromoteSplitTestAsync(
        //     string accessToken,
        //     SplitTestPromoteBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Promoting split test {SplitTestGroupId} for advertiser {AdvertiserId} with winning object {WinningObjectId}",
        //         body.SplitTestGroupId, body.AdvertiserId, body.WinningObjectId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<object>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{SplitTestEndpoints.PromoteSplitTest}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }
    }
}
