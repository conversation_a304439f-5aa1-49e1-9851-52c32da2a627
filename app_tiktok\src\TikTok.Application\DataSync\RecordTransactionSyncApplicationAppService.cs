using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// Application App Service implementation cho việc đồng bộ dữ liệu bản ghi giao dịch BC và AdAccount
    /// </summary>
    public class RecordTransactionSyncApplicationAppService : ApplicationService, IRecordTransactionSyncApplicationAppService
    {
        private readonly IRecordTransactionSyncService _recordTransactionSyncService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="recordTransactionSyncService">Record Transaction Sync Service</param>
        public RecordTransactionSyncApplicationAppService(IRecordTransactionSyncService recordTransactionSyncService)
        {
            _recordTransactionSyncService = recordTransactionSyncService;
        }

        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch BC theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<RecordTransactionSyncResult> SyncBcTransactionsAsync(string bcId)
        {
            return await _recordTransactionSyncService.SyncBcTransactionsAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch AdAccount theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<RecordTransactionSyncResult> SyncAdAccountTransactionsAsync(string bcId)
        {
            return await _recordTransactionSyncService.SyncAdAccountTransactionsRecordAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<RecordTransactionSyncResult> SyncAllRecordTransactionsAsync()
        {
            return await _recordTransactionSyncService.SyncAllRecordTransactionsAsync();
        }
    }
}