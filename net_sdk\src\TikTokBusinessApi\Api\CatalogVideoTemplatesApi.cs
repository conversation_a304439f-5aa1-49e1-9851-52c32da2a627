/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Catalog Video Templates operations
    /// </summary>
    public class CatalogVideoTemplatesApi : ICatalogVideoTemplatesApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<CatalogVideoTemplatesApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the CatalogVideoTemplatesApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public CatalogVideoTemplatesApi(IApiClient apiClient, ILogger<CatalogVideoTemplatesApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get information about all catalog video packages, or a particular video package, under your Business Center
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting video packages</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing video package information</returns>
        public async Task<VideoPackageGetResponse> GetVideoPackagesAsync(
            string accessToken,
            VideoPackageGetRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogInformation("Getting video packages for catalog {CatalogId} in BC {BcId}", 
                request.CatalogId, request.BcId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["catalog_id"] = request.CatalogId
            };

            if (!string.IsNullOrWhiteSpace(request.ShoppingAdsVideoPackageId))
            {
                queryParams["shopping_ads_video_package_id"] = request.ShoppingAdsVideoPackageId;
            }

            var response = await _apiClient.CallApiAsync<Response<VideoPackageGetResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogVideoTemplatesEndpoints.GetVideoPackages}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);

            return response.Data ?? new VideoPackageGetResponse();
        }

        /// <summary>
        /// Create a video package (Deprecated)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing video package creation parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created video package ID</returns>
        //public async Task<VideoPackageCreateResponse> CreateVideoPackageAsync(
        //    string accessToken,
        //    VideoPackageCreateBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));

        //    _logger?.LogInformation("Creating video package for catalog {CatalogId} in BC {BcId}", 
        //        body.CatalogId, body.BcId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    var response = await _apiClient.CallApiAsync<Response<VideoPackageCreateResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogVideoTemplatesEndpoints.CreateVideoPackage}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response.Data ?? new VideoPackageCreateResponse();
        //}

        /// <summary>
        /// Update the name of a video package (Deprecated)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing video package update parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<object> UpdateVideoPackageAsync(
        //    string accessToken,
        //    VideoPackageUpdateBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));

        //    _logger?.LogInformation("Updating video package {VideoPackageId} for catalog {CatalogId}", 
        //        body.ShoppingAdsVideoPackageId, body.CatalogId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    var response = await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogVideoTemplatesEndpoints.UpdateVideoPackage}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response.Data ?? new object();
        //}

        /// <summary>
        /// Delete a video package (Deprecated)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing video package deletion parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<object> DeleteVideoPackageAsync(
        //    string accessToken,
        //    VideoPackageDeleteBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));

        //    _logger?.LogInformation("Deleting video package {VideoPackageId} for catalog {CatalogId}", 
        //        body.ShoppingAdsVideoPackageId, body.CatalogId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    var response = await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogVideoTemplatesEndpoints.DeleteVideoPackage}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);

        //    return response.Data ?? new object();
        //}
    }
}
