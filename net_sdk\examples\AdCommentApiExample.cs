/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the AdCommentApi
    /// </summary>
    public class AdCommentApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public AdCommentApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example: Get comments for an ad group
        /// </summary>
        public async Task GetCommentsExample()
        {
            try
            {
                Console.WriteLine("=== Getting Comments for Ad Group ===");

                var response = await _client.AdComment.GetCommentsAsync(
                    accessToken: "your-access-token",
                    advertiserId: "your-advertiser-id",
                    searchField: "ADGROUP_ID",
                    searchValue: "your-adgroup-id",
                    startTime: "2023-01-01",
                    endTime: "2023-01-31",
                    commentType: new List<string> { "ALL" },
                    sortField: "CREATE_TIME",
                    sortType: "DESC",
                    pageSize: 10,
                    page: 1
                );

                Console.WriteLine($"Found {response.Data?.Comments?.Count ?? 0} comments");

                if (response.Data?.Comments != null)
                {
                    foreach (var comment in response.Data.Comments)
                    {
                        Console.WriteLine($"Comment ID: {comment.CommentId}");
                        Console.WriteLine($"User: {comment.UserName}");
                        Console.WriteLine($"Content: {comment.Content}");
                        Console.WriteLine($"Likes: {comment.Likes}, Replies: {comment.Replies}");
                        Console.WriteLine($"Status: {comment.CommentStatus}");
                        Console.WriteLine($"Created: {comment.CreateTime}");
                        Console.WriteLine($"Ad: {comment.AdName} (Campaign: {comment.CampaignName})");
                        Console.WriteLine("---");
                    }
                }

                // Display pagination info
                if (response.Data?.PageInfo != null)
                {
                    var pageInfo = response.Data.PageInfo;
                    Console.WriteLine($"Page: {pageInfo.Page}/{pageInfo.TotalPage}");
                    Console.WriteLine($"Total Comments: {pageInfo.TotalNumber}");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Reply to a comment
        /// </summary>
        public async Task ReplyToCommentExample()
        {
            try
            {
                Console.WriteLine("=== Replying to a Comment ===");

                var replyRequest = new AdCommentReplyRequest
                {
                    AdvertiserId = "your-advertiser-id",
                    AdId = "your-ad-id",
                    TikTokItemId = "tiktok-video-id",
                    CommentId = "comment-to-reply-to",
                    CommentType = "REPLY",
                    Text = "Thank you for your feedback! We appreciate your engagement.",
                    IdentityType = "TT_USER",
                    IdentityId = "your-identity-id"
                };

                var response = await _client.AdComment.ReplyToCommentAsync(
                    accessToken: "your-access-token",
                    body: replyRequest
                );

                Console.WriteLine($"Reply created successfully!");
                Console.WriteLine($"Reply ID: {response.Data?.CommentId}");
                Console.WriteLine($"Reply Text: {response.Data?.Text}");
                Console.WriteLine($"Created Time: {response.Data?.CreateTime}");
                Console.WriteLine($"Replied to Comment ID: {response.Data?.ReplyToCommentId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Create and manage comment export task
        /// </summary>
        public async Task ExportCommentsExample()
        {
            try
            {
                Console.WriteLine("=== Creating Comment Export Task ===");

                // Create export task
                var exportRequest = new AdCommentExportTaskRequest
                {
                    AdvertiserId = "your-advertiser-id",
                    SearchField = "ADGROUP_ID",
                    SearchValue = "your-adgroup-id",
                    StartTime = "2023-01-01",
                    EndTime = "2023-01-31",
                    CommentType = new List<string> { "ALL" },
                    CommentStatus = new List<string> { "PUBLIC" },
                    SortField = "CREATE_TIME",
                    SortType = "DESC",
                    Language = "EN"
                };

                var createResponse = await _client.AdComment.CreateExportTaskAsync(
                    accessToken: "your-access-token",
                    body: exportRequest
                );

                string taskId = createResponse.Data?.TaskId;
                Console.WriteLine($"Export task created with ID: {taskId}");

                // Check task status
                Console.WriteLine("Checking task status...");
                var statusResponse = await _client.AdComment.CheckExportTaskStatusAsync(
                    accessToken: "your-access-token",
                    advertiserId: "your-advertiser-id",
                    taskId: taskId
                );

                Console.WriteLine($"Task Status: {statusResponse.Data?.Status}");

                // If task is completed, download the data
                if (statusResponse.Data?.Status == "SUCCEED")
                {
                    Console.WriteLine("Task completed! Downloading data...");
                    var downloadResponse = await _client.AdComment.DownloadExportTaskAsync(
                        accessToken: "your-access-token",
                        advertiserId: "your-advertiser-id",
                        taskId: taskId
                    );

                    Console.WriteLine("Download initiated successfully!");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Manage comments (hide, pin, etc.)
        /// </summary>
        public async Task ManageCommentsExample()
        {
            try
            {
                Console.WriteLine("=== Managing Comments ===");

                var commentIds = new List<string> { "comment-id-1", "comment-id-2" };
                var managementRequest = new AdCommentManagementRequest
                {
                    AdvertiserId = "your-advertiser-id",
                    CommentIds = commentIds
                };

                // Hide comments
                Console.WriteLine("Hiding comments...");
                await _client.AdComment.HideCommentsAsync(
                    accessToken: "your-access-token",
                    body: managementRequest
                );
                Console.WriteLine("Comments hidden successfully!");

                // Pin comments
                Console.WriteLine("Pinning comments...");
                await _client.AdComment.PinCommentsAsync(
                    accessToken: "your-access-token",
                    body: managementRequest
                );
                Console.WriteLine("Comments pinned successfully!");

                // Show comments (unhide)
                Console.WriteLine("Showing comments...");
                await _client.AdComment.ShowCommentsAsync(
                    accessToken: "your-access-token",
                    body: managementRequest
                );
                Console.WriteLine("Comments shown successfully!");

                // Unpin comments
                Console.WriteLine("Unpinning comments...");
                await _client.AdComment.UnpinCommentsAsync(
                    accessToken: "your-access-token",
                    body: managementRequest
                );
                Console.WriteLine("Comments unpinned successfully!");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Manage blocked words
        /// </summary>
        public async Task ManageBlockedWordsExample()
        {
            try
            {
                Console.WriteLine("=== Managing Blocked Words ===");

                // Create blocked words
                var createRequest = new AdCommentBlockedWordsCreateRequest
                {
                    AdvertiserId = "your-advertiser-id",
                    BlockedWords = new List<string> { "spam", "inappropriate", "offensive" }
                };

                await _client.AdComment.CreateBlockedWordsAsync(
                    accessToken: "your-access-token",
                    body: createRequest
                );
                Console.WriteLine("Blocked words created successfully!");

                // Get blocked words
                var blockedWordsResponse = await _client.AdComment.GetBlockedWordsAsync(
                    accessToken: "your-access-token",
                    advertiserId: "your-advertiser-id",
                    pageSize: 50,
                    page: 1
                );

                Console.WriteLine($"Found {blockedWordsResponse.Data?.BlockedWords?.Count ?? 0} blocked words:");
                if (blockedWordsResponse.Data?.BlockedWords != null)
                {
                    foreach (var blockedWord in blockedWordsResponse.Data.BlockedWords)
                    {
                        Console.WriteLine($"- {blockedWord.BlockedWordText} (ID: {blockedWord.BlockedWordId})");
                    }
                }

                // Update blocked words (if needed)
                if (blockedWordsResponse.Data?.BlockedWords?.Count > 0)
                {
                    var updateRequest = new AdCommentBlockedWordsUpdateRequest
                    {
                        AdvertiserId = "your-advertiser-id",
                        BlockedWords = new List<AdCommentBlockedWordUpdate>
                        {
                            new AdCommentBlockedWordUpdate
                            {
                                BlockedWordId = blockedWordsResponse.Data.BlockedWords[0].BlockedWordId,
                                BlockedWordText = "updated-word"
                            }
                        }
                    };

                    await _client.AdComment.UpdateBlockedWordsAsync(
                        accessToken: "your-access-token",
                        body: updateRequest
                    );
                    Console.WriteLine("Blocked word updated successfully!");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _client?.Dispose();
        }
    }

    /// <summary>
    /// Program entry point for running the examples
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Replace with your actual access token
            const string accessToken = "your-access-token";
            
            var example = new AdCommentApiExample(accessToken);
            
            try
            {
                Console.WriteLine("=== TikTok Business API - Ad Comment Examples ===\n");
                
                // Example 1: Get comments
                await example.GetCommentsExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 2: Reply to comment
                await example.ReplyToCommentExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 3: Export comments
                await example.ExportCommentsExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 4: Manage comments
                await example.ManageCommentsExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 5: Manage blocked words
                await example.ManageBlockedWordsExample();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
            finally
            {
                example.Dispose();
            }
        }
    }
}
