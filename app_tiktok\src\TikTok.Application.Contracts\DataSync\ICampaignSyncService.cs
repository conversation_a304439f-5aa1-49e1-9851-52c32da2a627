using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu chiến dịch (Campaigns)
    /// </summary>
    public interface ICampaignSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ chiến dịch theo Advertiser ID và BC ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<CampaignSyncResult> SyncCampaignsAsync(string advertiserId, string bcId);

        /// <summary>
        /// Đồng bộ chiến dịch theo Advertiser ID và BC ID với filtering
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="objectiveType">Loại mục tiêu</param>
        /// <param name="operationStatus">Trạng thái hoạt động</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<CampaignSyncResult> SyncCampaignsAsync(string advertiserId, string bcId, string? objectiveType = null, string? operationStatus = null);

        /// <summary>
        /// Đồng bộ nhiều chiến dịch cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds">Danh sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<CampaignSyncResult> SyncManyCampaignsAsync(string bcId, List<string>? advertiserIds = null);

        /// <summary>
        /// Đồng bộ tất cả chiến dịch cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<CampaignSyncResult> SyncAllCampaignsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ chiến dịch
    /// </summary>
    public class CampaignSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số bản ghi lỗi
        /// </summary>
        public int ErrorRecords { get; set; }

        /// <summary>
        /// Tổng số bản ghi đã xử lý
        /// </summary>
        public int TotalRecords => NewRecords + UpdatedRecords + ErrorRecords;

        /// <summary>
        /// Thời gian bắt đầu đồng bộ
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Thời gian kết thúc đồng bộ
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Thời gian thực hiện (tính bằng giây)
        /// </summary>
        public double DurationSeconds => (EndTime - StartTime).TotalSeconds;
    }
}