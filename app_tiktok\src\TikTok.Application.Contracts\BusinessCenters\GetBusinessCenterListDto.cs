using System;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.BusinessCenters
{
    /// <summary>
    /// DTO cho việc tìm kiếm và phân trang trung tâm kinh doanh
    /// </summary>
    public class GetBusinessCenterListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Từ khóa tìm kiếm (tìm theo tên, công ty, bc_id)
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// ID của trung tâm kinh doanh
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// Tên trung tâm kinh doanh
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// Tên công ty
        /// </summary>
        public string? Company { get; set; }

        /// <summary>
        /// Đơn vị tiền tệ
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Khu vực đăng ký
        /// </summary>
        public string? RegisteredArea { get; set; }

        /// <summary>
        /// Trạng thái
        /// </summary>
        public BusinessCenterStatus? Status { get; set; }

        /// <summary>
        /// Múi giờ
        /// </summary>
        public string? Timezone { get; set; }

        /// <summary>
        /// Loại trung tâm kinh doanh
        /// </summary>
        public BusinessCenterType? Type { get; set; }

        /// <summary>
        /// Vai trò người dùng
        /// </summary>
        public UserRole? UserRole { get; set; }

        /// <summary>
        /// Vai trò tài chính mở rộng
        /// </summary>
        public ExtUserFinanceRole? ExtUserFinanceRole { get; set; }

        /// <summary>
        /// Ngày tạo từ
        /// </summary>
        public DateTime? CreationTimeFrom { get; set; }

        /// <summary>
        /// Ngày tạo đến
        /// </summary>
        public DateTime? CreationTimeTo { get; set; }
    }
} 