/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for post authorization deletion
    /// </summary>
    public class PostAuthorizationDeleteResponse
    {
        /// <summary>
        /// The ID of the TikTok post
        /// </summary>
        [JsonPropertyName("item_id")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// The status of the authorization code
        /// Enum value: DELETED
        /// </summary>
        [JsonPropertyName("auth_code_status")]
        public string AuthCodeStatus { get; set; } = string.Empty;
    }
}
