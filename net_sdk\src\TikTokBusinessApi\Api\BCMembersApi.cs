/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API BC Members operations
    /// </summary>
    public class BCMembersApi : IBCMembersApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<BCMembersApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the BCMembersApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public BCMembersApi(IApiClient apiClient, ILogger<BCMembersApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get members in a Business Center. You need to be an Admin of the Business Center.
        /// </summary>
        /// <param name="request">Request parameters for getting members</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the list of members</returns>
        public async Task<GetMembersResponse> GetMembersAsync(
            GetMembersRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BcId))
                throw new ArgumentException("BcId is required", nameof(request));

            _logger?.LogInformation("Getting members for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();
            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering);

            return await _apiClient.CallApiAsync<GetMembersResponse>(
                $"open_api/v1.3{BCMembersEndpoints.GetMembers}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <summary>
        // /// Invite members to a Business Center and assign roles and assets to members. You need to be an Admin of the Business Center.
        // /// For each Business Center, the maximum number of members is 4000, and the maximum number of admins online is 20.
        // /// </summary>
        // /// <param name="request">Request parameters for inviting members</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response confirming the members were invited</returns>
        // public async Task<InviteMembersResponse> InviteMembersAsync(
        //     InviteMembersRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.BcId))
        //         throw new ArgumentException("BcId is required", nameof(request));

        //     if (request.Emails == null || request.Emails.Count == 0)
        //         throw new ArgumentException("Emails list is required and cannot be empty", nameof(request));

        //     _logger?.LogInformation("Inviting {EmailCount} members to BC {BcId}", request.Emails.Count, request.BcId);

        //     return await _apiClient.CallApiAsync<InviteMembersResponse>(
        //         $"open_api/v1.3{BCMembersEndpoints.InviteMembers}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <summary>
        // /// Update a member's user name and their role in the Business Center. You need to be an Admin of the Business Center.
        // /// </summary>
        // /// <param name="request">Request parameters for updating a member</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response confirming the member was updated</returns>
        // public async Task<UpdateMemberResponse> UpdateMemberAsync(
        //     UpdateMemberRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.BcId))
        //         throw new ArgumentException("BcId is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.UserId))
        //         throw new ArgumentException("UserId is required", nameof(request));

        //     _logger?.LogInformation("Updating member {UserId} in BC {BcId}", request.UserId, request.BcId);

        //     return await _apiClient.CallApiAsync<UpdateMemberResponse>(
        //         $"open_api/v1.3{BCMembersEndpoints.UpdateMember}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <summary>
        // /// Delete a member in a Business Center. You need to be an Admin of the Business Center.
        // /// </summary>
        // /// <param name="request">Request parameters for deleting a member</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response confirming the member was deleted</returns>
        // public async Task<DeleteMemberResponse> DeleteMemberAsync(
        //     DeleteMemberRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.BcId))
        //         throw new ArgumentException("BcId is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.UserId) && string.IsNullOrEmpty(request.UserEmail))
        //         throw new ArgumentException("Either UserId or UserEmail is required", nameof(request));

        //     _logger?.LogInformation("Deleting member from BC {BcId} (UserId: {UserId}, UserEmail: {UserEmail})", 
        //         request.BcId, request.UserId, request.UserEmail);

        //     return await _apiClient.CallApiAsync<DeleteMemberResponse>(
        //         $"open_api/v1.3{BCMembersEndpoints.DeleteMember}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }
    }
}
