/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response data for video list API
    /// </summary>
    public class VideoListResponse
    {
        /// <summary>
        /// The list of public video posts of the TikTok account
        /// </summary>
        [JsonPropertyName("videos")]
        public List<VideoData>? Videos { get; set; }

        /// <summary>
        /// Cursor for the next page of results
        /// </summary>
        [JsonPropertyName("cursor")]
        public int? Cursor { get; set; }

        /// <summary>
        /// Whether an additional page of data is available
        /// </summary>
        [JsonPropertyName("has_more")]
        public bool? HasMore { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"VideoListResponse(Videos={Videos?.Count}, HasMore={HasMore})";
        }
    }

    /// <summary>
    /// Video data
    /// </summary>
    public class VideoData
    {
        /// <summary>
        /// Unique identifier for the video
        /// </summary>
        [JsonPropertyName("item_id")]
        public string? ItemId { get; set; }

        /// <summary>
        /// Temporary URL for thumbnail of video content
        /// </summary>
        [JsonPropertyName("thumbnail_url")]
        public string? ThumbnailUrl { get; set; }

        /// <summary>
        /// A shareable URL for this TikTok video
        /// </summary>
        [JsonPropertyName("share_url")]
        public string? ShareUrl { get; set; }

        /// <summary>
        /// An embeddable link for this TikTok video
        /// </summary>
        [JsonPropertyName("embed_url")]
        public string? EmbedUrl { get; set; }

        /// <summary>
        /// Video description
        /// </summary>
        [JsonPropertyName("caption")]
        public string? Caption { get; set; }

        /// <summary>
        /// Video length - in seconds, up to three decimal places
        /// </summary>
        [JsonPropertyName("video_duration")]
        public float? VideoDuration { get; set; }

        /// <summary>
        /// Total number of likes on the video
        /// </summary>
        [JsonPropertyName("likes")]
        public int? Likes { get; set; }

        /// <summary>
        /// Total number of comments on the video
        /// </summary>
        [JsonPropertyName("comments")]
        public int? Comments { get; set; }

        /// <summary>
        /// Total number of times the video has been shared
        /// </summary>
        [JsonPropertyName("shares")]
        public int? Shares { get; set; }

        /// <summary>
        /// The total number of times the video has been added to favorites
        /// </summary>
        [JsonPropertyName("favorites")]
        public int? Favorites { get; set; }

        /// <summary>
        /// Unix/epoch date-time when the video was posted
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// The number of people who watched your published content at least once
        /// </summary>
        [JsonPropertyName("reach")]
        public int? Reach { get; set; }

        /// <summary>
        /// The number of times viewers watched your video
        /// </summary>
        [JsonPropertyName("video_views")]
        public int? VideoViews { get; set; }

        /// <summary>
        /// The amount of time viewers spent watching your video, in seconds
        /// </summary>
        [JsonPropertyName("total_time_watched")]
        public float? TotalTimeWatched { get; set; }

        /// <summary>
        /// The average time viewers spent watching your video, in seconds
        /// </summary>
        [JsonPropertyName("average_time_watched")]
        public float? AverageTimeWatched { get; set; }

        /// <summary>
        /// The percentage of viewers who finish watching your video
        /// </summary>
        [JsonPropertyName("full_video_watched_rate")]
        public float? FullVideoWatchedRate { get; set; }

        /// <summary>
        /// The number of viewers who started following you
        /// </summary>
        [JsonPropertyName("new_followers")]
        public int? NewFollowers { get; set; }

        /// <summary>
        /// The total number of profile views from users who visited through your video
        /// </summary>
        [JsonPropertyName("profile_views")]
        public int? ProfileViews { get; set; }

        /// <summary>
        /// Audience retention data
        /// </summary>
        [JsonPropertyName("video_view_retention")]
        public List<VideoViewRetention>? VideoViewRetention { get; set; }

        /// <summary>
        /// Traffic source breakdown
        /// </summary>
        [JsonPropertyName("impression_sources")]
        public List<ImpressionSource>? ImpressionSources { get; set; }

        /// <summary>
        /// Gender distribution of viewers
        /// </summary>
        [JsonPropertyName("audience_genders")]
        public List<AudienceGender>? AudienceGenders { get; set; }

        /// <summary>
        /// Top countries distribution of viewers
        /// </summary>
        [JsonPropertyName("audience_countries")]
        public List<AudienceCountry>? AudienceCountries { get; set; }

        /// <summary>
        /// Top cities distribution of viewers
        /// </summary>
        [JsonPropertyName("audience_cities")]
        public List<AudienceCity>? AudienceCities { get; set; }

        /// <summary>
        /// Viewer types breakdown
        /// </summary>
        [JsonPropertyName("audience_types")]
        public List<AudienceType>? AudienceTypes { get; set; }

        /// <summary>
        /// Engagement likes distribution
        /// </summary>
        [JsonPropertyName("engagement_likes")]
        public List<EngagementLike>? EngagementLikes { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"VideoData(ItemId={ItemId}, Caption={Caption}, Likes={Likes})";
        }
    }

    /// <summary>
    /// Video view retention data
    /// </summary>
    public class VideoViewRetention
    {
        /// <summary>
        /// A specific second in the video's timeline
        /// </summary>
        [JsonPropertyName("second")]
        public string? Second { get; set; }

        /// <summary>
        /// The percentage of viewers who are still watching your video at the specific second
        /// </summary>
        [JsonPropertyName("percentage")]
        public float? Percentage { get; set; }
    }

    /// <summary>
    /// Impression source data
    /// </summary>
    public class ImpressionSource
    {
        /// <summary>
        /// The traffic source type
        /// </summary>
        [JsonPropertyName("impression_source")]
        public string? Source { get; set; }

        /// <summary>
        /// The percentage of views from the source type
        /// </summary>
        [JsonPropertyName("percentage")]
        public float? Percentage { get; set; }
    }

    /// <summary>
    /// Audience type data
    /// </summary>
    public class AudienceType
    {
        /// <summary>
        /// Audience type
        /// </summary>
        [JsonPropertyName("type")]
        public string? Type { get; set; }

        /// <summary>
        /// Percentage of viewers associated with the audience type
        /// </summary>
        [JsonPropertyName("percentage")]
        public float? Percentage { get; set; }
    }

    /// <summary>
    /// Engagement like data
    /// </summary>
    public class EngagementLike
    {
        /// <summary>
        /// A specific second in the video's timeline
        /// </summary>
        [JsonPropertyName("second")]
        public string? Second { get; set; }

        /// <summary>
        /// Percentage of viewers who liked your video at the specific second
        /// </summary>
        [JsonPropertyName("percentage")]
        public float? Percentage { get; set; }
    }
}
