using System;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;

namespace TikTok.ReportIntegratedAdAccounts
{
    /// <summary>
    /// DTO cho việc tạo mới báo cáo tích hợp AdAccount
    /// </summary>
    public class CreateReportIntegratedAdAccountDto
    {
        /// <summary>
        /// ID tài khoản nhà quảng cáo - Được hỗ trợ ở cấp độ <PERSON>hà quảng cáo, Chiến dịch, Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// Tên tài khoản nhà quảng cáo - Được hỗ trợ ở cấp độ Nhà quảng cáo, Chiến dịch, Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [Required]
        [StringLength(255)]
        public string AdvertiserName { get; set; }

        /// <summary>
        /// Ngày tổng hợp báo cáo
        /// </summary>
        [Required]
        public DateTime Date { get; set; }

        /// <summary>
        /// Chi tiêu tổng - Tổng chi phí quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal Spend { get; set; }

        /// <summary>
        /// Chi phí Thanh toán bằng Tiền mặt - Số tiền ước tính bạn đã chi cho chiến dịch, nhóm quảng cáo hoặc quảng cáo trong lịch trình của nó được thanh toán bằng tiền mặt
        /// </summary>
        [Required]
        public decimal CashSpend { get; set; }

        /// <summary>
        /// Chi phí Thanh toán bằng Phiếu giảm giá - Số tiền ước tính bạn đã chi cho chiến dịch, nhóm quảng cáo hoặc quảng cáo trong lịch trình của nó được thanh toán bằng phiếu giảm giá
        /// </summary>
        [Required]
        public decimal VoucherSpend { get; set; }

        /// <summary>
        /// Chi phí thuần - Tổng chi phí quảng cáo của bạn, không bao gồm tín dụng quảng cáo hoặc phiếu giảm giá đã sử dụng
        /// </summary>
        [Required]
        public decimal BilledCost { get; set; }

        /// <summary>
        /// Lần hiển thị - Số lần quảng cáo của bạn được hiển thị
        /// </summary>
        [Required]
        public long Impressions { get; set; }

        /// <summary>
        /// Lần nhấp (đích đến) - Số lần nhấp từ quảng cáo của bạn đến một đích đến được chỉ định
        /// </summary>
        [Required]
        public long Clicks { get; set; }

        /// <summary>
        /// CTR (đích đến) - Tỷ lệ phần trăm lần hiển thị dẫn đến một lần nhấp đích đến trên tổng số lần hiển thị
        /// </summary>
        [Required]
        public decimal Ctr { get; set; }

        /// <summary>
        /// CPM - Số tiền trung bình bạn chi cho 1.000 lần hiển thị
        /// </summary>
        [Required]
        public decimal Cpm { get; set; }

        /// <summary>
        /// CPC (đích đến) - Chi phí trung bình cho mỗi lần nhấp đến một đích đến được chỉ định
        /// </summary>
        [Required]
        public decimal Cpc { get; set; }

        /// <summary>
        /// Tiếp cận - Số lượng người dùng duy nhất đã xem quảng cáo của bạn ít nhất một lần
        /// </summary>
        [Required]
        public long Reach { get; set; }

        /// <summary>
        /// Tần suất - Số lần trung bình mỗi người dùng xem quảng cáo của bạn trong một khoảng thời gian nhất định
        /// </summary>
        [Required]
        public decimal Frequency { get; set; }

        /// <summary>
        /// Tiền tệ - Mã tiền tệ, ví dụ: USD
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Currency { get; set; }

        /// <summary>
        /// Id Business Center
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }
    }
} 