/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok One (TTO) Business API operations
    /// </summary>
    public interface ITikTokOneApi
    {
        /// <summary>
        /// Get Creator access token
        /// </summary>
        /// <param name="request">Request body containing client credentials and authorization code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing access token and related information</returns>
        // Task<Response<CreatorAccessTokenResponse>> GetCreatorAccessTokenAsync(
        //     CreatorAccessTokenRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Renew Creator access token
        /// </summary>
        /// <param name="request">Request body containing client credentials and refresh token</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing renewed access token and related information</returns>
        //Task<Response<CreatorAccessTokenResponse>> RenewCreatorAccessTokenAsync(
        //    CreatorAccessTokenRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Revoke Creator access token
        /// </summary>
        /// <param name="request">Request body containing client credentials and access token to revoke</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> RevokeCreatorAccessTokenAsync(
        //    RevokeCreatorAccessTokenRequest request,
        //    CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Get authorized Creator permissions
        // /// </summary>
        // /// <param name="request">Request body containing app ID and access token</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing Creator token information</returns>
        // Task<Response<CreatorTokenInfoResponse>> GetCreatorTokenInfoAsync(
        //     CreatorTokenInfoRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get authorized TTO Creator Marketplace accounts
        /// </summary>
        /// <param name="accessToken">Access token authorized by a TikTok One Creator Marketplace account</param>
        /// <param name="appId">ID of your developer application</param>
        /// <param name="secret">Secret of your developer application</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TTO Creator Marketplace account IDs</returns>
        Task<Response<TtoCreatorMarketplaceAccountsResponse>> GetTtoCreatorMarketplaceAccountsAsync(
            string accessToken,
            string appId,
            string secret,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the details of a TTO Creator Marketplace account
        /// </summary>
        /// <param name="accessToken">Access token authorized by a TTO Creator Marketplace account</param>
        /// <param name="ttoTcmAccountId">The ID of the TTO Creator Marketplace account</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TTO account information</returns>
        Task<Response<TtoAccountInfoResponse>> GetTtoAccountInfoAsync(
            string accessToken,
            string ttoTcmAccountId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get information and insights about a TikTok creator who has joined TikTok One (TTO)
        /// </summary>
        /// <param name="accessToken">Access token authorized by TikTok creators</param>
        /// <param name="creatorId">Application specific unique ID of the TikTok account</param>
        /// <param name="fields">Requested fields</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing authorized TTO Creator insights</returns>
        Task<Response<AuthorizedTtoCreatorInsightsResponse>> GetAuthorizedTtoCreatorInsightsAsync(
            string accessToken,
            string creatorId,
            List<string>? fields = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the reporting insights of videos of a TikTok creator who has joined TikTok One (TTO)
        /// </summary>
        /// <param name="accessToken">Access token authorized by TikTok creators</param>
        /// <param name="creatorId">Application specific unique ID of the TikTok account</param>
        /// <param name="videoIds">The IDs of the videos to retrieve information for</param>
        /// <param name="limit">The number of videos that you want to retrieve per request</param>
        /// <param name="cursor">Timestamp cursor</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing authorized TTO Media insights</returns>
        Task<Response<AuthorizedTtoMediaInsightsResponse>> GetAuthorizedTtoMediaInsightsAsync(
            string accessToken,
            string creatorId,
            List<string>? videoIds = null,
            int? limit = null,
            long? cursor = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the basic profile information of a creator on TikTok One (TTO) Creator Marketplace
        /// </summary>
        /// <param name="accessToken">Access token authorized by a TTO Creator Marketplace account</param>
        /// <param name="ttoTcmAccountId">The ID of the TTO Creator Marketplace account</param>
        /// <param name="handleName">The handle name of the TTO Creator Marketplace creator</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TTO Public Account insights</returns>
        Task<Response<TtoPublicAccountInsightsResponse>> GetTtoPublicAccountInsightsAsync(
            string accessToken,
            string ttoTcmAccountId,
            string handleName,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve the creator industry or content tag labels associated with TikTok One (TTO) creators
        /// </summary>
        /// <param name="accessToken">Access token authorized by a TikTok One Creator Marketplace account</param>
        /// <param name="ttoTcmAccountId">The ID of the TikTok One Creator Marketplace account</param>
        /// <param name="labelType">The type of label (RANKING or SEARCH)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TTO creator labels</returns>
        Task<Response<TtoCreatorLabelsResponse>> GetTtoCreatorLabelsAsync(
            string accessToken,
            string ttoTcmAccountId,
            string labelType,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Obtain a list of up to 100 US creators who rank highest for a ranking label on TikTok One (TTO) Creator Leaderboard
        /// </summary>
        /// <param name="accessToken">Access token authorized by a TikTok One Creator Marketplace account</param>
        /// <param name="ttoTcmAccountId">The ID of the TikTok One Creator Marketplace account</param>
        /// <param name="rankingType">The type of ranking (BRANDED_CONTENT or ORGANIC_CONTENT)</param>
        /// <param name="timePeriod">The time period for the ranking (WEEK or MONTH)</param>
        /// <param name="timePeriodLookback">The specific lookback period for the ranking (ONE, TWO, or THREE)</param>
        /// <param name="labelId">Creator label ID</param>
        /// <param name="countryCode">The code of the country or region that the creator ranking is for</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TTO creator rankings</returns>
        Task<Response<TtoCreatorRankingsResponse>> GetTtoCreatorRankingsAsync(
            string accessToken,
            string ttoTcmAccountId,
            string rankingType,
            string timePeriod,
            string timePeriodLookback,
            string labelId,
            string countryCode,
            int? page = null,
            int? pageSize = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Search for TikTok One (TTO) creators
        /// </summary>
        /// <param name="accessToken">Access token authorized by a TikTok One Creator Marketplace account</param>
        /// <param name="ttoTcmAccountId">The ID of the TikTok One Creator Marketplace account</param>
        /// <param name="countryCodes">Filter by the country or region codes of the creators</param>
        /// <param name="stateProvinces">Filter by the states or provinces of the creators</param>
        /// <param name="contentLabelIds">Filter by the IDs of video content tags</param>
        /// <param name="industryLabelIds">Filter by the IDs of industry tags</param>
        /// <param name="minFollowers">Filter by the minimum number of followers</param>
        /// <param name="maxFollowers">Filter by the maximum number of followers</param>
        /// <param name="languages">Filter by language</param>
        /// <param name="minCreatorPrice">Filter by the minimum creator starting price</param>
        /// <param name="maxCreatorPrice">Filter by the maximum creator starting price</param>
        /// <param name="creatorPriceCurrency">The currency for creator starting price filters</param>
        /// <param name="minAvgViews">Filter by the minimum average views</param>
        /// <param name="maxAvgViews">Filter by the maximum average views</param>
        /// <param name="minMedianViews">Filter by the minimum median views</param>
        /// <param name="maxMedianViews">Filter by the maximum median views</param>
        /// <param name="minEngagementRate">Filter by the minimum engagement rate</param>
        /// <param name="maxEngagementRate">Filter by the maximum engagement rate</param>
        /// <param name="followerCountryCode">Filter by the code of followers' location</param>
        /// <param name="followerGenderRatio">Filter by followers' gender ratio</param>
        /// <param name="followerAge">Filter by followers' majority age group</param>
        /// <param name="keywordSearch">The keyword to search for creators</param>
        /// <param name="sortField">The specific metric by which to sort the results</param>
        /// <param name="sortOrder">The order in which to sort the results</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Current page size</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing discovered TTO creators</returns>
        //Task<Response<DiscoverTtoCreatorsResponse>> DiscoverTtoCreatorsAsync(
        //    string accessToken,
        //    string ttoTcmAccountId,
        //    List<string> countryCodes,
        //    List<string>? stateProvinces = null,
        //    List<string>? contentLabelIds = null,
        //    List<string>? industryLabelIds = null,
        //    int? minFollowers = null,
        //    int? maxFollowers = null,
        //    List<string>? languages = null,
        //    decimal? minCreatorPrice = null,
        //    decimal? maxCreatorPrice = null,
        //    string? creatorPriceCurrency = null,
        //    int? minAvgViews = null,
        //    int? maxAvgViews = null,
        //    int? minMedianViews = null,
        //    int? maxMedianViews = null,
        //    decimal? minEngagementRate = null,
        //    decimal? maxEngagementRate = null,
        //    string? followerCountryCode = null,
        //    string? followerGenderRatio = null,
        //    string? followerAge = null,
        //    string? keywordSearch = null,
        //    string? sortField = null,
        //    string? sortOrder = null,
        //    int? page = null,
        //    int? pageSize = null,
        //    CancellationToken cancellationToken = default);
    }
}
