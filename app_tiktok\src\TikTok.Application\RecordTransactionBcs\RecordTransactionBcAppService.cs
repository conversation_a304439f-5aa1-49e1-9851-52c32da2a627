using System;
using System.Linq;
using System.Threading.Tasks;
using TikTok.RecordTransactionBcs;
using TikTok.Localization;
using TikTok.Permissions;
using TikTok.RecordTransactionBcs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using TikTok.Entities;
using TikTok.Repositories;

namespace TikTok.RecordTransactionBcs
{
    /// <summary>
    /// App Service cho RecordTransactionBc
    /// </summary>
    public class RecordTransactionBcAppService :
        CrudAppService<
            RawRecordTransactionBcEntity,
            RecordTransactionBcDto,
            Guid,
            GetRecordTransactionBcListDto,
            CreateRecordTransactionBcDto,
            UpdateRecordTransactionBcDto>,
        IRecordTransactionBcAppService
    {
        private readonly IRecordTransactionBcRepository _bcTransactionRecordRepository;

        public RecordTransactionBcAppService(
            IRepository<RawRecordTransactionBcEntity, Guid> repository,
            IRecordTransactionBcRepository bcTransactionRecordRepository)
            : base(repository)
        {
            _bcTransactionRecordRepository = bcTransactionRecordRepository;

            // Định nghĩa quyền
            GetPolicyName = TikTokPermissions.RecordTransactionBcs.Default;
            GetListPolicyName = TikTokPermissions.RecordTransactionBcs.Default;
            CreatePolicyName = TikTokPermissions.RecordTransactionBcs.Create;
            UpdatePolicyName = TikTokPermissions.RecordTransactionBcs.Edit;
            DeletePolicyName = TikTokPermissions.RecordTransactionBcs.Delete;
        }

        /// <summary>
        /// Ghi đè phương thức GetListAsync để sử dụng custom repository
        /// </summary>
        /// <param name="input">Input để lấy danh sách</param>
        /// <returns>Danh sách bản ghi giao dịch</returns>
        public override async Task<PagedResultDto<RecordTransactionBcDto>> GetListAsync(GetRecordTransactionBcListDto input)
        {
            var entities = await _bcTransactionRecordRepository.GetListAsync(
                input.Sorting,
                input.MaxResultCount,
                input.SkipCount,
                input.Filter
            );

            var totalCount = await _bcTransactionRecordRepository.GetCountAsync(input.Filter);

            var dtos = ObjectMapper.Map<RawRecordTransactionBcEntity[], RecordTransactionBcDto[]>(entities.ToArray());

            return new PagedResultDto<RecordTransactionBcDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }
    }
}