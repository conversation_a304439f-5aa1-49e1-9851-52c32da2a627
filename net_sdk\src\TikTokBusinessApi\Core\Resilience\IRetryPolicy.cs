/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace TikTokBusinessApi.Core.Resilience
{
    /// <summary>
    /// Interface for retry policy implementations
    /// </summary>
    public interface IRetryPolicy
    {
        /// <summary>
        /// Maximum number of retry attempts
        /// </summary>
        int MaxRetryAttempts { get; }

        /// <summary>
        /// Determines if an exception should trigger a retry
        /// </summary>
        /// <param name="exception">The exception that occurred</param>
        /// <param name="attemptNumber">Current attempt number (0-based)</param>
        /// <returns>True if the operation should be retried</returns>
        bool ShouldRetry(Exception exception, int attemptNumber);

        /// <summary>
        /// Calculates the delay before the next retry attempt
        /// </summary>
        /// <param name="attemptNumber">Current attempt number (0-based)</param>
        /// <param name="exception">The exception that occurred</param>
        /// <returns>Delay before next retry</returns>
        TimeSpan GetRetryDelay(int attemptNumber, Exception? exception = null);

        /// <summary>
        /// Executes an operation with retry logic
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="operation">Operation to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        Task<T> ExecuteAsync<T>(Func<CancellationToken, Task<T>> operation, CancellationToken cancellationToken = default);

        /// <summary>
        /// Executes an HTTP request with retry logic
        /// </summary>
        /// <param name="httpClient">HTTP client to use</param>
        /// <param name="requestFactory">Factory to create HTTP request messages</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>HTTP response</returns>
        Task<HttpResponseMessage> ExecuteHttpAsync(
            HttpClient httpClient,
            Func<HttpRequestMessage> requestFactory,
            CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Retry policy configuration options
    /// </summary>
    public class RetryPolicyOptions
    {
        /// <summary>
        /// Maximum number of retry attempts (default: 3)
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Base delay for exponential backoff (default: 1 second)
        /// </summary>
        public TimeSpan BaseDelay { get; set; } = TimeSpan.FromSeconds(1);

        /// <summary>
        /// Maximum delay between retries (default: 30 seconds)
        /// </summary>
        public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// Jitter factor for randomizing delays (default: 0.1)
        /// </summary>
        public double JitterFactor { get; set; } = 0.1;

        /// <summary>
        /// Whether to use exponential backoff (default: true)
        /// </summary>
        public bool UseExponentialBackoff { get; set; } = true;

        /// <summary>
        /// Custom retry conditions
        /// </summary>
        public Func<Exception, int, bool>? CustomRetryCondition { get; set; }
    }
}
