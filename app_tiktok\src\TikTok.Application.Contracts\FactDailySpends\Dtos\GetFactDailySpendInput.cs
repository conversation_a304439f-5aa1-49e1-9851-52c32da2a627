﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TikTok.FactDailySpends.Dtos
{
    public class GetFactDailySpendInput
    {
        /// <summary>
        /// Khóa ngoại liên kết với Dim_Date (YYYYMMDD)
        /// </summary>
        public int? DimDateId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_AdAccount
        /// </summary>
        public Guid? DimAdAccountId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_BusinessCenter
        /// </summary>
        public Guid? DimBusinessCenterId { get; set; }

        /// <summary>
        /// ID nhà quảng cáo (Business Key)
        /// </summary>
        public string AdvertiserId { get; set; }

        /// <summary>
        /// Tên nhà quảng cáo
        /// </summary>
        public string AdvertiserName { get; set; }

        /// <summary>
        /// ID Business Center (Business Key)
        /// </summary>
        public string BcId { get; set; }


        /// <summary>
        /// Tổng số tiền chi phí cho nhà quảng cáo
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Số tiền chi phí tiền mặt
        /// </summary>
        public decimal CashAmount { get; set; }

        /// <summary>
        /// Số tiền chi phí tín dụng quảng cáo
        /// </summary>
        public decimal GrantAmount { get; set; }

        /// <summary>
        /// Số tiền thuế ước tính
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// Tiền tệ theo mã ISO 4217
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Ngày chi tiêu (UTC)
        /// </summary>
        public DateTime From { get; set; }
        public DateTime To { get; set; }
    }
}
