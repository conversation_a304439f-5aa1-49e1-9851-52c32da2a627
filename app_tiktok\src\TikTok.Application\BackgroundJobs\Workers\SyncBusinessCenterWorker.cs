using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Worker để đồng bộ Business Center - đồng bộ tất cả (Asset, Campaign, Balance, Transaction)
    /// </summary>
    public class SyncBusinessCenterWorker : WorkerJobBase
    {
        private readonly IAssetSyncService _assetSyncService;
        private readonly ICampaignSyncService _campaignSyncService;
        private readonly IBalanceSyncService _balanceSyncService;
        private readonly ITransactionSyncService _transactionSyncService;
        private readonly IBcManagementSyncService _cManagementSyncService;
        private readonly IAdAccountSyncService _adAccountSyncService;
        private readonly ICostProfileSyncService _costProfileSyncService;
        private readonly IRecordTransactionSyncService _recordTransactionSyncService;

        private readonly IReportIntegratedBcSyncService _reportIntegratedBcSyncService;
        private readonly IReportIntegratedAdAccountSyncService _reportIntegratedAdAccountSyncService;
        private readonly IReportIntegratedCampaignSyncService _reportIntegratedCampaignSyncService;
        private readonly IReportIntegratedAdGroupSyncService _reportIntegratedAdGroupSyncService;

        private readonly IReportIntegratedAdSyncService _reportIntegratedAdSyncService;
        private readonly IGmvMaxSyncService _gmvMaxSyncService;

        public SyncBusinessCenterWorker(
            ILogger<SyncBusinessCenterWorker> logger,
            IServiceProvider serviceProvider,
            IAssetSyncService assetSyncService,
            ICampaignSyncService campaignSyncService,
            IBalanceSyncService balanceSyncService,
            ITransactionSyncService transactionSyncService,
            IBcManagementSyncService cManagementSyncService,
            IAdAccountSyncService adAccountSyncService,
            ICostProfileSyncService costProfileSyncService,
            IRecordTransactionSyncService recordTransactionSyncService,
            IReportIntegratedBcSyncService reportIntegratedBcSyncService,
            IReportIntegratedAdAccountSyncService reportIntegratedAdAccountSyncService,
            IReportIntegratedCampaignSyncService reportIntegratedCampaignSyncService,
            IReportIntegratedAdGroupSyncService reportIntegratedAdGroupSyncService,
            IReportIntegratedAdSyncService reportIntegratedAdSyncService,
            IGmvMaxSyncService gmvMaxSyncService)
            : base(logger, serviceProvider)
        {
            _assetSyncService = assetSyncService;
            _campaignSyncService = campaignSyncService;
            _balanceSyncService = balanceSyncService;
            _transactionSyncService = transactionSyncService;
            _cManagementSyncService = cManagementSyncService;
            _adAccountSyncService = adAccountSyncService;
            _costProfileSyncService = costProfileSyncService;
            _recordTransactionSyncService = recordTransactionSyncService;
            _reportIntegratedBcSyncService = reportIntegratedBcSyncService;
            _reportIntegratedAdAccountSyncService = reportIntegratedAdAccountSyncService;
            _reportIntegratedCampaignSyncService = reportIntegratedCampaignSyncService;
            _reportIntegratedAdGroupSyncService = reportIntegratedAdGroupSyncService;
            _reportIntegratedAdSyncService = reportIntegratedAdSyncService;
            _gmvMaxSyncService = gmvMaxSyncService;
        }
        

        public override CommandType CommandType => CommandType.SyncBusinessCenter;

        protected override async Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job)
        {
            var startTime = DateTime.Now;
            _logger.LogDebug("Bắt đầu đồng bộ Business Center {BcId} vào lúc {DateTime}", args.Parameters, startTime);

            // Parse parameters
            var parameters = ParseParameters(args.Parameters);
            //_logger.LogDebug("Bắt đầu đồng bộ Business Center cho Business Center {BcId}", parameters.BcId);
            //// Test performance await 2 minutes
            //await Task.Delay(2 * 60 * 1000);
            //_logger.LogDebug("Đồng bộ Business Center thành công cho Business Center {BcId}", parameters.BcId);
            //return JobResult.Success(JsonConvert.SerializeObject(new { Success = true, Data = "Test performance" }));

            var syncResults = new
            {
                parameters.BcId,
                parameters.SyncDate,
                BCSync = await SyncBCAsync(parameters),
                //AssetSync = await SyncAssetAsync(parameters),
                //AdAccountSync = await SyncAdAccountAsync(parameters),
                //CampaignSync = await SyncCampaignAsync(parameters),
                //BalanceSync = await SyncBalanceAsync(parameters),
                //TransactionSync = await SyncTransactionAsync(parameters),
                //CostSync = await SyncCostAsync(parameters),
                //RecordTransactionSync = await SyncRecordTransactionAsync(parameters),
                //ReportIntegratedBcSync = await SyncReportIntegratedBcAsync(parameters),
                //ReportIntegratedAdAccountSync = await SyncReportIntegratedAdAccountAsync(parameters),
                //ReportIntegratedCampaignSync = await SyncReportIntegratedCampaignAsync(parameters),
                //ReportIntegratedAdGroupSync = await SyncReportIntegratedAdGroupAsync(parameters),
                //ReportIntegratedAdSync = await SyncReportIntegratedAdAsync(parameters),
                //GmvMaxSync = await SyncGmvMaxAsync(parameters),
            };

            _logger.LogDebug("Đồng bộ Business Center {BcId} thành công vào lúc {DateTime} - tổng thời gian {TotalTime}", args.Parameters, DateTime.Now, DateTime.Now - startTime);

            return JobResult.Success(JsonConvert.SerializeObject(syncResults));
        }

        /// <summary>
        /// Đồng bộ tất cả dữ liệu GMV Max
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncGmvMaxAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ GMV Max cho Business Center {BcId}", parameters.BcId);
                var responseSync = await _gmvMaxSyncService.SyncGmvMaxAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ GMV Max thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ GMV Max thất bại cho Business Center {BcId}: {ErrorMessage}", parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ GMV Max thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ GMV Max" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ GMV Max cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }



        /// <summary>
        /// Đồng bộ báo cáo tích hợp Ad
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncReportIntegratedAdAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp Ad cho Business Center {BcId}", parameters.BcId);
                var responseSync = await _reportIntegratedAdSyncService.SyncReportIntegratedAdAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ báo cáo tích hợp Ad thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ báo cáo tích hợp Ad thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ báo cáo tích hợp Ad thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ báo cáo tích hợp Ad" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ báo cáo tích hợp Ad cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp BC
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncReportIntegratedBcAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp BC cho Business Center {BcId}", parameters.BcId);
                var responseSync = await _reportIntegratedBcSyncService.SyncReportIntegratedBcAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ báo cáo tích hợp BC thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ báo cáo tích hợp BC thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ báo cáo tích hợp BC thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ báo cáo tích hợp BC" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ báo cáo tích hợp BC cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdAccount
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncReportIntegratedAdAccountAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp AdAccount cho Business Center {BcId}", parameters.BcId);
                var responseSync = await _reportIntegratedAdAccountSyncService.SyncReportIntegratedAdAccountAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ báo cáo tích hợp AdAccount thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ báo cáo tích hợp AdAccount thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ báo cáo tích hợp AdAccount thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ báo cáo tích hợp AdAccount" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ báo cáo tích hợp AdAccount cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp Campaign
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncReportIntegratedCampaignAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp Campaign cho Business Center {BcId}", parameters.BcId);
                var responseSync = await _reportIntegratedCampaignSyncService.SyncReportIntegratedCampaignAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ báo cáo tích hợp Campaign thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ báo cáo tích hợp Campaign thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ báo cáo tích hợp Campaign thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ báo cáo tích hợp Campaign" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ báo cáo tích hợp Campaign cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdGroup
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncReportIntegratedAdGroupAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp AdGroup cho Business Center {BcId}", parameters.BcId);
                var responseSync = await _reportIntegratedAdGroupSyncService.SyncReportIntegratedAdGroupAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ báo cáo tích hợp AdGroup thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ báo cáo tích hợp AdGroup thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ báo cáo tích hợp AdGroup thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ báo cáo tích hợp AdGroup" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ báo cáo tích hợp AdGroup cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }

        /// <summary>
        /// Đồng bộ bản ghi giao dịch
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncRecordTransactionAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ bản ghi giao dịch cho Business Center {BcId}", parameters.BcId);
                var responseSync = await _recordTransactionSyncService.SyncAllRecordTransactionsForBcAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ bản ghi giao dịch thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ bản ghi giao dịch thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ bản ghi giao dịch thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ bản ghi giao dịch" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ bản ghi giao dịch cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }

        /// <summary>
        /// Đồng bộ CostProfile
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncCostAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ CostProfile cho Business Center {BcId}", parameters.BcId);

                var responseSync = await _costProfileSyncService.SyncCostProfilesAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ CostProfile thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ CostProfile thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ CostProfile thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ CostProfile" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ CostProfile cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }

        /// <summary>
        /// Đồng bộ Business Center
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncBCAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ Business Center cho Business Center {BcId}", parameters.BcId);

                var responseSync = await _cManagementSyncService.SyncBusinessCenterAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ Business Center thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ Business Center thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ Business Center thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ Business Center" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ Business Center cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }

        /// <summary>
        /// Đồng bộ AdAccount
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncAdAccountAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ AdAccount cho Business Center {BcId}", parameters.BcId);

                var responseSync = await _adAccountSyncService.SyncAdAccountsAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ AdAccount thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ AdAccount thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ AdAccount thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ AdAccount" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ AdAccount cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }

        /// <summary>
        /// Đồng bộ Asset
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncAssetAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ Asset cho Business Center {BcId}", parameters.BcId);

                var responseSync = await _assetSyncService.SyncAssetsAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ Asset thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ Asset thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ Asset thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ Asset" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ Asset cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }

        /// <summary>
        /// Đồng bộ Campaign
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncCampaignAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ Campaign cho Business Center {BcId}", parameters.BcId);

                var responseSync = await _campaignSyncService.SyncManyCampaignsAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (string.IsNullOrEmpty(responseSync.ErrorMessage))
                    {
                        _logger.LogDebug("Đồng bộ Campaign thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ Campaign thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ Campaign thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ Campaign" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ Campaign cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }

        /// <summary>
        /// Đồng bộ Balance
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncBalanceAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ Balance cho Business Center {BcId}", parameters.BcId);

                var responseSync = await _balanceSyncService.SyncAllBalancesAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ Balance thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ Balance thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ Balance thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ Balance" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ Balance cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }

        /// <summary>
        /// Đồng bộ Transaction
        /// </summary>
        /// <param name="parameters">Parameters</param>
        /// <returns>Sync result</returns>
        private async Task<object> SyncTransactionAsync(DefaultParameters parameters)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ Transaction cho Business Center {BcId}", parameters.BcId);

                var responseSync = await _transactionSyncService.SyncTransactionsAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ Transaction thành công cho Business Center {BcId}", parameters.BcId);
                        return new { Success = true, Data = responseSync };
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ Transaction thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return new { Success = false, Error = responseSync.ErrorMessage };
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ Transaction thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return new { Success = false, Error = "Lỗi đồng bộ Transaction" };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ Transaction cho Business Center {BcId}", parameters.BcId);
                return new { Success = false, Error = ex.Message };
            }
        }
    }
}