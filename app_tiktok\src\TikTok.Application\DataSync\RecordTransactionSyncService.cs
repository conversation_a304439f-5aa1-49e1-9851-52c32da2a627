using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.BusinessCenters;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu bản ghi giao dịch BC và AdAccount
    /// </summary>
    public class RecordTransactionSyncService : BaseSyncService, IRecordTransactionSyncService
    {
        private readonly IRecordTransactionBcRepository _recordTransactionBcRepository;
        private readonly IRecordTransactionAdAccountRepository _recordTransactionAdAccountRepository;
        private readonly IBusinessCenterCache _businessCenterCache;

        /// <summary>
        /// Constructor
        /// </summary>
        public RecordTransactionSyncService(
            IServiceProvider serviceProvider,
            ILogger<RecordTransactionSyncService> logger,
            IRecordTransactionBcRepository recordTransactionBcRepository,
            IRecordTransactionAdAccountRepository recordTransactionAdAccountRepository,
            IBusinessCenterCache businessCenterCache) : base(serviceProvider, logger)
        {
            _recordTransactionBcRepository = recordTransactionBcRepository;
            _recordTransactionAdAccountRepository = recordTransactionAdAccountRepository;
            _businessCenterCache = businessCenterCache;
        }

        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch BC theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<RecordTransactionSyncResult> SyncBcTransactionsAsync(string bcId)
        {
            var result = new RecordTransactionSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ bản ghi giao dịch BC cho BC ID: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                string timezone = DateTimeService.UTC_TIMEZONE;

                var rawBc = await _businessCenterCache.GetByBcIdAsync(bcId);
                if (rawBc != null && !string.IsNullOrEmpty(rawBc.Timezone))
                {
                    timezone = rawBc.Timezone;
                }

                // Lấy bản ghi giao dịch BC mới nhất theo BcId để xác định ngày bắt đầu
                var latestBcRecord = await _recordTransactionBcRepository.GetLatestByDateAsync(bcId);
                var filterDate = GetRangeDateForSync(latestBcRecord?.Date);
                var startDateLocal = _dateTimeService.ConvertFromUtc(filterDate.startDate, timezone);
                var endDateLocal = _dateTimeService.ConvertFromUtc(filterDate.endDate, timezone);
                _logger.LogDebug("Đồng bộ bản ghi giao dịch BC từ {StartDate} đến {EndDate}", startDateLocal, endDateLocal);

                // Lấy dữ liệu từ TikTok API
                var bcTransactionRecords = await GetBcTransactionRecordsFromApiAsync(tikTokClient, bcId, startDateLocal, endDateLocal);

                if (bcTransactionRecords == null || !bcTransactionRecords.Any())
                {
                    _logger.LogDebug("Không có dữ liệu bản ghi giao dịch BC mới để đồng bộ");
                    return result;
                }

                await SyncBcTransactionRecordAsync(bcTransactionRecords, result, bcId, timezone);

                _logger.LogDebug("Hoàn thành đồng bộ bản ghi giao dịch BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ bản ghi giao dịch BC cho BC ID: {BcId}", bcId);
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch AdAccount theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<RecordTransactionSyncResult> SyncAdAccountTransactionsRecordAsync(string bcId)
        {
            var result = new RecordTransactionSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ bản ghi giao dịch AdAccount cho BC ID: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // Lấy bản ghi giao dịch AdAccount mới nhất để xác định ngày bắt đầu
                var latestAdAccountRecord = await _recordTransactionAdAccountRepository.GetLatestByDateAsync();
                var rangeDate = GetRangeDateForSync(latestAdAccountRecord?.Date);
                var startDate = rangeDate.startDate;
                var endDate = rangeDate.endDate;

                _logger.LogDebug("Đồng bộ bản ghi giao dịch AdAccount UTC từ {StartDate} đến {EndDate}", startDate, endDate);

                // Lấy dữ liệu từ TikTok API
                var adAccountTransactionRecords = await GetAdAccountTransactionRecordsFromApiAsync(tikTokClient, bcId, startDate, endDate);

                if (adAccountTransactionRecords == null || !adAccountTransactionRecords.Any())
                {
                    _logger.LogDebug("Không có dữ liệu bản ghi giao dịch AdAccount mới để đồng bộ");
                    return result;
                }

                // Đồng bộ dữ liệu vào database
                await SyncAdAccountTransactionRecordAsync(adAccountTransactionRecords, result);

                _logger.LogDebug("Hoàn thành đồng bộ bản ghi giao dịch AdAccount. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (BusinessException bex)
            {
                _logger.LogError(bex, "Lỗi khi đồng bộ bản ghi giao dịch AdAccount cho BC ID: {BcId}", bcId);
                result.Code = bex.Code ?? string.Empty;
                result.ErrorMessage = bex.Message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ bản ghi giao dịch AdAccount cho BC ID: {BcId}", bcId);
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<RecordTransactionSyncResult> SyncAllRecordTransactionsAsync()
        {
            var result = new RecordTransactionSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ bản ghi giao dịch cho tất cả Business Centers");

                // Lấy danh sách tất cả Business Applications
                var allBusinessApplications = await _businessApplicationCache.GetAllActiveAsync();
                var bcGroups = allBusinessApplications.GroupBy(x => x.BcId);

                foreach (var bcGroup in bcGroups)
                {
                    var bcId = bcGroup.Key;
                    try
                    {
                        // Đồng bộ bản ghi giao dịch BC
                        var bcResult = await SyncBcTransactionsAsync(bcId);
                        result.TotalSynced += bcResult.TotalSynced;
                        result.NewRecords += bcResult.NewRecords;
                        result.UpdatedRecords += bcResult.UpdatedRecords;

                        // Đồng bộ bản ghi giao dịch AdAccount
                        var adAccountResult = await SyncAdAccountTransactionsRecordAsync(bcId);
                        result.TotalSynced += adAccountResult.TotalSynced;
                        result.NewRecords += adAccountResult.NewRecords;
                        result.UpdatedRecords += adAccountResult.UpdatedRecords;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ bản ghi giao dịch cho BC ID: {BcId}", bcId);
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ bản ghi giao dịch cho tất cả Business Centers. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ bản ghi giao dịch cho tất cả Business Centers");
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// Lấy dữ liệu bản ghi giao dịch BC từ TikTok API
        /// </summary>
        private async Task<List<BcTransactionRecord>> GetBcTransactionRecordsFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, DateTime startDate, DateTime endDate)
        {
            var records = new List<BcTransactionRecord>();
            var page = 1;
            const int pageSize = 50;
            var filtering = new TransactionFiltering()
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            };

            while (true)
            {
                var request = new GetBcTransactionRequest
                {
                    BcId = bcId,
                    Page = page,
                    PageSize = pageSize,
                    Filtering = filtering
                };

                var response = await tikTokClient.BcPayments.GetBcTransactionAsync(request);

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break;
                }

                records.AddRange(response.Data.List);

                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            return records;
        }

        /// <summary>
        /// Lấy dữ liệu bản ghi giao dịch AdAccount từ TikTok API
        /// </summary>
        private async Task<List<AdvertiserTransactionRecord>> GetAdAccountTransactionRecordsFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, DateTime startDate, DateTime endDate)
        {
            var records = new List<AdvertiserTransactionRecord>();
            var page = 1;
            const int pageSize = 50;

            var filtering = new AdvertiserTransactionFiltering
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            };

            while (true)
            {
                var request = new GetAdvertiserTransactionRequest
                {
                    BcId = bcId,
                    Page = page,
                    PageSize = pageSize,
                    Filtering = filtering
                };

                var response = await tikTokClient.BcPayments.GetAdvertiserTransactionAsync(request);

                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), response.Message);
                }

                if (response?.Data?.TransactionList == null || !response.Data.TransactionList.Any())
                {
                    break;
                }

                records.AddRange(response.Data.TransactionList);

                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            return records;
        }

        /// <summary>
        /// Đồng bộ một bản ghi giao dịch BC
        /// </summary>
        private async Task SyncBcTransactionRecordAsync(List<BcTransactionRecord> apiRecords, RecordTransactionSyncResult result, string bcId, string timezone)
        {
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var apiNewRecords = MapManyApiBcTransactionRecordToEntity(apiRecords, bcId, timezone);
                DateTime minDateTime = apiNewRecords.Min(x => x.Date);
                DateTime maxDateTime = apiNewRecords.Max(x => x.Date);

                // Lấy dữ liệu cũ theo BcId và khoảng ngày
                var dataOlds = await _recordTransactionBcRepository.GetByDateRangeAsync(minDateTime, maxDateTime, bcId);

                var insertRecords = new List<RawRecordTransactionBcEntity>();

                foreach (var apiNewRecord in apiNewRecords)
                {
                    try
                    {
                        // Kiểm tra xem bản ghi đã tồn tại chưa theo BcId và Date
                        var existingRecordForDate = dataOlds.FirstOrDefault(x => x.BcId == apiNewRecord.BcId && x.Date == apiNewRecord.Date);

                        if (existingRecordForDate == null)
                        {
                            insertRecords.Add(apiNewRecord);
                            result.NewRecords++;
                        }

                        result.TotalSynced++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ bản ghi giao dịch BC với BcId: {BcId}, ngày: {Date}", apiNewRecord.BcId, apiNewRecord.Date);
                    }
                }

                if (insertRecords.Count > 0)
                {
                    await _recordTransactionBcRepository.InsertManyAsync(insertRecords);
                }

                await uow.CompleteAsync();
            }
        }

        private RawRecordTransactionBcEntity MapApiBcTransactionRecordToEntity(BcTransactionRecord apiRecord, string bcId, string timezone)
        {
            var transactionDateLocal = ParseDateTime(apiRecord.Date) ?? _dateTimeService.GetDateTimeNow();
            // Chuyển đổi timestamp thành DateTime UTC
            var transactionDate = _dateTimeService.ConvertToUtc(transactionDateLocal, timezone);
            return new RawRecordTransactionBcEntity(Guid.NewGuid())
            {
                BcId = bcId,
                Date = transactionDate,
                Amount = apiRecord.Amount ?? 0,
                Timezone = apiRecord.Timezone ?? "UTC",
                Currency = apiRecord.Currency ?? "USD",
                FundsType = Enum.TryParse<FundsType>(apiRecord.FundsType, out var fundsType) ? fundsType : FundsType.FUNDS_TYPE_CASH,
                InvoiceId = apiRecord.InvoiceId,
                InvoiceSerialNumber = apiRecord.InvoiceSerialNumber
            };
        }

        private List<RawRecordTransactionBcEntity> MapManyApiBcTransactionRecordToEntity(List<BcTransactionRecord> apiRecords, string bcId, string timezone)
        {
            var output = new List<RawRecordTransactionBcEntity>();
            foreach (var apiRecord in apiRecords)
            {
                output.Add(MapApiBcTransactionRecordToEntity(apiRecord, bcId, timezone));
            }

            return output;
        }

        /// <summary>
        /// Đồng bộ một bản ghi giao dịch AdAccount
        /// </summary>
        private async Task SyncAdAccountTransactionRecordAsync(List<AdvertiserTransactionRecord> apiRecords, RecordTransactionSyncResult result)
        {
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var apiNewRecords = MapManyApiRecordTransactionAdAccountToEntity(apiRecords);
                DateTime minDateTime = apiNewRecords.Min(x => x.Date);
                DateTime maxDateTime = apiNewRecords.Max(x => x.Date);

                var dataOlds = await _recordTransactionAdAccountRepository.GetByDateRangeAsync(minDateTime, maxDateTime);

                var insertRecords = new List<RawRecordTransactionAdAccountEntity>();

                foreach (var apiNewRecord in apiNewRecords)
                {
                    try
                    {
                        // Kiểm tra xem bản ghi đã tồn tại chưa
                        var existingRecordForDate = dataOlds.FirstOrDefault(x => x.AdvertiserId == apiNewRecord.AdvertiserId && x.Date == apiNewRecord.Date);

                        if (existingRecordForDate == null)
                        {
                            insertRecords.Add(apiNewRecord);
                            result.NewRecords++;
                        }

                        result.TotalSynced++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ bản ghi giao dịch AdAccount với ngày: {Date}", apiNewRecord.Date);
                    }
                }

                if (insertRecords.Count > 0)
                {
                    await _recordTransactionAdAccountRepository.InsertManyAsync(insertRecords);
                }

                await uow.CompleteAsync();
            }
        }

        private RawRecordTransactionAdAccountEntity MapApiRecordTransactionAdAccountToEntity(AdvertiserTransactionRecord apiRaw)
        {
            return new RawRecordTransactionAdAccountEntity(Guid.NewGuid())
            {
                AdvertiserId = apiRaw.AdvertiserId ?? string.Empty,
                AdvertiserName = apiRaw.AdvertiserName ?? string.Empty,
                Amount = apiRaw.Amount ?? 0,
                Currency = apiRaw.Currency ?? "USD",
                Date = ParseDateTime(apiRaw.Date) ?? _dateTimeService.GetDateUtcNow(),
                FundsType = Enum.TryParse<FundsType>(apiRaw.FundsType, out var fundType) ? fundType : FundsType.FUNDS_TYPE_CASH,
                TransferType = Enum.TryParse<TransferType>(apiRaw.TransferType, out var transferType) ? transferType : TransferType.TRANS_TYPE_TRANSFER,
                Timezone = apiRaw.Timezone ?? DateTimeService.UTC_TIMEZONE,
            };
        }

        private List<RawRecordTransactionAdAccountEntity> MapManyApiRecordTransactionAdAccountToEntity(List<AdvertiserTransactionRecord> apiRaws)
        {
            var output = new List<RawRecordTransactionAdAccountEntity>();

            foreach (var apiRaw in apiRaws)
            {
                output.Add(MapApiRecordTransactionAdAccountToEntity((AdvertiserTransactionRecord)apiRaw));
            }

            return output;
        }

        /// <summary>
        /// Xác định ngày bắt đầu để đồng bộ
        /// </summary>
        private (DateTime startDate, DateTime endDate) GetRangeDateForSync(DateTime? latestDate)
        {
            DateTime start = DateTime.MinValue;
            DateTime end = DateTime.MinValue;

            if (!latestDate.HasValue)
            {
                // Nếu chưa có bản ghi nào, lấy từ 30 ngày trước
                start = _dateTimeService.GetDateTimeUtcNow().AddDays(-LAST_SYNC_DAYS);
            }
            else
            {
                start = latestDate.Value;
            }

            end = _dateTimeService.GetDateTimeUtcNow();

            return (start, end);
        }

        /// <summary>
        /// Parse DateTime từ The time when the ad account was created, in the format of an Epoch/Unix timestamp in seconds.
        ///  Example: **********.
        /// </summary>
        /// <param name="value">Giá trị string</param>
        /// <returns>DateTime hoặc null</returns>
        private DateTime? ParseDateTime(long? value)
        {
            if (value == null)
                return null;

            return DateTimeOffset.FromUnixTimeSeconds(value.Value).DateTime;
        }

        /// <summary>
        /// Parse DateTime từ string
        /// </summary>
        private DateTime? ParseDateTime(string? value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            if (DateTime.TryParse(value, out var result))
                return result;

            return null;
        }

        public async Task<RecordTransactionSyncResult> SyncAllRecordTransactionsForBcAsync(string bcId)
        {
            var result = new RecordTransactionSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ bản ghi giao dịch cho tất cả Business Centers");
                // Đồng bộ bản ghi giao dịch BC
                var bcResult = await SyncBcTransactionsAsync(bcId);
                result.TotalSynced += bcResult.TotalSynced;
                result.NewRecords += bcResult.NewRecords;
                result.UpdatedRecords += bcResult.UpdatedRecords;

                // Đồng bộ bản ghi giao dịch AdAccount
                var adAccountResult = await SyncAdAccountTransactionsRecordAsync(bcId);
                result.TotalSynced += adAccountResult.TotalSynced;
                result.NewRecords += adAccountResult.NewRecords;
                result.UpdatedRecords += adAccountResult.UpdatedRecords;

                _logger.LogDebug("Hoàn thành đồng bộ bản ghi giao dịch cho tất cả Business Centers. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ bản ghi giao dịch cho tất cả Business Centers");
                result.ErrorMessage = ex.Message;
            }

            return result;
        }
    }
}