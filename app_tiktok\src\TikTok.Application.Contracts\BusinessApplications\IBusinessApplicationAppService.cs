using System;
using System.Threading.Tasks;
using TikTok.BusinessApplications;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using TikTok.Enums;
using TikTok.DataSync;

namespace TikTok.BusinessApplications
{
    /// <summary>
    /// Service interface cho ứng dụng Business
    /// </summary>
    public interface IBusinessApplicationAppService :
        ICrudAppService<
            BusinessApplicationDto,
            Guid,
            GetBusinessApplicationListDto,
            CreateBusinessApplicationDto,
            UpdateBusinessApplicationDto>
    {
        /// <summary>
        /// Lấy ứng dụng Business theo Application ID
        /// </summary>
        /// <param name="applicationId">ID của ứng dụng</param>
        /// <returns>Ứng dụng Business</returns>
        Task<BusinessApplicationDto> GetByApplicationIdAsync(string applicationId);

        /// <summary>
        /// Lấy danh sách ứng dụng Business theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Danh sách <PERSON>ng dụng Business</returns>
        Task<BusinessApplicationDto> GetByBcIdAsync(string bcId);

        /// <summary>
        /// Lấy redirect URI được cấu hình cho TikTok authorization
        /// </summary>
        /// <returns>Redirect URI</returns>
        Task<RedirectUriResponseDto> GetRedirectUriAsync();

        /// <summary>
        /// Xử lý callback từ TikTok sau khi user authorize
        /// </summary>
        /// <param name="request">Thông tin callback request</param>
        /// <returns>Kết quả xử lý callback</returns>
        Task<TikTokCallbackResponseDto> ProcessTikTokCallbackAsync(TikTokCallbackRequestDto request);

        /// <summary>
        /// Tạo job cho Business Application với CommandType cụ thể
        /// </summary>
        /// <param name="businessApplicationId">ID của Business Application</param>
        /// <param name="commandType">Loại lệnh</param>
        /// <returns>Kết quả tạo job</returns>
        Task<bool> CreateJobAsync(Guid businessApplicationId, CommandType commandType);

        /// <summary>
        /// Đồng bộ dữ liệu từ Raw sang Fact cho Business Center cụ thể
        /// </summary>
        /// <param name="businessApplicationId">ID của Business Application</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncRawToFactAsync(Guid businessApplicationId);

        /// <summary>
        /// Tạo job đồng bộ lùi cho Business Center với khoảng thời gian và các loại lệnh cụ thể
        /// </summary>
        /// <param name="request">Thông tin cài đặt đồng bộ lùi</param>
        /// <returns>Kết quả tạo job đồng bộ lùi</returns>
        Task<SyncBackResponseDto> CreateSyncBackJobsAsync(SyncBackRequestDto request);
    }
}