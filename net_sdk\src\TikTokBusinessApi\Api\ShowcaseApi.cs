/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Showcase operations
    /// </summary>
    public class ShowcaseApi : IShowcaseApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<ShowcaseApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the ShowcaseApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public ShowcaseApi(IApiClient apiClient, ILogger<ShowcaseApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get identities with Showcase permission under an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing identities with Showcase permission</returns>
        public async Task<Response<ShowcaseIdentitiesResponse>> GetIdentitiesAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogInformation("Getting Showcase identities for advertiser {AdvertiserId}", advertiserId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<ShowcaseIdentitiesResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ShowcaseEndpoints.GetIdentities}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the available regions for a Showcase via identity
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="identityId">The ID of the identity that is bound to a Showcase and the identity needs to have permission of the Showcase</param>
        /// <param name="identityType">Identity type. Enum values: TT_USER (TikTok User), BC_AUTH_TT (TikTok Account User in Business Center)</param>
        /// <param name="identityAuthorizedBcId">Required when identity_type is BC_AUTH_TT. ID of the Business Center that a TikTok Account User in Business Center identity is associated with</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing available regions for the Showcase</returns>
        public async Task<Response<ShowcaseRegionsResponse>> GetRegionsAsync(
            string accessToken,
            string advertiserId,
            string identityId,
            string identityType,
            string? identityAuthorizedBcId = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            if (string.IsNullOrEmpty(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            if (string.IsNullOrEmpty(identityId))
                throw new ArgumentException("Identity ID cannot be null or empty", nameof(identityId));

            if (string.IsNullOrEmpty(identityType))
                throw new ArgumentException("Identity type cannot be null or empty", nameof(identityType));

            _logger?.LogInformation("Getting Showcase regions for advertiser {AdvertiserId}, identity {IdentityId}", 
                advertiserId, identityId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["identity_id"] = identityId,
                ["identity_type"] = identityType
            };

            if (!string.IsNullOrEmpty(identityAuthorizedBcId))
            {
                queryParams["identity_authorized_bc_id"] = identityAuthorizedBcId;
            }

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<ShowcaseRegionsResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ShowcaseEndpoints.GetRegions}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the available products in a Showcase
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request containing parameters for getting Showcase products</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing available products in the Showcase</returns>
        public async Task<Response<ShowcaseProductsResponse>> GetProductsAsync(
            string accessToken,
            ShowcaseProductsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request.AdvertiserId));

            if (string.IsNullOrEmpty(request.IdentityId))
                throw new ArgumentException("Identity ID cannot be null or empty", nameof(request.IdentityId));

            if (string.IsNullOrEmpty(request.IdentityType))
                throw new ArgumentException("Identity type cannot be null or empty", nameof(request.IdentityType));

            if (request.RegionCodes == null || !request.RegionCodes.Any())
                throw new ArgumentException("Region codes cannot be null or empty", nameof(request.RegionCodes));

            _logger?.LogInformation("Getting Showcase products for advertiser {AdvertiserId}, identity {IdentityId}", 
                request.AdvertiserId, request.IdentityId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["identity_id"] = request.IdentityId,
                ["identity_type"] = request.IdentityType,
                ["region_codes"] = JsonSerializer.Serialize(request.RegionCodes)
            };

            if (!string.IsNullOrEmpty(request.IdentityAuthorizedBcId))
            {
                queryParams["identity_authorized_bc_id"] = request.IdentityAuthorizedBcId;
            }

            if (request.Filtering != null)
            {
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering);
            }

            if (request.Page.HasValue)
            {
                queryParams["page"] = request.Page.Value.ToString();
            }

            if (request.PageSize.HasValue)
            {
                queryParams["page_size"] = request.PageSize.Value.ToString();
            }

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<ShowcaseProductsResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ShowcaseEndpoints.GetProducts}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
