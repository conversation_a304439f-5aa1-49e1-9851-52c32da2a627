/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for creating a creative portfolio
    /// </summary>
    public class CreativePortfolioCreateBody
    {
        /// <summary>
        /// The advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Type of the portfolio
        /// </summary>
        [JsonPropertyName("creative_portfolio_type")]
        public string? CreativePortfolioType { get; set; }

        /// <summary>
        /// The content of the portfolio
        /// </summary>
        [JsonPropertyName("portfolio_content")]
        public List<PortfolioContent> PortfolioContent { get; set; } = new List<PortfolioContent>();
    }

    /// <summary>
    /// Portfolio content for different types of creative portfolios
    /// </summary>
    public class PortfolioContent
    {
        /// <summary>
        /// A call-to-action text (for CTA type)
        /// </summary>
        [JsonPropertyName("asset_content")]
        public string? AssetContent { get; set; }

        /// <summary>
        /// A list of CTAs (for CTA type)
        /// </summary>
        [JsonPropertyName("asset_ids")]
        public List<string>? AssetIds { get; set; }

        /// <summary>
        /// Card type (for CARD type)
        /// </summary>
        [JsonPropertyName("card_type")]
        public string? CardType { get; set; }

        /// <summary>
        /// Gesture type (for GESTURE type)
        /// </summary>
        [JsonPropertyName("gesture_type")]
        public string? GestureType { get; set; }

        /// <summary>
        /// Image ID
        /// </summary>
        [JsonPropertyName("image_id")]
        public string? ImageId { get; set; }

        /// <summary>
        /// Pop-up window image ID
        /// </summary>
        [JsonPropertyName("pop_up_window_image_id")]
        public string? PopUpWindowImageId { get; set; }

        /// <summary>
        /// Title of the card (for WEB_INFO_CARD type)
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// Selling points (for WEB_INFO_CARD type)
        /// </summary>
        [JsonPropertyName("selling_points")]
        public List<string>? SellingPoints { get; set; }

        /// <summary>
        /// Interactive guide text
        /// </summary>
        [JsonPropertyName("call_to_action_text")]
        public string? CallToActionText { get; set; }

        /// <summary>
        /// Badge show time in seconds
        /// </summary>
        [JsonPropertyName("badge_show_time")]
        public int? BadgeShowTime { get; set; }

        /// <summary>
        /// Badge position information
        /// </summary>
        [JsonPropertyName("badge_position")]
        public BadgePosition? BadgePosition { get; set; }

        /// <summary>
        /// Badge image information
        /// </summary>
        [JsonPropertyName("badge_image_info")]
        public BadgeImageInfo? BadgeImageInfo { get; set; }

        /// <summary>
        /// Slide length (for STRAIGHT_SLIDE gesture)
        /// </summary>
        [JsonPropertyName("slide_length")]
        public double? SlideLength { get; set; }

        /// <summary>
        /// Slide dimensions (for CURVED_SLIDE gesture)
        /// </summary>
        [JsonPropertyName("slide_dimension")]
        public SlideDimension? SlideDimension { get; set; }

        /// <summary>
        /// Interactive music ID
        /// </summary>
        [JsonPropertyName("interactive_music_id")]
        public string? InteractiveMusicId { get; set; }

        /// <summary>
        /// Layout types (for DOWNLOAD_CARD type)
        /// </summary>
        [JsonPropertyName("layouts")]
        public List<string>? Layouts { get; set; }

        /// <summary>
        /// Description
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Tags (for DOWNLOAD_CARD type)
        /// </summary>
        [JsonPropertyName("tags")]
        public List<string>? Tags { get; set; }

        /// <summary>
        /// Category label
        /// </summary>
        [JsonPropertyName("category_label")]
        public string? CategoryLabel { get; set; }

        /// <summary>
        /// App ID
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Profile image URL
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string? ProfileImage { get; set; }

        /// <summary>
        /// Call-to-action text
        /// </summary>
        [JsonPropertyName("call_to_action")]
        public string? CallToAction { get; set; }

        /// <summary>
        /// Mobile app ID
        /// </summary>
        [JsonPropertyName("mobile_app_id")]
        public string? MobileAppId { get; set; }

        /// <summary>
        /// Country codes
        /// </summary>
        [JsonPropertyName("country_code")]
        public List<string>? CountryCode { get; set; }

        /// <summary>
        /// Sticker parameters (for STICKER type)
        /// </summary>
        [JsonPropertyName("sticker_param")]
        public StickerParam? StickerParam { get; set; }

        /// <summary>
        /// Product source
        /// </summary>
        [JsonPropertyName("product_source")]
        public string? ProductSource { get; set; }

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Identity type
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// Identity authorized BC ID
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }

        /// <summary>
        /// Catalog authorized BC ID
        /// </summary>
        [JsonPropertyName("catalog_authorized_bc_id")]
        public string? CatalogAuthorizedBcId { get; set; }

        /// <summary>
        /// Store ID
        /// </summary>
        [JsonPropertyName("store_id")]
        public string? StoreId { get; set; }

        /// <summary>
        /// Store authorized BC ID
        /// </summary>
        [JsonPropertyName("store_authorized_bc_id")]
        public string? StoreAuthorizedBcId { get; set; }

        /// <summary>
        /// Product specific type
        /// </summary>
        [JsonPropertyName("product_specific_type")]
        public string? ProductSpecificType { get; set; }

        /// <summary>
        /// Product set ID
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string? ProductSetId { get; set; }

        /// <summary>
        /// Item group IDs
        /// </summary>
        [JsonPropertyName("item_group_ids")]
        public List<string>? ItemGroupIds { get; set; }

        /// <summary>
        /// SKU IDs
        /// </summary>
        [JsonPropertyName("sku_ids")]
        public List<string>? SkuIds { get; set; }

        /// <summary>
        /// Dynamic format
        /// </summary>
        [JsonPropertyName("dynamic_format")]
        public string? DynamicFormat { get; set; }

        /// <summary>
        /// Vertical video strategy
        /// </summary>
        [JsonPropertyName("vertical_video_strategy")]
        public string? VerticalVideoStrategy { get; set; }

        /// <summary>
        /// Ad text
        /// </summary>
        [JsonPropertyName("ad_text")]
        public string? AdText { get; set; }

        /// <summary>
        /// Whether to show price on product card
        /// </summary>
        [JsonPropertyName("card_show_price")]
        public bool? CardShowPrice { get; set; }

        /// <summary>
        /// Card tags
        /// </summary>
        [JsonPropertyName("card_tags")]
        public List<string>? CardTags { get; set; }

        /// <summary>
        /// Card image index
        /// </summary>
        [JsonPropertyName("card_image_index")]
        public int? CardImageIndex { get; set; }

        /// <summary>
        /// Showcase products
        /// </summary>
        [JsonPropertyName("showcase_products")]
        public List<ShowcaseProduct>? ShowcaseProducts { get; set; }
    }

    /// <summary>
    /// Badge position information
    /// </summary>
    public class BadgePosition
    {
        /// <summary>
        /// Relative X-axis value of the top-left corner
        /// </summary>
        [JsonPropertyName("position_x")]
        public float PositionX { get; set; }

        /// <summary>
        /// Relative Y-axis value of the top-left corner
        /// </summary>
        [JsonPropertyName("position_y")]
        public float PositionY { get; set; }

        /// <summary>
        /// Angle of the badge
        /// </summary>
        [JsonPropertyName("angle")]
        public double? Angle { get; set; }
    }

    /// <summary>
    /// Badge image information
    /// </summary>
    public class BadgeImageInfo
    {
        /// <summary>
        /// ID of the badge image
        /// </summary>
        [JsonPropertyName("image_id")]
        public string ImageId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Slide dimension for curved slide gesture
    /// </summary>
    public class SlideDimension
    {
        /// <summary>
        /// Width of the curved slide gesture
        /// </summary>
        [JsonPropertyName("dimension_width")]
        public double DimensionWidth { get; set; }

        /// <summary>
        /// Height of the curved slide gesture
        /// </summary>
        [JsonPropertyName("dimension_height")]
        public double DimensionHeight { get; set; }
    }

    /// <summary>
    /// Sticker parameters for countdown and gift code stickers
    /// </summary>
    public class StickerParam
    {
        /// <summary>
        /// Sticker type
        /// </summary>
        [JsonPropertyName("sticker_type")]
        public string? StickerType { get; set; }

        /// <summary>
        /// Sticker title
        /// </summary>
        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Gift code (for GIFTCODE type)
        /// </summary>
        [JsonPropertyName("giftcode")]
        public string? Giftcode { get; set; }

        /// <summary>
        /// Countdown deadline or LIVE start time
        /// </summary>
        [JsonPropertyName("cutoff_time")]
        public string? CutoffTime { get; set; }

        /// <summary>
        /// Background color of the sticker
        /// </summary>
        [JsonPropertyName("color")]
        public string Color { get; set; } = string.Empty;

        /// <summary>
        /// Sticker display angle
        /// </summary>
        [JsonPropertyName("display_angle")]
        public int? DisplayAngle { get; set; }

        /// <summary>
        /// Predefined placement
        /// </summary>
        [JsonPropertyName("predefined_placement")]
        public string? PredefinedPlacement { get; set; }

        /// <summary>
        /// X-axis coordinate
        /// </summary>
        [JsonPropertyName("position_x")]
        public int? PositionX { get; set; }

        /// <summary>
        /// Y-axis coordinate
        /// </summary>
        [JsonPropertyName("position_y")]
        public int? PositionY { get; set; }

        /// <summary>
        /// Sticker size by percentage
        /// </summary>
        [JsonPropertyName("size")]
        public string? Size { get; set; }

        /// <summary>
        /// Sticker opacity
        /// </summary>
        [JsonPropertyName("opacity")]
        public string? Opacity { get; set; }

        /// <summary>
        /// Reminder time
        /// </summary>
        [JsonPropertyName("reminder_time")]
        public string? ReminderTime { get; set; }

        /// <summary>
        /// Landing page URL for reminder countdown
        /// </summary>
        [JsonPropertyName("landing_page_url")]
        public string? LandingPageUrl { get; set; }

        /// <summary>
        /// TikTok user ID of LIVE event host
        /// </summary>
        [JsonPropertyName("live_tiktok_user_id")]
        public string? LiveTiktokUserId { get; set; }
    }



    /// <summary>
    /// Response for creating a creative portfolio
    /// </summary>
    public class CreativePortfolioCreateResponse
    {
        /// <summary>
        /// ID of the creative portfolio that has been created
        /// </summary>
        [JsonPropertyName("creative_portfolio_id")]
        public string CreativePortfolioId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting a creative portfolio by ID
    /// </summary>
    public class CreativePortfolioGetResponse
    {
        /// <summary>
        /// Creative portfolio ID
        /// </summary>
        [JsonPropertyName("creative_portfolio_id")]
        public string CreativePortfolioId { get; set; } = string.Empty;

        /// <summary>
        /// Creative portfolio type
        /// </summary>
        [JsonPropertyName("creative_portfolio_type")]
        public string CreativePortfolioType { get; set; } = string.Empty;

        /// <summary>
        /// The content of the creative portfolio
        /// </summary>
        [JsonPropertyName("portfolio_content")]
        public List<PortfolioContentResponse> PortfolioContent { get; set; } = new List<PortfolioContentResponse>();
    }

    /// <summary>
    /// Portfolio content response with additional fields
    /// </summary>
    public class PortfolioContentResponse : PortfolioContent
    {
        /// <summary>
        /// Content URL (for WEB_INFO_CARD)
        /// </summary>
        [JsonPropertyName("content_url")]
        public string? ContentUrl { get; set; }

        /// <summary>
        /// Thumbnail ID
        /// </summary>
        [JsonPropertyName("thumbnail_id")]
        public string? ThumbnailId { get; set; }
    }

    /// <summary>
    /// Response for getting portfolios within an ad account
    /// </summary>
    public class CreativePortfoliosListResponse
    {
        /// <summary>
        /// Information about the creative portfolios
        /// </summary>
        [JsonPropertyName("creative_portfolios")]
        public List<CreativePortfolioInfo> CreativePortfolios { get; set; } = new List<CreativePortfolioInfo>();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Creative portfolio information
    /// </summary>
    public class CreativePortfolioInfo
    {
        /// <summary>
        /// ID of the creative portfolio
        /// </summary>
        [JsonPropertyName("creative_portfolio_id")]
        public string CreativePortfolioId { get; set; } = string.Empty;

        /// <summary>
        /// Type of the creative portfolio
        /// </summary>
        [JsonPropertyName("creative_portfolio_type")]
        public string CreativePortfolioType { get; set; } = string.Empty;

        /// <summary>
        /// Preview link for the creative portfolio
        /// </summary>
        [JsonPropertyName("creative_portfolio_preview_url")]
        public string? CreativePortfolioPreviewUrl { get; set; }

        /// <summary>
        /// Card type (for CARD type)
        /// </summary>
        [JsonPropertyName("card_type")]
        public string? CardType { get; set; }

        /// <summary>
        /// Gesture type (for GESTURE type)
        /// </summary>
        [JsonPropertyName("gesture_type")]
        public string? GestureType { get; set; }

        /// <summary>
        /// Image ID
        /// </summary>
        [JsonPropertyName("image_id")]
        public string? ImageId { get; set; }

        /// <summary>
        /// Pop-up window image ID
        /// </summary>
        [JsonPropertyName("pop_up_window_image_id")]
        public string? PopUpWindowImageId { get; set; }

        /// <summary>
        /// Title (for WEB_INFO_CARD)
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// Selling points (for WEB_INFO_CARD)
        /// </summary>
        [JsonPropertyName("selling_points")]
        public List<string>? SellingPoints { get; set; }

        /// <summary>
        /// Content URL (for WEB_INFO_CARD)
        /// </summary>
        [JsonPropertyName("content_url")]
        public string? ContentUrl { get; set; }

        /// <summary>
        /// Thumbnail ID
        /// </summary>
        [JsonPropertyName("thumbnail_id")]
        public string? ThumbnailId { get; set; }

        /// <summary>
        /// Interactive guide text
        /// </summary>
        [JsonPropertyName("call_to_action_text")]
        public string? CallToActionText { get; set; }

        /// <summary>
        /// Badge show time
        /// </summary>
        [JsonPropertyName("badge_show_time")]
        public int? BadgeShowTime { get; set; }

        /// <summary>
        /// Badge position
        /// </summary>
        [JsonPropertyName("badge_position")]
        public BadgePosition? BadgePosition { get; set; }

        /// <summary>
        /// Badge image info
        /// </summary>
        [JsonPropertyName("badge_image_info")]
        public BadgeImageInfo? BadgeImageInfo { get; set; }

        /// <summary>
        /// Slide length
        /// </summary>
        [JsonPropertyName("slide_length")]
        public double? SlideLength { get; set; }

        /// <summary>
        /// Slide dimension
        /// </summary>
        [JsonPropertyName("slide_dimension")]
        public SlideDimension? SlideDimension { get; set; }

        /// <summary>
        /// Interactive music ID
        /// </summary>
        [JsonPropertyName("interactive_music_id")]
        public string? InteractiveMusicId { get; set; }

        /// <summary>
        /// Layouts
        /// </summary>
        [JsonPropertyName("layouts")]
        public List<string>? Layouts { get; set; }

        /// <summary>
        /// Description
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Tags
        /// </summary>
        [JsonPropertyName("tags")]
        public List<string>? Tags { get; set; }

        /// <summary>
        /// Category label
        /// </summary>
        [JsonPropertyName("category_label")]
        public string? CategoryLabel { get; set; }

        /// <summary>
        /// App ID
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Sticker parameters
        /// </summary>
        [JsonPropertyName("sticker_param")]
        public StickerParam? StickerParam { get; set; }

        /// <summary>
        /// Whether to show price on card
        /// </summary>
        [JsonPropertyName("card_show_price")]
        public bool? CardShowPrice { get; set; }

        /// <summary>
        /// Card tags
        /// </summary>
        [JsonPropertyName("card_tags")]
        public List<string>? CardTags { get; set; }

        /// <summary>
        /// Card image index
        /// </summary>
        [JsonPropertyName("card_image_index")]
        public int? CardImageIndex { get; set; }
    }

    /// <summary>
    /// Filtering conditions for getting portfolios
    /// </summary>
    public class CreativePortfolioFiltering
    {
        /// <summary>
        /// The types of creative portfolios to filter by
        /// </summary>
        [JsonPropertyName("creative_portfolio_types")]
        public List<string>? CreativePortfolioTypes { get; set; }

        /// <summary>
        /// The list of creative portfolio IDs to filter by
        /// </summary>
        [JsonPropertyName("creative_portfolio_ids")]
        public List<string>? CreativePortfolioIds { get; set; }
    }

    /// <summary>
    /// Request body for deleting creative portfolios
    /// </summary>
    public class CreativePortfolioDeleteBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// List of creative portfolio IDs to delete
        /// </summary>
        [JsonPropertyName("creative_portfolio_ids")]
        public List<string> CreativePortfolioIds { get; set; } = new List<string>();
    }
}
