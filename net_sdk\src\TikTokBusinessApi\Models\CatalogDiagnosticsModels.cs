/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Get Catalog Diagnostics Models

    /// <summary>
    /// Request for getting synchronous catalog product diagnostic information
    /// </summary>
    public class GetCatalogDiagnosticsRequest
    {
        /// <summary>
        /// ID of a catalog that you have permission to access (Required)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// ID of a Business Center that either owns the catalog, or has been granted access to it as an asset (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID. If not specified, the ID of the default feed for the catalog will be used.
        /// To retrieve the diagnostics for all feeds for the catalog, set this field to "ALL".
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string? FeedId { get; set; }

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public CatalogDiagnosticsFiltering? Filtering { get; set; }

        /// <summary>
        /// The language you want to set for the returned issue_title and reason_and_suggestion.
        /// Default value: "en".
        /// </summary>
        [JsonPropertyName("lang")]
        public string? Lang { get; set; }

        /// <summary>
        /// Current page number. Default value: 1. Value range: ≥1.
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10. Value range: [1, 20].
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Filtering conditions for catalog diagnostics
    /// </summary>
    public class CatalogDiagnosticsFiltering
    {
        /// <summary>
        /// The issue level to filter the results by.
        /// Enum values: "CRITICAL", "WARNING"
        /// </summary>
        [JsonPropertyName("issue_level")]
        public string? IssueLevel { get; set; }

        /// <summary>
        /// The issue category to filter the results by.
        /// Enum values: "PRODUCT_ATTRIBUTES", "PRODUCT_REVIEW", "CATALOG", "PIXEL_OR_EVENT", "FILE_UPLOAD_OR_FEED"
        /// </summary>
        [JsonPropertyName("issue_category")]
        public string? IssueCategory { get; set; }
    }

    /// <summary>
    /// Response for getting catalog diagnostics
    /// </summary>
    public class GetCatalogDiagnosticsResponse
    {
        /// <summary>
        /// The date (UTC +0 Time) when the diagnostic information was generated, in the format of "YYYY-MM-DD"
        /// </summary>
        [JsonPropertyName("diagnostic_date")]
        public string? DiagnosticDate { get; set; }

        /// <summary>
        /// Diagnostic information about issues detected in the catalog
        /// </summary>
        [JsonPropertyName("issues")]
        public List<CatalogDiagnosticIssue>? Issues { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Catalog diagnostic issue information
    /// </summary>
    public class CatalogDiagnosticIssue
    {
        /// <summary>
        /// Issue ID
        /// </summary>
        [JsonPropertyName("issue_id")]
        public string? IssueId { get; set; }

        /// <summary>
        /// The issue title, which provides a summary of the issue
        /// </summary>
        [JsonPropertyName("issue_title")]
        public string? IssueTitle { get; set; }

        /// <summary>
        /// Reason and suggestion. This field contains a description of the issue and a suggestion on how to fix it.
        /// </summary>
        [JsonPropertyName("reason_and_suggestion")]
        public string? ReasonAndSuggestion { get; set; }

        /// <summary>
        /// Issue level. Enum values: "CRITICAL", "WARNING"
        /// </summary>
        [JsonPropertyName("issue_level")]
        public string? IssueLevel { get; set; }

        /// <summary>
        /// Issue category. Enum values: "PRODUCT_ATTRIBUTES", "PRODUCT_REVIEW", "CATALOG", "PIXEL_OR_EVENT", "FILE_UPLOAD_OR_FEED"
        /// </summary>
        [JsonPropertyName("issue_category")]
        public string? IssueCategory { get; set; }

        /// <summary>
        /// The product field for which the issue was detected
        /// </summary>
        [JsonPropertyName("issue_product_field")]
        public string? IssueProductField { get; set; }

        /// <summary>
        /// The number of catalog products for which the issue was detected
        /// </summary>
        [JsonPropertyName("affected_product_count")]
        public int? AffectedProductCount { get; set; }

        /// <summary>
        /// The percentage of catalog products for which the issue was detected. Value range: [0,100].
        /// </summary>
        [JsonPropertyName("affected_product_percentage")]
        public decimal? AffectedProductPercentage { get; set; }

        /// <summary>
        /// An object array containing up to 10 sample catalog products for which the issue was detected
        /// </summary>
        [JsonPropertyName("example_affected_products")]
        public List<object>? ExampleAffectedProducts { get; set; }
    }

    #endregion

    #region Create Diagnostic Task Models

    /// <summary>
    /// Request for creating an asynchronous download task for catalog product diagnostic information
    /// </summary>
    public class CreateDiagnosticTaskRequest
    {
        /// <summary>
        /// ID of a catalog that you have permission to access (Required)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// ID of a Business Center that either owns the catalog, or has been granted access to it as an asset (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID. If not specified, the ID of the default feed for the catalog will be used.
        /// To retrieve the diagnostics for all feeds for the catalog, set this field to "ALL".
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string? FeedId { get; set; }

        /// <summary>
        /// The language you want to set for the returned catalog diagnostic information.
        /// Default value: "en".
        /// </summary>
        [JsonPropertyName("lang")]
        public string? Lang { get; set; }

        /// <summary>
        /// The ID of the issue you want to download the diagnostic information for.
        /// If not specified, the diagnostics for all detected issues will be returned.
        /// </summary>
        [JsonPropertyName("issue_id")]
        public string? IssueId { get; set; }
    }

    /// <summary>
    /// Response for creating diagnostic task
    /// </summary>
    public class CreateDiagnosticTaskResponse
    {
        /// <summary>
        /// ID of the download task for catalog diagnostic information
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }
    }

    #endregion

    #region Get Diagnostic Task Models

    /// <summary>
    /// Request for downloading asynchronous catalog product diagnostic information
    /// </summary>
    public class GetDiagnosticTaskRequest
    {
        /// <summary>
        /// Catalog ID that you used to create the download task for catalog diagnostic information (Required)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Business Center ID that you used to create the download task for catalog diagnostic information (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the download task for catalog diagnostic information (Required)
        /// </summary>
        [JsonPropertyName("task_id")]
        public string TaskId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting diagnostic task
    /// </summary>
    public class GetDiagnosticTaskResponse
    {
        /// <summary>
        /// The status of the download task for catalog diagnostic information.
        /// Enum values: "SUCCEED", "PROCESSING", "FAILED"
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// The URL to download the CSV file of catalog diagnostic information
        /// </summary>
        [JsonPropertyName("diagnostic_file_url")]
        public string? DiagnosticFileUrl { get; set; }
    }

    #endregion

    #region Event Source Issues Models

    /// <summary>
    /// Request for getting catalog event source diagnostic information
    /// </summary>
    public class GetEventSourceIssuesRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID (Required)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// The event source type (Required)
        /// Enum values: "APP", "PIXEL"
        /// </summary>
        [JsonPropertyName("event_source_type")]
        public string EventSourceType { get; set; } = string.Empty;

        /// <summary>
        /// Required when event_source_type is set to "APP". The App ID.
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Required when event_source_type is set to "PIXEL". The Pixel code.
        /// </summary>
        [JsonPropertyName("pixel_code")]
        public string? PixelCode { get; set; }

        /// <summary>
        /// The event type that you want to retrieve data for.
        /// Enum values: "VIEW_CONTENT", "ADD_TO_CART", "PURCHASE"
        /// Default value: "VIEW_CONTENT".
        /// </summary>
        [JsonPropertyName("event_type")]
        public string? EventType { get; set; }

        /// <summary>
        /// The time range that you want to retrieve data for.
        /// Enum values: "YESTERDAY", "LAST_7_DAYS", "LAST_30_DAYS"
        /// Default value: "LAST_7_DAYS".
        /// </summary>
        [JsonPropertyName("time_range")]
        public string? TimeRange { get; set; }
    }

    /// <summary>
    /// Response for getting event source issues
    /// </summary>
    public class GetEventSourceIssuesResponse
    {
        /// <summary>
        /// A list of returned data
        /// </summary>
        [JsonPropertyName("list")]
        public List<EventSourceIssue>? List { get; set; }
    }

    /// <summary>
    /// Event source issue information
    /// </summary>
    public class EventSourceIssue
    {
        /// <summary>
        /// The issue detected from diagnostics
        /// </summary>
        [JsonPropertyName("diagnostic_result")]
        public string? DiagnosticResult { get; set; }

        /// <summary>
        /// Issue level. Enum values: "ERROR", "WARNING", "INFO"
        /// </summary>
        [JsonPropertyName("level")]
        public string? Level { get; set; }

        /// <summary>
        /// The suggested solution for the issue
        /// </summary>
        [JsonPropertyName("diagnostic_solution")]
        public string? DiagnosticSolution { get; set; }
    }

    #endregion

    #region Event Source Metrics Models

    /// <summary>
    /// Request for getting catalog event trends and match rate
    /// </summary>
    public class GetEventSourceMetricsRequest
    {
        /// <summary>
        /// Business Center ID (Required)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID (Required)
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// The event source type (Required)
        /// Enum values: "APP", "PIXEL"
        /// </summary>
        [JsonPropertyName("event_source_type")]
        public string EventSourceType { get; set; } = string.Empty;

        /// <summary>
        /// Required when event_source_type is set to "APP". The App ID.
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Required when event_source_type is set to "PIXEL". The Pixel code.
        /// </summary>
        [JsonPropertyName("pixel_code")]
        public string? PixelCode { get; set; }

        /// <summary>
        /// The event type that you want to retrieve data for.
        /// Enum values: "VIEW_CONTENT", "ADD_TO_CART", "PURCHASE"
        /// Default value: "VIEW_CONTENT".
        /// </summary>
        [JsonPropertyName("event_type")]
        public string? EventType { get; set; }

        /// <summary>
        /// The time range that you want to retrieve data for.
        /// Enum values: "YESTERDAY", "LAST_7_DAYS", "LAST_30_DAYS"
        /// Default value: "LAST_7_DAYS".
        /// </summary>
        [JsonPropertyName("time_range")]
        public string? TimeRange { get; set; }
    }

    /// <summary>
    /// Response for getting event source metrics
    /// </summary>
    public class GetEventSourceMetricsResponse
    {
        /// <summary>
        /// Information about the trend of events received and the number of events available for retargeting
        /// </summary>
        [JsonPropertyName("list")]
        public List<EventSourceMetric>? List { get; set; }
    }

    /// <summary>
    /// Event source metric information
    /// </summary>
    public class EventSourceMetric
    {
        /// <summary>
        /// The available categories of events.
        /// Enum values: "EVENT_RECEIVED", "EVENT_WITH_CONTENT_ID", "EVENT_WITH_CONTENT_ID_MATCHING_INVENTORY"
        /// </summary>
        [JsonPropertyName("available_type")]
        public string? AvailableType { get; set; }

        /// <summary>
        /// The details of the event category
        /// </summary>
        [JsonPropertyName("event_details")]
        public List<EventDetail>? EventDetails { get; set; }
    }

    /// <summary>
    /// Event detail information
    /// </summary>
    public class EventDetail
    {
        /// <summary>
        /// The date, in the format of "YYYY-MM-DD"
        /// </summary>
        [JsonPropertyName("date")]
        public string? Date { get; set; }

        /// <summary>
        /// The number of events that belong to the event category are received on the date
        /// </summary>
        [JsonPropertyName("count")]
        public string? Count { get; set; }

        /// <summary>
        /// The percentage of the events belonging to the event category that are received on the date, out of all events received on the date
        /// </summary>
        [JsonPropertyName("percentage")]
        public string? Percentage { get; set; }
    }

    #endregion
}
