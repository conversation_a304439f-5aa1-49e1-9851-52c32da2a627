using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Worker để đồng bộ GMV Max
    /// </summary>
    public class SyncGmvMaxWorker : WorkerJobBase
    {
        private readonly IGmvMaxSyncService _gmvMaxSyncService;

        public SyncGmvMaxWorker(
            ILogger<SyncGmvMaxWorker> logger,
            IServiceProvider serviceProvider,
            IGmvMaxSyncService gmvMaxSyncService)
            : base(logger, serviceProvider)
        {
            _gmvMaxSyncService = gmvMaxSyncService;
        }

        public override CommandType CommandType => CommandType.SyncGmvMax;

        protected override async Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job)
        {
            // Parse parameters
            var parameters = ParseParameters(args.Parameters);

            // Sync GMV Max - sync all GMV Max data for the BC
            var responseSync = await _gmvMaxSyncService.SyncGmvMaxAsync(parameters.BcId, parameters.StartDate,parameters.EndDate);
            if (responseSync != null)
            {
                if (string.IsNullOrEmpty(responseSync.ErrorMessage))
                    return JobResult.Success(JsonConvert.SerializeObject(responseSync));
                else
                    return JobResult.Error(responseSync.ErrorMessage, JsonConvert.SerializeObject(responseSync));
            }
            else
            {
                return JobResult.Error("Lỗi đồng bộ GMV Max");
            }
        }

        /// <summary>
        /// Parse parameters từ JSON
        /// </summary>
        /// <param name="parametersJson">JSON parameters</param>
        /// <returns>Parameters object</returns>
        private SyncGmvMaxParameters ParseParameters(string parametersJson)
        {
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<SyncGmvMaxParameters>(parametersJson)
                    ?? new SyncGmvMaxParameters();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse parameters, using defaults");
                return new SyncGmvMaxParameters();
            }
        }

        /// <summary>
        /// Parameters cho SyncGmvMax
        /// </summary>
        public class SyncGmvMaxParameters:DefaultParameters
        {
        }
    }
}
