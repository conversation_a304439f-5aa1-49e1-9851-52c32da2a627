/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for updating Pangle block list
    /// </summary>
    public class PangleBlockListUpdateBody
    {
        /// <summary>
        /// List of app IDs to add to the block list
        /// </summary>
        [JsonPropertyName("add_app_list")]
        public List<string>? AddAppList { get; set; }

        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Whether to clear old apps from the block list
        /// </summary>
        [JsonPropertyName("clear_old_app")]
        public bool? ClearOldApp { get; set; } = false;

        /// <summary>
        /// List of app IDs to delete from the block list
        /// </summary>
        [JsonPropertyName("delete_app_list")]
        public List<string>? DeleteAppList { get; set; }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="obj">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not PangleBlockListUpdateBody other)
                return false;

            return AdvertiserId == other.AdvertiserId &&
                   ClearOldApp == other.ClearOldApp;
        }

        /// <summary>
        /// Returns the hash code for this instance
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AdvertiserId, ClearOldApp);
        }

        /// <summary>
        /// Returns the string representation of the object
        /// </summary>
        /// <returns>String representation of the object</returns>
        public override string ToString()
        {
            return $"PangleBlockListUpdateBody {{ AdvertiserId = {AdvertiserId}, ClearOldApp = {ClearOldApp} }}";
        }
    }
}
