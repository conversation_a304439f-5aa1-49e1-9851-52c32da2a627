/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Reach and Frequency operations
    /// </summary>
    public interface IReachAndFrequencyApi
    {
        /// <summary>
        /// Get inventory estimates for Reach & Frequency ads
        /// </summary>
        /// <param name="request">Inventory estimate request</param>
        /// <returns>Inventory estimate response</returns>
        Task<RFInventoryEstimateResponse> GetInventoryEstimateAsync(RFInventoryEstimateRequest request);

        /// <summary>
        /// Create a Reach & Frequency ad group
        /// </summary>
        /// <param name="request">RF ad group creation request</param>
        /// <returns>RF ad group creation response</returns>
        // Task<RFAdGroupCreateResponse> CreateRFAdGroupAsync(RFAdGroupCreateRequest request);

        /// <summary>
        /// Update Reach & Frequency ad groups
        /// </summary>
        /// <param name="request">RF ad group update request</param>
        /// <returns>RF ad group creation response</returns>
        // Task<RFAdGroupCreateResponse> UpdateRFAdGroupAsync(RFAdGroupUpdateRequest request);

        /// <summary>
        /// Cancel the R&F ad order to suspend its delivery
        /// </summary>
        /// <param name="request">RF order cancel request</param>
        /// <returns>RF order cancel response</returns>
        // Task<RFOrderCancelResponse> CancelRFOrderAsync(RFOrderCancelRequest request);

        /// <summary>
        /// Get estimated info of R&F ad groups
        /// </summary>
        /// <param name="request">RF estimated info request</param>
        /// <returns>RF estimated info response</returns>
        Task<RFEstimatedInfoResponse> GetRFEstimatedInfoAsync(RFEstimatedInfoRequest request);

        /// <summary>
        /// Query contracts
        /// </summary>
        /// <param name="request">RF contract query request</param>
        /// <returns>RF contract query response</returns>
        Task<RFContractQueryResponse> QueryContractsAsync(RFContractQueryRequest request);

        /// <summary>
        /// Get R&F time zones according to location
        /// </summary>
        /// <param name="request">RF time zone request</param>
        /// <returns>RF time zone response</returns>
        Task<RFTimeZoneResponse> GetRFTimeZonesAsync(RFTimeZoneRequest request);
    }
}
