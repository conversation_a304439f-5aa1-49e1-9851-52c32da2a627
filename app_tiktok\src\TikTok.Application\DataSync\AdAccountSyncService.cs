using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu tài khoản quảng cáo
    ///
    /// Logic mới:
    /// - Sử dụng Asset từ database thay vì API ủy quyền để lấy danh sách tài khoản quảng cáo
    /// - Chỉ đồng bộ các tài khoản quảng cáo có AssetType là ADVERTISER
    /// - <PERSON><PERSON>h dấu xóa AdAccount dựa trên Asset có IsRemoved = true
    /// - Khôi phục AdAccount nếu Asset được khôi phục (IsRemoved = false)
    /// - Xử lý nâng cao lỗi quyền truy cập: tự động loại bỏ tài khoản không có quyền và thử lại
    /// - Với những tài khoản không có quyền, lấy thông tin từ Asset và map sang AdAccount
    ///
    /// Lưu ý về xử lý thời gian:
    /// - CreateTime từ API sẽ được convert sang UTC trước khi lưu vào database
    /// - Timezone lưu trữ múi giờ gốc để có thể convert ngược lại từ UTC khi cần thiết
    /// - CreateTime từ API là Unix timestamp, được parse và convert sang UTC
    /// </summary>
    public class AdAccountSyncService : BaseSyncService, IAdAccountSyncService
    {
        private readonly IAdAccountRepository _adAccountRepository;
        private readonly IRepository<RawAdAccountEntity, Guid> _adAccountEntityRepository;
        private readonly IAssetRepository _assetRepository;

        public AdAccountSyncService(
            IServiceProvider serviceProvider,
            ILogger<AdAccountSyncService> logger,
            IAdAccountRepository adAccountRepository,
            IRepository<RawAdAccountEntity, Guid> adAccountEntityRepository,
            IAssetRepository assetRepository
            ) : base(serviceProvider, logger)
        {
            _adAccountRepository = adAccountRepository;
            _adAccountEntityRepository = adAccountEntityRepository;
            _assetRepository = assetRepository;
        }

        /// <summary>
        /// Đồng bộ tài khoản quảng cáo theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AdAccountSyncResult> SyncAdAccountsAsync(string bcId)
        {
            var result = new AdAccountSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tài khoản quảng cáo cho BC: {BcId}", bcId);
                // 2. Tạo TikTok client
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                // 3. Đồng bộ tài khoản quảng cáo
                var adAccountResult = await SyncAdAccountsFromApiAsync(tikTokClient, bcId);
                result.TotalSynced = adAccountResult.TotalSynced;
                result.NewRecords = adAccountResult.NewRecords;
                result.UpdatedRecords = adAccountResult.UpdatedRecords;
                result.AdAccountCount = adAccountResult.AdAccountCount;

                _logger.LogDebug("Hoàn thành đồng bộ tài khoản quảng cáo cho BC: {BcId}. Asset Count: {AssetCount}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    bcId, result.AdAccountCount, result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (BusinessException ex)
            {
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ tài khoản quảng cáo cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tài khoản quảng cáo: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tài khoản quảng cáo cho BC: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ tài khoản quảng cáo theo Advertiser ID
        /// Logic mới:
        /// 1. Lấy tài khoản quảng cáo hiện tại để lấy BC ID
        /// 2. Kiểm tra xem tài khoản quảng cáo có tồn tại trong Asset không
        /// 3. Tạo TikTok client và đồng bộ
        /// </summary>
        /// <param name="advertiserId">ID của tài khoản quảng cáo</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AdAccountSyncResult> SyncAdAccountAsync(string advertiserId)
        {
            var result = new AdAccountSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tài khoản quảng cáo: {AdvertiserId}", advertiserId);

                // 1. Lấy tài khoản quảng cáo hiện tại để lấy BC ID
                var existingAdAccount = await _adAccountRepository.GetByAdvertiserIdAsync(advertiserId);
                if (existingAdAccount == null)
                {
                    result.ErrorMessage = $"Không tìm thấy tài khoản quảng cáo với Advertiser ID: {advertiserId}";
                    _logger.LogWarning(result.ErrorMessage);
                    return result;
                }

                // 2. Kiểm tra xem tài khoản quảng cáo có tồn tại trong Asset không
                var asset = await _assetRepository.GetByAssetIdAsync(advertiserId, false);
                if (asset == null || asset.AssetType != AssetType.ADVERTISER || asset.IsRemoved)
                {
                    result.ErrorMessage = $"Tài khoản quảng cáo {advertiserId} không tồn tại trong Asset hoặc đã bị xóa";
                    _logger.LogWarning(result.ErrorMessage);
                    return result;
                }

                // Kiểm tra xem Asset có thuộc về BC đúng không
                if (asset.BcId != existingAdAccount.OwnerBcId)
                {
                    result.ErrorMessage = $"Tài khoản quảng cáo {advertiserId} không thuộc về BC {existingAdAccount.OwnerBcId}";
                    _logger.LogWarning(result.ErrorMessage);
                    return result;
                }

                // 3. Tạo TikTok client
                using var tikTokClient = await CreateTikTokBusinessApiClient(existingAdAccount.OwnerBcId);

                // 4. Đồng bộ tài khoản quảng cáo cụ thể
                var adAccountResult = await SyncSpecificAdAccountFromApiAsync(tikTokClient, advertiserId);
                result.TotalSynced = adAccountResult.TotalSynced;
                result.NewRecords = adAccountResult.NewRecords;
                result.UpdatedRecords = adAccountResult.UpdatedRecords;
                result.AdAccountCount = adAccountResult.AdAccountCount;

                _logger.LogDebug("Hoàn thành đồng bộ tài khoản quảng cáo: {AdvertiserId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                    advertiserId, result.TotalSynced, result.NewRecords, result.UpdatedRecords);
            }
            catch (BusinessException ex)
            {
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ tài khoản quảng cáo: {AdvertiserId}", advertiserId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tài khoản quảng cáo: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tài khoản quảng cáo: {AdvertiserId}", advertiserId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ tất cả tài khoản quảng cáo cho tất cả Business Centers
        /// Logic mới: Sử dụng Asset từ database thay vì API ủy quyền
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AdAccountSyncResult> SyncAllAdAccountsForAllBcsAsync()
        {
            var result = new AdAccountSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ tất cả tài khoản quảng cáo cho tất cả Business Centers");

                // 1. Lấy tất cả Business Centers có ứng dụng active
                var allBusinessApplications = await _businessApplicationCache.GetAllActiveAsync();
                var bcGroups = allBusinessApplications.GroupBy(x => x.BcId);

                foreach (var bcGroup in bcGroups)
                {
                    var bcId = bcGroup.Key;
                    var activeApplication = bcGroup.FirstOrDefault(x => x.IsActive);

                    if (activeApplication == null || string.IsNullOrEmpty(activeApplication.AccessToken))
                    {
                        _logger.LogWarning("Bỏ qua BC {BcId} - không có ứng dụng active hoặc access token", bcId);
                        continue;
                    }

                    try
                    {
                        // 2. Tạo TikTok client cho từng BC
                        using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                        // 3. Đồng bộ tài khoản quảng cáo cho BC này
                        var bcResult = await SyncAdAccountsFromApiAsync(tikTokClient, bcId);
                        result.TotalSynced += bcResult.TotalSynced;
                        result.NewRecords += bcResult.NewRecords;
                        result.UpdatedRecords += bcResult.UpdatedRecords;
                        result.AdAccountCount += bcResult.AdAccountCount;
                        result.BcCount++;

                        _logger.LogDebug("Hoàn thành đồng bộ tài khoản quảng cáo cho BC: {BcId}. Asset Count: {AssetCount}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}",
                            bcId, bcResult.AdAccountCount, bcResult.TotalSynced, bcResult.NewRecords, bcResult.UpdatedRecords);
                    }
                    catch (BusinessException bex)
                    {
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        result.ErrorMessage += bex.Message + Environment.NewLine;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Lỗi khi đồng bộ tài khoản quảng cáo cho BC: {BcId}", bcId);
                        // Tiếp tục với BC tiếp theo
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ tất cả tài khoản quảng cáo. Tổng BC: {BcCount}, Tổng Asset: {AdAccountCount}, Tổng bản ghi: {Total}",
                    result.BcCount, result.AdAccountCount, result.TotalSynced);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ tất cả tài khoản quảng cáo: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ tất cả tài khoản quảng cáo");
            }

            return result;
        }

        /// <summary>
        /// Cập nhật danh sách tài khoản quảng cáo từ tài sản theo BcId
        /// Logic:
        /// 1. Lấy danh sách tài sản hiện tại từ BC trong Db với AssetType là ADVERTISER
        /// 2. Lấy danh sách tài khoản quảng cáo hiện tại trong Db cho BC này
        /// 3. So sánh và thực hiện các thao tác:
        ///    - Thêm mới: Tài sản có nhưng tài khoản quảng cáo chưa có
        ///    - Cập nhật: Tài sản và tài khoản quảng cáo đều có, cập nhật trạng thái IsRemoved theo tài sản
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AdAccountSyncResult> UpdateAdAccountsFromAssetsAsync(string bcId)
        {
            var result = new AdAccountSyncResult();

            try
            {
                _logger.LogDebug("Bắt đầu cập nhật danh sách tài khoản quảng cáo từ tài sản cho BC: {BcId}", bcId);

                // 1. Lấy danh sách tài sản hiện tại từ BC trong Db với AssetType là ADVERTISER, RelationType là OWNER_BC
                var advertiserAssets = await _assetRepository.GetByBcIdAsync(
                                                                      bcId: bcId
                                                                      , includeRemoved: true
                                                                      , assetType: AssetType.ADVERTISER
                                                                      , relationType: RelationType.OWNER_BC
                                                                      );

                if (!advertiserAssets.Any())
                {
                    _logger.LogWarning("Không có tài sản quảng cáo nào cho BC: {BcId}", bcId);
                    return result;
                }

                // 2. Lấy danh sách tài khoản quảng cáo hiện tại trong Db cho BC này
                // Todo hostFix: Lấy tài khoản quản cáo nếu là owner BC
                //var existingAdAccounts = await _adAccountRepository.GetByOwnerBcIdAsync(bcId, true);
                var advertiserIds = advertiserAssets.Select(x => x.AssetId).ToList();
                var existingAdAccounts = await _adAccountRepository.GetListByAdvertiserIdsAsync(advertiserIds, true);

                using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
                {
                    var newEntities = new List<RawAdAccountEntity>();
                    var updatedEntities = new List<RawAdAccountEntity>();

                    // 3. Xử lý tất cả tài sản (bao gồm cả đã xóa và chưa xóa)
                    foreach (var asset in advertiserAssets)
                    {
                        var existingAdAccount = existingAdAccounts.FirstOrDefault(x => x.AdvertiserId == asset.AssetId);

                        if (existingAdAccount == null)
                        {
                            // Thêm mới: Tài sản có nhưng tài khoản quảng cáo chưa có
                            var newEntity = RawAdAccountEntity.CreateFromAsset(asset, bcId);
                            newEntities.Add(newEntity);
                            result.NewRecords++;
                            _logger.LogDebug("Thêm mới tài khoản quảng cáo từ tài sản: {AdvertiserId}", asset.AssetId);
                        }
                        else
                        {
                            // Cập nhật: Kiểm tra thay đổi chỉ với các trường có trong Asset
                            if (HasAssetChanged(existingAdAccount, asset))
                            {
                                existingAdAccount.UpdateFromAsset(asset);
                                updatedEntities.Add(existingAdAccount);
                                result.UpdatedRecords++;
                                _logger.LogDebug("Cập nhật tài khoản quảng cáo từ tài sản: {AdvertiserId}", asset.AssetId);
                            }
                        }
                        result.TotalSynced++;
                    }

                    // 4. Lưu vào database
                    if (newEntities.Any())
                    {
                        await _adAccountEntityRepository.InsertManyAsync(newEntities);
                    }

                    if (updatedEntities.Any())
                    {
                        await _adAccountEntityRepository.UpdateManyAsync(updatedEntities);
                    }

                    await uow.CompleteAsync();

                    _logger.LogDebug("Hoàn thành cập nhật danh sách tài khoản quảng cáo từ tài sản cho BC: {BcId}. " +
                        "Tổng tài sản: {AssetCount}, Thêm mới: {NewCount}, Cập nhật: {UpdatedCount}",
                        bcId, advertiserAssets.Count, newEntities.Count, updatedEntities.Count);
                }

                result.AdAccountCount = advertiserAssets.Count(x => !x.IsRemoved);
            }
            catch (BusinessException ex)
            {
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi cập nhật danh sách tài khoản quảng cáo từ tài sản cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi cập nhật danh sách tài khoản quảng cáo từ tài sản: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi cập nhật danh sách tài khoản quảng cáo từ tài sản cho BC: {BcId}", bcId);
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ tài khoản quảng cáo từ API theo BC ID
        /// Logic mới:
        /// 1. Lấy danh sách tài sản của BC từ database với AssetType là ADVERTISER (bao gồm cả đã xóa)
        /// 2. Lấy chi tiết tài khoản quảng cáo từ API (chỉ từ asset active)
        /// 3. Xử lý nâng cao lỗi quyền truy cập: tự động loại bỏ tài khoản không có quyền và thử lại
        /// 4. Map dữ liệu đồng bộ:
        ///    - Map danh sách dữ liệu đồng bộ về từ API thành entity
        ///    - Map danh sách tài khoản không có quyền, đọc dữ liệu từ asset thành entity
        /// 5. Xử lý database:
        ///    - Duyệt danh sách map entity
        ///    - Kiểm tra tồn tại: Đã có thì kiểm tra thay đổi và cập nhật, chưa có thì thêm mới
        /// 6. Đánh dấu xóa AdAccount dựa trên Asset có IsRemoved = true
        /// 7. Khôi phục AdAccount nếu Asset được khôi phục (IsRemoved = false)
        /// </summary>
        /// <param name="client">TikTok client</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        private async Task<AdAccountSyncResult> SyncAdAccountsFromApiAsync(TikTokBusinessApiClient client, string bcId)
        {
            var result = new AdAccountSyncResult();

            // 1. Lấy danh sách tài sản của BC từ database với AssetType là ADVERTISER, RelationType là OWNER_BC (bao gồm cả đã xóa)
            var advertiserAssets = await _assetRepository.GetByBcIdAsync(
                                                                        bcId: bcId
                                                                        , includeRemoved: true
                                                                        , assetType: AssetType.ADVERTISER
                                                                        , relationType: RelationType.OWNER_BC
                                                                        );

            if (!advertiserAssets.Any())
            {
                _logger.LogWarning("Không có tài sản quảng cáo nào cho BC: {BcId}", bcId);
                return result;
            }

            // Tách riêng các asset đã xóa và chưa xóa
            var activeAssets = advertiserAssets.Where(x => !x.IsRemoved).ToList();
            var removedAssets = advertiserAssets.Where(x => x.IsRemoved).ToList();

            // 2. Lấy chi tiết tài khoản quảng cáo (chỉ lấy từ các asset đang active)
            var advertiserIds = activeAssets.Select(x => x.AssetId).ToList();
            if (!advertiserIds.Any())
            {
                _logger.LogWarning("Không có tài khoản quảng cáo active nào cho BC: {BcId}", bcId);
                return result;
            }

            // TikTok API chỉ cho phép tối đa 100 advertiserIds trong một lần gọi
            const int maxBatchSize = 100;
            var allAdAccountDetails = new List<AdAccountDetail>();
            var unauthorizedAdvertiserIds = new List<string>();

            // Chia nhỏ danh sách advertiserIds thành các batch
            for (int i = 0; i < advertiserIds.Count; i += maxBatchSize)
            {
                var batch = advertiserIds.Skip(i).Take(maxBatchSize).ToList();
                _logger.LogDebug("Đang xử lý batch {BatchNumber} với {BatchSize} advertiserIds cho BC: {BcId}",
                    (i / maxBatchSize) + 1, batch.Count, bcId);

                var adAccountDetailsResponse = await GetAdAccountDetailsWithRetryAsync(client, batch, bcId);

                if (adAccountDetailsResponse.Data?.List != null && adAccountDetailsResponse.Data.List.Count > 0)
                {
                    allAdAccountDetails.AddRange(adAccountDetailsResponse.Data.List);
                }

                // Nếu có lỗi quyền truy cập, lưu lại danh sách tài khoản không có quyền
                if (AdAccountSyncHelper.IsPermissionError(adAccountDetailsResponse))
                {
                    var batchUnauthorizedIds = AdAccountSyncHelper.ParseUnauthorizedAdvertiserIds(adAccountDetailsResponse.Message);
                    unauthorizedAdvertiserIds.AddRange(batchUnauthorizedIds);
                }
            }

            // Kiểm tra xem tất cả các tài khoản quảng cáo từ API có tồn tại trong Asset không
            var apiAdvertiserIds = allAdAccountDetails.Select(x => x.AdvertiserId).ToHashSet();
            var assetAdvertiserIds = activeAssets.Select(x => x.AssetId).ToHashSet();
            var missingInAsset = apiAdvertiserIds.Except(assetAdvertiserIds).ToList();
            if (missingInAsset.Any())
            {
                _logger.LogWarning("Có {MissingCount} tài khoản quảng cáo từ API không tồn tại trong Asset active cho BC: {BcId}. Missing: {MissingIds}",
                    missingInAsset.Count, bcId, string.Join(", ", missingInAsset));
            }

            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                // ===== BƯỚC 1: MAP DỮ LIỆU ĐỒNG BỘ THÀNH DANH SÁCH ENTITY =====
                var syncEntities = new List<RawAdAccountEntity>();

                // 1.1. Map danh sách dữ liệu đồng bộ về từ API thành entity
                foreach (var apiAdAccount in allAdAccountDetails)
                {
                    if (string.IsNullOrEmpty(apiAdAccount.AdvertiserId))
                    {
                        _logger.LogWarning("Bỏ qua tài khoản quảng cáo - AdvertiserId rỗng");
                        continue;
                    }

                    // Chỉ đồng bộ các tài khoản quảng cáo có trong Asset
                    if (!assetAdvertiserIds.Contains(apiAdAccount.AdvertiserId))
                    {
                        _logger.LogDebug("Bỏ qua tài khoản quảng cáo {AdvertiserId} - không có trong Asset", apiAdAccount.AdvertiserId);
                        continue;
                    }

                    var syncEntity = MapApiAdAccountToEntity(apiAdAccount, bcId);
                    syncEntities.Add(syncEntity);
                }

                // 1.2. Map danh sách tài khoản không có quyền, đọc dữ liệu từ asset thành entity
                foreach (var unauthorizedId in unauthorizedAdvertiserIds.Distinct())
                {
                    var asset = activeAssets.FirstOrDefault(x => x.AssetId == unauthorizedId);
                    if (asset == null)
                    {
                        _logger.LogWarning("Không tìm thấy Asset cho tài khoản không có quyền: {AdvertiserId}", unauthorizedId);
                        continue;
                    }

                    var syncEntity = RawAdAccountEntity.CreateFromAsset(asset, bcId);
                    syncEntities.Add(syncEntity);
                    _logger.LogDebug("Đã map tài khoản không có quyền từ Asset: {AdvertiserId}", unauthorizedId);
                }

                // ===== BƯỚC 2: DUYỆT DANH SÁCH MAP VÀ XỬ LÝ DATABASE =====

                // 2.1. Lấy danh sách AdAccount hiện tại trong DB cho BC này
                var existingAdAccounts = await _adAccountRepository.GetByOwnerBcIdAsync(bcId, true);
                var existingAdvertiserIds = existingAdAccounts.Select(x => x.AdvertiserId).ToHashSet();

                // 2.2. Duyệt danh sách map và xử lý database
                var newEntities = new List<RawAdAccountEntity>();
                var updatedEntities = new List<RawAdAccountEntity>();

                foreach (var syncEntity in syncEntities)
                {
                    var existingEntity = existingAdAccounts.FirstOrDefault(x => x.AdvertiserId == syncEntity.AdvertiserId);

                    if (existingEntity == null)
                    {
                        // Chưa có: Thêm mới
                        newEntities.Add(syncEntity);
                        result.NewRecords++;
                    }
                    else
                    {
                        // Đã có: Kiểm tra thay đổi => cập nhật
                        if (existingEntity.HasIsChanged(syncEntity))
                        {
                            existingEntity.UpdateFrom(syncEntity);
                            existingEntity.IsRemoved = false;
                            existingEntity.RemovedAt = null;
                            updatedEntities.Add(existingEntity);
                            result.UpdatedRecords++;
                        }
                    }
                    result.TotalSynced++;
                }

                // 2.3. Xử lý các AdAccount đã bị xóa (dựa trên Asset có IsRemoved = true)
                var removedAdvertiserIds = removedAssets.Select(x => x.AssetId).ToList();
                foreach (var removedAdvertiserId in removedAdvertiserIds)
                {
                    var removedEntity = existingAdAccounts.FirstOrDefault(x => x.AdvertiserId == removedAdvertiserId);
                    if (removedEntity != null && !removedEntity.IsRemoved)
                    {
                        // Lấy thông tin thời gian xóa từ Asset
                        var removedAsset = removedAssets.FirstOrDefault(x => x.AssetId == removedAdvertiserId);
                        removedEntity.IsRemoved = true;
                        removedEntity.RemovedAt = removedAsset?.RemovedAt ?? DateTime.UtcNow;
                        updatedEntities.Add(removedEntity);
                        result.UpdatedRecords++;
                    }
                }

                // 2.4. Xử lý các AdAccount cần khôi phục (Asset đã được khôi phục từ IsRemoved = true thành false)
                var activeAdvertiserIds = activeAssets.Select(x => x.AssetId).ToHashSet();
                var existingRemovedAdAccounts = existingAdAccounts.Where(x => x.IsRemoved && activeAdvertiserIds.Contains(x.AdvertiserId)).ToList();
                foreach (var restoredEntity in existingRemovedAdAccounts)
                {
                    restoredEntity.IsRemoved = false;
                    restoredEntity.RemovedAt = null;
                    updatedEntities.Add(restoredEntity);
                    result.UpdatedRecords++;
                }

                // 2.5. Lưu vào database
                await _adAccountEntityRepository.InsertManyAsync(newEntities);
                await _adAccountEntityRepository.UpdateManyAsync(updatedEntities);

                _logger.LogDebug("Tạo mới {NewCount} tài khoản quảng cáo, cập nhật {UpdatedCount} tài khoản quảng cáo, đánh dấu xóa {RemovedCount} tài khoản quảng cáo, khôi phục {RestoredCount} tài khoản quảng cáo (dựa trên Asset IsRemoved)",
                    newEntities.Count, updatedEntities.Count, removedAdvertiserIds.Count, existingRemovedAdAccounts.Count);

                await uow.CompleteAsync();
            }

            result.AdAccountCount = activeAssets.Count;

            return result;
        }

        /// <summary>
        /// Đồng bộ tài khoản quảng cáo cụ thể từ API
        /// </summary>
        /// <param name="client">TikTok client</param>
        /// <param name="advertiserId">ID của tài khoản quảng cáo</param>
        /// <returns>Kết quả đồng bộ</returns>
        private async Task<AdAccountSyncResult> SyncSpecificAdAccountFromApiAsync(TikTokBusinessApiClient client, string advertiserId)
        {
            var result = new AdAccountSyncResult();

            // 1. Lấy chi tiết tài khoản quảng cáo cụ thể
            var advertiserIds = new List<string> { advertiserId };
            var adAccountDetailsResponse = await GetAdAccountDetailsWithRetryAsync(client, advertiserIds, advertiserId);

            if (!TikTokApiCodes.IsSuccess(adAccountDetailsResponse.Code))
            {
                throw new BusinessException(adAccountDetailsResponse.Code.ToString(), $"Không thể lấy được thông tin tài khoản: {adAccountDetailsResponse.Message}");
            }

            if (adAccountDetailsResponse.Data?.List == null || adAccountDetailsResponse.Data.List.Count == 0)
            {
                _logger.LogWarning("Không có chi tiết tài khoản quảng cáo nào được trả về cho: {AdvertiserId}", advertiserId);
                return result;
            }

            var apiAdAccount = adAccountDetailsResponse.Data.List.First();
            var existingAdAccount = await _adAccountRepository.GetByAdvertiserIdAsync(advertiserId);
            var bcId = existingAdAccount?.OwnerBcId ?? apiAdAccount.OwnerBcId;

            // 2. Đồng bộ tài khoản quảng cáo
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                await SyncAdAccountEntityAsync(apiAdAccount, bcId, result);
                await uow.CompleteAsync();
            }

            result.AdAccountCount = 1;
            return result;
        }

        /// <summary>
        /// Đồng bộ entity tài khoản quảng cáo
        /// </summary>
        /// <param name="apiAdAccount">Dữ liệu từ API</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task SyncAdAccountEntityAsync(AdAccountDetail apiAdAccount, string bcId, AdAccountSyncResult result)
        {
            if (string.IsNullOrEmpty(apiAdAccount.AdvertiserId))
            {
                _logger.LogWarning("Bỏ qua tài khoản quảng cáo - AdvertiserId rỗng");
                return;
            }

            var existingEntity = await _adAccountRepository.GetByAdvertiserIdAsync(apiAdAccount.AdvertiserId);
            // Tạo mới
            var newEntity = MapApiAdAccountToEntity(apiAdAccount, bcId);
            if (existingEntity == null)
            {
                await _adAccountEntityRepository.InsertAsync(newEntity);
                result.NewRecords++;
            }
            else
            {
                if (existingEntity.HasIsChanged(newEntity))
                {
                    // Cập nhật và đảm bảo cờ xóa được reset
                    existingEntity.UpdateFrom(newEntity);
                    existingEntity.IsRemoved = false;
                    existingEntity.RemovedAt = null;
                    await _adAccountEntityRepository.UpdateAsync(existingEntity);
                    result.UpdatedRecords++;
                }
            }
        }

        /// <summary>
        /// Map dữ liệu từ API sang entity
        /// </summary>
        /// <param name="apiAdAccount">Dữ liệu từ API</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Entity tài khoản quảng cáo</returns>
        private RawAdAccountEntity MapApiAdAccountToEntity(AdAccountDetail apiAdAccount, string bcId)
        {
            // Parse và convert CreateTime sang UTC
            var originalTimezone = apiAdAccount.Timezone ?? DateTimeService.UTC_TIMEZONE;
            var originalCreateTime = ParseDateTime(apiAdAccount.CreateTime) ?? DateTime.UtcNow;
            var utcCreateTime = _dateTimeService.ConvertToUtc(originalCreateTime, originalTimezone);

            return new RawAdAccountEntity(Guid.NewGuid())
            {
                AdvertiserId = apiAdAccount.AdvertiserId ?? string.Empty,
                OwnerBcId = apiAdAccount.OwnerBcId ?? bcId,
                Status = ParseAdAccountStatus(apiAdAccount.Status),
                Role = ParseAdAccountRole(apiAdAccount.Role),
                RejectionReason = apiAdAccount.RejectionReason,
                Name = apiAdAccount.Name ?? string.Empty,
                Timezone = originalTimezone, // Lưu timezone gốc để tham chiếu
                DisplayTimezone = apiAdAccount.DisplayTimezone,
                Company = apiAdAccount.Company,
                CompanyNameEditable = apiAdAccount.CompanyNameEditable ?? false,
                Industry = apiAdAccount.Industry,
                Address = apiAdAccount.Address,
                Country = apiAdAccount.Country ?? string.Empty,
                AdvertiserAccountType = ParseAdAccountType(apiAdAccount.AdvertiserAccountType),
                Currency = apiAdAccount.Currency ?? string.Empty,
                Contacter = apiAdAccount.Contacter,
                Email = apiAdAccount.Email,
                CellphoneNumber = apiAdAccount.CellphoneNumber,
                TelephoneNumber = apiAdAccount.TelephoneNumber,
                Language = apiAdAccount.Language ?? string.Empty,
                LicenseNo = apiAdAccount.LicenseNo,
                LicenseUrl = apiAdAccount.LicenseUrl,
                Description = apiAdAccount.Description,
                Balance = apiAdAccount.Balance ?? 0,
                CreateTime = utcCreateTime, // Lưu thời gian đã convert sang UTC
                IsRemoved = false,
                RemovedAt = null
            };
        }

        /// <summary>
        /// Parse trạng thái tài khoản quảng cáo
        /// </summary>
        /// <param name="status">Trạng thái từ API</param>
        /// <returns>Enum trạng thái</returns>
        private AdAccountStatus ParseAdAccountStatus(string? status)
        {
            if (string.IsNullOrEmpty(status))
                return AdAccountStatus.STATUS_ENABLE;

            if (Enum.TryParse<AdAccountStatus>(status, out var adAccountStatus))
                return adAccountStatus;

            return AdAccountStatus.STATUS_ENABLE;
        }

        /// <summary>
        /// Parse vai trò tài khoản quảng cáo
        /// </summary>
        /// <param name="role">Vai trò từ API</param>
        /// <returns>Enum vai trò</returns>
        private AdAccountRole ParseAdAccountRole(string? role)
        {
            if (string.IsNullOrEmpty(role))
                return AdAccountRole.ROLE_ADVERTISER;

            if (Enum.TryParse<AdAccountRole>(role, out var adAccountRole))
                return adAccountRole;

            return AdAccountRole.ROLE_ADVERTISER;
        }

        /// <summary>
        /// Parse loại tài khoản quảng cáo
        /// </summary>
        /// <param name="accountType">Loại tài khoản từ API</param>
        /// <returns>Enum loại tài khoản</returns>
        private AdAccountType ParseAdAccountType(string? accountType)
        {
            if (string.IsNullOrEmpty(accountType))
                return AdAccountType.AUCTION;

            if (Enum.TryParse<AdAccountType>(accountType, out var adAccountType))
                return adAccountType;

            return AdAccountType.AUCTION;
        }

        /// <summary>
        /// Lấy chi tiết tài khoản quảng cáo với xử lý retry khi gặp lỗi quyền truy cập
        /// </summary>
        /// <param name="client">TikTok client</param>
        /// <param name="advertiserIds">Danh sách advertiser IDs</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Response từ API</returns>
        private async Task<TikTokApiResponse<AdAccountDetailsResponse>> GetAdAccountDetailsWithRetryAsync(
            TikTokBusinessApiClient client,
            List<string> advertiserIds,
            string bcId)
        {
            var adAccountDetailsResponse = await client.AdAccount.GetAdAccountDetailsAsync(advertiserIds);

            // Kiểm tra nếu có lỗi quyền truy cập (code 40001)
            if (AdAccountSyncHelper.IsPermissionError(adAccountDetailsResponse))
            {
                _logger.LogWarning("Gặp lỗi quyền truy cập khi lấy chi tiết tài khoản quảng cáo cho BC: {BcId}. Message: {Message}",
                    bcId, adAccountDetailsResponse.Message);

                // Parse danh sách tài khoản không có quyền từ message
                var unauthorizedAdvertiserIds = AdAccountSyncHelper.ParseUnauthorizedAdvertiserIds(adAccountDetailsResponse.Message);

                if (unauthorizedAdvertiserIds.Any())
                {
                    _logger.LogDebug("Tìm thấy {UnauthorizedCount} tài khoản không có quyền: {UnauthorizedIds}. Sẽ thử lại với các tài khoản còn lại.",
                        unauthorizedAdvertiserIds.Count, string.Join(", ", unauthorizedAdvertiserIds));

                    // Loại bỏ các tài khoản không có quyền và thử lại
                    var authorizedAdvertiserIds = AdAccountSyncHelper.GetAuthorizedAdvertiserIds(advertiserIds, unauthorizedAdvertiserIds);

                    if (authorizedAdvertiserIds.Any())
                    {
                        _logger.LogDebug("Thử lại với {AuthorizedCount} tài khoản có quyền: {AuthorizedIds}",
                            authorizedAdvertiserIds.Count, string.Join(", ", authorizedAdvertiserIds));

                        adAccountDetailsResponse = await client.AdAccount.GetAdAccountDetailsAsync(authorizedAdvertiserIds);

                        if (TikTokApiCodes.IsSuccess(adAccountDetailsResponse.Code))
                        {
                            _logger.LogDebug("Thành công lấy chi tiết cho {SuccessCount} tài khoản có quyền",
                                adAccountDetailsResponse.Data?.List?.Count ?? 0);
                        }
                        else
                        {
                            _logger.LogError("Vẫn gặp lỗi sau khi loại bỏ tài khoản không có quyền. Code: {Code}, Message: {Message}",
                                adAccountDetailsResponse.Code, adAccountDetailsResponse.Message);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("Tất cả tài khoản trong batch đều không có quyền truy cập");
                    }
                }
                else
                {
                    _logger.LogWarning("Không thể parse được danh sách tài khoản không có quyền từ message: {Message}",
                        adAccountDetailsResponse.Message);
                }
            }

            return adAccountDetailsResponse;
        }

        /// <summary>
        /// Kiểm tra xem tài khoản quảng cáo có thay đổi so với Asset không
        /// Chỉ so sánh các trường có trong Asset, không so sánh các trường chi tiết từ API
        /// </summary>
        /// <param name="adAccount">Tài khoản quảng cáo hiện tại</param>
        /// <param name="asset">Asset để so sánh</param>
        /// <returns>True nếu có thay đổi, False nếu không có thay đổi</returns>
        private bool HasAssetChanged(RawAdAccountEntity adAccount, RawAssetEntity asset)
        {
            if (asset == null) return false;

            // So sánh các trường có trong Asset
            return adAccount.AdvertiserId != asset.AssetId ||
                   adAccount.OwnerBcId != asset.BcId ||
                   adAccount.Name != asset.AssetName ||
                   adAccount.AdvertiserAccountType != (asset.AdvertiserAccountType ?? AdAccountType.AUCTION) ||
                   adAccount.Status != (asset.AdvertiserStatus ?? AdAccountStatus.STATUS_ENABLE) ||
                   adAccount.IsRemoved != asset.IsRemoved ||
                   adAccount.RemovedAt != asset.RemovedAt;
        }
    }
}