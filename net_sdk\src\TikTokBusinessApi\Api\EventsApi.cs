/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Events operations
    /// </summary>
    public class EventsApi : IEventsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<EventsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the EventsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public EventsApi(IApiClient apiClient, ILogger<EventsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        #region App Events

        /// <summary>
        /// Report an app event to TikTok
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">App event request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<object>> ReportAppEventAsync(
        //    string accessToken,
        //    AppEventRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Reporting app event for TikTok App ID {TikTokAppId}", request.TikTokAppId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{EventsEndpoints.ReportAppEvent}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Report app events in bulk to TikTok
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">App events batch request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response with batch operation results</returns>
        //public async Task<Response<EventsBatchResponse>> ReportAppEventsBatchAsync(
        //    string accessToken,
        //    AppEventBatchRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Reporting {Count} app events in batch for TikTok App ID {TikTokAppId}", 
        //        request.Batch?.Count ?? 0, request.TikTokAppId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<EventsBatchResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{EventsEndpoints.ReportAppEventsBatch}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get information about an app
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="appId">App ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing app information</returns>
        public async Task<Response<AppInfoResponse>> GetAppInfoAsync(
            string accessToken,
            string advertiserId,
            string appId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrWhiteSpace(appId))
                throw new ArgumentException("App ID cannot be null or empty", nameof(appId));

            _logger?.LogInformation("Getting app info for advertiser {AdvertiserId} and app {AppId}", advertiserId, appId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["app_id"] = appId
            };

            return await _apiClient.CallApiAsync<Response<AppInfoResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{EventsEndpoints.GetAppInfo}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        #endregion

        #region Web Events

        /// <summary>
        /// Report a web event to TikTok
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Web event request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<object>> ReportWebEventAsync(
        //    string accessToken,
        //    WebEventRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Reporting web event {Event} for pixel {PixelCode}", request.Event, request.PixelCode);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{EventsEndpoints.ReportWebEvent}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Report web events in bulk to TikTok
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Web events batch request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response with batch operation results</returns>
        //public async Task<Response<EventsBatchResponse>> ReportWebEventsBatchAsync(
        //    string accessToken,
        //    WebEventBatchRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Reporting {Count} web events in batch for pixel {PixelCode}", 
        //        request.Batch?.Count ?? 0, request.PixelCode);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<EventsBatchResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{EventsEndpoints.ReportWebEventsBatch}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        #endregion

        #region Offline Events

        /// <summary>
        /// Create a new offline event set
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Create offline event set request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created event set ID</returns>
        //public async Task<Response<CreateOfflineEventSetResponse>> CreateOfflineEventSetAsync(
        //    string accessToken,
        //    CreateOfflineEventSetRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Creating offline event set {Name} for advertiser {AdvertiserId}",
        //        request.Name, request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<CreateOfflineEventSetResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{EventsEndpoints.CreateOfflineEventSet}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Update an existing offline event set
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Update offline event set request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<object>> UpdateOfflineEventSetAsync(
        //    string accessToken,
        //    UpdateOfflineEventSetRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Updating offline event set {EventSetId} for advertiser {AdvertiserId}",
        //        request.EventSetId, request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{EventsEndpoints.UpdateOfflineEventSet}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get offline event sets
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Get offline event sets request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing offline event sets</returns>
        public async Task<Response<GetOfflineEventSetsResponse>> GetOfflineEventSetsAsync(
            string accessToken,
            GetOfflineEventSetsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogInformation("Getting offline event sets for advertiser {AdvertiserId}", request.AdvertiserId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId
            };

            if (request.EventSetIds != null && request.EventSetIds.Count > 0)
            {
                queryParams["event_set_ids"] = string.Join(",", request.EventSetIds);
            }

            return await _apiClient.CallApiAsync<Response<GetOfflineEventSetsResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{EventsEndpoints.GetOfflineEventSets}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Delete an offline event set
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Delete offline event set request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<object>> DeleteOfflineEventSetAsync(
        //    string accessToken,
        //    DeleteOfflineEventSetRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Deleting offline event set {EventSetId} for advertiser {AdvertiserId}",
        //        request.EventSetId, request.AdvertiserId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{EventsEndpoints.DeleteOfflineEventSet}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Report offline events to TikTok
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Report offline events request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<object>> ReportOfflineEventsAsync(
        //    string accessToken,
        //    ReportOfflineEventsRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Reporting offline event {Event} for event set {EventSetId}",
        //        request.Event, request.EventSetId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{EventsEndpoints.ReportOfflineEvents}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Report offline events in bulk to TikTok
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Report offline events batch request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response with batch operation results</returns>
        //public async Task<Response<EventsBatchResponse>> ReportOfflineEventsBatchAsync(
        //    string accessToken,
        //    ReportOfflineEventsBatchRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrWhiteSpace(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogInformation("Reporting {Count} offline events in batch for event set {EventSetId}",
        //        request.Batch?.Count ?? 0, request.EventSetId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<EventsBatchResponse>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{EventsEndpoints.ReportOfflineEventsBatch}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        #endregion
    }
}
