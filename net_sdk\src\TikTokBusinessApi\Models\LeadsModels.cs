/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for creating a test lead
    /// </summary>
    public class CreateTestLeadRequest
    {
        /// <summary>
        /// The source of the lead. Default value: INSTANT_FORM
        /// </summary>
        [JsonPropertyName("lead_source")]
        public string? LeadSource { get; set; }

        /// <summary>
        /// Required when you want to create a test lead under an ad account. Advertiser ID.
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Required when you want to create a test lead under a form library of a Business Center. ID of a form library.
        /// </summary>
        [JsonPropertyName("library_id")]
        public string? LibraryId { get; set; }

        /// <summary>
        /// Required when lead_source is INSTANT_FORM or not specified. The page ID of an Instant Form.
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }
    }

    /// <summary>
    /// Response for creating a test lead
    /// </summary>
    public class CreateTestLeadResponse
    {
        /// <summary>
        /// The lead data
        /// </summary>
        [JsonPropertyName("lead_data")]
        public Dictionary<string, object>? LeadData { get; set; }

        /// <summary>
        /// Metadata about the lead
        /// </summary>
        [JsonPropertyName("meta_data")]
        public TestLeadMetadata? MetaData { get; set; }
    }

    /// <summary>
    /// Request body for getting a test lead
    /// </summary>
    public class GetTestLeadRequest
    {
        /// <summary>
        /// The source of the lead. Default value: INSTANT_FORM
        /// </summary>
        [JsonPropertyName("lead_source")]
        public string? LeadSource { get; set; }

        /// <summary>
        /// Required when the test lead is under an ad account. Advertiser ID.
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Required when the test lead is under a form library of a Business Center. ID of a form library.
        /// </summary>
        [JsonPropertyName("library_id")]
        public string? LibraryId { get; set; }

        /// <summary>
        /// Required when lead_source is INSTANT_FORM or not specified. The page ID of an Instant Form.
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }
    }

    /// <summary>
    /// Response for getting a test lead
    /// </summary>
    public class GetTestLeadResponse
    {
        /// <summary>
        /// The lead data
        /// </summary>
        [JsonPropertyName("lead_data")]
        public Dictionary<string, object>? LeadData { get; set; }

        /// <summary>
        /// Metadata about the lead
        /// </summary>
        [JsonPropertyName("meta_data")]
        public TestLeadMetadata? MetaData { get; set; }
    }

    /// <summary>
    /// Request body for deleting a test lead
    /// </summary>
    public class DeleteTestLeadRequest
    {
        /// <summary>
        /// Required when the test lead is under an ad account. Advertiser ID.
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Required when the test lead is under a form library of a Business Center. ID of a form library.
        /// </summary>
        [JsonPropertyName("library_id")]
        public string? LibraryId { get; set; }

        /// <summary>
        /// The ID of the test lead that you want to delete.
        /// </summary>
        [JsonPropertyName("lead_id")]
        public string LeadId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Metadata about a test lead
    /// </summary>
    public class TestLeadMetadata
    {
        /// <summary>
        /// The source of the lead
        /// </summary>
        [JsonPropertyName("lead_source")]
        public string? LeadSource { get; set; }

        /// <summary>
        /// The lead ID
        /// </summary>
        [JsonPropertyName("lead_id")]
        public string? LeadId { get; set; }

        /// <summary>
        /// The page ID of the Instant Form
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }

        /// <summary>
        /// The campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// The campaign name
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// The ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdGroupId { get; set; }

        /// <summary>
        /// The ad group name
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdGroupName { get; set; }

        /// <summary>
        /// The ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// The ad name
        /// </summary>
        [JsonPropertyName("ad_name")]
        public string? AdName { get; set; }

        /// <summary>
        /// The creation time of the test lead
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }
    }

    /// <summary>
    /// Request body for creating a lead download task
    /// </summary>
    public class CreateLeadDownloadTaskRequest
    {
        /// <summary>
        /// Either advertiser_id or library_id has to be set. Advertiser ID.
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Either advertiser_id or library_id has to be set. Form library ID.
        /// </summary>
        [JsonPropertyName("library_id")]
        public string? LibraryId { get; set; }

        /// <summary>
        /// Either ad_id or page_id has to be set. Ad ID.
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// Either ad_id or page_id has to be set. Instant form ID.
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }

        /// <summary>
        /// Task ID. If not passed, creates a new task. If passed, polls the task status.
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }
    }

    /// <summary>
    /// Response for creating a lead download task
    /// </summary>
    public class CreateLeadDownloadTaskResponse
    {
        /// <summary>
        /// Task status
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }

        /// <summary>
        /// File name of the lead file (returned only when status is SUCCEED)
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }

        /// <summary>
        /// File type of the lead file (returned only when status is SUCCEED)
        /// </summary>
        [JsonPropertyName("file_type")]
        public string? FileType { get; set; }
    }

    /// <summary>
    /// Request for downloading leads
    /// </summary>
    public class DownloadLeadsRequest
    {
        /// <summary>
        /// If leads are under an ad account, you must specify the advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// If leads are under a Business Center, you must specify the ID of the form library
        /// </summary>
        [JsonPropertyName("library_id")]
        public string? LibraryId { get; set; }

        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string TaskId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for getting form libraries
    /// </summary>
    public class GetFormLibrariesRequest
    {
        /// <summary>
        /// Number of pages in response
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Number of records in each page
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Response for getting form libraries
    /// </summary>
    public class GetFormLibrariesResponse
    {
        /// <summary>
        /// List of form libraries
        /// </summary>
        [JsonPropertyName("list")]
        public List<FormLibrary>? List { get; set; }
    }

    /// <summary>
    /// Form library information
    /// </summary>
    public class FormLibrary
    {
        /// <summary>
        /// Form library ID
        /// </summary>
        [JsonPropertyName("library_id")]
        public string? LibraryId { get; set; }

        /// <summary>
        /// Form library name
        /// </summary>
        [JsonPropertyName("library_name")]
        public string? LibraryName { get; set; }

        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Time when the library is created
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Time when the library is updated
        /// </summary>
        [JsonPropertyName("update_time")]
        public string? UpdateTime { get; set; }
    }

    /// <summary>
    /// Request body for migrating leads to a Business Center
    /// </summary>
    public class MigrateLeadsToBCRequest
    {
        /// <summary>
        /// ID of the advertiser that owns the leads
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// ID of the Business Center that you want to migrate leads to
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for migrating leads to a Business Center
    /// </summary>
    public class MigrateLeadsToBCResponse
    {
        /// <summary>
        /// ID of the form library that was created to hold the leads for the ad account
        /// </summary>
        [JsonPropertyName("library_id")]
        public string? LibraryId { get; set; }
    }

    /// <summary>
    /// Request for getting the fields of an Instant Form
    /// </summary>
    public class GetInstantFormFieldsRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the Instant Form
        /// </summary>
        [JsonPropertyName("page_id")]
        public string PageId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting the fields of an Instant Form
    /// </summary>
    public class GetInstantFormFieldsResponse
    {
        /// <summary>
        /// The Instant Form fields
        /// </summary>
        [JsonPropertyName("fields")]
        public List<string>? Fields { get; set; }

        /// <summary>
        /// Metadata about the Instant Form
        /// </summary>
        [JsonPropertyName("meta_data")]
        public InstantFormMetadata? MetaData { get; set; }
    }

    /// <summary>
    /// Metadata about an Instant Form
    /// </summary>
    public class InstantFormMetadata
    {
        /// <summary>
        /// The Instant Form ID
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }

        /// <summary>
        /// The creation time of the Instant Form
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Page title
        /// </summary>
        [JsonPropertyName("page_name")]
        public string? PageName { get; set; }

        /// <summary>
        /// Instant Form URL
        /// </summary>
        [JsonPropertyName("page_url")]
        public string? PageUrl { get; set; }
    }

    /// <summary>
    /// Request for getting fields of an Instant Form or direct message leads
    /// </summary>
    public class GetLeadFieldsRequest
    {
        /// <summary>
        /// The source of the leads
        /// </summary>
        [JsonPropertyName("lead_source")]
        public string LeadSource { get; set; } = string.Empty;

        /// <summary>
        /// Required when the leads are under an ad account. Advertiser ID.
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Required when the leads are under a form library of a Business Center. ID of a form library.
        /// </summary>
        [JsonPropertyName("library_id")]
        public string? LibraryId { get; set; }

        /// <summary>
        /// Required when lead_source is INSTANT_FORM. The page ID of an Instant Form.
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }
    }

    /// <summary>
    /// Response for getting fields of an Instant Form or direct message leads
    /// </summary>
    public class GetLeadFieldsResponse
    {
        /// <summary>
        /// The fields of the Instant Form or direct message leads
        /// </summary>
        [JsonPropertyName("fields")]
        public List<string>? Fields { get; set; }

        /// <summary>
        /// Metadata about the Instant Form or direct message leads
        /// </summary>
        [JsonPropertyName("meta_data")]
        public LeadFieldsMetadata? MetaData { get; set; }
    }

    /// <summary>
    /// Metadata about lead fields
    /// </summary>
    public class LeadFieldsMetadata
    {
        /// <summary>
        /// The time when the Instant Form was created or when the direct message lead was collected
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// The page ID of the Instant Form
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }

        /// <summary>
        /// The page name of the Instant Form
        /// </summary>
        [JsonPropertyName("page_name")]
        public string? PageName { get; set; }

        /// <summary>
        /// The URL of the Instant Form
        /// </summary>
        [JsonPropertyName("page_url")]
        public string? PageUrl { get; set; }
    }

    /// <summary>
    /// Request for getting an Instant Form lead or a direct message lead
    /// </summary>
    public class GetLeadRequest
    {
        /// <summary>
        /// The source of the leads
        /// </summary>
        [JsonPropertyName("lead_source")]
        public string LeadSource { get; set; } = string.Empty;

        /// <summary>
        /// Required when the leads are under an ad account. Advertiser ID.
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Required when the leads are under a form library of a Business Center. ID of a form library.
        /// </summary>
        [JsonPropertyName("library_id")]
        public string? LibraryId { get; set; }

        /// <summary>
        /// Required when lead_source is INSTANT_FORM. The page ID of an Instant Form.
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }
    }

    /// <summary>
    /// Response for getting an Instant Form lead or a direct message lead
    /// </summary>
    public class GetLeadResponse
    {
        /// <summary>
        /// The lead data
        /// </summary>
        [JsonPropertyName("lead_data")]
        public Dictionary<string, object>? LeadData { get; set; }

        /// <summary>
        /// Metadata about the lead
        /// </summary>
        [JsonPropertyName("meta_data")]
        public LeadMetadata? MetaData { get; set; }
    }

    /// <summary>
    /// Metadata about a lead
    /// </summary>
    public class LeadMetadata
    {
        /// <summary>
        /// The source of the lead
        /// </summary>
        [JsonPropertyName("lead_source")]
        public string? LeadSource { get; set; }

        /// <summary>
        /// The lead ID
        /// </summary>
        [JsonPropertyName("lead_id")]
        public string? LeadId { get; set; }

        /// <summary>
        /// The page ID of the Instant Form
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }

        /// <summary>
        /// The campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// The campaign name
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// The ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdGroupId { get; set; }

        /// <summary>
        /// The ad group name
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdGroupName { get; set; }

        /// <summary>
        /// The ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// The ad name
        /// </summary>
        [JsonPropertyName("ad_name")]
        public string? AdName { get; set; }

        /// <summary>
        /// The time when the lead was created
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }
    }
}
