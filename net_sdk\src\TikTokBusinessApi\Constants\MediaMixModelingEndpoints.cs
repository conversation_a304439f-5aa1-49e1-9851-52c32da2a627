/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Media Mix Modeling endpoints
    /// </summary>
    public static class MediaMixModelingEndpoints
    {
        /// <summary>
        /// Create an MMM data request endpoint (v1.3)
        /// </summary>
        public const string CreateMmmDataRequest = "/mmm/api/create/";

        /// <summary>
        /// Check the status of an MMM data request endpoint (v1.3)
        /// </summary>
        public const string CheckMmmDataRequestStatus = "/mmm/api/check/";

        /// <summary>
        /// Obtain the download URL for MMM data endpoint (v1.3)
        /// </summary>
        public const string GetMmmDataDownloadUrl = "/mmm/api/download/";

        /// <summary>
        /// Get the MMM data request history endpoint (v1.3)
        /// </summary>
        public const string GetMmmDataRequestHistory = "/mmm/api/history/";
    }
}
