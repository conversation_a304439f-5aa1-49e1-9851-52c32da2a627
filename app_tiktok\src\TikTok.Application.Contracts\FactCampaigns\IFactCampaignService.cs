﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using TikTok.FactCampaigns.Dtos;

namespace TikTok.FactCampaigns
{
    public interface IFactCampaignService : IApplicationService
    {
        Task<GetFactCampaignDataResponse> GetListAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<FactCampaignDto>> GetListFactCampaignByAdvertiserIdAsync(string advertiserId);
        Task<FactCampaignSummary> GetSummaryAsync();
    }
}
