/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Catalog Video Templates operations
    /// </summary>
    public interface ICatalogVideoTemplatesApi
    {
        /// <summary>
        /// Get information about all catalog video packages, or a particular video package, under your Business Center
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting video packages</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing video package information</returns>
        Task<VideoPackageGetResponse> GetVideoPackagesAsync(
            string accessToken,
            VideoPackageGetRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a video package (Deprecated)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing video package creation parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created video package ID</returns>
        //Task<VideoPackageCreateResponse> CreateVideoPackageAsync(
        //    string accessToken,
        //    VideoPackageCreateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update the name of a video package (Deprecated)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing video package update parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<object> UpdateVideoPackageAsync(
        //    string accessToken,
        //    VideoPackageUpdateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a video package (Deprecated)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing video package deletion parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<object> DeleteVideoPackageAsync(
        //    string accessToken,
        //    VideoPackageDeleteBody body,
        //    CancellationToken cancellationToken = default);
    }
}
