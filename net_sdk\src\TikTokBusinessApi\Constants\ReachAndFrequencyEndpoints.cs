/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Reach and Frequency endpoints
    /// </summary>
    public static class ReachAndFrequencyEndpoints
    {
        /// <summary>
        /// Get inventory estimates for Reach & Frequency ads endpoint (v1.3)
        /// </summary>
        public const string GetInventoryEstimate = "/rf/inventory/estimate/";

        /// <summary>
        /// Create a Reach & Frequency ad group endpoint (v1.3)
        /// </summary>
        public const string CreateRFAdGroup = "/adgroup/rf/create/";

        /// <summary>
        /// Update Reach & Frequency ad groups endpoint (v1.3)
        /// </summary>
        public const string UpdateRFAdGroup = "/adgroup/rf/update/";

        /// <summary>
        /// Cancel the R&F ad order endpoint (v1.3)
        /// </summary>
        public const string CancelRFOrder = "/rf/order/cancel/";

        /// <summary>
        /// Get estimated info of R&F ad groups endpoint (v1.3)
        /// </summary>
        public const string GetRFEstimatedInfo = "/adgroup/rf/estimated/info/";

        /// <summary>
        /// Query contracts endpoint (v1.3)
        /// </summary>
        public const string QueryContracts = "/rf/contract/query/";

        /// <summary>
        /// Get R&F time zones endpoint (v1.3)
        /// </summary>
        public const string GetRFTimeZones = "/rf/delivery/timezone/";
    }
}
