using System;

namespace TikTok.DateTimes;

/// <summary>
/// DTO chứa thông tin DateTime và timezone tương ứng
/// </summary>
public class DateTimeWithTimezoneDto
{
    /// <summary>
    /// DateTime theo timezone
    /// </summary>
    public System.DateTime DateTime { get; set; }

    /// <summary>
    /// Timezone tương ứng
    /// </summary>
    public string Timezone { get; set; }

    /// <summary>
    /// Offset của timezone so với UTC
    /// </summary>
    public TimeSpan TimezoneOffset { get; set; }

    public DateTimeWithTimezoneDto(System.DateTime dateTime, string timezone, TimeSpan timezoneOffset)
    {
        DateTime = dateTime;
        Timezone = timezone;
        TimezoneOffset = timezoneOffset;
    }
} 