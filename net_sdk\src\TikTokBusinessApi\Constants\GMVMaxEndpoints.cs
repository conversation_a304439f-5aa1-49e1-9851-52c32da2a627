/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API GMV Max endpoints
    /// </summary>
    public static class GMVMaxEndpoints
    {
        /// <summary>
        /// Get GMV Max Campaigns endpoint (v1.3)
        /// </summary>
        public const string GetCampaigns = "/gmv_max/campaign/get/";

        /// <summary>
        /// Get the details of a GMV Max Campaign endpoint (v1.3)
        /// </summary>
        public const string GetCampaignInfo = "/campaign/gmv_max/info/";

        /// <summary>
        /// Create a GMV Max Campaign endpoint (v1.3)
        /// </summary>
        public const string CreateCampaign = "/campaign/gmv_max/create/";

        /// <summary>
        /// Update a GMV Max Campaign endpoint (v1.3)
        /// </summary>
        public const string UpdateCampaign = "/campaign/gmv_max/update/";

        /// <summary>
        /// Update the status of a GMV Max Campaign endpoint (v1.3)
        /// </summary>
        public const string UpdateCampaignStatus = "/campaign/gmv_max/status/update/";

        /// <summary>
        /// Get TikTok Shops for GMV Max Campaigns endpoint (v1.3)
        /// </summary>
        public const string GetStores = "/gmv_max/store/list/";

        /// <summary>
        /// Get the recommended GMV Max ROI target and budget endpoint (v1.3)
        /// </summary>
        public const string GetBidRecommendation = "/gmv_max/bid/recommend/";

        /// <summary>
        /// Get identities for GMV Max Campaigns endpoint (v1.3)
        /// </summary>
        public const string GetIdentities = "/gmv_max/identity/get/";

        /// <summary>
        /// Run a GMV Max Campaign report endpoint (v1.3)
        /// </summary>
        public const string GetReport = "/gmv_max/report/get/";

        /// <summary>
        /// Get videos for GMV Max Campaigns endpoint (v1.3)
        /// </summary>
        public const string GetVideos = "/gmv_max/video/get/";

        /// <summary>
        /// Get custom anchor video list for GMV Max Campaigns endpoint (v1.3)
        /// </summary>
        public const string GetCustomAnchorVideoList = "/gmv_max/custom_anchor_video_list/get/";

        /// <summary>
        /// Check TikTok Shop ad usage for GMV Max Campaigns endpoint (v1.3)
        /// </summary>
        public const string CheckShopAdUsage = "/gmv_max/store/shop_ad_usage_check/";

        /// <summary>
        /// Create exclusive authorization for GMV Max Campaigns endpoint (v1.3)
        /// </summary>
        public const string CreateExclusiveAuthorization = "/gmv_max/exclusive_authorization/create/";

        /// <summary>
        /// Get exclusive authorization for GMV Max Campaigns endpoint (v1.3)
        /// </summary>
        public const string GetExclusiveAuthorization = "/gmv_max/exclusive_authorization/get/";

        /// <summary>
        /// Update exclusive authorization for GMV Max Campaigns endpoint (v1.3)
        /// </summary>
        public const string UpdateExclusiveAuthorization = "/gmv_max/exclusive_authorization/update/";

        /// <summary>
        /// Delete exclusive authorization for GMV Max Campaigns endpoint (v1.3)
        /// </summary>
        public const string DeleteExclusiveAuthorization = "/gmv_max/exclusive_authorization/delete/";

        /// <summary>
        /// Get GMV Max Campaign performance data endpoint (v1.3)
        /// </summary>
        public const string GetCampaignPerformance = "/gmv_max/campaign/performance/get/";

        /// <summary>
        /// Get GMV Max Campaign insights endpoint (v1.3)
        /// </summary>
        public const string GetCampaignInsights = "/gmv_max/campaign/insights/get/";

        /// <summary>
        /// Get GMV Max Campaign product performance endpoint (v1.3)
        /// </summary>
        public const string GetProductPerformance = "/gmv_max/product/performance/get/";
    }
}
