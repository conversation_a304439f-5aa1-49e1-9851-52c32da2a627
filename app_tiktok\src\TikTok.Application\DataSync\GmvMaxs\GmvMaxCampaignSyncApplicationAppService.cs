using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu GMV Max Campaign
    /// </summary>
    public class GmvMaxCampaignSyncApplicationAppService : ApplicationService, IGmvMaxCampaignSyncApplicationAppService
    {
        private readonly IGmvMaxCampaignSyncService _gmvMaxCampaignSyncService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="gmvMaxCampaignSyncService">GMV Max Campaign sync service</param>
        public GmvMaxCampaignSyncApplicationAppService(IGmvMaxCampaignSyncService gmvMaxCampaignSyncService)
        {
            _gmvMaxCampaignSyncService = gmvMaxCampaignSyncService;
        }

        /// <summary>
        /// Đồng bộ GMV Max Campaign theo Advertiser ID và BC ID
        /// </summary>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxCampaignSyncResult> SyncGmvMaxCampaignsAsync(string advertiserId, string bcId)
        {
            return await _gmvMaxCampaignSyncService.SyncGmvMaxCampaignsAsync(advertiserId, bcId);
        }

        /// <summary>
        /// Đồng bộ nhiều GMV Max Campaign cho nhiều Advertiser
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserIds">Danh sách ID của Advertiser (null để đồng bộ tất cả)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxCampaignSyncResult> SyncManyGmvMaxCampaignsAsync(string bcId, List<string>? advertiserIds = null)
        {
            return await _gmvMaxCampaignSyncService.SyncManyGmvMaxCampaignsAsync(bcId, advertiserIds);
        }

        /// <summary>
        /// Đồng bộ tất cả GMV Max Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxCampaignSyncResult> SyncAllGmvMaxCampaignsAsync()
        {
            return await _gmvMaxCampaignSyncService.SyncAllGmvMaxCampaignsAsync();
        }
    }
} 