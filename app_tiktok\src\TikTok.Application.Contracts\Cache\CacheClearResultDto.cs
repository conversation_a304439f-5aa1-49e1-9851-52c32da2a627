using System.Collections.Generic;

namespace TikTok.Cache
{
    /// <summary>
    /// DTO cho kết quả clear cache của một service
    /// </summary>
    public class CacheClearResultDto
    {
        /// <summary>
        /// Tên cache service
        /// </summary>
        public string CacheName { get; set; } = string.Empty;

        /// <summary>
        /// Cache key cụ thể (nếu có)
        /// </summary>
        public string? CacheKey { get; set; }

        /// <summary>
        /// Kết quả thành công hay không
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Thông báo kết quả
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO cho kết quả clear tất cả cache
    /// </summary>
    public class ClearCacheResultDto
    {
        /// <summary>
        /// Tổng số cache services
        /// </summary>
        public int TotalCaches { get; set; }

        /// <summary>
        /// Số cache clear thành công
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// Số cache clear thất bại
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// Danh sách kết quả chi tiết
        /// </summary>
        public List<CacheClearResultDto> Results { get; set; } = new List<CacheClearResultDto>();
    }
}
