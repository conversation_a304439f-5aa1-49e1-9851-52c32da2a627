/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Catalog Products endpoints
    /// </summary>
    public static class CatalogProductsEndpoints
    {
        /// <summary>
        /// Upload products via a file URL endpoint (v1.3)
        /// </summary>
        public const string UploadProductsViaFile = "/catalog/product/file/";

        /// <summary>
        /// Upload products via JSON schema endpoint (v1.3)
        /// </summary>
        public const string UploadProductsViaJson = "/catalog/product/upload/";

        /// <summary>
        /// Update products endpoint (v1.3)
        /// </summary>
        public const string UpdateProducts = "/catalog/product/update/";

        /// <summary>
        /// Delete products endpoint (v1.3)
        /// </summary>
        public const string DeleteProducts = "/catalog/product/delete/";

        /// <summary>
        /// Get products endpoint (v1.3)
        /// </summary>
        public const string GetProducts = "/catalog/product/get/";

        /// <summary>
        /// Get product handling log endpoint (v1.3)
        /// </summary>
        public const string GetProductLog = "/catalog/product/log/";
    }
}
