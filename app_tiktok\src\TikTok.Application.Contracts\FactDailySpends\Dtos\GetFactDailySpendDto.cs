using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TikTok.FactDailySpends.Dtos
{
    public class GetFactDailySpendDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// ID nhà quảng cáo để lọc
        /// </summary>
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// ID Business Center để lọc
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// Ngày bắt đầu để lọc (UTC)
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Ng<PERSON>y kết thúc để lọc (UTC)
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// ID DimDate bắt đầu (YYYYMMDD)
        /// </summary>
        public int? StartDimDateId { get; set; }

        /// <summary>
        /// ID DimDate kết thúc (YYYYMMDD)
        /// </summary>
        public int? EndDimDateId { get; set; }

        /// <summary>
        /// ID tài khoản quảng cáo để lọc
        /// </summary>
        public Guid? DimAdAccountId { get; set; }

        /// <summary>
        /// ID Business Center để lọc
        /// </summary>
        public Guid? DimBusinessCenterId { get; set; }

        /// <summary>
        /// Tiền tệ để lọc
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Số tiền tối thiểu để lọc
        /// </summary>
        public decimal? MinAmount { get; set; }

        /// <summary>
        /// Số tiền tối đa để lọc
        /// </summary>
        public decimal? MaxAmount { get; set; }

        /// <summary>
        /// Từ khóa tìm kiếm trong tên nhà quảng cáo
        /// </summary>
        public string? SearchKeyword { get; set; }
    }
}