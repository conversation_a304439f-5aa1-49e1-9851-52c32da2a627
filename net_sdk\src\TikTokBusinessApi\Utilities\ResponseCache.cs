/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TikTokBusinessApi.Utilities
{
    /// <summary>
    /// Response caching utility for frequently accessed data
    /// </summary>
    public class ResponseCache : IDisposable
    {
        private readonly ConcurrentDictionary<string, CacheEntry> _cache;
        private readonly ILogger<ResponseCache>? _logger;
        private readonly Timer _cleanupTimer;
        private readonly ResponseCacheOptions _options;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the ResponseCache class
        /// </summary>
        /// <param name="options">Cache configuration options</param>
        /// <param name="logger">Logger instance (optional)</param>
        public ResponseCache(ResponseCacheOptions? options = null, ILogger<ResponseCache>? logger = null)
        {
            _cache = new ConcurrentDictionary<string, CacheEntry>();
            _logger = logger;
            _options = options ?? new ResponseCacheOptions();
            
            // Clean up expired entries periodically
            _cleanupTimer = new Timer(CleanupExpiredEntries, null, 
                _options.CleanupInterval, _options.CleanupInterval);
        }

        /// <summary>
        /// Get a cached response or execute the factory function if not cached
        /// </summary>
        /// <typeparam name="T">Type of the response</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="factory">Function to execute if not cached</param>
        /// <param name="ttl">Time to live for the cache entry (optional, uses default if not specified)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Cached or newly fetched response</returns>
        public async Task<T> GetOrSetAsync<T>(
            string key,
            Func<CancellationToken, Task<T>> factory,
            TimeSpan? ttl = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Cache key cannot be null or empty", nameof(key));
            if (factory == null)
                throw new ArgumentNullException(nameof(factory));

            var effectiveTtl = ttl ?? _options.DefaultTtl;
            
            // Try to get from cache first
            if (_cache.TryGetValue(key, out var entry) && !entry.IsExpired)
            {
                try
                {
                    var cachedValue = JsonSerializer.Deserialize<T>(entry.Data, _options.JsonOptions);
                    if (cachedValue != null)
                    {
                        entry.LastAccessed = DateTimeOffset.UtcNow;
                        entry.AccessCount++;
                        
                        _logger?.LogDebug("Cache hit for key: {Key}, TTL remaining: {TTL}", 
                            key, entry.ExpiresAt - DateTimeOffset.UtcNow);
                        
                        return cachedValue;
                    }
                }
                catch (JsonException ex)
                {
                    _logger?.LogWarning(ex, "Failed to deserialize cached value for key: {Key}", key);
                    // Remove corrupted entry and continue to fetch fresh data
                    _cache.TryRemove(key, out _);
                }
            }

            // Cache miss or expired - fetch fresh data
            _logger?.LogDebug("Cache miss for key: {Key}, fetching fresh data", key);
            
            var result = await factory(cancellationToken);
            
            // Cache the result
            try
            {
                var serializedData = JsonSerializer.Serialize(result, _options.JsonOptions);
                var newEntry = new CacheEntry
                {
                    Data = serializedData,
                    CreatedAt = DateTimeOffset.UtcNow,
                    ExpiresAt = DateTimeOffset.UtcNow.Add(effectiveTtl),
                    LastAccessed = DateTimeOffset.UtcNow,
                    AccessCount = 1,
                    Ttl = effectiveTtl
                };

                _cache.AddOrUpdate(key, newEntry, (_, _) => newEntry);
                
                _logger?.LogDebug("Cached response for key: {Key}, TTL: {TTL}", key, effectiveTtl);
            }
            catch (JsonException ex)
            {
                _logger?.LogWarning(ex, "Failed to serialize response for caching, key: {Key}", key);
                // Continue without caching if serialization fails
            }

            return result;
        }

        /// <summary>
        /// Get a cached response without executing a factory function
        /// </summary>
        /// <typeparam name="T">Type of the response</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached response or null if not found/expired</returns>
        public T? Get<T>(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Cache key cannot be null or empty", nameof(key));

            if (_cache.TryGetValue(key, out var entry) && !entry.IsExpired)
            {
                try
                {
                    var cachedValue = JsonSerializer.Deserialize<T>(entry.Data, _options.JsonOptions);
                    if (cachedValue != null)
                    {
                        entry.LastAccessed = DateTimeOffset.UtcNow;
                        entry.AccessCount++;
                        
                        _logger?.LogDebug("Cache hit for key: {Key}", key);
                        return cachedValue;
                    }
                }
                catch (JsonException ex)
                {
                    _logger?.LogWarning(ex, "Failed to deserialize cached value for key: {Key}", key);
                    _cache.TryRemove(key, out _);
                }
            }

            _logger?.LogDebug("Cache miss for key: {Key}", key);
            return default;
        }

        /// <summary>
        /// Set a value in the cache
        /// </summary>
        /// <typeparam name="T">Type of the value</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Value to cache</param>
        /// <param name="ttl">Time to live (optional, uses default if not specified)</param>
        public void Set<T>(string key, T value, TimeSpan? ttl = null)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Cache key cannot be null or empty", nameof(key));

            var effectiveTtl = ttl ?? _options.DefaultTtl;

            try
            {
                var serializedData = JsonSerializer.Serialize(value, _options.JsonOptions);
                var entry = new CacheEntry
                {
                    Data = serializedData,
                    CreatedAt = DateTimeOffset.UtcNow,
                    ExpiresAt = DateTimeOffset.UtcNow.Add(effectiveTtl),
                    LastAccessed = DateTimeOffset.UtcNow,
                    AccessCount = 0,
                    Ttl = effectiveTtl
                };

                _cache.AddOrUpdate(key, entry, (_, _) => entry);
                
                _logger?.LogDebug("Set cache entry for key: {Key}, TTL: {TTL}", key, effectiveTtl);
            }
            catch (JsonException ex)
            {
                _logger?.LogWarning(ex, "Failed to serialize value for caching, key: {Key}", key);
            }
        }

        /// <summary>
        /// Remove a specific cache entry
        /// </summary>
        /// <param name="key">Cache key to remove</param>
        /// <returns>True if the entry was removed, false if it didn't exist</returns>
        public bool Remove(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Cache key cannot be null or empty", nameof(key));

            var removed = _cache.TryRemove(key, out _);
            if (removed)
            {
                _logger?.LogDebug("Removed cache entry for key: {Key}", key);
            }
            return removed;
        }

        /// <summary>
        /// Clear all cache entries
        /// </summary>
        public void Clear()
        {
            var count = _cache.Count;
            _cache.Clear();
            _logger?.LogDebug("Cleared {Count} cache entries", count);
        }

        /// <summary>
        /// Get cache statistics
        /// </summary>
        /// <returns>Cache statistics</returns>
        public CacheStatistics GetStatistics()
        {
            var totalEntries = 0;
            var expiredEntries = 0;
            var totalAccessCount = 0L;
            var oldestEntry = DateTimeOffset.MaxValue;
            var newestEntry = DateTimeOffset.MinValue;

            foreach (var entry in _cache.Values)
            {
                totalEntries++;
                totalAccessCount += entry.AccessCount;

                if (entry.IsExpired)
                    expiredEntries++;

                if (entry.CreatedAt < oldestEntry)
                    oldestEntry = entry.CreatedAt;

                if (entry.CreatedAt > newestEntry)
                    newestEntry = entry.CreatedAt;
            }

            return new CacheStatistics
            {
                TotalEntries = totalEntries,
                ExpiredEntries = expiredEntries,
                ActiveEntries = totalEntries - expiredEntries,
                TotalAccessCount = totalAccessCount,
                OldestEntry = oldestEntry == DateTimeOffset.MaxValue ? null : oldestEntry,
                NewestEntry = newestEntry == DateTimeOffset.MinValue ? null : newestEntry
            };
        }

        private void CleanupExpiredEntries(object? state)
        {
            var expiredKeys = new List<string>();
            var now = DateTimeOffset.UtcNow;

            foreach (var kvp in _cache)
            {
                if (kvp.Value.IsExpired)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            foreach (var key in expiredKeys)
            {
                _cache.TryRemove(key, out _);
            }

            if (expiredKeys.Count > 0)
            {
                _logger?.LogDebug("Cleaned up {Count} expired cache entries", expiredKeys.Count);
            }
        }

        /// <summary>
        /// Dispose of the cache and clean up resources
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _cleanupTimer?.Dispose();
                _cache.Clear();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Configuration options for the response cache
    /// </summary>
    public class ResponseCacheOptions
    {
        /// <summary>
        /// Default time to live for cache entries
        /// </summary>
        public TimeSpan DefaultTtl { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// Interval for cleaning up expired entries
        /// </summary>
        public TimeSpan CleanupInterval { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// JSON serialization options
        /// </summary>
        public JsonSerializerOptions JsonOptions { get; set; } = new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    /// <summary>
    /// Cache entry internal representation
    /// </summary>
    internal class CacheEntry
    {
        public string Data { get; set; } = string.Empty;
        public DateTimeOffset CreatedAt { get; set; }
        public DateTimeOffset ExpiresAt { get; set; }
        public DateTimeOffset LastAccessed { get; set; }
        public long AccessCount { get; set; }
        public TimeSpan Ttl { get; set; }

        public bool IsExpired => DateTimeOffset.UtcNow > ExpiresAt;
    }

    /// <summary>
    /// Cache statistics information
    /// </summary>
    public class CacheStatistics
    {
        /// <summary>
        /// Total number of cache entries
        /// </summary>
        public int TotalEntries { get; set; }

        /// <summary>
        /// Number of expired entries
        /// </summary>
        public int ExpiredEntries { get; set; }

        /// <summary>
        /// Number of active (non-expired) entries
        /// </summary>
        public int ActiveEntries { get; set; }

        /// <summary>
        /// Total access count across all entries
        /// </summary>
        public long TotalAccessCount { get; set; }

        /// <summary>
        /// Creation time of the oldest entry
        /// </summary>
        public DateTimeOffset? OldestEntry { get; set; }

        /// <summary>
        /// Creation time of the newest entry
        /// </summary>
        public DateTimeOffset? NewestEntry { get; set; }
    }
}
