# RawReportIntegratedAdGroupEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawReportIntegratedAdGroups`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawReportIntegratedAdGroups`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho báo cáo tích hợp của AdGroup. Lưu trữ thông tin báo cáo chi tiêu theo giờ tổng hợp của ADGROUP, bao gồm các chỉ số hiệu suất quảng cáo, cài đặt nhóm quảng cáo, và các metrics TikTok Shop.

## Bảng dữ liệu: RawReportIntegratedAdGroup

| Tên Field                   | Kiểu dữ liệu | Bắt buộc | Độ dài | <PERSON>ô tả                                                                                                                          |
| --------------------------- | ------------ | -------- | ------ | ------------------------------------------------------------------------------------------------------------------------------ |
| Id                          | Guid         | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                                                                          |
| AdvertiserId                | string       | ✅       | 100    | ID tài khoản nhà quảng cáo - Được hỗ trợ ở cấp độ Nhà quảng cáo, Chiến dịch, Nhóm quảng cáo và Quảng cáo                       |
| AdvertiserName              | string       | ✅       | 255    | Tên tài khoản nhà quảng cáo                                                                                                    |
| CampaignId                  | string       | ✅       | 100    | ID chiến dịch - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo                                                               |
| CampaignName                | string       | ✅       | 255    | Tên chiến dịch                                                                                                                 |
| AdGroupId                   | string       | ✅       | 100    | ID nhóm quảng cáo - Được hỗ trợ ở cấp độ Quảng cáo                                                                             |
| AdGroupName                 | string       | ✅       | 255    | Tên nhóm quảng cáo - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo                                                          |
| Date                        | DateTime     | ✅       | -      | Ngày giờ tổng hợp báo cáo, thời gian UTC format yyyy-MM-dd HH:mm:ss (theo giờ)                                                 |
| Spend                       | decimal      | ✅       | 18,2   | Chi tiêu - Tổng chi phí quảng cáo của bạn                                                                                      |
| Impressions                 | long         | ✅       | -      | Lần hiển thị - Số lần quảng cáo của bạn được hiển thị                                                                          |
| Clicks                      | long         | ✅       | -      | Lần nhấp (đích đến) - Số lần nhấp từ quảng cáo của bạn đến một đích đến được chỉ định                                          |
| Ctr                         | decimal      | ✅       | 18,4   | CTR (đích đến) - Tỷ lệ phần trăm lần hiển thị dẫn đến một lần nhấp đích đến trên tổng số lần hiển thị                          |
| Cpm                         | decimal      | ✅       | 18,2   | CPM - Số tiền trung bình bạn chi cho 1.000 lần hiển thị                                                                        |
| Cpc                         | decimal      | ✅       | 18,2   | CPC (đích đến) - Chi phí trung bình cho mỗi lần nhấp đến một đích đến được chỉ định                                            |
| Conversion                  | long         | ✅       | -      | Chuyển đổi - Số lần quảng cáo của bạn dẫn đến sự kiện tối ưu hóa mà bạn đã chọn                                                |
| CostPerConversion           | decimal      | ✅       | 18,2   | Chi phí mỗi chuyển đổi - Số tiền trung bình chi cho một chuyển đổi                                                             |
| ConversionRateV2            | decimal      | ✅       | 18,4   | Tỷ lệ chuyển đổi (CVR) - Tỷ lệ phần trăm chuyển đổi bạn nhận được trên tổng số lần hiển thị trên quảng cáo của bạn             |
| Reach                       | long         | ✅       | -      | Tiếp cận - Số lượng người dùng duy nhất đã xem quảng cáo của bạn ít nhất một lần                                               |
| Frequency                   | decimal      | ✅       | 18,4   | Tần suất - Số lần trung bình mỗi người dùng xem quảng cáo của bạn trong một khoảng thời gian nhất định                         |
| OnsiteShoppingRoas          | decimal      | ✅       | 18,4   | ROAS (Shop) - Lợi tức trên chi phí quảng cáo (ROAS) từ tổng doanh thu TikTok Shop được ghi nhận cho quảng cáo của bạn          |
| TotalOnsiteShoppingValue    | decimal      | ✅       | 18,2   | Tổng doanh thu (Shop) - Tổng doanh thu của các đơn hàng Shop được ghi nhận cho quảng cáo của bạn                               |
| OnsiteShopping              | long         | ✅       | -      | Mua hàng (Shop) - Số lượng hành động gửi đơn hàng Shop được ghi nhận cho quảng cáo của bạn                                     |
| CostPerOnsiteShopping       | decimal      | ✅       | 18,2   | Chi phí mỗi lần mua hàng (Shop) - Chi phí trung bình cho mỗi lần mua hàng Shop được ghi nhận cho quảng cáo của bạn             |
| ValuePerOnsiteShopping      | decimal      | ✅       | 18,2   | Giá trị đơn hàng trung bình (Shop) - Giá trị đơn hàng trung bình cho mỗi lần mua hàng Shop được ghi nhận cho quảng cáo của bạn |
| OnsiteOnWebDetail           | long         | ✅       | -      | Lượt xem trang sản phẩm (Shop) - Số lượng lượt xem trang chi tiết sản phẩm Shop được ghi nhận cho quảng cáo của bạn            |
| OnsiteOnWebCart             | long         | ✅       | -      | Thêm vào giỏ hàng (Shop) - Số lượng hành động thêm vào giỏ hàng Shop được ghi nhận cho quảng cáo của bạn                       |
| OnsiteInitiateCheckoutCount | long         | ✅       | -      | Bắt đầu thanh toán (Shop) - Số lượng hành động bắt đầu thanh toán trong Shop được ghi nhận cho quảng cáo của bạn               |
| PlacementType               | string       | ❌       | 100    | Loại vị trí đặt quảng cáo - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo                                                   |
| Budget                      | string       | ❌       | 100    | Ngân sách nhóm quảng cáo - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo                                                    |
| SmartTarget                 | string       | ❌       | 100    | Mục tiêu tối ưu hóa - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo                                                         |
| BillingEvent                | string       | ❌       | 100    | Sự kiện Thanh toán - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo                                                          |
| BidStrategy                 | string       | ❌       | 100    | Chiến lược đấu giá - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo                                                          |
| Bid                         | string       | ❌       | 100    | Giá đấu - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo                                                                     |
| Currency                    | string       | ✅       | 10     | Tiền tệ - Mã tiền tệ, ví dụ: USD                                                                                               |
| BcId                        | string       | ✅       | 100    | Id Business Center                                                                                                             |
| CreationTime                | DateTime     | ✅       | -      | Thời gian tạo bản ghi (Audit)                                                                                                  |
| CreatorId                   | Guid?        | ❌       | -      | ID người tạo (Audit)                                                                                                           |
| LastModificationTime        | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                                                                                 |
| LastModifierId              | Guid?        | ❌       | -      | ID người sửa đổi cuối (Audit)                                                                                                  |

## Cấu trúc dữ liệu

### Thông tin định danh

-   **AdvertiserId**: ID duy nhất của tài khoản quảng cáo từ TikTok API
-   **CampaignId**: ID chiến dịch chứa nhóm quảng cáo
-   **AdGroupId**: ID duy nhất của nhóm quảng cáo
-   **BcId**: ID của Business Center sở hữu tài khoản quảng cáo
-   **Date**: Ngày tổng hợp báo cáo theo timezone của Business Center

### Thông tin nhóm quảng cáo

-   **AdvertiserName**: Tên hiển thị của tài khoản quảng cáo
-   **CampaignName**: Tên hiển thị của chiến dịch
-   **AdGroupName**: Tên hiển thị của nhóm quảng cáo

### Thông tin chi phí và hiệu suất cơ bản

-   **Spend**: Tổng chi phí quảng cáo đã chi
-   **Impressions**: Số lần quảng cáo được hiển thị
-   **Clicks**: Số lần nhấp vào quảng cáo
-   **Ctr**: Tỷ lệ nhấp (Click-Through Rate)
-   **Cpm**: Chi phí cho 1000 lần hiển thị
-   **Cpc**: Chi phí cho mỗi lần nhấp
-   **Conversion**: Số lần chuyển đổi
-   **CostPerConversion**: Chi phí cho mỗi lần chuyển đổi
-   **ConversionRateV2**: Tỷ lệ chuyển đổi (CVR)

### Chỉ số tiếp cận

-   **Reach**: Số lượng người dùng duy nhất đã xem quảng cáo
-   **Frequency**: Tần suất trung bình mỗi người dùng xem quảng cáo

### Chỉ số TikTok Shop

-   **OnsiteShoppingRoas**: ROAS từ doanh thu TikTok Shop
-   **TotalOnsiteShoppingValue**: Tổng doanh thu TikTok Shop
-   **OnsiteShopping**: Số lần mua hàng Shop
-   **CostPerOnsiteShopping**: Chi phí cho mỗi lần mua hàng Shop
-   **ValuePerOnsiteShopping**: Giá trị đơn hàng trung bình Shop
-   **OnsiteOnWebDetail**: Lượt xem trang sản phẩm Shop
-   **OnsiteOnWebCart**: Số lần thêm vào giỏ hàng Shop
-   **OnsiteInitiateCheckoutCount**: Số lần bắt đầu thanh toán Shop

### Cài đặt nhóm quảng cáo

-   **PlacementType**: Loại vị trí đặt quảng cáo
-   **Budget**: Ngân sách nhóm quảng cáo
-   **SmartTarget**: Mục tiêu tối ưu hóa
-   **BillingEvent**: Sự kiện thanh toán
-   **BidStrategy**: Chiến lược đấu giá
-   **Bid**: Giá đấu

## Mục đích sử dụng

-   Lưu trữ báo cáo tổng hợp hàng ngày cho từng nhóm quảng cáo
-   Theo dõi hiệu suất chi tiết của từng nhóm quảng cáo
-   Phân tích hiệu quả TikTok Shop và chuyển đổi
-   Đánh giá cài đặt và chiến lược nhóm quảng cáo
-   Cung cấp dữ liệu cho báo cáo chi tiết cấp nhóm quảng cáo
-   Hỗ trợ tối ưu hóa nhóm quảng cáo dựa trên metrics

## Mối quan hệ

-   **RawAdAccountEntity**: Liên kết với tài khoản quảng cáo (AdvertiserId)
-   **RawCampaignEntity**: Liên kết với chiến dịch (CampaignId)
-   **RawBusinessCenterEntity**: Liên kết với Business Center (BcId)
-   **RawReportIntegratedAdAccountEntity**: Báo cáo tổng hợp cấp tài khoản
-   **RawReportIntegratedCampaignEntity**: Báo cáo tổng hợp cấp chiến dịch
-   **RawReportIntegratedAdEntity**: Báo cáo chi tiết cấp quảng cáo

## Indexes

-   **Unique Index**: `(AdGroupId, Date)` - Đảm bảo không có báo cáo trùng lặp cho cùng một nhóm quảng cáo và ngày
-   **Index**: `AdvertiserId` - Tối ưu truy vấn theo tài khoản quảng cáo
-   **Index**: `CampaignId` - Tối ưu truy vấn theo chiến dịch
-   **Index**: `AdGroupId` - Tối ưu truy vấn theo nhóm quảng cáo
-   **Index**: `Date` - Tối ưu truy vấn theo ngày
-   **Index**: `Spend` - Tối ưu truy vấn theo chi phí
-   **Index**: `Impressions` - Tối ưu truy vấn theo số lần hiển thị
-   **Index**: `Clicks` - Tối ưu truy vấn theo số lần nhấp
-   **Index**: `Conversion` - Tối ưu truy vấn theo số lần chuyển đổi

## Lưu ý

-   Entity này kế thừa từ AuditedEntity để có đầy đủ thông tin audit
-   Tất cả các trường decimal được cấu hình với precision 18,2 hoặc 18,4 tùy theo yêu cầu
-   Date được lưu trữ theo timezone của Business Center
-   Báo cáo được tổng hợp hàng ngày và không có báo cáo trùng lặp
-   Entity này cung cấp cái nhìn chi tiết về hiệu suất quảng cáo ở cấp độ nhóm quảng cáo
-   Bao gồm các cài đặt và chiến lược của nhóm quảng cáo
-   AdGroupId là khóa ngoại quan trọng để liên kết với các entity khác
-   Các trường cài đặt nhóm quảng cáo có thể null để đảm bảo tính linh hoạt
