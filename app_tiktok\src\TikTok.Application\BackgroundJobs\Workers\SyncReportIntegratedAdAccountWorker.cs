using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;
using static TikTok.BackgroundJobs.Workers.SyncCampaignWorker;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Worker để đồng bộ báo cáo tích hợp AdAccount
    /// </summary>
    public class SyncReportIntegratedAdAccountWorker : WorkerJobBase
    {
        private readonly IReportIntegratedAdAccountSyncService _reportIntegratedAdAccountSyncService;

        public SyncReportIntegratedAdAccountWorker(
            ILogger<SyncReportIntegratedAdAccountWorker> logger,
            IServiceProvider serviceProvider,
            IReportIntegratedAdAccountSyncService reportIntegratedAdAccountSyncService)
            : base(logger, serviceProvider)
        {
            _reportIntegratedAdAccountSyncService = reportIntegratedAdAccountSyncService;
        }

        public override CommandType CommandType => CommandType.SyncReportIntegratedAdAccount;

        protected override async Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job)
        {
            // Parse parameters
            var parameters = ParseParameters(args.Parameters);

            // Sync Report Integrated AdAccount
            var responseSync = await _reportIntegratedAdAccountSyncService.SyncReportIntegratedAdAccountAsync(parameters.BcId);
            if (responseSync != null)
            {
                if (responseSync.IsSuccess)
                    return JobResult.Success(JsonConvert.SerializeObject(responseSync));
                else
                    return JobResult.Error(responseSync.ErrorMessage ?? string.Empty, JsonConvert.SerializeObject(responseSync));
            }
            else
            {
                return JobResult.Error("Lỗi đồng bộ báo cáo tích hợp AdAccount");
            }
        }
        /// <summary>
        /// Parse parameters từ JSON
        /// </summary>
        /// <param name="parametersJson">JSON parameters</param>
        /// <returns>Parameters object</returns>
        private DefaultParameters ParseParameters(string parametersJson)
        {
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<DefaultParameters>(parametersJson)
                    ?? new DefaultParameters();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse parameters, using defaults");
                return new DefaultParameters();
            }
        }

    }
} 