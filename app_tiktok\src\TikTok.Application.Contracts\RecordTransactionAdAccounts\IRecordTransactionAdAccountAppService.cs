using System;
using System.Threading.Tasks;
using TikTok.RecordTransactionAdAccounts;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.RecordTransactionAdAccounts
{
    /// <summary>
    /// Interface cho RecordTransactionAdAccount App Service
    /// </summary>
    public interface IRecordTransactionAdAccountAppService :
        ICrudAppService<
            RecordTransactionAdAccountDto,
            Guid,
            GetRecordTransactionAdAccountListDto,
            CreateRecordTransactionAdAccountDto,
            UpdateRecordTransactionAdAccountDto>
    {
        /// <summary>
        /// Lấy bản ghi giao dịch theo Advertiser ID và Date
        /// </summary>
        /// <param name="advertiserId">ID Tài khoản Quảng cáo</param>
        /// <param name="date">Thời gian giao dịch</param>
        /// <returns>Bản ghi giao dịch</returns>
        Task<RecordTransactionAdAccountDto> GetByAdvertiserIdAndDateAsync(string advertiserId, DateTime date);

        /// <summary>
        /// Lấy danh sách bản ghi giao dịch theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID Tài khoản Quảng cáo</param>
        /// <returns>Danh sách bản ghi giao dịch</returns>
        Task<PagedResultDto<RecordTransactionAdAccountDto>> GetByAdvertiserIdAsync(string advertiserId);
    }
}