using System;

namespace TikTok.DateTimes;

/// <summary>
/// DTO chứa thông tin DateRange và timezone tương ứng
/// </summary>
public class DateRangeWithTimezoneDto
{
    /// <summary>
    /// Thời gian bắt đầu theo timezone
    /// </summary>
    public System.DateTime StartDateTime { get; set; }

    /// <summary>
    /// Thời gian kết thúc theo timezone
    /// </summary>
    public System.DateTime EndDateTime { get; set; }

    /// <summary>
    /// Timezone tương ứng
    /// </summary>
    public string Timezone { get; set; }

    /// <summary>
    /// Offset của timezone so với UTC
    /// </summary>
    public TimeSpan TimezoneOffset { get; set; }

    public DateRangeWithTimezoneDto(System.DateTime startDateTime, System.DateTime endDateTime, string timezone, TimeSpan timezoneOffset)
    {
        StartDateTime = startDateTime;
        EndDateTime = endDateTime;
        Timezone = timezone;
        TimezoneOffset = timezoneOffset;
    }
} 