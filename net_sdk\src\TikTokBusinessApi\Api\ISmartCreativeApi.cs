/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Smart Creative operations
    /// </summary>
    public interface ISmartCreativeApi
    {
        /// <summary>
        /// Create Smart Creative ads by uploading necessary ad creatives to the library
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing Smart Creative ad information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created Smart Creative ad information</returns>
        // Task<Response<SmartCreativeCreateResponse>> CreateSmartCreativeAdsAsync(
        //     string accessToken,
        //     SmartCreativeCreateBody body,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get creative materials for Smart Creative ads, including call-to-actions, texts, ad names, images, or video materials
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="adGroupIds">A list of ad group IDs</param>
        /// <param name="excludeFieldTypesInResponse">The type of fields that you want to remove from the response</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing Smart Creative materials</returns>
        Task<Response<SmartCreativeGetResponse>> GetSmartCreativeMaterialsAsync(
            string accessToken,
            string advertiserId,
            List<string> adGroupIds,
            List<string>? excludeFieldTypesInResponse = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Modify Smart Creative ad creatives. You can modify call-to-action, ad names, images, and video materials
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing Smart Creative ad information to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated Smart Creative ad information</returns>
        // Task<Response<SmartCreativeCreateResponse>> UpdateSmartCreativeMaterialsAsync(
        //     string accessToken,
        //     SmartCreativeUpdateBody body,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Update the status of creative materials for Smart Creative ads, including ad texts, images, and video materials
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing material status update information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated material status information</returns>
        // Task<Response<SmartCreativeMaterialStatusUpdateResponse>> UpdateSmartCreativeMaterialStatusesAsync(
        //     string accessToken,
        //     SmartCreativeMaterialStatusUpdateBody body,
        //     CancellationToken cancellationToken = default);
    }
}
