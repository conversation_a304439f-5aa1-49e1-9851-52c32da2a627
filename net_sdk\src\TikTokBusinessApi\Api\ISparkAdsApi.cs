/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Spark Ads operations
    /// </summary>
    public interface ISparkAdsApi
    {
        /// <summary>
        /// Get info about a Spark Ad post
        /// Use this endpoint to get information about the Spark Ads post that you have been authorized to use as an ad.
        /// You need to pass in the authorization code from the post owner.
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request containing advertiser ID and authorization code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing Spark Ad post information</returns>
        Task<SparkAdInfoResponse> GetSparkAdInfoAsync(
            string accessToken,
            SparkAdInfoRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Apply an authorization code
        /// Use this endpoint to apply an authorization code. After you get the authorization code from the post owner,
        /// the authorization is not automatically in effect. You need to apply the code to connect the ad account with the Spark Ads post.
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request containing advertiser ID and authorization codes</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        //Task AuthorizeSparkAdAsync(
        //    string accessToken,
        //    SparkAdAuthorizeRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get Spark Ad posts
        /// Use this endpoint to get a list of Spark Ads posts that have been authorized to an ad account.
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request containing search criteria and pagination parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of Spark Ad posts</returns>
        Task<SparkAdPostsResponse> GetSparkAdPostsAsync(
            string accessToken,
            SparkAdPostsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Unbind a Spark Ad post
        /// Use this endpoint to unbind a Spark Ad post after the authorization has expired or has been revoked.
        /// To unbind a Spark Ad post is to remove the post from the list of authorized Spark Ad posts for the ad account.
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request containing advertiser ID and item ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        //Task UnbindSparkAdAsync(
        //    string accessToken,
        //    SparkAdUnbindRequest request,
        //    CancellationToken cancellationToken = default);
    }
}
