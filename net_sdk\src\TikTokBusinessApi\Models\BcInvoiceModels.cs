/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Get Invoices Models

    /// <summary>
    /// Request model for getting invoices of a Business Center
    /// </summary>
    public class GetInvoicesRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Invoice ID
        /// </summary>
        [JsonPropertyName("invoice_id")]
        public string? InvoiceId { get; set; }

        /// <summary>
        /// Invoice title
        /// </summary>
        [JsonPropertyName("invoice_title")]
        public string? InvoiceTitle { get; set; }

        /// <summary>
        /// Invoice types. Enum values: RECON (Reconciliation Invoice), CREDIT (Credit Invoice), AUTO_PAY (Automatic Payment Invoice)
        /// Note: AUTO_PAY cannot be used with other invoice types
        /// </summary>
        [JsonPropertyName("invoice_types")]
        public List<string> InvoiceTypes { get; set; } = new List<string>();

        /// <summary>
        /// Payment statuses of the invoices. Enum values: UNPAID, PAID, PARTIAL_PAID, NO_NEED (No need to pay)
        /// </summary>
        [JsonPropertyName("pay_statuses")]
        public List<string>? PayStatuses { get; set; }

        /// <summary>
        /// The start time for the invoice issuance, in the format of YYYY-MM-DD HH:MM:SS
        /// Example: 2024-01-01 00:00:00
        /// You must specify both start_time and end_time or neither
        /// </summary>
        [JsonPropertyName("start_time")]
        public string? StartTime { get; set; }

        /// <summary>
        /// The end time for the invoice issuance, in the format of YYYY-MM-DD HH:MM:SS
        /// Example: 2024-02-01 00:00:00
        /// You must specify both start_time and end_time or neither
        /// </summary>
        [JsonPropertyName("end_time")]
        public string? EndTime { get; set; }

        /// <summary>
        /// Page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size, in the range of 1-50. Default value: 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Associated invoice information
    /// </summary>
    public class AssociatedInvoice
    {
        /// <summary>
        /// Invoice ID
        /// </summary>
        [JsonPropertyName("invoice_id")]
        public string? InvoiceId { get; set; }

        /// <summary>
        /// Invoice serial number
        /// </summary>
        [JsonPropertyName("serial_number")]
        public string? SerialNumber { get; set; }
    }

    /// <summary>
    /// Invoice information
    /// </summary>
    public class InvoiceInfo
    {
        /// <summary>
        /// Invoice ID
        /// </summary>
        [JsonPropertyName("invoice_id")]
        public string? InvoiceId { get; set; }

        /// <summary>
        /// Invoice serial number
        /// </summary>
        [JsonPropertyName("serial_number")]
        public string? SerialNumber { get; set; }

        /// <summary>
        /// Contract subject ID
        /// </summary>
        [JsonPropertyName("subject_id")]
        public long? SubjectId { get; set; }

        /// <summary>
        /// Invoice issue date, in the format of YYYY-MM-DD
        /// </summary>
        [JsonPropertyName("send_date")]
        public string? SendDate { get; set; }

        /// <summary>
        /// Currency
        /// </summary>
        [JsonPropertyName("currency_code")]
        public string? CurrencyCode { get; set; }

        /// <summary>
        /// Invoice approval status. Enum values: CREATED, SUBMITTED, APPROVED, DELETED
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Invoice due date
        /// </summary>
        [JsonPropertyName("due_date")]
        public string? DueDate { get; set; }

        /// <summary>
        /// Invoice type. Enum values: RECON (Reconciliation Invoice), CREDIT (Credit Invoice), AUTO_PAY (Automatic Payment Invoice)
        /// </summary>
        [JsonPropertyName("invoice_type")]
        public string? InvoiceType { get; set; }

        /// <summary>
        /// Entity serial numbers associated with the invoice
        /// </summary>
        [JsonPropertyName("entity_serials")]
        public List<string>? EntitySerials { get; set; }

        /// <summary>
        /// ID and serial number of the associated negative invoices that has been approved, returned when the current invoice is a positive invoice and has been reversed
        /// </summary>
        [JsonPropertyName("negative_invoice_ids")]
        public List<AssociatedInvoice>? NegativeInvoiceIds { get; set; }

        /// <summary>
        /// ID and serial number of the associated positive invoices, returned when the current invoice is a negative invoice
        /// </summary>
        [JsonPropertyName("positive_invoice_ids")]
        public List<AssociatedInvoice>? PositiveInvoiceIds { get; set; }

        /// <summary>
        /// Billing Group ID of the invoice
        /// </summary>
        [JsonPropertyName("billing_group_id")]
        public string? BillingGroupId { get; set; }

        /// <summary>
        /// Account name
        /// </summary>
        [JsonPropertyName("account_name")]
        public string? AccountName { get; set; }

        /// <summary>
        /// Invoice title
        /// </summary>
        [JsonPropertyName("invoice_title")]
        public string? InvoiceTitle { get; set; }

        /// <summary>
        /// Location code
        /// </summary>
        [JsonPropertyName("country_code")]
        public string? CountryCode { get; set; }

        /// <summary>
        /// Account billing address
        /// </summary>
        [JsonPropertyName("address")]
        public string? Address { get; set; }

        /// <summary>
        /// A list of email addresses from invoice for billing
        /// </summary>
        [JsonPropertyName("emails")]
        public List<string>? Emails { get; set; }

        /// <summary>
        /// ID number of the first tax type of the invoice
        /// </summary>
        [JsonPropertyName("tax_id1")]
        public string? TaxId1 { get; set; }

        /// <summary>
        /// ID number of the second tax type of the invoice
        /// </summary>
        [JsonPropertyName("tax_id2")]
        public string? TaxId2 { get; set; }

        /// <summary>
        /// Total amount, including tax
        /// </summary>
        [JsonPropertyName("amount")]
        public decimal? Amount { get; set; }

        /// <summary>
        /// Total amount, excluding tax
        /// </summary>
        [JsonPropertyName("amount_excluding_tax")]
        public decimal? AmountExcludingTax { get; set; }

        /// <summary>
        /// Total tax amount, in the same currency of the invoice
        /// </summary>
        [JsonPropertyName("total_tax_amount")]
        public decimal? TotalTaxAmount { get; set; }

        /// <summary>
        /// The first tax type of the invoice
        /// </summary>
        [JsonPropertyName("tax_type1")]
        public string? TaxType1 { get; set; }

        /// <summary>
        /// Rate of the first tax type
        /// </summary>
        [JsonPropertyName("tax_rate1")]
        public decimal? TaxRate1 { get; set; }

        /// <summary>
        /// Amount of the first tax type
        /// </summary>
        [JsonPropertyName("tax_amount1")]
        public decimal? TaxAmount1 { get; set; }

        /// <summary>
        /// The second tax type of the invoice
        /// </summary>
        [JsonPropertyName("tax_type2")]
        public string? TaxType2 { get; set; }

        /// <summary>
        /// Rate of the second tax type
        /// </summary>
        [JsonPropertyName("tax_rate2")]
        public decimal? TaxRate2 { get; set; }

        /// <summary>
        /// Amount of the second tax type
        /// </summary>
        [JsonPropertyName("tax_amount2")]
        public decimal? TaxAmount2 { get; set; }

        /// <summary>
        /// Payment statuses of the invoices
        /// </summary>
        [JsonPropertyName("pay_status")]
        public string? PayStatus { get; set; }

        /// <summary>
        /// Overdue status. Enum values: INITIALIZED, YES, NO
        /// </summary>
        [JsonPropertyName("overdue_status")]
        public string? OverdueStatus { get; set; }

        /// <summary>
        /// Unpaid amount
        /// </summary>
        [JsonPropertyName("unpaid_amount")]
        public decimal? UnpaidAmount { get; set; }
    }

    /// <summary>
    /// Response data for get invoices
    /// </summary>
    public class GetInvoicesData
    {
        /// <summary>
        /// List of invoices
        /// </summary>
        [JsonPropertyName("list")]
        public List<InvoiceInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Response model for get invoices
    /// </summary>
    public class GetInvoicesResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        public GetInvoicesData? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        public string? RequestId { get; set; }
    }

    #endregion

    #region Get Unpaid Amount Models

    /// <summary>
    /// Request model for getting unpaid amount of a Business Center
    /// </summary>
    public class GetUnpaidAmountRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Invoice type. Enum values: RECON (Reconciliation Invoice)
        /// </summary>
        [JsonPropertyName("invoice_type")]
        public string InvoiceType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Unpaid amount result
    /// </summary>
    public class UnpaidAmountResult
    {
        /// <summary>
        /// Currency code
        /// </summary>
        [JsonPropertyName("currency_code")]
        public string? CurrencyCode { get; set; }

        /// <summary>
        /// Total unpaid amount in related currency
        /// </summary>
        [JsonPropertyName("amount")]
        public decimal? Amount { get; set; }
    }

    /// <summary>
    /// Response data for get unpaid amount
    /// </summary>
    public class GetUnpaidAmountData
    {
        /// <summary>
        /// The result of the currency and the unpaid amount
        /// </summary>
        [JsonPropertyName("result")]
        public UnpaidAmountResult? Result { get; set; }
    }

    /// <summary>
    /// Response model for get unpaid amount
    /// </summary>
    public class GetUnpaidAmountResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        public GetUnpaidAmountData? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        public string? RequestId { get; set; }
    }

    #endregion

    #region Download Invoice Models

    /// <summary>
    /// Request model for downloading an individual invoice synchronously
    /// </summary>
    public class DownloadInvoiceRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Invoice ID
        /// </summary>
        [JsonPropertyName("invoice_id")]
        public string InvoiceId { get; set; } = string.Empty;
    }

    #endregion

    #region Create Download Task Models

    /// <summary>
    /// Filtering conditions for download task
    /// </summary>
    public class DownloadTaskFiltering
    {
        /// <summary>
        /// Invoice title
        /// </summary>
        [JsonPropertyName("invoice_title")]
        public string? InvoiceTitle { get; set; }

        /// <summary>
        /// Serial number
        /// </summary>
        [JsonPropertyName("serial_number")]
        public string? SerialNumber { get; set; }

        /// <summary>
        /// Billing Group ID
        /// </summary>
        [JsonPropertyName("billing_group_id")]
        public string? BillingGroupId { get; set; }

        /// <summary>
        /// Payment statuses of the invoices. Enum values: UNPAID, PAID, PARTIAL_PAID, NO_NEED
        /// </summary>
        [JsonPropertyName("pay_statuses")]
        public List<string>? PayStatuses { get; set; }

        /// <summary>
        /// Start date of the invoice send date, in the format of YYYY-MM-DD
        /// </summary>
        [JsonPropertyName("start_time")]
        public string? StartTime { get; set; }

        /// <summary>
        /// End date of the invoice send date, in the format of YYYY-MM-DD
        /// </summary>
        [JsonPropertyName("end_time")]
        public string? EndTime { get; set; }
    }

    /// <summary>
    /// Request model for creating an asynchronous download task
    /// </summary>
    public class CreateDownloadTaskRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Invoice ID. Required when download_type is BILLING_REPORT. For other download types, use this field to apply filtering
        /// </summary>
        [JsonPropertyName("invoice_id")]
        public string? InvoiceId { get; set; }

        /// <summary>
        /// Download type. Enum values: INVOICE_LIST (Invoice list in Excel format), INVOICE_BATCH (Invoice batch download in ZIP format), BILLING_REPORT (billing report in Excel format)
        /// </summary>
        [JsonPropertyName("download_type")]
        public string DownloadType { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public DownloadTaskFiltering? Filtering { get; set; }
    }

    /// <summary>
    /// Response data for create download task
    /// </summary>
    public class CreateDownloadTaskData
    {
        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }
    }

    /// <summary>
    /// Response model for create download task
    /// </summary>
    public class CreateDownloadTaskResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        public CreateDownloadTaskData? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        public string? RequestId { get; set; }
    }

    #endregion

    #region Get Download Task Models

    /// <summary>
    /// Request model for getting asynchronous download task (BILLING_REPORT)
    /// </summary>
    public class GetDownloadTaskRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string TaskId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response data for get download task
    /// </summary>
    public class GetDownloadTaskData
    {
        /// <summary>
        /// A url to download the task file
        /// </summary>
        [JsonPropertyName("download_url")]
        public string? DownloadUrl { get; set; }

        /// <summary>
        /// Asynchronous download task status. Enum values: FAILED, CREATED, RUNNING, SUCCEED, POLLING
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// If the action failed, this field returns the error message
        /// </summary>
        [JsonPropertyName("error_msg")]
        public string? ErrorMsg { get; set; }
    }

    /// <summary>
    /// Response model for get download task
    /// </summary>
    public class GetDownloadTaskResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        public GetDownloadTaskData? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        public string? RequestId { get; set; }
    }

    #endregion

    #region Get Download Task List Models

    /// <summary>
    /// Request model for getting asynchronous download task list (INVOICE_LIST and INVOICE_BATCH)
    /// </summary>
    public class GetDownloadTaskListRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size, in the range of 1-50. Default value: 10
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Download task information
    /// </summary>
    public class DownloadTaskInfo
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string? BcId { get; set; }

        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }

        /// <summary>
        /// Asynchronous download task status. Enum values: FAILED, CREATED, RUNNING, SUCCEED, POLLING
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Created time of the task
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Update time of the task
        /// </summary>
        [JsonPropertyName("update_time")]
        public string? UpdateTime { get; set; }

        /// <summary>
        /// If the task has been completed successfully, it will be a url to download the task file
        /// </summary>
        [JsonPropertyName("download_url")]
        public string? DownloadUrl { get; set; }

        /// <summary>
        /// Download type. Enum values: INVOICE_LIST, INVOICE_BATCH
        /// </summary>
        [JsonPropertyName("download_type")]
        public string? DownloadType { get; set; }
    }

    /// <summary>
    /// Response data for get download task list
    /// </summary>
    public class GetDownloadTaskListData
    {
        /// <summary>
        /// List of tasks
        /// </summary>
        [JsonPropertyName("list")]
        public List<DownloadTaskInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Response model for get download task list
    /// </summary>
    public class GetDownloadTaskListResponse
    {
        /// <summary>
        /// Response data
        /// </summary>
        public GetDownloadTaskListData? Data { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        public string? RequestId { get; set; }
    }

    #endregion
}
