/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Change Log operations
    /// </summary>
    public interface IChangeLogApi
    {
        /// <summary>
        /// Initiate a change log download task for an ad account based on the parameters that you specify
        /// </summary>
        /// <param name="request">Create change log task request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task ID</returns>
        //Task<CreateChangeLogTaskResponse> CreateTaskAsync(
        //    CreateChangeLogTaskRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Check whether a change log download task has completed or not
        /// </summary>
        /// <param name="request">Check change log task status request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing task status</returns>
        //Task<CheckChangeLogTaskResponse> CheckTaskStatusAsync(
        //    CheckChangeLogTaskRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Download the change log file
        /// </summary>
        /// <param name="request">Download change log task request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing change log file data</returns>
        Task<DownloadChangeLogTaskResponse> DownloadTaskAsync(
            DownloadChangeLogTaskRequest request,
            CancellationToken cancellationToken = default);
    }
}
