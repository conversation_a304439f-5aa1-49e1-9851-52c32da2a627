using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TikTok.AdGroups
{
    public class GetAdGroupListDto : PagedAndSortedResultRequestDto
    {
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        public List<string> AdvertiserIds { get; set; }

        [StringLength(100)]
        public string CampaignId { get; set; }

        public List<string> CampaignIds { get; set; }

        [StringLength(255)]
        public string CampaignName { get; set; }

        [StringLength(100)]
        public string AdgroupId { get; set; }

        public List<string> AdgroupIds { get; set; }

        [StringLength(255)]
        public string AdgroupName { get; set; }

        [StringLength(50)]
        public string CampaignSystemOrigin { get; set; }

        public List<string> CampaignSystemOrigins { get; set; }

        public bool? IsSmartPerformanceCampaign { get; set; }

        [StringLength(50)]
        public string ShoppingAdsType { get; set; }

        public List<string> ShoppingAdsTypes { get; set; }

        [StringLength(100)]
        public string IdentityId { get; set; }

        public List<string> IdentityIds { get; set; }

        [StringLength(50)]
        public string IdentityType { get; set; }

        public List<string> IdentityTypes { get; set; }

        [StringLength(100)]
        public string IdentityAuthorizedBcId { get; set; }

        [StringLength(50)]
        public string ProductSource { get; set; }

        public List<string> ProductSources { get; set; }

        [StringLength(100)]
        public string CatalogId { get; set; }

        public List<string> CatalogIds { get; set; }

        [StringLength(100)]
        public string CatalogAuthorizedBcId { get; set; }

        [StringLength(100)]
        public string StoreId { get; set; }

        public List<string> StoreIds { get; set; }

        [StringLength(100)]
        public string StoreAuthorizedBcId { get; set; }

        [StringLength(50)]
        public string PromotionType { get; set; }

        public List<string> PromotionTypes { get; set; }

        [StringLength(50)]
        public string PromotionTargetType { get; set; }

        public List<string> PromotionTargetTypes { get; set; }

        [StringLength(50)]
        public string MessagingAppType { get; set; }

        [StringLength(100)]
        public string MessagingAppAccountId { get; set; }

        [StringLength(50)]
        public string PromotionWebsiteType { get; set; }

        [StringLength(100)]
        public string AppId { get; set; }

        public List<string> AppIds { get; set; }

        [StringLength(50)]
        public string AppType { get; set; }

        public List<string> AppTypes { get; set; }

        [StringLength(100)]
        public string PixelId { get; set; }

        public List<string> PixelIds { get; set; }

        [StringLength(100)]
        public string OptimizationEvent { get; set; }

        public List<string> OptimizationEvents { get; set; }

        [StringLength(50)]
        public string DeepFunnelOptimizationStatus { get; set; }

        [StringLength(50)]
        public string DeepFunnelEventSource { get; set; }

        [StringLength(100)]
        public string DeepFunnelEventSourceId { get; set; }

        [StringLength(100)]
        public string DeepFunnelOptimizationEvent { get; set; }

        [StringLength(50)]
        public string PlacementType { get; set; }

        public List<string> PlacementTypes { get; set; }

        public List<string> Placements { get; set; }

        public bool? SearchResultEnabled { get; set; }

        public bool? AutomatedKeywordsEnabled { get; set; }

        public bool? CommentDisabled { get; set; }

        public bool? VideoDownloadDisabled { get; set; }

        public bool? ShareDisabled { get; set; }

        [StringLength(50)]
        public string AudienceType { get; set; }

        public List<string> AudienceTypes { get; set; }

        public bool? AutoTargetingEnabled { get; set; }

        [StringLength(50)]
        public string ShoppingAdsRetargetingType { get; set; }

        public int? ShoppingAdsRetargetingActionsDaysFrom { get; set; }

        public int? ShoppingAdsRetargetingActionsDaysTo { get; set; }

        [StringLength(50)]
        public string ShoppingAdsRetargetingCustomAudienceRelation { get; set; }

        public List<string> LocationIds { get; set; }

        public List<string> ZipcodeIds { get; set; }

        public List<string> Languages { get; set; }

        [StringLength(20)]
        public string Gender { get; set; }

        public List<string> Genders { get; set; }

        public List<string> AgeGroups { get; set; }

        [StringLength(50)]
        public string SpendingPower { get; set; }

        public List<string> SpendingPowers { get; set; }

        public List<string> HouseholdIncomes { get; set; }

        public List<string> AudienceIds { get; set; }

        public bool? SmartAudienceEnabled { get; set; }

        public List<string> ExcludedAudienceIds { get; set; }

        public List<string> InterestCategoryIds { get; set; }

        public List<string> InterestKeywordIds { get; set; }

        public List<string> PurchaseIntentionKeywordIds { get; set; }

        public bool? SmartInterestBehaviorEnabled { get; set; }

        public List<string> IncludedPangleAudiencePackageIds { get; set; }

        public List<string> ExcludedPangleAudiencePackageIds { get; set; }

        public List<string> OperatingSystems { get; set; }

        [StringLength(20)]
        public string MinAndroidVersion { get; set; }

        [StringLength(50)]
        public string Ios14Targeting { get; set; }

        [StringLength(20)]
        public string MinIosVersion { get; set; }

        [StringLength(50)]
        public string Ios14QuotaType { get; set; }

        public List<string> DeviceModelIds { get; set; }

        public List<string> NetworkTypes { get; set; }

        public List<string> CarrierIds { get; set; }

        public List<string> IspIds { get; set; }

        public List<int> DevicePriceRanges { get; set; }

        [StringLength(100)]
        public string SavedAudienceId { get; set; }

        public List<string> SavedAudienceIds { get; set; }

        public List<string> ContextualTagIds { get; set; }

        [StringLength(50)]
        public string BrandSafetyType { get; set; }

        public List<string> BrandSafetyTypes { get; set; }

        [StringLength(100)]
        public string BrandSafetyPartner { get; set; }

        public List<string> BrandSafetyPartners { get; set; }

        public bool? InventoryFilterEnabled { get; set; }

        public List<string> CategoryExclusionIds { get; set; }

        [StringLength(100)]
        public string VerticalSensitivityId { get; set; }

        public List<string> VerticalSensitivityIds { get; set; }

        [StringLength(50)]
        public string BudgetMode { get; set; }

        public List<string> BudgetModes { get; set; }

        public decimal? BudgetFrom { get; set; }

        public decimal? BudgetTo { get; set; }

        public decimal? ScheduledBudgetFrom { get; set; }

        public decimal? ScheduledBudgetTo { get; set; }

        [StringLength(50)]
        public string ScheduleType { get; set; }

        public List<string> ScheduleTypes { get; set; }

        public DateTime? ScheduleStartTimeFrom { get; set; }

        public DateTime? ScheduleStartTimeTo { get; set; }

        public DateTime? ScheduleEndTimeFrom { get; set; }

        public DateTime? ScheduleEndTimeTo { get; set; }

        [StringLength(100)]
        public string OptimizationGoal { get; set; }

        public List<string> OptimizationGoals { get; set; }

        [StringLength(100)]
        public string SecondaryOptimizationEvent { get; set; }

        public List<string> SecondaryOptimizationEvents { get; set; }

        [StringLength(100)]
        public string MessageEventSetId { get; set; }

        public List<string> MessageEventSetIds { get; set; }

        public int? FrequencyFrom { get; set; }

        public int? FrequencyTo { get; set; }

        public int? FrequencyScheduleFrom { get; set; }

        public int? FrequencyScheduleTo { get; set; }

        [StringLength(50)]
        public string BidType { get; set; }

        public List<string> BidTypes { get; set; }

        public decimal? BidPriceFrom { get; set; }

        public decimal? BidPriceTo { get; set; }

        public decimal? ConversionBidPriceFrom { get; set; }

        public decimal? ConversionBidPriceTo { get; set; }

        [StringLength(50)]
        public string DeepBidType { get; set; }

        public List<string> DeepBidTypes { get; set; }

        public decimal? RoasBidFrom { get; set; }

        public decimal? RoasBidTo { get; set; }

        [StringLength(50)]
        public string VboWindow { get; set; }

        public List<string> VboWindows { get; set; }

        [StringLength(50)]
        public string BidDisplayMode { get; set; }

        public List<string> BidDisplayModes { get; set; }

        public decimal? DeepCpaBidFrom { get; set; }

        public decimal? DeepCpaBidTo { get; set; }

        [StringLength(50)]
        public string CpvVideoDuration { get; set; }

        public List<string> CpvVideoDurations { get; set; }

        public decimal? NextDayRetentionFrom { get; set; }

        public decimal? NextDayRetentionTo { get; set; }

        [StringLength(50)]
        public string ClickAttributionWindow { get; set; }

        public List<string> ClickAttributionWindows { get; set; }

        [StringLength(50)]
        public string EngagedViewAttributionWindow { get; set; }

        public List<string> EngagedViewAttributionWindows { get; set; }

        [StringLength(50)]
        public string ViewAttributionWindow { get; set; }

        public List<string> ViewAttributionWindows { get; set; }

        [StringLength(50)]
        public string AttributionEventCount { get; set; }

        public List<string> AttributionEventCounts { get; set; }

        [StringLength(50)]
        public string BillingEvent { get; set; }

        public List<string> BillingEvents { get; set; }

        [StringLength(50)]
        public string Pacing { get; set; }

        public List<string> Pacings { get; set; }

        [StringLength(20)]
        public string OperationStatus { get; set; }

        public List<string> OperationStatuses { get; set; }

        [StringLength(100)]
        public string SecondaryStatus { get; set; }

        public List<string> SecondaryStatuses { get; set; }

        [StringLength(50)]
        public string StatisticType { get; set; }

        public List<string> StatisticTypes { get; set; }

        public bool? IsHfss { get; set; }

        [StringLength(50)]
        public string CreativeMaterialMode { get; set; }

        public List<string> CreativeMaterialModes { get; set; }

        [StringLength(50)]
        public string AdgroupAppProfilePageState { get; set; }

        public List<string> AdgroupAppProfilePageStates { get; set; }

        [StringLength(50)]
        public string FeedType { get; set; }

        public List<string> FeedTypes { get; set; }

        [StringLength(50)]
        public string RfPurchasedType { get; set; }

        public List<string> RfPurchasedTypes { get; set; }

        public long? PurchasedImpressionFrom { get; set; }

        public long? PurchasedImpressionTo { get; set; }

        public long? PurchasedReachFrom { get; set; }

        public long? PurchasedReachTo { get; set; }

        public decimal? RfEstimatedCprFrom { get; set; }

        public decimal? RfEstimatedCprTo { get; set; }

        public decimal? RfEstimatedFrequencyFrom { get; set; }

        public decimal? RfEstimatedFrequencyTo { get; set; }

        [StringLength(100)]
        public string SplitTestGroupId { get; set; }

        public List<string> SplitTestGroupIds { get; set; }

        [StringLength(50)]
        public string SplitTestStatus { get; set; }

        public List<string> SplitTestStatuses { get; set; }

        public bool? IsNewStructure { get; set; }

        public bool? SkipLearningPhase { get; set; }

        public DateTime? CreateTimeFrom { get; set; }

        public DateTime? CreateTimeTo { get; set; }

        public DateTime? ModifyTimeFrom { get; set; }

        public DateTime? ModifyTimeTo { get; set; }

        [StringLength(100)]
        public string SortBy { get; set; }

        [StringLength(10)]
        public string SortOrder { get; set; }

        public List<string> ExcludeFields { get; set; }

        public List<string> IncludeFields { get; set; }

        public bool? IncludeDeprecatedFields { get; set; }

        [StringLength(255)]
        public string SearchKeyword { get; set; }

        public bool? ActiveOnly { get; set; }

        public bool? PausedOnly { get; set; }

        public bool? HasBudget { get; set; }

        public bool? HasSchedule { get; set; }

        public bool? HasTargeting { get; set; }

        public GetAdGroupListDto()
        {
            AdvertiserIds = new List<string>();
            CampaignIds = new List<string>();
            AdgroupIds = new List<string>();
            CampaignSystemOrigins = new List<string>();
            ShoppingAdsTypes = new List<string>();
            IdentityIds = new List<string>();
            IdentityTypes = new List<string>();
            ProductSources = new List<string>();
            CatalogIds = new List<string>();
            StoreIds = new List<string>();
            PromotionTypes = new List<string>();
            PromotionTargetTypes = new List<string>();
            AppIds = new List<string>();
            AppTypes = new List<string>();
            PixelIds = new List<string>();
            OptimizationEvents = new List<string>();
            PlacementTypes = new List<string>();
            Placements = new List<string>();
            AudienceTypes = new List<string>();
            LocationIds = new List<string>();
            ZipcodeIds = new List<string>();
            Languages = new List<string>();
            Genders = new List<string>();
            AgeGroups = new List<string>();
            SpendingPowers = new List<string>();
            HouseholdIncomes = new List<string>();
            AudienceIds = new List<string>();
            ExcludedAudienceIds = new List<string>();
            InterestCategoryIds = new List<string>();
            InterestKeywordIds = new List<string>();
            PurchaseIntentionKeywordIds = new List<string>();
            IncludedPangleAudiencePackageIds = new List<string>();
            ExcludedPangleAudiencePackageIds = new List<string>();
            OperatingSystems = new List<string>();
            DeviceModelIds = new List<string>();
            NetworkTypes = new List<string>();
            CarrierIds = new List<string>();
            IspIds = new List<string>();
            DevicePriceRanges = new List<int>();
            SavedAudienceIds = new List<string>();
            ContextualTagIds = new List<string>();
            BrandSafetyTypes = new List<string>();
            BrandSafetyPartners = new List<string>();
            CategoryExclusionIds = new List<string>();
            VerticalSensitivityIds = new List<string>();
            BudgetModes = new List<string>();
            ScheduleTypes = new List<string>();
            OptimizationGoals = new List<string>();
            SecondaryOptimizationEvents = new List<string>();
            MessageEventSetIds = new List<string>();
            BidTypes = new List<string>();
            DeepBidTypes = new List<string>();
            VboWindows = new List<string>();
            BidDisplayModes = new List<string>();
            CpvVideoDurations = new List<string>();
            ClickAttributionWindows = new List<string>();
            EngagedViewAttributionWindows = new List<string>();
            ViewAttributionWindows = new List<string>();
            AttributionEventCounts = new List<string>();
            BillingEvents = new List<string>();
            Pacings = new List<string>();
            OperationStatuses = new List<string>();
            SecondaryStatuses = new List<string>();
            StatisticTypes = new List<string>();
            CreativeMaterialModes = new List<string>();
            AdgroupAppProfilePageStates = new List<string>();
            FeedTypes = new List<string>();
            RfPurchasedTypes = new List<string>();
            SplitTestGroupIds = new List<string>();
            SplitTestStatuses = new List<string>();
            ExcludeFields = new List<string>();
            IncludeFields = new List<string>();

            MaxResultCount = 50;
            SkipCount = 0;
            SortBy = "CreationTime";
            SortOrder = "DESC";
        }
    }
}