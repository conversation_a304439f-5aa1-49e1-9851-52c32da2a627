using Humanizer.Localisation.Formatters;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Worker để đồng bộ Balance
    /// </summary>
    public class SyncBalanceWorker : WorkerJobBase
    {
        private readonly IBalanceSyncService _balanceSyncService;

        public SyncBalanceWorker(
            ILogger<SyncBalanceWorker> logger,
            IServiceProvider serviceProvider,
            IBalanceSyncService balanceSyncService)
            : base(logger, serviceProvider)
        {
            _balanceSyncService = balanceSyncService;
        }

        public override CommandType CommandType => CommandType.SyncBalance;

        protected override async Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job)
        {
            // Parse parameters
            var parameters = ParseParameters(args.Parameters);

            // Sync Balance
            var responseSync = await _balanceSyncService.SyncAllBalancesAsync(parameters.BcId);
            if (responseSync != null)
            {
                if (responseSync.IsSuccess)
                    return JobResult.Success(JsonConvert.SerializeObject(responseSync));
                else
                    return JobResult.Error(responseSync.ErrorMessage ?? string.Empty, JsonConvert.SerializeObject(responseSync));
            }
            else
            {
                return JobResult.Error("Lỗi đồng bộ Balance");
            }
        }
    }
}