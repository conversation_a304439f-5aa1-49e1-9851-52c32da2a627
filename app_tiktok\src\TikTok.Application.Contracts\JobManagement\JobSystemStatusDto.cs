using System;

namespace TikTok.JobManagement
{
    /// <summary>
    /// DTO cho trạng thái hệ thống job
    /// </summary>
    public class JobSystemStatusDto
    {
        /// <summary>
        /// Hệ thống có đang hoạt động không
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Số worker đang hoạt động
        /// </summary>
        public int ActiveWorkers { get; set; }

        /// <summary>
        /// Tổng số worker
        /// </summary>
        public int TotalWorkers { get; set; }

        /// <summary>
        /// Số job đang chờ xử lý
        /// </summary>
        public int PendingJobs { get; set; }

        /// <summary>
        /// Số job đã hoàn thành hôm nay
        /// </summary>
        public int CompletedJobsToday { get; set; }

        /// <summary>
        /// Số job bị lỗi hôm nay
        /// </summary>
        public int FailedJobsToday { get; set; }

        /// <summary>
        /// Thời gian cập nhật cuối cùng
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }
}