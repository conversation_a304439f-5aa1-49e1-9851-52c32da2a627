/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Terms operations
    /// </summary>
    public interface ITermsApi
    {
        /// <summary>
        /// Get the agreement for the Lead Generation Ads feature.
        /// You must specify the feature that you want to request the agreement for using the term_type field.
        /// </summary>
        /// <param name="request">Terms get request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the terms content</returns>
        Task<TermGetResponse> GetTermsAsync(
            TermGetRequest request,
            CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Sign the agreement for the Lead Generation Ads feature.
        // /// After signing an agreement, you can use CheckTermsStatusAsync to check the status of the agreement.
        // /// </summary>
        // /// <param name="request">Terms confirm request parameters</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response confirming the terms have been signed</returns>
        // Task<TermConfirmResponse> SignTermsAsync(
        //     TermConfirmRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Check the status of an agreement.
        /// If the agreement has been signed, you can get the agreement using GetTermsAsync.
        /// </summary>
        /// <param name="request">Terms check request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the terms status</returns>
        //Task<TermCheckResponse> CheckTermsStatusAsync(
        //    TermCheckRequest request,
        //    CancellationToken cancellationToken = default);
    }
}
