using System;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.BusinessCenters
{
    /// <summary>
    /// DTO cho trung tâm kinh doanh
    /// </summary>
    public class BusinessCenterDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID của trung tâm kinh doanh
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// Tên trung tâm kinh doanh
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Tên công ty của trung tâm kinh doanh
        /// </summary>
        public string Company { get; set; }

        /// <summary>
        /// Đơn vị tiền tệ thanh toán
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Khu vực đăng ký
        /// </summary>
        public string RegisteredArea { get; set; }

        /// <summary>
        /// Trạng thái của trung tâm kinh doanh
        /// </summary>
        public BusinessCenterStatus Status { get; set; }

        /// <summary>
        /// Múi giờ
        /// </summary>
        public string Timezone { get; set; }

        /// <summary>
        /// Loại trung tâm kinh doanh
        /// </summary>
        public BusinessCenterType Type { get; set; }

        /// <summary>
        /// Vai trò người dùng
        /// </summary>
        public UserRole UserRole { get; set; }

        /// <summary>
        /// Vai trò tài chính mở rộng
        /// </summary>
        public ExtUserFinanceRole? ExtUserFinanceRole { get; set; }
    }
} 