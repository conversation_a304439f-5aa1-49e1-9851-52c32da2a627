using System;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;

namespace TikTok.RecordTransactionAdAccounts
{
    /// <summary>
    /// DTO tạo mới RecordTransactionAdAccount
    /// </summary>
    public class CreateRecordTransactionAdAccountDto
    {
        /// <summary>
        /// ID Tài khoản Quảng cáo
        /// </summary>
        [Required]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// Tên Tài khoản Quảng cáo
        /// </summary>
        [Required]
        public string AdvertiserName { get; set; }

        /// <summary>
        /// Số tiền giao dịch, được giữ ở hai chữ số thập phân
        /// </summary>
        [Required]
        public decimal Amount { get; set; }

        /// <summary>
        /// Tiền tệ của giao dịch
        /// </summary>
        [Required]
        public string Currency { get; set; }

        /// <summary>
        /// Thời gian giao dịch theo múi giờ UTC+0
        /// </summary>
        [Required]
        public DateTime Date { get; set; }

        /// <summary>
        /// Loại hình tài trợ
        /// </summary>
        [Required]
        public FundsType FundsType { get; set; }

        /// <summary>
        /// Loại hình thanh toán
        /// </summary>
        [Required]
        public TransferType TransferType { get; set; }

        /// <summary>
        /// Múi giờ giao dịch
        /// </summary>
        [Required]
        public string Timezone { get; set; }
    }
}