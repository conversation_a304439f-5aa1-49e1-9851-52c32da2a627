/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Filtering criteria for creative reports
    /// </summary>
    public class CreativeReportsFiltering
    {
        /// <summary>
        /// Material ID. You can specify at most 10 material IDs to filter the results for.
        /// </summary>
        [JsonPropertyName("material_id")]
        public List<string>? MaterialId { get; set; }

        /// <summary>
        /// Material name. Fuzzy search is supported.
        /// </summary>
        [JsonPropertyName("material_name")]
        public string? MaterialName { get; set; }

        /// <summary>
        /// The ad name. Fuzzy search is supported.
        /// </summary>
        [JsonPropertyName("ad_name")]
        public string? AdName { get; set; }

        /// <summary>
        /// A list of ad IDs you want to filter by. Bulk query is supported.
        /// </summary>
        [JsonPropertyName("ad_id")]
        public List<string>? AdId { get; set; }

        /// <summary>
        /// The ad group name that you want to filter by. Fuzzy search is supported.
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdgroupName { get; set; }

        /// <summary>
        /// The list of ad groups that you want to filter by. Bulk query is supported.
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public List<string>? AdgroupId { get; set; }

        /// <summary>
        /// The name of the campaign that you want to filter by. Fuzzy search is supported.
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// The list of campaigns that you want to filter by. Bulk query is supported.
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public List<string>? CampaignId { get; set; }

        /// <summary>
        /// List of location codes that you want to filter by.
        /// </summary>
        [JsonPropertyName("country_code")]
        public List<string>? CountryCode { get; set; }

        /// <summary>
        /// List of ad placements that you want to filter by.
        /// </summary>
        [JsonPropertyName("placement")]
        public List<string>? Placement { get; set; }

        /// <summary>
        /// Instant page IDs
        /// </summary>
        [JsonPropertyName("page_id")]
        public List<string>? PageId { get; set; }

        /// <summary>
        /// Instant page status. Enum values: DRAFT, READY
        /// </summary>
        [JsonPropertyName("page_status")]
        public List<string>? PageStatus { get; set; }

        /// <summary>
        /// Instant page business type. Enum values: CUSTOM, APP_PROFILE, INSTANT_FORM
        /// </summary>
        [JsonPropertyName("page_biz_type")]
        public List<string>? PageBizType { get; set; }

        /// <summary>
        /// Instant page template type
        /// </summary>
        [JsonPropertyName("page_template_type")]
        public List<string>? PageTemplateType { get; set; }

        /// <summary>
        /// Codes of the country or location that the instant page is deployed to
        /// </summary>
        [JsonPropertyName("app_profile_country")]
        public List<string>? AppProfileCountry { get; set; }

        /// <summary>
        /// ID of the app that the instant page directs to
        /// </summary>
        [JsonPropertyName("app_profile_external_app_id")]
        public List<string>? AppProfileExternalAppId { get; set; }

        /// <summary>
        /// Conversion events that are specific to tiktok instant pages
        /// </summary>
        [JsonPropertyName("customized_page_external_action")]
        public List<string>? CustomizedPageExternalAction { get; set; }

        /// <summary>
        /// Total Cost Range
        /// </summary>
        [JsonPropertyName("spend")]
        public CreativeReportsRangeFilter? Spend { get; set; }

        /// <summary>
        /// The conversion value
        /// </summary>
        [JsonPropertyName("conversion")]
        public CreativeReportsRangeFilter? Conversion { get; set; }
    }

    /// <summary>
    /// Range filter for numeric values
    /// </summary>
    public class CreativeReportsRangeFilter
    {
        /// <summary>
        /// Minimum value, open interval. Not filling means there is no minimum.
        /// </summary>
        [JsonPropertyName("min")]
        public float? Min { get; set; }

        /// <summary>
        /// Maximum value, open interval. Not filling means there is no maximum.
        /// </summary>
        [JsonPropertyName("max")]
        public float? Max { get; set; }
    }

    /// <summary>
    /// Response for creative reports
    /// </summary>
    public class CreativeReportsResponse
    {
        /// <summary>
        /// Report information
        /// </summary>
        [JsonPropertyName("list")]
        public List<CreativeReportItem> List { get; set; } = new List<CreativeReportItem>();

        /// <summary>
        /// Paging information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    /// <summary>
    /// Individual creative report item
    /// </summary>
    public class CreativeReportItem
    {
        /// <summary>
        /// Material information
        /// </summary>
        [JsonPropertyName("info")]
        public CreativeReportInfo Info { get; set; } = new CreativeReportInfo();

        /// <summary>
        /// Metrics data
        /// </summary>
        [JsonPropertyName("metrics")]
        public Dictionary<string, object> Metrics { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Material information in creative reports
    /// </summary>
    public class CreativeReportInfo
    {
        /// <summary>
        /// Material ID
        /// </summary>
        [JsonPropertyName("material_id")]
        public string? MaterialId { get; set; }

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Image ID
        /// </summary>
        [JsonPropertyName("image_id")]
        public string? ImageId { get; set; }

        /// <summary>
        /// Source of material
        /// </summary>
        [JsonPropertyName("source")]
        public string? Source { get; set; }

        /// <summary>
        /// Country or location code
        /// </summary>
        [JsonPropertyName("country_code")]
        public List<string>? CountryCode { get; set; }

        /// <summary>
        /// Placement
        /// </summary>
        [JsonPropertyName("placement")]
        public List<string>? Placement { get; set; }

        /// <summary>
        /// The number of ads
        /// </summary>
        [JsonPropertyName("num_ads")]
        public int? NumAds { get; set; }

        /// <summary>
        /// The total active days. An active day is a day that has spending (spend > 0).
        /// This field only supports VIDEO type of material.
        /// </summary>
        [JsonPropertyName("total_active_days")]
        public int? TotalActiveDays { get; set; }

        /// <summary>
        /// The IDs of the related ads. This field only supports VIDEO type of material.
        /// </summary>
        [JsonPropertyName("related_ad_ids")]
        public List<string>? RelatedAdIds { get; set; }

        /// <summary>
        /// The number of related ad groups. This field only supports VIDEO type of material.
        /// </summary>
        [JsonPropertyName("adgroup_number")]
        public int? AdgroupNumber { get; set; }

        /// <summary>
        /// The IDs of the related ad groups. This field only supports VIDEO type of material.
        /// </summary>
        [JsonPropertyName("related_adgroup_ids")]
        public List<string>? RelatedAdgroupIds { get; set; }

        /// <summary>
        /// Instant page ID
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }

        /// <summary>
        /// Instant page status. Enum values: DRAFT, READY
        /// </summary>
        [JsonPropertyName("page_status")]
        public string? PageStatus { get; set; }

        /// <summary>
        /// Instant page business type. Enum values: CUSTOM, APP_PROFILE, INSTANT_FORM
        /// </summary>
        [JsonPropertyName("page_biz_type")]
        public List<string>? PageBizType { get; set; }

        /// <summary>
        /// Instant page template type
        /// </summary>
        [JsonPropertyName("page_template_type")]
        public List<string>? PageTemplateType { get; set; }

        /// <summary>
        /// Link to the page thumbnail
        /// </summary>
        [JsonPropertyName("page_thumbnail")]
        public string? PageThumbnail { get; set; }

        /// <summary>
        /// Codes of the countries or locations that the instant page is deployed to
        /// </summary>
        [JsonPropertyName("app_profile_country")]
        public List<string>? AppProfileCountry { get; set; }

        /// <summary>
        /// ID of the app that the instant page directs to
        /// </summary>
        [JsonPropertyName("app_profile_external_app_id")]
        public List<string>? AppProfileExternalAppId { get; set; }

        /// <summary>
        /// App icon
        /// </summary>
        [JsonPropertyName("app_profile_icon")]
        public string? AppProfileIcon { get; set; }

        /// <summary>
        /// Conversion events that are specific to customized instant pages
        /// </summary>
        [JsonPropertyName("customized_page_external_action")]
        public List<string>? CustomizedPageExternalAction { get; set; }
    }
}
