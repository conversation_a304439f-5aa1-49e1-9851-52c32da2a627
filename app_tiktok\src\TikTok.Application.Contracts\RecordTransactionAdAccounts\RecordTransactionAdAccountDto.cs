using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.RecordTransactionAdAccounts
{
    /// <summary>
    /// DTO cho RecordTransactionAdAccount
    /// </summary>
    public class RecordTransactionAdAccountDto : EntityDto<Guid>
    {
        public string AdvertiserId { get; set; }
        public string AdvertiserName { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; }
        public DateTime Date { get; set; }
        public int FundsType { get; set; }
        public int TransferType { get; set; }
        public string Timezone { get; set; }
    }
}