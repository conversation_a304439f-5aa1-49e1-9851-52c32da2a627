using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.CostProfiles
{
    /// <summary>
    /// Service implementation cho hồ sơ chi phí
    /// </summary>
    [Authorize(TikTokPermissions.CostProfiles.Default)]
    public class CostProfileAppService :
        CrudAppService<
            RawCostProfileEntity,               // The CostProfile entity
            CostProfileDto,                     // Used to show cost profiles
            Guid,                               // Primary key of the cost profile entity
            GetCostProfileListDto,              // Used for paging/sorting
            CreateCostProfileDto,               // Used to create a new cost profile
            UpdateCostProfileDto>,              // Used to update a cost profile
        ICostProfileAppService
    {
        private readonly IRawCostProfileRepository _rawCostProfileRepository;

        public CostProfileAppService(
            IRawCostProfileRepository rawCostProfileRepository)
            : base(rawCostProfileRepository)
        {
            _rawCostProfileRepository = rawCostProfileRepository;

            GetPolicyName = TikTokPermissions.CostProfiles.Default;
            GetListPolicyName = TikTokPermissions.CostProfiles.Default;
            CreatePolicyName = TikTokPermissions.CostProfiles.Create;
            UpdatePolicyName = TikTokPermissions.CostProfiles.Edit;
            DeletePolicyName = TikTokPermissions.CostProfiles.Delete;
        }

        public override async Task<PagedResultDto<CostProfileDto>> GetListAsync(GetCostProfileListDto input)
        {
            // Get the total count
            var totalCount = await _rawCostProfileRepository.GetCountAsync(
                input.AdvertiserId,
                input.AdvertiserName,
                input.BcId,
                input.Currency,
                input.StartDate,
                input.EndDate,
                input.MinAmount,
                input.MaxAmount);

            // Get the items
            var items = await _rawCostProfileRepository.GetListAsync(
                input.AdvertiserId,
                input.AdvertiserName,
                input.BcId,
                input.Currency,
                input.StartDate,
                input.EndDate,
                input.MinAmount,
                input.MaxAmount,
                input.Sorting,
                input.MaxResultCount,
                input.SkipCount);

            // Map to DTOs
            var costProfileDtos = ObjectMapper.Map<RawCostProfileEntity[], CostProfileDto[]>(items.ToArray());

            return new PagedResultDto<CostProfileDto>
            {
                TotalCount = totalCount,
                Items = costProfileDtos
            };
        }
    }
}