/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response data for comment list API
    /// </summary>
    public class CommentListResponse
    {
        /// <summary>
        /// List of comments
        /// </summary>
        [JsonPropertyName("comments")]
        public List<CommentData>? Comments { get; set; }

        /// <summary>
        /// Cursor for the next page of results
        /// </summary>
        [JsonPropertyName("cursor")]
        public int? Cursor { get; set; }

        /// <summary>
        /// Whether an additional page of data is available
        /// </summary>
        [JsonPropertyName("has_more")]
        public bool? HasMore { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CommentListResponse(Comments={Comments?.Count}, HasMore={HasMore})";
        }
    }

    /// <summary>
    /// Comment data
    /// </summary>
    public class CommentData
    {
        /// <summary>
        /// Unique identifier for the comment
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string? CommentId { get; set; }

        /// <summary>
        /// The ID of the comment to which the reply is directed (for replies)
        /// </summary>
        [JsonPropertyName("parent_comment_id")]
        public string? ParentCommentId { get; set; }

        /// <summary>
        /// Unique identifier for the owned video the comment was created on
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Developer application and TikTok account scoped unique identifier for the user that made the comment
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// A globally unique identifier assigned to each user commenting
        /// </summary>
        [JsonPropertyName("unique_identifier")]
        public string? UniqueIdentifier { get; set; }

        /// <summary>
        /// Unix/Epoch date-time when the comment was created
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Text content of the comment
        /// </summary>
        [JsonPropertyName("text")]
        public string? Text { get; set; }

        /// <summary>
        /// Number of likes the comment has received
        /// </summary>
        [JsonPropertyName("likes")]
        public int? Likes { get; set; }

        /// <summary>
        /// Number of replies the comment has received
        /// </summary>
        [JsonPropertyName("replies")]
        public int? Replies { get; set; }

        /// <summary>
        /// Whether the current application user owns the comment
        /// </summary>
        [JsonPropertyName("owner")]
        public bool? Owner { get; set; }

        /// <summary>
        /// Whether the current application user likes the comment
        /// </summary>
        [JsonPropertyName("liked")]
        public bool? Liked { get; set; }

        /// <summary>
        /// Whether the current application user pinned the comment
        /// </summary>
        [JsonPropertyName("pinned")]
        public bool? Pinned { get; set; }

        /// <summary>
        /// The visibility status of the comment
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// The username of the TikTok user who made the comment
        /// </summary>
        [JsonPropertyName("username")]
        public string? Username { get; set; }

        /// <summary>
        /// The display name of the TikTok user who made the comment
        /// </summary>
        [JsonPropertyName("display_name")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// The avatar link for the account
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string? ProfileImage { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CommentData(CommentId={CommentId}, Username={Username}, Text={Text})";
        }
    }

    /// <summary>
    /// Request body for creating a comment
    /// </summary>
    public class CommentCreateRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Unique identifier for owned TikTok video to create comment on
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Text content of the comment
        /// </summary>
        [JsonPropertyName("text")]
        public string? Text { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CommentCreateRequest(BusinessId={BusinessId}, VideoId={VideoId}, Text={Text})";
        }
    }

    /// <summary>
    /// Request body for creating a comment reply
    /// </summary>
    public class CommentReplyCreateRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Unique identifier for owned TikTok video
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Unique identifier for comment to reply to
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string? CommentId { get; set; }

        /// <summary>
        /// Text content of the reply
        /// </summary>
        [JsonPropertyName("text")]
        public string? Text { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CommentReplyCreateRequest(BusinessId={BusinessId}, VideoId={VideoId}, CommentId={CommentId}, Text={Text})";
        }
    }

    /// <summary>
    /// Request body for liking/unliking a comment
    /// </summary>
    public class CommentLikeRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Unique identifier for owned TikTok video
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Unique identifier for comment to like/unlike
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string? CommentId { get; set; }

        /// <summary>
        /// Whether to like or unlike the comment
        /// </summary>
        [JsonPropertyName("like")]
        public bool? Like { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CommentLikeRequest(BusinessId={BusinessId}, VideoId={VideoId}, CommentId={CommentId}, Like={Like})";
        }
    }

    /// <summary>
    /// Request body for hiding/unhiding a comment
    /// </summary>
    public class CommentHideRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Unique identifier for owned TikTok video
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Unique identifier for comment to hide/unhide
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string? CommentId { get; set; }

        /// <summary>
        /// Whether to hide or unhide the comment
        /// </summary>
        [JsonPropertyName("hide")]
        public bool? Hide { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CommentHideRequest(BusinessId={BusinessId}, VideoId={VideoId}, CommentId={CommentId}, Hide={Hide})";
        }
    }

    /// <summary>
    /// Request body for deleting a comment
    /// </summary>
    public class CommentDeleteRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Unique identifier for owned TikTok video
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Unique identifier for comment to delete
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string? CommentId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"CommentDeleteRequest(BusinessId={BusinessId}, VideoId={VideoId}, CommentId={CommentId})";
        }
    }
}