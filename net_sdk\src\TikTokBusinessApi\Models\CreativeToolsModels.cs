/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Task Status Models

    /// <summary>
    /// Response for getting task status
    /// </summary>
    public class TaskStatusResponse
    {
        /// <summary>
        /// Task status
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Video IDs (valid only when status is SUCCESS)
        /// </summary>
        [JsonPropertyName("video_ids")]
        public List<string>? VideoIds { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("error_msg")]
        public string? ErrorMsg { get; set; }

        /// <summary>
        /// Extra callback info
        /// </summary>
        [JsonPropertyName("extra_info")]
        public object? ExtraInfo { get; set; }
    }

    #endregion

    #region Image Edit Models

    /// <summary>
    /// Request body for editing an image
    /// </summary>
    public class ImageEditRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Image ID
        /// </summary>
        [JsonPropertyName("image_id")]
        public string ImageId { get; set; } = string.Empty;

        /// <summary>
        /// Editing method
        /// </summary>
        [JsonPropertyName("edit_method")]
        public string? EditMethod { get; set; }

        /// <summary>
        /// Width of edited image
        /// </summary>
        [JsonPropertyName("width")]
        public int Width { get; set; }

        /// <summary>
        /// Height of edited image
        /// </summary>
        [JsonPropertyName("height")]
        public int Height { get; set; }

        /// <summary>
        /// Image name after editing
        /// </summary>
        [JsonPropertyName("image_name")]
        public string? ImageName { get; set; }
    }

    /// <summary>
    /// Response for image edit operation
    /// </summary>
    public class ImageEditResponse
    {
        /// <summary>
        /// Image ID
        /// </summary>
        [JsonPropertyName("image_id")]
        public string ImageId { get; set; } = string.Empty;

        /// <summary>
        /// Material ID
        /// </summary>
        [JsonPropertyName("material_id")]
        public string MaterialId { get; set; } = string.Empty;

        /// <summary>
        /// Whether it can be displayed on the platform
        /// </summary>
        [JsonPropertyName("displayable")]
        public bool Displayable { get; set; }

        /// <summary>
        /// Image width
        /// </summary>
        [JsonPropertyName("width")]
        public int Width { get; set; }

        /// <summary>
        /// Image format
        /// </summary>
        [JsonPropertyName("format")]
        public string Format { get; set; } = string.Empty;

        /// <summary>
        /// Image URL
        /// </summary>
        [JsonPropertyName("image_url")]
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// Image height
        /// </summary>
        [JsonPropertyName("height")]
        public int Height { get; set; }

        /// <summary>
        /// MD5 of Picture
        /// </summary>
        [JsonPropertyName("signature")]
        public string Signature { get; set; } = string.Empty;

        /// <summary>
        /// Image size in bytes
        /// </summary>
        [JsonPropertyName("size")]
        public long Size { get; set; }

        /// <summary>
        /// Image name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// Creation time
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;

        /// <summary>
        /// Modification time
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string ModifyTime { get; set; } = string.Empty;
    }

    #endregion

    #region Ad Preview Models

    /// <summary>
    /// Request body for creating ad preview
    /// </summary>
    public class AdPreviewRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Preview type
        /// </summary>
        [JsonPropertyName("preview_type")]
        public string PreviewType { get; set; } = string.Empty;

        /// <summary>
        /// Objective type
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string? ObjectiveType { get; set; }

        /// <summary>
        /// Whether the campaign is a Smart+ Campaign
        /// </summary>
        [JsonPropertyName("is_smart_performance_campaign")]
        public bool? IsSmartPerformanceCampaign { get; set; }

        /// <summary>
        /// Placement
        /// </summary>
        [JsonPropertyName("placement")]
        public string? Placement { get; set; }

        /// <summary>
        /// Preview format
        /// </summary>
        [JsonPropertyName("preview_format")]
        public string? PreviewFormat { get; set; }

        /// <summary>
        /// Shopping ads type
        /// </summary>
        [JsonPropertyName("shopping_ads_type")]
        public string? ShoppingAdsType { get; set; }

        /// <summary>
        /// Product source
        /// </summary>
        [JsonPropertyName("product_source")]
        public string? ProductSource { get; set; }

        /// <summary>
        /// Store ID
        /// </summary>
        [JsonPropertyName("store_id")]
        public string? StoreId { get; set; }

        /// <summary>
        /// Store authorized BC ID
        /// </summary>
        [JsonPropertyName("store_authorized_bc_id")]
        public string? StoreAuthorizedBcId { get; set; }

        /// <summary>
        /// Showcase products
        /// </summary>
        [JsonPropertyName("showcase_products")]
        public List<ShowcaseProduct>? ShowcaseProducts { get; set; }

        /// <summary>
        /// Promotion type
        /// </summary>
        [JsonPropertyName("promotion_type")]
        public string? PromotionType { get; set; }

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Identity type
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// Identity authorized BC ID
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// Ad format
        /// </summary>
        [JsonPropertyName("ad_format")]
        public string? AdFormat { get; set; }

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Image IDs
        /// </summary>
        [JsonPropertyName("image_ids")]
        public List<string>? ImageIds { get; set; }

        /// <summary>
        /// End card CTA
        /// </summary>
        [JsonPropertyName("end_card_cta")]
        public List<string>? EndCardCta { get; set; }

        /// <summary>
        /// Music ID
        /// </summary>
        [JsonPropertyName("music_id")]
        public string? MusicId { get; set; }

        /// <summary>
        /// TikTok item ID
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TiktokItemId { get; set; }

        /// <summary>
        /// Carousel image index
        /// </summary>
        [JsonPropertyName("carousel_image_index")]
        public int? CarouselImageIndex { get; set; }

        /// <summary>
        /// Ad text
        /// </summary>
        [JsonPropertyName("ad_text")]
        public string? AdText { get; set; }

        /// <summary>
        /// Call to action
        /// </summary>
        [JsonPropertyName("call_to_action")]
        public string? CallToAction { get; set; }

        /// <summary>
        /// Call to action ID
        /// </summary>
        [JsonPropertyName("call_to_action_id")]
        public string? CallToActionId { get; set; }

        /// <summary>
        /// Card ID
        /// </summary>
        [JsonPropertyName("card_id")]
        public string? CardId { get; set; }

        /// <summary>
        /// Landing page URL
        /// </summary>
        [JsonPropertyName("landing_page_url")]
        public string? LandingPageUrl { get; set; }

        /// <summary>
        /// Page ID
        /// </summary>
        [JsonPropertyName("page_id")]
        public string? PageId { get; set; }

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }

        /// <summary>
        /// Product specific type
        /// </summary>
        [JsonPropertyName("product_specific_type")]
        public string? ProductSpecificType { get; set; }

        /// <summary>
        /// Item group IDs
        /// </summary>
        [JsonPropertyName("item_group_ids")]
        public List<string>? ItemGroupIds { get; set; }

        /// <summary>
        /// Product set ID
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string? ProductSetId { get; set; }

        /// <summary>
        /// SKU IDs
        /// </summary>
        [JsonPropertyName("sku_ids")]
        public List<string>? SkuIds { get; set; }

        /// <summary>
        /// Vehicle IDs
        /// </summary>
        [JsonPropertyName("vehicle_ids")]
        public List<string>? VehicleIds { get; set; }

        /// <summary>
        /// Auto disclaimer types
        /// </summary>
        [JsonPropertyName("auto_disclaimer_types")]
        public List<string>? AutoDisclaimerTypes { get; set; }

        /// <summary>
        /// Catalog authorized BC ID
        /// </summary>
        [JsonPropertyName("catalog_authorized_bc_id")]
        public string? CatalogAuthorizedBcId { get; set; }

        /// <summary>
        /// Dynamic format
        /// </summary>
        [JsonPropertyName("dynamic_format")]
        public string? DynamicFormat { get; set; }

        /// <summary>
        /// Vertical video strategy
        /// </summary>
        [JsonPropertyName("vertical_video_strategy")]
        public string? VerticalVideoStrategy { get; set; }

        /// <summary>
        /// Shopping ads video package ID
        /// </summary>
        [JsonPropertyName("shopping_ads_video_package_id")]
        public string? ShoppingAdsVideoPackageId { get; set; }

        /// <summary>
        /// Shopping ads fallback type
        /// </summary>
        [JsonPropertyName("shopping_ads_fallback_type")]
        public string? ShoppingAdsFallbackType { get; set; }

        /// <summary>
        /// Dynamic destination
        /// </summary>
        [JsonPropertyName("dynamic_destination")]
        public string? DynamicDestination { get; set; }

        /// <summary>
        /// Instant product page used
        /// </summary>
        [JsonPropertyName("instant_product_page_used")]
        public bool? InstantProductPageUsed { get; set; }

        /// <summary>
        /// Ad ID (for existing ads preview)
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// Device models
        /// </summary>
        [JsonPropertyName("device")]
        public List<string>? Device { get; set; }

        /// <summary>
        /// Language
        /// </summary>
        [JsonPropertyName("language")]
        public string? Language { get; set; }

        /// <summary>
        /// Thumbnail ID
        /// </summary>
        [JsonPropertyName("thumbnail")]
        public string? Thumbnail { get; set; }

        /// <summary>
        /// Profile image ID
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string? ProfileImage { get; set; }

        /// <summary>
        /// Display name
        /// </summary>
        [JsonPropertyName("display_name")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// Placements
        /// </summary>
        [JsonPropertyName("placements")]
        public List<string>? Placements { get; set; }

        /// <summary>
        /// Image ID (for single image preview)
        /// </summary>
        [JsonPropertyName("image_id")]
        public string? ImageId { get; set; }
    }



    /// <summary>
    /// Response for ad preview creation
    /// </summary>
    public class AdPreviewResponse
    {
        /// <summary>
        /// Preview link
        /// </summary>
        [JsonPropertyName("preview_link")]
        public string PreviewLink { get; set; } = string.Empty;

        /// <summary>
        /// iframe code snippet
        /// </summary>
        [JsonPropertyName("iframe")]
        public string Iframe { get; set; } = string.Empty;

        /// <summary>
        /// Adjustment advice
        /// </summary>
        [JsonPropertyName("tips")]
        public List<PreviewTip>? Tips { get; set; }
    }

    /// <summary>
    /// Preview tip information
    /// </summary>
    public class PreviewTip
    {
        /// <summary>
        /// Placement
        /// </summary>
        [JsonPropertyName("placement")]
        public string? Placement { get; set; }

        /// <summary>
        /// Messages
        /// </summary>
        [JsonPropertyName("messages")]
        public List<string> Messages { get; set; } = new List<string>();
    }

    #endregion

    #region Video Soundtrack Models (Deprecated)

    /// <summary>
    /// Request body for creating a Smart Video Soundtrack task
    /// </summary>
    public class VideoSoundtrackCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Music IDs
        /// </summary>
        [JsonPropertyName("music_ids")]
        public List<string>? MusicIds { get; set; }

        /// <summary>
        /// Video volume
        /// </summary>
        [JsonPropertyName("video_volume")]
        public float? VideoVolume { get; set; }

        /// <summary>
        /// Music volume
        /// </summary>
        [JsonPropertyName("music_volume")]
        public float? MusicVolume { get; set; }

        /// <summary>
        /// Video name prefix
        /// </summary>
        [JsonPropertyName("name_prefix")]
        public string? NamePrefix { get; set; }

        /// <summary>
        /// Callback information
        /// </summary>
        [JsonPropertyName("callback_info")]
        public CallbackInfo? CallbackInfo { get; set; }
    }

    #endregion

    #region Quick Optimization Models (Deprecated)

    /// <summary>
    /// Request body for creating a Quick Optimization task
    /// </summary>
    public class QuickOptimizationCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Logo image ID
        /// </summary>
        [JsonPropertyName("logo")]
        public string? Logo { get; set; }

        /// <summary>
        /// Title
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// Description
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Callback information
        /// </summary>
        [JsonPropertyName("callback_info")]
        public CallbackInfo? CallbackInfo { get; set; }
    }

    #endregion

    #region Smart Video Models (Deprecated)

    /// <summary>
    /// Request body for creating a Smart Video task
    /// </summary>
    public class SmartVideoCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Video list
        /// </summary>
        [JsonPropertyName("videos")]
        public List<SmartVideoItem>? Videos { get; set; }

        /// <summary>
        /// Image list
        /// </summary>
        [JsonPropertyName("images")]
        public List<SmartImageItem>? Images { get; set; }

        /// <summary>
        /// Text list
        /// </summary>
        [JsonPropertyName("texts")]
        public List<SmartTextItem>? Texts { get; set; }

        /// <summary>
        /// Layout
        /// </summary>
        [JsonPropertyName("layout")]
        public string Layout { get; set; } = string.Empty;

        /// <summary>
        /// Style
        /// </summary>
        [JsonPropertyName("style")]
        public string Style { get; set; } = string.Empty;

        /// <summary>
        /// Music ID
        /// </summary>
        [JsonPropertyName("music_id")]
        public string? MusicId { get; set; }

        /// <summary>
        /// Duration
        /// </summary>
        [JsonPropertyName("duration")]
        public int? Duration { get; set; }

        /// <summary>
        /// Keyframe
        /// </summary>
        [JsonPropertyName("frame")]
        public List<SmartVideoFrame>? Frame { get; set; }

        /// <summary>
        /// Callback information
        /// </summary>
        [JsonPropertyName("callback_info")]
        public CallbackInfo? CallbackInfo { get; set; }
    }

    /// <summary>
    /// Smart video item
    /// </summary>
    public class SmartVideoItem
    {
        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Material tag
        /// </summary>
        [JsonPropertyName("tag")]
        public string? Tag { get; set; }
    }

    /// <summary>
    /// Smart image item
    /// </summary>
    public class SmartImageItem
    {
        /// <summary>
        /// Image ID
        /// </summary>
        [JsonPropertyName("image_id")]
        public string ImageId { get; set; } = string.Empty;

        /// <summary>
        /// Material tag
        /// </summary>
        [JsonPropertyName("tag")]
        public string? Tag { get; set; }
    }

    /// <summary>
    /// Smart text item
    /// </summary>
    public class SmartTextItem
    {
        /// <summary>
        /// Text
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; } = string.Empty;

        /// <summary>
        /// Material tag
        /// </summary>
        [JsonPropertyName("tag")]
        public string? Tag { get; set; }
    }

    /// <summary>
    /// Smart video frame
    /// </summary>
    public class SmartVideoFrame
    {
        /// <summary>
        /// Frame type
        /// </summary>
        [JsonPropertyName("frame_type")]
        public string? FrameType { get; set; }

        /// <summary>
        /// Material type
        /// </summary>
        [JsonPropertyName("material_type")]
        public string? MaterialType { get; set; }

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Image ID
        /// </summary>
        [JsonPropertyName("image_id")]
        public string? ImageId { get; set; }

        /// <summary>
        /// Template type
        /// </summary>
        [JsonPropertyName("template_type")]
        public string? TemplateType { get; set; }

        /// <summary>
        /// Logo image ID
        /// </summary>
        [JsonPropertyName("logo")]
        public string? Logo { get; set; }

        /// <summary>
        /// Call to action
        /// </summary>
        [JsonPropertyName("call_to_action")]
        public string? CallToAction { get; set; }

        /// <summary>
        /// Slogan
        /// </summary>
        [JsonPropertyName("slogan")]
        public string? Slogan { get; set; }

        /// <summary>
        /// Brand name
        /// </summary>
        [JsonPropertyName("brand_name")]
        public string? BrandName { get; set; }
    }

    #endregion

    #region Common Models

    /// <summary>
    /// Callback information
    /// </summary>
    public class CallbackInfo
    {
        /// <summary>
        /// Callback URL
        /// </summary>
        [JsonPropertyName("callback_url")]
        public string? CallbackUrl { get; set; }

        /// <summary>
        /// Extra callback information
        /// </summary>
        [JsonPropertyName("callback_extra_info")]
        public object? CallbackExtraInfo { get; set; }
    }

    /// <summary>
    /// Task creation response
    /// </summary>
    public class TaskCreateResponse
    {
        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string TaskId { get; set; } = string.Empty;
    }

    #endregion

    #region Creative Asset Delete Models

    /// <summary>
    /// Request body for deleting creative assets
    /// </summary>
    public class CreativeAssetDeleteRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Video IDs to delete
        /// </summary>
        [JsonPropertyName("video_ids")]
        public List<string>? VideoIds { get; set; }

        /// <summary>
        /// Image IDs to delete
        /// </summary>
        [JsonPropertyName("image_ids")]
        public List<string>? ImageIds { get; set; }
    }

    /// <summary>
    /// Response for creative asset deletion
    /// </summary>
    public class CreativeAssetDeleteResponse
    {
        /// <summary>
        /// Failed video IDs
        /// </summary>
        [JsonPropertyName("failed_video_ids")]
        public List<string> FailedVideoIds { get; set; } = new List<string>();

        /// <summary>
        /// Failed image IDs
        /// </summary>
        [JsonPropertyName("failed_image_ids")]
        public List<string> FailedImageIds { get; set; } = new List<string>();
    }

    #endregion

    #region Smart Text Models

    /// <summary>
    /// Request body for generating Smart Text recommendations
    /// </summary>
    public class SmartTextGenerateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string AdgroupId { get; set; } = string.Empty;

        /// <summary>
        /// Parameter type
        /// </summary>
        [JsonPropertyName("param_type")]
        public string? ParamType { get; set; }

        /// <summary>
        /// Language
        /// </summary>
        [JsonPropertyName("language")]
        public string? Language { get; set; }

        /// <summary>
        /// Industry ID
        /// </summary>
        [JsonPropertyName("industry_id")]
        public string? IndustryId { get; set; }

        /// <summary>
        /// Keywords
        /// </summary>
        [JsonPropertyName("keywords")]
        public List<string>? Keywords { get; set; }

        /// <summary>
        /// Number of texts to generate
        /// </summary>
        [JsonPropertyName("limit")]
        public int? Limit { get; set; }
    }

    /// <summary>
    /// Response for Smart Text generation
    /// </summary>
    public class SmartTextGenerateResponse
    {
        /// <summary>
        /// Generated text ID
        /// </summary>
        [JsonPropertyName("generate_id")]
        public string? GenerateId { get; set; }

        /// <summary>
        /// Industry ID
        /// </summary>
        [JsonPropertyName("industry_id")]
        public string? IndustryId { get; set; }

        /// <summary>
        /// Language
        /// </summary>
        [JsonPropertyName("language")]
        public string? Language { get; set; }

        /// <summary>
        /// Generated texts
        /// </summary>
        [JsonPropertyName("texts")]
        public List<string>? Texts { get; set; }
    }

    /// <summary>
    /// Request body for Smart Text feedback
    /// </summary>
    public class SmartTextFeedbackRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string AdgroupId { get; set; } = string.Empty;

        /// <summary>
        /// Generated text ID
        /// </summary>
        [JsonPropertyName("generated_text_id")]
        public string GeneratedTextId { get; set; } = string.Empty;

        /// <summary>
        /// Selected titles
        /// </summary>
        [JsonPropertyName("selected_titles")]
        public List<SelectedTitle>? SelectedTitles { get; set; }
    }

    /// <summary>
    /// Selected title information
    /// </summary>
    public class SelectedTitle
    {
        /// <summary>
        /// Generated title
        /// </summary>
        [JsonPropertyName("generated_title")]
        public string GeneratedTitle { get; set; } = string.Empty;

        /// <summary>
        /// Final title
        /// </summary>
        [JsonPropertyName("final_title")]
        public string FinalTitle { get; set; } = string.Empty;
    }

    #endregion

    #region CTA Recommendation Models

    /// <summary>
    /// Response for CTA recommendations
    /// </summary>
    public class CtaRecommendResponse
    {
        /// <summary>
        /// Recommended assets
        /// </summary>
        [JsonPropertyName("recommend_assets")]
        public List<RecommendedAsset> RecommendAssets { get; set; } = new List<RecommendedAsset>();
    }

    /// <summary>
    /// Recommended asset information
    /// </summary>
    public class RecommendedAsset
    {
        /// <summary>
        /// Asset IDs
        /// </summary>
        [JsonPropertyName("asset_ids")]
        public List<string> AssetIds { get; set; } = new List<string>();

        /// <summary>
        /// Asset content
        /// </summary>
        [JsonPropertyName("asset_content")]
        public string AssetContent { get; set; } = string.Empty;
    }

    #endregion

    #region Smart Fix Models

    /// <summary>
    /// Request body for creating Smart Fix task
    /// </summary>
    public class SmartFixCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Tasks
        /// </summary>
        [JsonPropertyName("tasks")]
        public List<SmartFixTask> Tasks { get; set; } = new List<SmartFixTask>();
    }

    /// <summary>
    /// Smart Fix task information
    /// </summary>
    public class SmartFixTask
    {
        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Auto bind enabled
        /// </summary>
        [JsonPropertyName("auto_bind_enabled")]
        public bool? AutoBindEnabled { get; set; }
    }

    /// <summary>
    /// Response for Smart Fix task creation
    /// </summary>
    public class SmartFixCreateResponse
    {
        /// <summary>
        /// Tasks
        /// </summary>
        [JsonPropertyName("tasks")]
        public List<SmartFixTaskResult> Tasks { get; set; } = new List<SmartFixTaskResult>();
    }

    /// <summary>
    /// Smart Fix task result
    /// </summary>
    public class SmartFixTaskResult
    {
        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Fix task ID
        /// </summary>
        [JsonPropertyName("fix_task_id")]
        public string? FixTaskId { get; set; }

        /// <summary>
        /// Flaw types
        /// </summary>
        [JsonPropertyName("flaw_types")]
        public List<string>? FlawTypes { get; set; }
    }

    /// <summary>
    /// Response for Smart Fix task results
    /// </summary>
    public class SmartFixResultsResponse
    {
        /// <summary>
        /// Task status
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("error_msg")]
        public string? ErrorMsg { get; set; }

        /// <summary>
        /// Fixed videos
        /// </summary>
        [JsonPropertyName("videos")]
        public List<FixedVideo>? Videos { get; set; }
    }

    /// <summary>
    /// Fixed video information
    /// </summary>
    public class FixedVideo
    {
        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Video URL
        /// </summary>
        [JsonPropertyName("video_url")]
        public string VideoUrl { get; set; } = string.Empty;
    }

    #endregion

    #region Creative Fatigue Models

    /// <summary>
    /// Request for Creative Fatigue Detection
    /// </summary>
    public class CreativeFatigueRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string AdId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public CreativeFatigueFiltering Filtering { get; set; } = new CreativeFatigueFiltering();

        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Creative Fatigue filtering conditions
    /// </summary>
    public class CreativeFatigueFiltering
    {
        /// <summary>
        /// Start date
        /// </summary>
        [JsonPropertyName("start_date")]
        public string StartDate { get; set; } = string.Empty;

        /// <summary>
        /// End date
        /// </summary>
        [JsonPropertyName("end_date")]
        public string EndDate { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for Creative Fatigue Detection
    /// </summary>
    public class CreativeFatigueResponse
    {
        /// <summary>
        /// List of fatigue detection results
        /// </summary>
        [JsonPropertyName("list")]
        public List<CreativeFatigueResult> List { get; set; } = new List<CreativeFatigueResult>();

        /// <summary>
        /// Page information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new PageInfo();
    }

    /// <summary>
    /// Creative Fatigue detection result
    /// </summary>
    public class CreativeFatigueResult
    {
        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string AdgroupId { get; set; } = string.Empty;

        /// <summary>
        /// Ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string AdId { get; set; } = string.Empty;

        /// <summary>
        /// Date
        /// </summary>
        [JsonPropertyName("date")]
        public string Date { get; set; } = string.Empty;

        /// <summary>
        /// Metrics
        /// </summary>
        [JsonPropertyName("metrics")]
        public CreativeFatigueMetrics Metrics { get; set; } = new CreativeFatigueMetrics();
    }

    /// <summary>
    /// Creative Fatigue metrics
    /// </summary>
    public class CreativeFatigueMetrics
    {
        /// <summary>
        /// Whether Creative Fatigue is detected
        /// </summary>
        [JsonPropertyName("has_fatigue")]
        public bool HasFatigue { get; set; }

        /// <summary>
        /// Fatigue index
        /// </summary>
        [JsonPropertyName("fatigue_index")]
        public double FatigueIndex { get; set; }

        /// <summary>
        /// Daily new users
        /// </summary>
        [JsonPropertyName("dnu")]
        public long Dnu { get; set; }

        /// <summary>
        /// DNU ratio
        /// </summary>
        [JsonPropertyName("dnu_ratio")]
        public double DnuRatio { get; set; }

        /// <summary>
        /// Total spend
        /// </summary>
        [JsonPropertyName("spend")]
        public double Spend { get; set; }

        /// <summary>
        /// Cost per conversion
        /// </summary>
        [JsonPropertyName("cost_per_conversion")]
        public double CostPerConversion { get; set; }

        /// <summary>
        /// SKAN cost per conversion
        /// </summary>
        [JsonPropertyName("skan_cost_per_conversion")]
        public double SkanCostPerConversion { get; set; }
    }

    #endregion
}
