using System;
using System.Collections.Generic;
using TikTok.Enums;

namespace TikTok.BusinessApplications
{
    /// <summary>
    /// DTO cho response cài đặt đồng bộ lùi
    /// </summary>
    public class SyncBackResponseDto
    {
        /// <summary>
        /// ID của Business Center
        /// </summary>
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Ngày bắt đầu đồng bộ
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// Ng<PERSON>y kết thúc đồng bộ
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Danh sách các loại lệnh đã được tạo job
        /// </summary>
        public List<CommandType> CreatedCommandTypes { get; set; } = new List<CommandType>();

        /// <summary>
        /// Tổng số job đã được tạo
        /// </summary>
        public int TotalJobsCreated { get; set; }

        /// <summary>
        /// Thông báo kết quả
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Có thành công hay không
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Thời gian tạo
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
