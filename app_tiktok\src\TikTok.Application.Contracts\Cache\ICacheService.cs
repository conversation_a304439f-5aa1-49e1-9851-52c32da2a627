using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.Cache
{
    /// <summary>
    /// Interface chung cho tất cả cache services
    /// </summary>
    public interface ICacheService : IScopedDependency
    {
        /// <summary>
        /// Tên cache để hiển thị trong monitor
        /// </summary>
        string CacheName { get; }

        /// <summary>
        /// Lấy danh sách các cache keys đang được sử dụng
        /// </summary>
        /// <returns>Danh sách cache keys</returns>
        Task<List<string>> GetCacheKeysAsync();

        /// <summary>
        /// Xóa tất cả cache của service này
        /// </summary>
        /// <returns>True nếu xóa thành công</returns>
        Task<bool> ClearCacheAsync();

        /// <summary>
        /// Xóa một cache key cụ thể
        /// </summary>
        /// <param name="cacheKey">Cache key cần xóa</param>
        /// <returns>True nếu xóa thành công</returns>
        Task<bool> ClearCacheByKeyAsync(string cacheKey);

        /// <summary>
        /// Lấy thông tin chi tiết về cache (số lượng items, thời gian tạo, etc.)
        /// </summary>
        /// <returns>Thông tin cache</returns>
        Task<CacheInfoDto> GetCacheInfoAsync();
    }
}
