/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Exceptions
{
    /// <summary>
    /// Common TikTok API error codes
    /// </summary>
    public static class TikTokErrorCodes
    {
        // Success
        public const int Success = 0;

        // Authentication & Authorization Errors (40xxx)
        public const int InvalidAccessToken = 40001;
        public const int AccessTokenExpired = 40002;
        public const int InsufficientPermissions = 40003;
        public const int InvalidAppId = 40004;
        public const int InvalidSecret = 40005;
        public const int AuthorizationFailed = 40006;

        // Request Validation Errors (40xxx)
        public const int InvalidParameter = 40100;
        public const int MissingParameter = 40101;
        public const int InvalidParameterValue = 40102;
        public const int InvalidRequestFormat = 40103;
        public const int RequestTooLarge = 40104;

        // Resource Errors (40xxx)
        public const int ResourceNotFound = 40404;
        public const int ResourceAlreadyExists = 40409;
        public const int ResourceConflict = 40410;

        // Rate Limiting Errors (42xxx)
        public const int RateLimitExceeded = 42001;
        public const int QuotaExceeded = 42002;
        public const int ConcurrencyLimitExceeded = 42003;

        // Business Logic Errors (50xxx)
        public const int InsufficientBalance = 50001;
        public const int CampaignNotActive = 50002;
        public const int AdGroupNotActive = 50003;
        public const int InvalidTargeting = 50004;
        public const int BudgetTooLow = 50005;

        // Server Errors (50xxx)
        public const int InternalServerError = 50000;
        public const int ServiceUnavailable = 50100;
        public const int DatabaseError = 50101;
        public const int ExternalServiceError = 50102;

        // Network & Infrastructure Errors (60xxx)
        public const int NetworkTimeout = 60001;
        public const int ConnectionError = 60002;
        public const int DnsResolutionError = 60003;
        public const int SslError = 60004;

        /// <summary>
        /// Determines the error category based on error code
        /// </summary>
        /// <param name="errorCode">TikTok API error code</param>
        /// <returns>Error category</returns>
        public static TikTokErrorCategory GetErrorCategory(int errorCode)
        {
            return errorCode switch
            {
                Success => TikTokErrorCategory.Success,
                >= 40001 and <= 40006 => TikTokErrorCategory.Authentication,
                >= 40100 and <= 40199 => TikTokErrorCategory.Validation,
                >= 40400 and <= 40499 => TikTokErrorCategory.Resource,
                >= 42000 and <= 42999 => TikTokErrorCategory.RateLimit,
                >= 50000 and <= 50999 => TikTokErrorCategory.Business,
                >= 60000 and <= 69999 => TikTokErrorCategory.Network,
                _ => TikTokErrorCategory.Unknown
            };
        }

        /// <summary>
        /// Determines if an error is retryable
        /// </summary>
        /// <param name="errorCode">TikTok API error code</param>
        /// <returns>True if the error is retryable</returns>
        public static bool IsRetryable(int errorCode)
        {
            return GetErrorCategory(errorCode) switch
            {
                TikTokErrorCategory.Network => true,
                TikTokErrorCategory.RateLimit => true,
                TikTokErrorCategory.Business when errorCode == ServiceUnavailable => true,
                TikTokErrorCategory.Business when errorCode == InternalServerError => true,
                _ => false
            };
        }

        /// <summary>
        /// Gets a human-readable description for an error code
        /// </summary>
        /// <param name="errorCode">TikTok API error code</param>
        /// <returns>Error description</returns>
        public static string GetErrorDescription(int errorCode)
        {
            return errorCode switch
            {
                Success => "Success",
                InvalidAccessToken => "Invalid access token",
                AccessTokenExpired => "Access token has expired",
                InsufficientPermissions => "Insufficient permissions for this operation",
                InvalidAppId => "Invalid application ID",
                InvalidSecret => "Invalid application secret",
                AuthorizationFailed => "Authorization failed",
                InvalidParameter => "Invalid parameter provided",
                MissingParameter => "Required parameter is missing",
                InvalidParameterValue => "Parameter value is invalid",
                InvalidRequestFormat => "Request format is invalid",
                RequestTooLarge => "Request payload is too large",
                ResourceNotFound => "Requested resource not found",
                ResourceAlreadyExists => "Resource already exists",
                ResourceConflict => "Resource conflict",
                RateLimitExceeded => "Rate limit exceeded",
                QuotaExceeded => "API quota exceeded",
                ConcurrencyLimitExceeded => "Concurrency limit exceeded",
                InsufficientBalance => "Insufficient account balance",
                CampaignNotActive => "Campaign is not active",
                AdGroupNotActive => "Ad group is not active",
                InvalidTargeting => "Invalid targeting configuration",
                BudgetTooLow => "Budget is too low",
                InternalServerError => "Internal server error",
                ServiceUnavailable => "Service temporarily unavailable",
                DatabaseError => "Database error",
                ExternalServiceError => "External service error",
                NetworkTimeout => "Network timeout",
                ConnectionError => "Connection error",
                DnsResolutionError => "DNS resolution error",
                SslError => "SSL/TLS error",
                _ => "Unknown error"
            };
        }
    }

    /// <summary>
    /// TikTok error categories
    /// </summary>
    public enum TikTokErrorCategory
    {
        /// <summary>
        /// Success response
        /// </summary>
        Success,

        /// <summary>
        /// Authentication and authorization errors
        /// </summary>
        Authentication,

        /// <summary>
        /// Request validation errors
        /// </summary>
        Validation,

        /// <summary>
        /// Resource-related errors
        /// </summary>
        Resource,

        /// <summary>
        /// Rate limiting errors
        /// </summary>
        RateLimit,

        /// <summary>
        /// Business logic errors
        /// </summary>
        Business,

        /// <summary>
        /// Network and infrastructure errors
        /// </summary>
        Network,

        /// <summary>
        /// Unknown error category
        /// </summary>
        Unknown
    }
}
