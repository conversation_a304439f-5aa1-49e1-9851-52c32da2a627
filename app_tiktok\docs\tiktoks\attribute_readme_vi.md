# Chỉ số <PERSON>hu<PERSON> t<PERSON> (Attribute Metrics)

Chỉ số thuộ<PERSON> t<PERSON>, chẳng hạn như tên nhóm quảng cáo và loại khuyến mãi, là những thuộc tính cơ bản của chiến dịch, nhóm quảng cáo hoặc quảng cáo của bạn. Chỉ số thuộc tính chỉ có hiệu lực khi sử dụng kiểu dimension là ID.

## Bảng Thông Tin Chi Tiết

| Trường | Loại | Mô tả | Chi tiết |
|--------|------|-------|----------|
| advertiser_name | string | Tên tài khoản nhà quảng cáo | Được hỗ trợ ở cấp độ <PERSON>hà quảng cáo, Chiến dịch, Nhóm quảng cáo và Quảng cáo. |
| advertiser_id | string | ID tài kho<PERSON> nhà quảng cáo | <PERSON><PERSON><PERSON><PERSON> hỗ trợ ở cấp độ <PERSON> qu<PERSON> cá<PERSON>, <PERSON><PERSON><PERSON> d<PERSON>, <PERSON>hó<PERSON> quả<PERSON> cáo và Quảng cáo. |
| campaign_name | string | Tên chiến dịch | Được hỗ trợ ở cấp độ Chiến dịch, Nhóm quảng cáo và Quảng cáo. |
| campaign_id | string | ID chiến dịch | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. |
| objective_type | string | Mục tiêu quảng cáo | Được hỗ trợ ở cấp độ Chiến dịch, Nhóm quảng cáo và Quảng cáo. |
| split_test | string | Trạng thái kiểm thử phân tách | Được hỗ trợ ở cấp độ Chiến dịch, Nhóm quảng cáo và Quảng cáo. |
| campaign_budget | string | Ngân sách chiến dịch | Được hỗ trợ ở cấp độ Chiến dịch, Nhóm quảng cáo và Quảng cáo. |
| campaign_dedicate_type | string | Loại chiến dịch | Chiến dịch Chuyên dụng iOS14 hoặc chiến dịch thông thường. Được hỗ trợ ở cấp độ Chiến dịch, Nhóm quảng cáo và Quảng cáo. |
| app_promotion_type | string | Loại khuyến mãi ứng dụng | Được hỗ trợ ở cấp độ Chiến dịch, Nhóm quảng cáo và Quảng cáo. Các giá trị enum: `APP_INSTALL`, `APP_RETARGETING`. `APP_INSTALL` và `APP_RETARGETING` sẽ được trả về khi `objective_type` là `APP_PROMOTION`. Ngược lại, `UNSET` sẽ được trả về. |
| adgroup_name | string | Tên nhóm quảng cáo | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. |
| adgroup_id | string | ID nhóm quảng cáo | Được hỗ trợ ở cấp độ Quảng cáo. |
| placement_type | string | Loại vị치 đặt quảng cáo | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. |
| promotion_type | string | Loại khuyến mãi | Có thể là ứng dụng, trang web hoặc khác. Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo trong cả báo cáo đồng bộ và không đồng bộ. |
| opt_status | string | Tối ưu hóa sáng tạo tự động | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. |
| adgroup_download_url | string | URL Tải xuống/URL Trang web | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. |
| profile_image | string | Hình ảnh hồ sơ | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. |
| dpa_target_audience_type | string | Loại đối tượng mục tiêu cho DPA | Đối tượng mà sản phẩm DPA nhắm tới. Được hỗ trợ ở cấp độ Nhóm quảng cáo hoặc Quảng cáo trong cả báo cáo đồng bộ và không đồng bộ. |
| budget | string | Ngân sách nhóm quảng cáo | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. |
| smart_target | string | Mục tiêu tối ưu hóa | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. |
| pricing_category *Sẽ bị loại bỏ* | string | Sự kiện Thanh toán | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. Nếu bạn muốn lấy sự kiện thanh toán của quảng cáo, hãy sử dụng chỉ số mới `billing_event`. |
| billing_event | string | Sự kiện Thanh toán | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. Ví dụ: `"Clicks"`, `"Impression"`. |
| bid_strategy | string | Chiến lược đấu giá | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. |
| bid | string | Giá đấu | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. |
| bid_secondary_goal | string | Giá đấu cho mục tiêu phụ | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. |
| aeo_type | string | Loại Tối ưu hóa Sự kiện Ứng dụng | Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. (Đã được hỗ trợ ở cấp độ Nhóm quảng cáo, và sẽ được hỗ trợ ở cấp độ Quảng cáo) |
| ad_name | string | Tên quảng cáo | Được hỗ trợ ở cấp độ Quảng cáo. |
| ad_id | string | ID quảng cáo | Được hỗ trợ ở cấp độ Quảng cáo. |
| ad_text | string | Tiêu đề quảng cáo | Được hỗ trợ ở cấp độ Quảng cáo. |
| call_to_action | string | Lời kêu gọi hành động | Được hỗ trợ ở cấp độ Quảng cáo. |
| ad_profile_image | string | Hình ảnh hồ sơ (Cấp độ Quảng cáo) | Được hỗ trợ ở cấp độ Quảng cáo. |
| ad_url | string | URL (Cấp độ Quảng cáo) | Được hỗ trợ ở cấp độ Quảng cáo. |
| tt_app_id | string | ID Ứng dụng TikTok | ID Ứng dụng TikTok, ID Ứng dụng bạn đã sử dụng khi tạo Nhóm quảng cáo. Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. Được trả về nếu loại khuyến mãi của một Nhóm quảng cáo là Ứng dụng. |
| tt_app_name | string | Tên Ứng dụng TikTok | Tên của Ứng dụng TikTok của bạn. Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. Được trả về nếu loại khuyến mãi của một Nhóm quảng cáo là Ứng dụng. |
| mobile_app_id | string | ID Ứng dụng Di động | ID Ứng dụng Di động. Ví dụ: App Store: https://apps.apple.com/us/app/angry-birds/id**343200656**; Google Play: https://play.google.com/store/apps/details?id=**com.rovio.angrybirds**. Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo. Được trả về nếu loại khuyến mãi của một Nhóm quảng cáo là Ứng dụng. |
| image_mode | string | Định dạng | Được hỗ trợ ở cấp độ Quảng cáo. |
| currency | string | tiền tệ | Mã tiền tệ, ví dụ: USD. Lưu ý rằng nếu bạn muốn sử dụng `currency` làm chỉ số, thì trường `dimensions` trong yêu cầu của bạn phải bao gồm `adgroup_id`/`ad_id`/`campaign_id`/`advertiser_id`. |
| is_aco | boolean | Liệu quảng cáo có phải là quảng cáo tự động hay quảng cáo Smart Creative không. Đặt thành `True` cho quảng cáo tự động hoặc quảng cáo Smart Creative. | Được hỗ trợ ở cấp độ AUCTION_ADGROUP. |
| is_smart_creative | boolean | Liệu quảng cáo có phải là quảng cáo Smart Creative không. | Được hỗ trợ ở cấp độ AUCTION_AD. |