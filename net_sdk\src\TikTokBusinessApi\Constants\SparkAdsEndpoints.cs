/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Spark Ads endpoints
    /// </summary>
    public static class SparkAdsEndpoints
    {
        /// <summary>
        /// Get info about a Spark Ad post endpoint (v1.3)
        /// </summary>
        public const string GetSparkAdInfo = "/tt_video/info/";

        /// <summary>
        /// Apply an authorization code endpoint (v1.3)
        /// </summary>
        public const string AuthorizeSparkAd = "/tt_video/authorize/";

        /// <summary>
        /// Get Spark Ad posts endpoint (v1.3)
        /// </summary>
        public const string GetSparkAdPosts = "/tt_video/list/";

        /// <summary>
        /// Unbind a Spark Ad post endpoint (v1.3)
        /// </summary>
        public const string UnbindSparkAd = "/tt_video/unbind/";
    }
}
