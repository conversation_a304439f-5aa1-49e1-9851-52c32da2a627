/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Creative Insights operations
    /// </summary>
    public interface ICreativeInsightsApi
    {
        /// <summary>
        /// Get ad benchmarks - Use this endpoint to get the performance data of ads against benchmarks
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting ad benchmarks</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing ad benchmark data</returns>
        Task<Response<AdBenchmarkResponse>> GetAdBenchmarksAsync(
            string accessToken,
            AdBenchmarkRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get in-second performance - Use this endpoint to get in-second performance data about ads, or Video Insights data about a video
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting video performance data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing video performance data</returns>
        Task<Response<VideoPerformanceResponse>> GetVideoPerformanceAsync(
            string accessToken,
            VideoPerformanceRequest request,
            CancellationToken cancellationToken = default);
    }
}
