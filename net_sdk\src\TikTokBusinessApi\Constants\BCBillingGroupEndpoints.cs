/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API BC Billing Group endpoints
    /// </summary>
    public static class BCBillingGroupEndpoints
    {
        /// <summary>
        /// Create a billing group endpoint (v1.3)
        /// </summary>
        public const string CreateBillingGroup = "/v1.3/bc/billing_group/create/";

        /// <summary>
        /// Update a billing group endpoint (v1.3)
        /// </summary>
        public const string UpdateBillingGroup = "/v1.3/bc/billing_group/update/";

        /// <summary>
        /// Get billing groups endpoint (v1.3)
        /// </summary>
        public const string GetBillingGroups = "/v1.3/bc/billing_group/get/";

        /// <summary>
        /// Get billing group advertisers endpoint (v1.3)
        /// </summary>
        public const string GetBillingGroupAdvertisers = "/v1.3/bc/billing_group/advertiser/list/";
    }
}
