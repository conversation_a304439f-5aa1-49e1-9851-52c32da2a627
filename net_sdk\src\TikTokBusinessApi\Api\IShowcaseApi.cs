/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Showcase operations
    /// </summary>
    public interface IShowcaseApi
    {
        /// <summary>
        /// Get identities with Showcase permission under an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing identities with Showcase permission</returns>
        Task<Response<ShowcaseIdentitiesResponse>> GetIdentitiesAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the available regions for a Showcase via identity
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="identityId">The ID of the identity that is bound to a Showcase and the identity needs to have permission of the Showcase</param>
        /// <param name="identityType">Identity type. Enum values: TT_USER (TikTok User), BC_AUTH_TT (TikTok Account User in Business Center)</param>
        /// <param name="identityAuthorizedBcId">Required when identity_type is BC_AUTH_TT. ID of the Business Center that a TikTok Account User in Business Center identity is associated with</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing available regions for the Showcase</returns>
        Task<Response<ShowcaseRegionsResponse>> GetRegionsAsync(
            string accessToken,
            string advertiserId,
            string identityId,
            string identityType,
            string? identityAuthorizedBcId = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the available products in a Showcase
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request containing parameters for getting Showcase products</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing available products in the Showcase</returns>
        Task<Response<ShowcaseProductsResponse>> GetProductsAsync(
            string accessToken,
            ShowcaseProductsRequest request,
            CancellationToken cancellationToken = default);
    }
}
