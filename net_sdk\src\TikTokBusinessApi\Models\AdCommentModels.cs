/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response data for ad comment list API
    /// </summary>
    public class AdCommentListResponse
    {
        /// <summary>
        /// List of comments
        /// </summary>
        [JsonPropertyName("comments")]
        public List<AdCommentData>? Comments { get; set; }

        /// <summary>
        /// Page information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentListResponse(Comments={Comments?.Count}, PageInfo={PageInfo})";
        }
    }

    /// <summary>
    /// Ad comment data
    /// </summary>
    public class AdCommentData
    {
        /// <summary>
        /// Comment ID
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string? CommentId { get; set; }

        /// <summary>
        /// App ID
        /// </summary>
        [JsonPropertyName("app")]
        public string? App { get; set; }

        /// <summary>
        /// Comment content
        /// </summary>
        [JsonPropertyName("content")]
        public string? Content { get; set; }

        /// <summary>
        /// Number of likes
        /// </summary>
        [JsonPropertyName("likes")]
        public int? Likes { get; set; }

        /// <summary>
        /// Number of replies
        /// </summary>
        [JsonPropertyName("replies")]
        public int? Replies { get; set; }

        /// <summary>
        /// Comment type (COMMENT or REPLY)
        /// </summary>
        [JsonPropertyName("comment_type")]
        public string? CommentType { get; set; }

        /// <summary>
        /// ID of the original comment (for replies)
        /// </summary>
        [JsonPropertyName("original_comment_id")]
        public string? OriginalCommentId { get; set; }

        /// <summary>
        /// Comment status (HIDDEN or PUBLIC)
        /// </summary>
        [JsonPropertyName("comment_status")]
        public string? CommentStatus { get; set; }

        /// <summary>
        /// Whether the comment contains blocked words
        /// </summary>
        [JsonPropertyName("hit_blockedword")]
        public bool? HitBlockedWord { get; set; }

        /// <summary>
        /// Ad title
        /// </summary>
        [JsonPropertyName("ad_text")]
        public string? AdText { get; set; }

        /// <summary>
        /// The time when the comment is created
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// Campaign name
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdGroupId { get; set; }

        /// <summary>
        /// Ad group name
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdGroupName { get; set; }

        /// <summary>
        /// Ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// Ad name
        /// </summary>
        [JsonPropertyName("ad_name")]
        public string? AdName { get; set; }

        /// <summary>
        /// TikTok video ID
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TikTokItemId { get; set; }

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Identity type (CUSTOMIZED_USER or TT_USER)
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// Whether the comment is pinned
        /// </summary>
        [JsonPropertyName("is_pinned")]
        public bool? IsPinned { get; set; }

        /// <summary>
        /// Whether the comment can be deleted
        /// </summary>
        [JsonPropertyName("can_delete")]
        public bool? CanDelete { get; set; }

        /// <summary>
        /// Whether the user is linked to a TikTok Business Account
        /// </summary>
        [JsonPropertyName("is_auth_ttba")]
        public bool? IsAuthTtba { get; set; }

        /// <summary>
        /// Whether the user has the comment management permission
        /// </summary>
        [JsonPropertyName("is_auth_comment_manage_scope")]
        public bool? IsAuthCommentManageScope { get; set; }

        /// <summary>
        /// Video play URL
        /// </summary>
        [JsonPropertyName("video_play_url")]
        public string? VideoPlayUrl { get; set; }

        /// <summary>
        /// Video cover URL
        /// </summary>
        [JsonPropertyName("video_cover_url")]
        public string? VideoCoverUrl { get; set; }

        /// <summary>
        /// URL to the user's avatar
        /// </summary>
        [JsonPropertyName("user_avatar_url")]
        public string? UserAvatarUrl { get; set; }

        /// <summary>
        /// Unique user name
        /// </summary>
        [JsonPropertyName("user_name")]
        public string? UserName { get; set; }

        /// <summary>
        /// The ID of the TikTok user who made the comment
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// Information about the user who posted the original comment (for replies)
        /// </summary>
        [JsonPropertyName("reply_user_info")]
        public object? ReplyUserInfo { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentData(CommentId={CommentId}, UserName={UserName}, Content={Content})";
        }
    }

    /// <summary>
    /// Request parameters for getting ad comments
    /// </summary>
    public class AdCommentListRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Comment type (ALL, COMMENT, REPLY)
        /// </summary>
        [JsonPropertyName("comment_type")]
        public List<string>? CommentType { get; set; }

        /// <summary>
        /// Field to search by (ADGROUP_ID)
        /// </summary>
        [JsonPropertyName("search_field")]
        public string? SearchField { get; set; }

        /// <summary>
        /// Value to search
        /// </summary>
        [JsonPropertyName("search_value")]
        public string? SearchValue { get; set; }

        /// <summary>
        /// Field to sort by (CREATE_TIME, LIKES, REPLIES)
        /// </summary>
        [JsonPropertyName("sort_field")]
        public string? SortField { get; set; }

        /// <summary>
        /// Sorting order (ASC, DESC)
        /// </summary>
        [JsonPropertyName("sort_type")]
        public string? SortType { get; set; }

        /// <summary>
        /// Start date (YYYY-MM-DD)
        /// </summary>
        [JsonPropertyName("start_time")]
        public string? StartTime { get; set; }

        /// <summary>
        /// End date (YYYY-MM-DD)
        /// </summary>
        [JsonPropertyName("end_time")]
        public string? EndTime { get; set; }

        /// <summary>
        /// Page size (1-100)
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentListRequest(AdvertiserId={AdvertiserId}, SearchField={SearchField}, SearchValue={SearchValue})";
        }
    }

    /// <summary>
    /// Request parameters for getting related comments
    /// </summary>
    public class AdCommentReferenceRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Comment ID
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string? CommentId { get; set; }

        /// <summary>
        /// Comment type (COMMENT or REPLY)
        /// </summary>
        [JsonPropertyName("comment_type")]
        public string? CommentType { get; set; }

        /// <summary>
        /// ID of the original comment (for replies)
        /// </summary>
        [JsonPropertyName("original_comment_id")]
        public string? OriginalCommentId { get; set; }

        /// <summary>
        /// Page size (1-1000)
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentReferenceRequest(AdvertiserId={AdvertiserId}, CommentId={CommentId}, CommentType={CommentType})";
        }
    }

    /// <summary>
    /// Request body for replying to a comment
    /// </summary>
    public class AdCommentReplyRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// TikTok video ID
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TikTokItemId { get; set; }

        /// <summary>
        /// ID of the comment to reply to
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string? CommentId { get; set; }

        /// <summary>
        /// Comment type (currently only REPLY is supported)
        /// </summary>
        [JsonPropertyName("comment_type")]
        public string? CommentType { get; set; }

        /// <summary>
        /// Comment text
        /// </summary>
        [JsonPropertyName("text")]
        public string? Text { get; set; }

        /// <summary>
        /// Type of the identity (CUSTOMIZED_USER or TT_USER)
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentReplyRequest(AdvertiserId={AdvertiserId}, AdId={AdId}, CommentId={CommentId}, Text={Text})";
        }
    }

    /// <summary>
    /// Response data for comment reply
    /// </summary>
    public class AdCommentReplyResponse
    {
        /// <summary>
        /// ID of the comment or reply that has just been created
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string? CommentId { get; set; }

        /// <summary>
        /// ID of the TikTok video
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TikTokItemId { get; set; }

        /// <summary>
        /// Comment or reply text that you just created
        /// </summary>
        [JsonPropertyName("text")]
        public string? Text { get; set; }

        /// <summary>
        /// Time when your comment or reply was created (UTC+0)
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// ID of the comment that you replied to
        /// </summary>
        [JsonPropertyName("reply_to_comment_id")]
        public string? ReplyToCommentId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentReplyResponse(CommentId={CommentId}, Text={Text}, CreateTime={CreateTime})";
        }
    }

    /// <summary>
    /// Request body for creating a comment export task
    /// </summary>
    public class AdCommentExportTaskRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Comment status (ALL, PUBLIC, HIDDEN)
        /// </summary>
        [JsonPropertyName("comment_status")]
        public List<string>? CommentStatus { get; set; }

        /// <summary>
        /// Comment type (ALL, COMMENT, REPLY)
        /// </summary>
        [JsonPropertyName("comment_type")]
        public List<string>? CommentType { get; set; }

        /// <summary>
        /// Field to search by (ADGROUP_ID)
        /// </summary>
        [JsonPropertyName("search_field")]
        public string? SearchField { get; set; }

        /// <summary>
        /// Value to search
        /// </summary>
        [JsonPropertyName("search_value")]
        public string? SearchValue { get; set; }

        /// <summary>
        /// IDs of the comments that you want to export
        /// </summary>
        [JsonPropertyName("comment_ids")]
        public List<string>? CommentIds { get; set; }

        /// <summary>
        /// Field to sort by (CREATE_TIME, LIKES, REPLIES)
        /// </summary>
        [JsonPropertyName("sort_field")]
        public string? SortField { get; set; }

        /// <summary>
        /// Sorting order (ASC, DESC)
        /// </summary>
        [JsonPropertyName("sort_type")]
        public string? SortType { get; set; }

        /// <summary>
        /// Start date (YYYY-MM-DD)
        /// </summary>
        [JsonPropertyName("start_time")]
        public string? StartTime { get; set; }

        /// <summary>
        /// End date (YYYY-MM-DD)
        /// </summary>
        [JsonPropertyName("end_time")]
        public string? EndTime { get; set; }

        /// <summary>
        /// Language of the headers (EN, JA, ZH)
        /// </summary>
        [JsonPropertyName("lang")]
        public string? Language { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentExportTaskRequest(AdvertiserId={AdvertiserId}, SearchField={SearchField}, SearchValue={SearchValue})";
        }
    }

    /// <summary>
    /// Response data for comment export task creation
    /// </summary>
    public class AdCommentExportTaskResponse
    {
        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentExportTaskResponse(TaskId={TaskId})";
        }
    }

    /// <summary>
    /// Request parameters for checking export task status
    /// </summary>
    public class AdCommentExportTaskStatusRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentExportTaskStatusRequest(AdvertiserId={AdvertiserId}, TaskId={TaskId})";
        }
    }

    /// <summary>
    /// Response data for export task status
    /// </summary>
    public class AdCommentExportTaskStatusResponse
    {
        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }

        /// <summary>
        /// Task status (RUNNING, SUCCEED, FAILED)
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentExportTaskStatusResponse(TaskId={TaskId}, Status={Status})";
        }
    }

    /// <summary>
    /// Request body for updating comment statuses (hide/show)
    /// </summary>
    public class AdCommentStatusUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// List of comment IDs to update
        /// </summary>
        [JsonPropertyName("comment_ids")]
        public List<string>? CommentIds { get; set; }

        /// <summary>
        /// Operation type (HIDDEN or PUBLIC)
        /// </summary>
        [JsonPropertyName("operation")]
        public string? Operation { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentStatusUpdateRequest(AdvertiserId={AdvertiserId}, CommentIds={CommentIds?.Count}, Operation={Operation})";
        }
    }

    /// <summary>
    /// Request body for deleting a comment
    /// </summary>
    public class AdCommentDeleteRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Ad ID
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// TikTok video ID
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TikTokItemId { get; set; }

        /// <summary>
        /// ID of the comment to delete
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string? CommentId { get; set; }

        /// <summary>
        /// Type of the identity (CUSTOMIZED_USER or TT_USER)
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentDeleteRequest(AdvertiserId={AdvertiserId}, AdId={AdId}, CommentId={CommentId})";
        }
    }

    /// <summary>
    /// Response data for comment export task download
    /// </summary>
    public class AdCommentExportDownloadResponse
    {
        /// <summary>
        /// CSV comments content
        /// </summary>
        [JsonPropertyName("csv")]
        public string? Csv { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentExportDownloadResponse(CsvLength={Csv?.Length})";
        }
    }

    /// <summary>
    /// Request body for creating blocked words
    /// </summary>
    public class AdCommentBlockedWordsCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// List of words that you want to block
        /// </summary>
        [JsonPropertyName("blocked_words")]
        public List<string>? BlockedWords { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordsCreateRequest(AdvertiserId={AdvertiserId}, BlockedWords={BlockedWords?.Count})";
        }
    }

    /// <summary>
    /// Request parameters for getting blocked words
    /// </summary>
    public class AdCommentBlockedWordsGetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordsGetRequest(AdvertiserId={AdvertiserId}, Page={Page}, PageSize={PageSize})";
        }
    }

    /// <summary>
    /// Response data for blocked words list
    /// </summary>
    public class AdCommentBlockedWordsListResponse
    {
        /// <summary>
        /// List of blocked words
        /// </summary>
        [JsonPropertyName("list")]
        public List<AdCommentBlockedWord>? List { get; set; }

        /// <summary>
        /// Page information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordsListResponse(List={List?.Count}, PageInfo={PageInfo})";
        }
    }

    /// <summary>
    /// Blocked word data
    /// </summary>
    public class AdCommentBlockedWord
    {
        /// <summary>
        /// Blocked word content
        /// </summary>
        [JsonPropertyName("bw_content")]
        public string? BwContent { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWord(BwContent={BwContent})";
        }
    }

    /// <summary>
    /// Request body for checking blocked word statuses
    /// </summary>
    public class AdCommentBlockedWordsCheckRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// List of words to check
        /// </summary>
        [JsonPropertyName("blocked_words")]
        public List<string>? BlockedWords { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordsCheckRequest(AdvertiserId={AdvertiserId}, BlockedWords={BlockedWords?.Count})";
        }
    }

    /// <summary>
    /// Response data for blocked word status check
    /// </summary>
    public class AdCommentBlockedWordsCheckResponse
    {
        /// <summary>
        /// Results of the check
        /// </summary>
        [JsonPropertyName("results")]
        public List<AdCommentBlockedWordCheckResult>? Results { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordsCheckResponse(Results={Results?.Count})";
        }
    }

    /// <summary>
    /// Result of blocked word check
    /// </summary>
    public class AdCommentBlockedWordCheckResult
    {
        /// <summary>
        /// Word being checked
        /// </summary>
        [JsonPropertyName("word")]
        public string? Word { get; set; }

        /// <summary>
        /// Whether the word is blocked
        /// </summary>
        [JsonPropertyName("blocked")]
        public bool? Blocked { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordCheckResult(Word={Word}, Blocked={Blocked})";
        }
    }

    /// <summary>
    /// Request body for updating a blocked word
    /// </summary>
    public class AdCommentBlockedWordUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Old blocked word
        /// </summary>
        [JsonPropertyName("old_word")]
        public string? OldWord { get; set; }

        /// <summary>
        /// New blocked word
        /// </summary>
        [JsonPropertyName("new_word")]
        public string? NewWord { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordUpdateRequest(AdvertiserId={AdvertiserId}, OldWord={OldWord}, NewWord={NewWord})";
        }
    }

    /// <summary>
    /// Request body for deleting blocked words
    /// </summary>
    public class AdCommentBlockedWordsDeleteRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// List of blocked words to delete
        /// </summary>
        [JsonPropertyName("blocked_words")]
        public List<string>? BlockedWords { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordsDeleteRequest(AdvertiserId={AdvertiserId}, BlockedWords={BlockedWords?.Count})";
        }
    }

    /// <summary>
    /// Request body for creating a blocked word export task
    /// </summary>
    public class AdCommentBlockedWordExportTaskRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// List of blocked words to export (optional)
        /// </summary>
        [JsonPropertyName("blocked_words")]
        public List<string>? BlockedWords { get; set; }

        /// <summary>
        /// Language of the headers (EN, JA, ZH)
        /// </summary>
        [JsonPropertyName("language")]
        public string? Language { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordExportTaskRequest(AdvertiserId={AdvertiserId}, BlockedWords={BlockedWords?.Count}, Language={Language})";
        }
    }

    /// <summary>
    /// Response data for blocked word export task creation
    /// </summary>
    public class AdCommentBlockedWordExportTaskResponse
    {
        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordExportTaskResponse(TaskId={TaskId})";
        }
    }

    /// <summary>
    /// Request parameters for checking blocked word export task status
    /// </summary>
    public class AdCommentBlockedWordExportTaskStatusRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordExportTaskStatusRequest(AdvertiserId={AdvertiserId}, TaskId={TaskId})";
        }
    }

    /// <summary>
    /// Response data for blocked word export task status
    /// </summary>
    public class AdCommentBlockedWordExportTaskStatusResponse
    {
        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }

        /// <summary>
        /// Task status (RUNNING, SUCCEED, FAILED)
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordExportTaskStatusResponse(TaskId={TaskId}, Status={Status})";
        }
    }

    /// <summary>
    /// Response data for blocked word export download
    /// </summary>
    public class AdCommentBlockedWordExportDownloadResponse
    {
        /// <summary>
        /// CSV blocked words content
        /// </summary>
        [JsonPropertyName("csv")]
        public string? Csv { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdCommentBlockedWordExportDownloadResponse(CsvLength={Csv?.Length})";
        }
    }
}
