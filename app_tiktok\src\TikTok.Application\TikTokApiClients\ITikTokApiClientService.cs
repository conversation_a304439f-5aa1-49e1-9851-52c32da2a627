using System;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Core.Resilience;

namespace TikTok.TikTokApiClients
{
    /// <summary>
    /// Service để quản lý singleton instance của TikTokApiClient
    /// </summary>
    public interface ITikTokApiClientService
    {
        /// <summary>
        /// Lấy hoặc tạo TikTokBusinessApiClient singleton
        /// </summary>
        /// <param name="bcId">Business Center ID (chỉ dùng để lấy cấu hình, không ảnh hưởng đến client)</param>
        /// <param name="rateLimitOption">Cấu hình rate limit (optional)</param>
        /// <param name="retryPolicyOption">Cấu hình retry policy (optional)</param>
        /// <returns>TikTokBusinessApiClient instance</returns>
        Task<TikTokBusinessApiClient> GetOrCreateClientAsync(string bcId, RateLimiterOptions? rateLimitOption = null, RetryPolicyOptions? retryPolicyOption = null);

        /// <summary>
        /// Lấy hoặc tạo TikTokBusinessApiClient với cấu hình mặc định
        /// </summary>
        /// <param name="bcId">Business Center ID (chỉ dùng để lấy cấu hình, không ảnh hưởng đến client)</param>
        /// <returns>TikTokBusinessApiClient instance</returns>
        Task<TikTokBusinessApiClient> GetOrCreateClientAsync(string bcId);

        /// <summary>
        /// Xóa client singleton (nếu cần refresh)
        /// </summary>
        void RemoveClient();

        /// <summary>
        /// Xóa client singleton
        /// </summary>
        void ClearClient();
    }
}
