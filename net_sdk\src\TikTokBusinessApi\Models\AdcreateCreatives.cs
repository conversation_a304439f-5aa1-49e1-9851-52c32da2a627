/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Creative information for ad creation
    /// </summary>
    public class AdcreateCreatives
    {
        /// <summary>
        /// Ad name
        /// </summary>
        [JsonPropertyName("ad_name")]
        public string? AdName { get; set; }

        /// <summary>
        /// Ad text
        /// </summary>
        [JsonPropertyName("ad_text")]
        public string? AdText { get; set; }

        /// <summary>
        /// Call to action text
        /// </summary>
        [JsonPropertyName("call_to_action")]
        public string? CallToAction { get; set; }

        /// <summary>
        /// Creative type
        /// </summary>
        [JsonPropertyName("creative_type")]
        public string? CreativeType { get; set; }

        /// <summary>
        /// Image IDs for the creative
        /// </summary>
        [JsonPropertyName("image_ids")]
        public List<string>? ImageIds { get; set; }

        /// <summary>
        /// Video ID for the creative
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Landing page URL
        /// </summary>
        [JsonPropertyName("landing_page_url")]
        public string? LandingPageUrl { get; set; }

        /// <summary>
        /// Display name for the creative
        /// </summary>
        [JsonPropertyName("display_name")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdcreateCreatives class
        /// </summary>
        public AdcreateCreatives()
        {
        }

        /// <summary>
        /// Sets the ad name and returns this instance for method chaining
        /// </summary>
        /// <param name="adName">Ad name</param>
        /// <returns>This instance</returns>
        public AdcreateCreatives SetAdName(string adName)
        {
            AdName = adName;
            return this;
        }

        /// <summary>
        /// Sets the ad text and returns this instance for method chaining
        /// </summary>
        /// <param name="adText">Ad text</param>
        /// <returns>This instance</returns>
        public AdcreateCreatives SetAdText(string adText)
        {
            AdText = adText;
            return this;
        }

        /// <summary>
        /// Sets the call to action and returns this instance for method chaining
        /// </summary>
        /// <param name="callToAction">Call to action</param>
        /// <returns>This instance</returns>
        public AdcreateCreatives SetCallToAction(string callToAction)
        {
            CallToAction = callToAction;
            return this;
        }

        /// <summary>
        /// Sets the creative type and returns this instance for method chaining
        /// </summary>
        /// <param name="creativeType">Creative type</param>
        /// <returns>This instance</returns>
        public AdcreateCreatives SetCreativeType(string creativeType)
        {
            CreativeType = creativeType;
            return this;
        }

        /// <summary>
        /// Sets the image IDs and returns this instance for method chaining
        /// </summary>
        /// <param name="imageIds">Image IDs</param>
        /// <returns>This instance</returns>
        public AdcreateCreatives SetImageIds(List<string> imageIds)
        {
            ImageIds = imageIds;
            return this;
        }

        /// <summary>
        /// Sets the video ID and returns this instance for method chaining
        /// </summary>
        /// <param name="videoId">Video ID</param>
        /// <returns>This instance</returns>
        public AdcreateCreatives SetVideoId(string videoId)
        {
            VideoId = videoId;
            return this;
        }

        /// <summary>
        /// Sets the landing page URL and returns this instance for method chaining
        /// </summary>
        /// <param name="landingPageUrl">Landing page URL</param>
        /// <returns>This instance</returns>
        public AdcreateCreatives SetLandingPageUrl(string landingPageUrl)
        {
            LandingPageUrl = landingPageUrl;
            return this;
        }

        /// <summary>
        /// Sets the display name and returns this instance for method chaining
        /// </summary>
        /// <param name="displayName">Display name</param>
        /// <returns>This instance</returns>
        public AdcreateCreatives SetDisplayName(string displayName)
        {
            DisplayName = displayName;
            return this;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdcreateCreatives(AdName={AdName}, CreativeType={CreativeType}, VideoId={VideoId})";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not AdcreateCreatives other)
                return false;

            return AdName == other.AdName &&
                   AdText == other.AdText &&
                   CallToAction == other.CallToAction &&
                   CreativeType == other.CreativeType &&
                   VideoId == other.VideoId &&
                   LandingPageUrl == other.LandingPageUrl &&
                   DisplayName == other.DisplayName &&
                   (ImageIds?.SequenceEqual(other.ImageIds ?? new List<string>()) ?? other.ImageIds == null);
        }

        /// <summary>
        /// Serves as the default hash function
        /// </summary>
        /// <returns>A hash code for the current object</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AdName, AdText, CallToAction, CreativeType, VideoId, LandingPageUrl, DisplayName, ImageIds);
        }
    }
}
