/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for creating a comment reply
    /// </summary>
    public class CommentReplyCreateRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for owned TikTok video to create the reply on
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for comment on an owned TikTok video to create reply on
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string CommentId { get; set; } = string.Empty;

        /// <summary>
        /// Text content of the reply to create
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; } = string.Empty;
    }
}
