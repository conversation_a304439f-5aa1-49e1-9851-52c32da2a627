using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu ngân sách
    /// </summary>
    public class BalanceSyncApplicationAppService : ApplicationService, IBalanceSyncApplicationAppService
    {
        private readonly IBalanceSyncService _balanceSyncService;

        public BalanceSyncApplicationAppService(IBalanceSyncService balanceSyncService)
        {
            _balanceSyncService = balanceSyncService;
        }

        /// <summary>
        /// Đồng bộ ngân sách Business Center theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BalanceSyncResult> SyncBcBalanceAsync(string bcId)
        {
            return await _balanceSyncService.SyncBcBalanceAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ ngân sách AdAccount theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BalanceSyncResult> SyncAdAccountBalanceAsync(string bcId)
        {
            return await _balanceSyncService.SyncAdAccountBalanceAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ tất cả ngân sách (BC và AdAccount) theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BalanceSyncResult> SyncAllBalancesAsync(string bcId)
        {
            return await _balanceSyncService.SyncAllBalancesAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ tất cả ngân sách cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BalanceSyncResult> SyncAllBalancesForAllBcsAsync()
        {
            return await _balanceSyncService.SyncAllBalancesForAllBcsAsync();
        }
    }
}