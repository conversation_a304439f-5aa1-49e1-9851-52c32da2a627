/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using TikTokBusinessApi.Core;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request parameters for getting ad groups
    /// </summary>
    public class AdGroupGetRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Filtering conditions for ad groups
        /// </summary>
        [JsonPropertyName("filtering")]
        public AdGroupFiltering? Filtering { get; set; }

        /// <summary>
        /// Current page number. Default value: 1
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size. Default value: 10. Range: 1-1000
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Fields to be returned in the response
        /// </summary>
        [JsonPropertyName("fields")]
        public List<string>? Fields { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupGetRequest class
        /// </summary>
        public AdGroupGetRequest()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupGetRequest(AdvertiserId={AdvertiserId}, Page={Page}, PageSize={PageSize})";
        }
    }

    /// <summary>
    /// Filtering conditions for ad group queries
    /// </summary>
    public class AdGroupFiltering
    {
        /// <summary>
        /// Filter by ad group IDs
        /// </summary>
        [JsonPropertyName("adgroup_ids")]
        public List<string>? AdgroupIds { get; set; }

        /// <summary>
        /// Filter by ad group names
        /// </summary>
        [JsonPropertyName("adgroup_names")]
        public List<string>? AdgroupNames { get; set; }

        /// <summary>
        /// Filter by campaign IDs
        /// </summary>
        [JsonPropertyName("campaign_ids")]
        public List<string>? CampaignIds { get; set; }

        /// <summary>
        /// Filter by ad group statuses
        /// </summary>
        [JsonPropertyName("statuses")]
        public List<string>? Statuses { get; set; }

        /// <summary>
        /// Filter by objective types
        /// </summary>
        [JsonPropertyName("objective_type")]
        public List<string>? ObjectiveType { get; set; }

        /// <summary>
        /// Filter by creation time start (format: YYYY-MM-DD HH:MM:SS)
        /// </summary>
        [JsonPropertyName("creation_filter_start_time")]
        public string? CreationFilterStartTime { get; set; }

        /// <summary>
        /// Filter by creation time end (format: YYYY-MM-DD HH:MM:SS)
        /// </summary>
        [JsonPropertyName("creation_filter_end_time")]
        public string? CreationFilterEndTime { get; set; }

        /// <summary>
        /// Filter by secondary status
        /// </summary>
        [JsonPropertyName("secondary_status")]
        public List<string>? SecondaryStatus { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupFiltering class
        /// </summary>
        public AdGroupFiltering()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupFiltering(AdgroupIds={AdgroupIds?.Count}, CampaignIds={CampaignIds?.Count}, Statuses={Statuses?.Count})";
        }
    }


    public class AdGroupGetData {
        /// <summary>
        /// List of ad groups
        /// </summary>
        [JsonPropertyName("list")]
        public List<AdGroupInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupGetData(Count={List?.Count}, PageInfo={PageInfo})";
        }
    }

    /// <summary>
    /// Response data for ad group get requests
    /// </summary>
    public class AdGroupGetResponse: TikTokApiResponse<AdGroupGetData>
    {
        /// <summary>
        /// Initializes a new instance of the AdGroupGetResponse class
        /// </summary>
        public AdGroupGetResponse()
        {
        }

        public override string ToString()
        {
            return $"AdGroupGetResponse({Data.ToString})";
        }
    }

    /// <summary>
    /// Request parameters for getting ad group quota
    /// </summary>
    public class AdGroupQuotaRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the AdGroupQuotaRequest class
        /// </summary>
        public AdGroupQuotaRequest()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupQuotaRequest(AdvertiserId={AdvertiserId})";
        }
    }

    /// <summary>
    /// Response data for ad group quota requests
    /// </summary>
    public class AdGroupQuotaResponse
    {
        /// <summary>
        /// Total ad group quota for the advertiser
        /// </summary>
        [JsonPropertyName("total_adgroup_quota")]
        public int? TotalAdgroupQuota { get; set; }

        /// <summary>
        /// Used ad group quota for the advertiser
        /// </summary>
        [JsonPropertyName("used_adgroup_quota")]
        public int? UsedAdgroupQuota { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupQuotaResponse class
        /// </summary>
        public AdGroupQuotaResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupQuotaResponse(Total={TotalAdgroupQuota}, Used={UsedAdgroupQuota})";
        }
    }

    /// <summary>
    /// Request parameters for estimating audience size
    /// </summary>
    public class AudienceSizeEstimateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        [Required]
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// Targeting information for audience estimation
        /// </summary>
        [JsonPropertyName("targeting")]
        [Required]
        public AudienceTargeting Targeting { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AudienceSizeEstimateRequest class
        /// </summary>
        public AudienceSizeEstimateRequest()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AudienceSizeEstimateRequest(AdvertiserId={AdvertiserId}, CampaignId={CampaignId})";
        }
    }

    /// <summary>
    /// Response data for audience size estimation
    /// </summary>
    public class AudienceSizeEstimateResponse
    {
        /// <summary>
        /// Estimated audience size
        /// </summary>
        [JsonPropertyName("audience_size")]
        public long? AudienceSize { get; set; }

        /// <summary>
        /// Initializes a new instance of the AudienceSizeEstimateResponse class
        /// </summary>
        public AudienceSizeEstimateResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AudienceSizeEstimateResponse(AudienceSize={AudienceSize})";
        }
    }

    /// <summary>
    /// Request body for creating ad groups
    /// </summary>
    public class AdGroupCreateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// List of ad groups to create
        /// </summary>
        [JsonPropertyName("adgroups")]
        [Required]
        public List<AdGroupCreateBody> Adgroups { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AdGroupCreateRequest class
        /// </summary>
        public AdGroupCreateRequest()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupCreateRequest(AdvertiserId={AdvertiserId}, AdgroupsCount={Adgroups?.Count})";
        }
    }

    /// <summary>
    /// Request body for updating ad groups
    /// </summary>
    public class AdGroupUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// List of ad groups to update
        /// </summary>
        [JsonPropertyName("adgroups")]
        [Required]
        public List<AdGroupUpdateBody> Adgroups { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AdGroupUpdateRequest class
        /// </summary>
        public AdGroupUpdateRequest()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupUpdateRequest(AdvertiserId={AdvertiserId}, AdgroupsCount={Adgroups?.Count})";
        }
    }

    /// <summary>
    /// Request body for updating ad group status
    /// </summary>
    public class AdGroupStatusUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// List of ad group IDs to update
        /// </summary>
        [JsonPropertyName("adgroup_ids")]
        [Required]
        public List<string> AdgroupIds { get; set; } = new();

        /// <summary>
        /// Operation to perform on the ad groups
        /// </summary>
        [JsonPropertyName("operation")]
        [Required]
        public string Operation { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the AdGroupStatusUpdateRequest class
        /// </summary>
        public AdGroupStatusUpdateRequest()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupStatusUpdateRequest(AdvertiserId={AdvertiserId}, Operation={Operation}, AdgroupIds={AdgroupIds?.Count})";
        }
    }

    /// <summary>
    /// Request body for updating ad group budgets
    /// </summary>
    public class AdGroupBudgetUpdateRequest
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// List of ad group budget updates
        /// </summary>
        [JsonPropertyName("adgroups")]
        [Required]
        public List<AdGroupBudgetUpdateBody> Adgroups { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AdGroupBudgetUpdateRequest class
        /// </summary>
        public AdGroupBudgetUpdateRequest()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupBudgetUpdateRequest(AdvertiserId={AdvertiserId}, AdgroupsCount={Adgroups?.Count})";
        }
    }
}
