/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Creative Portfolios operations
    /// </summary>
    public interface ICreativePortfoliosApi
    {
        /// <summary>
        /// Create a portfolio of creative assets
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing portfolio details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created portfolio ID</returns>
        //Task<Response<CreativePortfolioCreateResponse>> CreatePortfolioAsync(
        //    string accessToken,
        //    CreativePortfolioCreateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get an existing creative portfolio by its ID
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="creativePortfolioId">ID of the creative portfolio to get</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing portfolio details</returns>
        Task<Response<CreativePortfolioGetResponse>> GetPortfolioByIdAsync(
            string accessToken,
            string advertiserId,
            string creativePortfolioId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve portfolios created within an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="filtering">Filtering conditions</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of portfolios</returns>
        Task<Response<CreativePortfoliosListResponse>> GetPortfoliosAsync(
            string accessToken,
            string advertiserId,
            CreativePortfolioFiltering? filtering = null,
            int page = 1,
            int pageSize = 20,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Bulk delete creative portfolios
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing portfolio IDs to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> DeletePortfoliosAsync(
        //    string accessToken,
        //    CreativePortfolioDeleteBody body,
        //    CancellationToken cancellationToken = default);
    }
}
