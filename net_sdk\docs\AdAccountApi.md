# AdAccountApi Documentation

The `AdAccount<PERSON>pi` provides methods to manage and retrieve information about TikTok Business API advertiser accounts.

## Overview

The AdAccountApi allows you to:
- Get a list of authorized advertiser accounts
- Retrieve detailed information about specific ad accounts
- Access account metadata such as status, currency, balance, and more

## Available Methods

### GetAuthorizedAccountsAsync

Retrieves a list of advertiser accounts that have authorized your application.

**Endpoint:** `/oauth2/advertiser/get/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| accessToken | string | Yes | Authorized access token |
| appId | string | Yes | The App ID applied by the developer |
| secret | string | Yes | The private key of the developer's application |
| cancellationToken | CancellationToken | No | Cancellation token |

#### Response

Returns `Response<AuthorizedAccountsResponse>` containing:
- `List<AdvertiserInfo>` - List of authorized advertiser accounts
- Each `AdvertiserInfo` contains:
  - `AdvertiserId` - Advertiser ID
  - `AdvertiserName` - Advertiser name

#### Example

```csharp
var response = await client.AdAccount.GetAuthorizedAccountsAsync(
    accessToken: "your-access-token",
    appId: "your-app-id", 
    secret: "your-app-secret"
);

foreach (var account in response.Data.List)
{
    Console.WriteLine($"Account: {account.AdvertiserName} (ID: {account.AdvertiserId})");
}
```

### GetAccountDetailsAsync

Retrieves detailed information about specific advertiser accounts.

**Endpoint:** `/advertiser/info/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| accessToken | string | Yes | Authorized access token |
| advertiserIds | List<string> | Yes | List of advertiser IDs to query |
| fields | List<string> | No | Specific fields to return (optional) |
| cancellationToken | CancellationToken | No | Cancellation token |

#### Available Fields

When using the `fields` parameter, you can specify which information to retrieve:

| Field | Description |
|-------|-------------|
| `advertiser_id` | Advertiser ID |
| `owner_bc_id` | Business Center ID that owns the account |
| `status` | Ad account status |
| `role` | Ad account role |
| `rejection_reason` | Reason for rejection (if applicable) |
| `name` | Ad account name |
| `timezone` | Ad account timezone |
| `display_timezone` | Display timezone name |
| `company` | Company name |
| `company_name_editable` | Whether company name can be updated via API |
| `industry` | Industry category code |
| `address` | Account address |
| `country` | Country code |
| `advertiser_account_type` | Account type (RESERVATION/AUCTION) |
| `currency` | Currency code (ISO 4217) |
| `contacter` | Contact name (masked) |
| `email` | Contact email (masked) |
| `cellphone_number` | Mobile number (masked) |
| `telephone_number` | Phone number (masked) |
| `language` | Language code |
| `license_no` | Business license number |
| `license_url` | Business license preview URL |
| `description` | Brand description |
| `balance` | Available balance |
| `create_time` | Account creation time (Unix timestamp) |

#### Response

Returns `Response<AdAccountDetailsResponse>` containing:
- `List<AdAccountDetail>` - List of detailed account information

#### Example

```csharp
var advertiserIds = new List<string> { "1234567890123456789" };
var fields = new List<string> 
{ 
    "advertiser_id", 
    "name", 
    "status", 
    "currency", 
    "balance" 
};

var response = await client.AdAccount.GetAccountDetailsAsync(
    accessToken: "your-access-token",
    advertiserIds: advertiserIds,
    fields: fields
);

foreach (var account in response.Data.List)
{
    Console.WriteLine($"Account: {account.Name}");
    Console.WriteLine($"Status: {account.Status}");
    Console.WriteLine($"Currency: {account.Currency}");
    Console.WriteLine($"Balance: {account.Balance}");
}
```

## Error Handling

All methods can throw the following exceptions:

- `ArgumentException` - When required parameters are null or empty
- `SdkException` - When the API returns an error response
- `ApiException` - When there's an HTTP-level error

### Example Error Handling

```csharp
try
{
    var response = await client.AdAccount.GetAuthorizedAccountsAsync(
        accessToken, appId, secret);
    
    // Process successful response
    foreach (var account in response.Data.List)
    {
        // Handle each account
    }
}
catch (SdkException ex)
{
    Console.WriteLine($"API Error: {ex.Message}");
    Console.WriteLine($"Error Code: {ex.ErrorCode}");
    Console.WriteLine($"Request ID: {ex.RequestId}");
}
catch (ArgumentException ex)
{
    Console.WriteLine($"Invalid parameters: {ex.Message}");
}
```

## Best Practices

1. **Use specific fields**: When calling `GetAccountDetailsAsync`, specify only the fields you need to improve performance and reduce response size.

2. **Handle rate limits**: Implement proper retry logic with exponential backoff for rate-limited requests.

3. **Cache account information**: Account details don't change frequently, so consider caching the results to reduce API calls.

4. **Validate advertiser IDs**: Ensure advertiser IDs are valid before making API calls to avoid unnecessary errors.

5. **Monitor request IDs**: Always log the `RequestId` from responses for debugging and support purposes.

## Complete Example

```csharp
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;

// Initialize the client
var client = TikTokBusinessApiClient.Create("your-access-token");

try
{
    // Step 1: Get authorized accounts
    var authorizedAccounts = await client.AdAccount.GetAuthorizedAccountsAsync(
        "your-access-token", "your-app-id", "your-app-secret");
    
    if (authorizedAccounts.Data?.List?.Count > 0)
    {
        // Step 2: Get detailed info for the first account
        var firstAccountId = authorizedAccounts.Data.List[0].AdvertiserId;
        var advertiserIds = new List<string> { firstAccountId };
        
        var accountDetails = await client.AdAccount.GetAccountDetailsAsync(
            "your-access-token", advertiserIds);
        
        var account = accountDetails.Data.List[0];
        Console.WriteLine($"Account Name: {account.Name}");
        Console.WriteLine($"Status: {account.Status}");
        Console.WriteLine($"Currency: {account.Currency}");
        Console.WriteLine($"Balance: {account.Balance}");
    }
}
catch (SdkException ex)
{
    Console.WriteLine($"API Error: {ex.Message}");
}
finally
{
    client.Dispose();
}
```

## API Versions

The AdAccountApi uses TikTok Business API v1.3. Key differences from v1.2:

- `app_id` parameter is now string type (was number in v1.2)
- `advertiser_id` in responses is now string type (was number in v1.2)
- Enhanced field options and response structure

For more information, refer to the [TikTok Business API documentation](https://business-api.tiktok.com/portal/docs).
