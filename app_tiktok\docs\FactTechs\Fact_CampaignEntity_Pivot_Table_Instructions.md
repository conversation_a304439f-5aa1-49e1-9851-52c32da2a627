# Implementation Guide: Syncfusion Pivot Table for Fact Campaign

## Overview

This guide provides step-by-step instructions to implement a Syncfusion Pivot Table for TikTok campaign performance analysis using the Fact_ChienDich (Campaign Facts) data model.

## Prerequisites

-   ASP.NET Core application with ABP Framework
-   Syncfusion ES5 license (Essential Studio)
-   TikTok Business API data synchronization in place
-   Fact_ChienDich table implemented following Star schema design

## Step 1: Setup Syncfusion Dependencies

Syncfusion is already installed in Project. Don not need to config

## Step 2: Data Structure Preparation

### 2.1 Define Fact Campaign Data Model

Create JavaScript object structure matching your Fact_ChienDich table:

```javascript
const factCampaignData = [
    {
        DateKey: 20250701,
        Date: '2025-07-01',
        CampaignId: 'CMP001',
        CampaignName: 'Summer Fashion Sale',
        AdvertiserId: 'ADV001',
        AdvertiserName: 'Fashion Store',
        BcId: 'BC001',
        BusinessCenter: 'TikTok Shop VN',
        ObjectiveType: 'CONVERSIONS',
        CampaignDedicateType: 'VIDEO',
        Spend: 5000000,
        Impressions: 900000,
        Clicks: 45000,
        Conversion: 1350,
        CostPerConversion: 3703.7,
        ConversionRate: 3.0,
        Result: 1350,
        CostPerResult: 3703.7,
        OnsiteShoppingRoas: 3.24,
        TotalOnsiteShoppingValue: 16200000,
        OnsiteShopping: 810,
        CostPerOnsiteShopping: 6172.84,
        ValuePerOnsiteShopping: 20000,
        Currency: 'VND',
    },
    // ... more data rows
];
```

### 2.2 Data Enhancement

Add calculated fields and formatting:

```javascript
factCampaignData.forEach((item) => {
    // Calculate KPIs
    item.CTR =
        item.Impressions > 0
            ? ((item.Clicks / item.Impressions) * 100).toFixed(2)
            : 0;
    item.CPC = item.Clicks > 0 ? (item.Spend / item.Clicks).toFixed(0) : 0;
    item.CPM =
        item.Impressions > 0
            ? ((item.Spend / item.Impressions) * 1000).toFixed(0)
            : 0;
    item.ROAS =
        item.Spend > 0
            ? (item.TotalOnsiteShoppingValue / item.Spend).toFixed(2)
            : 0;
    item.Profit = item.TotalOnsiteShoppingValue - item.Spend;
    item.ProfitMargin =
        item.TotalOnsiteShoppingValue > 0
            ? ((item.Profit / item.TotalOnsiteShoppingValue) * 100).toFixed(2)
            : 0;

    // Add date components
    const dateObj = new Date(item.Date);
    item.Year = dateObj.getFullYear();
    item.Month = dateObj.getMonth() + 1;
    item.WeekDay = dateObj.toLocaleDateString('vi-VN', { weekday: 'long' });
    item.IsWeekend = dateObj.getDay() === 0 || dateObj.getDay() === 6;
});
```

## Step 3: Create Pivot Table Component

### 3.1 HTML Container

```html
<div id="FactCampaignPivotTable"></div>
```

### 3.2 Initialize Pivot Table

```javascript
const pivotTableObj = new ej.pivotview.PivotView({
    dataSourceSettings: {
        // Data source
        dataSource: factCampaignData,

        // Row hierarchy - TikTok campaign structure
        rows: [
            { name: 'BusinessCenter', caption: 'Trung tâm kinh doanh' },
            { name: 'AdvertiserName', caption: 'Tài khoản quảng cáo' },
            { name: 'CampaignName', caption: 'Chiến dịch' },
            { name: 'ObjectiveType', caption: 'Mục tiêu' },
        ],

        // Column grouping - Time analysis
        columns: [
            { name: 'Date', caption: 'Ngày' },
            { name: 'WeekDay', caption: 'Thứ' },
        ],

        // Metrics - Key campaign performance indicators
        values: [
            { name: 'Spend', caption: 'Chi phí (VND)', type: 'Sum' },
            { name: 'Impressions', caption: 'Lượt hiển thị', type: 'Sum' },
            { name: 'Clicks', caption: 'Lượt nhấp', type: 'Sum' },
            { name: 'Conversion', caption: 'Chuyển đổi', type: 'Sum' },
            {
                name: 'TotalOnsiteShoppingValue',
                caption: 'Doanh thu (VND)',
                type: 'Sum',
            },
            { name: 'OnsiteShopping', caption: 'Lượt mua Shop', type: 'Sum' },
            { name: 'ROAS', caption: 'ROAS', type: 'Avg' },
            { name: 'CTR', caption: 'Tỷ lệ nhấp (%)', type: 'Avg' },
            {
                name: 'ConversionRate',
                caption: 'Tỷ lệ chuyển đổi (%)',
                type: 'Avg',
            },
            { name: 'CPC', caption: 'Chi phí/nhấp (VND)', type: 'Avg' },
            {
                name: 'CPM',
                caption: 'Chi phí/1000 hiển thị (VND)',
                type: 'Avg',
            },
            {
                name: 'CostPerConversion',
                caption: 'Chi phí/chuyển đổi (VND)',
                type: 'Avg',
            },
        ],

        // Filters
        filters: [
            { name: 'Currency', caption: 'Tiền tệ' },
            { name: 'PerformanceStatus', caption: 'Trạng thái hiệu suất' },
            { name: 'ROASStatus', caption: 'Trạng thái ROAS' },
            { name: 'ConversionStatus', caption: 'Trạng thái chuyển đổi' },
            { name: 'IsWeekend', caption: 'Cuối tuần' },
        ],

        // Formatting
        formatSettings: [
            { name: 'Spend', format: 'N0', currency: 'VND' },
            { name: 'TotalOnsiteShoppingValue', format: 'N0', currency: 'VND' },
            { name: 'Impressions', format: 'N0' },
            { name: 'Clicks', format: 'N0' },
            { name: 'Conversion', format: 'N0' },
            { name: 'OnsiteShopping', format: 'N0' },
            { name: 'ROAS', format: 'N2' },
            { name: 'CTR', format: 'N2', suffix: '%' },
            { name: 'ConversionRate', format: 'N2', suffix: '%' },
            { name: 'CPC', format: 'N0' },
            { name: 'CPM', format: 'N0' },
            { name: 'CostPerConversion', format: 'N0' },
        ],

        // Enable features
        allowLabelFilter: true,
        allowValueFilter: true,
        enableSorting: true,
        expandAll: false,
    },

    // Pivot table settings
    height: 1000,
    width: '100%',
    showGroupingBar: true,
    showFieldList: true,
    allowExcelExport: true,
    allowPdfExport: true,
    showToolbar: true,

    // Toolbar options
    toolbar: [
        'New',
        'Save',
        'Grid',
        'Chart',
        'Export',
        'SubTotal',
        'GrandTotal',
        'FieldList',
    ],
});

// Render to container
pivotTableObj.appendTo('#FactCampaignPivotTable');
```

## Step 4: Add Performance Features

### 4.1 Conditional Formatting for ROAS and Conversion Rate

```javascript
conditionalFormatSettings: [
    {
        measure: 'ROAS',
        value1: 3.0,
        conditions: 'GreaterThan',
        style: {
            backgroundColor: '#d4edda',
            color: '#155724',
        },
    },
    {
        measure: 'ROAS',
        value1: 2.0,
        value2: 3.0,
        conditions: 'Between',
        style: {
            backgroundColor: '#fff3cd',
            color: '#856404',
        },
    },
    {
        measure: 'ROAS',
        value1: 2.0,
        conditions: 'LessThan',
        style: {
            backgroundColor: '#f8d7da',
            color: '#721c24',
        },
    },
    {
        measure: 'ConversionRate',
        value1: 3.0,
        conditions: 'GreaterThan',
        style: {
            backgroundColor: '#d4edda',
            color: '#155724',
        },
    },
    {
        measure: 'ConversionRate',
        value1: 2.0,
        value2: 3.0,
        conditions: 'Between',
        style: {
            backgroundColor: '#fff3cd',
            color: '#856404',
        },
    },
];
```

### 4.2 Export Functionality

```javascript
// Excel Export
function exportToExcel() {
    if (pivotTableObj) {
        pivotTableObj.excelExport();
    }
}

// PDF Export
function exportToPdf() {
    if (pivotTableObj) {
        pivotTableObj.pdfExport();
    }
}

// Chart View
function showChart() {
    if (pivotTableObj) {
        pivotTableObj.displayOption.view = 'Chart';
    }
}
```

## Step 5: Add Data Integration

### 5.1 API Integration (ASP.NET Core)

```csharp
[HttpGet]
public async Task<IActionResult> GetCampaignData(DateTime? fromDate, DateTime? toDate, string objectiveType = null)
{
    var data = await _factCampaignService.GetDataAsync(fromDate, toDate, objectiveType);
    return Json(data);
}
```

### 5.2 Dynamic Data Loading

```javascript
async function loadCampaignData(filters = {}) {
    try {
        showLoading();
        const response = await fetch('/api/fact-campaign/data', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(filters),
        });

        const data = await response.json();

        // Update pivot table data
        pivotTableObj.dataSourceSettings.dataSource = data;
        pivotTableObj.refresh();

        hideLoading();
    } catch (error) {
        console.error('Error loading data:', error);
        hideLoading();
    }
}
```

## Step 6: Implement UI Controls

### 6.1 Filter Controls

```html
<div class="controls-section">
    <button onclick="exportToExcel()" class="btn btn-success">
        <i class="fas fa-file-excel"></i> Xuất Excel
    </button>
    <button onclick="exportToPdf()" class="btn btn-danger">
        <i class="fas fa-file-pdf"></i> Xuất PDF
    </button>
    <button onclick="refreshData()" class="btn btn-primary">
        <i class="fas fa-sync-alt"></i> Làm mới
    </button>
</div>
```

### 6.2 Performance Level Filtering

```javascript
// Filter by performance level
function filterByPerformance(level) {
    const filterItem = {
        name: 'PerformanceStatus',
        type: 'Include',
        items: [level],
    };

    pivotTableObj.dataSourceSettings.filterSettings = [filterItem];
    pivotTableObj.refresh();
}
```

## Step 7: Performance Optimization

### 7.1 Virtual Scrolling (for large datasets)

```javascript
virtualScrollSettings: {
    allowVirtualScrolling: true;
}
```

### 7.2 Data Compression

```javascript
// Enable data compression for large datasets
dataSourceSettings: {
    enableSorting: true,
    allowMemberFilter: true,
    compress: true
}
```

## Step 8: Campaign-Specific Features

### 8.1 Performance Status Indicators

```javascript
function getPerformanceStatus(roas, conversionRate) {
    if (roas >= 3.0 && conversionRate >= 3.0) return 'High';
    if (roas >= 2.0 && conversionRate >= 2.0) return 'Medium';
    return 'Low';
}

function getROASStatus(roas) {
    if (roas >= 3.0) return 'High';
    if (roas >= 2.0) return 'Medium';
    return 'Low';
}

function getConversionStatus(conversionRate) {
    if (conversionRate >= 3.0) return 'High';
    if (conversionRate >= 2.0) return 'Medium';
    return 'Low';
}
```

### 8.2 Alert System for Low Performance

```javascript
function updateAlerts() {
    const lowROASCampaigns = factCampaignData.filter((item) => item.ROAS < 2.0);
    const lowConversionCampaigns = factCampaignData.filter(
        (item) => item.ConversionRate < 2.0
    );
    const highCPCCampaigns = factCampaignData.filter((item) => item.CPC > 5000);

    let alertsHtml = '';

    if (lowROASCampaigns.length > 0) {
        alertsHtml += `
            <div class="alert-card alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> Cảnh báo ROAS thấp</h6>
                <p>${lowROASCampaigns.length} chiến dịch có ROAS dưới 2.0</p>
            </div>
        `;
    }

    if (lowConversionCampaigns.length > 0) {
        alertsHtml += `
            <div class="alert-card alert-danger">
                <h6><i class="fas fa-exclamation-circle"></i> Cảnh báo tỷ lệ chuyển đổi</h6>
                <p>${lowConversionCampaigns.length} chiến dịch có tỷ lệ chuyển đổi dưới 2%</p>
            </div>
        `;
    }

    if (highCPCCampaigns.length > 0) {
        alertsHtml += `
            <div class="alert-card alert-warning">
                <h6><i class="fas fa-dollar-sign"></i> Cảnh báo chi phí cao</h6>
                <p>${highCPCCampaigns.length} chiến dịch có CPC trên 5,000 VND</p>
            </div>
        `;
    }

    document.getElementById('alerts-container').innerHTML = alertsHtml;
}
```

## Step 9: Testing and Validation

### 9.1 Data Validation

-   Verify all campaign metrics calculate correctly
-   Test drill-down functionality through campaign hierarchy
-   Validate export formats (Excel/PDF)
-   Check responsive behavior on different screen sizes

### 9.2 Performance Testing

-   Test with large datasets (10K+ campaigns)
-   Verify loading times under 3 seconds
-   Test concurrent user access
-   Validate memory usage

## Step 10: Deployment Considerations

### 10.1 Production Settings

```javascript
// Disable development features in production
const isProduction = window.location.hostname !== 'localhost';

const pivotConfig = {
    showFieldList: !isProduction,
    allowConditionalFormatting: true,
    locale: 'vi-VN',
};
```

### 10.2 Security

-   Implement proper authentication for data access
-   Validate user permissions for different campaign levels
-   Sanitize all input parameters
-   Use HTTPS for all API calls

## Business Questions Supported

### 1. Campaign Performance Analysis

```sql
SELECT CampaignName, AdvertiserName,
       SUM(Spend) as TotalSpend,
       SUM(TotalOnsiteShoppingValue) as TotalRevenue,
       AVG(OnsiteShoppingRoas) as AvgROAS,
       AVG(ConversionRate) as AvgConversionRate
FROM Fact_ChienDich
WHERE DateKey >= 20250701
GROUP BY CampaignName, AdvertiserName
ORDER BY AvgROAS DESC
```

### 2. Top Performing Campaigns

```sql
SELECT CampaignName, ObjectiveType,
       SUM(Spend) as TotalSpend,
       SUM(TotalOnsiteShoppingValue) as TotalRevenue,
       SUM(TotalOnsiteShoppingValue)/SUM(Spend) as ROAS
FROM Fact_ChienDich
WHERE TotalOnsiteShoppingValue > 0
GROUP BY CampaignName, ObjectiveType
ORDER BY ROAS DESC
```

### 3. Cost Analysis by Objective

```sql
SELECT ObjectiveType,
       AVG(CostPerConversion) as AvgCostPerConversion,
       AVG(CostPerOnsiteShopping) as AvgCostPerShopping,
       COUNT(DISTINCT CampaignId) as CampaignCount
FROM Fact_ChienDich
GROUP BY ObjectiveType
ORDER BY AvgCostPerConversion
```

## Conclusion

This implementation provides a comprehensive TikTok campaign performance dashboard using Syncfusion Pivot Table with proper data hierarchy, real-time filtering, export capabilities, and performance optimization for production use. The dashboard supports campaign performance analysis, ROAS tracking, conversion rate monitoring, and cost optimization insights.
