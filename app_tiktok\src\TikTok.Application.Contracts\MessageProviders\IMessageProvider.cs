using System.Threading.Tasks;

namespace TikTok.Application.Contracts.MessageProviders
{
    public interface IMessageProvider
    {
        string Name { get; }
        bool IsEnabled { get; }
        Task SendMessageAsync(string message);
        Task SendMessageByChatIdAsync(string message, string chatId) => Task.CompletedTask;
        Task SendMessageAsync(string message, object additionalData);
    }
}