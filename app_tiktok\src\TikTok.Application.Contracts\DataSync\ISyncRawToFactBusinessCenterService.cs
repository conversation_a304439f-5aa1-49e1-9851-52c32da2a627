﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Đồng bộ thông tin BC từ Raw sang Fact
    /// </summary>
    public interface ISyncRawToFactBusinessCenterService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ Business Centers từ Raw sang Fact
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncBusinessCentersAsync();

        /// <summary>
        /// Đồng bộ Business Centers từ Raw sang Fact theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncBusinessCentersAsync(string bcId);

        /// <summary>
        /// Đồng bộ Ad Accounts từ Raw sang Fact
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncAdAccountsAsync();

        /// <summary>
        /// Đồng bộ Ad Accounts từ Raw sang Fact theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncAdAccountsAsync(string bcId);

        /// <summary>
        /// Đồng bộ tất cả dữ liệu từ Raw sang Fact
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncAllAsync();

        /// <summary>
        /// Đồng bộ tất cả dữ liệu từ Raw sang Fact theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<BusinessCenterSyncResult> SyncAllAsync(string bcId);
    }

    /// <summary>
    /// Kết quả đồng bộ Business Center
    /// </summary>
    public class BusinessCenterSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số Business Center đã đồng bộ
        /// </summary>
        public int BusinessCenterCount { get; set; }

        /// <summary>
        /// Số Ad Account đã đồng bộ
        /// </summary>
        public int AdAccountCount { get; set; }

        /// <summary>
        /// Thời gian bắt đầu
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Thời gian kết thúc
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Tổng thời gian thực hiện
        /// </summary>
        public TimeSpan Duration => EndTime - StartTime;

        public override int TotalSynced => BusinessCenterCount + AdAccountCount;
    }
}
