/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for adding a URL property to an ad account
    /// </summary>
    public class UrlPropertyAddRequest
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// Information about the URL property that you want to add and verify ownership for
        /// </summary>
        [JsonPropertyName("url_property_meta")]
        public UrlPropertyMeta UrlPropertyMeta { get; set; } = new UrlPropertyMeta();
    }
}
