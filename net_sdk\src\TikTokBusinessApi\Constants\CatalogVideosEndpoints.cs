/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Catalog Videos endpoints
    /// </summary>
    public static class CatalogVideosEndpoints
    {
        /// <summary>
        /// Upload catalog videos via a file URL endpoint (v1.3)
        /// </summary>
        public const string UploadVideoFile = "/catalog/video/file/";

        /// <summary>
        /// Get the catalog video handling log endpoint (v1.3)
        /// </summary>
        public const string GetVideoLog = "/catalog/video/log/";

        /// <summary>
        /// Get the uploaded catalog videos within a catalog endpoint (v1.3)
        /// </summary>
        public const string GetVideos = "/catalog/video/get/";

        /// <summary>
        /// Delete uploaded catalog videos endpoint (v1.3)
        /// </summary>
        public const string DeleteVideos = "/catalog/video/delete/";
    }
}
