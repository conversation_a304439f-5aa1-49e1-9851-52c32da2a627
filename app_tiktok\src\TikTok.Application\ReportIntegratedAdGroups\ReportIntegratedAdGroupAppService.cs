using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTok.ReportIntegratedAdGroups;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.ReportIntegratedAdGroups
{
    /// <summary>
    /// Service implementation cho báo cáo tích hợp AdGroup
    /// </summary>
    public class ReportIntegratedAdGroupAppService :
        CrudAppService<
            RawReportIntegratedAdGroupEntity,
            ReportIntegratedAdGroupDto,
            Guid,
            GetReportIntegratedAdGroupListDto,
            CreateReportIntegratedAdGroupDto,
            UpdateReportIntegratedAdGroupDto>,
        IReportIntegratedAdGroupAppService
    {
        private readonly IRawReportIntegratedAdGroupRepository _reportIntegratedAdGroupRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="repository">Repository</param>
        /// <param name="reportIntegratedAdGroupRepository">Report Integrated AdGroup Repository</param>
        public ReportIntegratedAdGroupAppService(
            IRepository<RawReportIntegratedAdGroupEntity, Guid> repository,
            IRawReportIntegratedAdGroupRepository reportIntegratedAdGroupRepository) : base(repository)
        {
            _reportIntegratedAdGroupRepository = reportIntegratedAdGroupRepository;

            GetPolicyName = TikTokPermissions.ReportIntegratedAdGroups.Default;
            GetListPolicyName = TikTokPermissions.ReportIntegratedAdGroups.Default;
            CreatePolicyName = TikTokPermissions.ReportIntegratedAdGroups.Create;
            UpdatePolicyName = TikTokPermissions.ReportIntegratedAdGroups.Edit;
            DeletePolicyName = TikTokPermissions.ReportIntegratedAdGroups.Delete;
        }

        /// <summary>
        /// Lấy danh sách báo cáo tích hợp AdGroup
        /// </summary>
        /// <param name="input">Thông tin đầu vào</param>
        /// <returns>Danh sách báo cáo tích hợp AdGroup</returns>
        public override async Task<PagedResultDto<ReportIntegratedAdGroupDto>> GetListAsync(GetReportIntegratedAdGroupListDto input)
        {
            var totalCount = await _reportIntegratedAdGroupRepository.GetCountAsync(
                input.AdvertiserId,
                input.CampaignId,
                input.AdGroupId,
                input.AdGroupName,
                input.StartDate,
                input.EndDate,
                input.Filter);

            var items = await _reportIntegratedAdGroupRepository.GetListAsync(
                input.AdvertiserId,
                input.CampaignId,
                input.AdGroupId,
                input.AdGroupName,
                input.StartDate,
                input.EndDate,
                input.Sorting,
                input.MaxResultCount,
                input.SkipCount,
                input.Filter);

            return new PagedResultDto<ReportIntegratedAdGroupDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<RawReportIntegratedAdGroupEntity>, List<ReportIntegratedAdGroupDto>>(items)
            };
        }

        /// <summary>
        /// Lấy danh sách theo Advertiser IDs và khoảng thời gian
        /// </summary>
        /// <param name="advertiserIds">Danh sách ID tài khoản nhà quảng cáo</param>
        /// <param name="startDate">Ngày bắt đầu</param>
        /// <param name="endDate">Ngày kết thúc</param>
        /// <returns>Danh sách báo cáo tích hợp AdGroup</returns>
        public async Task<List<ReportIntegratedAdGroupDto>> GetByAdvertiserIdsAndDateRangeAsync(
            List<string> advertiserIds,
            DateTime startDate,
            DateTime endDate)
        {
            var items = await _reportIntegratedAdGroupRepository.GetByAdvertiserIdsAndDateRangeAsync(
                advertiserIds,
                startDate,
                endDate);

            return ObjectMapper.Map<List<RawReportIntegratedAdGroupEntity>, List<ReportIntegratedAdGroupDto>>(items);
        }

        /// <summary>
        /// Lấy báo cáo theo AdGroup ID và ngày
        /// </summary>
        /// <param name="adGroupId">ID nhóm quảng cáo</param>
        /// <param name="date">Ngày báo cáo</param>
        /// <returns>Báo cáo tích hợp AdGroup</returns>
        public async Task<ReportIntegratedAdGroupDto?> GetByAdGroupIdAndDateAsync(
            string adGroupId,
            DateTime date)
        {
            var item = await _reportIntegratedAdGroupRepository.GetByAdGroupIdAndDateAsync(adGroupId, date);

            return item != null ? ObjectMapper.Map<RawReportIntegratedAdGroupEntity, ReportIntegratedAdGroupDto>(item) : null;
        }
    }
} 