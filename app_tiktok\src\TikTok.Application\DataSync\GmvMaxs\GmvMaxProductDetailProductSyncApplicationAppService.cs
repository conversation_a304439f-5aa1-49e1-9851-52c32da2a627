using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// Application Service implementation cho việc đồng bộ dữ liệu báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign
    /// </summary>
    public class GmvMaxProductDetailProductSyncApplicationAppService : ApplicationService, IGmvMaxProductDetailProductSyncApplicationAppService
    {
        private readonly IGmvMaxProductDetailProductSyncService _gmvMaxProductDetailProductSyncService;
        private readonly ILogger<GmvMaxProductDetailProductSyncApplicationAppService> _logger;

        public GmvMaxProductDetailProductSyncApplicationAppService(
            IGmvMaxProductDetailProductSyncService gmvMaxProductDetailProductSyncService,
            ILogger<GmvMaxProductDetailProductSyncApplicationAppService> logger)
        {
            _gmvMaxProductDetailProductSyncService = gmvMaxProductDetailProductSyncService;
            _logger = logger;
        }

        /// <summary>
        /// Đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate">Ngày bắt đầu (tùy chọn)</param>
        /// <param name="endDate">Ngày kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxProductDetailProductSyncResult> SyncGmvMaxProductDetailProductAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho BC: {BcId}", bcId);
                
                var result = await _gmvMaxProductDetailProductSyncService.SyncGmvMaxProductDetailProductAsync(bcId, startDate, endDate);
                
                _logger.LogDebug("Hoàn thành đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho BC: {BcId}. Kết quả: {IsSuccess}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}", 
                    bcId, result.IsSuccess, result.TotalSynced, result.NewRecords, result.UpdatedRecords);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho BC: {BcId}", bcId);
                return new GmvMaxProductDetailProductSyncResult
                {
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// Đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxProductDetailProductSyncResult> SyncAllGmvMaxProductDetailProductForAllBcsAsync()
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho tất cả BC");
                
                var result = await _gmvMaxProductDetailProductSyncService.SyncAllGmvMaxProductDetailProductForAllBcsAsync();
                
                _logger.LogDebug("Hoàn thành đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho tất cả BC. Kết quả: {IsSuccess}, Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}", 
                    result.IsSuccess, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho tất cả BC");
                return new GmvMaxProductDetailProductSyncResult
                {
                    ErrorMessage = ex.Message
                };
            }
        }
    }
} 