/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Get Spark Ad Info Models

    /// <summary>
    /// Request for getting info about a Spark Ad post
    /// </summary>
    public class SparkAdInfoRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The authorization code for the Spark Ads post (Required)
        /// </summary>
        [JsonPropertyName("auth_code")]
        [Required]
        public string AuthCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for getting info about a Spark Ad post
    /// </summary>
    public class SparkAdInfoResponse
    {
        /// <summary>
        /// Information about the authorization
        /// </summary>
        [JsonPropertyName("auth_info")]
        public SparkAdAuthInfo? AuthInfo { get; set; }

        /// <summary>
        /// Information about the Spark Ads post
        /// </summary>
        [JsonPropertyName("item_info")]
        public SparkAdItemInfo? ItemInfo { get; set; }

        /// <summary>
        /// Information about the TikTok account
        /// </summary>
        [JsonPropertyName("user_info")]
        public SparkAdUserInfo? UserInfo { get; set; }

        /// <summary>
        /// Information about the video post
        /// When item_type is CAROUSEL, the values for the parameters within the video_info object will be null.
        /// </summary>
        [JsonPropertyName("video_info")]
        public SparkAdVideoInfo? VideoInfo { get; set; }
    }

    #endregion

    #region Authorize Spark Ad Models

    /// <summary>
    /// Request for applying an authorization code
    /// </summary>
    public class SparkAdAuthorizeRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The authorization code for the Spark Ads post (Required)
        /// </summary>
        [JsonPropertyName("auth_code")]
        [Required]
        public string AuthCode { get; set; } = string.Empty;

        /// <summary>
        /// If this post is a duet or stitch from another post or it mentions another video,
        /// you also need to get the authorization code for the original post.
        /// Use this field to specify the authorization code for the original post.
        /// Note: Currently, the post can only mention at most one video.
        /// </summary>
        [JsonPropertyName("original_post_auth_code")]
        public string? OriginalPostAuthCode { get; set; }
    }

    #endregion

    #region Get Spark Ad Posts Models

    /// <summary>
    /// Request for getting Spark Ad posts
    /// </summary>
    public class SparkAdPostsRequest
    {
        /// <summary>
        /// The Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The types of Spark Ads posts to filter
        /// Enum values: VIDEO (video post), CAROUSEL (photo post)
        /// Default value: ["VIDEO", "CAROUSEL"]
        /// </summary>
        [JsonPropertyName("item_types")]
        public List<string>? ItemTypes { get; set; }

        /// <summary>
        /// The text or TikTok post ID as a keyword to search TikTok posts by
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }

        /// <summary>
        /// The current page number. The default value is 1. The value must be ≥ 1.
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// The page size. The default value: 20. The value must be in the range of 1-50.
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Response for getting Spark Ad posts
    /// </summary>
    public class SparkAdPostsResponse
    {
        /// <summary>
        /// Information about the list of Spark Ads posts
        /// </summary>
        [JsonPropertyName("list")]
        public List<SparkAdPostInfo> List { get; set; } = new();

        /// <summary>
        /// The information about pagination
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    #endregion

    #region Unbind Spark Ad Models

    /// <summary>
    /// Request for unbinding a Spark Ad post
    /// </summary>
    public class SparkAdUnbindRequest
    {
        /// <summary>
        /// The advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the Spark Ads post (Required)
        /// </summary>
        [JsonPropertyName("item_id")]
        [Required]
        public string ItemId { get; set; } = string.Empty;
    }

    #endregion

    #region Common Spark Ad Models

    /// <summary>
    /// Authorization information for Spark Ads
    /// </summary>
    public class SparkAdAuthInfo
    {
        /// <summary>
        /// The time when the authorization code expires (UTC+0), in the format of 2017-01-01 00:00:00
        /// </summary>
        [JsonPropertyName("auth_end_time")]
        public string AuthEndTime { get; set; } = string.Empty;

        /// <summary>
        /// The time when the authorization code becomes valid (UTC+0), in the format of 2017-01-01 00:00:00
        /// </summary>
        [JsonPropertyName("auth_start_time")]
        public string AuthStartTime { get; set; } = string.Empty;

        /// <summary>
        /// The time when the authorization starts (UTC+0), in the format of 2017-01-01 00:00:00
        /// </summary>
        [JsonPropertyName("invite_start_time")]
        public string? InviteStartTime { get; set; }

        /// <summary>
        /// The authorization status
        /// </summary>
        [JsonPropertyName("ad_auth_status")]
        public string? AdAuthStatus { get; set; }
    }

    /// <summary>
    /// Information about the Spark Ads post
    /// </summary>
    public class SparkAdItemInfo
    {
        /// <summary>
        /// The authorization code for the Spark Ads post
        /// </summary>
        [JsonPropertyName("auth_code")]
        public string AuthCode { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the Spark Ads post
        /// </summary>
        [JsonPropertyName("item_id")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// The type of Spark Ads post
        /// Enum values: VIDEO (video post), CAROUSEL (photo post)
        /// </summary>
        [JsonPropertyName("item_type")]
        public string? ItemType { get; set; }

        /// <summary>
        /// The description of the Spark Ads post
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; } = string.Empty;

        /// <summary>
        /// The status of the Spark Ads post
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Information about the photo post
        /// When item_type is VIDEO, the value of this field will be null.
        /// </summary>
        [JsonPropertyName("carousel_info")]
        public SparkAdCarouselInfo? CarouselInfo { get; set; }

        /// <summary>
        /// If the post is a stitch from another post, the item ID of the original post
        /// </summary>
        [JsonPropertyName("stitch_original_item_id")]
        public string? StitchOriginalItemId { get; set; }

        /// <summary>
        /// If the post is a duet from another post, the item ID of the original post
        /// </summary>
        [JsonPropertyName("duet_original_item_id")]
        public string? DuetOriginalItemId { get; set; }

        /// <summary>
        /// Whether this is a duet or stitch post that is created based on another duet or stitch post
        /// </summary>
        [JsonPropertyName("is_multi_duet_stitch")]
        public bool? IsMultiDuetStitch { get; set; }

        /// <summary>
        /// If the post mentions another video, the item IDs of the original posts
        /// Note: The post can only mention at most one video.
        /// </summary>
        [JsonPropertyName("mentioned_item_ids")]
        public List<string>? MentionedItemIds { get; set; }

        /// <summary>
        /// Information about anchors in the post (video)
        /// </summary>
        [JsonPropertyName("anchor_list")]
        public List<SparkAdAnchorInfo>? AnchorList { get; set; }
    }

    /// <summary>
    /// Information about the TikTok account
    /// </summary>
    public class SparkAdUserInfo
    {
        /// <summary>
        /// The user name of the TikTok account
        /// </summary>
        [JsonPropertyName("tiktok_name")]
        public string TikTokName { get; set; } = string.Empty;

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string IdentityId { get; set; } = string.Empty;

        /// <summary>
        /// Identity Type
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string IdentityType { get; set; } = string.Empty;
    }

    /// <summary>
    /// Information about the video post
    /// </summary>
    public class SparkAdVideoInfo
    {
        /// <summary>
        /// Video bit rate, in bps
        /// </summary>
        [JsonPropertyName("bit_rate")]
        public int? BitRate { get; set; }

        /// <summary>
        /// The duration of the video, in seconds
        /// </summary>
        [JsonPropertyName("duration")]
        public float? Duration { get; set; }

        /// <summary>
        /// The size of the video, in bytes
        /// </summary>
        [JsonPropertyName("size")]
        public int? Size { get; set; }

        /// <summary>
        /// The width of the video
        /// </summary>
        [JsonPropertyName("width")]
        public int? Width { get; set; }

        /// <summary>
        /// The height of the video
        /// </summary>
        [JsonPropertyName("height")]
        public int? Height { get; set; }

        /// <summary>
        /// Video ID
        /// </summary>
        [JsonPropertyName("video_id")]
        public string? VideoId { get; set; }

        /// <summary>
        /// Video name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }

        /// <summary>
        /// The URL to the video poster. It is valid for an hour. When it expires, you need to call the endpoint again to get a new URL.
        /// </summary>
        [JsonPropertyName("poster_url")]
        public string? PosterUrl { get; set; }

        /// <summary>
        /// The preview URL for the video. It is valid for an hour. When it expires, you need to call the endpoint again to get a new URL.
        /// </summary>
        [JsonPropertyName("preview_url")]
        public string? PreviewUrl { get; set; }

        /// <summary>
        /// MD5 for the video
        /// </summary>
        [JsonPropertyName("signature")]
        public string? Signature { get; set; }
    }

    /// <summary>
    /// Information about the photo post
    /// </summary>
    public class SparkAdCarouselInfo
    {
        /// <summary>
        /// Information about the images used in the photo post
        /// The images are returned in the same order as they are shown in the photo post.
        /// </summary>
        [JsonPropertyName("image_info")]
        public List<SparkAdImageInfo> ImageInfo { get; set; } = new();

        /// <summary>
        /// Information about the music used in the photo post
        /// If the photo post is directly published in the TikTok App without music, the value of this field will be null.
        /// </summary>
        [JsonPropertyName("music_info")]
        public SparkAdMusicInfo? MusicInfo { get; set; }
    }

    /// <summary>
    /// Information about images in photo posts
    /// </summary>
    public class SparkAdImageInfo
    {
        /// <summary>
        /// The URL of the image
        /// Validity period: 90 days.
        /// </summary>
        [JsonPropertyName("image_url")]
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// The height of the image, measured in pixels
        /// </summary>
        [JsonPropertyName("image_height")]
        public int? ImageHeight { get; set; }

        /// <summary>
        /// The width of the image, measured in pixels
        /// </summary>
        [JsonPropertyName("image_width")]
        public int? ImageWidth { get; set; }
    }

    /// <summary>
    /// Information about music in photo posts
    /// </summary>
    public class SparkAdMusicInfo
    {
        /// <summary>
        /// The URL of the music
        /// Validity period: 90 days.
        /// </summary>
        [JsonPropertyName("music_url")]
        public string MusicUrl { get; set; } = string.Empty;

        /// <summary>
        /// The duration of the music, in seconds
        /// </summary>
        [JsonPropertyName("music_duration")]
        public int? MusicDuration { get; set; }
    }

    /// <summary>
    /// Information about anchors in the post
    /// </summary>
    public class SparkAdAnchorInfo
    {
        /// <summary>
        /// Anchor ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Anchor title
        /// </summary>
        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Anchor status
        /// Enum values: CHECK_ING (anchor review in progress), CHECK_FAILED (anchor review failed), CHECK_SUCCESS (anchor review successful)
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Regions where the ad can be deployed
        /// </summary>
        [JsonPropertyName("product_regions")]
        public List<string>? ProductRegions { get; set; }

        /// <summary>
        /// Item URL
        /// </summary>
        [JsonPropertyName("url")]
        public string? Url { get; set; }

        /// <summary>
        /// Returned only when the post contains a product anchor link
        /// The SPU ID of the product that the post is associated with.
        /// </summary>
        [JsonPropertyName("spu_id")]
        public string? SpuId { get; set; }

        /// <summary>
        /// Returned only when the post contains a product anchor link
        /// The name of the product that the post is associated with.
        /// </summary>
        [JsonPropertyName("spu_name")]
        public string? SpuName { get; set; }

        /// <summary>
        /// Returned only when the post contains a product anchor link
        /// The ID of the TikTok Shop that the product belongs to.
        /// </summary>
        [JsonPropertyName("store_id")]
        public string? StoreId { get; set; }
    }

    /// <summary>
    /// Information about a Spark Ad post in the list
    /// </summary>
    public class SparkAdPostInfo
    {
        /// <summary>
        /// Information about the Spark Ads post
        /// </summary>
        [JsonPropertyName("item_info")]
        public SparkAdItemInfo? ItemInfo { get; set; }

        /// <summary>
        /// Information about the video post
        /// </summary>
        [JsonPropertyName("video_info")]
        public SparkAdVideoInfo? VideoInfo { get; set; }

        /// <summary>
        /// Information about the TikTok account
        /// </summary>
        [JsonPropertyName("user_info")]
        public SparkAdUserInfo? UserInfo { get; set; }

        /// <summary>
        /// Information about the authorization
        /// </summary>
        [JsonPropertyName("auth_info")]
        public SparkAdAuthInfo? AuthInfo { get; set; }
    }

    #endregion
}
