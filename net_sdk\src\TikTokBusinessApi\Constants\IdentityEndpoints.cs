/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Identity endpoints
    /// </summary>
    public static class IdentityEndpoints
    {
        /// <summary>
        /// Create an identity endpoint (v1.3)
        /// </summary>
        public const string CreateIdentity = "/identity/create/";

        /// <summary>
        /// Delete an identity endpoint (v1.3)
        /// </summary>
        public const string DeleteIdentity = "/identity/delete/";

        /// <summary>
        /// Get the identity list endpoint (v1.3)
        /// </summary>
        public const string GetIdentityList = "/identity/get/";

        /// <summary>
        /// Get info about an identity endpoint (v1.3)
        /// </summary>
        public const string GetIdentityInfo = "/identity/info/";

        /// <summary>
        /// Get posts under an identity endpoint (v1.3)
        /// </summary>
        public const string GetIdentityVideos = "/identity/video/get/";

        /// <summary>
        /// Get live videos under an identity endpoint (v1.3)
        /// </summary>
        public const string GetIdentityLiveVideos = "/identity/live/get/";

        /// <summary>
        /// Get music authorization info of a video endpoint (v1.3)
        /// </summary>
        public const string GetMusicAuthorization = "/identity/music/authorization/";

        /// <summary>
        /// Get info about TikTok posts endpoint (v1.3)
        /// </summary>
        public const string GetVideoInfo = "/identity/video/info/";
    }
}
