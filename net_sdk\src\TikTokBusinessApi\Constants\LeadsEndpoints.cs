/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Leads endpoints
    /// </summary>
    public static class LeadsEndpoints
    {
        /// <summary>
        /// Create a test lead endpoint (v1.3)
        /// </summary>
        public const string CreateTestLead = "/page/lead/mock/create/";

        /// <summary>
        /// Get a test lead endpoint (v1.3)
        /// </summary>
        public const string GetTestLead = "/page/lead/mock/get/";

        /// <summary>
        /// Delete a test lead endpoint (v1.3)
        /// </summary>
        public const string DeleteTestLead = "/page/lead/mock/delete/";

        /// <summary>
        /// Create a lead download task endpoint (v1.3)
        /// </summary>
        public const string CreateLeadDownloadTask = "/page/lead/task/";

        /// <summary>
        /// Download leads endpoint (v1.3)
        /// </summary>
        public const string DownloadLeads = "/page/lead/task/download/";

        /// <summary>
        /// Get form libraries endpoint (v1.3)
        /// </summary>
        public const string GetFormLibraries = "/page/library/get/";

        /// <summary>
        /// Migrate leads to a Business Center endpoint (v1.3)
        /// </summary>
        public const string MigrateLeadsToBC = "/page/library/transfer/";

        /// <summary>
        /// Get the fields of an Instant Form endpoint (v1.3)
        /// </summary>
        public const string GetInstantFormFields = "/page/field/get/";

        /// <summary>
        /// Get fields of an Instant Form or direct message leads endpoint (v1.3)
        /// </summary>
        public const string GetLeadFields = "/lead/field/get/";

        /// <summary>
        /// Get an Instant Form lead or a direct message lead endpoint (v1.3)
        /// </summary>
        public const string GetLead = "/lead/get/";
    }
}
