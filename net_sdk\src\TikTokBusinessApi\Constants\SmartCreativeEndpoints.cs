/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Smart Creative endpoints
    /// </summary>
    public static class SmartCreativeEndpoints
    {
        /// <summary>
        /// Create Smart Creative ads endpoint (v1.3)
        /// </summary>
        public const string CreateSmartCreativeAds = "/ad/aco/create/";

        /// <summary>
        /// Get Smart Creative materials endpoint (v1.3)
        /// </summary>
        public const string GetSmartCreativeMaterials = "/ad/aco/get/";

        /// <summary>
        /// Update Smart Creative materials endpoint (v1.3)
        /// </summary>
        public const string UpdateSmartCreativeMaterials = "/ad/aco/update/";

        /// <summary>
        /// Update the statuses of Smart Creative materials endpoint (v1.3)
        /// </summary>
        public const string UpdateSmartCreativeMaterialStatuses = "/ad/aco/material_status/update/";
    }
}
