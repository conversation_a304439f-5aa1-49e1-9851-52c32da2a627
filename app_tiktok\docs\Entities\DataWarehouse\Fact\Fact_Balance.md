# FactBalanceEntity

## Thông tin bảng dữ liệu

-   **Table**: `FactBalances`
-   **TablePrefix**: `Fact_`
-   **TableName**: `Fact_FactBalances`
-   **DbSchema**: `TikTok`

## Mô tả

Fact table lưu trữ thông tin số dư và ngân sách của tài khoản quảng cáo và Business Center theo thời gian. Dữ liệu được tổng hợp từ RawBalanceAdAccountEntity và RawBalanceBusinessCenterEntity để theo dõi xu hướng số dư và cảnh báo sớm khi sắp hết tiền.

**Lưu ý**: Bảng này bao gồm các trường tiền tệ chuẩn hóa (VND và USD) để hỗ trợ báo cáo và tổng hợp chính xác khi có nhiều loại tiền tệ khác nhau.

## C<PERSON>u tr<PERSON><PERSON> b<PERSON>

| Tên <PERSON>              | Kiểu dữ liệu | Bắt bu<PERSON> | Độ dà<PERSON> | Mô tả                                                                  |
| ---------------------- | ------------ | -------- | ------ | ---------------------------------------------------------------------- |
| Id                     | Guid         | ✓        | -      | ID duy nhất của bản ghi (Primary Key)                                  |
| DimDateId              | int          | ✓        | -      | Khóa ngoại liên kết với Dim_Date (YYYYMMDD)                            |
| DimAdAccountId         | Guid?        | ✗        | -      | Khóa ngoại liên kết với Dim_AdAccount (null nếu Type = BusinessCenter) |
| DimBusinessCenterId    | Guid         | ✓        | -      | Khóa ngoại liên kết với Dim_BusinessCenter                             |
| Type                   | string       | ✓        | 20     | Loại số dư: "AdAccount", "BusinessCenter"                              |
| EntityId               | string       | ✓        | 100    | ID của entity (AdvertiserId hoặc BcId)                                 |
| EntityName             | string       | ✓        | 200    | Tên của entity                                                         |
| AccountBalance         | decimal      | ✗        | 18,2   | Tổng số dư tài khoản                                                   |
| AccountBalanceVND      | decimal      | ✗        | 18,2   | Tổng số dư tài khoản (VND)                                             |
| AccountBalanceUSD      | decimal      | ✗        | 18,2   | Tổng số dư tài khoản (USD)                                             |
| ValidAccountBalance    | decimal      | ✗        | 18,2   | Số dư tài khoản hợp lệ                                                 |
| ValidAccountBalanceVND | decimal      | ✗        | 18,2   | Số dư tài khoản hợp lệ (VND)                                           |
| ValidAccountBalanceUSD | decimal      | ✗        | 18,2   | Số dư tài khoản hợp lệ (USD)                                           |
| FrozenBalance          | decimal      | ✗        | 18,2   | Số dư bị đóng băng                                                     |
| FrozenBalanceVND       | decimal      | ✗        | 18,2   | Số dư bị đóng băng (VND)                                               |
| FrozenBalanceUSD       | decimal      | ✗        | 18,2   | Số dư bị đóng băng (USD)                                               |
| Tax                    | decimal      | ✗        | 18,2   | Thuế                                                                   |
| TaxVND                 | decimal      | ✗        | 18,2   | Thuế (VND)                                                             |
| TaxUSD                 | decimal      | ✗        | 18,2   | Thuế (USD)                                                             |
| CashBalance            | decimal      | ✗        | 18,2   | Số dư tiền mặt                                                         |
| CashBalanceVND         | decimal      | ✗        | 18,2   | Số dư tiền mặt (VND)                                                   |
| CashBalanceUSD         | decimal      | ✗        | 18,2   | Số dư tiền mặt (USD)                                                   |
| ValidCashBalance       | decimal      | ✗        | 18,2   | Số dư tiền mặt hợp lệ                                                  |
| ValidCashBalanceVND    | decimal      | ✗        | 18,2   | Số dư tiền mặt hợp lệ (VND)                                            |
| ValidCashBalanceUSD    | decimal      | ✗        | 18,2   | Số dư tiền mặt hợp lệ (USD)                                            |
| GrantBalance           | decimal      | ✗        | 18,2   | Số dư phiếu giảm giá/voucher                                           |
| GrantBalanceVND        | decimal      | ✗        | 18,2   | Số dư phiếu giảm giá/voucher (VND)                                     |
| GrantBalanceUSD        | decimal      | ✗        | 18,2   | Số dư phiếu giảm giá/voucher (USD)                                     |
| ValidGrantBalance      | decimal      | ✗        | 18,2   | Số dư phiếu giảm giá/voucher hợp lệ                                    |
| ValidGrantBalanceVND   | decimal      | ✗        | 18,2   | Số dư phiếu giảm giá/voucher hợp lệ (VND)                              |
| ValidGrantBalanceUSD   | decimal      | ✗        | 18,2   | Số dư phiếu giảm giá/voucher hợp lệ (USD)                              |
| TransferableAmount     | decimal      | ✗        | 18,2   | Số tiền có thể chuyển (chỉ AdAccount)                                  |
| TransferableAmountVND  | decimal      | ✗        | 18,2   | Số tiền có thể chuyển (VND) (chỉ AdAccount)                            |
| TransferableAmountUSD  | decimal      | ✗        | 18,2   | Số tiền có thể chuyển (USD) (chỉ AdAccount)                            |
| Budget                 | decimal      | ✗        | 18,2   | Ngân sách (chỉ AdAccount)  
| BudgetMode             | int          | ✗        | -      | Chế độ ngân sách của tài khoản quảng cáo               |                                            |
| BudgetVND              | decimal      | ✗        | 18,2   | Ngân sách (VND) (chỉ AdAccount)                                        |
| BudgetUSD              | decimal      | ✗        | 18,2   | Ngân sách (USD) (chỉ AdAccount)                                        |
| BudgetCost             | decimal      | ✗        | 18,2   | Chi phí ngân sách đã sử dụng (chỉ AdAccount)                           |
| BudgetCostVND          | decimal      | ✗        | 18,2   | Chi phí ngân sách đã sử dụng (VND) (chỉ AdAccount)                     |
| BudgetCostUSD          | decimal      | ✗        | 18,2   | Chi phí ngân sách đã sử dụng (USD) (chỉ AdAccount)                     |
| BudgetRemaining        | decimal      | ✗        | 18,2   | Ngân sách còn lại (chỉ AdAccount)                                      |
| BudgetRemainingVND     | decimal      | ✗        | 18,2   | Ngân sách còn lại (VND) (chỉ AdAccount)                                |
| BudgetRemainingUSD     | decimal      | ✗        | 18,2   | Ngân sách còn lại (USD) (chỉ AdAccount)                                |
| Currency               | string       | ✓        | 10     | Tiền tệ theo mã ISO 4217                                               |
| Timezone               | string       | ✗        | 50     | Múi giờ                                                                |
| Date                   | DateTime     | ✓        | -      | Ngày số dư (UTC)                                                       |
| CreationTime           | DateTime     | ✓        | -      | Thời gian tạo bản ghi (Audit)                                          |
| CreatorId              | Guid?        | ✗        | -      | ID người tạo (Audit)                                                   |
| LastModificationTime   | DateTime?    | ✗        | -      | Thời gian sửa đổi cuối (Audit)                                         |
| LastModifierId         | Guid?        | ✗        | -      | ID người sửa đổi cuối (Audit)                                          |

## Grain Definition

-   **Mỗi dòng đại diện cho**: Số dư của 1 tài khoản quảng cáo hoặc 1 Business Center tại 1 thời điểm
-   **Unique Key**: (EntityId, Type, Date)

## Type Values

-   **"AdAccount"**: Dữ liệu từ RawBalanceAdAccountEntity
-   **"BusinessCenter"**: Dữ liệu từ RawBalanceBusinessCenterEntity

## Relationships

### Foreign Keys

-   **DimDateId** → DimDates.Id
-   **DimAdAccountId** → DimAdAccounts.Id (nullable)
-   **DimBusinessCenterId** → DimBusinessCenters.Id

## Measures (Các chỉ số có thể tính toán)

### Measures cơ bản (Original Currency)

-   **AccountBalance**: Tổng số dư
-   **ValidAccountBalance**: Số dư khả dụng
-   **FrozenBalance**: Số dư bị đóng băng
-   **CashBalance**: Số dư tiền mặt
-   **GrantBalance**: Số dư voucher

### Measures chuẩn hóa (VND)

-   **AccountBalanceVND**: Tổng số dư (VND)
-   **ValidAccountBalanceVND**: Số dư khả dụng (VND)
-   **FrozenBalanceVND**: Số dư bị đóng băng (VND)
-   **CashBalanceVND**: Số dư tiền mặt (VND)
-   **GrantBalanceVND**: Số dư voucher (VND)

### Measures chuẩn hóa (USD)

-   **AccountBalanceUSD**: Tổng số dư (USD)
-   **ValidAccountBalanceUSD**: Số dư khả dụng (USD)
-   **FrozenBalanceUSD**: Số dư bị đóng băng (USD)
-   **CashBalanceUSD**: Số dư tiền mặt (USD)
-   **GrantBalanceUSD**: Số dư voucher (USD)

### Measures tính toán

-   **AvailableBalance**: ValidAccountBalance - FrozenBalance
-   **AvailableBalanceVND**: ValidAccountBalanceVND - FrozenBalanceVND
-   **AvailableBalanceUSD**: ValidAccountBalanceUSD - FrozenBalanceUSD
-   **CashRatio**: CashBalance / AccountBalance
-   **CashRatioVND**: CashBalanceVND / AccountBalanceVND
-   **CashRatioUSD**: CashBalanceUSD / AccountBalanceUSD
-   **GrantRatio**: GrantBalance / AccountBalance
-   **GrantRatioVND**: GrantBalanceVND / AccountBalanceVND
-   **GrantRatioUSD**: GrantBalanceUSD / AccountBalanceUSD
-   **FrozenRatio**: FrozenBalance / AccountBalance
-   **FrozenRatioVND**: FrozenBalanceVND / AccountBalanceVND
-   **FrozenRatioUSD**: FrozenBalanceUSD / AccountBalanceUSD
-   **BudgetUtilization**: BudgetCost / Budget (chỉ AdAccount)
-   **BudgetUtilizationVND**: BudgetCostVND / BudgetVND (chỉ AdAccount)
-   **BudgetUtilizationUSD**: BudgetCostUSD / BudgetUSD (chỉ AdAccount)
-   **DaysToEmpty**: ValidAccountBalance / AvgDailySpend
-   **DaysToEmptyVND**: ValidAccountBalanceVND / AvgDailySpendVND
-   **DaysToEmptyUSD**: ValidAccountBalanceUSD / AvgDailySpendUSD

## Indexes

### Primary Index

-   **PK_Fact_SoDu**: Id (Clustered)

### Foreign Key Indexes

-   **IX_Fact_SoDu_DimDateId**: DimDateId
-   **IX_Fact_SoDu_DimAdAccountId**: DimAdAccountId
-   **IX_Fact_SoDu_DimBusinessCenterId**: DimBusinessCenterId

### Composite Indexes

-   **IX_Fact_SoDu_EntityId_Type_Date**: (EntityId, Type, Date) UNIQUE
-   **IX_Fact_SoDu_Type_Date**: (Type, DimDateId)

### Measure Indexes

-   **IX_Fact_SoDu_AccountBalance**: AccountBalance
-   **IX_Fact_SoDu_ValidAccountBalance**: ValidAccountBalance
-   **IX_Fact_SoDu_AccountBalanceVND**: AccountBalanceVND
-   **IX_Fact_SoDu_ValidAccountBalanceVND**: ValidAccountBalanceVND
-   **IX_Fact_SoDu_AccountBalanceUSD**: AccountBalanceUSD
-   **IX_Fact_SoDu_ValidAccountBalanceUSD**: ValidAccountBalanceUSD

## ETL Mapping

### Source AdAccount → Target

-   **RawBalanceAdAccountEntity.AdvertiserId** → EntityId
-   **RawBalanceAdAccountEntity.AdvertiserName** → EntityName
-   **"AdAccount"** → Type
-   **RawBalanceAdAccountEntity.AccountBalance** → AccountBalance
-   **ConvertToVND(RawBalanceAdAccountEntity.AccountBalance, Currency, Date)** → AccountBalanceVND
-   **ConvertToUSD(RawBalanceAdAccountEntity.AccountBalance, Currency, Date)** → AccountBalanceUSD
-   **RawBalanceAdAccountEntity.ValidAccountBalance** → ValidAccountBalance
-   **ConvertToVND(RawBalanceAdAccountEntity.ValidAccountBalance, Currency, Date)** → ValidAccountBalanceVND
-   **ConvertToUSD(RawBalanceAdAccountEntity.ValidAccountBalance, Currency, Date)** → ValidAccountBalanceUSD
-   **RawBalanceAdAccountEntity.FrozenBalance** → FrozenBalance
-   **ConvertToVND(RawBalanceAdAccountEntity.FrozenBalance, Currency, Date)** → FrozenBalanceVND
-   **ConvertToUSD(RawBalanceAdAccountEntity.FrozenBalance, Currency, Date)** → FrozenBalanceUSD
-   **RawBalanceAdAccountEntity.Tax** → Tax
-   **ConvertToVND(RawBalanceAdAccountEntity.Tax, Currency, Date)** → TaxVND
-   **ConvertToUSD(RawBalanceAdAccountEntity.Tax, Currency, Date)** → TaxUSD
-   **RawBalanceAdAccountEntity.CashBalance** → CashBalance
-   **ConvertToVND(RawBalanceAdAccountEntity.CashBalance, Currency, Date)** → CashBalanceVND
-   **ConvertToUSD(RawBalanceAdAccountEntity.CashBalance, Currency, Date)** → CashBalanceUSD
-   **RawBalanceAdAccountEntity.ValidCashBalance** → ValidCashBalance
-   **ConvertToVND(RawBalanceAdAccountEntity.ValidCashBalance, Currency, Date)** → ValidCashBalanceVND
-   **ConvertToUSD(RawBalanceAdAccountEntity.ValidCashBalance, Currency, Date)** → ValidCashBalanceUSD
-   **RawBalanceAdAccountEntity.GrantBalance** → GrantBalance
-   **ConvertToVND(RawBalanceAdAccountEntity.GrantBalance, Currency, Date)** → GrantBalanceVND
-   **ConvertToUSD(RawBalanceAdAccountEntity.GrantBalance, Currency, Date)** → GrantBalanceUSD
-   **RawBalanceAdAccountEntity.ValidGrantBalance** → ValidGrantBalance
-   **ConvertToVND(RawBalanceAdAccountEntity.ValidGrantBalance, Currency, Date)** → ValidGrantBalanceVND
-   **ConvertToUSD(RawBalanceAdAccountEntity.ValidGrantBalance, Currency, Date)** → ValidGrantBalanceUSD
-   **RawBalanceAdAccountEntity.TransferableAmount** → TransferableAmount
-   **ConvertToVND(RawBalanceAdAccountEntity.TransferableAmount, Currency, Date)** → TransferableAmountVND
-   **ConvertToUSD(RawBalanceAdAccountEntity.TransferableAmount, Currency, Date)** → TransferableAmountUSD
-   **RawBalanceAdAccountEntity.Budget** → Budget
-   **ConvertToVND(RawBalanceAdAccountEntity.Budget, Currency, Date)** → BudgetVND
-   **ConvertToUSD(RawBalanceAdAccountEntity.Budget, Currency, Date)** → BudgetUSD
-   **RawBalanceAdAccountEntity.BudgetCost** → BudgetCost
-   **ConvertToVND(RawBalanceAdAccountEntity.BudgetCost, Currency, Date)** → BudgetCostVND
-   **ConvertToUSD(RawBalanceAdAccountEntity.BudgetCost, Currency, Date)** → BudgetCostUSD
-   **RawBalanceAdAccountEntity.BudgetRemaining** → BudgetRemaining
-   **ConvertToVND(RawBalanceAdAccountEntity.BudgetRemaining, Currency, Date)** → BudgetRemainingVND
-   **ConvertToUSD(RawBalanceAdAccountEntity.BudgetRemaining, Currency, Date)** → BudgetRemainingUSD

### Source BusinessCenter → Target

-   **RawBalanceBusinessCenterEntity.BcId** → EntityId
-   **Lookup BC Name** → EntityName
-   **"BusinessCenter"** → Type
-   **RawBalanceBusinessCenterEntity.AccountBalance** → AccountBalance
-   **ConvertToVND(RawBalanceBusinessCenterEntity.AccountBalance, Currency, Date)** → AccountBalanceVND
-   **ConvertToUSD(RawBalanceBusinessCenterEntity.AccountBalance, Currency, Date)** → AccountBalanceUSD
-   **RawBalanceBusinessCenterEntity.ValidAccountBalance** → ValidAccountBalance
-   **ConvertToVND(RawBalanceBusinessCenterEntity.ValidAccountBalance, Currency, Date)** → ValidAccountBalanceVND
-   **ConvertToUSD(RawBalanceBusinessCenterEntity.ValidAccountBalance, Currency, Date)** → ValidAccountBalanceUSD
-   **RawBalanceBusinessCenterEntity.FrozenBalance** → FrozenBalance
-   **ConvertToVND(RawBalanceBusinessCenterEntity.FrozenBalance, Currency, Date)** → FrozenBalanceVND
-   **ConvertToUSD(RawBalanceBusinessCenterEntity.FrozenBalance, Currency, Date)** → FrozenBalanceUSD
-   **RawBalanceBusinessCenterEntity.Tax** → Tax
-   **ConvertToVND(RawBalanceBusinessCenterEntity.Tax, Currency, Date)** → TaxVND
-   **ConvertToUSD(RawBalanceBusinessCenterEntity.Tax, Currency, Date)** → TaxUSD
-   **RawBalanceBusinessCenterEntity.CashBalance** → CashBalance
-   **ConvertToVND(RawBalanceBusinessCenterEntity.CashBalance, Currency, Date)** → CashBalanceVND
-   **ConvertToUSD(RawBalanceBusinessCenterEntity.CashBalance, Currency, Date)** → CashBalanceUSD
-   **RawBalanceBusinessCenterEntity.ValidCashBalance** → ValidCashBalance
-   **ConvertToVND(RawBalanceBusinessCenterEntity.ValidCashBalance, Currency, Date)** → ValidCashBalanceVND
-   **ConvertToUSD(RawBalanceBusinessCenterEntity.ValidCashBalance, Currency, Date)** → ValidCashBalanceUSD
-   **RawBalanceBusinessCenterEntity.GrantBalance** → GrantBalance
-   **ConvertToVND(RawBalanceBusinessCenterEntity.GrantBalance, Currency, Date)** → GrantBalanceVND
-   **ConvertToUSD(RawBalanceBusinessCenterEntity.GrantBalance, Currency, Date)** → GrantBalanceUSD
-   **RawBalanceBusinessCenterEntity.ValidGrantBalance** → ValidGrantBalance
-   **ConvertToVND(RawBalanceBusinessCenterEntity.ValidGrantBalance, Currency, Date)** → ValidGrantBalanceVND
-   **ConvertToUSD(RawBalanceBusinessCenterEntity.ValidGrantBalance, Currency, Date)** → ValidGrantBalanceUSD

### Business Rules

1. Chỉ load dữ liệu có Date >= ngày hiện tại - 1 năm
2. Nếu Type = "BusinessCenter", các trường budget để null
3. Nếu Type = "AdAccount", AdAccountId phải có giá trị
4. Currency được chuẩn hóa theo uppercase
5. **Tỷ giá chuyển đổi**: Sử dụng tỷ giá ngày Date để chuyển đổi sang VND và USD
6. **Làm tròn**: Các giá trị VND và USD được làm tròn đến 2 chữ số thập phân

## Validation Rules

-   **Type** phải là "AdAccount" hoặc "BusinessCenter"
-   **EntityId** không được null hoặc empty
-   **ValidAccountBalance** <= AccountBalance
-   **ValidAccountBalanceVND** <= AccountBalanceVND
-   **ValidAccountBalanceUSD** <= AccountBalanceUSD
-   **FrozenBalance** >= 0
-   **FrozenBalanceVND** >= 0
-   **FrozenBalanceUSD** >= 0
-   Nếu Type = "AdAccount" thì DimAdAccountId không được null
-   Nếu Type = "BusinessCenter" thì Budget, BudgetCost, BudgetRemaining phải null
-   **Tỷ giá chuyển đổi**: AccountBalanceVND ≈ AccountBalance \* ExchangeRateToVND(Date)
-   **Tỷ giá chuyển đổi**: AccountBalanceUSD ≈ AccountBalance \* ExchangeRateToUSD(Date)

## Business Questions được hỗ trợ

1. **Tài khoản sắp hết tiền (cảnh báo) - VND**

    ```sql
    SELECT EntityId, EntityName, ValidAccountBalanceVND
    FROM Fact_SoDu
    WHERE Type = 'AdAccount' AND ValidAccountBalanceVND < 1000000
    AND DateKey = (SELECT MAX(DateKey) FROM Fact_SoDu)
    ```

2. **Tài khoản sắp hết tiền (cảnh báo) - USD**

    ```sql
    SELECT EntityId, EntityName, ValidAccountBalanceUSD
    FROM Fact_SoDu
    WHERE Type = 'AdAccount' AND ValidAccountBalanceUSD < 100
    AND DateKey = (SELECT MAX(DateKey) FROM Fact_SoDu)
    ```

3. **Xu hướng số dư theo thời gian - VND**

    ```sql
    SELECT DimDateId, SUM(ValidAccountBalanceVND) as TotalBalanceVND
    FROM FactBalances WHERE Type = 'AdAccount'
    GROUP BY DimDateId ORDER BY DimDateId
    ```

4. **Xu hướng số dư theo thời gian - USD**

    ```sql
    SELECT DimDateId, SUM(ValidAccountBalanceUSD) as TotalBalanceUSD
    FROM FactBalances WHERE Type = 'AdAccount'
    GROUP BY DimDateId ORDER BY DimDateId
    ```

5. **Tỷ lệ sử dụng ngân sách - VND**

    ```sql
    SELECT EntityName,
           BudgetVND, BudgetCostVND,
           BudgetCostVND/BudgetVND as UtilizationVND
    FROM Fact_SoDu
    WHERE Type = 'AdAccount' AND BudgetVND > 0
    ```

6. **Tỷ lệ sử dụng ngân sách - USD**

    ```sql
    SELECT EntityName,
           BudgetUSD, BudgetCostUSD,
           BudgetCostUSD/BudgetUSD as UtilizationUSD
    FROM Fact_SoDu
    WHERE Type = 'AdAccount' AND BudgetUSD > 0
    ```

7. **Số dư theo Business Center - VND**

    ```sql
    SELECT bc.Name as BCName,
           SUM(f.ValidAccountBalanceVND) as TotalBalanceVND
    FROM Fact_SoDu f
    JOIN Dim_BusinessCenter bc ON f.BusinessCenterKey = bc.BusinessCenterKey
    GROUP BY bc.Name
    ```

8. **Số dư theo Business Center - USD**
    ```sql
    SELECT bc.Name as BCName,
           SUM(f.ValidAccountBalanceUSD) as TotalBalanceUSD
    FROM Fact_SoDu f
    JOIN Dim_BusinessCenter bc ON f.BusinessCenterKey = bc.BusinessCenterKey
    GROUP BY bc.Name
    ```

## Alerting Rules

### Cảnh báo số dư thấp - VND

-   **Cảnh báo vàng**: ValidAccountBalanceVND < 30% của mức trung bình 30 ngày qua
-   **Cảnh báo đỏ**: ValidAccountBalanceVND < 7 ngày chi tiêu trung bình
-   **Cảnh báo khẩn cấp**: ValidAccountBalanceVND < 1 ngày chi tiêu trung bình

### Cảnh báo số dư thấp - USD

-   **Cảnh báo vàng**: ValidAccountBalanceUSD < 30% của mức trung bình 30 ngày qua
-   **Cảnh báo đỏ**: ValidAccountBalanceUSD < 7 ngày chi tiêu trung bình
-   **Cảnh báo khẩn cấp**: ValidAccountBalanceUSD < 1 ngày chi tiêu trung bình

### Cảnh báo ngân sách - VND

-   **Cảnh báo vàng**: BudgetUtilizationVND > 80%
-   **Cảnh báo đỏ**: BudgetUtilizationVND > 95%

### Cảnh báo ngân sách - USD

-   **Cảnh báo vàng**: BudgetUtilizationUSD > 80%
-   **Cảnh báo đỏ**: BudgetUtilizationUSD > 95%

## Performance Considerations

-   **Partitioning**: Partition theo DimDateId (monthly) và Type
-   **Compression**: Sử dụng Page compression
-   **Archive**: Archive dữ liệu cũ hơn 2 năm
-   **Real-time**: Cập nhật realtime để hỗ trợ alerting
-   **Indexes**: Thêm indexes cho các trường VND và USD để tối ưu query

## Data Quality Checks

-   **Completeness**: Đảm bảo mỗi tài khoản có số dư mỗi ngày
-   **Accuracy**: ValidAccountBalance <= AccountBalance
-   **Accuracy**: ValidAccountBalanceVND <= AccountBalanceVND
-   **Accuracy**: ValidAccountBalanceUSD <= AccountBalanceUSD
-   **Consistency**: Tổng số dư Business Center >= tổng số dư Ad Accounts
-   **Consistency**: Tổng số dư Business Center VND >= tổng số dư Ad Accounts VND
-   **Consistency**: Tổng số dư Business Center USD >= tổng số dư Ad Accounts USD
-   **Timeliness**: Dữ liệu được cập nhật trong vòng 2 giờ
-   **Currency Conversion**: Kiểm tra độ chính xác của tỷ giá chuyển đổi VND và USD
