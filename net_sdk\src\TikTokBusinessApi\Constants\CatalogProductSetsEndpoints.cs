/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Catalog Product Sets endpoints
    /// </summary>
    public static class CatalogProductSetsEndpoints
    {
        /// <summary>
        /// Get product sets endpoint (v1.3)
        /// </summary>
        public const string GetProductSets = "/v1.3/catalog/set/get/";

        /// <summary>
        /// Get products in a product set endpoint (v1.3)
        /// </summary>
        public const string GetProductsInSet = "/v1.3/catalog/set/product/get/";

        /// <summary>
        /// Create a product set endpoint (v1.3)
        /// </summary>
        public const string CreateProductSet = "/v1.3/catalog/set/create/";

        /// <summary>
        /// Update a product set endpoint (v1.3)
        /// </summary>
        public const string UpdateProductSet = "/v1.3/catalog/set/update/";

        /// <summary>
        /// Delete product sets endpoint (v1.3)
        /// </summary>
        public const string DeleteProductSets = "/v1.3/catalog/set/delete/";
    }
}
