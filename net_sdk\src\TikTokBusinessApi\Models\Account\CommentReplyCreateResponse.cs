/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for creating a comment reply
    /// </summary>
    public class CommentReplyCreateResponse
    {
        /// <summary>
        /// Unique identifier for the newly created reply
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string CommentId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for the comment to which the reply was created
        /// </summary>
        [JsonPropertyName("parent_comment_id")]
        public string ParentCommentId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for the owned video the reply was created on
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Developer application and TikTok account scoped unique identifier for the user that made the reply
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// A globally unique identifier assigned to each user commenting
        /// </summary>
        [JsonPropertyName("unique_identifier")]
        public string? UniqueIdentifier { get; set; }

        /// <summary>
        /// Unix/Epoch date-time when the reply was created
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;

        /// <summary>
        /// Text content of the reply
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; } = string.Empty;
    }
}
