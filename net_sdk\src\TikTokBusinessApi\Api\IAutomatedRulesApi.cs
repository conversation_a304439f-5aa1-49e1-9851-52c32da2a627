/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API AutomatedRules operations
    /// </summary>
    public interface IAutomatedRulesApi
    {
        /// <summary>
        /// Create automated rules
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing rules to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created rule IDs</returns>
        //Task<Response<AutomatedRulesResponse>> CreateRulesAsync(
        //    string accessToken,
        //    AutomatedRulesCreateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get rules by rule IDs
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="ruleIds">IDs of the rules to get</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing rule information</returns>
        Task<Response<AutomatedRulesGetResponse>> GetRulesByIdAsync(
            string accessToken,
            string advertiserId,
            List<string> ruleIds,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get rules based on filter values
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="filtering">Filtering options</param>
        /// <param name="tzone">User timezone</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing filtered rules</returns>
        Task<Response<AutomatedRulesGetResponse>> GetRulesByFiltersAsync(
            string accessToken,
            string advertiserId,
            AutomatedRulesFiltering? filtering = null,
            string? tzone = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get execution results of rules based on filter values
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="filtering">Filtering options</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="lang">Language of error messages</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing rule execution results</returns>
        Task<Response<AutomatedRulesGetResponse>> GetRuleResultsAsync(
            string accessToken,
            string advertiserId,
            AutomatedRulesFiltering? filtering = null,
            int page = 1,
            int pageSize = 10,
            string? lang = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get rule execution results by execution IDs
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing result details to get</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing result details</returns>
        Task<Response<AutomatedRulesResultDetailsResponse>> GetResultDetailsAsync(
            string accessToken,
            AutomatedRulesResultDetailsBody body,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Update the details of rules
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing rules to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated rule IDs</returns>
        //Task<Response<AutomatedRulesResponse>> UpdateRulesAsync(
        //    string accessToken,
        //    AutomatedRulesUpdateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Turn on, turn off, or delete a group of rules
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing rule status updates</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated rule IDs</returns>
        //Task<Response<AutomatedRulesResponse>> UpdateRuleStatusesAsync(
        //    string accessToken,
        //    AutomatedRulesStatusUpdateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Bind objects to an existing rule, or unbind objects from a rule
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing bind/unbind information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> BindUnbindRulesAsync(
        //    string accessToken,
        //    AutomatedRulesBindBody body,
        //    CancellationToken cancellationToken = default);
    }
}
