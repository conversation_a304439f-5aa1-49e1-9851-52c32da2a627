/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for creating a comment
    /// </summary>
    public class CommentCreateResponse
    {
        /// <summary>
        /// Unique identifier for the newly created comment
        /// </summary>
        [JsonPropertyName("comment_id")]
        public string CommentId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for the owned video the comment was created on
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// Developer application and TikTok account scoped unique identifier for the user that made the comment
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// A globally unique identifier assigned to each user commenting
        /// </summary>
        [JsonPropertyName("unique_identifier")]
        public string? UniqueIdentifier { get; set; }

        /// <summary>
        /// Unix/Epoch date-time when the comment was created
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;

        /// <summary>
        /// Text content of the comment
        /// </summary>
        [JsonPropertyName("text")]
        public string Text { get; set; } = string.Empty;
    }
}
