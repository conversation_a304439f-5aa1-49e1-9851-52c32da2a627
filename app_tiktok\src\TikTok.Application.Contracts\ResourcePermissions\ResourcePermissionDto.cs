using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.ResourcePermissions
{
    /// <summary>
    /// DTO cho quyền tài nguyên
    /// </summary>
    public class ResourcePermissionDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID của người dùng
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// ID của tài nguyên
        /// </summary>
        public string ResourceId { get; set; }

        /// <summary>
        /// Quyền được cấp
        /// </summary>
        public string Permission { get; set; }

        /// <summary>
        /// Loại tài nguyên TikTok
        /// </summary>
        public string ResourceType { get; set; }
    }
}
