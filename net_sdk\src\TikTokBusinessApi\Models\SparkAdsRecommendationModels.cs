/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Business Video Recommendations Models

    /// <summary>
    /// Request for getting Spark Ads video recommendations for a Business Account
    /// </summary>
    public class BusinessVideoRecommendationsRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account (Required)
        /// Pass the value of the open_id field returned in the response of /tt_user/oauth2/token/ to this field
        /// </summary>
        [JsonPropertyName("business_id")]
        [Required]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// The ID list of videos for which you wish to receive recommendations only
        /// Max size: 20
        /// </summary>
        [JsonPropertyName("video_ids")]
        public List<string>? VideoIds { get; set; }

        /// <summary>
        /// The ID list of videos for which you don't wish to receive recommendations
        /// No max size limit
        /// </summary>
        [JsonPropertyName("exclude_video_ids")]
        public List<string>? ExcludeVideoIds { get; set; }

        /// <summary>
        /// The time when the TikTok video posts were published
        /// Enum values: PAST_ONE_MONTH, PAST_THREE_MONTHS, PAST_SIX_MONTHS
        /// Default value: PAST_SIX_MONTHS
        /// </summary>
        [JsonPropertyName("time")]
        public string? Time { get; set; }

        /// <summary>
        /// The advertising objective for which to generate recommendations
        /// Enum values: REACH, TRAFFIC, VIDEO_VIEWS, ENGAGEMENT, APP_PROMOTION, LEAD_GENERATION, WEB_CONVERSIONS, PRODUCT_SALES
        /// Default value: REACH
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string? ObjectiveType { get; set; }
    }

    /// <summary>
    /// Response for getting Spark Ads video recommendations for a Business Account
    /// </summary>
    public class BusinessVideoRecommendationsResponse
    {
        /// <summary>
        /// A list of up to 20 TikTok video posts with their Spark Ads recommendation results
        /// </summary>
        [JsonPropertyName("videos")]
        public List<BusinessVideoRecommendation> Videos { get; set; } = new();
    }

    /// <summary>
    /// Business video recommendation information
    /// </summary>
    public class BusinessVideoRecommendation
    {
        /// <summary>
        /// The recommendation level, which indicates the video's suitability for Spark Ads
        /// Enum values: HIGH, MEDIUM, LOW
        /// </summary>
        [JsonPropertyName("recommendation_level")]
        public string RecommendationLevel { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for the TikTok video post
        /// </summary>
        [JsonPropertyName("item_id")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// An embeddable link for this TikTok video
        /// </summary>
        [JsonPropertyName("embed_url")]
        public string EmbedUrl { get; set; } = string.Empty;

        /// <summary>
        /// Temporary URL for the video thumbnail
        /// </summary>
        [JsonPropertyName("thumbnail_url")]
        public string ThumbnailUrl { get; set; } = string.Empty;

        /// <summary>
        /// The time when the video was published, in the format of an Epoch/Unix timestamp in seconds (UTC time)
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;

        /// <summary>
        /// Total number of video views for the video
        /// </summary>
        [JsonPropertyName("video_views")]
        public long VideoViews { get; set; }

        /// <summary>
        /// Total number of likes on the video
        /// </summary>
        [JsonPropertyName("likes")]
        public long Likes { get; set; }

        /// <summary>
        /// Total number of comments on the video
        /// </summary>
        [JsonPropertyName("comments")]
        public long Comments { get; set; }

        /// <summary>
        /// Total number of times the video has been shared
        /// </summary>
        [JsonPropertyName("shares")]
        public long Shares { get; set; }

        /// <summary>
        /// Percentage breakdown of the top 10 countries or regions where viewers of the video are located
        /// Max size: 10
        /// </summary>
        [JsonPropertyName("audience_countries")]
        public List<AudienceCountry> AudienceCountries { get; set; } = new();
    }

    #endregion

    #region TTO Video Recommendations Models

    /// <summary>
    /// Request for getting Spark Ads video recommendations for a TTO account
    /// </summary>
    public class TTOVideoRecommendationsRequest
    {
        /// <summary>
        /// The ID of a TTO Creator Marketplace account (Required)
        /// </summary>
        [JsonPropertyName("tcm_account_id")]
        [Required]
        public string TcmAccountId { get; set; } = string.Empty;

        /// <summary>
        /// The ID list of video posts for which you wish to receive recommendations only
        /// Max size: 100
        /// </summary>
        [JsonPropertyName("video_ids")]
        public List<string>? VideoIds { get; set; }

        /// <summary>
        /// The ID list of video posts for which you don't wish to receive recommendations
        /// Max size: 100
        /// </summary>
        [JsonPropertyName("exclude_video_ids")]
        public List<string>? ExcludeVideoIds { get; set; }

        /// <summary>
        /// The time when the TikTok video posts were published
        /// Enum values: PAST_ONE_MONTH, PAST_THREE_MONTHS, PAST_SIX_MONTHS
        /// </summary>
        [JsonPropertyName("time")]
        public string? Time { get; set; }

        /// <summary>
        /// The advertising objective for which to generate recommendations
        /// Enum values: REACH, TRAFFIC, VIDEO_VIEWS, ENGAGEMENT, APP_PROMOTION, LEAD_GENERATION, WEB_CONVERSIONS, PRODUCT_SALES
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string? ObjectiveType { get; set; }
    }

    /// <summary>
    /// Response for getting Spark Ads video recommendations for a TTO account
    /// </summary>
    public class TTOVideoRecommendationsResponse
    {
        /// <summary>
        /// A list of up to 100 TikTok video posts with their Spark Ads recommendation results
        /// </summary>
        [JsonPropertyName("videos")]
        public List<TTOVideoRecommendation> Videos { get; set; } = new();
    }

    /// <summary>
    /// TTO video recommendation information
    /// </summary>
    public class TTOVideoRecommendation
    {
        /// <summary>
        /// The recommendation level, which indicates the suitability of the video post for Spark Ads
        /// Enum values: HIGH, MEDIUM, LOW
        /// </summary>
        [JsonPropertyName("recommendation_level")]
        public string RecommendationLevel { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for the TikTok video post
        /// </summary>
        [JsonPropertyName("item_id")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// The handle name of the creator who uploaded the video post
        /// </summary>
        [JsonPropertyName("creator_handle")]
        public string CreatorHandle { get; set; } = string.Empty;

        /// <summary>
        /// An embeddable link for this TikTok video post
        /// </summary>
        [JsonPropertyName("embed_url")]
        public string EmbedUrl { get; set; } = string.Empty;

        /// <summary>
        /// Temporary URL for the video post thumbnail
        /// </summary>
        [JsonPropertyName("thumbnail_url")]
        public string ThumbnailUrl { get; set; } = string.Empty;

        /// <summary>
        /// The time when the video post was published, in the format of an Epoch/Unix timestamp in seconds (UTC time)
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;
    }

    #endregion

    #region All-in-One Spark Ad Creation Models

    /// <summary>
    /// Request for creating a campaign, an ad group, and a Spark Ad in one step
    /// </summary>
    public class AllInOneSparkAdRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Campaign name
        /// Length limit: 512 characters. Emojis are not supported.
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// Advertising objective
        /// Enum values: REACH, TRAFFIC, VIDEO_VIEWS, ENGAGEMENT
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string? ObjectiveType { get; set; }

        /// <summary>
        /// Ad group name
        /// Length limit: 512 characters. Emojis are not supported.
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdgroupName { get; set; }

        /// <summary>
        /// Saved Audience ID
        /// Either saved_audience_id or location_ids is required
        /// </summary>
        [JsonPropertyName("saved_audience_id")]
        public string? SavedAudienceId { get; set; }

        /// <summary>
        /// IDs of the locations that you want to target
        /// Either saved_audience_id or location_ids is required
        /// Max size: 3,000
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string>? LocationIds { get; set; }

        /// <summary>
        /// Gender that you want to target
        /// Enum values: GENDER_FEMALE, GENDER_MALE, GENDER_UNLIMITED
        /// </summary>
        [JsonPropertyName("gender")]
        public string? Gender { get; set; }

        /// <summary>
        /// Age groups you want to target
        /// </summary>
        [JsonPropertyName("age_groups")]
        public List<string>? AgeGroups { get; set; }

        /// <summary>
        /// Ad group budget mode
        /// Enum values: BUDGET_MODE_TOTAL, BUDGET_MODE_DAY
        /// </summary>
        [JsonPropertyName("budget_mode")]
        public string? BudgetMode { get; set; }

        /// <summary>
        /// Ad group budget
        /// </summary>
        [JsonPropertyName("budget")]
        public float? Budget { get; set; }

        /// <summary>
        /// Schedule type
        /// Enum values: SCHEDULE_START_END, SCHEDULE_FROM_NOW
        /// </summary>
        [JsonPropertyName("schedule_type")]
        public string? ScheduleType { get; set; }

        /// <summary>
        /// Schedule start time (UTC+0), in the format of YYYY-MM-DD HH:MM:SS
        /// </summary>
        [JsonPropertyName("schedule_start_time")]
        public string? ScheduleStartTime { get; set; }

        /// <summary>
        /// Schedule end time (UTC+0), in the format of YYYY-MM-DD HH:MM:SS
        /// Required when schedule_type is SCHEDULE_START_END
        /// </summary>
        [JsonPropertyName("schedule_end_time")]
        public string? ScheduleEndTime { get; set; }

        /// <summary>
        /// The measurable results you'd like to drive with your ads
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string? OptimizationGoal { get; set; }

        /// <summary>
        /// Frequency, the maximum number of times a user can see your ad within a given period
        /// Required when objective_type is REACH
        /// Range: 1-1,000
        /// </summary>
        [JsonPropertyName("frequency")]
        public int? Frequency { get; set; }

        /// <summary>
        /// Frequency schedule, the duration (in days) over which the frequency cap is applied
        /// Required when objective_type is REACH
        /// Range: 1-30 days
        /// </summary>
        [JsonPropertyName("frequency_schedule")]
        public int? FrequencySchedule { get; set; }

        /// <summary>
        /// The bidding strategy for your ad group
        /// Enum values: BID_TYPE_CUSTOM, BID_TYPE_NO_BID
        /// Default value: BID_TYPE_NO_BID
        /// </summary>
        [JsonPropertyName("bid_type")]
        public string? BidType { get; set; }

        /// <summary>
        /// The average cost per result that you want to achieve
        /// Required when optimization_goal is REACH, CLICK, PAGE_VISIT, or ENGAGED_VIEW and bid_type is BID_TYPE_CUSTOM
        /// </summary>
        [JsonPropertyName("bid_price")]
        public float? BidPrice { get; set; }

        /// <summary>
        /// The target cost per conversion for oCPM (Optimized Cost per Mille)
        /// Required when optimization_goal is TRAFFIC_LANDING_PAGE_VIEW or FOLLOWERS and bid_type is BID_TYPE_CUSTOM
        /// </summary>
        [JsonPropertyName("conversion_bid_price")]
        public float? ConversionBidPrice { get; set; }

        /// <summary>
        /// Ad name
        /// Length limit: 512 characters. Emojis are not supported.
        /// </summary>
        [JsonPropertyName("ad_name")]
        public string? AdName { get; set; }

        /// <summary>
        /// Identity type
        /// Enum values: AUTH_CODE, TT_USER, BC_AUTH_TT
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with
        /// Required when identity_type is BC_AUTH_TT
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// The ID of the TikTok post to be used as a Spark Ad
        /// </summary>
        [JsonPropertyName("tiktok_item_id")]
        public string? TiktokItemId { get; set; }

        /// <summary>
        /// Call-to-action text
        /// Required when optimization_goal is CLICK, TRAFFIC_LANDING_PAGE_VIEW, or PAGE_VISIT
        /// Optional when optimization_goal is REACH or ENGAGED_VIEW
        /// </summary>
        [JsonPropertyName("call_to_action")]
        public string? CallToAction { get; set; }

        /// <summary>
        /// The landing page that users will be redirected to
        /// Required in certain conditions based on optimization_goal
        /// </summary>
        [JsonPropertyName("landing_page_url")]
        public string? LandingPageUrl { get; set; }
    }

    /// <summary>
    /// Response for creating a campaign, an ad group, and a Spark Ad in one step
    /// </summary>
    public class AllInOneSparkAdResponse
    {
        /// <summary>
        /// The ID of the created campaign
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the ad group created within the campaign
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string AdgroupId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the Spark Ad created within the ad group
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string AdId { get; set; } = string.Empty;
    }

    #endregion
}
