using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service interface cho việc đồng bộ dữ liệu báo cáo tích hợp AdAccount
    /// </summary>
    public interface IReportIntegratedAdAccountSyncApplicationAppService : IApplicationService
    {

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdAccount theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> bắt đầu (tùy chọn)</param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<ReportIntegratedAdAccountSyncResult> SyncReportIntegratedAdAccountAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdAccount cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<ReportIntegratedAdAccountSyncResult> SyncAllReportIntegratedAdAccountForAllBcsAsync();
    }
} 