/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Campaign endpoints
    /// </summary>
    public static class CampaignEndpoints
    {
        /// <summary>
        /// Get campaigns endpoint (v1.3)
        /// </summary>
        public const string Get = "/campaign/get/";

        /// <summary>
        /// Create campaign endpoint (v1.3)
        /// </summary>
        public const string Create = "/campaign/create/";

        /// <summary>
        /// Update campaign endpoint (v1.3)
        /// </summary>
        public const string Update = "/campaign/update/";

        /// <summary>
        /// Update campaign status endpoint (v1.3)
        /// </summary>
        public const string UpdateStatus = "/campaign/status/update/";

        /// <summary>
        /// Get campaign quota endpoint (v1.3) - Deprecated
        /// </summary>
        public const string GetQuota = "/campaign/quota/get/";

        /// <summary>
        /// Get campaign quota info endpoint (v1.3)
        /// </summary>
        public const string GetQuotaInfo = "/campaign/quota/info/";

        /// <summary>
        /// Create campaign copy task endpoint (v1.3)
        /// </summary>
        public const string CreateCopyTask = "/campaign/copy/task/create/";

        /// <summary>
        /// Check campaign copy task endpoint (v1.3)
        /// </summary>
        public const string CheckCopyTask = "/campaign/copy/task/check/";
    }
}
