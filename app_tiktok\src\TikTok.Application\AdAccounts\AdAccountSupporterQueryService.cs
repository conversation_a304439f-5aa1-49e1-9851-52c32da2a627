using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Repositories;
using TikTok.ResourceProviders;
using TikTok.ResourceProviders.AdAccounts;
using Volo.Abp.Identity;

namespace TikTok.AdAccounts
{
    /// <summary>
    /// Service implementation để truy vấn dữ liệu người hỗ trợ cho các Activity
    /// </summary>
    public class AdAccountSupporterQueryService : IAdAccountSupporterQueryService
    {
        private readonly IAdAccountRepository _adAccountRepository;
        private readonly IAdAccountResourceProvider _resourceProviderFactory;
        private readonly IIdentityUserRepository _identityUserRepository;

        public AdAccountSupporterQueryService(
            IAdAccountResourceProvider resourceProviderFactory,
            IIdentityUserRepository identityUserRepository,
            IAdAccountRepository adAccountRepository)
        {
            _adAccountRepository = adAccountRepository;
            _resourceProviderFactory = resourceProviderFactory;
            _identityUserRepository = identityUserRepository;
        }

        public async Task<List<AdAccountSupporterListDto>> GetSupportersByAdvertiserIdsAsync(
            List<string> advertiserIds,
            bool includeInactive = false)
        {
            if (advertiserIds == null || !advertiserIds.Any())
            {
                return new();
            }
            var result = new List<AdAccountSupporterListDto>();

            var resources = await _resourceProviderFactory.GetListAsync(new GetResourcePermissionAssignedUserDto()
            {
                ResourceIds = advertiserIds,
            });

            var groupedResources = resources
                .GroupBy(x => x.ResourceId)
                .ToDictionary(g => g.Key, g => g.Select(x => x.UserId).ToList());
            foreach (var kvp in groupedResources)
            {
                var advertiserId = kvp.Key;
                var supporters = kvp.Value;
                result.AddRange(await MapToAdAccountSupporterListDtos(supporters, advertiserId));
            }

            return result;
        }

        public async Task<List<AdAccountSupporterListDto>> GetSupportersByBcIdAsync(
            string bcId,
            bool includeInactive = false)
        {
            if (string.IsNullOrEmpty(bcId))
            {
                return new List<AdAccountSupporterListDto>();
            }

            var advertiserIds = await _adAccountRepository.GetByBcIdAsync(bcId);
            return await GetSupportersByAdvertiserIdsAsync(advertiserIds, includeInactive);
        }

        public async Task<List<AdAccountSupporterListDto>> GetSupportersByBcIdAndAdvertiserIdsAsync(
            string bcId,
            List<string> advertiserIds,
            bool includeInactive = false)
        {
            if (string.IsNullOrEmpty(bcId))
            {
                return new List<AdAccountSupporterListDto>();
            }

            if (advertiserIds == null || !advertiserIds.Any())
            {
                return await GetSupportersByBcIdAsync(bcId, includeInactive);
            }

            return await GetSupportersByAdvertiserIdsAsync(advertiserIds);
        }

        private async Task<List<AdAccountSupporterListDto>> MapToAdAccountSupporterListDtos(List<Guid> supporters, string advertiserId)
        {
            var result = new List<AdAccountSupporterListDto>();
            var users = await _identityUserRepository.GetListByIdsAsync(supporters);
            return users.Select(supporter => new AdAccountSupporterListDto
            {
                AdvertiserId = advertiserId,
                SupporterId = supporter.Id,
                Email = supporter.Email,
                PhoneNumber = supporter.PhoneNumber,
                Name = supporter.Name
            }).ToList();
        }
    }
}