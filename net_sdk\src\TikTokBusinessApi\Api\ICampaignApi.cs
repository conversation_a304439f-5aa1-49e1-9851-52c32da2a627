/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Campaign operations
    /// </summary>
    public interface ICampaignApi
    {
        /// <summary>
        /// Get campaigns with optional filtering and pagination
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="fields">Fields to return (optional)</param>
        /// <param name="filtering">Filtering criteria (optional)</param>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10, max: 1000)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Campaign get response containing list of campaigns and pagination info</returns>
        Task<TikTokApiResponse<CampaignGetResponse>> GetCampaignsAsync(
            string advertiserId,
            List<string>? fields = null,
            CampaignFiltering? filtering = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create new campaigns
        /// </summary>
        /// <param name="request">Campaign creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Campaign create response containing created campaign IDs and details</returns>
        //Task<CampaignCreateResponse> CreateCampaignsAsync(
        //    CampaignCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update existing campaigns
        /// </summary>
        /// <param name="request">Campaign update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Campaign update response containing updated campaign IDs and details</returns>
        //Task<CampaignUpdateResponse> UpdateCampaignsAsync(
        //    CampaignUpdateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update campaign status (enable, disable, or delete)
        /// </summary>
        /// <param name="request">Campaign status update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Campaign status update response containing updated campaign IDs and status</returns>
        //Task<CampaignStatusUpdateResponse> UpdateCampaignStatusAsync(
        //    CampaignStatusUpdateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get campaign quota (Deprecated - use GetCampaignQuotaInfoAsync instead)
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Campaign quota response containing quota information</returns>
        [System.Obsolete("This method is deprecated. Use GetCampaignQuotaInfoAsync instead.")]
        Task<CampaignQuotaGetResponse> GetCampaignQuotaAsync(
            string advertiserId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get campaign quota information
        /// </summary>
        /// <param name="request">Campaign quota info request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Campaign quota info response containing quota information by ad network</returns>
        Task<CampaignQuotaInfoGetResponse> GetCampaignQuotaInfoAsync(
            CampaignQuotaInfoGetRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create campaign copy task
        /// </summary>
        /// <param name="request">Campaign copy task creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Campaign copy task create response containing task ID</returns>
        //Task<CampaignCopyTaskCreateResponse> CreateCampaignCopyTaskAsync(
        //    CampaignCopyTaskCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Check campaign copy task status and results
        /// </summary>
        /// <param name="request">Campaign copy task check request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Campaign copy task check response containing task status and results</returns>
        //Task<CampaignCopyTaskCheckResponse> CheckCampaignCopyTaskAsync(
        //    CampaignCopyTaskCheckRequest request,
        //    CancellationToken cancellationToken = default);
    }
}
