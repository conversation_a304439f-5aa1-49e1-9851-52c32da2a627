using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Customers;
using TikTok.Entities;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Entities.Events;
using Volo.Abp.EventBus;
using Volo.Abp.EventBus.Distributed;

namespace TikTok.EventHandles
{
    public class CustomerEventHandler : ILocalEventHandler<EntityChangedEventData<CustomerEntity>>, ITransientDependency
    {
        private readonly ICustomerCache _cache;

        public CustomerEventHandler(ICustomerCache cache)
        {
            _cache = cache;
        }

        public async Task HandleEventAsync(EntityChangedEventData<CustomerEntity> eventData)
        {
            if (eventData == null || eventData.Entity == null)
            {
                return;
            }

            await _cache.CleanCache();
        }
    }
}
