using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service interface cho việc đồng bộ dữ liệu tài khoản quảng cáo
    /// </summary>
    public interface IAdAccountSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ tài khoản quảng cáo theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AdAccountSyncResult> SyncAdAccountsAsync(string bcId);

        /// <summary>
        /// Đồng bộ tài khoản quảng cáo theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của tài khoản quảng cáo</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AdAccountSyncResult> SyncAdAccountAsync(string advertiserId);

        /// <summary>
        /// Đồng bộ tất cả tài khoản quảng cáo cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AdAccountSyncResult> SyncAllAdAccountsForAllBcsAsync();
    }
}