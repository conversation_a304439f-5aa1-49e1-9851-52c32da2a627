using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.Customers
{
    /// <summary>
    /// DTO cho việc tạo mới khách hàng
    /// </summary>
    public class CreateCustomerDto
    {
        /// <summary>
        /// Mã khách hàng (ví dụ: TT1010)
        /// </summary>
        [Required(ErrorMessage = "Mã khách hàng là bắt buộc")]
        [StringLength(50, ErrorMessage = "Mã khách hàng không được vượt quá 50 ký tự")]
        public string CustomerId { get; set; }

        /// <summary>
        /// Tên khách hàng
        /// </summary>
        [Required(ErrorMessage = "Tên khách hàng là bắt buộc")]
        [StringLength(255, ErrorMessage = "Tên khách hàng không được vượt quá 255 ký tự")]
        public string CustomerName { get; set; }

        /// <summary>
        /// Tên tài khoản
        /// </summary>
        [StringLength(255, ErrorMessage = "Tên tài khoản không được vượt quá 255 ký tự")]
        public string? AccountName { get; set; }

        /// <summary>
        /// Số điện thoại
        /// </summary>
        [StringLength(20, ErrorMessage = "Số điện thoại không được vượt quá 20 ký tự")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// Loại khách hàng (Đại lý, Khách lẻ)
        /// </summary>
        [Required(ErrorMessage = "Loại khách hàng là bắt buộc")]
        public CustomerType CustomerType { get; set; }

        /// <summary>
        /// Trang web
        /// </summary>
        [StringLength(500, ErrorMessage = "Website không được vượt quá 500 ký tự")]
        public string? Website { get; set; }
    }
}
