namespace TikTok.Application.Contracts.MessageProviders
{
    public class MessageProviderOptions
    {
        public string ApplicationName { get; set; } = "TikTok Application";

        public SlackOptions Slack { get; set; } = new();
        public TelegramOptions Telegram { get; set; } = new();
        public ZaloOptions Zalo { get; set; } = new();
    }

    public class SlackOptions
    {
        public bool Enabled { get; set; } = false;
        public string WebhookUrl { get; set; } = string.Empty;
    }

    public class TelegramOptions
    {
        public bool Enabled { get; set; } = false;
        public string BotToken { get; set; } = string.Empty;
        public string ChatId { get; set; } = string.Empty;
    }

    public class ZaloOptions
    {
        public bool Enabled { get; set; } = false;
        public string AccessToken { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
    }
}