/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Identity operations
    /// </summary>
    public interface IIdentityApi
    {
        /// <summary>
        /// Create a Custom User (CUSTOMIZED_USER) identity
        /// </summary>
        /// <param name="request">Identity creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created identity ID</returns>
        //Task<IdentityCreateResponse> CreateIdentityAsync(
        //    IdentityCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a customized user identity (identity_type = CUSTOMIZED_USER)
        /// If you want to edit an identity, you need to first delete the identity using this endpoint, 
        /// and then create a new one using CreateIdentityAsync.
        /// </summary>
        /// <param name="request">Identity deletion request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the asynchronous operation</returns>
        //Task DeleteIdentityAsync(
        //    IdentityDeleteRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a list of identities under an ad account. 
        /// You can filter results by identity type or display name.
        /// </summary>
        /// <param name="request">Identity list request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the list of identities and pagination information</returns>
        Task<IdentityGetResponse> GetIdentityListAsync(
            IdentityGetRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve details about an identity
        /// </summary>
        /// <param name="request">Identity info request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing detailed identity information</returns>
        Task<IdentityInfoResponse> GetIdentityInfoAsync(
            IdentityInfoRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get all posts under an identity
        /// </summary>
        /// <param name="request">Identity videos request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the list of posts under the identity</returns>
        Task<IdentityVideosResponse> GetIdentityVideosAsync(
            IdentityVideosRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get live videos under an identity
        /// </summary>
        /// <param name="request">Identity live videos request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the list of live videos under the identity</returns>
        Task<IdentityLiveVideosResponse> GetIdentityLiveVideosAsync(
            IdentityLiveVideosRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get music authorization information for videos under an identity
        /// </summary>
        /// <param name="request">Music authorization request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing music authorization information</returns>
        Task<MusicAuthorizationResponse> GetMusicAuthorizationAsync(
            MusicAuthorizationRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the information about one or more TikTok posts that you published using the 
        /// AUTH_CODE, TT_USER or BC_AUTH_TT identity
        /// </summary>
        /// <param name="request">Video info request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing detailed information about TikTok posts</returns>
        Task<VideoInfoResponse> GetVideoInfoAsync(
            VideoInfoRequest request,
            CancellationToken cancellationToken = default);
    }
}
