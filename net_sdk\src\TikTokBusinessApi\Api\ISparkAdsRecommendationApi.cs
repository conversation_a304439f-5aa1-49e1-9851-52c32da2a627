/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Spark Ads Recommendation operations
    /// </summary>
    public interface ISparkAdsRecommendationApi
    {
        /// <summary>
        /// Get Spark Ads video recommendations for a Business Account
        /// Use this endpoint to obtain the Spark Ads recommendation results for up to 20 TikTok video posts within your Business Account.
        /// The recommendation results are generated by comparing the video posts with benchmarks that consider data on audience interactions, 
        /// preferences, video post attributes (such as genre and length), and historical performance.
        /// </summary>
        /// <param name="request">Request parameters for getting business video recommendations</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing business video recommendations</returns>
        Task<BusinessVideoRecommendationsResponse> GetBusinessVideoRecommendationsAsync(
            BusinessVideoRecommendationsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get Spark Ads video recommendations for a TTO account
        /// Use this endpoint to obtain the Spark Ads recommendation results for up to 100 video posts associated with your TikTok One (TTO) Creator Marketplace account.
        /// The recommendation results are generated by comparing the videos with benchmarks that consider data on audience interactions, 
        /// preferences, video post attributes (such as genre and length), and historical performance.
        /// </summary>
        /// <param name="request">Request parameters for getting TTO video recommendations</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TTO video recommendations</returns>
        Task<TTOVideoRecommendationsResponse> GetTTOVideoRecommendationsAsync(
            TTOVideoRecommendationsRequest request,
            CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Create a campaign, an ad group, and a Spark Ad in one step
        // /// Use this endpoint to simultaneously set up a campaign, an ad group on the TikTok placement, and a single video Spark Ad within the ad group.
        // /// </summary>
        // /// <param name="request">Request parameters for creating all-in-one Spark Ad</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing the created campaign, ad group, and ad IDs</returns>
        // Task<AllInOneSparkAdResponse> CreateAllInOneSparkAdAsync(
        //     AllInOneSparkAdRequest request,
        //     CancellationToken cancellationToken = default);
    }
}
