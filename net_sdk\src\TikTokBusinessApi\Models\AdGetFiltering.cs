/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Filtering parameters for ad get requests
    /// </summary>
    public class AdGetFiltering
    {
        /// <summary>
        /// A list of Campaign IDs. Max size: 100.
        /// </summary>
        [JsonPropertyName("campaign_ids")]
        public List<string>? CampaignIds { get; set; }

        /// <summary>
        /// The origins (sources) of the campaigns that the ads belong to.
        /// Enum values: PROMOTE, TT_ADS_PLATFORM
        /// Default value: ["TT_ADS_PLATFORM"]
        /// </summary>
        [JsonPropertyName("campaign_system_origins")]
        public List<string>? CampaignSystemOrigins { get; set; }

        /// <summary>
        /// A list of Ad group IDs. Max size: 100.
        /// </summary>
        [JsonPropertyName("adgroup_ids")]
        public List<string>? AdgroupIds { get; set; }

        /// <summary>
        /// A list of Ad IDs. Max size: 100.
        /// </summary>
        [JsonPropertyName("ad_ids")]
        public List<string>? AdIds { get; set; }

        /// <summary>
        /// Primary status. For enum values, see Enumeration -Primary status
        /// </summary>
        [JsonPropertyName("primary_status")]
        public string? PrimaryStatus { get; set; }

        /// <summary>
        /// Ad secondary status. For enum values, see Enumeration - Ad Status - Secondary Status
        /// </summary>
        [JsonPropertyName("secondary_status")]
        public string? SecondaryStatus { get; set; }

        /// <summary>
        /// Advertising Objective. For enum values, see Enumeration - Advertising Objective
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string? ObjectiveType { get; set; }

        /// <summary>
        /// Filter by buying types.
        /// Enum values: AUCTION, RESERVATION_RF, RESERVATION_TOP_VIEW
        /// Default value: ["AUCTION", "RESERVATION_RF"]
        /// </summary>
        [JsonPropertyName("buying_types")]
        public List<string>? BuyingTypes { get; set; }

        /// <summary>
        /// Optimization goal. Enum values: see Enumeration - Optimization Goal
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string? OptimizationGoal { get; set; }

        /// <summary>
        /// The strategy that your creatives will be delivered.
        /// Enum values: CUSTOM (custom), DYNAMIC (automated), SMART_CREATIVE (Smart Creative)
        /// </summary>
        [JsonPropertyName("creative_material_mode")]
        public string? CreativeMaterialMode { get; set; }

        /// <summary>
        /// The destination page type that you want to filter by.
        /// Enum values: APP, TIKTOK_INSTANT_PAGE, WEBSITE, SOCIAL_MEDIA_APP, PHONE_CALL
        /// </summary>
        [JsonPropertyName("destination")]
        public string? Destination { get; set; }

        /// <summary>
        /// Filter ads created later than a specific time, in the format of YYYY-MM-DD HH:MM:SS (UTC time zone)
        /// </summary>
        [JsonPropertyName("creation_filter_start_time")]
        public string? CreationFilterStartTime { get; set; }

        /// <summary>
        /// Filter ads created earlier than a specific time, in the format of YYYY-MM-DD HH:MM:SS (UTC time zone)
        /// </summary>
        [JsonPropertyName("creation_filter_end_time")]
        public string? CreationFilterEndTime { get; set; }

        /// <summary>
        /// Filter ads modified after a specific time, in the format of YYYY-MM-DD HH:MM:SS (UTC time zone)
        /// </summary>
        [JsonPropertyName("modified_after")]
        public string? ModifiedAfter { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGetFiltering class
        /// </summary>
        public AdGetFiltering()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGetFiltering(CampaignIds={CampaignIds?.Count ?? 0} items, AdgroupIds={AdgroupIds?.Count ?? 0} items, AdIds={AdIds?.Count ?? 0} items)";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not AdGetFiltering other)
                return false;

            return Equals(CampaignIds, other.CampaignIds) &&
                   Equals(CampaignSystemOrigins, other.CampaignSystemOrigins) &&
                   Equals(AdgroupIds, other.AdgroupIds) &&
                   Equals(AdIds, other.AdIds) &&
                   PrimaryStatus == other.PrimaryStatus &&
                   SecondaryStatus == other.SecondaryStatus &&
                   ObjectiveType == other.ObjectiveType &&
                   Equals(BuyingTypes, other.BuyingTypes) &&
                   OptimizationGoal == other.OptimizationGoal &&
                   CreativeMaterialMode == other.CreativeMaterialMode &&
                   Destination == other.Destination &&
                   CreationFilterStartTime == other.CreationFilterStartTime &&
                   CreationFilterEndTime == other.CreationFilterEndTime &&
                   ModifiedAfter == other.ModifiedAfter;
        }

        /// <summary>
        /// Returns the hash code for this instance
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            var hash = new HashCode();
            hash.Add(CampaignIds);
            hash.Add(CampaignSystemOrigins);
            hash.Add(AdgroupIds);
            hash.Add(AdIds);
            hash.Add(PrimaryStatus);
            hash.Add(SecondaryStatus);
            hash.Add(ObjectiveType);
            hash.Add(BuyingTypes);
            hash.Add(OptimizationGoal);
            hash.Add(CreativeMaterialMode);
            hash.Add(Destination);
            hash.Add(CreationFilterStartTime);
            hash.Add(CreationFilterEndTime);
            hash.Add(ModifiedAfter);
            return hash.ToHashCode();
        }
    }
}
