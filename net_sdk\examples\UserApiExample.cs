/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the User API
    /// </summary>
    public class UserApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public UserApiExample(string appId, string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(appId, accessToken);
        }

        /// <summary>
        /// Example of getting user information
        /// </summary>
        /// <returns>Task representing the async operation</returns>
        public async Task GetUserInfoExample()
        {
            try
            {
                Console.WriteLine("=== Get User Info Example ===");

                var userInfo = await _client.User.GetUserInfoAsync();

                Console.WriteLine($"Display Name: {userInfo.DisplayName}");
                Console.WriteLine($"Email: {userInfo.Email}");
                Console.WriteLine($"Core User ID: {userInfo.CoreUserId}");
                Console.WriteLine($"Create Time: {userInfo.CreateTime}");
                Console.WriteLine($"Avatar URL: {userInfo.AvatarUrl ?? "Not provided"}");
            }
            catch (TikTokBusinessApiException ex)
            {
                Console.WriteLine($"TikTok API Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
                Console.WriteLine($"Request ID: {ex.RequestId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _client?.Dispose();
        }
    }

    /// <summary>
    /// Program entry point for running examples
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Replace with your actual app ID and access token
            var appId = "your-app-id";
            var accessToken = "your-access-token";

            var example = new UserApiExample(appId, accessToken);

            try
            {
                Console.WriteLine("=== TikTok Business API - User Examples ===\n");

                // Example: Get user information
                await example.GetUserInfoExample();
            }
            finally
            {
                example.Dispose();
            }
        }
    }
}
