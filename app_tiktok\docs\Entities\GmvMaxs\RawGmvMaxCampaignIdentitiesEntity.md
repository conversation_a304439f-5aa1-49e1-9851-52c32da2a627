# RawGmvMaxCampaignIdentitiesEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawGmvMaxCampaignIdentities`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawGmvMaxCampaignIdentities`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho danh sách danh tính (Identity List) của GMV Max Campaign. Chứa thông tin về các tài khoản TikTok được liên kết với chiến dịch GMV Max, bao gồm thông tin ủy quyền và liên kết với Business Center hoặc TikTok Shop.

## Bảng dữ liệu: RawGmvMaxCampaignIdentities

| Tên Field                | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                                                     |
| ------------------------ | ------------ | -------- | ------ | ------------------------------------------------------------------------- |
| Id                       | Guid         | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                     |
| CampaignId               | string       | ✅       | 100    | ID của GMV Max Campaign (Foreign Key)                                     |
| IdentityId               | string       | ✅       | 100    | ID danh tính TikTok                                                       |
| IdentityType             | string       | ✅       | 50     | Loại danh tính (AUTH_CODE, TT_USER, BC_AUTH_TT, TTS_TT)                   |
| IdentityAuthorizedBcId   | string       | ❌       | 100    | ID Business Center được ủy quyền (chỉ có khi identity_type là BC_AUTH_TT) |
| IdentityAuthorizedShopId | string       | ❌       | 100    | ID TikTok Shop được ủy quyền (cho một số identity BC_AUTH_TT)             |
| StoreId                  | string       | ❌       | 100    | ID TikTok Shop (chỉ có khi identity_type là TTS_TT)                       |
| CreationTime             | DateTime     | ✅       | -      | Thời gian tạo bản ghi (Audit)                                             |
| CreatorId                | Guid?        | ❌       | -      | ID người tạo (Audit)                                                      |
| LastModificationTime     | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                            |
| LastModifierId           | Guid?        | ❌       | -      | ID người sửa đổi cuối (Audit)                                             |

## Loại danh tính (Identity Types)

### AUTH_CODE

-   **Mô tả**: Danh tính được ủy quyền thông qua mã xác thực
-   **Đặc điểm**: Sử dụng cho việc ủy quyền tạm thời hoặc đặc biệt
-   **Trường liên quan**: IdentityId

### TT_USER

-   **Mô tả**: Người dùng TikTok trực tiếp
-   **Đặc điểm**: Tài khoản TikTok cá nhân hoặc creator
-   **Trường liên quan**: IdentityId

### BC_AUTH_TT

-   **Mô tả**: Business Center được ủy quyền truy cập TikTok
-   **Đặc điểm**: Business Center có quyền quản lý tài khoản TikTok
-   **Trường liên quan**: IdentityId, IdentityAuthorizedBcId, IdentityAuthorizedShopId

### TTS_TT

-   **Mô tả**: TikTok Shop được liên kết với TikTok
-   **Đặc điểm**: Shop có tài khoản TikTok để đăng nội dung
-   **Trường liên quan**: IdentityId, StoreId

## Cấu trúc dữ liệu

### Thông tin liên kết

-   **CampaignId**: Liên kết với GMV Max Campaign chính
-   **IdentityId**: ID duy nhất của danh tính TikTok
-   **IdentityType**: Phân loại loại danh tính

### Thông tin ủy quyền Business Center

-   **IdentityAuthorizedBcId**: Business Center được cấp quyền truy cập
-   **IdentityAuthorizedShopId**: TikTok Shop được Business Center quản lý

### Thông tin TikTok Shop

-   **StoreId**: ID của TikTok Shop sở hữu danh tính

## Navigation Properties

-   **Campaign**: Liên kết tới RawGmvMaxCampaignsEntity

## Đặc điểm chính

### Quản lý danh tính tập trung

Danh sách Identity cho phép GMV Max Campaign:

-   **Đa dạng nguồn nội dung**: Sử dụng nhiều loại tài khoản TikTok khác nhau
-   **Kiểm soát ủy quyền**: Quản lý quyền truy cập và sử dụng nội dung
-   **Liên kết với Shop**: Kết nối trực tiếp với TikTok Shop để bán hàng
-   **Quản lý tập trung**: Theo dõi tất cả danh tính từ một campaign

### Phân quyền và bảo mật

-   **AUTH_CODE**: Ủy quyền có thời hạn và mục đích cụ thể
-   **TT_USER**: Sử dụng trực tiếp tài khoản người dùng
-   **BC_AUTH_TT**: Ủy quyền thông qua Business Center
-   **TTS_TT**: Liên kết với TikTok Shop ecosystem

## Mục đích sử dụng

-   Lưu trữ danh sách các danh tính TikTok liên kết với GMV Max Campaign
-   Quản lý ủy quyền và quyền truy cập nội dung
-   Theo dõi liên kết giữa campaign và các tài khoản TikTok
-   Hỗ trợ đa dạng hóa nguồn nội dung quảng cáo
-   Kiểm soát bảo mật và phân quyền
-   Tích hợp với TikTok Business API cho Identity management

## Quan hệ với các entity khác

-   **RawGmvMaxCampaignsEntity**: Mối quan hệ Many-to-One, một campaign có thể có nhiều identities
-   **RawGmvMaxCampaignItemsEntity**: Các items có thể tham chiếu đến identities này
-   **RawGmvMaxCampaignCustomAnchorVideosEntity**: Custom anchor videos có thể liên kết với identities

## Use Cases thực tế

### Campaign với nhiều Creator

```
Campaign A có thể sử dụng:
- Identity 1: TT_USER (Creator nổi tiếng)
- Identity 2: BC_AUTH_TT (Agency quản lý nhiều creator)
- Identity 3: TTS_TT (TikTok Shop chính thức của thương hiệu)
```

### Phân quyền theo cấp độ

```
BC_AUTH_TT cho phép:
- Business Center quản lý nhiều TikTok account
- Ủy quyền truy cập đến TikTok Shop
- Kiểm soát nội dung và chiến dịch
```
