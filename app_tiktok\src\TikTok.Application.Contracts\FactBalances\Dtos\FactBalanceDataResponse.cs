﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.DimAdAccounts;
using TikTok.DimBusinessCenters;
using TikTok.DimDates;

namespace TikTok.FactBalances.Dtos
{
    public class FactBalanceDataResponse
    {
        public DateTime From { get; set; }
        public DateTime To { get; set; }
        public List<FactBalanceDto> FactBalances { get; set; }
        public List<DimDateDto> DimDates { get; set; }
        public List<DimAdAccountDto> DimAdAccounts { get; set; }
        public List<DimBusinessCenterDto> DimBusinessCenters { get; set; }
    }
}
