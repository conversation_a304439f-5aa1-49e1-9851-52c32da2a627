/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Catalog Product Sets operations
    /// </summary>
    public class CatalogProductSetsApi : ICatalogProductSetsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<CatalogProductSetsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the CatalogProductSetsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public CatalogProductSetsApi(IApiClient apiClient, ILogger<CatalogProductSetsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get a list of product sets or one specified product set in a catalog under your Business Center
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting product sets</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing product sets information</returns>
        public async Task<GetProductSetsResponse> GetProductSetsAsync(
            string accessToken,
            GetProductSetsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request.BcId));

            if (string.IsNullOrEmpty(request.CatalogId))
                throw new ArgumentException("Catalog ID cannot be null or empty", nameof(request.CatalogId));

            _logger?.LogInformation("Getting product sets for catalog {CatalogId} in BC {BcId}", 
                request.CatalogId, request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["catalog_id"] = request.CatalogId
            };

            if (!string.IsNullOrEmpty(request.ProductSetId))
            {
                queryParams["product_set_id"] = request.ProductSetId;
            }

            if (request.ReturnProductCount.HasValue)
            {
                queryParams["return_product_count"] = request.ReturnProductCount.Value.ToString().ToLowerInvariant();
            }

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<GetProductSetsResponse>(
                CatalogProductSetsEndpoints.GetProductSets,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get products in a product set
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting products in a set</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing products in the set</returns>
        public async Task<GetProductsInSetResponse> GetProductsInSetAsync(
            string accessToken,
            GetProductsInSetRequest request,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request.BcId));

            if (string.IsNullOrEmpty(request.CatalogId))
                throw new ArgumentException("Catalog ID cannot be null or empty", nameof(request.CatalogId));

            if (string.IsNullOrEmpty(request.ProductSetId))
                throw new ArgumentException("Product Set ID cannot be null or empty", nameof(request.ProductSetId));

            _logger?.LogInformation("Getting products in set {ProductSetId} for catalog {CatalogId} in BC {BcId}", 
                request.ProductSetId, request.CatalogId, request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["catalog_id"] = request.CatalogId,
                ["product_set_id"] = request.ProductSetId
            };

            if (request.Page.HasValue)
            {
                queryParams["page"] = request.Page.Value.ToString();
            }

            if (request.PageSize.HasValue)
            {
                queryParams["page_size"] = request.PageSize.Value.ToString();
            }

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken,
                ["Content-Type"] = "application/json"
            };

            return await _apiClient.CallApiAsync<GetProductsInSetResponse>(
                CatalogProductSetsEndpoints.GetProductsInSet,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Create a product set in a catalog under your Business Center
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for creating a product set</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created product set information</returns>
        //public async Task<CreateProductSetResponse> CreateProductSetAsync(
        //    string accessToken,
        //    CreateProductSetRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrEmpty(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    if (string.IsNullOrEmpty(request.BcId))
        //        throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request.BcId));

        //    if (string.IsNullOrEmpty(request.CatalogId))
        //        throw new ArgumentException("Catalog ID cannot be null or empty", nameof(request.CatalogId));

        //    if (string.IsNullOrEmpty(request.ProductSetName))
        //        throw new ArgumentException("Product Set Name cannot be null or empty", nameof(request.ProductSetName));

        //    if (request.Conditions == null)
        //        throw new ArgumentException("Conditions cannot be null", nameof(request.Conditions));

        //    _logger?.LogInformation("Creating product set '{ProductSetName}' in catalog {CatalogId} in BC {BcId}", 
        //        request.ProductSetName, request.CatalogId, request.BcId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken,
        //        ["Content-Type"] = "application/json"
        //    };

        //    return await _apiClient.CallApiAsync<CreateProductSetResponse>(
        //        CatalogProductSetsEndpoints.CreateProductSet,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Update the filter conditions or name of a product set
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for updating a product set</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated product set information</returns>
        //public async Task<UpdateProductSetResponse> UpdateProductSetAsync(
        //    string accessToken,
        //    UpdateProductSetRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrEmpty(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    if (string.IsNullOrEmpty(request.BcId))
        //        throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request.BcId));

        //    if (string.IsNullOrEmpty(request.CatalogId))
        //        throw new ArgumentException("Catalog ID cannot be null or empty", nameof(request.CatalogId));

        //    if (string.IsNullOrEmpty(request.ProductSetId))
        //        throw new ArgumentException("Product Set ID cannot be null or empty", nameof(request.ProductSetId));

        //    if (string.IsNullOrEmpty(request.ProductSetName) && request.Conditions == null)
        //        throw new ArgumentException("At least one of ProductSetName or Conditions must be provided");

        //    _logger?.LogInformation("Updating product set {ProductSetId} in catalog {CatalogId} in BC {BcId}", 
        //        request.ProductSetId, request.CatalogId, request.BcId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken,
        //        ["Content-Type"] = "application/json"
        //    };

        //    return await _apiClient.CallApiAsync<UpdateProductSetResponse>(
        //        CatalogProductSetsEndpoints.UpdateProductSet,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Delete product sets in a catalog under a Business Center
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for deleting product sets</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deleted product set IDs</returns>
        //public async Task<DeleteProductSetsResponse> DeleteProductSetsAsync(
        //    string accessToken,
        //    DeleteProductSetsRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrEmpty(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));

        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    if (string.IsNullOrEmpty(request.BcId))
        //        throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request.BcId));

        //    if (string.IsNullOrEmpty(request.CatalogId))
        //        throw new ArgumentException("Catalog ID cannot be null or empty", nameof(request.CatalogId));

        //    if (request.ProductSetIds == null || request.ProductSetIds.Count == 0)
        //        throw new ArgumentException("Product Set IDs cannot be null or empty", nameof(request.ProductSetIds));

        //    if (request.ProductSetIds.Count > 10)
        //        throw new ArgumentException("Cannot delete more than 10 product sets at once", nameof(request.ProductSetIds));

        //    _logger?.LogInformation("Deleting {Count} product sets in catalog {CatalogId} in BC {BcId}", 
        //        request.ProductSetIds.Count, request.CatalogId, request.BcId);

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken,
        //        ["Content-Type"] = "application/json"
        //    };

        //    return await _apiClient.CallApiAsync<DeleteProductSetsResponse>(
        //        CatalogProductSetsEndpoints.DeleteProductSets,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}
    }
}
