using System.Threading.Tasks;
using TikTok.BaseMessages;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.Contracts.MessageProviders
{
    /// <summary>
    /// Service interface cho việc gửi thông báo thông qua Elsa workflow
    /// </summary>
    public interface INotificationService : ITransientDependency
    {
        /// <summary>
        /// Gửi thông báo thông qua workflow
        /// </summary>
        /// <param name="workflowName">Tên workflow cần chạy</param>
        /// <param name="messageInput">Dữ liệu thông báo</param>
        /// <returns>Kết quả thực thi workflow</returns>
        Task<object> SendNotificationAsync(string workflowName, BaseMessageActivityInput messageInput);

        /// <summary>
        /// Gửi thông báo với workflow mặc định
        /// </summary>
        /// <param name="messageInput">Dữ liệu thông báo</param>
        /// <returns><PERSON>ế<PERSON> quả thực thi workflow</returns>
        Task<object> SendNotificationAsync(BaseMessageActivityInput messageInput);

        /// <summary>
        /// Gửi thông báo với thông tin cơ bản
        /// </summary>
        /// <param name="adAccountId">ID tài khoản quảng cáo</param>
        /// <param name="bcId">ID Business Center</param>
        /// <param name="textContent">Nội dung thông báo</param>
        /// <param name="phoneNumber">Số điện thoại (tùy chọn)</param>
        /// <param name="name">Tên người gửi (tùy chọn)</param>
        /// <param name="ownerId">Owner ID (tùy chọn)</param>
        /// <returns>Kết quả thực thi workflow</returns>
        Task<object> SendNotificationAsync(
            string adAccountId, 
            string bcId, 
            string textContent, 
            string phoneNumber = null, 
            string name = null, 
            string ownerId = null);

        /// <summary>
        /// Gửi thông báo theo danh sách tài khoản quảng cáo trong một Business Center.
        /// Thực thi workflow RequireSendMessage với thông tin từ DTO.
        /// </summary>
        /// <param name="notificationDto">DTO chứa thông tin thông báo</param>
        /// <returns>Kết quả thực thi workflow</returns>
        Task<object> SendNotificationAsync(SendNotificationDto notificationDto);
    }
} 