/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API BC Reporting operations
    /// </summary>
    public interface IBcReportingApi
    {
        /// <summary>
        /// Get currencies and registration areas for ad accounts within a Business Center.
        /// You can pass the returned currencies and places of registration to the filter fields 
        /// registered_area and currency_of_account in a Business Center report.
        /// </summary>
        /// <param name="request">Request containing the Business Center ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing currencies and region codes</returns>
        Task<GetAdvertiserAttributeResponse> GetAdvertiserAttributeAsync(
            GetAdvertiserAttributeRequest request,
            CancellationToken cancellationToken = default);
    }
}
