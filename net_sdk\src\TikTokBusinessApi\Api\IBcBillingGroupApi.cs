/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API BC Billing Group operations
    /// </summary>
    public interface IBcBillingGroupApi
    {
        ///// <summary>
        ///// Create a Billing Group in a Business Center
        ///// </summary>
        ///// <param name="request">Create billing group request</param>
        ///// <param name="cancellationToken">Cancellation token</param>
        ///// <returns>Response containing the created billing group ID</returns>
        //Task<CreateBillingGroupResponse> CreateBillingGroupAsync(
        //    CreateBillingGroupRequest request,
        //    CancellationToken cancellationToken = default);

        ///// <summary>
        ///// Update the settings of a Billing Group
        ///// </summary>
        ///// <param name="request">Update billing group request</param>
        ///// <param name="cancellationToken">Cancellation token</param>
        ///// <returns>Response indicating success or failure</returns>
        //Task<UpdateBillingGroupResponse> UpdateBillingGroupAsync(
        //    UpdateBillingGroupRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get all Billing Groups in a Business Center
        /// </summary>
        /// <param name="request">Get billing groups request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of billing groups with pagination</returns>
        Task<GetBillingGroupsResponse> GetBillingGroupsAsync(
            GetBillingGroupsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the list of advertisers that are bound to a Billing Group
        /// </summary>
        /// <param name="request">Get billing group advertisers request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of advertisers with pagination</returns>
        Task<GetBillingGroupAdvertisersResponse> GetBillingGroupAdvertisersAsync(
            GetBillingGroupAdvertisersRequest request,
            CancellationToken cancellationToken = default);
    }
}
