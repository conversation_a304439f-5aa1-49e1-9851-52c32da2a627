/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;
using TikTokBusinessApi.Serialization;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API BC Assets operations
    /// </summary>
    public class BCAssetsApi : IBCAssetsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<BCAssetsApi>? _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// Initializes a new instance of the BCAssetsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public BCAssetsApi(IApiClient apiClient, ILogger<BCAssetsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
            _jsonOptions = JsonSerializerOptionsExtensions.CreateDefault();

        }

        /// <summary>
        /// Create an ad account in the Business Center
        /// </summary>
        /// <param name="request">Create advertiser request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created advertiser ID</returns>
        // public async Task<CreateAdvertiserResponse> CreateAdvertiserAsync(
        //     CreateAdvertiserRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BcId))
        //         throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Creating advertiser for BC {BcId}", request.BcId);

        //     return await _apiClient.CallApiAsync<CreateAdvertiserResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCAssetsEndpoints.CreateAdvertiser}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Update an ad account in the Business Center
        /// </summary>
        /// <param name="request">Update advertiser request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing update results</returns>
        // public async Task<UpdateAdvertiserResponse> UpdateAdvertiserAsync(
        //     UpdateAdvertiserRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogInformation("Updating advertiser {AdvertiserId}", request.AdvertiserId ?? request.BcId);

        //     return await _apiClient.CallApiAsync<UpdateAdvertiserResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCAssetsEndpoints.UpdateAdvertiser}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Disable an ad account in the Business Center
        /// </summary>
        /// <param name="request">Disable advertiser request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing disable results</returns>
        // public async Task<DisableAdvertiserResponse> DisableAdvertiserAsync(
        //     DisableAdvertiserRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BcId))
        //         throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Disabling advertiser for BC {BcId}", request.BcId);

        //     return await _apiClient.CallApiAsync<DisableAdvertiserResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCAssetsEndpoints.DisableAdvertiser}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Upload a business certificate
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="imageFile">Certificate image file stream</param>
        /// <param name="fileName">File name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the uploaded image ID</returns>
        // public async Task<UploadBusinessCertificateResponse> UploadBusinessCertificateAsync(
        //     string bcId,
        //     Stream imageFile,
        //     string fileName,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(bcId))
        //         throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));
        //     if (imageFile == null)
        //         throw new ArgumentNullException(nameof(imageFile));
        //     if (string.IsNullOrWhiteSpace(fileName))
        //         throw new ArgumentException("File name cannot be null or empty", nameof(fileName));

        //     _logger?.LogInformation("Uploading business certificate for BC {BcId}", bcId);

        //     // Create multipart form data content
        //     using var content = new MultipartFormDataContent();
        //     content.Add(new StringContent(bcId), "bc_id");
        //     content.Add(new StreamContent(imageFile), "image_file", fileName);

        //     return await _apiClient.CallApiAsync<UploadBusinessCertificateResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCAssetsEndpoints.UploadBusinessCertificate}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: content,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Check UnionPay verification requirement for a business license
        /// </summary>
        /// <param name="request">Check UnionPay verification request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating if UnionPay verification is required</returns>
        //public async Task<CheckUnionPayVerificationResponse> CheckUnionPayVerificationAsync(
        //    CheckUnionPayVerificationRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));
        //    if (string.IsNullOrWhiteSpace(request.LicenseNo))
        //        throw new ArgumentException("License number cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Checking UnionPay verification requirement for license {LicenseNo}", request.LicenseNo);

        //    var queryParams = new Dictionary<string, string>
        //    {
        //        ["license_no"] = request.LicenseNo
        //    };

        //    return await _apiClient.CallApiAsync<CheckUnionPayVerificationResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCAssetsEndpoints.CheckUnionPayInfo}",
        //        HttpMethod.Get,
        //        queryParams: queryParams,
        //        body: null,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Submit UnionPay verification for an ad account
        /// </summary>
        /// <param name="request">Submit UnionPay verification request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing submission status</returns>
        // public async Task<SubmitUnionPayVerificationResponse> SubmitUnionPayVerificationAsync(
        //     SubmitUnionPayVerificationRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BcId))
        //         throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Submitting UnionPay verification for BC {BcId}", request.BcId);

        //     return await _apiClient.CallApiAsync<SubmitUnionPayVerificationResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCAssetsEndpoints.SubmitUnionPayVerification}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get UnionPay verification status for an ad account
        /// </summary>
        /// <param name="request">Get UnionPay status request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing verification status</returns>
        public async Task<GetUnionPayStatusResponse> GetUnionPayStatusAsync(
            GetUnionPayStatusRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting UnionPay verification status for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["advertiser_id"] = request.AdvertiserId
            };

            return await _apiClient.CallApiAsync<GetUnionPayStatusResponse>(
                $"{BCAssetsEndpoints.GetUnionPayStatus}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get assets that you or a particular user has access to
        /// </summary>
        /// <param name="request">Get assets request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of assets</returns>
        public async Task<TikTokApiResponse<GetAssetsResponse>> GetAssetsAsync(
            GetAssetsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting assets for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["asset_type"] = request.AssetType
            };

            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering, _jsonOptions);
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<TikTokApiResponse<GetAssetsResponse>>(
                $"{BCAssetsEndpoints.GetAssets}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get additional information about assets in a Business Center (Admin only)
        /// </summary>
        /// <param name="request">Get assets as admin request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing detailed asset information</returns>
        public async Task<TikTokApiResponse<GetAssetsAsAdminResponse>> GetAssetsAsAdminAsync(
            GetAssetsAsAdminRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting assets as admin for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["asset_type"] = request.AssetType
            };

            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering, _jsonOptions);
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<TikTokApiResponse<GetAssetsAsAdminResponse>>(
                $"{BCAssetsEndpoints.GetAssetsAsAdmin}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <summary>
        // /// Share assets with other Business Centers
        // /// </summary>
        // /// <param name="request">Share assets request</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing sharing results</returns>
        // public async Task<ShareAssetsResponse> ShareAssetsAsync(
        //     ShareAssetsRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BcId))
        //         throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Sharing assets for BC {BcId}", request.BcId);

        //     return await _apiClient.CallApiAsync<ShareAssetsResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCAssetsEndpoints.ShareAssets}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <summary>
        // /// Unshare assets from other Business Centers
        // /// </summary>
        // /// <param name="request">Unshare assets request</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing unsharing results</returns>
        // public async Task<UnshareAssetsResponse> UnshareAssetsAsync(
        //     UnshareAssetsRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BcId))
        //         throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Unsharing assets for BC {BcId}", request.BcId);

        //     return await _apiClient.CallApiAsync<UnshareAssetsResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCAssetsEndpoints.UnshareAssets}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get shared assets
        /// </summary>
        /// <param name="request">Get shared assets request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing shared assets</returns>
        public async Task<GetSharedAssetsResponse> GetSharedAssetsAsync(
            GetSharedAssetsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting shared assets for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["asset_type"] = request.AssetType,
                ["share_type"] = request.ShareType
            };

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetSharedAssetsResponse>(
                $"{BCAssetsEndpoints.GetSharedAssets}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <summary>
        // /// Delete assets from a Business Center
        // /// </summary>
        // /// <param name="request">Delete assets request</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing deletion results</returns>
        // public async Task<DeleteAssetsResponse> DeleteAssetsAsync(
        //     DeleteAssetsRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BcId))
        //         throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Deleting assets for BC {BcId}", request.BcId);

        //     return await _apiClient.CallApiAsync<DeleteAssetsResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCAssetsEndpoints.DeleteAssets}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get asset sharing history
        /// </summary>
        /// <param name="request">Get asset sharing history request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing sharing history</returns>
        public async Task<GetAssetSharingHistoryResponse> GetAssetSharingHistoryAsync(
            GetAssetSharingHistoryRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting asset sharing history for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["asset_id"] = request.AssetId,
                ["asset_type"] = request.AssetType
            };

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetAssetSharingHistoryResponse>(
                $"{BCAssetsEndpoints.GetAssetSharingHistory}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <summary>
        // /// Create an Asset Group
        // /// </summary>
        // /// <param name="request">Create asset group request</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing the created asset group ID</returns>
        // public async Task<CreateAssetGroupResponse> CreateAssetGroupAsync(
        //     CreateAssetGroupRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BcId))
        //         throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Creating asset group for BC {BcId}", request.BcId);

        //     return await _apiClient.CallApiAsync<CreateAssetGroupResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCAssetsEndpoints.CreateAssetGroup}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get Asset Groups
        /// </summary>
        /// <param name="request">List asset groups request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing asset groups</returns>
        public async Task<ListAssetGroupsResponse> GetAssetGroupsAsync(
            ListAssetGroupsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting asset groups for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<ListAssetGroupsResponse>(
                $"{BCAssetsEndpoints.GetAssetGroups}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get details of an Asset Group
        /// </summary>
        /// <param name="request">Get asset group request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing asset group details</returns>
        public async Task<GetAssetGroupResponse> GetAssetGroupAsync(
            GetAssetGroupRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting asset group details for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["asset_group_id"] = request.AssetGroupId,
                ["query_entity"] = request.QueryEntity
            };

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetAssetGroupResponse>(
                $"{BCAssetsEndpoints.GetAssetGroups}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <summary>
        // /// Update an Asset Group
        // /// </summary>
        // /// <param name="request">Update asset group request</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response indicating success or failure</returns>
        // public async Task<UpdateAssetGroupResponse> UpdateAssetGroupAsync(
        //     UpdateAssetGroupRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BcId))
        //         throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Updating asset group for BC {BcId}", request.BcId);

        //     return await _apiClient.CallApiAsync<UpdateAssetGroupResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCAssetsEndpoints.UpdateAssetGroup}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// List all Asset Groups
        /// </summary>
        /// <param name="request">List asset groups request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing asset groups</returns>
        public async Task<ListAssetGroupsResponse> ListAssetGroupsAsync(
            ListAssetGroupsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Listing asset groups for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            if (request.Filtering?.Keyword != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering, _jsonOptions);
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<ListAssetGroupsResponse>(
                $"{BCAssetsEndpoints.ListAssetGroups}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Delete Asset Groups
        /// </summary>
        /// <param name="request">Delete asset groups request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        // public async Task<DeleteAssetGroupsResponse> DeleteAssetGroupsAsync(
        //     DeleteAssetGroupsRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));
        //     if (string.IsNullOrWhiteSpace(request.BcId))
        //         throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));
        //     if (request.AssetGroupIds == null || request.AssetGroupIds.Count == 0)
        //         throw new ArgumentException("Asset Group IDs cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Deleting asset groups for BC {BcId}", request.BcId);

        //     return await _apiClient.CallApiAsync<DeleteAssetGroupsResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCAssetsEndpoints.DeleteAssetGroups}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get advertiser qualification information
        /// </summary>
        /// <param name="request">Get qualifications request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing qualification information</returns>
        public async Task<GetQualificationsResponse> GetAdvertiserQualificationAsync(
            GetQualificationsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));
            if (string.IsNullOrWhiteSpace(request.BcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting advertiser qualification for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering, _jsonOptions);
            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetQualificationsResponse>(
                $"{BCAssetsEndpoints.GetAdvertiserQualification}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
