using Microsoft.AspNetCore.Authorization;
using System.Threading.Tasks;
using TikTok.Permissions;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu hồ sơ chi phí
    /// </summary>
    public class CostProfileSyncApplicationAppService : ApplicationService, ICostProfileSyncApplicationAppService
    {
        private readonly ICostProfileSyncService _costProfileSyncService;

        public CostProfileSyncApplicationAppService(ICostProfileSyncService costProfileSyncService)
        {
            _costProfileSyncService = costProfileSyncService;
        }

        /// <summary>
        /// Đồng bộ hồ sơ chi phí theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        [Authorize(TikTokPermissions.CostProfiles.Sync)]
        public async Task<CostProfileSyncResult> SyncCostProfilesAsync(string bcId)
        {
            return await _costProfileSyncService.SyncCostProfilesAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ hồ sơ chi phí cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        [Authorize(TikTokPermissions.CostProfiles.Sync)]
        public async Task<CostProfileSyncResult> SyncAllCostProfilesAsync()
        {
            return await _costProfileSyncService.SyncAllCostProfilesAsync();
        }
    }
}