/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TikTokBusinessApi.Auth
{
    /// <summary>
    /// TikTok-specific authentication implementation that injects app_id and access_token into request bodies
    /// </summary>
    public class TikTokAuthentication : IBodyAuthentication, IAuthenticationContext
    {
        private readonly string? _appId;
        private readonly ILogger<TikTokAuthentication>? _logger;
        private string? _accessToken;

        /// <summary>
        /// Initializes a new instance of the TikTokAuthentication class
        /// </summary>
        /// <param name="appId">Application ID (global for the SDK instance)</param>
        /// <param name="accessToken">Initial access token (can be changed per request)</param>
        /// <param name="logger">Logger instance</param>
        public TikTokAuthentication(string? appId, string? accessToken = null, ILogger<TikTokAuthentication>? logger = null)
        {
            _appId = appId;
            _accessToken = accessToken;
            _logger = logger;
        }

        /// <summary>
        /// Gets the current access token
        /// </summary>
        /// <returns>Current access token or null if not available</returns>
        public Task<string?> GetAccessTokenAsync()
        {
            return Task.FromResult(_accessToken);
        }

        /// <summary>
        /// Sets the access token for subsequent requests
        /// </summary>
        /// <param name="accessToken">Access token to set</param>
        public Task SetAccessTokenAsync(string? accessToken)
        {
            _accessToken = accessToken;
            _logger?.LogDebug("Access token updated for TikTok authentication");
            return Task.CompletedTask;
        }

        /// <summary>
        /// Gets the application ID
        /// </summary>
        /// <returns>Application ID</returns>
        public string? GetAppId()
        {
            return _appId;
        }

        /// <summary>
        /// Applies authentication to the HTTP request (legacy interface - no-op for TikTok)
        /// </summary>
        /// <param name="request">HTTP request message</param>
        public void ApplyToRequest(HttpRequestMessage request)
        {
            // TikTok authentication is applied to the request body, not headers
            // This method is kept for backward compatibility but does nothing
            _logger?.LogDebug("ApplyToRequest called - TikTok authentication is applied to request body");
            // check access token if not null set header Access-Token
            if (!string.IsNullOrEmpty(_accessToken))
            {
                request.Headers.Add("Access-Token", _accessToken);
                _logger?.LogDebug("Access token added to request headers");
            }
            else
            {
                _logger?.LogWarning("Access token is null or empty - not added to request headers");
            }

        }

        /// <summary>
        /// Applies authentication by modifying the request body
        /// </summary>
        /// <param name="request">HTTP request message</param>
        /// <param name="requestBody">Current request body object (can be null)</param>
        /// <returns>Modified request body object with authentication parameters</returns>
        public async Task<object?> ApplyToRequestBodyAsync(HttpRequestMessage request, object? requestBody)
        {
            var authParams = await GetAuthenticationParametersAsync();
            
            if (authParams.Count == 0)
            {
                _logger?.LogWarning("No authentication parameters available - app_id or access_token missing");
                return requestBody;
            }

            // If no existing body, create a new object with just auth parameters
            if (requestBody == null)
            {
                _logger?.LogDebug("Creating new request body with authentication parameters");
                return authParams;
            }

            // Merge authentication parameters with existing request body
            var mergedBody = await MergeAuthenticationParametersAsync(requestBody, authParams);
            
            _logger?.LogDebug("Authentication parameters merged into request body");
            return mergedBody;
        }

        /// <summary>
        /// Gets authentication parameters that should be added to the request body
        /// </summary>
        /// <returns>Dictionary of authentication parameters</returns>
        public Task<Dictionary<string, object>> GetAuthenticationParametersAsync()
        {
            var parameters = new Dictionary<string, object>();

            if (!string.IsNullOrEmpty(_appId))
            {
                parameters["app_id"] = _appId;
            }

            if (!string.IsNullOrEmpty(_accessToken))
            {
                parameters["access_token"] = _accessToken;
            }

            return Task.FromResult(parameters);
        }

        /// <summary>
        /// Merges authentication parameters with existing request body
        /// </summary>
        /// <param name="requestBody">Existing request body</param>
        /// <param name="authParams">Authentication parameters to merge</param>
        /// <returns>Merged request body</returns>
        private async Task<object> MergeAuthenticationParametersAsync(object requestBody, Dictionary<string, object> authParams)
        {
            try
            {
                // Handle different types of request bodies
                switch (requestBody)
                {
                    case Dictionary<string, object> dictBody:
                        // Merge with existing dictionary
                        foreach (var param in authParams)
                        {
                            dictBody[param.Key] = param.Value;
                        }
                        return dictBody;

                    case string jsonString:
                        // Parse JSON string, merge, and return as object
                        var jsonDict = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonString);
                        if (jsonDict != null)
                        {
                            foreach (var param in authParams)
                            {
                                jsonDict[param.Key] = param.Value;
                            }
                            return jsonDict;
                        }
                        break;

                    default:
                        // For other object types, convert to dictionary and merge
                        var objectDict = await ConvertObjectToDictionaryAsync(requestBody);
                        foreach (var param in authParams)
                        {
                            objectDict[param.Key] = param.Value;
                        }
                        return objectDict;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to merge authentication parameters with request body");
            }

            // Fallback: create new object with both original body and auth params
            return new
            {
                RequestData = requestBody,
                AppId = authParams.GetValueOrDefault("app_id"),
                AccessToken = authParams.GetValueOrDefault("access_token")
            };
        }

        /// <summary>
        /// Converts an object to a dictionary for merging
        /// </summary>
        /// <param name="obj">Object to convert</param>
        /// <returns>Dictionary representation of the object</returns>
        private async Task<Dictionary<string, object>> ConvertObjectToDictionaryAsync(object obj)
        {
            try
            {
                // Serialize to JSON and then deserialize to dictionary
                var json = JsonSerializer.Serialize(obj);
                var dict = JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                return dict ?? new Dictionary<string, object>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to convert object to dictionary");
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Creates a TikTok authentication instance with app ID only
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="logger">Logger instance</param>
        /// <returns>TikTok authentication instance</returns>
        public static TikTokAuthentication WithAppId(string appId, ILogger<TikTokAuthentication>? logger = null)
        {
            return new TikTokAuthentication(appId, null, logger);
        }

        /// <summary>
        /// Creates a TikTok authentication instance with both app ID and access token
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="accessToken">Access token</param>
        /// <param name="logger">Logger instance</param>
        /// <returns>TikTok authentication instance</returns>
        public static TikTokAuthentication WithCredentials(string appId, string accessToken, ILogger<TikTokAuthentication>? logger = null)
        {
            return new TikTokAuthentication(appId, accessToken, logger);
        }

        /// <summary>
        /// Validates that required authentication parameters are available
        /// </summary>
        /// <param name="requireAccessToken">Whether access token is required</param>
        /// <returns>Validation result</returns>
        public async Task<AuthenticationValidationResult> ValidateAsync(bool requireAccessToken = true)
        {
            var result = new AuthenticationValidationResult();

            if (string.IsNullOrEmpty(_appId))
            {
                result.Errors.Add("app_id is required but not configured");
            }

            if (requireAccessToken && string.IsNullOrEmpty(_accessToken))
            {
                result.Errors.Add("access_token is required but not configured");
            }

            return result;
        }
    }

    /// <summary>
    /// Authentication validation result
    /// </summary>
    public class AuthenticationValidationResult
    {
        /// <summary>
        /// Validation errors
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// Whether authentication is valid
        /// </summary>
        public bool IsValid => Errors.Count == 0;
    }
}
