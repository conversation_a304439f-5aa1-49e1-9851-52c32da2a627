/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Pangle operations
    /// </summary>
    public interface IPangleApi
    {
        /// <summary>
        /// Get the Pangle block list of an ad account.
        /// Use this endpoint to get the Pangle block list of an ad account.
        /// </summary>
        /// <param name="request">Request parameters for getting the Pangle block list</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the Pangle block list</returns>
        Task<GetPangleBlockListResponse> GetBlockListAsync(
            GetPangleBlockListRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Update the Pangle block list.
        /// Use this endpoint to update the Pangle block list.
        /// </summary>
        /// <param name="request">Request parameters for updating the Pangle block list</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the update result</returns>
        //Task<UpdatePangleBlockListResponse> UpdateBlockListAsync(
        //    UpdatePangleBlockListRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the Pangle audience packages that are available to an advertiser.
        /// This audience package is only available for Pangle ad placement, and will not affect traffic for other placements.
        /// The audience package will be applied alongside any other targeting settings you select.
        /// Audience packages can help you more effectively reach your target audience, while narrowing down the scope for delivery.
        /// </summary>
        /// <param name="request">Request parameters for getting the Pangle audience packages</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the Pangle audience packages</returns>
        Task<GetPangleAudiencePackagesResponse> GetAudiencePackagesAsync(
            GetPangleAudiencePackagesRequest request,
            CancellationToken cancellationToken = default);
    }
}
