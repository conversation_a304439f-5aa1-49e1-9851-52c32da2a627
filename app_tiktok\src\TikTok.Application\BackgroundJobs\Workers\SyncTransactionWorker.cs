using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Application.Contracts.MessageProviders;
using TikTok.Repositories;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Worker để đồng bộ Transaction
    /// </summary>
    public class SyncTransactionWorker : WorkerJobBase
    {
        private readonly ITransactionSyncService _transactionSyncService;
        private readonly INotificationService _notificationService;
        private readonly IAdAccountRepository _adAccountRepository;

        public SyncTransactionWorker(
            ILogger<SyncTransactionWorker> logger,
            IServiceProvider serviceProvider,
            ITransactionSyncService transactionSyncService,
            INotificationService notificationService,
            IAdAccountRepository adAccountRepository)
            : base(logger, serviceProvider)
        {
            _transactionSyncService = transactionSyncService;
            _notificationService = notificationService;
            _adAccountRepository = adAccountRepository;
        }

        public override CommandType CommandType => CommandType.SyncTransaction;

        protected override async Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job)
        {
            // Parse parameters
            var parameters = ParseParameters(args.Parameters);

            // Sync Transaction
            var responseSync = await _transactionSyncService.SyncTransactionsAsync(parameters.BcId);
            if (responseSync != null)
            {
                if (responseSync.IsSuccess)
                {
                    // Gửi thông báo nếu có giao dịch mới
                    if (responseSync.NewTransactionIds != null && responseSync.NewTransactionIds.Any())
                    {
                        await SendNotificationForNewTransactionsAsync(parameters.BcId, responseSync.NewTransactionIds, responseSync.NewRecords);
                    }
                    
                    return JobResult.Success(JsonConvert.SerializeObject(responseSync));
                }
                else
                {
                    return JobResult.Error(responseSync.ErrorMessage ?? string.Empty, JsonConvert.SerializeObject(responseSync));
                }
            }
            else
            {
                return JobResult.Error("Lỗi đồng bộ");
            }
        }

        /// <summary>
        /// Parse parameters từ JSON
        /// </summary>
        /// <param name="parametersJson">JSON parameters</param>
        /// <returns>Parameters object</returns>
        private SyncTransactionParameters ParseParameters(string parametersJson)
        {
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<SyncTransactionParameters>(parametersJson)
                    ?? new SyncTransactionParameters();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse parameters, using defaults");
                return new SyncTransactionParameters();
            }
        }

        /// <summary>
        /// Gửi thông báo cho các giao dịch mới
        /// </summary>
        /// <param name="bcId">ID Business Center</param>
        /// <param name="newTransactionIds">Danh sách ID giao dịch mới</param>
        /// <param name="newRecordsCount">Số lượng giao dịch mới</param>
        private async Task SendNotificationForNewTransactionsAsync(string bcId, List<string> newTransactionIds, int newRecordsCount)
        {
           // send notification paging with page size MAX_PAGE_SIZE
            int pageSize = MAX_PAGE_SIZE;
            int totalPages = (int)Math.Ceiling((double)newTransactionIds.Count / pageSize);
            for (int page = 0; page < totalPages; page++)
            {
                try
                {
                    var newTransactionIdsChunk = newTransactionIds.Skip(page * pageSize).Take(pageSize).ToList();
                    var payload = JsonConvert.SerializeObject(new
                    {
                        Message = $"Có {newRecordsCount} giao dịch mới được đồng bộ từ TikTok Business API",
                        NewTransactionCount = newRecordsCount,
                        TotalTransactionIds = newTransactionIds.Count,
                        SyncTime = DateTime.Now,
                        BcId = bcId,
                    });

                    // Tạo notification DTO
                    var notificationDto = SendNotificationDto.CreateTransactionNotification(bcId, newTransactionIdsChunk, payload);

                    // Gửi thông báo
                    await _notificationService.SendNotificationAsync(notificationDto);
                    _logger.LogDebug("Đã gửi thông báo giao dịch mới cho {NewTransactionIdsCount} giao dịch mới", newTransactionIdsChunk.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi gửi thông báo cho giao dịch mới trong BC: {BcId}", bcId);
                }
            }
        }

        /// <summary>
        /// Parameters cho SyncTransaction
        /// </summary>
        public class SyncTransactionParameters:DefaultParameters
        {
            //public string BcId { get; set; }
            //public DateTime SyncDate { get; set; }
        }
    }
}