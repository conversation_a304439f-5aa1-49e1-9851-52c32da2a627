using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using TikTok.BackgroundJobs;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.JobManagement;
using Volo.Abp.DependencyInjection;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Worker cho việc đồng bộ báo cáo tích hợp AdGroup
    /// </summary>
    public class SyncReportIntegratedAdGroupWorker : WorkerJobBase
    {
        private readonly IReportIntegratedAdGroupSyncService _reportIntegratedAdGroupSyncService;

        public SyncReportIntegratedAdGroupWorker(
            ILogger<SyncReportIntegratedAdGroupWorker> logger,
            IServiceProvider serviceProvider,
            IReportIntegratedAdGroupSyncService reportIntegratedAdGroupSyncService) : base(logger, serviceProvider)
        {
            _reportIntegratedAdGroupSyncService = reportIntegratedAdGroupSyncService;
        }

        public override CommandType CommandType => CommandType.SyncReportIntegratedAdGroup;

        protected override async Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job)
        {
            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp AdGroup cho tất cả BC");

                var result = await _reportIntegratedAdGroupSyncService.SyncAllReportIntegratedAdGroupForAllBcsAsync();

                if (result.IsSuccess)
                {
                    _logger.LogDebug("Hoàn thành đồng bộ báo cáo tích hợp AdGroup. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}, AdGroup: {AdGroupCount}, Ngày: {DayCount}",
                        result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount, result.AdGroupCount, result.DayCount);

                    return new JobResult
                    {
                        IsSuccess = true,
                        ErrorMessage = $"Đồng bộ báo cáo tích hợp AdGroup thành công. Tổng: {result.TotalSynced}, Mới: {result.NewRecords}, Cập nhật: {result.UpdatedRecords}, BC: {result.BcCount}, AdGroup: {result.AdGroupCount}, Ngày: {result.DayCount}"
                    };
                }
                else
                {
                    _logger.LogError("Đồng bộ báo cáo tích hợp AdGroup thất bại: {ErrorMessage}", result.ErrorMessage);

                    return new JobResult
                    {
                        IsSuccess = false,
                        ErrorMessage = $"Đồng bộ báo cáo tích hợp AdGroup thất bại: {result.ErrorMessage}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp AdGroup");
                return new JobResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"Lỗi khi đồng bộ báo cáo tích hợp AdGroup: {ex.Message}"
                };
            }
        }
    }
}