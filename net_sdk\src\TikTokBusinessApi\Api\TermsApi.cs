/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Terms operations
    /// </summary>
    public class TermsApi : ITermsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<TermsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the TermsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public TermsApi(IApiClient apiClient, ILogger<TermsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get the agreement for the Lead Generation Ads feature.
        /// You must specify the feature that you want to request the agreement for using the term_type field.
        /// </summary>
        /// <param name="request">Terms get request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the terms content</returns>
        public async Task<TermGetResponse> GetTermsAsync(
            TermGetRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.AdvertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (string.IsNullOrEmpty(request.TermType))
                throw new ArgumentException("Term type cannot be null or empty", nameof(request));

            _logger?.LogInformation("Getting terms for advertiser {AdvertiserId} with term type {TermType}",
                request.AdvertiserId, request.TermType);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["term_type"] = request.TermType
            };

            if (!string.IsNullOrEmpty(request.Lang))
            {
                queryParams["lang"] = request.Lang;
            }

            return await _apiClient.CallApiAsync<TermGetResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TermsEndpoints.GetTerms}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <summary>
        // /// Sign the agreement for the Lead Generation Ads feature.
        // /// After signing an agreement, you can use CheckTermsStatusAsync to check the status of the agreement.
        // /// </summary>
        // /// <param name="request">Terms confirm request parameters</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response confirming the terms have been signed</returns>
        // public async Task<TermConfirmResponse> SignTermsAsync(
        //     TermConfirmRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrEmpty(request.TermType))
        //         throw new ArgumentException("Term type cannot be null or empty", nameof(request));

        //     _logger?.LogInformation("Signing terms for advertiser {AdvertiserId} with term type {TermType}",
        //         request.AdvertiserId, request.TermType);

        //     return await _apiClient.CallApiAsync<TermConfirmResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TermsEndpoints.SignTerms}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Check the status of an agreement.
        /// If the agreement has been signed, you can get the agreement using GetTermsAsync.
        /// </summary>
        /// <param name="request">Terms check request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the terms status</returns>
        //public async Task<TermCheckResponse> CheckTermsStatusAsync(
        //    TermCheckRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    if (string.IsNullOrEmpty(request.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //    if (string.IsNullOrEmpty(request.TermType))
        //        throw new ArgumentException("Term type cannot be null or empty", nameof(request));

        //    _logger?.LogInformation("Checking terms status for advertiser {AdvertiserId} with term type {TermType}",
        //        request.AdvertiserId, request.TermType);

        //    var queryParams = new Dictionary<string, string>
        //    {
        //        ["advertiser_id"] = request.AdvertiserId,
        //        ["term_type"] = request.TermType
        //    };

        //    return await _apiClient.CallApiAsync<TermCheckResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TermsEndpoints.CheckTermsStatus}",
        //        HttpMethod.Get,
        //        queryParams: queryParams,
        //        body: null,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}
    }
}
