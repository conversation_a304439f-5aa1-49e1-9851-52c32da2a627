/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Catalogs operations
    /// </summary>
    public interface ICatalogsApi
    {
        /// <summary>
        /// Create a catalog
        /// </summary>
        /// <param name="body">Request body containing catalog information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created catalog ID</returns>
        //Task<CatalogOperationResponse> CreateCatalogAsync(
        //    CatalogCreateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update the name of a catalog
        /// </summary>
        /// <param name="body">Request body containing catalog update information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated catalog ID</returns>
        //Task<CatalogOperationResponse> UpdateCatalogAsync(
        //    CatalogUpdateBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete a catalog
        /// </summary>
        /// <param name="body">Request body containing catalog deletion information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deleted catalog ID</returns>
        //Task<CatalogOperationResponse> DeleteCatalogAsync(
        //    CatalogDeleteBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get catalogs
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID (optional - if not specified, all catalogs will be returned)</param>
        /// <param name="page">Current number of pages (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10, range: 1-1000)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing catalog information</returns>
        Task<CatalogGetResponse> GetCatalogsAsync(
            string bcId,
            string? catalogId = null,
            int? page = null,
            int? pageSize = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the lexicon list for a catalog
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing catalog lexicon information</returns>
        Task<CatalogLexiconResponse> GetCatalogLexiconAsync(
            string bcId,
            string catalogId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Migrate a catalog to a Business Center
        /// </summary>
        /// <param name="body">Request body containing migration information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        //Task MigrateCatalogToBCAsync(
        //    CatalogCapitalizeBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get available regions
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing available region codes</returns>
        Task<CatalogAvailableCountryResponse> GetAvailableRegionsAsync(
            string bcId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get locations and currencies
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing location and currency information</returns>
        Task<CatalogLocationCurrencyResponse> GetLocationsCurrenciesAsync(
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the overview of a catalog
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing catalog overview information</returns>
        Task<CatalogOverviewResponse> GetCatalogOverviewAsync(
            string bcId,
            string catalogId,
            CancellationToken cancellationToken = default);
    }
}
