using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TikTok.ReportIntegratedAds
{
    /// <summary>
    /// DTO cho việc lấy danh sách báo cáo tích hợp Ad
    /// </summary>
    public class GetReportIntegratedAdListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// ID tài khoản nhà quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// ID chiến dịch
        /// </summary>
        [StringLength(100)]
        public string? CampaignId { get; set; }

        /// <summary>
        /// ID nhóm quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? AdGroupId { get; set; }

        /// <summary>
        /// ID quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? AdId { get; set; }

        /// <summary>
        /// Tên quảng cáo
        /// </summary>
        [StringLength(255)]
        public string? AdName { get; set; }

        /// <summary>
        /// Ngày bắt đầu
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Ngày kết thúc
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Bộ lọc tùy chỉnh
        /// </summary>
        public string? Filter { get; set; }
    }
} 