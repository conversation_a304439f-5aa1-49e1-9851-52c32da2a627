/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TikTokBusinessApi.Auth
{
    /// <summary>
    /// Interface for authentication factory that creates authentication instances per request
    /// </summary>
    public interface IAuthenticationFactory
    {
        /// <summary>
        /// Creates an authentication instance for the given context
        /// </summary>
        /// <param name="context">Authentication context (e.g., user ID, tenant ID)</param>
        /// <returns>Authentication instance for the context</returns>
        Task<IAuthentication?> CreateAuthenticationAsync(AuthenticationContext context);

        /// <summary>
        /// Gets the supported authentication types
        /// </summary>
        /// <returns>List of supported authentication type names</returns>
        IEnumerable<string> GetSupportedTypes();

        /// <summary>
        /// Validates if the factory can create authentication for the given context
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>True if authentication can be created</returns>
        Task<bool> CanCreateAuthenticationAsync(AuthenticationContext context);
    }

    /// <summary>
    /// Authentication context containing information needed to resolve authentication
    /// </summary>
    public class AuthenticationContext
    {
        /// <summary>
        /// User identifier (e.g., user ID, username, email)
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// Tenant or organization identifier
        /// </summary>
        public string? TenantId { get; set; }

        /// <summary>
        /// Authentication type (e.g., "tiktok", "oauth", "apikey")
        /// </summary>
        public string AuthenticationType { get; set; } = "tiktok";

        /// <summary>
        /// Additional context properties
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new();

        /// <summary>
        /// Request-specific metadata
        /// </summary>
        public Dictionary<string, string> RequestMetadata { get; set; } = new();

        /// <summary>
        /// Creates a simple authentication context with user ID
        /// </summary>
        /// <param name="userId">User identifier</param>
        /// <param name="authenticationType">Authentication type (default: "tiktok")</param>
        /// <returns>Authentication context</returns>
        public static AuthenticationContext ForUser(string userId, string authenticationType = "tiktok")
        {
            return new AuthenticationContext
            {
                UserId = userId,
                AuthenticationType = authenticationType
            };
        }

        /// <summary>
        /// Creates an authentication context with user and tenant
        /// </summary>
        /// <param name="userId">User identifier</param>
        /// <param name="tenantId">Tenant identifier</param>
        /// <param name="authenticationType">Authentication type (default: "tiktok")</param>
        /// <returns>Authentication context</returns>
        public static AuthenticationContext ForUserAndTenant(string userId, string tenantId, string authenticationType = "tiktok")
        {
            return new AuthenticationContext
            {
                UserId = userId,
                TenantId = tenantId,
                AuthenticationType = authenticationType
            };
        }

        /// <summary>
        /// Creates a default authentication context (for backward compatibility)
        /// </summary>
        /// <param name="authenticationType">Authentication type (default: "tiktok")</param>
        /// <returns>Authentication context</returns>
        public static AuthenticationContext Default(string authenticationType = "tiktok")
        {
            return new AuthenticationContext
            {
                AuthenticationType = authenticationType
            };
        }
    }

    /// <summary>
    /// Interface for resolving authentication credentials based on context
    /// </summary>
    public interface IAuthenticationResolver
    {
        /// <summary>
        /// Resolves authentication credentials for the given context
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>Authentication credentials or null if not found</returns>
        Task<AuthenticationCredentials?> ResolveCredentialsAsync(AuthenticationContext context);

        /// <summary>
        /// Checks if credentials can be resolved for the given context
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>True if credentials can be resolved</returns>
        Task<bool> CanResolveAsync(AuthenticationContext context);
    }

    /// <summary>
    /// Authentication credentials container
    /// </summary>
    public class AuthenticationCredentials
    {
        /// <summary>
        /// Application ID
        /// </summary>
        public string? AppId { get; set; }

        /// <summary>
        /// Access token
        /// </summary>
        public string? AccessToken { get; set; }

        /// <summary>
        /// API key (for API key authentication)
        /// </summary>
        public string? ApiKey { get; set; }

        /// <summary>
        /// Client secret (for OAuth)
        /// </summary>
        public string? ClientSecret { get; set; }

        /// <summary>
        /// Additional credential properties
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new();

        /// <summary>
        /// Credential expiration time (if applicable)
        /// </summary>
        public DateTimeOffset? ExpiresAt { get; set; }

        /// <summary>
        /// Whether the credentials are expired
        /// </summary>
        public bool IsExpired => ExpiresAt.HasValue && ExpiresAt.Value <= DateTimeOffset.UtcNow;

        /// <summary>
        /// Creates TikTok credentials
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="accessToken">Access token</param>
        /// <returns>TikTok authentication credentials</returns>
        public static AuthenticationCredentials ForTikTok(string appId, string accessToken)
        {
            return new AuthenticationCredentials
            {
                AppId = appId,
                AccessToken = accessToken
            };
        }

        /// <summary>
        /// Creates API key credentials
        /// </summary>
        /// <param name="apiKey">API key</param>
        /// <returns>API key authentication credentials</returns>
        public static AuthenticationCredentials ForApiKey(string apiKey)
        {
            return new AuthenticationCredentials
            {
                ApiKey = apiKey
            };
        }
    }

    /// <summary>
    /// Factory registration for different authentication types
    /// </summary>
    public interface IAuthenticationFactoryRegistry
    {
        /// <summary>
        /// Registers an authentication factory for a specific type
        /// </summary>
        /// <param name="authenticationType">Authentication type name</param>
        /// <param name="factory">Authentication factory</param>
        void RegisterFactory(string authenticationType, IAuthenticationFactory factory);

        /// <summary>
        /// Gets a factory for the specified authentication type
        /// </summary>
        /// <param name="authenticationType">Authentication type name</param>
        /// <returns>Authentication factory or null if not found</returns>
        IAuthenticationFactory? GetFactory(string authenticationType);

        /// <summary>
        /// Gets all registered authentication types
        /// </summary>
        /// <returns>List of registered authentication types</returns>
        IEnumerable<string> GetRegisteredTypes();

        /// <summary>
        /// Creates an authentication instance using the appropriate factory
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>Authentication instance or null if not found</returns>
        Task<IAuthentication?> CreateAuthenticationAsync(AuthenticationContext context);
    }
}
