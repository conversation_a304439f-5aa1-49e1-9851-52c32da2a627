/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Net.Http;
using System.Text;

namespace TikTokBusinessApi.Auth
{
    /// <summary>
    /// HTTP Basic authentication implementation
    /// </summary>
    public class HttpBasicAuth : IAuthentication
    {
        /// <summary>
        /// Username for basic authentication
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// Password for basic authentication
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// Initializes a new instance of the HttpBasicAuth class
        /// </summary>
        public HttpBasicAuth()
        {
        }

        /// <summary>
        /// Initializes a new instance of the HttpBasicAuth class with credentials
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        public HttpBasicAuth(string username, string password)
        {
            Username = username;
            Password = password;
        }

        /// <summary>
        /// Applies HTTP Basic authentication to the HTTP request
        /// </summary>
        /// <param name="request">HTTP request message</param>
        public void ApplyToRequest(HttpRequestMessage request)
        {
            if (string.IsNullOrEmpty(Username) && string.IsNullOrEmpty(Password))
                return;

            var credentials = $"{Username ?? ""}:{Password ?? ""}";
            var encodedCredentials = Convert.ToBase64String(Encoding.UTF8.GetBytes(credentials));
            
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", encodedCredentials);
        }
    }
}
