/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for video publishing
    /// </summary>
    public class VideoPublishResponse
    {
        /// <summary>
        /// Unique identifier for the video post publishing task
        /// </summary>
        [JsonPropertyName("share_id")]
        public string ShareId { get; set; } = string.Empty;
    }
}
