using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Customers;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.Customers
{
    /// <summary>
    /// Interface cho CustomerAdAccount AppService
    /// </summary>
    public interface ICustomerAdAccountAppService : 
        ICrudAppService<
            CustomerAdAccountDto,
            Guid,
            PagedAndSortedResultRequestDto,
            CreateCustomerAdAccountDto,
            UpdateCustomerAdAccountDto>
    {
        /// <summary>
        /// Lấy danh sách tài khoản quảng cáo theo Customer ID
        /// </summary>
        /// <param name="customerId">ID của khách hàng</param>
        /// <returns>Danh sách tài khoản quảng cáo</returns>
        Task<List<CustomerAdAccountDto>> GetByCustomerIdAsync(Guid customerId);

        /// <summary>
        /// Lấy tài khoản quảng cáo theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của advertiser</param>
        /// <returns>Tài khoản quảng cáo</returns>
        Task<CustomerAdAccountDto?> GetByAdvertiserIdAsync(string advertiserId);

        /// <summary>
        /// Lấy tài khoản quảng cáo theo Shop ID
        /// </summary>
        /// <param name="shopId">ID của shop</param>
        /// <returns>Tài khoản quảng cáo</returns>
        Task<CustomerAdAccountDto?> GetByShopIdAsync(string shopId);

        /// <summary>
        /// Lấy danh sách tài khoản khách hàng dạng bảng dàn phẳng
        /// </summary>
        /// <param name="input">Thông tin tìm kiếm và phân trang</param>
        /// <returns>Danh sách tài khoản khách hàng</returns>
        Task<PagedResultDto<CustomerAdAccountFlatDto>> GetFlatListAsync(GetCustomerAdAccountListDto input);
    }
}
