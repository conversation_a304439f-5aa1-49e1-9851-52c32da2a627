/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for adding a business property
    /// </summary>
    public class BusinessPropertyAddRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// URL property to add
        /// </summary>
        [JsonPropertyName("url")]
        public string? Url { get; set; }

        /// <summary>
        /// Property name
        /// </summary>
        [JsonPropertyName("property_name")]
        public string? PropertyName { get; set; }

        /// <summary>
        /// Property type
        /// </summary>
        [JsonPropertyName("property_type")]
        public string? PropertyType { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"BusinessPropertyAddRequest(BusinessId={BusinessId}, Url={Url}, PropertyName={PropertyName})";
        }
    }

    /// <summary>
    /// Response data for property verification API
    /// </summary>
    public class PropertyVerificationResponse
    {
        /// <summary>
        /// Property ID
        /// </summary>
        [JsonPropertyName("property_id")]
        public string? PropertyId { get; set; }

        /// <summary>
        /// Verification status
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Verification method
        /// </summary>
        [JsonPropertyName("verification_method")]
        public string? VerificationMethod { get; set; }

        /// <summary>
        /// Verification token (if applicable)
        /// </summary>
        [JsonPropertyName("verification_token")]
        public string? VerificationToken { get; set; }

        /// <summary>
        /// Verification instructions
        /// </summary>
        [JsonPropertyName("instructions")]
        public string? Instructions { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"PropertyVerificationResponse(PropertyId={PropertyId}, Status={Status})";
        }
    }

    /// <summary>
    /// Request body for deleting a business property
    /// </summary>
    public class BusinessPropertyDeleteRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Property ID to delete
        /// </summary>
        [JsonPropertyName("property_id")]
        public string? PropertyId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"BusinessPropertyDeleteRequest(BusinessId={BusinessId}, PropertyId={PropertyId})";
        }
    }

    /// <summary>
    /// Response data for business property list API
    /// </summary>
    public class BusinessPropertyListResponse
    {
        /// <summary>
        /// List of properties
        /// </summary>
        [JsonPropertyName("properties")]
        public List<PropertyData>? Properties { get; set; }

        /// <summary>
        /// Total count of properties
        /// </summary>
        [JsonPropertyName("total_count")]
        public int? TotalCount { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"BusinessPropertyListResponse(Properties={Properties?.Count}, TotalCount={TotalCount})";
        }
    }

    /// <summary>
    /// Property data
    /// </summary>
    public class PropertyData
    {
        /// <summary>
        /// Property ID
        /// </summary>
        [JsonPropertyName("property_id")]
        public string? PropertyId { get; set; }

        /// <summary>
        /// Property name
        /// </summary>
        [JsonPropertyName("property_name")]
        public string? PropertyName { get; set; }

        /// <summary>
        /// Property URL
        /// </summary>
        [JsonPropertyName("url")]
        public string? Url { get; set; }

        /// <summary>
        /// Property type
        /// </summary>
        [JsonPropertyName("property_type")]
        public string? PropertyType { get; set; }

        /// <summary>
        /// Verification status
        /// </summary>
        [JsonPropertyName("verification_status")]
        public string? VerificationStatus { get; set; }

        /// <summary>
        /// Date when property was added
        /// </summary>
        [JsonPropertyName("created_time")]
        public string? CreatedTime { get; set; }

        /// <summary>
        /// Date when property was last verified
        /// </summary>
        [JsonPropertyName("verified_time")]
        public string? VerifiedTime { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"PropertyData(PropertyId={PropertyId}, PropertyName={PropertyName}, Url={Url}, Status={VerificationStatus})";
        }
    }

    /// <summary>
    /// Property verification status enumeration
    /// </summary>
    public static class PropertyVerificationStatus
    {
        public const string Pending = "PENDING";
        public const string Verified = "VERIFIED";
        public const string Failed = "FAILED";
        public const string Expired = "EXPIRED";
    }

    /// <summary>
    /// Property type enumeration
    /// </summary>
    public static class PropertyType
    {
        public const string Website = "WEBSITE";
        public const string App = "APP";
        public const string Other = "OTHER";
    }

    /// <summary>
    /// Verification method enumeration
    /// </summary>
    public static class VerificationMethod
    {
        public const string HtmlFile = "HTML_FILE";
        public const string MetaTag = "META_TAG";
        public const string DnsRecord = "DNS_RECORD";
    }
}
