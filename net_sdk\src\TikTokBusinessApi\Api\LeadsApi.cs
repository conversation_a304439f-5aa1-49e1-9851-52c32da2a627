/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Leads operations
    /// </summary>
    public class LeadsApi : ILeadsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<LeadsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the LeadsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public LeadsApi(IApiClient apiClient, ILogger<LeadsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Create a test lead for end-to-end testing for lead generation
        /// </summary>
        /// <param name="request">Request containing test lead parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created test lead data</returns>
        // public async Task<CreateTestLeadResponse> CreateTestLeadAsync(
        //     CreateTestLeadRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogDebug("Creating test lead for advertiser {AdvertiserId} or library {LibraryId}", 
        //         request.AdvertiserId, request.LibraryId);

        //     var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{LeadsEndpoints.CreateTestLead}";

        //     return await _apiClient.CallApiAsync<CreateTestLeadResponse>(
        //         path,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken);
        // }

        /// <summary>
        /// Get a test lead
        /// </summary>
        /// <param name="request">Request containing test lead parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the test lead data</returns>
        public async Task<GetTestLeadResponse> GetTestLeadAsync(
            GetTestLeadRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting test lead for advertiser {AdvertiserId} or library {LibraryId}",
                request.AdvertiserId, request.LibraryId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{LeadsEndpoints.GetTestLead}";

            var queryParams = new Dictionary<string, string>();

            if (!string.IsNullOrEmpty(request.LeadSource))
                queryParams["lead_source"] = request.LeadSource;

            if (!string.IsNullOrEmpty(request.AdvertiserId))
                queryParams["advertiser_id"] = request.AdvertiserId;

            if (!string.IsNullOrEmpty(request.LibraryId))
                queryParams["library_id"] = request.LibraryId;

            if (!string.IsNullOrEmpty(request.PageId))
                queryParams["page_id"] = request.PageId;

            return await _apiClient.CallApiAsync<GetTestLeadResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }

        /// <summary>
        /// Delete an existing test lead
        /// </summary>
        /// <param name="request">Request containing test lead deletion parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the asynchronous operation</returns>
        // public async Task DeleteTestLeadAsync(
        //     DeleteTestLeadRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogDebug("Deleting test lead {LeadId} for advertiser {AdvertiserId} or library {LibraryId}", 
        //         request.LeadId, request.AdvertiserId, request.LibraryId);

        //     var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{LeadsEndpoints.DeleteTestLead}";

        //     await _apiClient.CallApiAsync<object>(
        //         path,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken);
        // }

        /// <summary>
        /// Create a download task for all the leads for the page_id or ad_id passed in the request
        /// </summary>
        /// <param name="request">Request containing lead download task parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the download task information</returns>
        // public async Task<CreateLeadDownloadTaskResponse> CreateLeadDownloadTaskAsync(
        //     CreateLeadDownloadTaskRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogDebug("Creating lead download task for advertiser {AdvertiserId} or library {LibraryId}", 
        //         request.AdvertiserId, request.LibraryId);

        //     var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{LeadsEndpoints.CreateLeadDownloadTask}";

        //     return await _apiClient.CallApiAsync<CreateLeadDownloadTaskResponse>(
        //         path,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken);
        // }

        /// <summary>
        /// Download leads. You need to create a download task first and ensure the task is complete
        /// </summary>
        /// <param name="request">Request containing lead download parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Raw CSV or ZIP file content as byte array</returns>
        public async Task<byte[]> DownloadLeadsAsync(
            DownloadLeadsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Downloading leads for task {TaskId}", request.TaskId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{LeadsEndpoints.DownloadLeads}";

            var queryParams = new Dictionary<string, string>
            {
                ["task_id"] = request.TaskId
            };

            if (!string.IsNullOrEmpty(request.AdvertiserId))
                queryParams["advertiser_id"] = request.AdvertiserId;

            if (!string.IsNullOrEmpty(request.LibraryId))
                queryParams["library_id"] = request.LibraryId;

            // This endpoint returns raw file content (CSV or ZIP), not JSON
            return await _apiClient.CallApiAsync<byte[]>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }

        /// <summary>
        /// Get form libraries that you have access to
        /// </summary>
        /// <param name="request">Request containing pagination parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing form libraries</returns>
        public async Task<GetFormLibrariesResponse> GetFormLibrariesAsync(
            GetFormLibrariesRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting form libraries with page {Page} and page size {PageSize}",
                request.Page, request.PageSize);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{LeadsEndpoints.GetFormLibraries}";

            var queryParams = new Dictionary<string, string>();

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();

            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            return await _apiClient.CallApiAsync<GetFormLibrariesResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }

        /// <summary>
        /// Migrate leads from an ad account to a Business Center
        /// </summary>
        /// <param name="request">Request containing migration parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created form library ID</returns>
        // public async Task<MigrateLeadsToBCResponse> MigrateLeadsToBCAsync(
        //     MigrateLeadsToBCRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogDebug("Migrating leads from advertiser {AdvertiserId} to BC {BcId}", 
        //         request.AdvertiserId, request.BcId);

        //     var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{LeadsEndpoints.MigrateLeadsToBC}";

        //     return await _apiClient.CallApiAsync<MigrateLeadsToBCResponse>(
        //         path,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken);
        // }

        /// <summary>
        /// Get the fields of an Instant Form (or lead form)
        /// </summary>
        /// <param name="request">Request containing Instant Form parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the Instant Form fields</returns>
        public async Task<GetInstantFormFieldsResponse> GetInstantFormFieldsAsync(
            GetInstantFormFieldsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting Instant Form fields for advertiser {AdvertiserId} and page {PageId}",
                request.AdvertiserId, request.PageId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{LeadsEndpoints.GetInstantFormFields}";

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["page_id"] = request.PageId
            };

            return await _apiClient.CallApiAsync<GetInstantFormFieldsResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }

        /// <summary>
        /// Get fields of an Instant Form or direct message leads
        /// </summary>
        /// <param name="request">Request containing lead field parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the lead fields</returns>
        public async Task<GetLeadFieldsResponse> GetLeadFieldsAsync(
            GetLeadFieldsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting lead fields for source {LeadSource} and advertiser {AdvertiserId} or library {LibraryId}",
                request.LeadSource, request.AdvertiserId, request.LibraryId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{LeadsEndpoints.GetLeadFields}";

            var queryParams = new Dictionary<string, string>
            {
                ["lead_source"] = request.LeadSource
            };

            if (!string.IsNullOrEmpty(request.AdvertiserId))
                queryParams["advertiser_id"] = request.AdvertiserId;

            if (!string.IsNullOrEmpty(request.LibraryId))
                queryParams["library_id"] = request.LibraryId;

            if (!string.IsNullOrEmpty(request.PageId))
                queryParams["page_id"] = request.PageId;

            return await _apiClient.CallApiAsync<GetLeadFieldsResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }

        /// <summary>
        /// Get an Instant Form lead or a direct message lead
        /// </summary>
        /// <param name="request">Request containing lead parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the lead data</returns>
        public async Task<GetLeadResponse> GetLeadAsync(
            GetLeadRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting lead for source {LeadSource} and advertiser {AdvertiserId} or library {LibraryId}",
                request.LeadSource, request.AdvertiserId, request.LibraryId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{LeadsEndpoints.GetLead}";

            var queryParams = new Dictionary<string, string>
            {
                ["lead_source"] = request.LeadSource
            };

            if (!string.IsNullOrEmpty(request.AdvertiserId))
                queryParams["advertiser_id"] = request.AdvertiserId;

            if (!string.IsNullOrEmpty(request.LibraryId))
                queryParams["library_id"] = request.LibraryId;

            if (!string.IsNullOrEmpty(request.PageId))
                queryParams["page_id"] = request.PageId;

            return await _apiClient.CallApiAsync<GetLeadResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }
    }
}
