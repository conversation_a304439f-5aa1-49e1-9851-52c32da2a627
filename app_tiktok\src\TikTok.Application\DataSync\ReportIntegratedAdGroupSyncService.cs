using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu báo cáo tích hợp AdGroup
    /// </summary>
    public class ReportIntegratedAdGroupSyncService : BaseSyncService, IReportIntegratedAdGroupSyncService
    {
        private readonly IRawReportIntegratedAdGroupRepository _reportIntegratedAdGroupRepository;
        private readonly IRepository<RawReportIntegratedAdGroupEntity, Guid> _reportIntegratedAdGroupEntityRepository;
        private readonly IAdAccountRepository _adAccountRepository;

        public ReportIntegratedAdGroupSyncService(
            IServiceProvider serviceProvider,
            IRawReportIntegratedAdGroupRepository reportIntegratedAdGroupRepository,
            IRepository<RawReportIntegratedAdGroupEntity, Guid> reportIntegratedAdGroupEntityRepository,
            ILogger<ReportIntegratedAdGroupSyncService> logger,
            IAdAccountRepository adAccountRepository) : base(serviceProvider, logger)
        {
            _reportIntegratedAdGroupRepository = reportIntegratedAdGroupRepository;
            _reportIntegratedAdGroupEntityRepository = reportIntegratedAdGroupEntityRepository;
            _adAccountRepository = adAccountRepository;
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdGroup cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedAdGroupSyncResult> SyncAllReportIntegratedAdGroupForAllBcsAsync()
        {
            var result = new ReportIntegratedAdGroupSyncResult
            {
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp AdGroup cho tất cả BC");

                var allBcs = await _businessApplicationCache.GetAllActiveAsync();
                var bcIds = allBcs.Select(x => x.BcId).Distinct().ToList();

                var totalResult = new ReportIntegratedAdGroupSyncResult
                {
                };

                foreach (var bcId in bcIds)
                {
                    var bcResult = await SyncReportIntegratedAdGroupAsync(bcId);
                    totalResult.TotalSynced += bcResult.TotalSynced;
                    totalResult.NewRecords += bcResult.NewRecords;
                    totalResult.UpdatedRecords += bcResult.UpdatedRecords;
                    totalResult.DayCount += bcResult.DayCount;
                    totalResult.AdGroupCount += bcResult.AdGroupCount;
                    totalResult.BcCount++;
                }

                result = totalResult;

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo tích hợp AdGroup cho tất cả BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}, AdGroup: {AdGroupCount}, Ngày: {DayCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount, result.AdGroupCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp AdGroup cho tất cả BC");
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo tích hợp AdGroup: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp AdGroup cho tất cả BC");
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdGroup theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedAdGroupSyncResult> SyncReportIntegratedAdGroupAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var result = new ReportIntegratedAdGroupSyncResult
            {
                BcCount = 1
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp AdGroup cho BC: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                string bcTimezone = DateTimeService.UTC_TIMEZONE;

                var bc = await _businessCenterCache.GetByBcIdAsync(bcId);
                if (bc != null && !string.IsNullOrEmpty(bc.Timezone))
                {
                    bcTimezone = bc.Timezone;
                }

                (DateTime startDate, DateTime endDate) rangeDateFiltering;
                if (startDate.HasValue && endDate.HasValue)
                {
                    if(startDate.Value > endDate.Value)
                    {
                        throw new UserFriendlyException("Ngày bắt đầu phải nhỏ hơn ngày kết thúc");
                    }
                    rangeDateFiltering = (startDate.Value, endDate.Value);
                }
                else
                {
                    rangeDateFiltering = await GetRangeDateFiltering(bcId, bcTimezone);
                }

                var adAccountIds = await _adAccountRepository.GetByBcIdAsync(bcId);

                var currentDate = rangeDateFiltering.startDate.Date;
                var end = rangeDateFiltering.endDate.Date;

                while (currentDate <= end)
                {
                    var pageEnd = currentDate.AddDays(30);
                    var daysDifference = (end - currentDate).TotalDays;
                    if (daysDifference < 30)
                    {
                        pageEnd = end;
                    }
                    _logger.LogDebug("Đồng bộ dữ liệu báo cáo AdGroup cho ngày: {Date}", currentDate.ToString("yyyy-MM-dd"));

                    // Một lần chỉ xử lý tối đa 5 tài khoản quảng cáo
                    int pageSize = 5;
                    var totalPages = (int)Math.Ceiling((double)adAccountIds.Count / pageSize);
                    for (int page = 0; page < totalPages; page++)
                    {
                        var adAccountIdsChunk = adAccountIds.Skip(page * pageSize).Take(pageSize).ToList();
                        var apiResponse = await GetSyncReportIntegratedAdGroupFromApiAsync(tikTokClient, bcId, adAccountIdsChunk, currentDate, pageEnd);
                        if (apiResponse != null && apiResponse.Any())
                        {
                            await ProcessReportDataAsync(bcId, adAccountIdsChunk, bcTimezone, apiResponse, result);
                        }
                        else
                        {
                            _logger.LogDebug("Không có dữ liệu báo cáo AdGroup cho BC: {BcId}, Ngày: {Date}", bcId, currentDate.ToString("yyyy-MM-dd"));
                        }
                    }

                    if (pageEnd == end)
                    {
                        break;
                    }
                    currentDate = currentDate.AddDays(29);
                }

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo tích hợp AdGroup cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, AdGroup: {AdGroupCount}, Ngày: {DayCount}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.AdGroupCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp AdGroup cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo tích hợp AdGroup: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp AdGroup cho BC: {BcId}", bcId);
            }

            return result;
        }

        private async Task<(DateTime startDate, DateTime endDate)> GetRangeDateFiltering(string bcId, string timezone)
        {
            // Lấy ngày hiện tại theo timezone của BC
            DateTime currentDateInTimezone = _dateTimeService.GetDateNow(timezone);
            DateTime endDate = currentDateInTimezone;

            // Lấy dữ liệu báo cáo tích hợp ad group mới nhất (theo giờ)
            var latestReport = await _reportIntegratedAdGroupRepository.GetLatestByBcIdAsync(bcId);

            DateTime startDate;
            if (latestReport == null)
            {
                // Nếu chưa có dữ liệu trong DB thì lấy khoảng 1 tuần từ ngày hiện tại
                startDate = currentDateInTimezone.AddDays(-LAST_SYNC_DAYS);
                _logger.LogDebug("Chưa có dữ liệu báo cáo AdGroup trong DB cho BC: {BcId}. Lấy dữ liệu 1 tuần từ {StartDate} đến {EndDate}",
                    bcId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            }
            else
            {
                // Nếu có dữ liệu trong DB thì lấy từ ngày trong DB đến ngày hiện tại
                // Convert từ UTC (trong DB) sang timezone của BC để so sánh
                var latestReportDateInTimezone = _dateTimeService.ConvertFromUtc(latestReport.Date, timezone).Date;
                startDate = latestReportDateInTimezone;
                _logger.LogDebug("Có dữ liệu báo cáo AdGroup trong DB cho BC: {BcId}. Lấy từ {StartDate} đến {EndDate}",
                    bcId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            }

            return (startDate, endDate);
        }

        /// <summary>
        /// Lấy dữ liệu báo cáo tích hợp AdGroup từ TikTok API
        /// Lưu ý: Khi lấy báo cáo theo giờ, startDate và endDate phải cùng một ngày
        /// </summary>
        private async Task<List<ReportDataItem>> GetSyncReportIntegratedAdGroupFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, List<string> adAccountIds, DateTime startDate, DateTime endDate)
        {

            var records = new List<ReportDataItem>();
            var page = 1;
            const int pageSize = PAGE_SIZE_SYNC_REPORT;

            while (true)
            {
                var request = new SynchronousReportRequest
                {
                    AdvertiserIds = adAccountIds,
                    ServiceType = "AUCTION",
                    ReportType = "BASIC",
                    DataLevel = "AUCTION_ADGROUP",  
                    Dimensions = new List<string> { "adgroup_id", "stat_time_day" },
                    Metrics = new List<string>
                    {
                        "advertiser_name", "advertiser_id", "campaign_name", "campaign_id", "spend", "impressions", "clicks", "ctr", "cpm", "cpc", "conversion", "cost_per_conversion", "conversion_rate_v2", "reach", "frequency",
                        "onsite_shopping_roas", "total_onsite_shopping_value", "onsite_shopping", "cost_per_onsite_shopping", "value_per_onsite_shopping", "onsite_on_web_detail", "onsite_on_web_cart", "onsite_initiate_checkout_count",
                        "adgroup_name", "placement_type", "budget", "smart_target", "billing_event", "bid_strategy", "bid", "currency", "timezone"
                    },
                    StartDate = startDate.ToString("yyyy-MM-dd"),
                    EndDate = endDate.ToString("yyyy-MM-dd"),
                    Page = page,
                    PageSize = pageSize
                };

                var response = await tikTokClient.Reporting.GetSynchronousReportAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu báo cáo tích hợp AdGroup: {response.Message}");
                }

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break;
                }

                records.AddRange(response.Data.List);

                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            return records;
        }

        /// <summary>
        /// Xử lý dữ liệu báo cáo từ API
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="adAccountIds">Danh sách ID tài khoản quảng cáo</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportDataList">Danh sách dữ liệu báo cáo</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task ProcessReportDataAsync(string bcId, List<string> adAccountIds, string bcTimezone, List<ReportDataItem> reportDataList, ReportIntegratedAdGroupSyncResult result)
        {
            // sử dụng UnitOfWork để đảm bảo tính toàn vẹn dữ liệu cho mỗi 1000 bản ghi
            if (reportDataList == null || !reportDataList.Any())
            {
                _logger.LogDebug("Không có dữ liệu báo cáo AdGroup để xử lý cho {Total} bản ghi", reportDataList.Count);
                return;
            }

            var pageSize = PAGE_SIZE_HANDLE_SAVE_TO_DATABASE;
            var totalPages = (int)Math.Ceiling((double)reportDataList.Count / pageSize);
            _logger.LogDebug("Bắt đầu xử lý {TotalRecords} bản ghi báo cáo AdGroup cho {TotalPages} trang", reportDataList.Count, totalPages);

            for (int page = 0; page < totalPages; page++)
            {
                var pageData = reportDataList.Skip(page * pageSize).Take(pageSize).ToList();
                if (pageData.Any())
                {
                    try
                    {
                        await ProcessPageDataAsync(bcId, bcTimezone, pageData, result);
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý dữ liệu báo cáo AdGroup cho {Total} bản ghi, Trang: {Page}", pageData.Count, page + 1);
                        result.ErrorMessage += ex.Message + Environment.NewLine;
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        continue; // Bỏ qua lỗi và tiếp tục với trang tiếp theo
                    }
                }
            }
        }

        private async Task ProcessPageDataAsync(string bcId, string bcTimezone, List<ReportDataItem> pageData, ReportIntegratedAdGroupSyncResult result)
        {
            // use unit of work
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var mappedEntities = await MapListReportDataToEntitiesAsync(bcId, bcTimezone, pageData);

                var existingEntities = await _reportIntegratedAdGroupRepository.GetByBcIdAndDateRangeAsync(bcId, mappedEntities.Min(x => x.Date), mappedEntities.Max(x => x.Date));

                var insertedEntities = new List<RawReportIntegratedAdGroupEntity>();
                var updatedEntities = new List<RawReportIntegratedAdGroupEntity>();

                foreach (var mappedEntity in mappedEntities)
                {
                    // So sánh theo AdGroupId và Date với độ chính xác đến giờ
                    var currentEntity = existingEntities.FirstOrDefault(x => x.AdGroupId == mappedEntity.AdGroupId && x.Date == mappedEntity.Date);
                    if (currentEntity == null)
                    {
                        insertedEntities.Add(mappedEntity);
                        result.NewRecords++;
                        result.TotalSynced++;
                    }
                    else
                    {
                        // Cập nhật nếu có thay đổi
                        if (currentEntity.HasChanged(mappedEntity))
                        {
                            UpdateEntityFromNewData(currentEntity, mappedEntity);
                            updatedEntities.Add(currentEntity);
                            result.UpdatedRecords++;
                            result.TotalSynced++;
                        }
                    }
                }

                // Thêm các bản ghi mới vào kho dữ liệu
                if (insertedEntities.Any())
                {
                    await _reportIntegratedAdGroupEntityRepository.InsertManyAsync(insertedEntities);
                }
                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _reportIntegratedAdGroupEntityRepository.UpdateManyAsync(updatedEntities);
                }

                result.DayCount += pageData.Count;
                result.AdGroupCount += pageData.Select(x => x.Dimensions?.GetValueOrDefault("adgroup_id")?.ToString()).Distinct().Count();

                await uow.CompleteAsync();
            }
        }

        private async Task<List<RawReportIntegratedAdGroupEntity>> MapListReportDataToEntitiesAsync(string bcId, string bcTimezone, List<ReportDataItem> reportDataList)
        {
            var entities = new List<RawReportIntegratedAdGroupEntity>();
            foreach (var reportData in reportDataList)
            {
                var entity = await MapReportDataToEntityAsync(bcId, bcTimezone, reportData);
                if (entity != null)
                {
                    entities.Add(entity);
                }
            }
            return entities;
        }

        /// <summary>
        /// Map dữ liệu báo cáo từ API sang entity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportData">Dữ liệu báo cáo</param>
        /// <returns>RawReportIntegratedAdGroupEntity</returns>
        private async Task<RawReportIntegratedAdGroupEntity?> MapReportDataToEntityAsync(string bcId, string bcTimezone, ReportDataItem reportData)
        {
            var dateTimeStr = reportData.Dimensions?.GetValueOrDefault("stat_time_day")?.ToString();

            if (string.IsNullOrEmpty(dateTimeStr) || !DateTime.TryParse(dateTimeStr, out var reportDateTime))
            {
                _logger.LogWarning("Ngày giờ báo cáo AdGroup không hợp lệ: {DateTime} cho BC: {BcId}", dateTimeStr, bcId);
                return null;
            }

            var adGroupTimezone = GetStringValue(reportData.Metrics, "timezone") ?? bcTimezone;
            // Convert datetime từ timezone của adgroup sang UTC
            var reportDateTimeUtc = _dateTimeService.ConvertToUtc(reportDateTime, adGroupTimezone);

            var adGroupId = reportData.Dimensions?.GetValueOrDefault("adgroup_id")?.ToString();
            if (string.IsNullOrEmpty(adGroupId))
            {
                _logger.LogWarning("AdGroup ID không hợp lệ cho BC: {BcId}", bcId);
                return null;
            }

            var entity = new RawReportIntegratedAdGroupEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdGroupId = adGroupId,
                Date = reportDateTimeUtc  // Lưu datetime UTC với giờ
            };

            if (reportData.Metrics != null)
            {
                // Map các metrics từ API response
                entity.AdvertiserId = GetStringValue(reportData.Metrics, "advertiser_id") ?? string.Empty;
                entity.AdvertiserName = GetStringValue(reportData.Metrics, "advertiser_name") ?? string.Empty;
                entity.CampaignId = GetStringValue(reportData.Metrics, "campaign_id") ?? string.Empty;
                entity.CampaignName = GetStringValue(reportData.Metrics, "campaign_name") ?? string.Empty;
                entity.AdGroupName = GetStringValue(reportData.Metrics, "adgroup_name") ?? string.Empty;
                entity.Spend = GetDecimalValue(reportData.Metrics, "spend");
                entity.Impressions = GetLongValue(reportData.Metrics, "impressions");
                entity.Clicks = GetLongValue(reportData.Metrics, "clicks");
                entity.Ctr = GetDecimalValue(reportData.Metrics, "ctr");
                entity.Cpm = GetDecimalValue(reportData.Metrics, "cpm");
                entity.Cpc = GetDecimalValue(reportData.Metrics, "cpc");
                entity.Conversion = GetLongValue(reportData.Metrics, "conversion");
                entity.CostPerConversion = GetDecimalValue(reportData.Metrics, "cost_per_conversion");
                entity.ConversionRateV2 = GetDecimalValue(reportData.Metrics, "conversion_rate_v2");
                entity.Reach = GetLongValue(reportData.Metrics, "reach");
                entity.Frequency = GetDecimalValue(reportData.Metrics, "frequency");
                entity.OnsiteShoppingRoas = GetDecimalValue(reportData.Metrics, "onsite_shopping_roas");
                entity.TotalOnsiteShoppingValue = GetDecimalValue(reportData.Metrics, "total_onsite_shopping_value");
                entity.OnsiteShopping = GetLongValue(reportData.Metrics, "onsite_shopping");
                entity.CostPerOnsiteShopping = GetDecimalValue(reportData.Metrics, "cost_per_onsite_shopping");
                entity.ValuePerOnsiteShopping = GetDecimalValue(reportData.Metrics, "value_per_onsite_shopping");
                entity.OnsiteOnWebDetail = GetLongValue(reportData.Metrics, "onsite_on_web_detail");
                entity.OnsiteOnWebCart = GetLongValue(reportData.Metrics, "onsite_on_web_cart");
                entity.OnsiteInitiateCheckoutCount = GetLongValue(reportData.Metrics, "onsite_initiate_checkout_count");
                entity.PlacementType = GetStringValue(reportData.Metrics, "placement_type");
                entity.Budget = GetStringValue(reportData.Metrics, "budget");
                entity.SmartTarget = GetStringValue(reportData.Metrics, "smart_target");
                entity.BillingEvent = GetStringValue(reportData.Metrics, "billing_event");
                entity.BidStrategy = GetStringValue(reportData.Metrics, "bid_strategy");
                entity.Bid = GetStringValue(reportData.Metrics, "bid");
                entity.Currency = GetStringValue(reportData.Metrics, "currency") ?? "USD";
            }

            return entity;
        }

        /// <summary>
        /// Cập nhật entity từ dữ liệu mới
        /// </summary>
        /// <param name="existingEntity">Entity hiện tại</param>
        /// <param name="newEntity">Entity mới</param>
        private void UpdateEntityFromNewData(RawReportIntegratedAdGroupEntity existingEntity, RawReportIntegratedAdGroupEntity newEntity)
        {
            existingEntity.AdvertiserId = newEntity.AdvertiserId;
            existingEntity.AdvertiserName = newEntity.AdvertiserName;
            existingEntity.CampaignId = newEntity.CampaignId;
            existingEntity.CampaignName = newEntity.CampaignName;
            existingEntity.AdGroupName = newEntity.AdGroupName;
            existingEntity.Spend = newEntity.Spend;
            existingEntity.Impressions = newEntity.Impressions;
            existingEntity.Clicks = newEntity.Clicks;
            existingEntity.Ctr = newEntity.Ctr;
            existingEntity.Cpm = newEntity.Cpm;
            existingEntity.Cpc = newEntity.Cpc;
            existingEntity.Conversion = newEntity.Conversion;
            existingEntity.CostPerConversion = newEntity.CostPerConversion;
            existingEntity.ConversionRateV2 = newEntity.ConversionRateV2;
            existingEntity.Reach = newEntity.Reach;
            existingEntity.Frequency = newEntity.Frequency;
            existingEntity.OnsiteShoppingRoas = newEntity.OnsiteShoppingRoas;
            existingEntity.TotalOnsiteShoppingValue = newEntity.TotalOnsiteShoppingValue;
            existingEntity.OnsiteShopping = newEntity.OnsiteShopping;
            existingEntity.CostPerOnsiteShopping = newEntity.CostPerOnsiteShopping;
            existingEntity.ValuePerOnsiteShopping = newEntity.ValuePerOnsiteShopping;
            existingEntity.OnsiteOnWebDetail = newEntity.OnsiteOnWebDetail;
            existingEntity.OnsiteOnWebCart = newEntity.OnsiteOnWebCart;
            existingEntity.OnsiteInitiateCheckoutCount = newEntity.OnsiteInitiateCheckoutCount;
            existingEntity.PlacementType = newEntity.PlacementType;
            existingEntity.Budget = newEntity.Budget;
            existingEntity.SmartTarget = newEntity.SmartTarget;
            existingEntity.BillingEvent = newEntity.BillingEvent;
            existingEntity.BidStrategy = newEntity.BidStrategy;
            existingEntity.Bid = newEntity.Bid;
            existingEntity.Currency = newEntity.Currency;
        }
    }
}