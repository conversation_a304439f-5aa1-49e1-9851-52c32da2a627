# RawLatestBalanceBusinessCenterEntity

## Thông tin bảng dữ liệu

-   **Table**: `RawLatestBalanceBusinessCenters`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RawLatestBalanceBusinessCenters`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho ngân sách mới nhất của Business Center (Latest Balance Business Center). Kh<PERSON><PERSON> với `RawBalanceBusinessCenterEntity` lưu trữ lịch sử, entity này chỉ lưu trữ dữ liệu mới nhất và cập nhật khi có thay đổi.

**Lưu ý quan trọng về logic đồng bộ:**

-   Entity này chỉ lưu trữ dữ liệu mới nhất thay vì lưu trữ lịch sử
-   Khi có thay đổi dữ liệu từ API, bản ghi hiện tại sẽ được cập nhật thay vì tạo bản ghi mới
-   Trường `Date` lưu đầy đủ thời gian UTC khi dữ liệu được đồng bộ mới nhất
-   Logic: Thêm mới nếu chưa có, cập nhật nếu có thay đổi

## Bảng dữ liệu: RawLatestBalanceBusinessCenter

| Tên Field            | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                                                                      |
| -------------------- | ------------ | -------- | ------ | ------------------------------------------------------------------------------------------ |
| Id                   | Guid         | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                                                      |
| BcId                 | string       | ✅       | 100    | ID của Business Center (Unique Index)                                                      |
| BcName               | string       | ❌       | 512    | Tên của Business Center                                                                    |
| Currency             | string       | ✅       | 10     | Tiền tệ của Business Center                                                                |
| AccountBalance       | decimal?     | ❌       | -      | Tổng số dư của Business Center, làm tròn đến hai chữ số thập phân                          |
| ValidAccountBalance  | decimal?     | ❌       | -      | Số dư tài khoản hợp lệ của Business Center, làm tròn đến hai chữ số thập phân              |
| FrozenBalance        | decimal?     | ❌       | -      | Số dư bị đóng băng của Business Center, làm tròn đến hai chữ số thập phân                  |
| Tax                  | decimal?     | ❌       | -      | Thuế của Business Center, làm tròn đến hai chữ số thập phân                                |
| CashBalance          | decimal?     | ❌       | -      | Số dư tiền mặt của Business Center, làm tròn đến hai chữ số thập phân                      |
| ValidCashBalance     | decimal?     | ❌       | -      | Số dư tiền mặt hợp lệ của Business Center, làm tròn đến hai chữ số thập phân               |
| GrantBalance         | decimal?     | ❌       | -      | Số dư phiếu giảm giá/voucher của Business Center, làm tròn đến hai chữ số thập phân        |
| ValidGrantBalance    | decimal?     | ❌       | -      | Số dư phiếu giảm giá/voucher hợp lệ của Business Center, làm tròn đến hai chữ số thập phân |
| Date                 | DateTime?    | ❌       | -      | Thời gian đồng bộ dữ liệu mới nhất (UTC)                                                   |
| Timezone             | string       | ❌       | 50     | Múi giờ của ngân sách                                                                      |
| CreationTime         | DateTime     | ✅       | -      | Thời gian tạo bản ghi (Audit)                                                              |
| CreatorId            | Guid?        | ❌       | -      | ID người tạo (Audit)                                                                       |
| LastModificationTime | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                                             |
| LastModifierId       | Guid?        | ❌       | -      | ID người sửa đổi cuối (Audit)                                                              |

## Cấu trúc dữ liệu

### Các loại số dư

-   **AccountBalance**: Tổng số dư của Business Center
-   **ValidAccountBalance**: Số dư hợp lệ có thể sử dụng
-   **FrozenBalance**: Số dư bị đóng băng, không thể sử dụng
-   **CashBalance**: Số dư tiền mặt
-   **ValidCashBalance**: Số dư tiền mặt hợp lệ
-   **GrantBalance**: Số dư phiếu giảm giá/voucher
-   **ValidGrantBalance**: Số dư phiếu giảm giá/voucher hợp lệ

### Thông tin bổ sung

-   **Tax**: Thuế phải nộp
-   **Currency**: Đơn vị tiền tệ
-   **Timezone**: Múi giờ để tính toán thời gian
-   **Date**: Thời điểm cập nhật thông tin ngân sách mới nhất (UTC)

## Logic đồng bộ mới nhất (Latest)

### Cách thức hoạt động

1. **Kiểm tra bản ghi hiện tại**: Hệ thống sẽ tìm bản ghi theo `BcId`
2. **So sánh thay đổi**: So sánh dữ liệu từ API với bản ghi hiện tại
3. **Upsert**:
    - Nếu chưa có bản ghi: Tạo mới
    - Nếu có bản ghi và có thay đổi: Cập nhật bản ghi hiện tại
    - Nếu có bản ghi và không có thay đổi: Không làm gì
4. **Lưu thời gian UTC**: Thời gian cập nhật được chuyển đổi về UTC

### Các trường được so sánh để phát hiện thay đổi

-   BcName
-   AccountBalance, ValidAccountBalance, FrozenBalance, Tax
-   CashBalance, ValidCashBalance, GrantBalance, ValidGrantBalance
-   Currency

### Lợi ích của logic Latest

-   **Hiệu suất cao**: Chỉ lưu trữ dữ liệu mới nhất, giảm kích thước database
-   **Truy vấn nhanh**: Không cần tìm kiếm trong lịch sử, luôn lấy bản ghi duy nhất
-   **Dễ dàng tích hợp**: Phù hợp cho các API cần dữ liệu thời gian thực
-   **Tiết kiệm storage**: Không lưu trữ lịch sử thay đổi

## So sánh với RawBalanceBusinessCenterEntity

| Tiêu chí                | RawBalanceBusinessCenterEntity       | RawLatestBalanceBusinessCenterEntity |
| ----------------------- | ------------------------------------ | ------------------------------------ |
| **Lưu trữ dữ liệu**     | Lịch sử đầy đủ                       | Chỉ dữ liệu mới nhất                 |
| **Khi có thay đổi**     | Tạo bản ghi mới                      | Cập nhật bản ghi hiện tại            |
| **Kích thước database** | Lớn (tăng theo thời gian)            | Nhỏ (cố định)                        |
| **Truy vấn**            | Phức tạp (cần filter theo thời gian) | Đơn giản (lấy trực tiếp)             |
| **Mục đích sử dụng**    | Phân tích lịch sử, tracking thay đổi | API realtime, dashboard hiện tại     |

## Mục đích sử dụng

-   Cung cấp dữ liệu ngân sách mới nhất cho dashboard và API
-   Hỗ trợ các tính năng realtime cần dữ liệu cập nhật
-   Tối ưu hiệu suất cho các truy vấn thường xuyên
-   Giảm độ phức tạp trong việc lấy dữ liệu hiện tại
-   Phù hợp cho các tích hợp cần dữ liệu nhanh và đơn giản

## Lưu ý

-   Tất cả các trường số dư đều được làm tròn đến hai chữ số thập phân
-   Các trường số dư có thể null để đảm bảo tính linh hoạt khi chưa có dữ liệu
-   Entity này kế thừa từ AuditedEntity để có đầy đủ thông tin audit
-   Trường `BcId` nên có unique index để đảm bảo chỉ có một bản ghi cho mỗi BC
-   **Quan trọng**: Entity này bổ sung chứ không thay thế `RawBalanceBusinessCenterEntity`
