using System;
using System.ComponentModel.DataAnnotations;

namespace TikTok.ResourcePermissions
{
    /// <summary>
    /// DTO để thu hồi quyền cho nhiều người dùng đối với tài nguyên
    /// </summary>
    public class RemovePermissionForResourceDto
    {
        /// <summary>
        /// ID của người dùng
        /// </summary>
        [Required]
        public Guid UserId { get; set; }

        /// <summary>
        /// ID của tài nguyên
        /// </summary>
        [Required]
        [StringLength(255)]
        public string ResourceId { get; set; }

        /// <summary>
        /// Loại tài nguyên TikTok
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ResourceType { get; set; }
    }
}
