﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TikTok.AdAccounts
{
    public interface IAdAccountDataFilterService
    {
        Task SetCurrentSupporterAsync(Guid supporterId);
        Task SetMultipleSupportersAsync(List<Guid> supporterIds);
        List<Guid> GetCurrentSupporterIds();
        IDisposable DisableFilter();
        void ClearFilter();
    }
}
