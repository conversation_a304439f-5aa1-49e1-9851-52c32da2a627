/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Audience operations
    /// </summary>
    public interface IAudienceApi
    {
        /// <summary>
        /// Upload an audience file for creating custom audiences
        /// </summary>
        /// <param name="request">File upload request</param>
        /// <param name="fileStream">File stream to upload</param>
        /// <param name="fileName">Name of the file</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>File upload response containing file path</returns>
        //Task<AudienceFileUploadResponse> UploadAudienceFileAsync(
        //    AudienceFileUploadRequest request,
        //    Stream fileStream,
        //    string fileName,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a custom audience using uploaded files
        /// </summary>
        /// <param name="request">Audience creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Audience creation response</returns>
        //Task<AudienceCreateResponse> CreateAudienceAsync(
        //    AudienceCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a custom audience using rules
        /// </summary>
        /// <param name="request">Audience creation by rules request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Audience creation response</returns>
        //Task<AudienceRuleCreateResponse> CreateAudienceByRulesAsync(
        //    AudienceRuleCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get list of custom audiences
        /// </summary>
        /// <param name="request">Audience list request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Audience list response</returns>
        Task<AudienceListResponse> GetAudienceListAsync(
            AudienceListRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get detailed information about specific audiences
        /// </summary>
        /// <param name="request">Audience get request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Audience get response</returns>
        Task<AudienceGetResponse> GetAudienceAsync(
            AudienceGetRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Update an existing custom audience
        /// </summary>
        /// <param name="request">Audience update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Audience update response</returns>
        //Task<AudienceUpdateResponse> UpdateAudienceAsync(
        //    AudienceUpdateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete custom audiences
        /// </summary>
        /// <param name="request">Audience delete request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Audience delete response</returns>
        //Task<AudienceDeleteResponse> DeleteAudienceAsync(
        //    AudienceDeleteRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Share audiences with other advertiser accounts
        /// </summary>
        /// <param name="request">Audience share request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Audience share response</returns>
        //Task<AudienceShareResponse> ShareAudienceAsync(
        //    AudienceShareRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get sharing log of a custom audience
        /// </summary>
        /// <param name="request">Audience share log request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Audience share log response</returns>
        Task<AudienceShareLogResponse> GetShareLogAsync(
            AudienceShareLogRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Apply audiences to ad groups
        /// </summary>
        /// <param name="request">Audience apply request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Audience apply response</returns>
        //Task<AudienceApplyResponse> ApplyAudienceAsync(
        //    AudienceApplyRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a lookalike audience
        /// </summary>
        /// <param name="request">Lookalike audience creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Lookalike audience creation response</returns>
        //Task<AudienceLookalikeCreateResponse> CreateLookalikeAudienceAsync(
        //    AudienceLookalikeCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update a lookalike audience
        /// </summary>
        /// <param name="request">Lookalike audience update request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Lookalike audience update response</returns>
        //Task<AudienceLookalikeUpdateResponse> UpdateLookalikeAudienceAsync(
        //    AudienceLookalikeUpdateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Cancel audience sharing
        /// </summary>
        /// <param name="request">Audience share cancel request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Audience share cancel response</returns>
        //Task<AudienceShareCancelResponse> CancelShareAudienceAsync(
        //    AudienceShareCancelRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get apply log of a custom audience
        /// </summary>
        /// <param name="request">Audience apply log request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Audience apply log response</returns>
        Task<AudienceApplyLogResponse> GetApplyLogAsync(
            AudienceApplyLogRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get enhanced audience details with additional fields
        /// </summary>
        /// <param name="request">Audience get request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Enhanced audience get response</returns>
        Task<EnhancedAudienceGetResponse> GetEnhancedAudienceAsync(
            AudienceGetRequest request,
            CancellationToken cancellationToken = default);

        // ===== STREAMING/SEGMENT API METHODS =====

        /// <summary>
        /// Create or delete an audience segment
        /// </summary>
        /// <param name="request">Segment audience request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Segment audience response</returns>
        //Task<SegmentAudienceResponse> ManageSegmentAudienceAsync(
        //    SegmentAudienceRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Add or delete audience segment mappings
        /// </summary>
        /// <param name="request">Segment mapping request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Segment mapping response</returns>
        //Task<SegmentMappingResponse> ManageSegmentMappingAsync(
        //    SegmentMappingRequest request,
        //    CancellationToken cancellationToken = default);

        // ===== SAVED AUDIENCE API METHODS =====

        /// <summary>
        /// Create a saved audience
        /// </summary>
        /// <param name="request">Saved audience creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Saved audience creation response</returns>
        //Task<SavedAudienceCreateResponse> CreateSavedAudienceAsync(
        //    SavedAudienceCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete saved audiences
        /// </summary>
        /// <param name="request">Saved audience delete request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success response</returns>
        //Task<AudienceSuccessResponse> DeleteSavedAudienceAsync(
        //    SavedAudienceDeleteRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get list of saved audiences
        /// </summary>
        /// <param name="request">Saved audience list request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Saved audience list response</returns>
        Task<SavedAudienceListResponse> GetSavedAudienceListAsync(
            SavedAudienceListRequest request,
            CancellationToken cancellationToken = default);

        // ===== PANGLE AUDIENCE API METHODS =====

        /// <summary>
        /// Get Pangle audience package
        /// </summary>
        /// <param name="request">Pangle audience package request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Pangle audience package response</returns>
        Task<PangleAudiencePackageResponse> GetPangleAudiencePackageAsync(
            PangleAudiencePackageRequest request,
            CancellationToken cancellationToken = default);
    }
}
