# <PERSON><PERSON> thống Message Provider

## Tổng quan

Hệ thống Message Provider cho phép gửi tin nhắn thông báo đến nhiều nền tảng khác nhau như Telegram, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, v.v. <PERSON><PERSON> thống được thiết kế theo kiến trúc plugin, d<PERSON> dàng mở rộng thêm các provider mới.

## Kiến trúc

### 1. Interfaces
- `IMessageProvider`: Interface cơ bản cho tất cả message providers
- `IMessageService`: Service chính để quản lý việc gửi tin nhắn

### 2. Models
- `MessageProviderOptions`: <PERSON><PERSON><PERSON> hình chung cho tất cả providers
- `TelegramOptions`: Cấu hình riêng cho Telegram
- `SlackOptions`: Cấu hình riêng cho Slack
- `ZaloOptions`: <PERSON><PERSON>u hình riêng cho Zalo

### 3. Implementations
- `TelegramMessageProvider`: <PERSON><PERSON><PERSON> tin nhắn qua Telegram Bot API
- `SlackMessageProvider`: <PERSON><PERSON><PERSON> tin nhắn qua Slack Webhook
- `MessageService`: Service chính quản lý tất cả providers

## Cấu hình

### appsettings.json
```json
{
  "MessageProviderOption": {
    "ApplicationName": "Module VBDH",
    "Slack": {
      "Enabled": true,
      "WebhookUrl": "https://hooks.slack.com/services/..."
    },
    "Telegram": {
      "Enabled": true,
      "BotToken": "YOUR_BOT_TOKEN",
      "ChatId": "YOUR_CHAT_ID"
    },
    "Zalo": {
      "Enabled": false,
      "AccessToken": "YOUR_ACCESS_TOKEN",
      "PhoneNumber": "YOUR_PHONE_NUMBER"
    }
  }
}
```

## Sử dụng

### 1. Dependency Injection
```csharp
public class MyService
{
    private readonly IMessageService _messageService;

    public MyService(IMessageService messageService)
    {
        _messageService = messageService;
    }

    public async Task SendAlertAsync(string message)
    {
        await _messageService.SendMessageAsync(message);
    }
}
```

### 2. Gửi tin nhắn đến provider cụ thể
```csharp
// Gửi đến Telegram
await _messageService.SendMessageAsync("Alert message", "Telegram");

// Gửi đến Slack
await _messageService.SendMessageAsync("Alert message", "Slack");
```

### 3. Gửi tin nhắn đến tất cả providers
```csharp
await _messageService.SendMessageToAllAsync("Alert message");
```

### 4. API Endpoints
```http
POST /api/message/send
Content-Type: application/json

{
  "message": "Test message",
  "provider": "Telegram"
}
```

```http
POST /api/message/send-to-all
Content-Type: application/json

{
  "message": "Test message to all providers"
}
```

## Tích hợp với Background Workers

Hệ thống đã được tích hợp với `JobHealthCheckWorker` để tự động gửi thông báo khi:

1. **Worker Timeout**: Khi có worker bị timeout
2. **Stuck Jobs**: Khi có job bị treo quá lâu
3. **Manager Job Alert**: Khi Manager Job không chạy trong thời gian dài
4. **Register Job Alert**: Khi Register Job không chạy trong thời gian dài

## Thêm Provider mới

### 1. Tạo Options class
```csharp
public class NewProviderOptions
{
    public bool Enabled { get; set; } = false;
    public string ApiKey { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
}
```

### 2. Thêm vào MessageProviderOptions
```csharp
public class MessageProviderOptions
{
    public string ApplicationName { get; set; } = "TikTok Application";
    public SlackOptions Slack { get; set; } = new();
    public TelegramOptions Telegram { get; set; } = new();
    public ZaloOptions Zalo { get; set; } = new();
    public NewProviderOptions NewProvider { get; set; } = new(); // Thêm dòng này
}
```

### 3. Tạo Provider implementation
```csharp
public class NewProviderMessageProvider : IMessageProvider
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<NewProviderMessageProvider> _logger;
    private readonly NewProviderOptions _options;

    public string Name => "NewProvider";
    public bool IsEnabled => _options.Enabled;

    public NewProviderMessageProvider(
        HttpClient httpClient,
        ILogger<NewProviderMessageProvider> logger,
        IOptions<MessageProviderOptions> options)
    {
        _httpClient = httpClient;
        _logger = logger;
        _options = options.Value.NewProvider;
    }

    public async Task SendMessageAsync(string message)
    {
        await SendMessageAsync(message, null);
    }

    public async Task SendMessageAsync(string message, object additionalData)
    {
        if (!_options.Enabled)
        {
            _logger.LogWarning("NewProvider is disabled");
            return;
        }

        // Implement logic gửi tin nhắn
        // ...
    }
}
```

### 4. Đăng ký trong Module
```csharp
// Trong TikTokApplicationModule.ConfigureServices
context.Services.AddTransient<IMessageProvider, NewProviderMessageProvider>();
```

## Lưu ý

1. **Error Handling**: Tất cả providers đều có error handling riêng
2. **Logging**: Mọi hoạt động đều được log
3. **Async/Await**: Tất cả operations đều là async
4. **Configuration**: Sử dụng Options pattern của .NET
5. **Dependency Injection**: Tuân thủ DI pattern của ABP Framework

## Testing

### Test từng provider
```csharp
[Fact]
public async Task Should_Send_Message_To_Telegram()
{
    // Arrange
    var messageService = GetRequiredService<IMessageService>();
    
    // Act
    await messageService.SendMessageAsync("Test message", "Telegram");
    
    // Assert
    // Kiểm tra tin nhắn đã được gửi
}
```

### Test tất cả providers
```csharp
[Fact]
public async Task Should_Send_Message_To_All_Providers()
{
    // Arrange
    var messageService = GetRequiredService<IMessageService>();
    
    // Act
    await messageService.SendMessageToAllAsync("Test message");
    
    // Assert
    // Kiểm tra tin nhắn đã được gửi đến tất cả providers
}
``` 