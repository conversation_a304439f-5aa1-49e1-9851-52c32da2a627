using System;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.ReportIntegratedAds
{
    /// <summary>
    /// DTO cho báo cáo tích hợp Ad
    /// </summary>
    public class ReportIntegratedAdDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID tài khoản nhà quảng cáo - Được hỗ trợ ở cấp độ Nhà quảng cáo, Chiến dịch, Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID chiến dịch - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CampaignId { get; set; }

        /// <summary>
        /// ID nhóm quảng cáo - Được hỗ trợ ở cấp độ Quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdGroupId { get; set; }

        /// <summary>
        /// ID quảng cáo - Được hỗ trợ ở cấp độ Quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdId { get; set; }

        /// <summary>
        /// Tên quảng cáo - Được hỗ trợ ở cấp độ Quảng cáo
        /// </summary>
        [Required]
        [StringLength(255)]
        public string AdName { get; set; }

        /// <summary>
        /// Tiêu đề quảng cáo - Được hỗ trợ ở cấp độ Quảng cáo
        /// </summary>
        [StringLength(500)]
        public string? AdText { get; set; }

        /// <summary>
        /// Lời kêu gọi hành động - Được hỗ trợ ở cấp độ Quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? CallToAction { get; set; }

        /// <summary>
        /// Định dạng - Được hỗ trợ ở cấp độ Quảng cáo
        /// </summary>
        [StringLength(50)]
        public string? ImageMode { get; set; }

        /// <summary>
        /// Quảng cáo tự động - Liệu quảng cáo có phải là quảng cáo tự động hay quảng cáo Smart Creative không
        /// </summary>
        public bool IsAco { get; set; }

        /// <summary>
        /// Smart Creative - Liệu quảng cáo có phải là quảng cáo Smart Creative không
        /// </summary>
        public bool IsSmartCreative { get; set; }

        /// <summary>
        /// Ngày tổng hợp báo cáo
        /// </summary>
        [Required]
        public DateTime Date { get; set; }

        /// <summary>
        /// Chi tiêu - Tổng chi phí quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal Spend { get; set; }

        /// <summary>
        /// Lần hiển thị - Số lần quảng cáo của bạn được hiển thị
        /// </summary>
        [Required]
        public long Impressions { get; set; }

        /// <summary>
        /// Lần nhấp (đích đến) - Số lần nhấp từ quảng cáo của bạn đến một đích đến được chỉ định
        /// </summary>
        [Required]
        public long Clicks { get; set; }

        /// <summary>
        /// CTR (đích đến) - Tỷ lệ phần trăm lần hiển thị dẫn đến một lần nhấp đích đến trên tổng số lần hiển thị
        /// </summary>
        [Required]
        public decimal Ctr { get; set; }

        /// <summary>
        /// CPM - Số tiền trung bình bạn chi cho 1.000 lần hiển thị
        /// </summary>
        [Required]
        public decimal Cpm { get; set; }

        /// <summary>
        /// CPC (đích đến) - Chi phí trung bình cho mỗi lần nhấp đến một đích đến được chỉ định
        /// </summary>
        [Required]
        public decimal Cpc { get; set; }

        /// <summary>
        /// Chuyển đổi - Số lần quảng cáo của bạn dẫn đến sự kiện tối ưu hóa mà bạn đã chọn
        /// </summary>
        [Required]
        public long Conversion { get; set; }

        /// <summary>
        /// Chi phí mỗi chuyển đổi - Số tiền trung bình chi cho một chuyển đổi
        /// </summary>
        [Required]
        public decimal CostPerConversion { get; set; }

        /// <summary>
        /// Tiếp cận - Số lượng người dùng duy nhất đã xem quảng cáo của bạn ít nhất một lần
        /// </summary>
        [Required]
        public long Reach { get; set; }

        /// <summary>
        /// Tần suất - Số lần trung bình mỗi người dùng xem quảng cáo của bạn trong một khoảng thời gian nhất định
        /// </summary>
        [Required]
        public decimal Frequency { get; set; }

        /// <summary>
        /// Lượt xem video - Số lần video của bạn bắt đầu phát
        /// </summary>
        [Required]
        public long VideoPlayActions { get; set; }

        /// <summary>
        /// Lượt xem video 2 giây - Số lần video của bạn được phát ít nhất 2 giây
        /// </summary>
        [Required]
        public long VideoWatched2s { get; set; }

        /// <summary>
        /// Lượt xem video 6 giây - Số lần video của bạn được phát ít nhất 6 giây
        /// </summary>
        [Required]
        public long VideoWatched6s { get; set; }

        /// <summary>
        /// Lượt xem video đạt 25% - Số lần video của bạn được phát ít nhất 25% độ dài của nó
        /// </summary>
        [Required]
        public long VideoViewsP25 { get; set; }

        /// <summary>
        /// Lượt xem video đạt 50% - Số lần video của bạn được phát ít nhất 50% độ dài của nó
        /// </summary>
        [Required]
        public long VideoViewsP50 { get; set; }

        /// <summary>
        /// Lượt xem video đạt 75% - Số lần video của bạn được phát ít nhất 75% độ dài của nó
        /// </summary>
        [Required]
        public long VideoViewsP75 { get; set; }

        /// <summary>
        /// Lượt xem video đạt 100% - Số lần video của bạn được phát 100% độ dài của nó
        /// </summary>
        [Required]
        public long VideoViewsP100 { get; set; }

        /// <summary>
        /// Thời gian phát trung bình mỗi lượt xem video - Thời gian trung bình video của bạn được phát cho mỗi lượt xem video
        /// </summary>
        [Required]
        public decimal AverageVideoPlay { get; set; }

        /// <summary>
        /// Lượt xem tập trung 6 giây - Số lần video của bạn được phát ít nhất 6 giây, được phát đầy đủ nếu dưới 6 giây, hoặc nhận được ít nhất 1 tương tác trong 6 giây đầu tiên
        /// </summary>
        [Required]
        public long EngagedView { get; set; }

        /// <summary>
        /// ROAS (Shop) - Lợi tức trên chi phí quảng cáo (ROAS) từ tổng doanh thu TikTok Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal OnsiteShoppingRoas { get; set; }

        /// <summary>
        /// Tổng doanh thu (Shop) - Tổng doanh thu của các đơn hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal TotalOnsiteShoppingValue { get; set; }

        /// <summary>
        /// Mua hàng (Shop) - Số lượng hành động gửi đơn hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public long OnsiteShopping { get; set; }

        /// <summary>
        /// Chi phí mỗi lần mua hàng (Shop) - Chi phí trung bình cho mỗi lần mua hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal CostPerOnsiteShopping { get; set; }

        /// <summary>
        /// Giá trị đơn hàng trung bình (Shop) - Giá trị đơn hàng trung bình cho mỗi lần mua hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal ValuePerOnsiteShopping { get; set; }

        /// <summary>
        /// Lượt xem trang sản phẩm (Shop) - Số lượng lượt xem trang chi tiết sản phẩm Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public long OnsiteOnWebDetail { get; set; }

        /// <summary>
        /// Thêm vào giỏ hàng (Shop) - Số lượng hành động thêm vào giỏ hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public long OnsiteOnWebCart { get; set; }

        /// <summary>
        /// Bắt đầu thanh toán (Shop) - Số lượng hành động bắt đầu thanh toán trong Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public long OnsiteInitiateCheckoutCount { get; set; }

        /// <summary>
        /// Lượt xem LIVE - Số lần LIVE của bạn được xem từ một quảng cáo
        /// </summary>
        [Required]
        public long LiveViews { get; set; }

        /// <summary>
        /// Lượt xem LIVE duy nhất - Số người dùng duy nhất đã xem LIVE của bạn ít nhất một lần từ một quảng cáo
        /// </summary>
        [Required]
        public long LiveUniqueViews { get; set; }

        /// <summary>
        /// Lượt xem LIVE 10 giây - Số lần LIVE của bạn được xem ít nhất 10 giây từ một quảng cáo
        /// </summary>
        [Required]
        public long LiveEffectiveViews { get; set; }

        /// <summary>
        /// Nhấp chuột Sản phẩm LIVE - Số lần người xem nhấp chuột vào một sản phẩm và xem trang chi tiết của nó trong quá trình LIVE của bạn
        /// </summary>
        [Required]
        public long LiveProductClicks { get; set; }

        /// <summary>
        /// Tiền tệ - Mã tiền tệ, ví dụ: USD
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Currency { get; set; }
    }
} 