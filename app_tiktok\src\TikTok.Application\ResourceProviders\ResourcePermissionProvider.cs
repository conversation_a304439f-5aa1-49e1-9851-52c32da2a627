﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.ResourcePermissions;
using Volo.Abp.DependencyInjection;

namespace TikTok.ResourceProviders
{
    public abstract class ResourcePermissionProvider : IResourcePermissionProvider
    {
        public string ResourceType { get; }
        protected IAbpLazyServiceProvider _lazyServiceProvider;

        private readonly IResourcePermissionAppService _service;

        public ResourcePermissionProvider(string resourceType, IAbpLazyServiceProvider lazyServiceProvider)
        {
            ResourceType = resourceType;
            _service = lazyServiceProvider.LazyGetRequiredService<IResourcePermissionAppService>();
        }

        public Task<bool> AssignPermission(List<AssignPermissionForResourceDto> input)
        {
            if (input == null || !input.Any())
            {
                throw new ArgumentException("Input cannot be null or empty", nameof(input));
            }
            List<ResourcePermissions.AssignPermissionForResourceDto> validInputs = input.Select(x => new ResourcePermissions.AssignPermissionForResourceDto()
            {
                ResourceId = x.ResourceId,
                ResourceType = ResourceType,
                UserId = x.UserId,
                Permissions = x.Permissions
            }).ToList();

            return _service.AssignPermissionsAsync(validInputs);
        }

        public async Task<List<AssignPermissionForResourceDto>> GetListAsync(GetResourcePermissionAssignedUserDto input)
        {
            if (input == null)
            {
                throw new ArgumentNullException(nameof(input), "Input cannot be null");
            }
            var response = await _service.GetAllAsync(new GetResourcePermissionListDto()
            {
                UserId = input.UserId,
                ResourceType = ResourceType,
                ResourceId = input.ResourceId,
                Permissions = input.Permissions,
                UserIds = input.UserIds,
                ResourceIds = input.ResourceIds
            });

            var groupedPermissions = response.GroupBy(x => new { x.UserId, x.ResourceId, x.ResourceType })
                .Select(g => new AssignPermissionForResourceDto
                {
                    UserId = g.Key.UserId,
                    ResourceId = g.Key.ResourceId,
                    Permissions = g.ToList().Select(p => p.Permission).Distinct().ToList()
                }).ToList();

            return groupedPermissions;
        }

        public Task<bool> RemovePermission(List<AssignPermissionForResourceDto> input)
        {
            if (input == null || !input.Any())
            {
                throw new ArgumentException("Input cannot be null or empty", nameof(input));
            }
            List<RemovePermissionForResourceDto> validInputs = input.Select(x => new RemovePermissionForResourceDto()
            {
                ResourceId = x.ResourceId,
                ResourceType = ResourceType,
                UserId = x.UserId,
            }).ToList();
            return _service.RevokePermissionsAsync(validInputs);
        }
    }
}