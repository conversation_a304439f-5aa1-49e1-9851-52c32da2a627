/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace TikTokBusinessApi.Utilities
{
    /// <summary>
    /// Utility class for validating TikTok webhook signatures and events
    /// </summary>
    public class WebhookValidator
    {
        private readonly ILogger<WebhookValidator>? _logger;

        /// <summary>
        /// Initializes a new instance of the WebhookValidator class
        /// </summary>
        /// <param name="logger">Optional logger instance</param>
        public WebhookValidator(ILogger<WebhookValidator>? logger = null)
        {
            _logger = logger;
        }

        /// <summary>
        /// Validates a TikTok webhook signature using HMAC-SHA256
        /// </summary>
        /// <param name="payload">The raw webhook payload as received</param>
        /// <param name="signature">The signature from the X-TikTok-Signature header</param>
        /// <param name="secret">Your webhook secret</param>
        /// <param name="timestamp">The timestamp from the X-TikTok-Timestamp header</param>
        /// <param name="toleranceSeconds">Tolerance for timestamp validation in seconds (default: 300)</param>
        /// <returns>True if the signature is valid, false otherwise</returns>
        public bool ValidateSignature(
            string payload, 
            string signature, 
            string secret, 
            string timestamp, 
            int toleranceSeconds = 300)
        {
            if (string.IsNullOrEmpty(payload))
            {
                _logger?.LogWarning("Webhook validation failed: payload is null or empty");
                return false;
            }

            if (string.IsNullOrEmpty(signature))
            {
                _logger?.LogWarning("Webhook validation failed: signature is null or empty");
                return false;
            }

            if (string.IsNullOrEmpty(secret))
            {
                _logger?.LogWarning("Webhook validation failed: secret is null or empty");
                return false;
            }

            if (string.IsNullOrEmpty(timestamp))
            {
                _logger?.LogWarning("Webhook validation failed: timestamp is null or empty");
                return false;
            }

            try
            {
                // Validate timestamp to prevent replay attacks
                if (!ValidateTimestamp(timestamp, toleranceSeconds))
                {
                    _logger?.LogWarning("Webhook validation failed: timestamp validation failed");
                    return false;
                }

                // Compute expected signature
                var expectedSignature = ComputeSignature(payload, secret, timestamp);

                // Compare signatures using constant-time comparison
                var isValid = ConstantTimeEquals(signature, expectedSignature);

                if (isValid)
                {
                    _logger?.LogDebug("Webhook signature validation successful");
                }
                else
                {
                    _logger?.LogWarning("Webhook validation failed: signature mismatch");
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error validating webhook signature");
                return false;
            }
        }

        /// <summary>
        /// Validates a TikTok webhook signature using byte arrays
        /// </summary>
        /// <param name="payloadBytes">The raw webhook payload bytes</param>
        /// <param name="signature">The signature from the X-TikTok-Signature header</param>
        /// <param name="secret">Your webhook secret</param>
        /// <param name="timestamp">The timestamp from the X-TikTok-Timestamp header</param>
        /// <param name="toleranceSeconds">Tolerance for timestamp validation in seconds (default: 300)</param>
        /// <returns>True if the signature is valid, false otherwise</returns>
        public bool ValidateSignature(
            byte[] payloadBytes, 
            string signature, 
            string secret, 
            string timestamp, 
            int toleranceSeconds = 300)
        {
            if (payloadBytes == null || payloadBytes.Length == 0)
            {
                _logger?.LogWarning("Webhook validation failed: payload bytes are null or empty");
                return false;
            }

            var payload = Encoding.UTF8.GetString(payloadBytes);
            return ValidateSignature(payload, signature, secret, timestamp, toleranceSeconds);
        }

        /// <summary>
        /// Computes the expected HMAC-SHA256 signature for a webhook payload
        /// </summary>
        /// <param name="payload">The webhook payload</param>
        /// <param name="secret">Your webhook secret</param>
        /// <param name="timestamp">The timestamp</param>
        /// <returns>The computed signature</returns>
        public string ComputeSignature(string payload, string secret, string timestamp)
        {
            // TikTok webhook signature format: HMAC-SHA256(timestamp + payload, secret)
            var message = timestamp + payload;
            var keyBytes = Encoding.UTF8.GetBytes(secret);
            var messageBytes = Encoding.UTF8.GetBytes(message);

            using var hmac = new HMACSHA256(keyBytes);
            var hashBytes = hmac.ComputeHash(messageBytes);
            return Convert.ToHexString(hashBytes).ToLowerInvariant();
        }

        /// <summary>
        /// Validates that the timestamp is within the acceptable tolerance
        /// </summary>
        /// <param name="timestamp">The timestamp string</param>
        /// <param name="toleranceSeconds">Tolerance in seconds</param>
        /// <returns>True if timestamp is valid, false otherwise</returns>
        public bool ValidateTimestamp(string timestamp, int toleranceSeconds = 300)
        {
            if (!long.TryParse(timestamp, out var timestampValue))
            {
                _logger?.LogWarning("Invalid timestamp format: {Timestamp}", timestamp);
                return false;
            }

            var webhookTime = DateTimeOffset.FromUnixTimeSeconds(timestampValue);
            var currentTime = DateTimeOffset.UtcNow;
            var timeDifference = Math.Abs((currentTime - webhookTime).TotalSeconds);

            if (timeDifference > toleranceSeconds)
            {
                _logger?.LogWarning("Timestamp outside tolerance: {TimeDifference}s > {Tolerance}s", 
                    timeDifference, toleranceSeconds);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Parses and validates a webhook event payload
        /// </summary>
        /// <typeparam name="T">The expected event type</typeparam>
        /// <param name="payload">The webhook payload JSON</param>
        /// <returns>The parsed event object</returns>
        /// <exception cref="JsonException">Thrown when the payload cannot be parsed</exception>
        public T ParseWebhookEvent<T>(string payload) where T : class
        {
            if (string.IsNullOrEmpty(payload))
            {
                throw new ArgumentException("Payload cannot be null or empty", nameof(payload));
            }

            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
                    PropertyNameCaseInsensitive = true
                };

                var eventData = JsonSerializer.Deserialize<T>(payload, options);
                if (eventData == null)
                {
                    throw new JsonException("Failed to deserialize webhook payload");
                }

                _logger?.LogDebug("Successfully parsed webhook event of type {EventType}", typeof(T).Name);
                return eventData;
            }
            catch (JsonException ex)
            {
                _logger?.LogError(ex, "Failed to parse webhook event payload");
                throw;
            }
        }

        /// <summary>
        /// Performs constant-time string comparison to prevent timing attacks
        /// </summary>
        /// <param name="a">First string</param>
        /// <param name="b">Second string</param>
        /// <returns>True if strings are equal, false otherwise</returns>
        private static bool ConstantTimeEquals(string a, string b)
        {
            if (a.Length != b.Length)
            {
                return false;
            }

            var result = 0;
            for (var i = 0; i < a.Length; i++)
            {
                result |= a[i] ^ b[i];
            }

            return result == 0;
        }
    }
}
