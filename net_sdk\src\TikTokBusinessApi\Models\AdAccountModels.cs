/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request parameters for getting authorized ad accounts
    /// </summary>
    public class GetAuthorizedAccountsRequest
    {
        /// <summary>
        /// The App id applied by the developer
        /// </summary>
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// The private key of the developer's application
        /// </summary>
        public string Secret { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the GetAuthorizedAccountsRequest class
        /// </summary>
        public GetAuthorizedAccountsRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the GetAuthorizedAccountsRequest class with parameters
        /// </summary>
        /// <param name="appId">App ID</param>
        /// <param name="secret">App secret</param>
        public GetAuthorizedAccountsRequest(string appId, string secret)
        {
            AppId = appId ?? string.Empty;
            Secret = secret ?? string.Empty;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"GetAuthorizedAccountsRequest(AppId={AppId})";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not GetAuthorizedAccountsRequest other)
                return false;

            return AppId == other.AppId && Secret == other.Secret;
        }

        /// <summary>
        /// Serves as the default hash function
        /// </summary>
        /// <returns>A hash code for the current object</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AppId, Secret);
        }
    }

    /// <summary>
    /// Request parameters for getting ad account details
    /// </summary>
    public class GetAdAccountDetailsRequest
    {
        /// <summary>
        /// List of advertiser IDs to query
        /// </summary>
        public List<string> AdvertiserIds { get; set; } = new();

        /// <summary>
        /// A list of information to be returned
        /// </summary>
        public List<string>? Fields { get; set; }

        /// <summary>
        /// Initializes a new instance of the GetAdAccountDetailsRequest class
        /// </summary>
        public GetAdAccountDetailsRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the GetAdAccountDetailsRequest class with parameters
        /// </summary>
        /// <param name="advertiserIds">List of advertiser IDs</param>
        /// <param name="fields">List of fields to return</param>
        public GetAdAccountDetailsRequest(List<string> advertiserIds, List<string>? fields = null)
        {
            AdvertiserIds = advertiserIds ?? new List<string>();
            Fields = fields;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"GetAdAccountDetailsRequest(AdvertiserIds={AdvertiserIds.Count} accounts, Fields={Fields?.Count ?? 0} fields)";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not GetAdAccountDetailsRequest other)
                return false;

            return AdvertiserIds.SequenceEqual(other.AdvertiserIds) &&
                   (Fields?.SequenceEqual(other.Fields ?? new List<string>()) ?? other.Fields == null);
        }

        /// <summary>
        /// Serves as the default hash function
        /// </summary>
        /// <returns>A hash code for the current object</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AdvertiserIds, Fields);
        }
    }
    /// <summary>
    /// Response data for getting authorized ad accounts
    /// </summary>
    public class AuthorizedAccountsResponse
    {
        /// <summary>
        /// List of authorized advertiser accounts
        /// </summary>
        [JsonPropertyName("list")]
        public List<AdvertiserInfo>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the AuthorizedAccountsResponse class
        /// </summary>
        public AuthorizedAccountsResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AuthorizedAccountsResponse class with parameters
        /// </summary>
        /// <param name="list">List of advertiser accounts</param>
        public AuthorizedAccountsResponse(List<AdvertiserInfo>? list = null)
        {
            List = list;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AuthorizedAccountsResponse(List={List?.Count} accounts)";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not AuthorizedAccountsResponse other)
                return false;

            return Equals(List, other.List);
        }

        /// <summary>
        /// Serves as the default hash function
        /// </summary>
        /// <returns>A hash code for the current object</returns>
        public override int GetHashCode()
        {
            return List?.GetHashCode() ?? 0;
        }
    }

    /// <summary>
    /// Basic advertiser account information
    /// </summary>
    public class AdvertiserInfo
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Advertiser name
        /// </summary>
        [JsonPropertyName("advertiser_name")]
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdvertiserInfo class
        /// </summary>
        public AdvertiserInfo()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdvertiserInfo class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="advertiserName">Advertiser name</param>
        public AdvertiserInfo(string? advertiserId = null, string? advertiserName = null)
        {
            AdvertiserId = advertiserId;
            AdvertiserName = advertiserName;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdvertiserInfo(AdvertiserId={AdvertiserId}, AdvertiserName={AdvertiserName})";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not AdvertiserInfo other)
                return false;

            return AdvertiserId == other.AdvertiserId &&
                   AdvertiserName == other.AdvertiserName;
        }

        /// <summary>
        /// Serves as the default hash function
        /// </summary>
        /// <returns>A hash code for the current object</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AdvertiserId, AdvertiserName);
        }
    }

    /// <summary>
    /// Response data for getting ad account details
    /// </summary>
    public class AdAccountDetailsResponse
    {
        /// <summary>
        /// List of ad account details
        /// </summary>
        [JsonPropertyName("list")]
        public List<AdAccountDetail>? List { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdAccountDetailsResponse class
        /// </summary>
        public AdAccountDetailsResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdAccountDetailsResponse class with parameters
        /// </summary>
        /// <param name="list">List of ad account details</param>
        public AdAccountDetailsResponse(List<AdAccountDetail>? list = null)
        {
            List = list;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdAccountDetailsResponse(List={List?.Count} accounts)";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not AdAccountDetailsResponse other)
                return false;

            return Equals(List, other.List);
        }

        /// <summary>
        /// Serves as the default hash function
        /// </summary>
        /// <returns>A hash code for the current object</returns>
        public override int GetHashCode()
        {
            return List?.GetHashCode() ?? 0;
        }
    }

    /// <summary>
    /// Detailed ad account information
    /// </summary>
    public class AdAccountDetail
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// The ID of a Business Center that the ad account belongs to
        /// </summary>
        [JsonPropertyName("owner_bc_id")]
        public string? OwnerBcId { get; set; }

        /// <summary>
        /// Ad account status
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Ad account role
        /// </summary>
        [JsonPropertyName("role")]
        public string? Role { get; set; }

        /// <summary>
        /// Reason for rejection
        /// </summary>
        [JsonPropertyName("rejection_reason")]
        public string? RejectionReason { get; set; }

        /// <summary>
        /// Ad account name
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Ad account time zone
        /// </summary>
        [JsonPropertyName("timezone")]
        public string? Timezone { get; set; }

        /// <summary>
        /// Time zone name in the format of "Region/City"
        /// </summary>
        [JsonPropertyName("display_timezone")]
        public string? DisplayTimezone { get; set; }

        /// <summary>
        /// Ad account's company name
        /// </summary>
        [JsonPropertyName("company")]
        public string? Company { get; set; }

        /// <summary>
        /// Whether the company name of the ad account can be updated via API
        /// </summary>
        [JsonPropertyName("company_name_editable")]
        public bool? CompanyNameEditable { get; set; }

        /// <summary>
        /// Ad account industry category code
        /// </summary>
        [JsonPropertyName("industry")]
        public string? Industry { get; set; }

        /// <summary>
        /// Ad account address
        /// </summary>
        [JsonPropertyName("address")]
        public string? Address { get; set; }

        /// <summary>
        /// The place of registration code of the ad account
        /// </summary>
        [JsonPropertyName("country")]
        public string? Country { get; set; }

        /// <summary>
        /// Type of the ad account
        /// </summary>
        [JsonPropertyName("advertiser_account_type")]
        public string? AdvertiserAccountType { get; set; }

        /// <summary>
        /// Type of currency used by the ad account
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// Contact name, in masked format
        /// </summary>
        [JsonPropertyName("contacter")]
        public string? Contacter { get; set; }

        /// <summary>
        /// Ad account contact email, in masked format
        /// </summary>
        [JsonPropertyName("email")]
        public string? Email { get; set; }

        /// <summary>
        /// Contact mobile number, in masked format
        /// </summary>
        [JsonPropertyName("cellphone_number")]
        public string? CellphoneNumber { get; set; }

        /// <summary>
        /// Fixed phone number, in masked format
        /// </summary>
        [JsonPropertyName("telephone_number")]
        public string? TelephoneNumber { get; set; }

        /// <summary>
        /// The code of the language used by the ad account
        /// </summary>
        [JsonPropertyName("language")]
        public string? Language { get; set; }

        /// <summary>
        /// Business license number
        /// </summary>
        [JsonPropertyName("license_no")]
        public string? LicenseNo { get; set; }

        /// <summary>
        /// Business license preview URL
        /// </summary>
        [JsonPropertyName("license_url")]
        public string? LicenseUrl { get; set; }

        /// <summary>
        /// Brand description
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Ad account available balance
        /// </summary>
        [JsonPropertyName("balance")]
        public decimal? Balance { get; set; }

        /// <summary>
        /// The time when the ad account was created, in the format of an Epoch/Unix timestamp in seconds
        /// </summary>
        [JsonPropertyName("create_time")]
        public long? CreateTime { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdAccountDetail class
        /// </summary>
        public AdAccountDetail()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdAccountDetail(AdvertiserId={AdvertiserId}, Name={Name}, Status={Status}, Currency={Currency})";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not AdAccountDetail other)
                return false;

            return AdvertiserId == other.AdvertiserId &&
                   Name == other.Name &&
                   Status == other.Status &&
                   Currency == other.Currency &&
                   Company == other.Company;
        }

        /// <summary>
        /// Serves as the default hash function
        /// </summary>
        /// <returns>A hash code for the current object</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AdvertiserId, Name, Status, Currency, Company);
        }
    }
}
