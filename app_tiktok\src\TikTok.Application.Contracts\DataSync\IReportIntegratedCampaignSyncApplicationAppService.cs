using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service interface cho việc đồng bộ dữ liệu báo cáo tích hợp Campaign
    /// </summary>
    public interface IReportIntegratedCampaignSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ báo cáo tích hợp Campaign theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu (tùy chọn)</param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kế<PERSON> thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<ReportIntegratedCampaignSyncResult> SyncReportIntegratedCampaignAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// Đồng bộ báo cáo tích hợp Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<ReportIntegratedCampaignSyncResult> SyncAllReportIntegratedCampaignForAllBcsAsync();
    }
}