# NotificationService - Hướng dẫn sử dụng

## Tổng quan

`NotificationService` là một service tổng quát để gửi thông báo thông qua Elsa workflow. Service này cho phép tái sử dụng code và không phụ thuộc vào một nền tảng thông báo cụ thể nào (Zalo, Telegram, etc.).

## Tính năng

- ✅ Gửi thông báo thông qua workflow tùy chỉnh
- ✅ Gửi thông báo với workflow mặc định
- ✅ Gửi thông báo với thông tin cơ bản
- ✅ Hỗ trợ attachments
- ✅ Logging và error handling
- ✅ Dependency injection ready

## Cách sử dụng

### 1. Inject service

```csharp
public class YourService
{
    private readonly INotificationService _notificationService;

    public YourService(INotificationService notificationService)
    {
        _notificationService = notificationService;
    }
}
```

### 2. <PERSON><PERSON><PERSON> thông báo với workflow mặc định

```csharp
var messageInput = new BaseMessageActivityInput()
{
    AdAccountId = "*********",
    BcId = "bc_001",
    TextContent = "Nội dung thông báo",
    Name = "Hệ thống",
    Phonenumber = "***********"
};

await _notificationService.SendNotificationAsync(messageInput);
```

### 3. Gửi thông báo với workflow cụ thể

```csharp
var messageInput = new BaseMessageActivityInput()
{
    AdAccountId = "*********",
    BcId = "bc_001",
    TextContent = "Thông báo Zalo",
    Name = "Hệ thống Zalo",
    OwnerId = "701495915639571502",
    Phonenumber = "***********"
};

// Gửi qua workflow Zalo
await _notificationService.SendNotificationAsync(
    WorkflowConst.RequireSendMessageZaloWorkflow, 
    messageInput);
```

### 4. Gửi thông báo đơn giản

```csharp
await _notificationService.SendNotificationAsync(
    adAccountId: "*********",
    bcId: "bc_001",
    textContent: "Tài khoản đã được cập nhật",
    phoneNumber: "***********",
    name: "Hệ thống TikTok"
);
```

### 5. Gửi thông báo với attachments

```csharp
var messageInput = new BaseMessageActivityInput()
{
    AdAccountId = "*********",
    BcId = "bc_001",
    TextContent = "Báo cáo đã được tạo",
    Name = "Hệ thống Báo cáo",
    Attachments = new List<string>
    {
        "https://example.com/report1.pdf",
        "https://example.com/report2.xlsx"
    }
};

await _notificationService.SendNotificationAsync(messageInput);
```

## Cấu trúc BaseMessageActivityInput

```csharp
public class BaseMessageActivityInput
{
    public string BcId { get; set; }                    // ID Business Center
    public string AdAccountId { get; set; }             // ID tài khoản quảng cáo
    public string? Phonenumber { get; set; }            // Số điện thoại
    public string? Name { get; set; }                   // Tên người gửi
    public string? TextContent { get; set; }            // Nội dung thông báo
    public List<string>? Attachments { get; set; }      // Danh sách file đính kèm
    public string? OwnerId { get; set; }                // Zalo Owner ID
}
```

## Workflow Constants

```csharp
public static class WorkflowConst
{
    public const string RequireSendMessageWorkflow = "RequireSendMessage";           // Workflow mặc định
    public const string RequireSendMessageZaloWorkflow = "RequireSendZaloMessage";   // Workflow Zalo
}
```

## Error Handling

Service sẽ tự động xử lý các lỗi và log thông tin:

- Nếu workflow không tồn tại: Log warning và trả về null
- Nếu có lỗi khi thực thi workflow: Log error và throw exception

## Ví dụ thực tế

Xem file `NotificationServiceExample.cs` để có các ví dụ chi tiết về cách sử dụng service này.

## Lưu ý

- Service này không quyết định việc gửi thông báo qua Zalo hay Telegram, điều đó được xử lý trong workflow
- Đảm bảo workflow đã được định nghĩa và published trước khi sử dụng
- Service đã được đăng ký trong DI container, có thể inject trực tiếp 