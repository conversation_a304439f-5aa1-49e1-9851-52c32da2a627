/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the BC Partners API
    /// </summary>
    public class BcPartnersApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public BcPartnersApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example of getting partners of a Business Center
        /// </summary>
        public async Task GetPartnersExample()
        {
            try
            {
                Console.WriteLine("Getting partners...");

                var getRequest = new GetPartnersRequest
                {
                    BcId = "your_bc_id_here",
                    Filtering = new GetPartnersFiltering
                    {
                        Name = "partner_name_keyword" // Optional: filter by partner name
                    },
                    Page = 1,
                    PageSize = 10
                };

                var response = await _client.BcPartners.GetPartnersAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    getRequest);

                if (response.Data?.List != null)
                {
                    Console.WriteLine($"Found {response.Data.List.Count} partners:");
                    foreach (var partner in response.Data.List)
                    {
                        Console.WriteLine($"- Partner ID: {partner.BcId}, Name: {partner.BcName}");
                    }

                    if (response.Data.PageInfo != null)
                    {
                        Console.WriteLine($"Page {response.Data.PageInfo.Page} of {response.Data.PageInfo.TotalPage}");
                        Console.WriteLine($"Total partners: {response.Data.PageInfo.TotalNumber}");
                    }
                }
                else
                {
                    Console.WriteLine("No partners found.");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of adding a partner to a Business Center
        /// </summary>
        public async Task AddPartnerExample()
        {
            try
            {
                Console.WriteLine("Adding partner...");

                var addRequest = new AddPartnerRequest
                {
                    BcId = "your_bc_id_here",
                    PartnerId = "partner_bc_id_here",
                    AssetType = "ADVERTISER", // Currently only ADVERTISER is supported
                    AssetIds = new List<string> { "advertiser_id_1", "advertiser_id_2" }, // Optional
                    AdvertiserRole = "ANALYST" // ADMIN, OPERATOR, or ANALYST (default: ANALYST)
                };

                var response = await _client.BcPartners.AddPartnerAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    addRequest);

                Console.WriteLine("Partner added successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of deleting a partner from a Business Center
        /// </summary>
        public async Task DeletePartnerExample()
        {
            try
            {
                Console.WriteLine("Deleting partner...");

                var deleteRequest = new DeletePartnerRequest
                {
                    BcId = "your_bc_id_here",
                    PartnerId = "partner_bc_id_here"
                };

                var response = await _client.BcPartners.DeletePartnerAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    deleteRequest);

                Console.WriteLine("Partner deleted successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of canceling asset sharing with a partner
        /// </summary>
        public async Task CancelAssetSharingExample()
        {
            try
            {
                Console.WriteLine("Canceling asset sharing...");

                var cancelRequest = new CancelAssetSharingRequest
                {
                    BcId = "your_bc_id_here",
                    PartnerId = "partner_bc_id_here",
                    AssetIds = new List<string> { "asset_id_to_unshare" },
                    AssetType = "ADVERTISER" // ADVERTISER, CATALOG, TIKTOK_SHOP, STOREFRANT, or PIXEL
                };

                var response = await _client.BcPartners.CancelAssetSharingAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    cancelRequest);

                Console.WriteLine("Asset sharing canceled successfully!");
                Console.WriteLine($"Request ID: {response.RequestId}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting partner assets
        /// </summary>
        public async Task GetPartnerAssetsExample()
        {
            try
            {
                Console.WriteLine("Getting partner assets...");

                var getAssetsRequest = new GetPartnerAssetsRequest
                {
                    BcId = "your_bc_id_here",
                    PartnerId = "partner_bc_id_here",
                    AssetType = "ADVERTISER", // ADVERTISER, CATALOG, TIKTOK_SHOP, or PIXEL
                    ShareType = "SHARED", // SHARED (assets shared with you) or SHARING (assets you share)
                    Filtering = new PartnerAssetFiltering
                    {
                        Keyword = "search_keyword" // Optional: filter by asset name or ID
                    },
                    Page = 1,
                    PageSize = 10
                };

                var response = await _client.BcPartners.GetPartnerAssetsAsync(
                    _client.ApiClient.DefaultHeaders["Access-Token"],
                    getAssetsRequest);

                if (response.Data?.List != null)
                {
                    Console.WriteLine($"Found {response.Data.List.Count} partner assets:");
                    foreach (var asset in response.Data.List)
                    {
                        Console.WriteLine($"- Asset ID: {asset.AssetId}");
                        Console.WriteLine($"  Name: {asset.AssetName}");
                        Console.WriteLine($"  Type: {asset.AssetType}");
                        
                        if (!string.IsNullOrEmpty(asset.AdvertiserRole))
                            Console.WriteLine($"  Advertiser Role: {asset.AdvertiserRole}");
                        
                        if (!string.IsNullOrEmpty(asset.AdvertiserAccountType))
                            Console.WriteLine($"  Account Type: {asset.AdvertiserAccountType}");
                        
                        if (!string.IsNullOrEmpty(asset.CatalogRole))
                            Console.WriteLine($"  Catalog Role: {asset.CatalogRole}");
                        
                        Console.WriteLine();
                    }

                    if (response.Data.PageInfo != null)
                    {
                        Console.WriteLine($"Page {response.Data.PageInfo.Page} of {response.Data.PageInfo.TotalPage}");
                        Console.WriteLine($"Total assets: {response.Data.PageInfo.TotalNumber}");
                    }
                }
                else
                {
                    Console.WriteLine("No partner assets found.");
                }
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message} (Code: {ex.Code})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example demonstrating a complete partner management workflow
        /// </summary>
        public async Task CompletePartnerWorkflowExample()
        {
            try
            {
                Console.WriteLine("=== Complete Partner Management Workflow ===");
                
                // 1. Get current partners
                Console.WriteLine("\n1. Getting current partners...");
                await GetPartnersExample();
                
                // 2. Add a new partner
                Console.WriteLine("\n2. Adding a new partner...");
                await AddPartnerExample();
                
                // 3. Get partner assets
                Console.WriteLine("\n3. Getting partner assets...");
                await GetPartnerAssetsExample();
                
                // 4. Cancel asset sharing (optional)
                Console.WriteLine("\n4. Canceling asset sharing...");
                await CancelAssetSharingExample();
                
                // 5. Delete partner (optional)
                Console.WriteLine("\n5. Deleting partner...");
                await DeletePartnerExample();
                
                Console.WriteLine("\n=== Workflow completed ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Workflow error: {ex.Message}");
            }
        }
    }
}
