using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using TikTok.AdAccounts;
using TikTok.Customers;
using Volo.Abp.DependencyInjection;

namespace TikTok.Activities.Notifications
{
    [Activity(Category = "Thông báo", DisplayName = "Chuẩn bị dữ liệu thông báo", Outcomes = new[] { OutcomeNames.Done })]
    public class PrepareDataNotificationActivity : UnitOfWorkActivity
    {
        private readonly IAbpLazyServiceProvider _lazyServiceProvider;
        private readonly ICustomerQueryService _customerQueryService;
        private readonly IAdAccountSupporterQueryService _adAccountSupporterQueryService;

        public PrepareDataNotificationActivity(IAbpLazyServiceProvider lazyServiceProvider)
        {
            _lazyServiceProvider = lazyServiceProvider;
            _customerQueryService = _lazyServiceProvider.GetRequiredService<ICustomerQueryService>();
            _adAccountSupporterQueryService = _lazyServiceProvider.GetRequiredService<IAdAccountSupporterQueryService>();
        }

        [ActivityInput(Hint = "Business Center ID", SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public string BcId { get; set; }

        [ActivityInput(Hint = "Danh sách Advertiser IDs", SupportedSyntaxes = new[] { SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json })]
        public List<string> AdvertiserIds { get; set; } = new List<string>();

        [ActivityOutput(Hint = "Danh sách khách hàng")]
        public List<CustomerAdvertiserDto> Customers { get; set; } = new List<CustomerAdvertiserDto>();

        [ActivityOutput(Hint = "Danh sách người hỗ trợ")]
        public List<AdAccountSupporterListDto> Supporters { get; set; } = new List<AdAccountSupporterListDto>();

        [ActivityOutput(Hint = "Thông tin về dữ liệu đã được lấy")]
        public DataRetrievalInfo DataRetrievalInfo { get; set; } = new DataRetrievalInfo();

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            if (string.IsNullOrEmpty(BcId))
            {
                throw new InvalidOperationException("BcId is required.");
            }

            var startTime = DateTime.UtcNow;
            var retrievalInfo = new DataRetrievalInfo
            {
                StartTime = startTime,
                BcId = BcId,
                AdvertiserIdsCount = AdvertiserIds?.Count ?? 0,
                RetrievedDataTypes = new List<string>()
            };

            try
            {
                // Lấy danh sách khách hàng
                await GetCustomersAsync();
                retrievalInfo.RetrievedDataTypes.Add("Customers");

                // Lấy danh sách người hỗ trợ
                await GetSupportersAsync();
                retrievalInfo.RetrievedDataTypes.Add("Supporters");

                retrievalInfo.EndTime = DateTime.UtcNow;
                retrievalInfo.Duration = retrievalInfo.EndTime - retrievalInfo.StartTime;
                retrievalInfo.Success = true;
                DataRetrievalInfo = retrievalInfo;

                return Done();
            }
            catch (Exception ex)
            {
                retrievalInfo.EndTime = DateTime.UtcNow;
                retrievalInfo.Duration = retrievalInfo.EndTime - retrievalInfo.StartTime;
                retrievalInfo.Success = false;
                retrievalInfo.ErrorMessage = ex.Message;
                DataRetrievalInfo = retrievalInfo;
                throw;
            }
        }

        private async Task GetCustomersAsync()
        {
            if (AdvertiserIds != null && AdvertiserIds.Any())
            {
                // Lấy khách hàng theo AdvertiserIds
                Customers = await _customerQueryService.GetCustomersByAdvertiserIdsAsync(AdvertiserIds);
            }
            else
            {
                // Lấy tất cả khách hàng theo BcId
                Customers = await _customerQueryService.GetCustomersByBcIdAsync(BcId);
            }
        }

        private async Task GetSupportersAsync()
        {
            if (AdvertiserIds != null && AdvertiserIds.Any())
            {
                // Lấy người hỗ trợ theo BcId và AdvertiserIds
                Supporters = await _adAccountSupporterQueryService.GetSupportersByBcIdAndAdvertiserIdsAsync(BcId, AdvertiserIds, false);
            }
            else
            {
                // Lấy tất cả người hỗ trợ theo BcId
                Supporters = await _adAccountSupporterQueryService.GetSupportersByBcIdAsync(BcId, false);
            }
        }
    }
}