﻿using System;
using Volo.Abp.Application.Dtos;
using TikTok.DimAdAccounts;
using TikTok.DimBusinessCenters;
using TikTok.DimDates;
using TikTok.Campaigns;

namespace TikTok.FactCampaigns.Dtos
{
    public class FactCampaignDto : AuditedEntityDto<Guid>
    {
        public int DimDateId { get; set; }
        public Guid DimAdAccountId { get; set; }
        public Guid DimBusinessCenterId { get; set; }
        public Guid DimCampaignId { get; set; }

        public string CampaignId { get; set; }
        public string CampaignName { get; set; }
        public string AdvertiserId { get; set; }
        public string? AdvertiserName { get; set; }
        public string BcId { get; set; }
        public string ObjectiveType { get; set; }
        public string? CampaignDedicateType { get; set; }

        public decimal Spend { get; set; }
        public long Impressions { get; set; }
        public long Clicks { get; set; }
        public long Conversion { get; set; }
        public decimal CostPerConversion { get; set; }
        public decimal ConversionRate { get; set; }
        public long Result { get; set; }
        public decimal CostPerResult { get; set; }
        public decimal OnsiteShoppingRoas { get; set; }
        public decimal TotalOnsiteShoppingValue { get; set; }
        public long OnsiteShopping { get; set; }
        public decimal CostPerOnsiteShopping { get; set; }
        public decimal ValuePerOnsiteShopping { get; set; }
        public string Currency { get; set; }
        public DateTime Date { get; set; }
    }
}
