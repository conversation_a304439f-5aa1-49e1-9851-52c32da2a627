/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Message Sending Models

    /// <summary>
    /// Request body for sending a message to a conversation
    /// </summary>
    public class MessageSendRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// The type of recipient (currently only CONVERSATION is supported)
        /// </summary>
        [JsonPropertyName("recipient_type")]
        public string? RecipientType { get; set; }

        /// <summary>
        /// Recipient (conversation ID when recipient_type is CONVERSATION)
        /// </summary>
        [JsonPropertyName("recipient")]
        public string? Recipient { get; set; }

        /// <summary>
        /// The type of the message
        /// </summary>
        [JsonPropertyName("message_type")]
        public string? MessageType { get; set; }

        /// <summary>
        /// Text information (required when message_type is TEXT)
        /// </summary>
        [JsonPropertyName("text")]
        public MessageText? Text { get; set; }

        /// <summary>
        /// Image information (required when message_type is IMAGE)
        /// </summary>
        [JsonPropertyName("image")]
        public MessageImage? Image { get; set; }

        /// <summary>
        /// Information about the post to be shared (required when message_type is SHARE_POST)
        /// </summary>
        [JsonPropertyName("share_post")]
        public MessageSharePost? SharePost { get; set; }

        /// <summary>
        /// Details about the message template to send (required when message_type is TEMPLATE)
        /// </summary>
        [JsonPropertyName("template")]
        public MessageTemplate? Template { get; set; }

        /// <summary>
        /// The sender's action in the messaging session (required when message_type is SENDER_ACTION)
        /// </summary>
        [JsonPropertyName("sender_action")]
        public string? SenderAction { get; set; }

        /// <summary>
        /// Information about the original message to reply to
        /// </summary>
        [JsonPropertyName("referenced_message_info")]
        public ReferencedMessageInfo? ReferencedMessageInfo { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageSendRequest(BusinessId={BusinessId}, MessageType={MessageType}, Recipient={Recipient})";
        }
    }

    /// <summary>
    /// Text information for a message
    /// </summary>
    public class MessageText
    {
        /// <summary>
        /// Text content (required when text is specified)
        /// </summary>
        [JsonPropertyName("body")]
        public string? Body { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageText(Body={Body})";
        }
    }

    /// <summary>
    /// Image information for a message
    /// </summary>
    public class MessageImage
    {
        /// <summary>
        /// Image ID (required when image is specified)
        /// </summary>
        [JsonPropertyName("media_id")]
        public string? MediaId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageImage(MediaId={MediaId})";
        }
    }

    /// <summary>
    /// Information about the post to be shared
    /// </summary>
    public class MessageSharePost
    {
        /// <summary>
        /// The ID of the TikTok post to share with others (required when share_post is specified)
        /// </summary>
        [JsonPropertyName("item_id")]
        public string? ItemId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageSharePost(ItemId={ItemId})";
        }
    }

    /// <summary>
    /// Details about the message template to send
    /// </summary>
    public class MessageTemplate
    {
        /// <summary>
        /// The type of message template (required when template is specified)
        /// </summary>
        [JsonPropertyName("type")]
        public string? Type { get; set; }

        /// <summary>
        /// The pre-set question on the card (required when template is specified)
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// A list of buttons or text links that represent answers to the question on the card (required when template is specified)
        /// </summary>
        [JsonPropertyName("buttons")]
        public List<MessageTemplateButton>? Buttons { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageTemplate(Type={Type}, Title={Title}, ButtonsCount={Buttons?.Count})";
        }
    }

    /// <summary>
    /// Button or text link for message template
    /// </summary>
    public class MessageTemplateButton
    {
        /// <summary>
        /// The type of the button or text link (required when buttons is specified)
        /// </summary>
        [JsonPropertyName("type")]
        public string? Type { get; set; }

        /// <summary>
        /// The text content of the button or text link (required when buttons is specified)
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// A self-defined ID for the button or text link
        /// </summary>
        [JsonPropertyName("id")]
        public string? Id { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageTemplateButton(Type={Type}, Title={Title}, Id={Id})";
        }
    }

    /// <summary>
    /// Information about the original message to reply to
    /// </summary>
    public class ReferencedMessageInfo
    {
        /// <summary>
        /// The ID of the original message that you want to reply to (required when referenced_message_info is specified)
        /// </summary>
        [JsonPropertyName("referenced_message_id")]
        public string? ReferencedMessageId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ReferencedMessageInfo(ReferencedMessageId={ReferencedMessageId})";
        }
    }

    /// <summary>
    /// Response data for message send API
    /// </summary>
    public class MessageSendResponse
    {
        /// <summary>
        /// Message details
        /// </summary>
        [JsonPropertyName("message")]
        public MessageSendData? Message { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageSendResponse(Message={Message})";
        }
    }

    /// <summary>
    /// Message send data
    /// </summary>
    public class MessageSendData
    {
        /// <summary>
        /// Message ID
        /// </summary>
        [JsonPropertyName("message_id")]
        public string? MessageId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageSendData(MessageId={MessageId})";
        }
    }

    #endregion

    #region Message Type Constants

    /// <summary>
    /// Message type enumeration
    /// </summary>
    public static class MessageType
    {
        public const string Text = "TEXT";
        public const string Image = "IMAGE";
        public const string SharePost = "SHARE_POST";
        public const string Template = "TEMPLATE";
        public const string SenderAction = "SENDER_ACTION";
    }

    /// <summary>
    /// Recipient type enumeration
    /// </summary>
    public static class RecipientType
    {
        public const string Conversation = "CONVERSATION";
    }

    /// <summary>
    /// Sender action enumeration
    /// </summary>
    public static class SenderAction
    {
        public const string Typing = "TYPING";
        public const string MarkRead = "MARK_READ";
    }

    /// <summary>
    /// Template type enumeration
    /// </summary>
    public static class TemplateType
    {
        public const string QaButtonCard = "QA_BUTTON_CARD";
        public const string QaLinkCard = "QA_LINK_CARD";
    }

    /// <summary>
    /// Template button type enumeration
    /// </summary>
    public static class TemplateButtonType
    {
        public const string Reply = "REPLY";
    }

    #endregion

    #region Conversation Models

    /// <summary>
    /// Response data for conversation list API
    /// </summary>
    public class ConversationListResponse
    {
        /// <summary>
        /// Conversation List
        /// </summary>
        [JsonPropertyName("conversations")]
        public List<ConversationData>? Conversations { get; set; }

        /// <summary>
        /// Whether an additional page of conversations is available
        /// </summary>
        [JsonPropertyName("has_more")]
        public bool? HasMore { get; set; }

        /// <summary>
        /// Cursor for the next page of conversations
        /// </summary>
        [JsonPropertyName("cursor")]
        public int? Cursor { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ConversationListResponse(ConversationsCount={Conversations?.Count}, HasMore={HasMore}, Cursor={Cursor})";
        }
    }

    /// <summary>
    /// Conversation data
    /// </summary>
    public class ConversationData
    {
        /// <summary>
        /// Conversation ID
        /// </summary>
        [JsonPropertyName("conversation_id")]
        public string? ConversationId { get; set; }

        /// <summary>
        /// The time of the last message in the conversation, in the format of an Epoch/Unix timestamp in milliseconds
        /// </summary>
        [JsonPropertyName("update_time")]
        public long? UpdateTime { get; set; }

        /// <summary>
        /// Referral information of the conversation
        /// </summary>
        [JsonPropertyName("referral")]
        public ConversationReferral? Referral { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ConversationData(ConversationId={ConversationId}, UpdateTime={UpdateTime})";
        }
    }

    /// <summary>
    /// Referral information of the conversation
    /// </summary>
    public class ConversationReferral
    {
        /// <summary>
        /// Related referral ad information of the conversation
        /// </summary>
        [JsonPropertyName("ad")]
        public List<ConversationReferralAd>? Ad { get; set; }

        /// <summary>
        /// Information about the tiktok.me link
        /// </summary>
        [JsonPropertyName("short_link")]
        public List<ConversationReferralShortLink>? ShortLink { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ConversationReferral(AdCount={Ad?.Count}, ShortLinkCount={ShortLink?.Count})";
        }
    }

    /// <summary>
    /// Related referral ad information of the conversation
    /// </summary>
    public class ConversationReferralAd
    {
        /// <summary>
        /// The ID of the advertiser account that is associated with the ad
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Ad ID of the related ad
        /// </summary>
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        /// <summary>
        /// The time of the referral event, in the format of an Epoch/Unix timestamp in seconds
        /// </summary>
        [JsonPropertyName("timestamp")]
        public long? Timestamp { get; set; }

        /// <summary>
        /// The name of the related ad
        /// </summary>
        [JsonPropertyName("ad_name")]
        public string? AdName { get; set; }

        /// <summary>
        /// An embeddable link for the TikTok post used in the related ad
        /// </summary>
        [JsonPropertyName("embed_url")]
        public string? EmbedUrl { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ConversationReferralAd(AdId={AdId}, AdName={AdName}, AdvertiserId={AdvertiserId})";
        }
    }

    /// <summary>
    /// Information about the tiktok.me link
    /// </summary>
    public class ConversationReferralShortLink
    {
        /// <summary>
        /// The referral parameter (ref) that is configured in the tiktok.me link
        /// </summary>
        [JsonPropertyName("ref")]
        public string? Ref { get; set; }

        /// <summary>
        /// The prefilled message that is configured in the tiktok.me link
        /// </summary>
        [JsonPropertyName("prefilled_message")]
        public string? PrefilledMessage { get; set; }

        /// <summary>
        /// The audit status of the prefilled message
        /// </summary>
        [JsonPropertyName("prefilled_message_audit_status")]
        public string? PrefilledMessageAuditStatus { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ConversationReferralShortLink(Ref={Ref}, PrefilledMessage={PrefilledMessage}, AuditStatus={PrefilledMessageAuditStatus})";
        }
    }

    /// <summary>
    /// Conversation type enumeration
    /// </summary>
    public static class ConversationType
    {
        public const string Stranger = "STRANGER";
        public const string Single = "SINGLE";
    }

    /// <summary>
    /// Prefilled message audit status enumeration
    /// </summary>
    public static class PrefilledMessageAuditStatus
    {
        public const string Reject = "REJECT";
        public const string Pass = "PASS";
    }

    #endregion

    #region Message Content Models

    /// <summary>
    /// Response data for message content list API
    /// </summary>
    public class MessageContentListResponse
    {
        /// <summary>
        /// Messages in the conversation
        /// </summary>
        [JsonPropertyName("messages")]
        public List<MessageContentData>? Messages { get; set; }

        /// <summary>
        /// Information of participants in the conversation
        /// </summary>
        [JsonPropertyName("participants")]
        public List<MessageParticipant>? Participants { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageContentListResponse(MessagesCount={Messages?.Count}, ParticipantsCount={Participants?.Count})";
        }
    }

    /// <summary>
    /// Message content data
    /// </summary>
    public class MessageContentData
    {
        /// <summary>
        /// TikTok username of the message sender
        /// </summary>
        [JsonPropertyName("sender")]
        public string? Sender { get; set; }

        /// <summary>
        /// TikTok username of the message recipient
        /// </summary>
        [JsonPropertyName("recipient")]
        public string? Recipient { get; set; }

        /// <summary>
        /// Conversation ID
        /// </summary>
        [JsonPropertyName("conversation_id")]
        public string? ConversationId { get; set; }

        /// <summary>
        /// Message ID
        /// </summary>
        [JsonPropertyName("message_id")]
        public string? MessageId { get; set; }

        /// <summary>
        /// The time when the message was sent, in the format of an Epoch/Unix timestamp in milliseconds
        /// </summary>
        [JsonPropertyName("timestamp")]
        public long? Timestamp { get; set; }

        /// <summary>
        /// The type of the message
        /// </summary>
        [JsonPropertyName("message_type")]
        public string? MessageType { get; set; }

        /// <summary>
        /// Text information (returned only when message_type is TEXT)
        /// </summary>
        [JsonPropertyName("text")]
        public MessageText? Text { get; set; }

        /// <summary>
        /// Image information (returned only when message_type is IMAGE)
        /// </summary>
        [JsonPropertyName("image")]
        public MessageImage? Image { get; set; }

        /// <summary>
        /// Information about the post shared (returned only when message_type is SHARE_POST)
        /// </summary>
        [JsonPropertyName("share_post")]
        public MessageContentSharePost? SharePost { get; set; }

        /// <summary>
        /// Details about the message template sent (returned only when message_type is TEMPLATE)
        /// </summary>
        [JsonPropertyName("template")]
        public MessageTemplate? Template { get; set; }

        /// <summary>
        /// User information of the sender
        /// </summary>
        [JsonPropertyName("from_user")]
        public MessageUser? FromUser { get; set; }

        /// <summary>
        /// User information of the receiver
        /// </summary>
        [JsonPropertyName("to_user")]
        public MessageUser? ToUser { get; set; }

        /// <summary>
        /// Information about the original message that is replied to
        /// </summary>
        [JsonPropertyName("referenced_message_info")]
        public ReferencedMessageInfo? ReferencedMessageInfo { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageContentData(MessageId={MessageId}, MessageType={MessageType}, Sender={Sender}, Recipient={Recipient})";
        }
    }

    /// <summary>
    /// Information about the post shared in message content
    /// </summary>
    public class MessageContentSharePost
    {
        /// <summary>
        /// An embeddable link for the TikTok post
        /// </summary>
        [JsonPropertyName("embed_url")]
        public string? EmbedUrl { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageContentSharePost(EmbedUrl={EmbedUrl})";
        }
    }

    /// <summary>
    /// User information for message sender/receiver
    /// </summary>
    public class MessageUser
    {
        /// <summary>
        /// The TikTok account type of the user
        /// </summary>
        [JsonPropertyName("role")]
        public string? Role { get; set; }

        /// <summary>
        /// The identifier of the user
        /// </summary>
        [JsonPropertyName("id")]
        public string? Id { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageUser(Role={Role}, Id={Id})";
        }
    }

    /// <summary>
    /// Information of participants in the conversation
    /// </summary>
    public class MessageParticipant
    {
        /// <summary>
        /// The TikTok account type of the participant
        /// </summary>
        [JsonPropertyName("role")]
        public string? Role { get; set; }

        /// <summary>
        /// The identifier of the participant
        /// </summary>
        [JsonPropertyName("id")]
        public string? Id { get; set; }

        /// <summary>
        /// The TikTok display name (nickname) of the participant
        /// </summary>
        [JsonPropertyName("display_name")]
        public string? DisplayName { get; set; }

        /// <summary>
        /// Temporary URL for the TikTok profile image of the participant
        /// </summary>
        [JsonPropertyName("profile_image")]
        public string? ProfileImage { get; set; }

        /// <summary>
        /// Whether the Personal Account follows the Business Account in the conversation (returned only when role is PERSONAL_ACCOUNT)
        /// </summary>
        [JsonPropertyName("is_follower")]
        public bool? IsFollower { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MessageParticipant(Role={Role}, Id={Id}, DisplayName={DisplayName}, IsFollower={IsFollower})";
        }
    }

    /// <summary>
    /// User role enumeration
    /// </summary>
    public static class UserRole
    {
        public const string BusinessAccount = "BUSINESS_ACCOUNT";
        public const string PersonalAccount = "PERSONAL_ACCOUNT";
    }

    #endregion

    #region Media Models

    /// <summary>
    /// Response data for media upload API
    /// </summary>
    public class MediaUploadResponse
    {
        /// <summary>
        /// Media ID of the uploaded image
        /// </summary>
        [JsonPropertyName("media_id")]
        public string? MediaId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MediaUploadResponse(MediaId={MediaId})";
        }
    }

    /// <summary>
    /// Request body for downloading an image from a message
    /// </summary>
    public class MediaDownloadRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// Conversation ID
        /// </summary>
        [JsonPropertyName("conversation_id")]
        public string? ConversationId { get; set; }

        /// <summary>
        /// Message ID
        /// </summary>
        [JsonPropertyName("message_id")]
        public string? MessageId { get; set; }

        /// <summary>
        /// Media ID
        /// </summary>
        [JsonPropertyName("media_id")]
        public string? MediaId { get; set; }

        /// <summary>
        /// Multimedia type (currently only IMAGE is supported)
        /// </summary>
        [JsonPropertyName("media_type")]
        public string? MediaType { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MediaDownloadRequest(BusinessId={BusinessId}, ConversationId={ConversationId}, MessageId={MessageId}, MediaId={MediaId})";
        }
    }

    /// <summary>
    /// Response data for media download API
    /// </summary>
    public class MediaDownloadResponse
    {
        /// <summary>
        /// Download URL (valid for 24 hours)
        /// </summary>
        [JsonPropertyName("download_url")]
        public string? DownloadUrl { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"MediaDownloadResponse(DownloadUrl={DownloadUrl})";
        }
    }

    /// <summary>
    /// Media type enumeration
    /// </summary>
    public static class MediaType
    {
        public const string Image = "IMAGE";
    }

    #endregion

    #region Webhook Models

    /// <summary>
    /// Request body for creating/updating Business Messaging Webhook configuration
    /// </summary>
    public class BusinessMessagingWebhookRequest
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Secret of your developer application
        /// </summary>
        [JsonPropertyName("secret")]
        public string? Secret { get; set; }

        /// <summary>
        /// The type of Webhook event that you want to subscribe to
        /// </summary>
        [JsonPropertyName("event_type")]
        public string? EventType { get; set; }

        /// <summary>
        /// The callback URL
        /// </summary>
        [JsonPropertyName("callback_url")]
        public string? CallbackUrl { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"BusinessMessagingWebhookRequest(AppId={AppId}, EventType={EventType}, CallbackUrl={CallbackUrl})";
        }
    }

    /// <summary>
    /// Response data for Business Messaging Webhook configuration
    /// </summary>
    public class BusinessMessagingWebhookResponse
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// The type of Webhook event that you subscribe to
        /// </summary>
        [JsonPropertyName("event_type")]
        public string? EventType { get; set; }

        /// <summary>
        /// The callback URL (returned only when the corresponding event configuration exists)
        /// </summary>
        [JsonPropertyName("callback_url")]
        public string? CallbackUrl { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"BusinessMessagingWebhookResponse(AppId={AppId}, EventType={EventType}, CallbackUrl={CallbackUrl})";
        }
    }

    /// <summary>
    /// Request body for deleting Business Messaging Webhook configuration
    /// </summary>
    public class BusinessMessagingWebhookDeleteRequest
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// Secret of your developer application
        /// </summary>
        [JsonPropertyName("secret")]
        public string? Secret { get; set; }

        /// <summary>
        /// The type of Webhook event configuration that you want to delete
        /// </summary>
        [JsonPropertyName("event_type")]
        public string? EventType { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"BusinessMessagingWebhookDeleteRequest(AppId={AppId}, EventType={EventType})";
        }
    }

    /// <summary>
    /// Business Messaging Webhook event type enumeration
    /// </summary>
    public static class BusinessMessagingWebhookEventType
    {
        public const string DirectMessage = "DIRECT_MESSAGE";
    }

    #endregion

    #region Auto Message Models

    /// <summary>
    /// Request body for creating an automatic message
    /// </summary>
    public class AutoMessageCreateRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// The type of automatic message
        /// </summary>
        [JsonPropertyName("auto_message_type")]
        public string? AutoMessageType { get; set; }

        /// <summary>
        /// Information about the welcome message (required when auto_message_type is WELCOME_MESSAGE)
        /// </summary>
        [JsonPropertyName("welcome_message")]
        public AutoMessageWelcome? WelcomeMessage { get; set; }

        /// <summary>
        /// Information about the suggested question (required when auto_message_type is SUGGESTED_QUESTION)
        /// </summary>
        [JsonPropertyName("suggested_question")]
        public AutoMessageSuggestedQuestion? SuggestedQuestion { get; set; }

        /// <summary>
        /// Information about the chat prompt (required when auto_message_type is CHAT_PROMPT)
        /// </summary>
        [JsonPropertyName("chat_prompt")]
        public AutoMessageChatPrompt? ChatPrompt { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AutoMessageCreateRequest(BusinessId={BusinessId}, AutoMessageType={AutoMessageType})";
        }
    }

    /// <summary>
    /// Request body for updating an automatic message
    /// </summary>
    public class AutoMessageUpdateRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// The ID of the automatic message
        /// </summary>
        [JsonPropertyName("auto_message_id")]
        public string? AutoMessageId { get; set; }

        /// <summary>
        /// The type of automatic message
        /// </summary>
        [JsonPropertyName("auto_message_type")]
        public string? AutoMessageType { get; set; }

        /// <summary>
        /// Information about the welcome message (required when auto_message_type is WELCOME_MESSAGE)
        /// </summary>
        [JsonPropertyName("welcome_message")]
        public AutoMessageWelcome? WelcomeMessage { get; set; }

        /// <summary>
        /// Information about the suggested question (required when auto_message_type is SUGGESTED_QUESTION)
        /// </summary>
        [JsonPropertyName("suggested_question")]
        public AutoMessageSuggestedQuestion? SuggestedQuestion { get; set; }

        /// <summary>
        /// Information about the chat prompt (required when auto_message_type is CHAT_PROMPT)
        /// </summary>
        [JsonPropertyName("chat_prompt")]
        public AutoMessageChatPrompt? ChatPrompt { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AutoMessageUpdateRequest(BusinessId={BusinessId}, AutoMessageId={AutoMessageId}, AutoMessageType={AutoMessageType})";
        }
    }

    /// <summary>
    /// Request body for updating automatic message status
    /// </summary>
    public class AutoMessageStatusUpdateRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// The type of automatic message
        /// </summary>
        [JsonPropertyName("auto_message_type")]
        public string? AutoMessageType { get; set; }

        /// <summary>
        /// The operation to perform on the type of automatic message
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string? OperationStatus { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AutoMessageStatusUpdateRequest(BusinessId={BusinessId}, AutoMessageType={AutoMessageType}, OperationStatus={OperationStatus})";
        }
    }

    /// <summary>
    /// Request body for deleting an automatic message
    /// </summary>
    public class AutoMessageDeleteRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// The type of automatic message
        /// </summary>
        [JsonPropertyName("auto_message_type")]
        public string? AutoMessageType { get; set; }

        /// <summary>
        /// The ID of the automatic message
        /// </summary>
        [JsonPropertyName("auto_message_id")]
        public string? AutoMessageId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AutoMessageDeleteRequest(BusinessId={BusinessId}, AutoMessageType={AutoMessageType}, AutoMessageId={AutoMessageId})";
        }
    }

    /// <summary>
    /// Request body for sorting automatic messages
    /// </summary>
    public class AutoMessageSortRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// The type of automatic message (must be CHAT_PROMPT)
        /// </summary>
        [JsonPropertyName("auto_message_type")]
        public string? AutoMessageType { get; set; }

        /// <summary>
        /// The IDs of the chat prompts in the desired order
        /// </summary>
        [JsonPropertyName("auto_message_ids")]
        public List<string>? AutoMessageIds { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AutoMessageSortRequest(BusinessId={BusinessId}, AutoMessageType={AutoMessageType}, IdsCount={AutoMessageIds?.Count})";
        }
    }

    /// <summary>
    /// Information about the welcome message
    /// </summary>
    public class AutoMessageWelcome
    {
        /// <summary>
        /// The text of the welcome message (required when welcome_message is passed)
        /// </summary>
        [JsonPropertyName("content")]
        public string? Content { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AutoMessageWelcome(Content={Content})";
        }
    }

    /// <summary>
    /// Information about the suggested question
    /// </summary>
    public class AutoMessageSuggestedQuestion
    {
        /// <summary>
        /// A frequently asked question to help users start a conversation (required when suggested_question is passed)
        /// </summary>
        [JsonPropertyName("question")]
        public string? Question { get; set; }

        /// <summary>
        /// The preset answer that will be automatically sent when a user selects the question (required when suggested_question is passed)
        /// </summary>
        [JsonPropertyName("answer")]
        public string? Answer { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AutoMessageSuggestedQuestion(Question={Question}, Answer={Answer})";
        }
    }

    /// <summary>
    /// Information about the chat prompt
    /// </summary>
    public class AutoMessageChatPrompt
    {
        /// <summary>
        /// The name of the prompt button that is displayed above the input box (required when chat_prompt is passed)
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// The corresponding question for the prompt (required when chat_prompt is passed)
        /// </summary>
        [JsonPropertyName("content")]
        public string? Content { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AutoMessageChatPrompt(Title={Title}, Content={Content})";
        }
    }

    /// <summary>
    /// Response data for auto message create/update API
    /// </summary>
    public class AutoMessageResponse
    {
        /// <summary>
        /// Information about the automatic message
        /// </summary>
        [JsonPropertyName("auto_message")]
        public AutoMessageData? AutoMessage { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AutoMessageResponse(AutoMessage={AutoMessage})";
        }
    }

    /// <summary>
    /// Auto message data
    /// </summary>
    public class AutoMessageData
    {
        /// <summary>
        /// The ID of the automatic message
        /// </summary>
        [JsonPropertyName("auto_message_id")]
        public string? AutoMessageId { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AutoMessageData(AutoMessageId={AutoMessageId})";
        }
    }

    /// <summary>
    /// Response data for auto message get API
    /// </summary>
    public class AutoMessageGetResponse
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok Business Account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string? BusinessId { get; set; }

        /// <summary>
        /// The status of the automatic message
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string? OperationStatus { get; set; }

        /// <summary>
        /// The list of automatic messages
        /// </summary>
        [JsonPropertyName("auto_messages")]
        public List<AutoMessageInfo>? AutoMessages { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AutoMessageGetResponse(BusinessId={BusinessId}, OperationStatus={OperationStatus}, AutoMessagesCount={AutoMessages?.Count})";
        }
    }

    /// <summary>
    /// Auto message information
    /// </summary>
    public class AutoMessageInfo
    {
        /// <summary>
        /// The ID of the automatic message
        /// </summary>
        [JsonPropertyName("auto_message_id")]
        public string? AutoMessageId { get; set; }

        /// <summary>
        /// The type of automatic message
        /// </summary>
        [JsonPropertyName("auto_message_type")]
        public string? AutoMessageType { get; set; }

        /// <summary>
        /// The review status of the automatic message
        /// </summary>
        [JsonPropertyName("audit_status")]
        public string? AuditStatus { get; set; }

        /// <summary>
        /// Information about the welcome message (returned only when auto_message_type is WELCOME_MESSAGE)
        /// </summary>
        [JsonPropertyName("welcome_message")]
        public AutoMessageWelcome? WelcomeMessage { get; set; }

        /// <summary>
        /// Information about the suggested question (returned only when auto_message_type is SUGGESTED_QUESTION)
        /// </summary>
        [JsonPropertyName("suggested_question")]
        public AutoMessageSuggestedQuestion? SuggestedQuestion { get; set; }

        /// <summary>
        /// Information about the chat prompt (returned only when auto_message_type is CHAT_PROMPT)
        /// </summary>
        [JsonPropertyName("chat_prompt")]
        public AutoMessageChatPrompt? ChatPrompt { get; set; }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AutoMessageInfo(AutoMessageId={AutoMessageId}, AutoMessageType={AutoMessageType}, AuditStatus={AuditStatus})";
        }
    }

    /// <summary>
    /// Auto message type enumeration
    /// </summary>
    public static class AutoMessageType
    {
        public const string WelcomeMessage = "WELCOME_MESSAGE";
        public const string SuggestedQuestion = "SUGGESTED_QUESTION";
        public const string ChatPrompt = "CHAT_PROMPT";
    }

    /// <summary>
    /// Auto message operation status enumeration
    /// </summary>
    public static class AutoMessageOperationStatus
    {
        public const string Enable = "ENABLE";
        public const string Disable = "DISABLE";
    }

    /// <summary>
    /// Auto message audit status enumeration
    /// </summary>
    public static class AutoMessageAuditStatus
    {
        public const string Reviewing = "REVIEWING";
        public const string Approved = "APPROVED";
        public const string Rejected = "REJECTED";
    }

    #endregion
}
