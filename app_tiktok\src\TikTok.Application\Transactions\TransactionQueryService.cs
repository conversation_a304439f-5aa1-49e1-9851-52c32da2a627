using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.Repositories;
using TikTok.Transactions;
using Volo.Abp;
using Volo.Abp.ObjectMapping;

namespace TikTok.Transactions
{
    /// <summary>
    /// Service implementation cho việc truy vấn giao dịch
    /// </summary>
    public class TransactionQueryService : ITransactionQueryService
    {
        private readonly ITransactionRepository _transactionRepository;
        private readonly IObjectMapper _objectMapper;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="transactionRepository">Transaction Repository</param>
        /// <param name="objectMapper">Object Mapper</param>
        public TransactionQueryService(
            ITransactionRepository transactionRepository,
            IObjectMapper objectMapper)
        {
            _transactionRepository = transactionRepository;
            _objectMapper = objectMapper;
        }

        /// <summary>
        /// Lấy danh sách giao dịch theo Transaction IDs và Transaction Level (tùy chọn)
        /// </summary>
        /// <param name="transactionIds">Danh sách Transaction IDs</param>
        /// <param name="transactionLevel">Cấp độ giao dịch (tùy chọn)</param>
        /// <returns>Danh sách giao dịch</returns>
        public async Task<List<TransactionDto>> GetTransactionsByIdsAsync(List<string> transactionIds, TransactionLevel? transactionLevel = null)
        {
            Check.NotNullOrEmpty(transactionIds, nameof(transactionIds));

            // Lấy danh sách giao dịch theo Transaction IDs
            var transactions = await _transactionRepository.GetByManyTransactionIdsAsync(transactionIds);

            // Lọc theo Transaction Level nếu có
            if (transactionLevel.HasValue)
            {
                transactions = transactions.Where(x => x.TransactionLevel == transactionLevel.Value).ToList();
            }

            // Map sang DTO và trả về
            return _objectMapper.Map<List<RawTransactionEntity>, List<TransactionDto>>(transactions);
        }

        /// <summary>
        /// Lấy danh sách giao dịch theo DTO input
        /// </summary>
        /// <param name="input">DTO chứa thông tin đầu vào</param>
        /// <returns>Danh sách giao dịch</returns>
        public async Task<List<TransactionDto>> GetTransactionsByIdsAsync(GetTransactionsByIdsDto input)
        {
            Check.NotNull(input, nameof(input));
            
            return await GetTransactionsByIdsAsync(input.TransactionIds, input.TransactionLevel);
        }
    }
}
