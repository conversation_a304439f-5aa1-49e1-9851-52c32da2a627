using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Worker để đồng bộ báo cáo tích hợp Campaign
    /// </summary>
    public class SyncReportIntegratedCampaignWorker : WorkerJobBase
    {
        private readonly IReportIntegratedCampaignSyncService _reportIntegratedCampaignSyncService;

        public SyncReportIntegratedCampaignWorker(
            ILogger<SyncReportIntegratedCampaignWorker> logger,
            IServiceProvider serviceProvider,
            IReportIntegratedCampaignSyncService reportIntegratedCampaignSyncService)
            : base(logger, serviceProvider)
        {
            _reportIntegratedCampaignSyncService = reportIntegratedCampaignSyncService;
        }

        public override CommandType CommandType => CommandType.SyncReportIntegratedCampaign;

        protected override async Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job)
        {
            // Parse parameters
            var parameters = ParseParameters(args.Parameters);

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp Campaign cho Business Center {BcId}", parameters.BcId);

                var responseSync = await _reportIntegratedCampaignSyncService.SyncReportIntegratedCampaignAsync(parameters.BcId);
                if (responseSync != null)
                {
                    if (responseSync.IsSuccess)
                    {
                        _logger.LogDebug("Đồng bộ báo cáo tích hợp Campaign thành công cho Business Center {BcId}", parameters.BcId);
                        return JobResult.Success(JsonConvert.SerializeObject(new { Success = true, Data = responseSync }));
                    }
                    else
                    {
                        _logger.LogWarning("Đồng bộ báo cáo tích hợp Campaign thất bại cho Business Center {BcId}: {ErrorMessage}",
                            parameters.BcId, responseSync.ErrorMessage);
                        return JobResult.Error(JsonConvert.SerializeObject(new { Success = false, Error = responseSync.ErrorMessage }));
                    }
                }
                else
                {
                    _logger.LogWarning("Đồng bộ báo cáo tích hợp Campaign thất bại cho Business Center {BcId}: Lỗi không xác định", parameters.BcId);
                    return JobResult.Error(JsonConvert.SerializeObject(new { Success = false, Error = "Lỗi đồng bộ báo cáo tích hợp Campaign" }));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi exception khi đồng bộ báo cáo tích hợp Campaign cho Business Center {BcId}", parameters.BcId);
                return JobResult.Error(JsonConvert.SerializeObject(new { Success = false, Error = ex.Message }));
            }
        }

        /// <summary>
        /// Parse parameters từ JSON
        /// </summary>
        /// <param name="parametersJson">JSON parameters</param>
        /// <returns>Parameters object</returns>
        private SyncReportIntegratedCampaignParameters ParseParameters(string parametersJson)
        {
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<SyncReportIntegratedCampaignParameters>(parametersJson)
                    ?? new SyncReportIntegratedCampaignParameters();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse parameters, using defaults");
                return new SyncReportIntegratedCampaignParameters();
            }
        }

        /// <summary>
        /// Parameters cho SyncReportIntegratedCampaign
        /// </summary>
        public class SyncReportIntegratedCampaignParameters:DefaultParameters
        {
            //public string BcId { get; set; }
            //public DateTime SyncDate { get; set; }
        }
    }
}