/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Auth;
using TikTokBusinessApi.Core.Resilience;

namespace TikTokBusinessApi.Core
{
    /// <summary>
    /// Interface for TikTok Business API client
    /// </summary>
    public interface IApiClient : IDisposable
    {
        /// <summary>
        /// Base path for API requests
        /// </summary>
        string BasePath { get; set; }

        /// <summary>
        /// API version (e.g., "v1.3")
        /// </summary>
        string ApiVersion { get; set; }

        /// <summary>
        /// API prefix (e.g., "open_api")
        /// </summary>
        string ApiPrefix { get; set; }

        /// <summary>
        /// Maximum number of retry attempts for failed requests
        /// </summary>
        int MaxRetryAttempts { get; set; }

        /// <summary>
        /// Delay between retry attempts in milliseconds
        /// </summary>
        int RetryDelayMs { get; set; }

        /// <summary>
        /// Whether to enable detailed request/response logging
        /// </summary>
        bool EnableDetailedLogging { get; set; }

        /// <summary>
        /// Default headers to be sent with every request
        /// </summary>
        Dictionary<string, string> DefaultHeaders { get; }

        /// <summary>
        /// Timeout for HTTP requests
        /// </summary>
        TimeSpan Timeout { get; set; }

        /// <summary>
        /// Retry policy for handling failed requests
        /// </summary>
        IRetryPolicy RetryPolicy { get; set; }

        /// <summary>
        /// Rate limiter for controlling request frequency
        /// </summary>
        IRateLimiter RateLimiter { get; set; }

        /// <summary>
        /// Circuit breaker for handling service failures
        /// </summary>
        ICircuitBreaker CircuitBreaker { get; set; }

        /// <summary>
        /// Request throttler for managing request load
        /// </summary>
        IRequestThrottler RequestThrottler { get; set; }

        /// <summary>
        /// Registers an authentication factory for a specific type
        /// </summary>
        /// <param name="authenticationType">Authentication type name</param>
        /// <param name="factory">Authentication factory</param>
        void RegisterAuthenticationFactory(string authenticationType, IAuthenticationFactory factory);

        /// <summary>
        /// Gets an authentication factory by type
        /// </summary>
        /// <param name="authenticationType">Authentication type name</param>
        /// <returns>Authentication factory or null if not found</returns>
        IAuthenticationFactory? GetAuthenticationFactory(string authenticationType);

        /// <summary>
        /// Creates an authentication instance using the appropriate factory
        /// </summary>
        /// <param name="context">Authentication context</param>
        /// <returns>Authentication instance or null if not found</returns>
        Task<IAuthentication?> CreateAuthenticationAsync(AuthenticationContext context);

        /// <summary>
        /// Gets the TikTok authentication context for managing access tokens
        /// </summary>
        /// <returns>TikTok authentication context or null if not configured</returns>
        IAuthenticationContext? GetTikTokAuthenticationContext();

        /// <summary>
        /// Sets the access token for TikTok authentication
        /// </summary>
        /// <param name="accessToken">Access token to set</param>
        /// <returns>Task that completes when the token is set</returns>
        Task SetAccessTokenAsync(string? accessToken);

        /// <summary>
        /// Gets the current access token from TikTok authentication
        /// </summary>
        /// <returns>Current access token or null if not available</returns>
        Task<string?> GetAccessTokenAsync();

        /// <summary>
        /// Makes an API call and returns the business model directly
        /// </summary>
        /// <typeparam name="T">Business model type</typeparam>
        /// <param name="path">API path</param>
        /// <param name="method">HTTP method</param>
        /// <param name="queryParams">Query parameters</param>
        /// <param name="body">Request body</param>
        /// <param name="headerParams">Header parameters</param>
        /// <param name="authNames">Authentication names to apply</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Business model data</returns>
        Task<T> CallApiAsync<T>(
            string path,
            HttpMethod method,
            Dictionary<string, string>? queryParams = null,
            object? body = null,
            Dictionary<string, string>? headerParams = null,
            string[]? authNames = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Call API with authentication context
        /// </summary>
        /// <typeparam name="T">Response type</typeparam>
        /// <param name="path">API path</param>
        /// <param name="method">HTTP method</param>
        /// <param name="queryParams">Query parameters</param>
        /// <param name="body">Request body</param>
        /// <param name="headerParams">Header parameters</param>
        /// <param name="authenticationContext">Authentication context</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Business model data</returns>
        Task<T> CallApiAsync<T>(
            string path,
            HttpMethod method,
            Dictionary<string, string>? queryParams = null,
            object? body = null,
            Dictionary<string, string>? headerParams = null,
            AuthenticationContext? authenticationContext = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Call API and return raw byte array (for file downloads)
        /// </summary>
        /// <param name="path">API path</param>
        /// <param name="method">HTTP method</param>
        /// <param name="queryParams">Query parameters</param>
        /// <param name="body">Request body</param>
        /// <param name="headerParams">Header parameters</param>
        /// <param name="authenticationContext">Authentication context</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Raw byte array response</returns>
        Task<byte[]> CallApiRawAsync(
            string path,
            HttpMethod method,
            Dictionary<string, string>? queryParams = null,
            object? body = null,
            Dictionary<string, string>? headerParams = null,
            AuthenticationContext? authenticationContext = null,
            CancellationToken cancellationToken = default);
    }
}
