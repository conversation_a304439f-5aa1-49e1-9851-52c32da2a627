using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service interface cho việc đồng bộ dữ liệu tài sản (Assets)
    /// </summary>
    public interface IAssetSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ tài sản theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AssetSyncResult> SyncAssetsAsync(string bcId);

        /// <summary>
        /// Đồng bộ tài sản theo loại tài sản và BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="assetType">Loại tài sản</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AssetSyncResult> SyncAssetsByTypeAsync(string bcId, string assetType);

        /// <summary>
        /// Đồng bộ tất cả tài sản cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AssetSyncResult> SyncAllAssetsForAllBcsAsync();
    }
}