using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace TikTok.Cache
{
    /// <summary>
    /// Service monitor cache để quản lý tất cả cache services
    /// </summary>
    public class CacheMonitorService : ICacheMonitorService, IScopedDependency
    {
        private readonly IEnumerable<ICacheService> _cacheServices;
        private readonly ILogger<CacheMonitorService> _logger;

        public CacheMonitorService(
            IEnumerable<ICacheService> cacheServices,
            ILogger<CacheMonitorService> logger)
        {
            _cacheServices = cacheServices;
            _logger = logger;
        }

        /// <summary>
        /// Lấy danh sách tất cả cache services
        /// </summary>
        /// <returns>Danh sách cache services</returns>
        public async Task<List<CacheInfoDto>> GetAllCacheInfoAsync()
        {
            var cacheInfos = new List<CacheInfoDto>();

            foreach (var cacheService in _cacheServices)
            {
                try
                {
                    var cacheInfo = await cacheService.GetCacheInfoAsync();
                    cacheInfos.Add(cacheInfo);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to get cache info for {CacheName}", cacheService.CacheName);
                    
                    // Thêm thông tin lỗi
                    cacheInfos.Add(new CacheInfoDto
                    {
                        CacheName = cacheService.CacheName,
                        Status = "Error",
                        Description = $"Error: {ex.Message}"
                    });
                }
            }

            return cacheInfos.OrderBy(x => x.CacheName).ToList();
        }

        /// <summary>
        /// Lấy thông tin cache theo tên
        /// </summary>
        /// <param name="cacheName">Tên cache</param>
        /// <returns>Thông tin cache</returns>
        public async Task<CacheInfoDto?> GetCacheInfoByNameAsync(string cacheName)
        {
            var cacheService = _cacheServices.FirstOrDefault(x => x.CacheName.Equals(cacheName, StringComparison.OrdinalIgnoreCase));
            
            if (cacheService == null)
            {
                _logger.LogWarning("Cache service not found: {CacheName}", cacheName);
                return null;
            }

            try
            {
                return await cacheService.GetCacheInfoAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get cache info for {CacheName}", cacheName);
                return new CacheInfoDto
                {
                    CacheName = cacheName,
                    Status = "Error",
                    Description = $"Error: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Clear tất cả cache
        /// </summary>
        /// <returns>Kết quả clear cache</returns>
        public async Task<ClearCacheResultDto> ClearAllCacheAsync()
        {
            var results = new List<CacheClearResultDto>();

            foreach (var cacheService in _cacheServices)
            {
                try
                {
                    var success = await cacheService.ClearCacheAsync();
                    results.Add(new CacheClearResultDto
                    {
                        CacheName = cacheService.CacheName,
                        Success = success,
                        Message = success ? "Cleared successfully" : "Failed to clear"
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to clear cache for {CacheName}", cacheService.CacheName);
                    results.Add(new CacheClearResultDto
                    {
                        CacheName = cacheService.CacheName,
                        Success = false,
                        Message = $"Error: {ex.Message}"
                    });
                }
            }

            return new ClearCacheResultDto
            {
                TotalCaches = results.Count,
                SuccessCount = results.Count(x => x.Success),
                FailedCount = results.Count(x => !x.Success),
                Results = results
            };
        }

        /// <summary>
        /// Clear cache theo tên
        /// </summary>
        /// <param name="cacheName">Tên cache</param>
        /// <param name="cacheKey">Cache key cụ thể (optional)</param>
        /// <returns>Kết quả clear cache</returns>
        public async Task<CacheClearResultDto> ClearCacheByNameAsync(string cacheName, string? cacheKey = null)
        {
            var cacheService = _cacheServices.FirstOrDefault(x => x.CacheName.Equals(cacheName, StringComparison.OrdinalIgnoreCase));
            
            if (cacheService == null)
            {
                _logger.LogWarning("Cache service not found: {CacheName}", cacheName);
                return new CacheClearResultDto
                {
                    CacheName = cacheName,
                    Success = false,
                    Message = "Cache service not found"
                };
            }

            try
            {
                bool success;
                if (!string.IsNullOrEmpty(cacheKey))
                {
                    success = await cacheService.ClearCacheByKeyAsync(cacheKey);
                }
                else
                {
                    success = await cacheService.ClearCacheAsync();
                }

                return new CacheClearResultDto
                {
                    CacheName = cacheName,
                    CacheKey = cacheKey,
                    Success = success,
                    Message = success ? "Cleared successfully" : "Failed to clear"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear cache for {CacheName}", cacheName);
                return new CacheClearResultDto
                {
                    CacheName = cacheName,
                    CacheKey = cacheKey,
                    Success = false,
                    Message = $"Error: {ex.Message}"
                };
            }
        }
    }
}
