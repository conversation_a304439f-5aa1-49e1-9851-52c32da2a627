/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Showcase endpoints
    /// </summary>
    public static class ShowcaseEndpoints
    {
        /// <summary>
        /// Get identities with Showcase permission under an ad account endpoint (v1.3)
        /// </summary>
        public const string GetIdentities = "/showcase/identity/get/";

        /// <summary>
        /// Get the available regions for a Showcase via identity endpoint (v1.3)
        /// </summary>
        public const string GetRegions = "/showcase/region/get/";

        /// <summary>
        /// Get the available products in a Showcase endpoint (v1.3)
        /// </summary>
        public const string GetProducts = "/showcase/product/get/";
    }
}
