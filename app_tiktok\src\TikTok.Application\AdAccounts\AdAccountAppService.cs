using Elsa;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Permissions;
using TikTok.Repositories;
using TikTok.ResourceProviders;
using TikTok.ResourceProviders.AdAccounts;
using TikTok.Users;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Users;

namespace TikTok.AdAccounts
{
    /// <summary>
    /// Service implementation cho tài khoản quảng cáo
    /// </summary>
    [Authorize]
    public class AdAccountAppService :
        CrudAppService<
            RawAdAccountEntity,
            AdAccountDto,
            Guid,
            GetAdAccountListDto,
            CreateAdAccountDto,
            UpdateAdAccountDto>,
        IAdAccountAppService
    {
        private readonly IAdAccountRepository _adAccountRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IdentityUserManager _userManager;
        private readonly IdentityRoleManager _roleManager;
        private readonly IPermissionChecker _permissionChecker;
        private readonly IAdAccountResourceProvider _resourceProvider;
        private readonly ICustomerAdAccountRepository _customerAdAccountRepository;
        private readonly IRepository<CustomerEntity,Guid> _customerRepository;
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="repository">Repository</param>
        /// <param name="adAccountRepository">Ad Account Repository</param>
        public AdAccountAppService(
            IRepository<RawAdAccountEntity, Guid> repository,
            IAdAccountRepository adAccountRepository,
            ICurrentUser currentUser,
            IdentityUserManager userManager,
            IdentityRoleManager roleManager,
            IPermissionChecker permissionChecker,
            IAdAccountResourceProvider resourceProvider,
            ICustomerAdAccountRepository customerAdAccountRepository,
            IRepository<CustomerEntity, Guid> customerRepository) : base(repository)
        {
            _adAccountRepository = adAccountRepository;
            _currentUser = currentUser;
            _userManager = userManager;
            _roleManager = roleManager;
            _permissionChecker = permissionChecker;
            _resourceProvider = resourceProvider;
            _customerAdAccountRepository = customerAdAccountRepository;
            _customerRepository = customerRepository;
        }

        /// <summary>
        /// Lấy tài khoản quảng cáo theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của tài khoản quảng cáo</param>
        /// <returns>Tài khoản quảng cáo</returns>
        public async Task<AdAccountDto> GetByAdvertiserIdAsync(string advertiserId)
        {
            var entity = await _adAccountRepository.GetByAdvertiserIdAsync(advertiserId);
            return ObjectMapper.Map<RawAdAccountEntity, AdAccountDto>(entity);
        }

        /// <summary>
        /// Lấy danh sách tài khoản quảng cáo theo Business Center ID
        /// </summary>
        /// <param name="ownerBcId">ID của Business Center</param>
        /// <returns>Danh sách tài khoản quảng cáo</returns>
        public async Task<PagedResultDto<AdAccountDto>> GetByOwnerBcIdAsync(string ownerBcId)
        {
            var entities = await _adAccountRepository.GetByOwnerBcIdAsync(ownerBcId);
            var dtos = ObjectMapper.Map<RawAdAccountEntity[], AdAccountDto[]>(entities.ToArray());

            return new PagedResultDto<AdAccountDto>
            {
                TotalCount = entities.Count,
                Items = dtos
            };
        }

        /// <summary>
        /// Ghi đè phương thức GetListAsync để sử dụng repository
        /// </summary>
        /// <param name="input">Input để lấy danh sách</param>
        /// <returns>Danh sách tài khoản quảng cáo</returns>
        public override async Task<PagedResultDto<AdAccountDto>> GetListAsync(GetAdAccountListDto input)
        {
            var isSupervisor = await _permissionChecker.IsGrantedAsync(TikTokPermissions.AdAccounts.Supervise);

            var resources = await _resourceProvider.GetListAsync(new GetResourcePermissionAssignedUserDto()
            {
                UserId = _currentUser.GetId()
            });
            List<string>? customerIds = null;

            if (!input.CustomerFilterText.IsNullOrEmpty())
            {
                customerIds = (await _customerRepository.GetListAsync(c => c.CustomerName.Contains(input.CustomerFilterText) || c.CustomerId.Contains(input.CustomerFilterText)))
                    .Select(c => c.CustomerId).ToList();
            }

            var adAccountIds = isSupervisor ? null : resources.Select(x => x.ResourceId).Distinct().ToList();

            // Sử dụng repository để lấy danh sách và tổng số
            var entities = await _adAccountRepository.GetListAsync(
                sorting: input.Sorting,
                maxResultCount: input.MaxResultCount,
                skipCount: input.SkipCount,
                filter: input.FilterText,
                advertiserId: input.AdvertiserId,
                ownerBcId: input.OwnerBcId,
                name: input.Name,
                company: input.Company,
                status: input.Status,
                statuses: input.Statuses,
                role: input.Role,
                industry: input.Industry,
                address: input.Address,
                country: input.Country,
                advertiserAccountType: input.AdvertiserAccountType,
                currency: input.Currency,
                language: input.Language,
                licenseNo: input.LicenseNo,
                createTimeFrom: input.CreateTimeFrom,
                createTimeTo: input.CreateTimeTo,
                balanceFrom: input.BalanceFrom,
                balanceTo: input.BalanceTo,
                isRemoved: false, // Chỉ lấy các AdAccount chưa bị xóa
                adAccountIds: adAccountIds,
                customerIds: customerIds);

            var totalCount = await _adAccountRepository.GetCountAsync(
                filter: input.FilterText,
                advertiserId: input.AdvertiserId,
                ownerBcId: input.OwnerBcId,
                name: input.Name,
                company: input.Company,
                status: input.Status,
                statuses: input.Statuses,
                role: input.Role,
                industry: input.Industry,
                address: input.Address,
                country: input.Country,
                advertiserAccountType: input.AdvertiserAccountType,
                currency: input.Currency,
                language: input.Language,
                licenseNo: input.LicenseNo,
                createTimeFrom: input.CreateTimeFrom,
                createTimeTo: input.CreateTimeTo,
                balanceFrom: input.BalanceFrom,
                balanceTo: input.BalanceTo,
                isRemoved: false,
                adAccountIds: adAccountIds,
                customerIds: customerIds); // Chỉ đếm các AdAccount chưa bị xóa


            // Lấy thông tin khách hàng của Tài khoản quảng cáo
            // Customer Join CustomerAdAccount to get all customers for returned ad accounts
            var advertiserIds = entities.Select(e => e.AdvertiserId)
                .Where(id => !string.IsNullOrEmpty(id))
                .Distinct()
                .ToList();

            var customerAdAccounts = advertiserIds.Count == 0
                ? new List<CustomerAdAccountEntity>()
                : await _customerAdAccountRepository.GetListWithCustomerInfoByAdvertiserIdsAsync(advertiserIds);

            var advertiserIdToCustomer = customerAdAccounts
                .Where(ca => ca.AdvertiserId != null && ca.Customer != null)
                .GroupBy(ca => ca.AdvertiserId!)
                .ToDictionary(g => g.Key, g => g.First().Customer);

            var dtos = ObjectMapper.Map<RawAdAccountEntity[], AdAccountDto[]>(entities.ToArray());

            foreach (var dto in dtos)
            {
                if (!string.IsNullOrEmpty(dto.AdvertiserId) && advertiserIdToCustomer.TryGetValue(dto.AdvertiserId, out var customer))
                {
                    dto.CustomerName = customer.CustomerName;
                    dto.CustomerId = customer.CustomerId;
                }
            }

            Parallel.ForEach(dtos, (dto) =>
            {
                dto.Permissions = resources.FirstOrDefault(x => x.ResourceId == dto.AdvertiserId)?.Permissions??new();
            });
            return new PagedResultDto<AdAccountDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }

        /// <summary>
        /// Ghi đè phương thức CreateAsync để thêm validation
        /// </summary>
        /// <param name="input">Input để tạo mới</param>
        /// <returns>Tài khoản quảng cáo đã tạo</returns>
        public override async Task<AdAccountDto> CreateAsync(CreateAdAccountDto input)
        {
            // Kiểm tra xem Advertiser ID đã tồn tại chưa
            if (await _adAccountRepository.IsAdvertiserIdExistsAsync(input.AdvertiserId))
            {
                throw new UserFriendlyException($"Advertiser ID '{input.AdvertiserId}' đã tồn tại.");
            }

            return await base.CreateAsync(input);
        }

        /// <summary>
        /// Gán người hỗ trợ và tài khoản quảng cáo
        /// </summary>
        /// <param name="advertiserId"></param>
        /// <param name="supporterId"></param>
        /// <param name="role"></param>
        /// <returns></returns>
        public async Task AssignSupporterAsync(string advertiserId, Guid supporterId, List<string> permissions)
        {
            await _resourceProvider.AssignPermission(new List<AssignPermissionForResourceDto>
            {
                new AssignPermissionForResourceDto
                {
                    ResourceId = advertiserId,
                    UserId = supporterId,
                    Permissions = permissions
                }
            });
        }

        public async Task RemoveSupporterAsync(string advertiserId, Guid supporterId)
        {
            // Remove all permissions by updating to empty permissions list
            await _resourceProvider.RemovePermission(new List<AssignPermissionForResourceDto>
            {
                new AssignPermissionForResourceDto
                {
                    ResourceId = advertiserId,
                    UserId = supporterId,
                }
            });
        }

        /// <summary>
        /// Update supporter configuration for an ad account
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="assignments">List of supporter assignments</param>
        /// <returns></returns>
        public async Task UpdateSupporterConfigurationAsync(string advertiserId, List<SupporterAssignmentDto> assignments)
        {
            var existingAssignments = await _resourceProvider.GetListAsync(new GetResourcePermissionAssignedUserDto()
            {
                ResourceId = advertiserId,
            });
            var deleteAssignments = existingAssignments.Where(x => !assignments.Any(a => a.SupporterId == x.UserId)).ToList();
            if (deleteAssignments.Any())
            {
                await _resourceProvider.RemovePermission(deleteAssignments);
            }

            var updateAssignments = assignments;

            var validPermissions = new List<string>() { 
                TikTokPermissions.AdAccounts.Default,
                TikTokPermissions.AdAccounts.Edit,
                TikTokPermissions.AdAccounts.Delete
            };

            // Convert assignments to UpdatePermission format
            var updateRequests = assignments.Select(assignment => new AssignPermissionForResourceDto
            {
                UserId = assignment.SupporterId,
                ResourceId = advertiserId,
                Permissions = assignment.Permissions ?? new List<string>()
            }).ToList();
            if (updateRequests.Any())
                await _resourceProvider.AssignPermission(updateRequests);
        }

        /// <summary>
        /// Get supporter configuration for an ad account
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>Supporter configuration</returns>
        public async Task<SupporterConfigurationDto> GetSupporterConfigurationAsync(string advertiserId)
        {
            // Get all users with supporter role
            var supporterUsers = await GetSupporterUsersAsync();

            // Get current assignments for this ad account
            var currentAssignments = await _resourceProvider.GetListAsync(new GetResourcePermissionAssignedUserDto()
            {
                ResourceId = advertiserId,
            });

            var configuration = new SupporterConfigurationDto
            {
                AdvertiserId = advertiserId,
                AvailableSupporters = new List<SupporterUserDto>(),
                AssignedSupporters = new List<SupporterAssignmentDto>()
            };

            // Process each supporter user
            foreach (var user in supporterUsers)
            {
                var assignment = currentAssignments.FirstOrDefault(x => x.UserId == user.Id);

                if (assignment != null && assignment.Permissions != null && assignment.Permissions.Any())
                {
                    // User is assigned with permissions
                    configuration.AssignedSupporters.Add(new SupporterAssignmentDto
                    {
                        SupporterId = user.Id,
                        UserName = user.UserName,
                        Name = user.Name,
                        Email = user.Email,
                        PhoneNumber = user.PhoneNumber,
                        Permissions = assignment.Permissions
                    });
                }
                else
                {
                    // User is available (not assigned or no permissions)
                    configuration.AvailableSupporters.Add(new SupporterUserDto
                    {
                        Id = user.Id,
                        UserName = user.UserName,
                        Name = user.Name,
                        Email = user.Email,
                        PhoneNumber = user.PhoneNumber,
                        IsAssigned = false
                    });
                }
            }

            return configuration;
        }

        /// <summary>
        /// Get all users with supporter role
        /// </summary>
        /// <returns>List of supporter users</returns>
        private async Task<List<IdentityUser>> GetSupporterUsersAsync()
        {
            const string supporterRoleName = "supporter";

            // Get the supporter role
            var supporterRole = await _roleManager.FindByNameAsync(supporterRoleName);
            if (supporterRole == null)
            {
                // If the role doesn't exist, return empty list
                return new List<IdentityUser>();
            }

            // Get all users in the supporter role
            var usersInRole = await _userManager.GetUsersInRoleAsync(supporterRoleName);

            // Filter only active users
            var activeUsers = usersInRole.Where(u => u.IsActive).ToList();

            return activeUsers;
        }
    }
}