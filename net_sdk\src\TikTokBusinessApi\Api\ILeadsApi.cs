/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Leads operations
    /// </summary>
    public interface ILeadsApi
    {
        /// <summary>
        /// Create a test lead for end-to-end testing for lead generation
        /// </summary>
        /// <param name="request">Request containing test lead parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created test lead data</returns>
        // Task<CreateTestLeadResponse> CreateTestLeadAsync(
        //     CreateTestLeadRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get a test lead
        /// </summary>
        /// <param name="request">Request containing test lead parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the test lead data</returns>
        Task<GetTestLeadResponse> GetTestLeadAsync(
            GetTestLeadRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete an existing test lead
        /// </summary>
        /// <param name="request">Request containing test lead deletion parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the asynchronous operation</returns>
        // Task DeleteTestLeadAsync(
        //     DeleteTestLeadRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Create a download task for all the leads for the page_id or ad_id passed in the request
        /// </summary>
        /// <param name="request">Request containing lead download task parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the download task information</returns>
        // Task<CreateLeadDownloadTaskResponse> CreateLeadDownloadTaskAsync(
        //     CreateLeadDownloadTaskRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Download leads. You need to create a download task first and ensure the task is complete
        /// </summary>
        /// <param name="request">Request containing lead download parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Raw CSV or ZIP file content as byte array</returns>
        Task<byte[]> DownloadLeadsAsync(
            DownloadLeadsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get form libraries that you have access to
        /// </summary>
        /// <param name="request">Request containing pagination parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing form libraries</returns>
        Task<GetFormLibrariesResponse> GetFormLibrariesAsync(
            GetFormLibrariesRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Migrate leads from an ad account to a Business Center
        /// </summary>
        /// <param name="request">Request containing migration parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created form library ID</returns>
        // Task<MigrateLeadsToBCResponse> MigrateLeadsToBCAsync(
        //     MigrateLeadsToBCRequest request,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the fields of an Instant Form (or lead form)
        /// </summary>
        /// <param name="request">Request containing Instant Form parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the Instant Form fields</returns>
        Task<GetInstantFormFieldsResponse> GetInstantFormFieldsAsync(
            GetInstantFormFieldsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get fields of an Instant Form or direct message leads
        /// </summary>
        /// <param name="request">Request containing lead field parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the lead fields</returns>
        Task<GetLeadFieldsResponse> GetLeadFieldsAsync(
            GetLeadFieldsRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get an Instant Form lead or a direct message lead
        /// </summary>
        /// <param name="request">Request containing lead parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the lead data</returns>
        Task<GetLeadResponse> GetLeadAsync(
            GetLeadRequest request,
            CancellationToken cancellationToken = default);
    }
}
