using System;

namespace TikTok.FactGmvMaxProducts.Dtos
{
    public class FactGmvMaxProductSummaryDto
    {
        // ========== Date Range ==========
        
        /// <summary>
        /// Ngày bắt đầu
        /// </summary>
        public DateTime FromDate { get; set; }

        /// <summary>
        /// Ngày kết thúc
        /// </summary>
        public DateTime ToDate { get; set; }

        /// <summary>
        /// Tổng số bản ghi
        /// </summary>
        public int TotalRecords { get; set; }

        // ========== Financial Metrics (USD) ==========

        /// <summary>
        /// Tổng chi phí quảng cáo (USD)
        /// </summary>
        public decimal TotalCostUSD { get; set; }

        /// <summary>
        /// Tổng doanh thu (USD)
        /// </summary>
        public decimal TotalGrossRevenueUSD { get; set; }

        /// <summary>
        /// Tổng doanh thu quảng cáo (USD)
        /// </summary>
        public decimal TotalAdsRevenueUSD { get; set; }

        /// <summary>
        /// Tổng doanh thu tự nhiên (USD)
        /// </summary>
        public decimal TotalOrganicRevenueUSD { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi đơn hàng (USD)
        /// </summary>
        public decimal AvgCostPerOrderUSD { get; set; }

        /// <summary>
        /// Giá sản phẩm trung bình (USD)
        /// </summary>
        public decimal AverageProductPriceUSD { get; set; }

        // ========== Performance Metrics ==========

        /// <summary>
        /// ROAS trung bình
        /// </summary>
        public decimal AverageROAS { get; set; }

        /// <summary>
        /// ACOS trung bình (%)
        /// </summary>
        public decimal AverageACOS { get; set; }

        /// <summary>
        /// ROI trung bình (%)
        /// </summary>
        public decimal AverageROI { get; set; }

        /// <summary>
        /// TACOS trung bình (%)
        /// </summary>
        public decimal AverageTACOS { get; set; }

        /// <summary>
        /// CTR trung bình (%)
        /// </summary>
        public decimal AverageCTR { get; set; }

        /// <summary>
        /// CPM trung bình (USD)
        /// </summary>
        public decimal AverageCPM { get; set; }

        /// <summary>
        /// CPC trung bình (USD)
        /// </summary>
        public decimal AverageCPC { get; set; }

        /// <summary>
        /// Tỷ lệ chuyển đổi trung bình (%)
        /// </summary>
        public decimal AverageConversionRate { get; set; }

        // ========== Volume Metrics ==========

        /// <summary>
        /// Tổng số đơn hàng
        /// </summary>
        public int TotalOrders { get; set; }

        /// <summary>
        /// Tổng số lượng sản phẩm bán được
        /// </summary>
        public int TotalQuantitySold { get; set; }

        /// <summary>
        /// Tổng lượt hiển thị
        /// </summary>
        public long TotalImpressions { get; set; }

        /// <summary>
        /// Tổng lượt nhấp
        /// </summary>
        public long TotalClicks { get; set; }

        // ========== Product Metrics ==========

        /// <summary>
        /// Số sản phẩm duy nhất
        /// </summary>
        public int UniqueProducts { get; set; }

        /// <summary>
        /// Số chiến dịch duy nhất
        /// </summary>
        public int UniqueCampaigns { get; set; }

        /// <summary>
        /// Số store duy nhất
        /// </summary>
        public int UniqueStores { get; set; }

        /// <summary>
        /// Số tài khoản quảng cáo duy nhất
        /// </summary>
        public int UniqueAdAccounts { get; set; }

        /// <summary>
        /// Số danh mục sản phẩm duy nhất
        /// </summary>
        public int UniqueCategories { get; set; }

        /// <summary>
        /// Số ngày có dữ liệu
        /// </summary>
        public int UniqueDates { get; set; }

        // ========== Performance Classification ==========

        /// <summary>
        /// Số sản phẩm có ROAS > 3.0 (Xuất sắc)
        /// </summary>
        public int ExcellentROASProducts { get; set; }

        /// <summary>
        /// Số sản phẩm có ROAS 2.0-3.0 (Tốt)
        /// </summary>
        public int GoodROASProducts { get; set; }

        /// <summary>
        /// Số sản phẩm có ROAS < 2.0 (Cần cải thiện)
        /// </summary>
        public int PoorROASProducts { get; set; }

        /// <summary>
        /// Số sản phẩm có ACOS > 50% (Chi phí cao)
        /// </summary>
        public int HighACOSProducts { get; set; }

        /// <summary>
        /// Số sản phẩm bán chạy (top 20% theo số lượng bán)
        /// </summary>
        public int BestSellingProducts { get; set; }

        /// <summary>
        /// Số sản phẩm có lợi nhuận cao (TACOS < 20%)
        /// </summary>
        public int HighProfitProducts { get; set; }

        // ========== Additional Product Metrics ==========

        /// <summary>
        /// Sản phẩm có doanh thu cao nhất
        /// </summary>
        public string TopRevenueProductId { get; set; }

        /// <summary>
        /// Sản phẩm có ROAS cao nhất
        /// </summary>
        public string TopROASProductId { get; set; }

        /// <summary>
        /// Sản phẩm bán chạy nhất
        /// </summary>
        public string BestSellingProductId { get; set; }

        /// <summary>
        /// Danh mục sản phẩm hiệu suất tốt nhất
        /// </summary>
        public string TopPerformingCategory { get; set; }
    }
}
