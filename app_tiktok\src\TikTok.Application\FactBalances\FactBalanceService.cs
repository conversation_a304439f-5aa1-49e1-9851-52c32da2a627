﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Entities;
using Volo.Abp.Domain.Repositories;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Services;
using TikTok.FactBalances.Dtos;
using TikTok.DimDates;
using TikTok.DimBusinessCenters;
using TikTok.DimAdAccounts;
using TikTok.Domain.Repositories;

namespace TikTok.FactBalances
{
    public class FactBalanceService : ApplicationService, IFactBalanceService
    {
        private readonly IRepository<FactBalanceEntity, Guid> _factBalanceRepository;
        private readonly IRepository<DimDateEntity, int> _dimDateRepository;
        private readonly IRepository<DimBusinessCenterEntity, Guid> _dimBusinessCenterRepository;
        private readonly IRepository<DimAdAccountEntity, Guid> _dimAdAccountRepository;
        private readonly IFactBalanceDapperRepository _factBalanceDapperRepository;
        public FactBalanceService(
            IRepository<FactBalanceEntity, Guid> factBalanceRepository,
            IRepository<DimDateEntity, int> dimDateRepository,
            IRepository<DimBusinessCenterEntity, Guid> dimBusinessCenterRepository,
            IRepository<DimAdAccountEntity, Guid> dimAdAccountRepository,
            IFactBalanceDapperRepository factBalanceDapperRepository)
        {
            _factBalanceRepository = factBalanceRepository;
            _dimDateRepository = dimDateRepository;
            _dimBusinessCenterRepository = dimBusinessCenterRepository;
            _dimAdAccountRepository = dimAdAccountRepository;
            _factBalanceDapperRepository = factBalanceDapperRepository;
        }

        public async Task<FactBalanceDataResponse> GetFactBalanceReport(DateTime from, DateTime to)
        {
            try
            {
                var result = new FactBalanceDataResponse()
                {
                    From = from,
                    To = to,
                    FactBalances = [],
                    DimAdAccounts = [],
                    DimBusinessCenters = [],
                    DimDates = []
                };

                // Get all fact balances and filter in memory for the specified date range
                var factBalances = await _factBalanceRepository.GetListAsync(ba => ba.Date >= from.Date && ba.Date <= to.Date);

                if (!factBalances.Any())
                {
                    // Return empty DTO if no data found
                    return result;
                }

                result.FactBalances = ObjectMapper.Map<List<FactBalanceEntity>, List<FactBalanceDto>>(factBalances);

                // Get all unique DimDateId values from factBalances
                var uniqueDimDateIds = factBalances.Select(fb => fb.DimDateId).Distinct().ToList();

                // Get all dim-dates that are included in factBalances data
                if (uniqueDimDateIds.Any())
                {
                    var dimDates = await _dimDateRepository.GetListAsync(dd => uniqueDimDateIds.Contains(dd.Id));
                    result.DimDates = ObjectMapper.Map<List<DimDateEntity>, List<DimDateDto>>(dimDates);
                }

                // Get all unique DimBusinessCenterId values from factBalances
                var uniqueDimBusinessCenterIds = factBalances.Select(fb => fb.DimBusinessCenterId).Distinct().ToList();

                // Get all dim-businesscenters that are included in factBalances data
                if (uniqueDimBusinessCenterIds.Any())
                {
                    var dimBusinessCenters = await _dimBusinessCenterRepository.GetListAsync(dbc => uniqueDimBusinessCenterIds.Contains(dbc.Id));
                    result.DimBusinessCenters = ObjectMapper.Map<List<DimBusinessCenterEntity>, List<DimBusinessCenterDto>>(dimBusinessCenters);
                }

                // Get all unique DimAdAccountId values from factBalances (excluding null values)
                var uniqueDimAdAccountIds = factBalances
                    .Where(fb => fb.DimAdAccountId.HasValue)
                    .Select(fb => fb.DimAdAccountId!.Value)
                    .Distinct()
                    .ToList();

                // Get all dim-adaccounts that are included in factBalances data
                if (uniqueDimAdAccountIds.Any())
                {
                    var dimAdAccounts = await _dimAdAccountRepository.GetListAsync(daa => uniqueDimAdAccountIds.Contains(daa.Id));
                    result.DimAdAccounts = ObjectMapper.Map<List<DimAdAccountEntity>, List<DimAdAccountDto>>(dimAdAccounts);
                }

                return result;
            }
            catch (Exception ex)
            {
                // Log the exception (you might want to inject ILogger here)
                throw new ValidationException($"Error retrieving fact balance data: {ex.Message}");
            }
        }

        public async Task<FactBalanceSummary> GetFactBalanceSummary()
        {
            var result = await _factBalanceDapperRepository.GetSummaryAsync();
            return result;
        }
    }
}