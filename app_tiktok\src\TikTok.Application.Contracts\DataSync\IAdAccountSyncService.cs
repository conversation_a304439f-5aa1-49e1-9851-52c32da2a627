using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu tài khoản quảng cáo
    /// </summary>
    public interface IAdAccountSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ tài khoản quảng cáo theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AdAccountSyncResult> SyncAdAccountsAsync(string bcId);

        /// <summary>
        /// Đồng bộ tài khoản quảng cáo theo Advertiser ID
        /// </summary>
        /// <param name="advertiserId">ID của tài khoản quảng cáo</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AdAccountSyncResult> SyncAdAccountAsync(string advertiserId);

        /// <summary>
        /// Đồng bộ tất cả tài khoản quảng cáo cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AdAccountSyncResult> SyncAllAdAccountsForAllBcsAsync();

        /// <summary>
        /// Cập nhật danh sách tài khoản quảng cáo từ tài sản theo BcId
        /// - Lấy danh sách tài sản hiện tại từ BC trong Db
        /// - Thêm sửa xóa vào danh sách tài khoản quảng cáo
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AdAccountSyncResult> UpdateAdAccountsFromAssetsAsync(string bcId);
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu tài khoản quảng cáo
    /// </summary>
    public class AdAccountSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số AdAccount đã đồng bộ
        /// </summary>
        public int AdAccountCount { get; set; }
    }
}