/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Auth;
using TikTokBusinessApi.Core.Resilience;

namespace TikTokBusinessApi.Core
{
    /// <summary>
    /// Configuration for TikTok Business API client
    /// </summary>
    public class Configuration
    {
        private static IApiClient? _defaultApiClient;

        /// <summary>
        /// Gets or sets the default API client
        /// </summary>
        public static IApiClient DefaultApiClient
        {
            get => _defaultApiClient ??= new ApiClient();
            set => _defaultApiClient = value;
        }

        /// <summary>
        /// TikTok environment configuration
        /// </summary>
        public TikTokEnvironment Environment { get; set; } = TikTokEnvironment.Production;

        /// <summary>
        /// Base URL for the TikTok Business API (deprecated - use Environment.BaseUrl instead)
        /// </summary>
        [Obsolete("Use Environment.BaseUrl instead")]
        public string BasePath
        {
            get => Environment.BaseUrl;
            set => Environment = TikTokEnvironment.Custom("Custom", value);
        }

        /// <summary>
        /// API version (deprecated - use Environment.ApiVersion instead)
        /// </summary>
        [Obsolete("Use Environment.ApiVersion instead")]
        public string ApiVersion
        {
            get => Environment.ApiVersion;
            set => Environment.ApiVersion = value;
        }

        /// <summary>
        /// API prefix (deprecated - use Environment.ApiPrefix instead)
        /// </summary>
        [Obsolete("Use Environment.ApiPrefix instead")]
        public string ApiPrefix
        {
            get => Environment.ApiPrefix;
            set => Environment.ApiPrefix = value;
        }

        /// <summary>
        /// Request timeout in seconds
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Base URL for the TikTok Business API
        /// </summary>
        public string BaseUrl
        {
            get => Environment.BaseUrl;
            set => Environment = TikTokEnvironment.Custom("Custom", value);
        }

        /// <summary>
        /// Request timeout as TimeSpan
        /// </summary>
        public TimeSpan Timeout
        {
            get => TimeSpan.FromSeconds(TimeoutSeconds);
            set => TimeoutSeconds = (int)value.TotalSeconds;
        }

        /// <summary>
        /// Maximum number of retry attempts
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// Delay between retry attempts
        /// </summary>
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);

        /// <summary>
        /// Rate limit per second
        /// </summary>
        public int RateLimitPerSecond { get; set; } = 10;

        /// <summary>
        /// Whether caching is enabled
        /// </summary>
        public bool EnableCaching { get; set; } = false;

        /// <summary>
        /// Cache time-to-live
        /// </summary>
        public TimeSpan CacheTtl { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// Access token for API authentication
        /// </summary>
        public string? AccessToken { get; set; }

        /// <summary>
        /// Application ID for TikTok API (global for SDK instance)
        /// </summary>
        public string? AppId { get; set; }

        /// <summary>
        /// Logger factory for creating loggers
        /// </summary>
        public ILoggerFactory? LoggerFactory { get; set; }

        /// <summary>
        /// Whether to enable debug logging
        /// </summary>
        public bool EnableDebugLogging { get; set; } = false;

        /// <summary>
        /// User agent string for requests
        /// </summary>
        public string UserAgent { get; set; } = "TikTokBusinessApi-CSharp/1.0.0";

        /// <summary>
        /// Retry policy configuration
        /// </summary>
        public RetryPolicyOptions RetryPolicyOptions { get; set; } = new();

        /// <summary>
        /// Rate limiter configuration
        /// </summary>
        public RateLimiterOptions RateLimiterOptions { get; set; } = new();

        /// <summary>
        /// Circuit breaker configuration
        /// </summary>
        public CircuitBreakerOptions CircuitBreakerOptions { get; set; } = new();

        /// <summary>
        /// Request throttler configuration
        /// </summary>
        public RequestThrottlerOptions RequestThrottlerOptions { get; set; } = new();

        /// <summary>
        /// Creates a new API client with this configuration
        /// </summary>
        /// <returns>Configured API client</returns>
        public IApiClient CreateApiClient()
        {
            var logger = LoggerFactory?.CreateLogger<ApiClient>();

            // Create resilience utilities with configuration
            var retryPolicy = new DefaultRetryPolicy(RetryPolicyOptions, LoggerFactory?.CreateLogger<DefaultRetryPolicy>());
            var rateLimiter = new DefaultRateLimiter(RateLimiterOptions, LoggerFactory?.CreateLogger<DefaultRateLimiter>());
            var circuitBreaker = new DefaultCircuitBreaker(CircuitBreakerOptions, LoggerFactory?.CreateLogger<DefaultCircuitBreaker>());
            var requestThrottler = new DefaultRequestThrottler(RequestThrottlerOptions, LoggerFactory?.CreateLogger<DefaultRequestThrottler>());

            var client = new ApiClient(
                logger: logger,
                retryPolicy: retryPolicy,
                rateLimiter: rateLimiter,
                circuitBreaker: circuitBreaker,
                requestThrottler: requestThrottler)
            {
                BasePath = Environment.BaseUrl,
                ApiVersion = Environment.ApiVersion,
                ApiPrefix = Environment.ApiPrefix,
                Timeout = TimeSpan.FromSeconds(TimeoutSeconds)
            };

            // Set user agent
            client.DefaultHeaders["User-Agent"] = UserAgent;

            // Add environment-specific headers
            foreach (var header in Environment.AdditionalHeaders)
            {
                client.DefaultHeaders[header.Key] = header.Value;
            }

            // Configure TikTok authentication factory if app_id is provided
            if (!string.IsNullOrEmpty(AppId))
            {
                var resolver = new ConfigurationAuthenticationResolver(AppId, AccessToken);
                var tikTokFactory = new TikTokAuthenticationFactory(resolver);
                client.RegisterAuthenticationFactory("tiktok", tikTokFactory);
            }

            return client;
        }

        /// <summary>
        /// Creates a default configuration
        /// </summary>
        /// <returns>Default configuration</returns>
        public static Configuration CreateDefault()
        {
            return new Configuration();
        }

        /// <summary>
        /// Creates a configuration with access token
        /// </summary>
        /// <param name="accessToken">Access token</param>
        /// <returns>Configuration with access token</returns>
        [Obsolete("Use CreateWithCredentials(appId, accessToken) instead")]
        public static Configuration CreateWithAccessToken(string accessToken)
        {
            return new Configuration
            {
                AccessToken = accessToken
            };
        }

        /// <summary>
        /// Creates a configuration with TikTok credentials
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="accessToken">Access token</param>
        /// <returns>Configuration with TikTok credentials</returns>
        public static Configuration CreateWithCredentials(string appId, string accessToken)
        {
            return new Configuration
            {
                AppId = appId,
                AccessToken = accessToken
            };
        }

        /// <summary>
        /// Creates a configuration for production environment
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="accessToken">Access token</param>
        /// <returns>Production configuration</returns>
        public static Configuration CreateForProduction(string appId, string accessToken)
        {
            return new Configuration
            {
                AppId = appId,
                AccessToken = accessToken,
                Environment = TikTokEnvironment.Production
            };
        }

        /// <summary>
        /// Creates a configuration for sandbox environment
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="accessToken">Access token</param>
        /// <returns>Sandbox configuration</returns>
        public static Configuration CreateForSandbox(string appId, string accessToken)
        {
            var config = new Configuration
            {
                AppId = appId,
                AccessToken = accessToken,
                Environment = TikTokEnvironment.Sandbox
            };

            // Apply sandbox-specific rate limits
            config.RateLimiterOptions.RequestsPerWindow = config.Environment.RateLimits.RequestsPerMinute;
            config.RequestThrottlerOptions.MaxRequestsPerSecond = config.Environment.RateLimits.RequestsPerSecond;
            config.RequestThrottlerOptions.MaxConcurrentRequests = config.Environment.RateLimits.ConcurrentRequests;

            return config;
        }

        /// <summary>
        /// Creates a configuration for custom environment
        /// </summary>
        /// <param name="appId">Application ID</param>
        /// <param name="accessToken">Access token</param>
        /// <param name="environment">Custom environment</param>
        /// <returns>Custom environment configuration</returns>
        public static Configuration CreateForEnvironment(string appId, string accessToken, TikTokEnvironment environment)
        {
            var config = new Configuration
            {
                AppId = appId,
                AccessToken = accessToken,
                Environment = environment
            };

            // Apply environment-specific rate limits
            config.RateLimiterOptions.RequestsPerWindow = environment.RateLimits.RequestsPerMinute;
            config.RequestThrottlerOptions.MaxRequestsPerSecond = environment.RateLimits.RequestsPerSecond;
            config.RequestThrottlerOptions.MaxConcurrentRequests = environment.RateLimits.ConcurrentRequests;

            return config;
        }
    }
}
