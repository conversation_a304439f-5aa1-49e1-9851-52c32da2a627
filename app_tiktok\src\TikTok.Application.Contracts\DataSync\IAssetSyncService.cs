using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Assets;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu tài sản (Assets)
    /// </summary>
    public interface IAssetSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ tài sản theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AssetSyncResult> SyncAssetsAsync(string bcId);

        /// <summary>
        /// Đồng bộ tài sản theo loại tài sản và BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="assetType">Loại tài sản</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AssetSyncResult> SyncAssetsByTypeAsync(string bcId, string assetType);

        /// <summary>
        /// Đồng bộ tất cả tài sản cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<AssetSyncResult> SyncAllAssetsForAllBcsAsync();
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu tài sản
    /// </summary>
    public class AssetSyncResult : SyncResultBase
    {
        /// <summary>
        /// Số BC đã đồng bộ
        /// </summary>
        public int BcCount { get; set; }

        /// <summary>
        /// Số loại tài sản đã đồng bộ
        /// </summary>
        public int AssetTypeCount { get; set; }

        /// <summary>
        /// Danh sách tài sản đã đồng bộ thành công
        /// </summary>
        public List<AssetDto> SyncedAssets { get; set; } = new List<AssetDto>();
    }
}