using System;
using System.Collections.Generic;
using TikTok.DimAdAccounts;
using TikTok.DimBusinessCenters;
using TikTok.DimCampaigns;
using TikTok.DimDates;

namespace TikTok.FactCampaigns.Dtos
{
    public class GetFactCampaignDataResponse
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public List<FactCampaignDto> FactCampaigns { get; set; }
        public List<DimDateDto> DimDates { get; set; }
        public List<DimAdAccountDto> DimAdAccounts { get; set; }
        public List<DimBusinessCenterDto> DimBusinessCenters { get; set; }
        public List<DimCampaignDto> DimCampaigns { get; set; }
    }
}