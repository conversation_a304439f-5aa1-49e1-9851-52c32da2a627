using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.BalanceBusinessCenters
{
    /// <summary>
    /// DTO cho Business Center với thông tin số dư mới nhất
    /// </summary>
    public class LatestBalanceBusinessCenterDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID của Business Center
        /// </summary>
        public string BcId { get; set; }

        /// <summary>
        /// Tên của Business Center
        /// </summary>
        public string? BcName { get; set; }

        /// <summary>
        /// Tiền tệ của Business Center
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Tổng số dư của Business Center
        /// </summary>
        public decimal? AccountBalance { get; set; }

        /// <summary>
        /// Số dư tài khoản hợp lệ của Business Center
        /// </summary>
        public decimal? ValidAccountBalance { get; set; }

        /// <summary>
        /// S<PERSON> dư bị đóng băng của Business Center
        /// </summary>
        public decimal? FrozenBalance { get; set; }

        /// <summary>
        /// Thuế của Business Center
        /// </summary>
        public decimal? Tax { get; set; }

        /// <summary>
        /// Số dư tiền mặt của Business Center
        /// </summary>
        public decimal? CashBalance { get; set; }

        /// <summary>
        /// Số dư tiền mặt hợp lệ của Business Center
        /// </summary>
        public decimal? ValidCashBalance { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher của Business Center
        /// </summary>
        public decimal? GrantBalance { get; set; }

        /// <summary>
        /// Số dư phiếu giảm giá/voucher hợp lệ của Business Center
        /// </summary>
        public decimal? ValidGrantBalance { get; set; }

        /// <summary>
        /// Thời gian đồng bộ dữ liệu mới nhất (UTC)
        /// </summary>
        public DateTime? Date { get; set; }

        /// <summary>
        /// Múi giờ của ngân sách
        /// </summary>
        public string? Timezone { get; set; }
    }
}
