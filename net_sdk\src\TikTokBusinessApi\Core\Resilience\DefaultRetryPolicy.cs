/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Net;
using System.Net.Http;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Exceptions;

namespace TikTokBusinessApi.Core.Resilience
{
    /// <summary>
    /// Default implementation of retry policy
    /// </summary>
    public class DefaultRetryPolicy : IRetryPolicy
    {
        private readonly RetryPolicyOptions _options;
        private readonly ILogger<DefaultRetryPolicy>? _logger;
        private readonly Random _random = new();

        /// <summary>
        /// Maximum number of retry attempts
        /// </summary>
        public int MaxRetryAttempts => _options.MaxRetryAttempts;

        /// <summary>
        /// Initializes a new instance of the DefaultRetryPolicy class
        /// </summary>
        /// <param name="options">Retry policy options</param>
        /// <param name="logger">Logger instance</param>
        public DefaultRetryPolicy(RetryPolicyOptions? options = null, ILogger<DefaultRetryPolicy>? logger = null)
        {
            _options = options ?? new RetryPolicyOptions();
            _logger = logger;
        }

        /// <summary>
        /// Determines if an exception should trigger a retry
        /// </summary>
        /// <param name="exception">The exception that occurred</param>
        /// <param name="attemptNumber">Current attempt number (0-based)</param>
        /// <returns>True if the operation should be retried</returns>
        public bool ShouldRetry(Exception exception, int attemptNumber)
        {
            if (attemptNumber >= _options.MaxRetryAttempts)
                return false;

            // Custom retry condition takes precedence
            if (_options.CustomRetryCondition != null)
                return _options.CustomRetryCondition(exception, attemptNumber);

            return exception switch
            {
                // Network-related exceptions
                HttpRequestException httpEx => IsRetryableHttpException(httpEx),
                TaskCanceledException tcEx when tcEx.InnerException is TimeoutException => true,
                SocketException => true,
                
                // TikTok-specific exceptions
                TikTokRateLimitException => true,
                TikTokNetworkException => true,
                SdkException sdkEx when sdkEx.IsRetryable => true,
                
                // API exceptions with retryable status codes
                ApiException apiEx => IsRetryableStatusCode(apiEx.StatusCode),
                
                _ => false
            };
        }

        /// <summary>
        /// Calculates the delay before the next retry attempt
        /// </summary>
        /// <param name="attemptNumber">Current attempt number (0-based)</param>
        /// <param name="exception">The exception that occurred</param>
        /// <returns>Delay before next retry</returns>
        public TimeSpan GetRetryDelay(int attemptNumber, Exception? exception = null)
        {
            TimeSpan delay;

            if (_options.UseExponentialBackoff)
            {
                // Exponential backoff: baseDelay * 2^attemptNumber
                var exponentialDelay = TimeSpan.FromMilliseconds(
                    _options.BaseDelay.TotalMilliseconds * Math.Pow(2, attemptNumber));
                delay = exponentialDelay > _options.MaxDelay ? _options.MaxDelay : exponentialDelay;
            }
            else
            {
                // Linear backoff
                delay = TimeSpan.FromMilliseconds(_options.BaseDelay.TotalMilliseconds * (attemptNumber + 1));
                delay = delay > _options.MaxDelay ? _options.MaxDelay : delay;
            }

            // Add jitter to prevent thundering herd
            if (_options.JitterFactor > 0)
            {
                var jitterMs = delay.TotalMilliseconds * _options.JitterFactor * (_random.NextDouble() - 0.5);
                delay = delay.Add(TimeSpan.FromMilliseconds(jitterMs));
            }

            // Handle rate limit exceptions with specific retry-after
            if (exception is TikTokRateLimitException rateLimitEx && rateLimitEx.ResetTime.HasValue)
            {
                var rateLimitDelay = rateLimitEx.ResetTime.Value - DateTimeOffset.UtcNow;
                if (rateLimitDelay > TimeSpan.Zero && rateLimitDelay < _options.MaxDelay)
                    delay = rateLimitDelay;
            }

            return delay;
        }

        /// <summary>
        /// Executes an operation with retry logic
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="operation">Operation to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        public async Task<T> ExecuteAsync<T>(Func<CancellationToken, Task<T>> operation, CancellationToken cancellationToken = default)
        {
            var attemptNumber = 0;
            Exception? lastException = null;

            while (attemptNumber <= _options.MaxRetryAttempts)
            {
                try
                {
                    _logger?.LogDebug("Executing operation (attempt {AttemptNumber})", attemptNumber + 1);
                    return await operation(cancellationToken);
                }
                catch (Exception ex) when (ShouldRetry(ex, attemptNumber))
                {
                    lastException = ex;
                    var delay = GetRetryDelay(attemptNumber, ex);
                    
                    _logger?.LogWarning(ex, "Operation failed (attempt {AttemptNumber}), retrying after {Delay}ms", 
                        attemptNumber + 1, delay.TotalMilliseconds);

                    await Task.Delay(delay, cancellationToken);
                    attemptNumber++;
                }
            }

            _logger?.LogError(lastException, "Operation failed after {MaxAttempts} attempts", _options.MaxRetryAttempts + 1);
            throw lastException ?? new InvalidOperationException("Operation failed without exception");
        }

        /// <summary>
        /// Executes an HTTP request with retry logic
        /// </summary>
        /// <param name="httpClient">HTTP client to use</param>
        /// <param name="requestFactory">Factory to create HTTP request messages</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>HTTP response</returns>
        public async Task<HttpResponseMessage> ExecuteHttpAsync(
            HttpClient httpClient,
            Func<HttpRequestMessage> requestFactory,
            CancellationToken cancellationToken = default)
        {
            return await ExecuteAsync(async ct =>
            {
                using var request = requestFactory();
                return await httpClient.SendAsync(request, ct);
            }, cancellationToken);
        }

        private static bool IsRetryableHttpException(HttpRequestException httpEx)
        {
            var message = httpEx.Message.ToLowerInvariant();
            return message.Contains("timeout") ||
                   message.Contains("connection") ||
                   message.Contains("network") ||
                   message.Contains("dns");
        }

        private static bool IsRetryableStatusCode(int statusCode)
        {
            return statusCode switch
            {
                >= 500 and <= 599 => true, // Server errors (includes 502, 503, 504)
                429 => true, // Too Many Requests
                408 => true, // Request Timeout
                _ => false
            };
        }
    }
}
