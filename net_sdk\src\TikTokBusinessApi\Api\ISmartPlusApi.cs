/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Smart Plus operations
    /// </summary>
    public interface ISmartPlusApi
    {
        /// <summary>
        /// Get the dynamic quota on the number of active Smart+ Campaigns within an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing Smart+ Campaign quota information</returns>
        Task<Response<SmartPlusQuotaResponse>> GetSmartPlusQuotaAsync(
            string accessToken,
            string advertiserId,
            CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Create a Smart+ Campaign
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing Smart+ Campaign information</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing created Smart+ Campaign information</returns>
        // Task<Response<SmartPlusResponse>> CreateSmartPlusCampaignAsync(
        //     string accessToken,
        //     SmartPlusCreateBody body,
        //     CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Update a Smart+ Campaign
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing Smart+ Campaign information to update</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing updated Smart+ Campaign information</returns>
        // Task<Response<SmartPlusResponse>> UpdateSmartPlusCampaignAsync(
        //     string accessToken,
        //     SmartPlusUpdateBody body,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get detailed information of Smart+ Campaigns and Smart Performance Web Campaigns
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="campaignIds">A list of campaign IDs (max size: 100)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing Smart+ Campaign information</returns>
        Task<Response<SmartPlusListResponse>> GetSmartPlusCampaignsAsync(
            string accessToken,
            string advertiserId,
            List<string> campaignIds,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Run a report on Smart+ Campaigns and Smart Performance Web Campaigns at the creative level
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="campaignIds">List of campaign IDs (max size: 1)</param>
        /// <param name="materialType">Creative material type</param>
        /// <param name="isIncludeRemovedMaterial">Whether to query data of deleted videos, texts, or posts</param>
        /// <param name="metrics">Metrics to query</param>
        /// <param name="queryLifetime">Whether to request the lifetime metrics</param>
        /// <param name="startDate">Query start date (required when queryLifetime is false)</param>
        /// <param name="endDate">Query end date (required when queryLifetime is false)</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size (1-100)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing Smart+ Campaign report data</returns>
        Task<Response<SmartPlusReportResponse>> GetSmartPlusReportAsync(
            string accessToken,
            string advertiserId,
            List<string> campaignIds,
            string materialType,
            bool? isIncludeRemovedMaterial = null,
            List<string>? metrics = null,
            bool? queryLifetime = null,
            string? startDate = null,
            string? endDate = null,
            int? page = null,
            int? pageSize = null,
            CancellationToken cancellationToken = default);
    }
}
