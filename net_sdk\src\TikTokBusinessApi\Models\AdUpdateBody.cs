/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for updating ads
    /// </summary>
    public class AdUpdateBody
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        [Required]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        [Required]
        public string AdgroupId { get; set; } = string.Empty;

        /// <summary>
        /// Whether to use incremental update mode
        /// </summary>
        [JsonPropertyName("patch_update")]
        public bool? PatchUpdate { get; set; }

        /// <summary>
        /// Advertising creatives. Max size: 20.
        /// </summary>
        [JsonPropertyName("creatives")]
        [Required]
        public List<AdUpdateCreatives> Creatives { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the AdUpdateBody class
        /// </summary>
        public AdUpdateBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the AdUpdateBody class with parameters
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="adgroupId">Ad group ID</param>
        /// <param name="creatives">List of creatives</param>
        public AdUpdateBody(string advertiserId, string adgroupId, List<AdUpdateCreatives> creatives)
        {
            AdvertiserId = advertiserId;
            AdgroupId = adgroupId;
            Creatives = creatives;
        }

        /// <summary>
        /// Sets the advertiser ID and returns this instance for method chaining
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>This instance</returns>
        public AdUpdateBody SetAdvertiserId(string advertiserId)
        {
            AdvertiserId = advertiserId;
            return this;
        }

        /// <summary>
        /// Sets the ad group ID and returns this instance for method chaining
        /// </summary>
        /// <param name="adgroupId">Ad group ID</param>
        /// <returns>This instance</returns>
        public AdUpdateBody SetAdgroupId(string adgroupId)
        {
            AdgroupId = adgroupId;
            return this;
        }

        /// <summary>
        /// Sets the patch update flag and returns this instance for method chaining
        /// </summary>
        /// <param name="patchUpdate">Whether to use incremental update mode</param>
        /// <returns>This instance</returns>
        public AdUpdateBody SetPatchUpdate(bool patchUpdate)
        {
            PatchUpdate = patchUpdate;
            return this;
        }

        /// <summary>
        /// Sets the creatives list and returns this instance for method chaining
        /// </summary>
        /// <param name="creatives">List of creatives</param>
        /// <returns>This instance</returns>
        public AdUpdateBody SetCreatives(List<AdUpdateCreatives> creatives)
        {
            Creatives = creatives;
            return this;
        }

        /// <summary>
        /// Adds a creative to the list and returns this instance for method chaining
        /// </summary>
        /// <param name="creative">Creative to add</param>
        /// <returns>This instance</returns>
        public AdUpdateBody AddCreative(AdUpdateCreatives creative)
        {
            Creatives.Add(creative);
            return this;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdUpdateBody(AdvertiserId={AdvertiserId}, AdgroupId={AdgroupId}, PatchUpdate={PatchUpdate}, Creatives={Creatives?.Count ?? 0} items)";
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object
        /// </summary>
        /// <param name="obj">The object to compare with the current object</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not AdUpdateBody other)
                return false;

            return AdvertiserId == other.AdvertiserId &&
                   AdgroupId == other.AdgroupId &&
                   PatchUpdate == other.PatchUpdate &&
                   Equals(Creatives, other.Creatives);
        }

        /// <summary>
        /// Returns the hash code for this instance
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            return HashCode.Combine(AdvertiserId, AdgroupId, PatchUpdate, Creatives);
        }
    }
}
