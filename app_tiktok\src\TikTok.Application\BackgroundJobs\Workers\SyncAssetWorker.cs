using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using TikTok.DataSync;
using TikTok.Entities;
using TikTok.Enums;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Worker để đồng bộ Asset
    /// </summary>
    public class SyncAssetWorker : WorkerJobBase
    {
        private readonly IAssetSyncService _assetSyncService;
        private readonly IAdAccountSyncService _adAccountSyncService;

        public SyncAssetWorker(
            ILogger<SyncAssetWorker> logger,
            IServiceProvider serviceProvider,
            IAssetSyncService assetSyncService,
            IAdAccountSyncService adAccountSyncService)
            : base(logger, serviceProvider)
        {
            _assetSyncService = assetSyncService;
            _adAccountSyncService = adAccountSyncService;
        }

        public override CommandType CommandType => CommandType.SyncAsset;

        protected override async Task<JobResult> WorkerExecuteAsync(WorkerJobArgs args, JobEntity job)
        {
            // Parse parameters
            var parameters = ParseParameters(args.Parameters);

            // Sync Asset
            var responseSync = await _assetSyncService.SyncAssetsAsync(parameters.BcId);
            if (responseSync != null)
            {
                if (responseSync.IsSuccess)
                {
                    // Đồng bộ thành công, tiếp tục đồng bộ AdAccount
                    var responseAdAccountSync = await _adAccountSyncService.UpdateAdAccountsFromAssetsAsync(parameters.BcId);

                    return JobResult.Success(JsonConvert.SerializeObject(new
                    {
                        BcId = parameters.BcId,
                        SyncDate = parameters.SyncDate,
                        AssetSyncResult = responseSync,
                        AccountSync = responseAdAccountSync,
                        Message = "Đồng bộ Asset và AdAccount thành công"
                    }));
                }
                else
                    return JobResult.Error(responseSync.ErrorMessage ?? string.Empty, JsonConvert.SerializeObject(responseSync));
            }
            else
            {
                return JobResult.Error("Lỗi đồng bộ Asset");
            }
        }

        /// <summary>
        /// Parse parameters từ JSON
        /// </summary>
        /// <param name="parametersJson">JSON parameters</param>
        /// <returns>Parameters object</returns>
        private SyncAssetParameters ParseParameters(string parametersJson)
        {
            try
            {
                return System.Text.Json.JsonSerializer.Deserialize<SyncAssetParameters>(parametersJson)
                    ?? new SyncAssetParameters();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse parameters, using defaults");
                return new SyncAssetParameters();
            }
        }

        /// <summary>
        /// Parameters cho SyncAsset
        /// </summary>
        public class SyncAssetParameters:DefaultParameters
        {
            //public string BcId { get; set; }
            //public DateTime SyncDate { get; set; }
        }
    }
}