/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the AutomatedRulesApi
    /// </summary>
    public class AutomatedRulesApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public AutomatedRulesApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example: Create an automated rule to turn off campaigns with low performance
        /// </summary>
        public async Task CreatePerformanceRuleExample()
        {
            try
            {
                const string accessToken = "your-access-token";
                const string advertiserId = "your-advertiser-id";

                Console.WriteLine("Creating automated rule to turn off low-performing campaigns...");

                var createBody = new AutomatedRulesCreateBody
                {
                    AdvertiserId = advertiserId,
                    Rules = new List<AutomatedRule>
                    {
                        new AutomatedRule
                        {
                            Name = "[API] Low Performance Campaign Auto-Pause",
                            ApplyObjects = new List<RuleApplyObject>
                            {
                                new RuleApplyObject
                                {
                                    Dimension = "CAMPAIGN",
                                    DimensionIds = new List<string>(), // Empty for all active campaigns
                                    PreConditionType = "ALL_ACTIVE_CAMPAIGN"
                                }
                            },
                            Conditions = new List<RuleCondition>
                            {
                                new RuleCondition
                                {
                                    SubjectType = "CTR",
                                    RangeType = "PAST_SEVEN_DAYS",
                                    MatchType = "LT",
                                    Values = new List<string> { "1.0" } // CTR less than 1%
                                },
                                new RuleCondition
                                {
                                    SubjectType = "COST",
                                    RangeType = "PAST_SEVEN_DAYS",
                                    MatchType = "GT",
                                    Values = new List<string> { "100.0" } // Cost greater than $100
                                }
                            },
                            Actions = new List<RuleAction>
                            {
                                new RuleAction
                                {
                                    SubjectType = "TURN_OFF"
                                }
                            },
                            Notification = new RuleNotification
                            {
                                NotificationType = "ANY_CHANGES",
                                EmailSetting = new RuleEmailSetting
                                {
                                    NotificationPeriod = "EVERY_TIME",
                                    NoResultNotification = false
                                }
                            },
                            RuleExecInfo = new RuleExecutionInfo
                            {
                                ExecTimeType = "CUSTOM",
                                ExecTime = "09:00" // Run daily at 9 AM
                            }
                        }
                    }
                };

                var response = await _client.AutomatedRules.CreateRulesAsync(accessToken, createBody);

                if (response.Data?.RuleIds != null && response.Data.RuleIds.Count > 0)
                {
                    Console.WriteLine($"Successfully created rule with ID: {response.Data.RuleIds[0]}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
                else
                {
                    Console.WriteLine("Failed to create rule - no rule IDs returned");
                }
            }
            catch (TikTokBusinessApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Create a budget optimization rule
        /// </summary>
        public async Task CreateBudgetOptimizationRuleExample()
        {
            try
            {
                const string accessToken = "your-access-token";
                const string advertiserId = "your-advertiser-id";

                Console.WriteLine("Creating budget optimization rule...");

                var createBody = new AutomatedRulesCreateBody
                {
                    AdvertiserId = advertiserId,
                    Rules = new List<AutomatedRule>
                    {
                        new AutomatedRule
                        {
                            Name = "[API] Budget Increase for High-Performing Campaigns",
                            ApplyObjects = new List<RuleApplyObject>
                            {
                                new RuleApplyObject
                                {
                                    Dimension = "CAMPAIGN",
                                    DimensionIds = new List<string>(), // Empty for all active campaigns
                                    PreConditionType = "ALL_ACTIVE_CAMPAIGN"
                                }
                            },
                            Conditions = new List<RuleCondition>
                            {
                                new RuleCondition
                                {
                                    SubjectType = "ROAS_PURCHASE",
                                    RangeType = "PAST_THREE_DAYS",
                                    MatchType = "GT",
                                    Values = new List<string> { "3.0" } // ROAS greater than 3.0
                                },
                                new RuleCondition
                                {
                                    SubjectType = "DAILY_BUDGET_SPENDING_RATE",
                                    RangeType = "YESTERDAY",
                                    MatchType = "GT",
                                    Values = new List<string> { "80.0" } // Spending rate > 80%
                                }
                            },
                            Actions = new List<RuleAction>
                            {
                                new RuleAction
                                {
                                    SubjectType = "DAILY_BUDGET",
                                    ActionType = "INCREASE",
                                    ValueType = "PERCENT",
                                    Value = new RuleActionValue
                                    {
                                        Value = 20.0f, // Increase by 20%
                                        UseLimit = true,
                                        Limit = 1000.0f // Maximum budget cap of $1000
                                    },
                                    FrequencyInfo = new RuleFrequencyInfo
                                    {
                                        Type = "ONCE_IN_24_H" // Only once per day
                                    }
                                }
                            },
                            Notification = new RuleNotification
                            {
                                NotificationType = "ANY_CHANGES",
                                EmailSetting = new RuleEmailSetting
                                {
                                    NotificationPeriod = "TIME_SCHEDULE",
                                    EmailExecTime = new List<string> { "18:00" }, // Send summary at 6 PM
                                    NoResultNotification = false
                                }
                            },
                            RuleExecInfo = new RuleExecutionInfo
                            {
                                ExecTimeType = "PER_HALF_HOUR" // Check every 30 minutes
                            }
                        }
                    }
                };

                var response = await _client.AutomatedRules.CreateRulesAsync(accessToken, createBody);

                if (response.Data?.RuleIds != null && response.Data.RuleIds.Count > 0)
                {
                    Console.WriteLine($"Successfully created budget optimization rule with ID: {response.Data.RuleIds[0]}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
                else
                {
                    Console.WriteLine("Failed to create budget optimization rule");
                }
            }
            catch (TikTokBusinessApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get all active rules for an advertiser
        /// </summary>
        public async Task GetActiveRulesExample()
        {
            try
            {
                const string accessToken = "your-access-token";
                const string advertiserId = "your-advertiser-id";

                Console.WriteLine("Getting all active rules...");

                var filtering = new AutomatedRulesFiltering
                {
                    Status = "ON" // Only get active rules
                };

                var response = await _client.AutomatedRules.GetRulesByFiltersAsync(
                    accessToken,
                    advertiserId,
                    filtering,
                    page: 1,
                    pageSize: 50);

                if (response.Data?.Rules != null)
                {
                    Console.WriteLine($"Found {response.Data.Rules.Count} active rules:");
                    
                    foreach (var rule in response.Data.Rules)
                    {
                        Console.WriteLine($"- Rule ID: {rule.RuleId}");
                        Console.WriteLine($"  Name: {rule.Name}");
                        Console.WriteLine($"  Status: {rule.RuleStatus}");
                        Console.WriteLine($"  Created: {rule.CreateDatetime}");
                        
                        if (rule.LastCheckResultSummary != null)
                        {
                            Console.WriteLine($"  Last Check: {rule.LastCheckResultSummary.CheckDatetime}");
                            Console.WriteLine($"  Changes: {rule.LastCheckResultSummary.ChangeSuccess} success, {rule.LastCheckResultSummary.ChangeFail} failed");
                        }
                        
                        Console.WriteLine();
                    }

                    if (response.Data.PageInfo != null)
                    {
                        Console.WriteLine($"Page {response.Data.PageInfo.Page} of {response.Data.PageInfo.TotalPage}");
                        Console.WriteLine($"Total rules: {response.Data.PageInfo.TotalNumber}");
                    }
                }
                else
                {
                    Console.WriteLine("No rules found");
                }
            }
            catch (TikTokBusinessApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Update rule status (turn on/off/delete)
        /// </summary>
        public async Task UpdateRuleStatusExample()
        {
            try
            {
                const string accessToken = "your-access-token";
                const string advertiserId = "your-advertiser-id";
                const string ruleId = "your-rule-id";

                Console.WriteLine($"Turning off rule {ruleId}...");

                var statusUpdateBody = new AutomatedRulesStatusUpdateBody
                {
                    AdvertiserId = advertiserId,
                    RuleIds = new List<string> { ruleId },
                    OperateType = "TURN_OFF"
                };

                var response = await _client.AutomatedRules.UpdateRuleStatusesAsync(accessToken, statusUpdateBody);

                if (response.Data?.RuleIds != null && response.Data.RuleIds.Count > 0)
                {
                    Console.WriteLine($"Successfully updated status for rule: {response.Data.RuleIds[0]}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                }
                else
                {
                    Console.WriteLine("Failed to update rule status");
                }
            }
            catch (TikTokBusinessApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Program entry point for running the examples
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Replace with your actual access token
            const string accessToken = "your-access-token";
            
            var example = new AutomatedRulesApiExample(accessToken);
            
            try
            {
                Console.WriteLine("=== TikTok Business API - AutomatedRules Examples ===\n");
                
                // Example 1: Create performance rule
                await example.CreatePerformanceRuleExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 2: Create budget optimization rule
                await example.CreateBudgetOptimizationRuleExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 3: Get active rules
                await example.GetActiveRulesExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 4: Update rule status
                await example.UpdateRuleStatusExample();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Example execution failed: {ex.Message}");
            }
        }
    }
}
