/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Threading;
using System.Threading.Tasks;

namespace TikTokBusinessApi.Core.Resilience
{
    /// <summary>
    /// Interface for rate limiting implementations
    /// </summary>
    public interface IRateLimiter
    {
        /// <summary>
        /// Waits for permission to proceed with an operation
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task that completes when permission is granted</returns>
        Task WaitAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Tries to acquire permission immediately without waiting
        /// </summary>
        /// <returns>True if permission was granted, false otherwise</returns>
        bool TryAcquire();

        /// <summary>
        /// Gets the current rate limit status
        /// </summary>
        RateLimitStatus GetStatus();

        /// <summary>
        /// Resets the rate limiter state
        /// </summary>
        void Reset();
    }

    /// <summary>
    /// Rate limit status information
    /// </summary>
    public class RateLimitStatus
    {
        /// <summary>
        /// Number of requests remaining in current window
        /// </summary>
        public int RemainingRequests { get; set; }

        /// <summary>
        /// Total requests allowed per window
        /// </summary>
        public int TotalRequests { get; set; }

        /// <summary>
        /// Time when the current window resets
        /// </summary>
        public DateTimeOffset WindowResetTime { get; set; }

        /// <summary>
        /// Duration of the rate limit window
        /// </summary>
        public TimeSpan WindowDuration { get; set; }

        /// <summary>
        /// Whether the rate limit is currently exceeded
        /// </summary>
        public bool IsLimitExceeded => RemainingRequests <= 0;

        /// <summary>
        /// Time to wait before next request can be made
        /// </summary>
        public TimeSpan TimeToWait => WindowResetTime > DateTimeOffset.UtcNow 
            ? WindowResetTime - DateTimeOffset.UtcNow 
            : TimeSpan.Zero;
    }

    /// <summary>
    /// Rate limiter configuration options
    /// </summary>
    public class RateLimiterOptions
    {
        /// <summary>
        /// Maximum number of requests per time window
        /// </summary>
        public int RequestsPerWindow { get; set; } = 100;

        /// <summary>
        /// Duration of the time window
        /// </summary>
        public TimeSpan WindowDuration { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// Whether to use a sliding window (true) or fixed window (false)
        /// </summary>
        public bool UseSlidingWindow { get; set; } = true;

        /// <summary>
        /// Maximum time to wait for rate limit permission
        /// </summary>
        public TimeSpan MaxWaitTime { get; set; } = TimeSpan.FromMinutes(5);
    }
}
