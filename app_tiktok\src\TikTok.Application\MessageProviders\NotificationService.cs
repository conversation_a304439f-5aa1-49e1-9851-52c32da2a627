using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTok.Application.Contracts.MessageProviders;
using TikTok.BaseMessages;
using TikTok.Const;
using Elsa.Models;
using Elsa.Providers.Workflows;
using Elsa.Services;
using Volo.Abp.DependencyInjection;

namespace TikTok.Application.MessageProviders
{
    /// <summary>
    /// Service implementation cho việc gửi thông báo thông qua Elsa workflow
    /// </summary>
    public class NotificationService : INotificationService, ITransientDependency
    {
        private readonly ILogger<NotificationService> _logger;
        private readonly IWorkflowProvider _workflowProvider;
        private readonly IStartsWorkflow _startsWorkflow;

        public NotificationService(
            ILogger<NotificationService> logger,
            IWorkflowProvider workflowProvider,
            IStartsWorkflow startsWorkflow)
        {
            _logger = logger;
            _workflowProvider = workflowProvider;
            _startsWorkflow = startsWorkflow;
        }

        /// <summary>
        /// <PERSON><PERSON><PERSON> thông báo thông qua workflow
        /// </summary>
        /// <param name="workflowName">Tên workflow cần chạy</param>
        /// <param name="messageInput">Dữ liệu thông báo</param>
        /// <returns>Kết quả thực thi workflow</returns>
        public async Task<object> SendNotificationAsync(string workflowName, BaseMessageActivityInput messageInput)
        {
            try
            {
                _logger.LogDebug("Bắt đầu gửi thông báo qua workflow: {WorkflowName}", workflowName);

                // Tìm workflow theo tên
                var workflow = await _workflowProvider.FindByNameAsync(workflowName, VersionOptions.LatestOrPublished);

                if (workflow == null)
                {
                    _logger.LogWarning("Không tìm thấy workflow: {WorkflowName}", workflowName);
                    return null;
                }

                // Tạo input cho workflow
                var input = new WorkflowInput()
                {
                    Input = messageInput
                };

                // Thực thi workflow
                var workflowResult = await _startsWorkflow.StartWorkflowAsync(workflow, input: input);

                _logger.LogDebug("Hoàn thành gửi thông báo qua workflow: {WorkflowName}", workflowName);

                return workflowResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi gửi thông báo qua workflow: {WorkflowName}", workflowName);
                throw;
            }
        }

        /// <summary>
        /// Gửi thông báo với workflow mặc định
        /// </summary>
        /// <param name="messageInput">Dữ liệu thông báo</param>
        /// <returns>Kết quả thực thi workflow</returns>
        public async Task<object> SendNotificationAsync(BaseMessageActivityInput messageInput)
        {
            return await SendNotificationAsync(WorkflowConst.RequireSendMessageWorkflow, messageInput);
        }

        /// <summary>
        /// Gửi thông báo với thông tin cơ bản
        /// </summary>
        /// <param name="adAccountId">ID tài khoản quảng cáo</param>
        /// <param name="bcId">ID Business Center</param>
        /// <param name="textContent">Nội dung thông báo</param>
        /// <param name="phoneNumber">Số điện thoại (tùy chọn)</param>
        /// <param name="name">Tên người gửi (tùy chọn)</param>
        /// <param name="ownerId">Owner ID (tùy chọn)</param>
        /// <returns>Kết quả thực thi workflow</returns>
        public async Task<object> SendNotificationAsync(
            string adAccountId, 
            string bcId, 
            string textContent, 
            string phoneNumber = null, 
            string name = null, 
            string ownerId = null)
        {
            var messageInput = new BaseMessageActivityInput()
            {
                AdAccountId = adAccountId,
                BcId = bcId,
                TextContent = textContent,
                Phonenumber = phoneNumber,
                Name = name,
                OwnerId = ownerId
            };

            return await SendNotificationAsync(messageInput);
        }

        /// <summary>
        /// Gửi thông báo theo danh sách tài khoản quảng cáo trong một Business Center.
        /// Thực thi workflow RequireSendMessage với thông tin từ DTO.
        /// </summary>
        /// <param name="notificationDto">DTO chứa thông tin thông báo</param>
        /// <returns>Kết quả thực thi workflow</returns>
        public async Task<object> SendNotificationAsync(SendNotificationDto notificationDto)
        {
            try
            {
                var workflowName = WorkflowConst.RequireSendMessageWorkflow;
                _logger.LogDebug("Bắt đầu gửi thông báo qua workflow: {WorkflowName}", workflowName);

                // Tìm workflow theo tên
                var workflow = await _workflowProvider.FindByNameAsync(workflowName, VersionOptions.LatestOrPublished);
                if (workflow == null)
                {
                    _logger.LogWarning("Không tìm thấy workflow: {WorkflowName}", workflowName);
                    return null;
                }

                // Đầu vào workflow gồm thông tin từ DTO: BcId, AdvertiserIds, Payload, NotificationType
                var input = new WorkflowInput
                {
                    Input = new 
                    {
                        notificationDto.BcId,
                        notificationDto.AdvertiserIds,
                        notificationDto.ObjectIds,
                        notificationDto.Payload,
                        notificationDto.NotificationType
                    }
                };

                var result = await _startsWorkflow.StartWorkflowAsync(workflow, input: input);

                _logger.LogDebug("Hoàn thành gửi thông báo qua workflow: {WorkflowName}", workflowName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi gửi thông báo qua workflow RequireSendMessage");
                throw;
            }
        }
    }
} 