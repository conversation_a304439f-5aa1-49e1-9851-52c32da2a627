/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API BC Reporting endpoints
    /// </summary>
    public static class BCReportingEndpoints
    {
        /// <summary>
        /// Get currencies and registration areas for ad accounts endpoint (v1.3)
        /// </summary>
        public const string GetAdvertiserAttribute = "/bc/advertiser/attribute/";
    }
}
