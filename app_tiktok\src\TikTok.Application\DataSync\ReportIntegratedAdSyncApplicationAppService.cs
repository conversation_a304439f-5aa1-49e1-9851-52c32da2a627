using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu báo cáo tích hợp Ad
    /// </summary>
    public class ReportIntegratedAdSyncApplicationAppService : ApplicationService, IReportIntegratedAdSyncApplicationAppService
    {
        private readonly IReportIntegratedAdSyncService _reportIntegratedAdSyncService;

        public ReportIntegratedAdSyncApplicationAppService(IReportIntegratedAdSyncService reportIntegratedAdSyncService)
        {
            _reportIntegratedAdSyncService = reportIntegratedAdSyncService;
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp Ad cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedAdSyncResult> SyncAllReportIntegratedAdForAllBcsAsync()
        {
            return await _reportIntegratedAdSyncService.SyncAllReportIntegratedAdForAllBcsAsync();
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp Ad theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate">Ngày bắt đầu (tùy chọn)</param>
        /// <param name="endDate">Ngày kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedAdSyncResult> SyncReportIntegratedAdAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _reportIntegratedAdSyncService.SyncReportIntegratedAdAsync(bcId, startDate, endDate);
        }
    }
}