/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Smart Plus endpoints
    /// </summary>
    public static class SmartPlusEndpoints
    {
        /// <summary>
        /// Get the dynamic quota on Smart+ Campaigns endpoint (v1.3)
        /// </summary>
        public const string GetSmartPlusQuota = "/campaign/spc/quota/get/";

        /// <summary>
        /// Create a Smart+ Campaign endpoint (v1.3)
        /// </summary>
        public const string CreateSmartPlusCampaign = "/campaign/spc/create/";

        /// <summary>
        /// Update a Smart+ Campaign endpoint (v1.3)
        /// </summary>
        public const string UpdateSmartPlusCampaign = "/campaign/spc/update/";

        /// <summary>
        /// Get Smart+ Campaigns endpoint (v1.3)
        /// </summary>
        public const string GetSmartPlusCampaigns = "/campaign/spc/get/";

        /// <summary>
        /// Run a Smart+ Campaign report endpoint (v1.3)
        /// </summary>
        public const string GetSmartPlusReport = "/campaign/spc/report/get/";
    }
}
