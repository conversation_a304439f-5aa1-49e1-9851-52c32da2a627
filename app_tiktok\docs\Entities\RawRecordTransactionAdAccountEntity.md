# RawRecordTransactionAdAccountEntity

## Thông tin bảng dữ liệu

-   **Table**: `RecordTransactionAdAccounts`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RecordTransactionAdAccounts`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho bản ghi giao dịch tài khoản quảng cáo. Lưu trữ thông tin về các giao dịch tài chính liên quan đến tài khoản quảng cáo, bao gồm chi tiết giao dịch, loại hình tài trợ và thanh toán.

## Bảng dữ liệu: RawRecordTransactionAdAccount

| Tên Field            | Kiểu dữ liệu | Bắt buộc | Độ dài | <PERSON>ô tả                                                           |
| -------------------- | ------------ | -------- | ------ | --------------------------------------------------------------- |
| Id                   | Guid         | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                           |
| AdvertiserId         | string       | ✅       | 100    | ID Tài khoản Quảng cáo (phần của composite key)                 |
| AdvertiserName       | string       | ✅       | 255    | Tên Tài khoản Quảng cáo                                         |
| Amount               | decimal      | ✅       | 18,2   | Số tiền giao dịch, được giữ ở hai chữ số thập phân              |
| Currency             | string       | ✅       | 10     | Tiền tệ của giao dịch                                           |
| Date                 | DateTime     | ✅       | -      | Thời gian giao dịch theo múi giờ UTC+0 (phần của composite key) |
| FundsType            | FundsType    | ✅       | -      | Loại hình tài trợ                                               |
| TransferType         | TransferType | ✅       | -      | Loại hình thanh toán                                            |
| Timezone             | string       | ✅       | 20     | Múi giờ giao dịch                                               |
| CreationTime         | DateTime     | ✅       | -      | Thời gian tạo bản ghi (Audit)                                   |
| CreatorId            | Guid?        | ❌       | -      | ID người tạo (Audit)                                            |
| LastModificationTime | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                  |
| LastModifierId       | Guid?        | ❌       | -      | ID người sửa đổi cuối (Audit)                                   |

## Cấu trúc dữ liệu

### Thông tin định danh

-   **AdvertiserId**: ID duy nhất của tài khoản quảng cáo từ TikTok API
-   **AdvertiserName**: Tên hiển thị của tài khoản quảng cáo
-   **Date**: Thời gian giao dịch theo múi giờ UTC+0

### Thông tin giao dịch

-   **Amount**: Số tiền giao dịch với độ chính xác 2 chữ số thập phân
-   **Currency**: Đơn vị tiền tệ của giao dịch
-   **FundsType**: Loại hình tài trợ (CASH, CREDIT, GRANT, etc.)
-   **TransferType**: Loại hình thanh toán (DEPOSIT, WITHDRAWAL, etc.)
-   **Timezone**: Múi giờ gốc để tham chiếu khi convert từ UTC

## Mục đích sử dụng

-   Lưu trữ bản ghi giao dịch tài chính cho từng tài khoản quảng cáo
-   Theo dõi chi tiêu và thu nhập của tài khoản quảng cáo
-   Phân tích luồng tiền và tài chính
-   Cung cấp dữ liệu cho báo cáo tài chính chi tiết
-   Hỗ trợ kiểm toán và đối soát tài chính

## Mối quan hệ

-   **RawAdAccountEntity**: Liên kết với tài khoản quảng cáo (AdvertiserId)
-   **RawRecordTransactionBcEntity**: Bản ghi giao dịch tổng hợp cấp Business Center
-   **RawTransactionEntity**: Entity tổng hợp chứa tất cả các loại giao dịch

## Indexes

-   **Unique Index**: `(AdvertiserId, Date)` - Đảm bảo không có giao dịch trùng lặp cho cùng một tài khoản và thời điểm
-   **Index**: `AdvertiserId` - Tối ưu truy vấn theo tài khoản quảng cáo
-   **Index**: `Date` - Tối ưu truy vấn theo ngày giao dịch
-   **Index**: `Amount` - Tối ưu truy vấn theo số tiền giao dịch
-   **Index**: `FundsType` - Tối ưu truy vấn theo loại tài trợ
-   **Index**: `TransferType` - Tối ưu truy vấn theo loại thanh toán

## Constructors

### Constructor mặc định

```csharp
public RawRecordTransactionAdAccountEntity()
```

### Constructor với ID

```csharp
public RawRecordTransactionAdAccountEntity(Guid id)
```

**Tham số:**

-   `id` (Guid): ID của entity

### Constructor với composite key

```csharp
public RawRecordTransactionAdAccountEntity(string advertiserId, DateTime date)
```

**Tham số:**

-   `advertiserId` (string): ID Tài khoản Quảng cáo
-   `date` (DateTime): Thời gian giao dịch

## Lưu ý

-   Entity này kế thừa từ AuditedEntity để có đầy đủ thông tin audit
-   Sử dụng composite key (AdvertiserId + Date) để đảm bảo tính duy nhất
-   Amount được cấu hình với precision 18,2 để đảm bảo độ chính xác tài chính
-   Date được lưu trữ theo múi giờ UTC+0 để đảm bảo tính nhất quán
-   Timezone lưu trữ múi giờ gốc để có thể convert ngược lại khi cần thiết
-   AdvertiserId là khóa ngoại quan trọng để liên kết với các entity khác
-   Entity này cung cấp cái nhìn chi tiết về giao dịch tài chính ở cấp độ tài khoản quảng cáo
