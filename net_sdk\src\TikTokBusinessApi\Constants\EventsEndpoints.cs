/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Events endpoints
    /// </summary>
    public static class EventsEndpoints
    {
        /// <summary>
        /// Report an app event endpoint (v1.3)
        /// </summary>
        public const string ReportAppEvent = "/app/track/";

        /// <summary>
        /// Report app events in bulk endpoint (v1.3)
        /// </summary>
        public const string ReportAppEventsBatch = "/app/batch/";

        /// <summary>
        /// Get app info endpoint (v1.3)
        /// </summary>
        public const string GetAppInfo = "/app/info/";

        /// <summary>
        /// Report a web event endpoint (v1.3)
        /// </summary>
        public const string ReportWebEvent = "/pixel/track/";

        /// <summary>
        /// Report web events in bulk endpoint (v1.3)
        /// </summary>
        public const string ReportWebEventsBatch = "/pixel/batch/";

        /// <summary>
        /// Create an offline event set endpoint (v1.3)
        /// </summary>
        public const string CreateOfflineEventSet = "/offline/create/";

        /// <summary>
        /// Update an offline event set endpoint (v1.3)
        /// </summary>
        public const string UpdateOfflineEventSet = "/offline/update/";

        /// <summary>
        /// Get offline event sets endpoint (v1.3)
        /// </summary>
        public const string GetOfflineEventSets = "/offline/get/";

        /// <summary>
        /// Delete an offline event set endpoint (v1.3)
        /// </summary>
        public const string DeleteOfflineEventSet = "/offline/delete/";

        /// <summary>
        /// Report offline events endpoint (v1.3)
        /// </summary>
        public const string ReportOfflineEvents = "/offline/track/";

        /// <summary>
        /// Report offline events in bulk endpoint (v1.3)
        /// </summary>
        public const string ReportOfflineEventsBatch = "/offline/batch/";

        /// <summary>
        /// Get event sets endpoint (v1.3)
        /// </summary>
        public const string GetEventSets = "/event/get/";

        /// <summary>
        /// Create event set endpoint (v1.3)
        /// </summary>
        public const string CreateEventSet = "/event/create/";

        /// <summary>
        /// Update event set endpoint (v1.3)
        /// </summary>
        public const string UpdateEventSet = "/event/update/";

        /// <summary>
        /// Delete event set endpoint (v1.3)
        /// </summary>
        public const string DeleteEventSet = "/event/delete/";

        /// <summary>
        /// Get event set assets endpoint (v1.3)
        /// </summary>
        public const string GetEventSetAssets = "/event/asset/get/";

        /// <summary>
        /// Share event set endpoint (v1.3)
        /// </summary>
        public const string ShareEventSet = "/event/share/";

        /// <summary>
        /// Get shared event sets endpoint (v1.3)
        /// </summary>
        public const string GetSharedEventSets = "/event/shared/get/";

        /// <summary>
        /// Accept shared event set endpoint (v1.3)
        /// </summary>
        public const string AcceptSharedEventSet = "/event/shared/accept/";

        /// <summary>
        /// Reject shared event set endpoint (v1.3)
        /// </summary>
        public const string RejectSharedEventSet = "/event/shared/reject/";

        /// <summary>
        /// Get event set logs endpoint (v1.3)
        /// </summary>
        public const string GetEventSetLogs = "/event/log/get/";

        /// <summary>
        /// Get event set properties endpoint (v1.3)
        /// </summary>
        public const string GetEventSetProperties = "/event/property/get/";

        /// <summary>
        /// Get event set configs endpoint (v1.3)
        /// </summary>
        public const string GetEventSetConfigs = "/event/config/get/";

        /// <summary>
        /// Update event set configs endpoint (v1.3)
        /// </summary>
        public const string UpdateEventSetConfigs = "/event/config/update/";
    }
}
