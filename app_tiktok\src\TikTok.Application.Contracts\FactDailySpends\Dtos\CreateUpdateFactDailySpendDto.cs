using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace TikTok.FactDailySpends.Dtos
{
    public class CreateUpdateFactDailySpendDto : IEntityDto<Guid>
    {
        public Guid Id { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_Date (YYYYMMDD)
        /// </summary>
        [Required]
        public int DimDateId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_AdAccount
        /// </summary>
        [Required]
        public Guid DimAdAccountId { get; set; }

        /// <summary>
        /// Khóa ngoại liên kết với Dim_BusinessCenter
        /// </summary>
        [Required]
        public Guid DimBusinessCenterId { get; set; }

        /// <summary>
        /// ID nhà quảng cáo (Business Key)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// Tên nhà quảng cáo
        /// </summary>
        [Required]
        [StringLength(200)]
        public string AdvertiserName { get; set; }

        /// <summary>
        /// ID Business Center (Business Key)
        /// </summary>
        [Required]
        [StringLength(100)]
        public string BcId { get; set; }

        /// <summary>
        /// Tổng số tiền chi phí cho nhà quảng cáo
        /// </summary>
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Tổng số tiền phải lớn hơn hoặc bằng 0")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Số tiền chi phí tiền mặt
        /// </summary>
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Số tiền tiền mặt phải lớn hơn hoặc bằng 0")]
        public decimal CashAmount { get; set; }

        /// <summary>
        /// Số tiền chi phí tín dụng quảng cáo
        /// </summary>
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Số tiền tín dụng phải lớn hơn hoặc bằng 0")]
        public decimal GrantAmount { get; set; }

        /// <summary>
        /// Số tiền thuế ước tính
        /// </summary>
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Số tiền thuế phải lớn hơn hoặc bằng 0")]
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// Tiền tệ theo mã ISO 4217
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Currency { get; set; }

        /// <summary>
        /// Ngày chi tiêu (UTC)
        /// </summary>
        [Required]
        public DateTime Date { get; set; }
    }
}