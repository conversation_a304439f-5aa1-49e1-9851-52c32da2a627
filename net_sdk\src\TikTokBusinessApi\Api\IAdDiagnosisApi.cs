/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Ad Diagnosis operations
    /// </summary>
    public interface IAdDiagnosisApi
    {
        /// <summary>
        /// Get diagnoses, including possible issues and suggestions for corrections or improvements, for your active ad groups.
        /// Ad groups with no suggestions will not be returned.
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="filtering">Filtering conditions (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad diagnosis response containing list of ad groups with diagnoses</returns>
        Task<AdDiagnosisResponse> GetDiagnosisAsync(
            string advertiserId,
            AdDiagnosisFiltering? filtering = null,
            CancellationToken cancellationToken = default);
    }
}
