/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Catalog Video Templates endpoints
    /// </summary>
    public static class CatalogVideoTemplatesEndpoints
    {
        /// <summary>
        /// Get video packages endpoint (v1.3)
        /// </summary>
        public const string GetVideoPackages = "/catalog/video_package/get/";

        /// <summary>
        /// Create video package endpoint (v1.3) - Deprecated
        /// </summary>
        public const string CreateVideoPackage = "/catalog/video_package/create/";

        /// <summary>
        /// Update video package endpoint (v1.3) - Deprecated
        /// </summary>
        public const string UpdateVideoPackage = "/catalog/video_package/update/";

        /// <summary>
        /// Delete video package endpoint (v1.3) - Deprecated
        /// </summary>
        public const string DeleteVideoPackage = "/catalog/video_package/delete/";
    }
}
