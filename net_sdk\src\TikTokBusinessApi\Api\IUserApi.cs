/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API User operations
    /// </summary>
    public interface IUserApi
    {
        /// <summary>
        /// Get information about users who are authorized to access the developer App
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>User information response</returns>
        Task<UserInfoResponse> GetUserInfoAsync(CancellationToken cancellationToken = default);
    }
}
