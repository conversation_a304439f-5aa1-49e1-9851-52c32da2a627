using System;
using System.ComponentModel.DataAnnotations;

namespace TikTok.ReportIntegratedCampaigns
{
    /// <summary>
    /// DTO cho việc tạo mới báo cáo tích hợp Campaign
    /// </summary>
    public class CreateReportIntegratedCampaignDto
    {
        /// <summary>
        /// ID tài khoản nhà quảng cáo - Được hỗ trợ ở cấp độ Nhà quảng cáo, Chiến dịch, Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID chiến dịch
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CampaignId { get; set; }

        /// <summary>
        /// Ngày tổng hợp báo cáo
        /// </summary>
        [Required]
        public DateTime Date { get; set; }

        /// <summary>
        /// Chi tiêu - Tổng chi phí quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal Spend { get; set; }

        /// <summary>
        /// Chi phí Thanh toán bằng Tiền mặt - Số tiền ước tính bạn đã chi cho chiến dịch được thanh toán bằng tiền mặt
        /// </summary>
        [Required]
        public decimal CashSpend { get; set; }

        /// <summary>
        /// Chi phí Thanh toán bằng Phiếu giảm giá - Số tiền ước tính bạn đã chi cho chiến dịch được thanh toán bằng phiếu giảm giá
        /// </summary>
        [Required]
        public decimal VoucherSpend { get; set; }

        /// <summary>
        /// Lần hiển thị - Số lần quảng cáo của bạn được hiển thị
        /// </summary>
        [Required]
        public long Impressions { get; set; }

        /// <summary>
        /// Lần nhấp (đích đến) - Số lần nhấp từ quảng cáo của bạn đến một đích đến được chỉ định
        /// </summary>
        [Required]
        public long Clicks { get; set; }

        /// <summary>
        /// Chuyển đổi - Số lần quảng cáo của bạn dẫn đến sự kiện tối ưu hóa mà bạn đã chọn
        /// </summary>
        [Required]
        public long Conversion { get; set; }

        /// <summary>
        /// Chi phí mỗi chuyển đổi - Số tiền trung bình chi cho một chuyển đổi
        /// </summary>
        [Required]
        public decimal CostPerConversion { get; set; }

        /// <summary>
        /// Tỷ lệ chuyển đổi (CVR) - Tỷ lệ phần trăm chuyển đổi bạn nhận được trên tổng số lần hiển thị trên quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal ConversionRateV2 { get; set; }

        /// <summary>
        /// Kết quả - Số lần quảng cáo của bạn dẫn đến kết quả dự định dựa trên mục tiêu chiến dịch và mục tiêu tối ưu hóa của bạn
        /// </summary>
        [Required]
        public long Result { get; set; }

        /// <summary>
        /// Chi phí mỗi kết quả - Chi phí trung bình cho mỗi kết quả từ quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal CostPerResult { get; set; }

        /// <summary>
        /// Tên chiến dịch - Được hỗ trợ ở cấp độ Chiến dịch, Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [StringLength(255)]
        public string? CampaignName { get; set; }

        /// <summary>
        /// Mục tiêu quảng cáo - Được hỗ trợ ở cấp độ Chiến dịch, Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? ObjectiveType { get; set; }

        /// <summary>
        /// Loại khuyến mãi - Có thể là ứng dụng, trang web hoặc khác. Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? PromotionType { get; set; }

        /// <summary>
        /// Loại chiến dịch - Chiến dịch Chuyên dụng iOS14 hoặc chiến dịch thông thường
        /// </summary>
        [StringLength(100)]
        public string? CampaignDedicateType { get; set; }

        /// <summary>
        /// ROAS (Shop) - Lợi tức trên chi phí quảng cáo (ROAS) từ tổng doanh thu TikTok Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal OnsiteShoppingRoas { get; set; }

        /// <summary>
        /// Tổng doanh thu (Shop) - Tổng doanh thu của các đơn hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal TotalOnsiteShoppingValue { get; set; }

        /// <summary>
        /// Mua hàng (Shop) - Số lượng hành động gửi đơn hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public long OnsiteShopping { get; set; }

        /// <summary>
        /// Chi phí mỗi lần mua hàng (Shop) - Chi phí trung bình cho mỗi lần mua hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal CostPerOnsiteShopping { get; set; }

        /// <summary>
        /// Giá trị đơn hàng trung bình (Shop) - Giá trị đơn hàng trung bình cho mỗi lần mua hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal ValuePerOnsiteShopping { get; set; }

        /// <summary>
        /// Tiền tệ - Mã tiền tệ, ví dụ: USD
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Currency { get; set; }
    }
} 