# DimProductEntity

## Thông tin bảng dữ liệu

-   **Table**: `Dim_Product`
-   **TablePrefix**: `Dim_`
-   **TableName**: `Dim_Product`
-   **DbSchema**: `null`

## Mô tả

Dimension table cho Product từ TikTok Shop. Lưu trữ thông tin các sản phẩm trong TikTok Shop để phân tích hiệu suất GMV Max Campaign theo từng sản phẩm.

## Bảng dữ liệu: Dim_Product

| Tên Field            | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                                |
| -------------------- | ------------ | -------- | ------ | ---------------------------------------------------- |
| Id                   | Guid         | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                |
| ProductId            | string       | ✅       | 100    | Business Key - Product ID từ TikTok Shop             |
| ProductName          | string       | ✅       | 500    | Tên sản phẩm                                         |
| SKU                  | string       | ❌       | 100    | SKU của sản phẩm                                     |
| Description          | string       | ❌       | 2000   | Mô tả sản phẩm                                       |
| Category             | string       | ❌       | 200    | Danh mục chính                                       |
| SubCategory          | string       | ❌       | 200    | Danh mục phụ                                         |
| Brand                | string       | ❌       | 200    | Thương hiệu                                          |
| OriginalPrice        | decimal?     | ❌       | -      | Giá gốc                                              |
| CurrentPrice         | decimal?     | ❌       | -      | Giá bán hiện tại                                     |
| Currency             | string       | ❌       | 10     | Tiền tệ                                              |
| Status               | string       | ❌       | 20     | Trạng thái sản phẩm (ACTIVE, INACTIVE, OUT_OF_STOCK) |
| StockQuantity        | int?         | ❌       | -      | Số lượng tồn kho                                     |
| AverageRating        | decimal?     | ❌       | -      | Đánh giá trung bình                                  |
| ReviewCount          | int?         | ❌       | -      | Số lượng đánh giá                                    |
| SoldQuantity         | int?         | ❌       | -      | Số lượng đã bán                                      |
| MainImageUrl         | string       | ❌       | 500    | URL hình ảnh chính                                   |
| ProductUrl           | string       | ❌       | 500    | URL sản phẩm                                         |
| CreatedAt            | DateTime?    | ❌       | -      | Thời gian tạo sản phẩm                               |
| UpdatedAt            | DateTime?    | ❌       | -      | Thời gian cập nhật gần nhất                          |
| EffectiveStartDate   | DateTime     | ✅       | -      | Ngày bắt đầu hiệu lực (SCD Type 2)                   |
| EffectiveEndDate     | DateTime?    | ❌       | -      | Ngày kết thúc hiệu lực (SCD Type 2)                  |
| CreationTime         | DateTime     | ✅       | -      | Thời gian tạo bản ghi (AuditedEntity)                |
| CreatorId            | Guid?        | ❌       | -      | ID người tạo (AuditedEntity)                         |
| LastModificationTime | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (AuditedEntity)               |
| LastModifierId       | Guid?        | ❌       | -      | ID người sửa đổi cuối (AuditedEntity)                |

## Cấu trúc dữ liệu

### Business Key

-   **ProductId**: ID sản phẩm từ TikTok Shop API

### Thông tin cơ bản

-   **ProductName**: Tên hiển thị của sản phẩm
-   **SKU**: Mã SKU (Stock Keeping Unit) của sản phẩm
-   **Description**: Mô tả chi tiết về sản phẩm
-   **MainImageUrl**: Link hình ảnh chính của sản phẩm
-   **ProductUrl**: Link trực tiếp đến sản phẩm trên TikTok Shop

### Phân loại sản phẩm

-   **Category**: Danh mục chính của sản phẩm
-   **SubCategory**: Danh mục phụ (nếu có)
-   **Brand**: Thương hiệu sản phẩm

### Thông tin giá cả

-   **OriginalPrice**: Giá gốc của sản phẩm
-   **CurrentPrice**: Giá bán hiện tại (có thể khác với giá gốc do khuyến mãi)
-   **Currency**: Đơn vị tiền tệ

### Trạng thái và tồn kho

-   **Status**: Trạng thái hiện tại của sản phẩm
-   **StockQuantity**: Số lượng còn trong kho
-   **SoldQuantity**: Số lượng đã bán được

### Đánh giá và phản hồi

-   **AverageRating**: Điểm đánh giá trung bình (thang điểm 5)
-   **ReviewCount**: Tổng số lượt đánh giá

### SCD Type 2 (Slowly Changing Dimension)

-   **EffectiveStartDate**: Ngày bắt đầu hiệu lực của phiên bản
-   **EffectiveEndDate**: Ngày kết thúc hiệu lực (NULL nếu là phiên bản hiện tại)

## Navigation Properties

-   **FactGmvMaxProduct**: One-to-Many, các bản ghi hiệu suất sản phẩm

## Mục đích sử dụng

-   Phân tích hiệu suất GMV Max theo từng sản phẩm
-   So sánh hiệu suất giữa các sản phẩm
-   Quản lý danh sách sản phẩm TikTok Shop
-   Hỗ trợ drill-down analysis từ campaign xuống product level
-   Tracking lịch sử thay đổi thông tin sản phẩm
-   Phân tích theo danh mục và thương hiệu

## Quan hệ với các entity khác

-   **FactGmvMaxProductEntity**: One-to-Many, hiệu suất sản phẩm chi tiết

## Use Cases thực tế

### Product Performance Analysis

```sql
-- Phân tích hiệu suất theo sản phẩm
SELECT
    p.ProductName,
    p.Category,
    p.Brand,
    p.CurrentPrice,
    SUM(f.GrossRevenue) as TotalRevenue,
    AVG(f.ROI) as AverageROI,
    SUM(f.Orders) as TotalOrders,
    SUM(f.QuantitySold) as TotalQuantitySold,
    SUM(f.Cost) as TotalCost
FROM Fact_GmvMaxProduct f
JOIN Dim_Product p ON f.ProductKey = p.Id
GROUP BY p.ProductName, p.Category, p.Brand, p.CurrentPrice
ORDER BY TotalRevenue DESC
```

### Category Performance

```sql
-- Phân tích hiệu suất theo danh mục
SELECT
    p.Category,
    COUNT(DISTINCT p.Id) as ProductCount,
    SUM(f.GrossRevenue) as TotalRevenue,
    AVG(f.ROI) as AverageROI,
    SUM(f.Orders) as TotalOrders
FROM Fact_GmvMaxProduct f
JOIN Dim_Product p ON f.ProductKey = p.Id
GROUP BY p.Category
ORDER BY TotalRevenue DESC
```

### Brand Performance

```sql
-- Phân tích hiệu suất theo thương hiệu
SELECT
    p.Brand,
    COUNT(DISTINCT p.Id) as ProductCount,
    SUM(f.GrossRevenue) as TotalRevenue,
    AVG(f.ROI) as AverageROI,
    SUM(f.Orders) as TotalOrders
FROM Fact_GmvMaxProduct f
JOIN Dim_Product p ON f.ProductKey = p.Id
WHERE p.Brand IS NOT NULL
GROUP BY p.Brand
ORDER BY TotalRevenue DESC
```

### Product Comparison

```sql
-- So sánh hiệu suất giữa các sản phẩm
SELECT
    p1.ProductName as Product1,
    p2.ProductName as Product2,
    f1.ROI as ROI1,
    f2.ROI as ROI2,
    f1.Orders as Orders1,
    f2.Orders as Orders2,
    f1.Cost as Cost1,
    f2.Cost as Cost2
FROM Fact_GmvMaxProduct f1
JOIN Dim_Product p1 ON f1.ProductKey = p1.Id
JOIN Fact_GmvMaxProduct f2 ON f1.DateKey = f2.DateKey
JOIN Dim_Product p2 ON f2.ProductKey = p2.Id
WHERE p1.Id != p2.Id
  AND p1.Category = p2.Category
```

### Product History Tracking

```sql
-- Theo dõi lịch sử thay đổi thông tin sản phẩm
SELECT
    ProductId,
    ProductName,
    CurrentPrice,
    Status,
    EffectiveStartDate,
    EffectiveEndDate
FROM Dim_Product
WHERE ProductId = @ProductId
ORDER BY EffectiveStartDate DESC
```

### Price Performance Analysis

```sql
-- Phân tích hiệu suất theo mức giá
SELECT
    CASE
        WHEN p.CurrentPrice < 100000 THEN 'Under 100k'
        WHEN p.CurrentPrice < 500000 THEN '100k-500k'
        WHEN p.CurrentPrice < 1000000 THEN '500k-1M'
        ELSE 'Over 1M'
    END as PriceRange,
    COUNT(DISTINCT p.Id) as ProductCount,
    SUM(f.GrossRevenue) as TotalRevenue,
    AVG(f.ROI) as AverageROI
FROM Fact_GmvMaxProduct f
JOIN Dim_Product p ON f.ProductKey = p.Id
WHERE p.CurrentPrice IS NOT NULL
GROUP BY
    CASE
        WHEN p.CurrentPrice < 100000 THEN 'Under 100k'
        WHEN p.CurrentPrice < 500000 THEN '100k-500k'
        WHEN p.CurrentPrice < 1000000 THEN '500k-1M'
        ELSE 'Over 1M'
    END
ORDER BY TotalRevenue DESC
```
