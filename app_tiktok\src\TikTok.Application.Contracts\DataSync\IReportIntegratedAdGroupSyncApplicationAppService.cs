using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service interface cho việc đồng bộ dữ liệu báo cáo tích hợp AdGroup
    /// </summary>
    public interface IReportIntegratedAdGroupSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdGroup cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<ReportIntegratedAdGroupSyncResult> SyncAllReportIntegratedAdGroupForAllBcsAsync();

        /// <summary>
        /// Đồng bộ báo cáo tích hợp AdGroup theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> bắ<PERSON> đầu (tù<PERSON> chọn)</param>
        /// <param name="endDate"><PERSON><PERSON><PERSON> kế<PERSON> thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<ReportIntegratedAdGroupSyncResult> SyncReportIntegratedAdGroupAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null);
    }
}