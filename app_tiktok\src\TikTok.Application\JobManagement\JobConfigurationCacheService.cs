using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using TikTok.Consts;
using TikTok.Entities;
using TikTok.Repositories;
using TikTok.Cache;
using Volo.Abp.Caching;
using Volo.Abp.DependencyInjection;
using Volo.Abp.ObjectMapping;

namespace TikTok.JobManagement
{
    /// <summary>
    /// Service implementation cho cache cấu hình job
    /// </summary>
    public class JobConfigurationCacheService : IJobConfigurationCacheService, ICacheService
    {
        private readonly ILogger<JobConfigurationCacheService> _logger;
        private readonly IJobConfigurationRepository _jobConfigurationRepository;
        private readonly IDistributedCache<JobConfigurationDto, string> _configurationCache;
        private readonly IObjectMapper _mapper;

        // Cache key constants
        private const string CurrentConfigurationCacheKey = "JobConfiguration:Current";
        private static readonly TimeSpan CacheExpiration = CacheConst.CACHE_EXPIRATION;

        public JobConfigurationCacheService(
            ILogger<JobConfigurationCacheService> logger,
            IJobConfigurationRepository jobConfigurationRepository,
            IDistributedCache<JobConfigurationDto, string> configurationCache,
            IObjectMapper mapper)
        {
            _logger = logger;
            _jobConfigurationRepository = jobConfigurationRepository;
            _configurationCache = configurationCache;
            _mapper = mapper;
        }

        /// <summary>
        /// Lấy cấu hình hiện tại với cache
        /// </summary>
        /// <returns>Cấu hình hiện tại</returns>
        public async Task<JobConfigurationDto> GetCurrentConfigurationWithCacheAsync()
        {
            var configuration = await _configurationCache.GetOrAddAsync(
                CurrentConfigurationCacheKey,
                async () =>
                {
                    var config = await _jobConfigurationRepository.GetCurrentConfigurationAsync();
                    return _mapper.Map<JobConfigurationEntity, JobConfigurationDto>(config);
                },
                () => new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = CacheExpiration
                });
            return configuration;
        }

        /// <summary>
        /// Clear cache của cấu hình job
        /// </summary>
        public async Task ClearConfigurationCacheAsync()
        {
            try
            {
                await _configurationCache.RemoveAsync(CurrentConfigurationCacheKey);
                _logger.LogDebug("Configuration cache cleared successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear configuration cache");
            }
        }

        // ICacheService implementation
        public string CacheName => "Job Configuration Cache";

        public async Task<List<string>> GetCacheKeysAsync()
        {
            return new List<string> { CurrentConfigurationCacheKey };
        }

        public async Task<bool> ClearCacheAsync()
        {
            try
            {
                await _configurationCache.RemoveAsync(CurrentConfigurationCacheKey);
                _logger.LogDebug("Configuration cache cleared successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear configuration cache");
                return false;
            }
        }

        public async Task<bool> ClearCacheByKeyAsync(string cacheKey)
        {
            try
            {
                if (cacheKey == CurrentConfigurationCacheKey)
                {
                    await _configurationCache.RemoveAsync(cacheKey);
                    _logger.LogDebug("Configuration cache cleared successfully for key: {Key}", cacheKey);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear configuration cache for key: {Key}", cacheKey);
                return false;
            }
        }

        public async Task<CacheInfoDto> GetCacheInfoAsync()
        {
            var cacheKeys = await GetCacheKeysAsync();
            return new CacheInfoDto
            {
                CacheName = CacheName,
                ActiveKeysCount = cacheKeys.Count,
                CacheKeys = cacheKeys,
                CreatedAt = DateTime.UtcNow,
                LastAccessedAt = DateTime.UtcNow,
                LastModifiedAt = DateTime.UtcNow,
                Status = "Active",
                Description = "Cache cho cấu hình Job hiện tại"
            };
        }
    }
}