using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.ReportIntegratedAds
{
    /// <summary>
    /// Interface cho ReportIntegratedAd AppService
    /// </summary>
    public interface IReportIntegratedAdAppService :
        ICrudAppService<
            ReportIntegratedAdDto,
            Guid,
            GetReportIntegratedAdListDto,
            CreateReportIntegratedAdDto,
            UpdateReportIntegratedAdDto>
    {
        /// <summary>
        /// Lấy danh sách báo cáo tích hợp Ad theo điều kiện tìm kiếm
        /// </summary>
        /// <param name="input">Thông tin tìm kiếm</param>
        /// <returns>Danh sách báo cáo tích hợp Ad</returns>
        Task<PagedResultDto<ReportIntegratedAdDto>> GetListAsync(GetReportIntegratedAdListDto input);

        /// <summary>
        /// Lấy báo cáo tích hợp Ad theo ID
        /// </summary>
        /// <param name="id">ID của báo cáo</param>
        /// <returns>Báo cáo tích hợp Ad</returns>
        Task<ReportIntegratedAdDto> GetAsync(Guid id);

        /// <summary>
        /// Tạo mới báo cáo tích hợp Ad
        /// </summary>
        /// <param name="input">Thông tin báo cáo</param>
        /// <returns>Báo cáo tích hợp Ad đã tạo</returns>
        Task<ReportIntegratedAdDto> CreateAsync(CreateReportIntegratedAdDto input);

        /// <summary>
        /// Cập nhật báo cáo tích hợp Ad
        /// </summary>
        /// <param name="id">ID của báo cáo</param>
        /// <param name="input">Thông tin cập nhật</param>
        /// <returns>Báo cáo tích hợp Ad đã cập nhật</returns>
        Task<ReportIntegratedAdDto> UpdateAsync(Guid id, UpdateReportIntegratedAdDto input);

        /// <summary>
        /// Xóa báo cáo tích hợp Ad
        /// </summary>
        /// <param name="id">ID của báo cáo</param>
        /// <returns>Task</returns>
        Task DeleteAsync(Guid id);
    }
} 