﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.BusinessApplications;
using TikTok.Entities;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Entities.Events;
using Volo.Abp.EventBus;
using Volo.Abp.EventBus.Distributed;

namespace TikTok.EventHandles
{
    public class BusinessApplicationEventHandler : ILocalEventHandler<EntityChangedEventData<BusinessApplicationEntity>>, ITransientDependency
    {
        private readonly IBusinessApplicationCache _cache;

        public BusinessApplicationEventHandler(IBusinessApplicationCache cache)
        {
            _cache = cache;
        }

        public async Task HandleEventAsync(EntityChangedEventData<BusinessApplicationEntity> eventData)
        {
            if (eventData == null || eventData.Entity == null)
            {
                return;
            }

            await _cache.CleanCache();
        }
    }
}