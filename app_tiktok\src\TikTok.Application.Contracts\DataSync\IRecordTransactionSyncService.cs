using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service interface cho việc đồng bộ dữ liệu bản ghi giao dịch BC và AdAccount
    /// </summary>
    public interface IRecordTransactionSyncService : ITransientDependency
    {
        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch BC theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<RecordTransactionSyncResult> SyncBcTransactionsAsync(string bcId);

        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch AdAccount theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<RecordTransactionSyncResult> SyncAdAccountTransactionsRecordAsync(string bcId);

        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<RecordTransactionSyncResult> SyncAllRecordTransactionsAsync();

        /// <summary>
        /// Đồng bộ dữ liệu bản ghi giao dịch cho Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<RecordTransactionSyncResult> SyncAllRecordTransactionsForBcAsync(string bcId);
    }

    /// <summary>
    /// Kết quả đồng bộ dữ liệu bản ghi giao dịch
    /// </summary>
    public class RecordTransactionSyncResult : SyncResultBase
    {
        
    }
}