# FactCampaignEntity

## Thông tin bảng dữ liệu

-   **Table**: `FactCampaigns`
-   **TablePrefix**: `Fact_`
-   **TableName**: `Fact_FactCampaigns`
-   **DbSchema**: `TikTok`

## Mô tả

Fact table lưu trữ **hiệu suất chiến dịch quảng cáo theo ngày**. Chỉ chứa performance metrics, configuration data được lưu trong Dim_Campaign.

## Cấu trúc bảng

| Tên Field                | Kiểu dữ liệu | Bắt buộc | Đ<PERSON> dài | Mô tả                                       |
| ------------------------ | ------------ | -------- | ------ | ------------------------------------------- |
| Id                       | Guid         | ✓        | -      | ID duy nhất của bản <PERSON>hi (Primary Key)       |
| DimDateId                | int          | ✓        | -      | Khóa ngoại liên kết với Dim_Date (YYYYMMDD) |
| DimAdAccountId           | Guid         | ✓        | -      | Khóa ngoại liên kết với Dim_AdAccount       |
| DimBusinessCenterId      | Guid         | ✓        | -      | Khóa ngoại liên kết với Dim_BusinessCenter  |
| DimCampaignId            | Guid         | ✓        | -      | Khóa ngoại liên kết với Dim_Campaign        |
| CampaignId               | string       | ✓        | 100    | ID chiến dịch (Business Key)                |
| CampaignName             | string       | ✓        | 255    | Tên chiến dịch                              |
| AdvertiserId             | string       | ✓        | 100    | ID tài khoản quảng cáo                      |
| AdvertiserName           | string       | ✗        | 255    | Tên tài khoản quảng cáo                     |
| BcId                     | string       | ✓        | 100    | ID Business Center                          |
| ObjectiveType            | string       | ✓        | 100    | Mục tiêu quảng cáo                          |
| CampaignDedicateType     | string       | ✗        | 100    | Loại chiến dịch chuyên dụng                 |
| Spend                    | decimal      | ✓        | 18,2   | Chi tiêu                                    |
| Impressions              | long         | ✓        | -      | Lượt hiển thị                               |
| Clicks                   | long         | ✓        | -      | Lượt nhấp                                   |
| Conversion               | long         | ✓        | -      | Lượt chuyển đổi                             |
| CostPerConversion        | decimal      | ✓        | 18,2   | Chi phí mỗi lượt chuyển đổi                 |
| ConversionRate           | decimal      | ✓        | 18,4   | Tỷ lệ chuyển đổi                            |
| Result                   | long         | ✓        | -      | Kết quả                                     |
| CostPerResult            | decimal      | ✓        | 18,2   | Chi phí mỗi kết quả                         |
| OnsiteShoppingRoas       | decimal      | ✓        | 18,4   | ROAS TikTok Shop                            |
| TotalOnsiteShoppingValue | decimal      | ✓        | 18,2   | Tổng doanh thu TikTok Shop                  |
| OnsiteShopping           | long         | ✓        | -      | Lượt mua hàng Shop                          |
| CostPerOnsiteShopping    | decimal      | ✓        | 18,2   | Chi phí mỗi lượt mua Shop                   |
| ValuePerOnsiteShopping   | decimal      | ✓        | 18,2   | Giá trị đơn hàng trung bình Shop            |
| Currency                 | string       | ✓        | 10     | Tiền tệ                                     |
| Date                     | DateTime     | ✓        | -      | Ngày báo cáo (UTC)                          |
| CreationTime             | DateTime     | ✓        | -      | Thời gian tạo bản ghi                       |

## ETL Mapping từ RawReportIntegratedCampaignEntity

-   **CampaignId** ← RawReportIntegratedCampaignEntity.CampaignId
-   **CampaignName** ← RawReportIntegratedCampaignEntity.CampaignName
-   **AdvertiserId** ← RawReportIntegratedCampaignEntity.AdvertiserId
-   **AdvertiserName** ← RawReportIntegratedCampaignEntity.AdvertiserName
-   **BcId** ← RawReportIntegratedCampaignEntity.BcId
-   **ObjectiveType** ← RawReportIntegratedCampaignEntity.ObjectiveType
-   **CampaignDedicateType** ← RawReportIntegratedCampaignEntity.CampaignDedicateType
-   **Spend** ← RawReportIntegratedCampaignEntity.Spend
-   **Impressions** ← RawReportIntegratedCampaignEntity.Impressions
-   **Clicks** ← RawReportIntegratedCampaignEntity.Clicks
-   **Conversion** ← RawReportIntegratedCampaignEntity.Conversion
-   **CostPerConversion** ← RawReportIntegratedCampaignEntity.CostPerConversion
-   **ConversionRate** ← RawReportIntegratedCampaignEntity.ConversionRateV2
-   **Result** ← RawReportIntegratedCampaignEntity.Result
-   **CostPerResult** ← RawReportIntegratedCampaignEntity.CostPerResult
-   **OnsiteShoppingRoas** ← RawReportIntegratedCampaignEntity.OnsiteShoppingRoas
-   **TotalOnsiteShoppingValue** ← RawReportIntegratedCampaignEntity.TotalOnsiteShoppingValue
-   **OnsiteShopping** ← RawReportIntegratedCampaignEntity.OnsiteShopping
-   **CostPerOnsiteShopping** ← RawReportIntegratedCampaignEntity.CostPerOnsiteShopping
-   **ValuePerOnsiteShopping** ← RawReportIntegratedCampaignEntity.ValuePerOnsiteShopping
-   **Currency** ← RawReportIntegratedCampaignEntity.Currency
-   **Date** ← RawReportIntegratedCampaignEntity.Date

## Grain Definition

-   **Mỗi dòng đại diện cho**: Hiệu suất 1 chiến dịch trong 1 ngày
-   **Unique Key**: (CampaignId, DateKey)

## Business Questions

### 1. Hiệu suất chiến dịch cơ bản

```sql
SELECT CampaignName, Spend, TotalOnsiteShoppingValue,
       TotalOnsiteShoppingValue/Spend as ROAS,
       ConversionRate
FROM Fact_ChienDich
WHERE DateKey >= ********
ORDER BY ROAS DESC
```

### 2. Top chiến dịch GMV Max

```sql
SELECT CampaignName, ObjectiveType,
       SUM(Spend) as TotalSpend,
       SUM(TotalOnsiteShoppingValue) as TotalRevenue,
       SUM(TotalOnsiteShoppingValue)/SUM(Spend) as ROAS
FROM Fact_ChienDich
WHERE TotalOnsiteShoppingValue > 0
GROUP BY CampaignName, ObjectiveType
ORDER BY ROAS DESC
```

### 3. Hiệu suất vs Ngân sách (kết hợp với Dim_Campaign)

```sql
SELECT fc.CampaignName,
       SUM(fc.Spend) as ActualSpend,
       dc.Budget,
       SUM(fc.Spend)/dc.Budget as BudgetUtilization,
       AVG(fc.ConversionRate) as AvgCVR
FROM Fact_ChienDich fc
JOIN Dim_Campaign dc ON fc.CampaignKey = dc.CampaignKey
WHERE dc.IsCurrent = 1 AND dc.Budget > 0
GROUP BY fc.CampaignName, dc.Budget
ORDER BY BudgetUtilization DESC
```

### 4. Phân tích theo mục tiêu quảng cáo

```sql
SELECT ObjectiveType,
       COUNT(DISTINCT CampaignId) as CampaignCount,
       SUM(Spend) as TotalSpend,
       AVG(ConversionRate) as AvgCVR,
       AVG(OnsiteShoppingRoas) as AvgROAS
FROM Fact_ChienDich
WHERE DateKey >= ********
GROUP BY ObjectiveType
ORDER BY TotalSpend DESC
```

### 5. Xu hướng hiệu suất theo thời gian

```sql
SELECT d.YearMonth,
       SUM(fc.Spend) as MonthlySpend,
       SUM(fc.TotalOnsiteShoppingValue) as MonthlyRevenue,
       AVG(fc.ConversionRate) as AvgCVR
FROM Fact_ChienDich fc
JOIN Dim_Date d ON fc.DateKey = d.DateKey
GROUP BY d.YearMonth
ORDER BY d.YearMonth
```

## Relationships

### Foreign Keys

-   **DimDateId** → DimDates.Id
-   **DimAdAccountId** → DimAdAccounts.Id
-   **DimBusinessCenterId** → DimBusinessCenters.Id
-   **DimCampaignId** → DimCampaigns.Id

### Referenced By

-   Được join với Dim_Campaign để lấy configuration data

## Measures (Metrics)

### Performance Measures

-   **Spend**: Chi tiêu quảng cáo
-   **Impressions**: Lượt hiển thị
-   **Clicks**: Lượt nhấp
-   **Conversion**: Lượt chuyển đổi
-   **Result**: Kết quả đạt được

### TikTok Shop Measures

-   **TotalOnsiteShoppingValue**: Doanh thu Shop
-   **OnsiteShopping**: Lượt mua hàng
-   **OnsiteShoppingRoas**: ROAS Shop

### Calculated Measures

-   **CTR**: Clicks / Impressions
-   **CPC**: Spend / Clicks
-   **CPM**: Spend / Impressions \* 1000
-   **ROAS**: TotalOnsiteShoppingValue / Spend
-   **AOV**: ValuePerOnsiteShopping (trực tiếp từ data)

## Indexes

### Primary Index

-   **PK_Fact_ChienDich**: Id (Clustered)

### Unique Index

-   **UK_Fact_ChienDich_Campaign_Date**: (CampaignId, DimDateId) UNIQUE

### Foreign Key Indexes

-   **IX_Fact_ChienDich_DimDateId**: DimDateId
-   **IX_Fact_ChienDich_DimAdAccountId**: DimAdAccountId
-   **IX_Fact_ChienDich_DimBusinessCenterId**: DimBusinessCenterId
-   **IX_Fact_ChienDich_DimCampaignId**: DimCampaignId

### Performance Indexes

-   **IX_Fact_ChienDich_Spend**: Spend
-   **IX_Fact_ChienDich_ROAS**: OnsiteShoppingRoas
-   **IX_Fact_ChienDich_ObjectiveType**: ObjectiveType

## Performance Considerations

### Data Volume

-   **Estimated**: ~100K campaigns × 30 days = 3M records/month
-   **Growth**: Linear with campaign volume

### Partitioning

-   **Monthly partition** theo DimDateId (YYYYMM)
-   **Archive**: Data older than 2 years

### ETL Performance

-   **Single source**: Chỉ từ RawReportIntegratedCampaignEntity
-   **Incremental load**: Theo Date field
-   **Bulk insert**: Daily batch processing
