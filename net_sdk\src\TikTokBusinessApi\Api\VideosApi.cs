/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Videos operations
    /// </summary>
    public class VideosApi : IVideosApi
    {
        private readonly IApiClient _apiClient;

        /// <summary>
        /// Initializes a new instance of the VideosApi class
        /// </summary>
        /// <param name="apiClient">API client for making requests</param>
        public VideosApi(IApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new System.ArgumentNullException(nameof(apiClient));
        }

        /// <inheritdoc />
        // public async Task<VideoUploadResponse> UploadVideoAsync(
        //     VideoUploadRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     System.ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     // Validate upload type specific requirements
        //     switch (request.UploadType)
        //     {
        //         case "UPLOAD_BY_FILE":
        //             if (string.IsNullOrWhiteSpace(request.VideoSignature))
        //                 throw new System.ArgumentException("Video signature is required when upload_type is UPLOAD_BY_FILE", nameof(request));
        //             break;
        //         case "UPLOAD_BY_URL":
        //             if (string.IsNullOrWhiteSpace(request.VideoUrl))
        //                 throw new System.ArgumentException("Video URL is required when upload_type is UPLOAD_BY_URL", nameof(request));
        //             break;
        //         case "UPLOAD_BY_FILE_ID":
        //             if (string.IsNullOrWhiteSpace(request.FileId))
        //                 throw new System.ArgumentException("File ID is required when upload_type is UPLOAD_BY_FILE_ID", nameof(request));
        //             break;
        //         case "UPLOAD_BY_VIDEO_ID":
        //             if (string.IsNullOrWhiteSpace(request.VideoId))
        //                 throw new System.ArgumentException("Video ID is required when upload_type is UPLOAD_BY_VIDEO_ID", nameof(request));
        //             break;
        //     }

        //     return await _apiClient.CallApiAsync<VideoUploadResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{VideosEndpoints.Upload}",
        //         System.Net.Http.HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        // public async Task UpdateVideoAsync(
        //     VideoUpdateRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     System.ArgumentNullException.ThrowIfNull(request);

        //     if (string.IsNullOrWhiteSpace(request.AdvertiserId))
        //         throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.FileName))
        //         throw new System.ArgumentException("File name cannot be null or empty", nameof(request));

        //     if (string.IsNullOrWhiteSpace(request.VideoId))
        //         throw new System.ArgumentException("Video ID cannot be null or empty", nameof(request));

        //     await _apiClient.CallApiAsync<object>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{VideosEndpoints.Update}",
        //         System.Net.Http.HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <inheritdoc />
        public async Task<VideoDetailsResponse> GetVideoInfoAsync(
            VideoDetailsRequest request,
            CancellationToken cancellationToken = default)
        {
            System.ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (request.VideoIds == null || request.VideoIds.Count == 0)
                throw new System.ArgumentException("At least one video ID must be provided", nameof(request));

            if (request.VideoIds.Count > 60)
                throw new System.ArgumentException("Maximum 60 video IDs are allowed", nameof(request));

            return await _apiClient.CallApiAsync<VideoDetailsResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{VideosEndpoints.GetInfo}",
                System.Net.Http.HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<VideoSearchResponse> SearchVideosAsync(
            VideoSearchRequest request,
            CancellationToken cancellationToken = default)
        {
            System.ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (request.Page < 1)
                throw new System.ArgumentException("Page must be greater than 0", nameof(request));

            if (request.PageSize < 1 || request.PageSize > 100)
                throw new System.ArgumentException("Page size must be between 1 and 100", nameof(request));

            // Validate filtering constraints if provided
            if (request.Filtering != null)
            {
                if (request.Filtering.VideoIds != null && request.Filtering.VideoIds.Count > 100)
                    throw new System.ArgumentException("Maximum 100 video IDs are allowed in filtering", nameof(request));

                if (request.Filtering.MaterialIds != null && request.Filtering.MaterialIds.Count > 20)
                    throw new System.ArgumentException("Maximum 20 material IDs are allowed in filtering", nameof(request));
            }

            return await _apiClient.CallApiAsync<VideoSearchResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{VideosEndpoints.Search}",
                System.Net.Http.HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <inheritdoc />
        public async Task<VideoThumbnailResponse> GetVideoThumbnailsAsync(
            VideoThumbnailRequest request,
            CancellationToken cancellationToken = default)
        {
            System.ArgumentNullException.ThrowIfNull(request);

            if (string.IsNullOrWhiteSpace(request.AdvertiserId))
                throw new System.ArgumentException("Advertiser ID cannot be null or empty", nameof(request));

            if (string.IsNullOrWhiteSpace(request.VideoId))
                throw new System.ArgumentException("Video ID cannot be null or empty", nameof(request));

            if (request.PosterNumber < 1 || request.PosterNumber > 10)
                throw new System.ArgumentException("Poster number must be between 1 and 10", nameof(request));

            return await _apiClient.CallApiAsync<VideoThumbnailResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{VideosEndpoints.GetSuggestedThumbnails}",
                System.Net.Http.HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
