using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.BusinessCenters
{
    /// <summary>
    /// Interface cho service quản lý trung tâm kinh doanh
    /// </summary>
    public interface IBusinessCenterAppService :
        ICrudAppService<
            BusinessCenterDto,                     // Used to show business centers
            Guid,                                  // Primary key of the business center entity
            GetBusinessCenterListDto,              // Used for paging/sorting
            CreateBusinessCenterDto,               // Used to create a new business center
            UpdateBusinessCenterDto>               // Used to update a business center
    {
        /// <summary>
        /// Lấy trung tâm kinh doanh theo BC ID
        /// </summary>
        /// <param name="bcId">BC ID</param>
        /// <returns>Thông tin trung tâm kinh doanh</returns>
        Task<BusinessCenterDto> GetByBcIdAsync(string bcId);

        /// <summary>
        /// Kiểm tra BC ID đã tồn tại chưa
        /// </summary>
        /// <param name="bcId">BC ID cần kiểm tra</param>
        /// <returns>True nếu đã tồn tại, False nếu chưa</returns>
        Task<bool> IsBcIdExistsAsync(string bcId);
    }
} 