/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Media Mix Modeling operations
    /// </summary>
    public interface IMediaMixModelingApi
    {
        /// <summary>
        /// Create a Media Mix Modeling (MMM) data request.
        /// After you create the data request, wait for up to 24 hours for the data processing to complete.
        /// Then use CheckMmmDataRequestStatusAsync to check the data request status.
        /// </summary>
        /// <param name="request">MMM data request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the MMM request ID</returns>
        //Task<MmmCreateResponse> CreateMmmDataRequestAsync(
        //    MmmCreateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Check the status of a Media Mix Modeling (MMM) data request.
        /// Once the status of the data request is "completed - ready for download",
        /// you can use GetMmmDataDownloadUrlAsync to obtain a download URL for the data.
        /// </summary>
        /// <param name="mmmRequestId">Unique identifier for the MMM data request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the request status</returns>
        //Task<MmmCheckResponse> CheckMmmDataRequestStatusAsync(
        //    string mmmRequestId,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Obtain the download URL for Media Mix Modeling (MMM) data.
        /// The URL is valid for seven days. Once the URL expires, you need to call this method again to obtain a new URL.
        /// </summary>
        /// <param name="mmmRequestId">Unique identifier for the MMM data request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the download URL</returns>
        Task<MmmDownloadResponse> GetMmmDataDownloadUrlAsync(
            string mmmRequestId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieve the details of historical Media Mix Modeling (MMM) data requests.
        /// </summary>
        /// <param name="request">Request parameters including date range</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing historical request details</returns>
        Task<MmmHistoryResponse> GetMmmDataRequestHistoryAsync(
            MmmHistoryRequest request,
            CancellationToken cancellationToken = default);
    }
}
