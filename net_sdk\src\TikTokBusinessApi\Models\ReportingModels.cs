/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Synchronous Report Models

    /// <summary>
    /// Request for running a synchronous report
    /// </summary>
    public class SynchronousReportRequest
    {
        /// <summary>
        /// Advertiser ID (conditional)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// List of advertiser IDs (conditional)
        /// </summary>
        [JsonPropertyName("advertiser_ids")]
        public List<string>? AdvertiserIds { get; set; }

        /// <summary>
        /// Business Center ID (conditional)
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string? BcId { get; set; }

        /// <summary>
        /// Ad service type
        /// </summary>
        [JsonPropertyName("service_type")]
        public string? ServiceType { get; set; }

        /// <summary>
        /// Report type (Required)
        /// </summary>
        [JsonPropertyName("report_type")]
        public string ReportType { get; set; } = string.Empty;

        /// <summary>
        /// Data level (conditional)
        /// </summary>
        [JsonPropertyName("data_level")]
        public string? DataLevel { get; set; }

        /// <summary>
        /// Grouping conditions (Required)
        /// </summary>
        [JsonPropertyName("dimensions")]
        public List<string> Dimensions { get; set; } = new();

        /// <summary>
        /// Metrics to query
        /// </summary>
        [JsonPropertyName("metrics")]
        public List<string>? Metrics { get; set; }

        /// <summary>
        /// Whether to enable total metrics
        /// </summary>
        [JsonPropertyName("enable_total_metrics")]
        public bool? EnableTotalMetrics { get; set; }

        /// <summary>
        /// Query start date (conditional)
        /// </summary>
        [JsonPropertyName("start_date")]
        public string? StartDate { get; set; }

        /// <summary>
        /// Query end date (conditional)
        /// </summary>
        [JsonPropertyName("end_date")]
        public string? EndDate { get; set; }

        /// <summary>
        /// Whether to request lifetime metrics
        /// </summary>
        [JsonPropertyName("query_lifetime")]
        public bool? QueryLifetime { get; set; }

        /// <summary>
        /// Whether to set returned metrics in UTC timezone for all advertisers
        /// </summary>
        [JsonPropertyName("multi_adv_report_in_utc_time")]
        public bool? MultiAdvReportInUtcTime { get; set; }

        /// <summary>
        /// Sorting field
        /// </summary>
        [JsonPropertyName("order_field")]
        public string? OrderField { get; set; }

        /// <summary>
        /// Sorting order
        /// </summary>
        [JsonPropertyName("order_type")]
        public string? OrderType { get; set; }

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public List<ReportFilter>? Filtering { get; set; }

        /// <summary>
        /// Query mode (deprecated)
        /// </summary>
        [JsonPropertyName("query_mode")]
        public string? QueryMode { get; set; }

        /// <summary>
        /// Current page number
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Response for synchronous report
    /// </summary>
    public class SynchronousReportResponse
    {
        /// <summary>
        /// Total metrics data
        /// </summary>
        [JsonPropertyName("total_metrics")]
        public Dictionary<string, object>? TotalMetrics { get; set; }

        /// <summary>
        /// Data list
        /// </summary>
        [JsonPropertyName("list")]
        public List<ReportDataItem> List { get; set; } = new();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }
    }

    #endregion

    #region Asynchronous Report Models

    /// <summary>
    /// Request for creating an asynchronous report task
    /// </summary>
    public class AsynchronousReportTaskCreateRequest
    {
        /// <summary>
        /// Advertiser ID (conditional)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// List of advertiser IDs (conditional)
        /// </summary>
        [JsonPropertyName("advertiser_ids")]
        public List<string>? AdvertiserIds { get; set; }

        /// <summary>
        /// Ad service type
        /// </summary>
        [JsonPropertyName("service_type")]
        public string? ServiceType { get; set; }

        /// <summary>
        /// Report type (Required)
        /// </summary>
        [JsonPropertyName("report_type")]
        public string ReportType { get; set; } = string.Empty;

        /// <summary>
        /// Data level (conditional)
        /// </summary>
        [JsonPropertyName("data_level")]
        public string? DataLevel { get; set; }

        /// <summary>
        /// Grouping conditions (Required)
        /// </summary>
        [JsonPropertyName("dimensions")]
        public List<string> Dimensions { get; set; } = new();

        /// <summary>
        /// Metrics to query
        /// </summary>
        [JsonPropertyName("metrics")]
        public List<string>? Metrics { get; set; }

        /// <summary>
        /// Query start date (conditional)
        /// </summary>
        [JsonPropertyName("start_date")]
        public string? StartDate { get; set; }

        /// <summary>
        /// Query end date (conditional)
        /// </summary>
        [JsonPropertyName("end_date")]
        public string? EndDate { get; set; }

        /// <summary>
        /// Whether to request lifetime metrics
        /// </summary>
        [JsonPropertyName("query_lifetime")]
        public bool? QueryLifetime { get; set; }

        /// <summary>
        /// Sorting field
        /// </summary>
        [JsonPropertyName("order_field")]
        public string? OrderField { get; set; }

        /// <summary>
        /// Sorting order
        /// </summary>
        [JsonPropertyName("order_type")]
        public string? OrderType { get; set; }

        /// <summary>
        /// Whether to enable report title translation
        /// </summary>
        [JsonPropertyName("enable_report_title_translation")]
        public bool? EnableReportTitleTranslation { get; set; }

        /// <summary>
        /// Output format
        /// </summary>
        [JsonPropertyName("output_format")]
        public string? OutputFormat { get; set; }

        /// <summary>
        /// Custom file name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }

        /// <summary>
        /// Filtering conditions
        /// </summary>
        [JsonPropertyName("filtering")]
        public List<ReportFilter>? Filtering { get; set; }
    }

    /// <summary>
    /// Response for creating an asynchronous report task
    /// </summary>
    public class AsynchronousReportTaskCreateResponse
    {
        /// <summary>
        /// Task ID
        /// </summary>
        [JsonPropertyName("task_id")]
        public string? TaskId { get; set; }
    }

    /// <summary>
    /// Request for checking asynchronous report task status
    /// </summary>
    public class AsynchronousReportTaskStatusRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Task ID (Required)
        /// </summary>
        [JsonPropertyName("task_id")]
        public string TaskId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for checking asynchronous report task status
    /// </summary>
    public class AsynchronousReportTaskStatusResponse
    {
        /// <summary>
        /// Task status
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Error message (when status is FAILED)
        /// </summary>
        [JsonPropertyName("message")]
        public string? Message { get; set; }
    }

    /// <summary>
    /// Request for downloading asynchronous report task output
    /// </summary>
    public class AsynchronousReportTaskDownloadRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Task ID (Required)
        /// </summary>
        [JsonPropertyName("task_id")]
        public string TaskId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for downloading asynchronous report task output
    /// </summary>
    public class AsynchronousReportTaskDownloadResponse
    {
        /// <summary>
        /// Download URL
        /// </summary>
        [JsonPropertyName("download_url")]
        public string? DownloadUrl { get; set; }

        /// <summary>
        /// File name
        /// </summary>
        [JsonPropertyName("file_name")]
        public string? FileName { get; set; }

        /// <summary>
        /// Output format
        /// </summary>
        [JsonPropertyName("output_format")]
        public string? OutputFormat { get; set; }
    }

    /// <summary>
    /// Request for canceling an asynchronous report task
    /// </summary>
    public class AsynchronousReportTaskCancelRequest
    {
        /// <summary>
        /// Advertiser ID (Required)
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string AdvertiserId { get; set; } = string.Empty;

        /// <summary>
        /// Task ID (Required)
        /// </summary>
        [JsonPropertyName("task_id")]
        public string TaskId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for canceling an asynchronous report task
    /// </summary>
    public class AsynchronousReportTaskCancelResponse
    {
        /// <summary>
        /// Task status after cancellation
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }
    }

    #endregion

    #region Common Report Models

    /// <summary>
    /// Report filter for filtering conditions
    /// </summary>
    public class ReportFilter
    {
        /// <summary>
        /// Filter field name
        /// </summary>
        [JsonPropertyName("field_name")]
        public string FieldName { get; set; } = string.Empty;

        /// <summary>
        /// Filter type
        /// </summary>
        [JsonPropertyName("filter_type")]
        public string FilterType { get; set; } = string.Empty;

        /// <summary>
        /// Filter value
        /// </summary>
        [JsonPropertyName("filter_value")]
        public string FilterValue { get; set; } = string.Empty;
    }

    /// <summary>
    /// Report data item containing dimensions and metrics
    /// </summary>
    public class ReportDataItem
    {
        /// <summary>
        /// Dimension data
        /// </summary>
        [JsonPropertyName("dimensions")]
        public Dictionary<string, object>? Dimensions { get; set; }

        /// <summary>
        /// Metric data
        /// </summary>
        [JsonPropertyName("metrics")]
        public Dictionary<string, object>? Metrics { get; set; }
    }

    #endregion
}
