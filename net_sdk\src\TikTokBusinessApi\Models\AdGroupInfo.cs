/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Ad group information
    /// </summary>
    public class AdGroupInfo
    {
        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Campaign ID
        /// </summary>
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        /// <summary>
        /// Campaign name
        /// </summary>
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        /// <summary>
        /// The origin (source) of the campaign that the ad group belongs to
        /// </summary>
        [JsonPropertyName("campaign_system_origin")]
        public string? CampaignSystemOrigin { get; set; }

        /// <summary>
        /// Whether the ad group is within an automated campaign
        /// </summary>
        [JsonPropertyName("is_smart_performance_campaign")]
        public bool? IsSmartPerformanceCampaign { get; set; }

        /// <summary>
        /// Ad group ID
        /// </summary>
        [JsonPropertyName("adgroup_id")]
        public string? AdgroupId { get; set; }

        /// <summary>
        /// Ad group name
        /// </summary>
        [JsonPropertyName("adgroup_name")]
        public string? AdgroupName { get; set; }

        /// <summary>
        /// The time at which the ad group was created
        /// </summary>
        [JsonPropertyName("create_time")]
        public string? CreateTime { get; set; }

        /// <summary>
        /// The time at which the ad group was modified
        /// </summary>
        [JsonPropertyName("modify_time")]
        public string? ModifyTime { get; set; }

        /// <summary>
        /// Shopping ads type
        /// </summary>
        [JsonPropertyName("shopping_ads_type")]
        public string? ShoppingAdsType { get; set; }

        /// <summary>
        /// Identity ID
        /// </summary>
        [JsonPropertyName("identity_id")]
        public string? IdentityId { get; set; }

        /// <summary>
        /// Identity type
        /// </summary>
        [JsonPropertyName("identity_type")]
        public string? IdentityType { get; set; }

        /// <summary>
        /// ID of the Business Center that a TikTok Account User in Business Center identity is associated with
        /// </summary>
        [JsonPropertyName("identity_authorized_bc_id")]
        public string? IdentityAuthorizedBcId { get; set; }

        /// <summary>
        /// Product source where you want to get products for promotion
        /// </summary>
        [JsonPropertyName("product_source")]
        public string? ProductSource { get; set; }

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }

        /// <summary>
        /// For catalogs in Business Center, this field returns the ID of the Business Center that a catalog belongs to
        /// </summary>
        [JsonPropertyName("catalog_authorized_bc_id")]
        public string? CatalogAuthorizedBcId { get; set; }

        /// <summary>
        /// ID of the TikTok Shop
        /// </summary>
        [JsonPropertyName("store_id")]
        public string? StoreId { get; set; }

        /// <summary>
        /// ID of the Business Center that is authorized to access the store
        /// </summary>
        [JsonPropertyName("store_authorized_bc_id")]
        public string? StoreAuthorizedBcId { get; set; }

        /// <summary>
        /// Promotion type (Optimization location)
        /// </summary>
        [JsonPropertyName("promotion_type")]
        public string? PromotionType { get; set; }

        /// <summary>
        /// The promotion type for Lead Generation objective
        /// </summary>
        [JsonPropertyName("promotion_target_type")]
        public string? PromotionTargetType { get; set; }

        /// <summary>
        /// The type of instant messaging app or customized URL to use in the Instant Messaging Ad Group
        /// </summary>
        [JsonPropertyName("messaging_app_type")]
        public string? MessagingAppType { get; set; }

        /// <summary>
        /// The ID of the instant messaging app account
        /// </summary>
        [JsonPropertyName("messaging_app_account_id")]
        public string? MessagingAppAccountId { get; set; }

        /// <summary>
        /// The region code for the WhatsApp or Zalo phone number
        /// </summary>
        [JsonPropertyName("phone_region_code")]
        public string? PhoneRegionCode { get; set; }

        /// <summary>
        /// The region calling code for the WhatsApp or Zalo phone number
        /// </summary>
        [JsonPropertyName("phone_region_calling_code")]
        public string? PhoneRegionCallingCode { get; set; }

        /// <summary>
        /// The WhatsApp or Zalo phone number
        /// </summary>
        [JsonPropertyName("phone_number")]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// TikTok Instant Page type in the ad group
        /// </summary>
        [JsonPropertyName("promotion_website_type")]
        public string? PromotionWebsiteType { get; set; }

        /// <summary>
        /// The Application ID of the promoted app
        /// </summary>
        [JsonPropertyName("app_id")]
        public string? AppId { get; set; }

        /// <summary>
        /// The type of the promoted app
        /// </summary>
        [JsonPropertyName("app_type")]
        public string? AppType { get; set; }

        /// <summary>
        /// App download link
        /// </summary>
        [JsonPropertyName("app_download_url")]
        public string? AppDownloadUrl { get; set; }

        /// <summary>
        /// Pixel ID. Only applicable for landing pages
        /// </summary>
        [JsonPropertyName("pixel_id")]
        public string? PixelId { get; set; }

        /// <summary>
        /// Conversion event for the ad group
        /// </summary>
        [JsonPropertyName("optimization_event")]
        public string? OptimizationEvent { get; set; }

        /// <summary>
        /// The status of deep funnel optimization
        /// </summary>
        [JsonPropertyName("deep_funnel_optimization_status")]
        public string? DeepFunnelOptimizationStatus { get; set; }

        /// <summary>
        /// The event source type
        /// </summary>
        [JsonPropertyName("deep_funnel_event_source")]
        public string? DeepFunnelEventSource { get; set; }

        /// <summary>
        /// Event Source ID
        /// </summary>
        [JsonPropertyName("deep_funnel_event_source_id")]
        public string? DeepFunnelEventSourceId { get; set; }

        /// <summary>
        /// Deep funnel optimization event
        /// </summary>
        [JsonPropertyName("deep_funnel_optimization_event")]
        public string? DeepFunnelOptimizationEvent { get; set; }

        /// <summary>
        /// The placement strategy that decides where your ads will be shown
        /// </summary>
        [JsonPropertyName("placement_type")]
        public string? PlacementType { get; set; }

        /// <summary>
        /// The apps where you want to deliver your ads
        /// </summary>
        [JsonPropertyName("placements")]
        public List<string>? Placements { get; set; }

        /// <summary>
        /// The subplacements within TikTok for your ads
        /// </summary>
        [JsonPropertyName("tiktok_subplacements")]
        public List<string>? TiktokSubplacements { get; set; }

        /// <summary>
        /// Whether to include your ads in Search Ads
        /// </summary>
        [JsonPropertyName("search_result_enabled")]
        public bool? SearchResultEnabled { get; set; }

        /// <summary>
        /// Whether to enable automated keywords
        /// </summary>
        [JsonPropertyName("automated_keywords_enabled")]
        public bool? AutomatedKeywordsEnabled { get; set; }

        /// <summary>
        /// A list of search keywords
        /// </summary>
        [JsonPropertyName("search_keywords")]
        public List<AdGroupSearchKeyword>? SearchKeywords { get; set; }

        /// <summary>
        /// Whether to allow comments on your ads on TikTok
        /// </summary>
        [JsonPropertyName("comment_disabled")]
        public bool? CommentDisabled { get; set; }

        /// <summary>
        /// Whether users can download your video ads on TikTok
        /// </summary>
        [JsonPropertyName("video_download_disabled")]
        public bool? VideoDownloadDisabled { get; set; }

        /// <summary>
        /// Whether sharing to third-party platforms is disabled for ads in this ad group
        /// </summary>
        [JsonPropertyName("share_disabled")]
        public bool? ShareDisabled { get; set; }

        /// <summary>
        /// Pangle app block list ID
        /// </summary>
        [JsonPropertyName("blocked_pangle_app_ids")]
        public List<string>? BlockedPangleAppIds { get; set; }

        /// <summary>
        /// App retargeting audience type
        /// </summary>
        [JsonPropertyName("audience_type")]
        public string? AudienceType { get; set; }

        /// <summary>
        /// Rules that specify your audience
        /// </summary>
        [JsonPropertyName("audience_rule")]
        public object? AudienceRule { get; set; }

        /// <summary>
        /// Whether to enable automated targeting (deprecated)
        /// </summary>
        [JsonPropertyName("auto_targeting_enabled")]
        public bool? AutoTargetingEnabled { get; set; }

        /// <summary>
        /// The retargeting type of shopping ads
        /// </summary>
        [JsonPropertyName("shopping_ads_retargeting_type")]
        public string? ShoppingAdsRetargetingType { get; set; }

        /// <summary>
        /// The valid time range for the specified audience action
        /// </summary>
        [JsonPropertyName("shopping_ads_retargeting_actions_days")]
        public int? ShoppingAdsRetargetingActionsDays { get; set; }

        /// <summary>
        /// The custom action that you want to use as "Include" conditions
        /// </summary>
        [JsonPropertyName("included_custom_actions")]
        public List<AdGroupInfoCustomAction>? IncludedCustomActions { get; set; }

        /// <summary>
        /// The custom action that you want to use as "Exclude" conditions
        /// </summary>
        [JsonPropertyName("excluded_custom_actions")]
        public List<AdGroupInfoCustomAction>? ExcludedCustomActions { get; set; }

        /// <summary>
        /// The logical relation between the VSA retargeting audience and the custom audience
        /// </summary>
        [JsonPropertyName("shopping_ads_retargeting_custom_audience_relation")]
        public string? ShoppingAdsRetargetingCustomAudienceRelation { get; set; }

        /// <summary>
        /// IDs of the targeted locations
        /// </summary>
        [JsonPropertyName("location_ids")]
        public List<string>? LocationIds { get; set; }

        /// <summary>
        /// Zip code IDs or postal code IDs of the targeted locations
        /// </summary>
        [JsonPropertyName("zipcode_ids")]
        public List<string>? ZipcodeIds { get; set; }

        /// <summary>
        /// Codes of the languages that you want to target
        /// </summary>
        [JsonPropertyName("languages")]
        public List<string>? Languages { get; set; }

        /// <summary>
        /// Gender that you want to target
        /// </summary>
        [JsonPropertyName("gender")]
        public string? Gender { get; set; }

        /// <summary>
        /// Age groups you want to target
        /// </summary>
        [JsonPropertyName("age_groups")]
        public List<string>? AgeGroups { get; set; }

        /// <summary>
        /// Spending power that you want to target
        /// </summary>
        [JsonPropertyName("spending_power")]
        public string? SpendingPower { get; set; }

        /// <summary>
        /// Household income that you want to target
        /// </summary>
        [JsonPropertyName("household_income")]
        public List<string>? HouseholdIncome { get; set; }

        /// <summary>
        /// A list of audience IDs
        /// </summary>
        [JsonPropertyName("audience_ids")]
        public List<string>? AudienceIds { get; set; }

        /// <summary>
        /// Whether Smart audience is turned on
        /// </summary>
        [JsonPropertyName("smart_audience_enabled")]
        public bool? SmartAudienceEnabled { get; set; }

        /// <summary>
        /// A list of excluded audience ID
        /// </summary>
        [JsonPropertyName("excluded_audience_ids")]
        public List<string>? ExcludedAudienceIds { get; set; }

        /// <summary>
        /// IDs of general interest keywords that you want to use to target audiences
        /// </summary>
        [JsonPropertyName("interest_category_ids")]
        public List<string>? InterestCategoryIds { get; set; }

        /// <summary>
        /// IDs of additional interest keywords that you want to use to target audiences
        /// </summary>
        [JsonPropertyName("interest_keyword_ids")]
        public List<string>? InterestKeywordIds { get; set; }

        /// <summary>
        /// IDs of purchase intention categories
        /// </summary>
        [JsonPropertyName("purchase_intention_keyword_ids")]
        public List<string>? PurchaseIntentionKeywordIds { get; set; }

        /// <summary>
        /// A list of targeting behavioral category objects
        /// </summary>
        [JsonPropertyName("actions")]
        public List<AdGroupBehaviorAction>? Actions { get; set; }

        /// <summary>
        /// Whether Smart interests & behaviors is turned on
        /// </summary>
        [JsonPropertyName("smart_interest_behavior_enabled")]
        public bool? SmartInterestBehaviorEnabled { get; set; }

        /// <summary>
        /// IDs of the Pangle audiences that you want to include
        /// </summary>
        [JsonPropertyName("included_pangle_audience_package_ids")]
        public List<string>? IncludedPangleAudiencePackageIds { get; set; }

        /// <summary>
        /// IDs of the Pangle audiences that you want to exclude
        /// </summary>
        [JsonPropertyName("excluded_pangle_audience_package_ids")]
        public List<string>? ExcludedPangleAudiencePackageIds { get; set; }

        /// <summary>
        /// Device operating systems that you want to target
        /// </summary>
        [JsonPropertyName("operating_systems")]
        public List<string>? OperatingSystems { get; set; }

        /// <summary>
        /// Minimum Android version
        /// </summary>
        [JsonPropertyName("min_android_version")]
        public string? MinAndroidVersion { get; set; }

        /// <summary>
        /// The iOS devices that you want to target
        /// </summary>
        [JsonPropertyName("ios14_targeting")]
        public string? Ios14Targeting { get; set; }

        /// <summary>
        /// Audience minimum ios version
        /// </summary>
        [JsonPropertyName("min_ios_version")]
        public string? MinIosVersion { get; set; }

        /// <summary>
        /// Whether the campaign will be counted against the iOS 14 dedicated campaign quota
        /// </summary>
        [JsonPropertyName("ios14_quota_type")]
        public string? Ios14QuotaType { get; set; }

        /// <summary>
        /// List of device model IDs
        /// </summary>
        [JsonPropertyName("device_model_ids")]
        public List<string>? DeviceModelIds { get; set; }

        /// <summary>
        /// Network types that you want to target
        /// </summary>
        [JsonPropertyName("network_types")]
        public List<string>? NetworkTypes { get; set; }

        /// <summary>
        /// Carriers that you want to target
        /// </summary>
        [JsonPropertyName("carrier_ids")]
        public List<string>? CarrierIds { get; set; }

        /// <summary>
        /// IDs of the targeted Internet service providers
        /// </summary>
        [JsonPropertyName("isp_ids")]
        public List<string>? IspIds { get; set; }

        /// <summary>
        /// Targeting device price range
        /// </summary>
        [JsonPropertyName("device_price_ranges")]
        public List<int>? DevicePriceRanges { get; set; }

        /// <summary>
        /// Settings about targeting expansion (deprecated)
        /// </summary>
        [JsonPropertyName("targeting_expansion")]
        public AdGroupTargetingExpansion? TargetingExpansion { get; set; }

        /// <summary>
        /// Saved Audience ID
        /// </summary>
        [JsonPropertyName("saved_audience_id")]
        public string? SavedAudienceId { get; set; }

        /// <summary>
        /// Contextual tag IDs
        /// </summary>
        [JsonPropertyName("contextual_tag_ids")]
        public List<string>? ContextualTagIds { get; set; }

        /// <summary>
        /// Brand safety type
        /// </summary>
        [JsonPropertyName("brand_safety_type")]
        public string? BrandSafetyType { get; set; }

        /// <summary>
        /// Brand safety partner
        /// </summary>
        [JsonPropertyName("brand_safety_partner")]
        public string? BrandSafetyPartner { get; set; }

        /// <summary>
        /// Inventory filtering (filtering security videos, hides unsafe videos)
        /// </summary>
        [JsonPropertyName("inventory_filter_enabled")]
        public bool? InventoryFilterEnabled { get; set; }

        /// <summary>
        /// Content exclusion category IDs
        /// </summary>
        [JsonPropertyName("category_exclusion_ids")]
        public List<string>? CategoryExclusionIds { get; set; }

        /// <summary>
        /// Vertical sensitivity category ID
        /// </summary>
        [JsonPropertyName("vertical_sensitivity_id")]
        public string? VerticalSensitivityId { get; set; }

        /// <summary>
        /// Budget mode
        /// </summary>
        [JsonPropertyName("budget_mode")]
        public string? BudgetMode { get; set; }

        /// <summary>
        /// Ad group budget
        /// </summary>
        [JsonPropertyName("budget")]
        public float? Budget { get; set; }

        /// <summary>
        /// Scheduled ad budget for next day
        /// </summary>
        [JsonPropertyName("scheduled_budget")]
        public float? ScheduledBudget { get; set; }

        /// <summary>
        /// The schedule type
        /// </summary>
        [JsonPropertyName("schedule_type")]
        public string? ScheduleType { get; set; }

        /// <summary>
        /// Ad delivery start time
        /// </summary>
        [JsonPropertyName("schedule_start_time")]
        public string? ScheduleStartTime { get; set; }

        /// <summary>
        /// Ad delivery end time
        /// </summary>
        [JsonPropertyName("schedule_end_time")]
        public string? ScheduleEndTime { get; set; }

        /// <summary>
        /// The estimated number of impressions (TopView ads only)
        /// </summary>
        [JsonPropertyName("predict_impression")]
        public long? PredictImpression { get; set; }

        /// <summary>
        /// The estimated range of reach (TopView ads only)
        /// </summary>
        [JsonPropertyName("topview_reach_range")]
        public List<long>? TopviewReachRange { get; set; }

        /// <summary>
        /// The estimated cost per mille (CPM) before applying any budget discount (TopView ads only)
        /// </summary>
        [JsonPropertyName("pre_discount_cpm")]
        public float? PreDiscountCpm { get; set; }

        /// <summary>
        /// The estimated cost per mille (CPM) after applying any budget discount (TopView ads only)
        /// </summary>
        [JsonPropertyName("cpm")]
        public float? Cpm { get; set; }

        /// <summary>
        /// The type of discount applied to the budget (TopView ads only)
        /// </summary>
        [JsonPropertyName("discount_type")]
        public string? DiscountType { get; set; }

        /// <summary>
        /// The fixed amount by which the budget is discounted (TopView ads only)
        /// </summary>
        [JsonPropertyName("discount_amount")]
        public float? DiscountAmount { get; set; }

        /// <summary>
        /// The percentage by which the budget is discounted (TopView ads only)
        /// </summary>
        [JsonPropertyName("discount_percentage")]
        public float? DiscountPercentage { get; set; }

        /// <summary>
        /// The budget amount before applying any discount (TopView ads only)
        /// </summary>
        [JsonPropertyName("pre_discount_budget")]
        public float? PreDiscountBudget { get; set; }

        /// <summary>
        /// Ad delivery information of R&F ad groups
        /// </summary>
        [JsonPropertyName("schedule_infos")]
        public List<AdDeliveryScheduleInfo>? ScheduleInfos { get; set; }

        /// <summary>
        /// The strategy for sequencing and scheduling your ad delivery in a Reach & Frequency ad group
        /// </summary>
        [JsonPropertyName("delivery_mode")]
        public string? DeliveryMode { get; set; }

        /// <summary>
        /// Day parting schedule
        /// </summary>
        [JsonPropertyName("dayparting")]
        public string? Dayparting { get; set; }

        /// <summary>
        /// Optimization goal
        /// </summary>
        [JsonPropertyName("optimization_goal")]
        public string? OptimizationGoal { get; set; }

        /// <summary>
        /// The secondary goal when optimization_goal is INSTALL or VALUE
        /// </summary>
        [JsonPropertyName("secondary_optimization_event")]
        public string? SecondaryOptimizationEvent { get; set; }

        /// <summary>
        /// The ID of the message event set to use in the Instant Messaging Ad Group
        /// </summary>
        [JsonPropertyName("message_event_set_id")]
        public string? MessageEventSetId { get; set; }

        /// <summary>
        /// Frequency
        /// </summary>
        [JsonPropertyName("frequency")]
        public int? Frequency { get; set; }

        /// <summary>
        /// Frequency schedule
        /// </summary>
        [JsonPropertyName("frequency_schedule")]
        public int? FrequencySchedule { get; set; }

        /// <summary>
        /// Bidding strategy
        /// </summary>
        [JsonPropertyName("bid_type")]
        public string? BidType { get; set; }

        /// <summary>
        /// Bid price
        /// </summary>
        [JsonPropertyName("bid_price")]
        public float? BidPrice { get; set; }

        /// <summary>
        /// Target cost per conversion for oCPM
        /// </summary>
        [JsonPropertyName("conversion_bid_price")]
        public float? ConversionBidPrice { get; set; }

        /// <summary>
        /// Bidding strategy for in-app events
        /// </summary>
        [JsonPropertyName("deep_bid_type")]
        public string? DeepBidType { get; set; }

        /// <summary>
        /// ROAS goal for Value Optimization
        /// </summary>
        [JsonPropertyName("roas_bid")]
        public float? RoasBid { get; set; }

        /// <summary>
        /// The time window of the specified bidding strategy for VBO
        /// </summary>
        [JsonPropertyName("vbo_window")]
        public string? VboWindow { get; set; }

        /// <summary>
        /// How you calculate and measure Cost per View
        /// </summary>
        [JsonPropertyName("bid_display_mode")]
        public string? BidDisplayMode { get; set; }

        /// <summary>
        /// Deep CPA bid
        /// </summary>
        [JsonPropertyName("deep_cpa_bid")]
        public float? DeepCpaBid { get; set; }

        /// <summary>
        /// Optimized video playback duration
        /// </summary>
        [JsonPropertyName("cpv_video_duration")]
        public string? CpvVideoDuration { get; set; }

        /// <summary>
        /// Day 2 retention ratio
        /// </summary>
        [JsonPropertyName("next_day_retention")]
        public float? NextDayRetention { get; set; }

        /// <summary>
        /// Click-through window for the ad group
        /// </summary>
        [JsonPropertyName("click_attribution_window")]
        public string? ClickAttributionWindow { get; set; }

        /// <summary>
        /// Engaged view-through window for the ad group
        /// </summary>
        [JsonPropertyName("engaged_view_attribution_window")]
        public string? EngagedViewAttributionWindow { get; set; }

        /// <summary>
        /// View-through window for the ad group
        /// </summary>
        [JsonPropertyName("view_attribution_window")]
        public string? ViewAttributionWindow { get; set; }

        /// <summary>
        /// Event count (Statistic type) for the ad group
        /// </summary>
        [JsonPropertyName("attribution_event_count")]
        public string? AttributionEventCount { get; set; }

        /// <summary>
        /// Billing event
        /// </summary>
        [JsonPropertyName("billing_event")]
        public string? BillingEvent { get; set; }

        /// <summary>
        /// Pacing
        /// </summary>
        [JsonPropertyName("pacing")]
        public string? Pacing { get; set; }

        /// <summary>
        /// Operation status
        /// </summary>
        [JsonPropertyName("operation_status")]
        public string? OperationStatus { get; set; }

        /// <summary>
        /// Ad group status (secondary status)
        /// </summary>
        [JsonPropertyName("secondary_status")]
        public string? SecondaryStatus { get; set; }

        /// <summary>
        /// Conversion bid statistic type
        /// </summary>
        [JsonPropertyName("statistic_type")]
        public string? StatisticType { get; set; }

        /// <summary>
        /// Whether the promoted product is HFSS foods
        /// </summary>
        [JsonPropertyName("is_hfss")]
        public bool? IsHfss { get; set; }

        /// <summary>
        /// The strategy that your creatives will be delivered
        /// </summary>
        [JsonPropertyName("creative_material_mode")]
        public string? CreativeMaterialMode { get; set; }

        /// <summary>
        /// Indicates whether the adgroup is using app profile page
        /// </summary>
        [JsonPropertyName("adgroup_app_profile_page_state")]
        public string? AdgroupAppProfilePageState { get; set; }

        /// <summary>
        /// Feed type option
        /// </summary>
        [JsonPropertyName("feed_type")]
        public string? FeedType { get; set; }

        /// <summary>
        /// Billing method of Reach & Frequency ad groups
        /// </summary>
        [JsonPropertyName("rf_purchased_type")]
        public string? RfPurchasedType { get; set; }

        /// <summary>
        /// Impressions to be purchased
        /// </summary>
        [JsonPropertyName("purchased_impression")]
        public long? PurchasedImpression { get; set; }

        /// <summary>
        /// Purchased user reach
        /// </summary>
        [JsonPropertyName("purchased_reach")]
        public long? PurchasedReach { get; set; }

        /// <summary>
        /// The estimated cost per mile reach
        /// </summary>
        [JsonPropertyName("rf_estimated_cpr")]
        public float? RfEstimatedCpr { get; set; }

        /// <summary>
        /// The estimated show frequency
        /// </summary>
        [JsonPropertyName("rf_estimated_frequency")]
        public float? RfEstimatedFrequency { get; set; }

        /// <summary>
        /// Split test group ID
        /// </summary>
        [JsonPropertyName("split_test_group_id")]
        public string? SplitTestGroupId { get; set; }

        /// <summary>
        /// Split test status
        /// </summary>
        [JsonPropertyName("split_test_status")]
        public string? SplitTestStatus { get; set; }

        /// <summary>
        /// Whether the campaign is a new structure
        /// </summary>
        [JsonPropertyName("is_new_structure")]
        public bool? IsNewStructure { get; set; }

        /// <summary>
        /// Whether to skip the learning stage
        /// </summary>
        [JsonPropertyName("skip_learning_phase")]
        public bool? SkipLearningPhase { get; set; }

        /// <summary>
        /// Conversion window (deprecated)
        /// </summary>
        [JsonPropertyName("conversion_window")]
        public string? ConversionWindow { get; set; }

        /// <summary>
        /// Attribution window (deprecated - kept for compatibility)
        /// </summary>
        [JsonPropertyName("attribution_window")]
        public string? AttributionWindow { get; set; }

        /// <summary>
        /// Genders (deprecated - kept for compatibility)
        /// </summary>
        [JsonPropertyName("genders")]
        public List<string>? Genders { get; set; }

        /// <summary>
        /// Behavior category IDs (deprecated - kept for compatibility)
        /// </summary>
        [JsonPropertyName("behavior_category_ids")]
        public List<string>? BehaviorCategoryIds { get; set; }

        /// <summary>
        /// Device models (deprecated - kept for compatibility)
        /// </summary>
        [JsonPropertyName("device_models")]
        public List<string>? DeviceModels { get; set; }

        /// <summary>
        /// Device price buckets (deprecated - kept for compatibility)
        /// </summary>
        [JsonPropertyName("device_price_buckets")]
        public List<string>? DevicePriceBuckets { get; set; }

        /// <summary>
        /// Custom audiences (deprecated - kept for compatibility)
        /// </summary>
        [JsonPropertyName("custom_audiences")]
        public List<string>? CustomAudiences { get; set; }

        /// <summary>
        /// Excluded custom audiences (deprecated - kept for compatibility)
        /// </summary>
        [JsonPropertyName("excluded_custom_audiences")]
        public List<string>? ExcludedCustomAudiences { get; set; }

        /// <summary>
        /// Lookalike audiences (deprecated - kept for compatibility)
        /// </summary>
        [JsonPropertyName("lookalike_audiences")]
        public List<string>? LookalikeAudiences { get; set; }

        /// <summary>
        /// Excluded lookalike audiences (deprecated - kept for compatibility)
        /// </summary>
        [JsonPropertyName("excluded_lookalike_audiences")]
        public List<string>? ExcludedLookalikeAudiences { get; set; }

        /// <summary>
        /// Objective type (deprecated - kept for compatibility)
        /// </summary>
        [JsonPropertyName("objective_type")]
        public string? ObjectiveType { get; set; }

        /// <summary>
        /// Ad group status (deprecated - kept for compatibility)
        /// </summary>
        [JsonPropertyName("status")]
        public string? Status { get; set; }

        /// <summary>
        /// Initializes a new instance of the AdGroupInfo class
        /// </summary>
        public AdGroupInfo()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"AdGroupInfo(AdgroupId={AdgroupId}, AdgroupName={AdgroupName}, Status={OperationStatus ?? Status})";
        }
    }

    /// <summary>
    /// Search keyword information
    /// </summary>
    public class AdGroupSearchKeyword
    {
        /// <summary>
        /// The search keyword
        /// </summary>
        [JsonPropertyName("keyword")]
        public string? Keyword { get; set; }

        /// <summary>
        /// The match type for the search keyword
        /// </summary>
        [JsonPropertyName("match_type")]
        public string? MatchType { get; set; }

        /// <summary>
        /// The bid type for the keyword and match type combination
        /// </summary>
        [JsonPropertyName("keyword_bid_type")]
        public string? KeywordBidType { get; set; }

        /// <summary>
        /// The bid price for the keyword and match type combination
        /// </summary>
        [JsonPropertyName("keyword_bid")]
        public float? KeywordBid { get; set; }

        /// <summary>
        /// The review status of the search keyword
        /// </summary>
        [JsonPropertyName("audit_status")]
        public string? AuditStatus { get; set; }

        /// <summary>
        /// Details about the rejection
        /// </summary>
        [JsonPropertyName("reject_info")]
        public List<AdGroupSearchRejectInfo>? RejectInfo { get; set; }
    }

    /// <summary>
    /// Rejection information
    /// </summary>
    public class AdGroupSearchRejectInfo
    {
        /// <summary>
        /// The targeted region that failed the review
        /// </summary>
        [JsonPropertyName("forbidden_location")]
        public string? ForbiddenLocation { get; set; }

        /// <summary>
        /// List of rejection reasons
        /// </summary>
        [JsonPropertyName("reject_reasons")]
        public List<AdGroupRejectReason>? RejectReasons { get; set; }
    }

    /// <summary>
    /// Rejection reason
    /// </summary>
    public class AdGroupRejectReason
    {
        /// <summary>
        /// The rejection reason
        /// </summary>
        [JsonPropertyName("reason")]
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Custom action for shopping ads retargeting
    /// </summary>
    public class AdGroupInfoCustomAction
    {
        /// <summary>
        /// The custom action used to filter out the audiences to be retargeted
        /// </summary>
        [JsonPropertyName("code")]
        public string? Code { get; set; }

        /// <summary>
        /// The time range used to filter out the audiences
        /// </summary>
        [JsonPropertyName("days")]
        public int? Days { get; set; }
    }

    /// <summary>
    /// Behavioral action for targeting
    /// </summary>
    public class AdGroupBehaviorAction
    {
        /// <summary>
        /// The type of user behavior that you want to target
        /// </summary>
        [JsonPropertyName("action_scene")]
        public string? ActionScene { get; set; }

        /// <summary>
        /// The time period to include behaviors from
        /// </summary>
        [JsonPropertyName("action_period")]
        public int? ActionPeriod { get; set; }

        /// <summary>
        /// The specific user interactions that you want to target
        /// </summary>
        [JsonPropertyName("video_user_actions")]
        public List<string>? VideoUserActions { get; set; }

        /// <summary>
        /// IDs of the categories that you want to use to target audiences
        /// </summary>
        [JsonPropertyName("action_category_ids")]
        public List<string>? ActionCategoryIds { get; set; }
    }

    /// <summary>
    /// Targeting expansion settings (deprecated)
    /// </summary>
    public class AdGroupTargetingExpansion
    {
        /// <summary>
        /// Whether to enable targeting expansion
        /// </summary>
        [JsonPropertyName("expansion_enabled")]
        public bool? ExpansionEnabled { get; set; }

        /// <summary>
        /// The target audience types that you want to expand
        /// </summary>
        [JsonPropertyName("expansion_types")]
        public List<string>? ExpansionTypes { get; set; }
    }

    /// <summary>
    /// Schedule information for R&F ad groups
    /// </summary>
    public class AdDeliveryScheduleInfo
    {
        /// <summary>
        /// The details of the scheduled delivery for the ad
        /// </summary>
        [JsonPropertyName("schedules")]
        public List<AdDeliverySchedule>? Schedules { get; set; }

        /// <summary>
        /// The delivery order for an ad within the ad group
        /// </summary>
        [JsonPropertyName("expected_orders")]
        public List<int>? ExpectedOrders { get; set; }

        /// <summary>
        /// Whether the ad delivery information is in draft mode
        /// </summary>
        [JsonPropertyName("is_draft")]
        public bool? IsDraft { get; set; }

        /// <summary>
        /// Schedule ID
        /// </summary>
        [JsonPropertyName("schedule_id")]
        public string? ScheduleId { get; set; }
    }

    /// <summary>
    /// Schedule details
    /// </summary>
    public class AdDeliverySchedule
    {
        /// <summary>
        /// Ad delivery start time
        /// </summary>
        [JsonPropertyName("start_time")]
        public string? StartTime { get; set; }

        /// <summary>
        /// Ad delivery end time
        /// </summary>
        [JsonPropertyName("end_time")]
        public string? EndTime { get; set; }
    }
}