/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Catalog Videos operations
    /// </summary>
    public interface ICatalogVideosApi
    {
        /// <summary>
        /// Upload catalog videos via a file URL
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing upload parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing feed log ID</returns>
        //Task<Response<CatalogVideoUploadResponse>> UploadVideoFileAsync(
        //    string accessToken,
        //    CatalogVideoUploadBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the catalog video handling log
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting video log</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing video handling log details</returns>
        Task<Response<CatalogVideoLogResponse>> GetVideoLogAsync(
            string accessToken,
            CatalogVideoLogRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get the uploaded catalog videos within a catalog
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Request parameters for getting videos</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing list of catalog videos</returns>
        Task<Response<CatalogVideosGetResponse>> GetVideosAsync(
            string accessToken,
            CatalogVideosGetRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Delete uploaded catalog videos
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing videos to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> DeleteVideosAsync(
        //    string accessToken,
        //    CatalogVideosDeleteBody body,
        //    CancellationToken cancellationToken = default);
    }
}
