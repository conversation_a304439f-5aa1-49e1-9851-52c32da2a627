/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Store operations
    /// </summary>
    public class StoreApi : IStoreApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<StoreApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the StoreApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public StoreApi(IApiClient apiClient, ILogger<StoreApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get the list of available first-party stores (TikTok Shops) under an ad account
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="storeId">ID of the first-party store (TikTok Shop) to filter by</param>
        /// <param name="storeType">Store type to filter by. Enum values: TIKTOK_SHOP</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing available stores</returns>
        public async Task<Response<StoreListResponse>> GetStoreListAsync(
            string accessToken,
            string advertiserId,
            string? storeId = null,
            string? storeType = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogInformation("Getting store list for advertiser {AdvertiserId}", advertiserId);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId
            };

            if (!string.IsNullOrWhiteSpace(storeId))
                queryParams["store_id"] = storeId;
            if (!string.IsNullOrWhiteSpace(storeType))
                queryParams["store_type"] = storeType;

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<StoreListResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{StoreEndpoints.GetStoreList}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get products within a first-party store (TikTok Shop)
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="storeId">TikTok Shop ID</param>
        /// <param name="filtering">Filtering conditions</param>
        /// <param name="advertiserId">Advertiser ID (required when ad_creation_eligible is passed)</param>
        /// <param name="sortField">Field to sort by. Enum values: min_price, historical_sales</param>
        /// <param name="sortType">Sorting order. Enum values: ASC, DESC</param>
        /// <param name="page">Current number of pages. Default value: 1</param>
        /// <param name="pageSize">Page size. Default value: 10. Value range: 1-100</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing store products</returns>
        public async Task<Response<StoreProductsResponse>> GetStoreProductsAsync(
            string accessToken,
            string bcId,
            string storeId,
            StoreProductFiltering? filtering = null,
            string? advertiserId = null,
            string? sortField = null,
            string? sortType = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(bcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));
            if (string.IsNullOrWhiteSpace(storeId))
                throw new ArgumentException("Store ID cannot be null or empty", nameof(storeId));
            if (page < 1)
                throw new ArgumentException("Page must be greater than 0", nameof(page));
            if (pageSize < 1 || pageSize > 100)
                throw new ArgumentException("Page size must be between 1 and 100", nameof(pageSize));

            _logger?.LogInformation("Getting products for store {StoreId} in BC {BcId}", storeId, bcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = bcId,
                ["store_id"] = storeId,
                ["page"] = page.ToString(),
                ["page_size"] = pageSize.ToString()
            };

            if (filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(filtering);
            if (!string.IsNullOrWhiteSpace(advertiserId))
                queryParams["advertiser_id"] = advertiserId;
            if (!string.IsNullOrWhiteSpace(sortField))
                queryParams["sort_field"] = sortField;
            if (!string.IsNullOrWhiteSpace(sortType))
                queryParams["sort_type"] = sortType;

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<StoreProductsResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{StoreEndpoints.GetStoreProducts}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
