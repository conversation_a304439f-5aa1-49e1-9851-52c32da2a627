using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu tài sản (Assets)
    /// </summary>
    public class AssetSyncApplicationAppService : ApplicationService, IAssetSyncApplicationAppService
    {
        private readonly IAssetSyncService _assetSyncService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="assetSyncService">Asset sync service</param>
        public AssetSyncApplicationAppService(IAssetSyncService assetSyncService)
        {
            _assetSyncService = assetSyncService;
        }

        /// <summary>
        /// Đồng bộ tài sản theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AssetSyncResult> SyncAssetsAsync(string bcId)
        {
            return await _assetSyncService.SyncAssetsAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ tài sản theo loại tài sản và BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="assetType">Loại tài sản</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AssetSyncResult> SyncAssetsByTypeAsync(string bcId, string assetType)
        {
            return await _assetSyncService.SyncAssetsByTypeAsync(bcId, assetType);
        }

        /// <summary>
        /// Đồng bộ tất cả tài sản cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<AssetSyncResult> SyncAllAssetsForAllBcsAsync()
        {
            return await _assetSyncService.SyncAllAssetsForAllBcsAsync();
        }
    }
}