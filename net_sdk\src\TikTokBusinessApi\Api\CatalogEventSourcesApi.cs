/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Catalog Event Sources operations
    /// </summary>
    public class CatalogEventSourcesApi : ICatalogEventSourcesApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<CatalogEventSourcesApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the CatalogEventSourcesApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public CatalogEventSourcesApi(IApiClient apiClient, ILogger<CatalogEventSourcesApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Bind an event source to a catalog
        /// Use this endpoint to bind an app event or a website event to a catalog in a Business Center.
        /// </summary>
        /// <param name="body">Request body containing event source binding information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<object>> BindEventSourceAsync(
        //    CatalogEventSourceBindBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.BcId))
        //        throw new ArgumentException("Business Center ID cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.CatalogId))
        //        throw new ArgumentException("Catalog ID cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.AppId) && string.IsNullOrWhiteSpace(body.PixelCode))
        //        throw new ArgumentException("At least one of AppId or PixelCode must be specified", nameof(body));

        //    _logger?.LogInformation("Binding event source to catalog {CatalogId} for advertiser {AdvertiserId}", 
        //        body.CatalogId, body.AdvertiserId);

        //    return await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogEventSourcesEndpoints.BindEventSource}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Unbind an event source from a catalog
        /// Use this endpoint to unbind an app event or a website event from a catalog in a Business Center.
        /// </summary>
        /// <param name="body">Request body containing event source unbinding information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //public async Task<Response<object>> UnbindEventSourceAsync(
        //    CatalogEventSourceUnbindBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.BcId))
        //        throw new ArgumentException("Business Center ID cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.CatalogId))
        //        throw new ArgumentException("Catalog ID cannot be null or empty", nameof(body));

        //    _logger?.LogInformation("Unbinding event source from catalog {CatalogId} for advertiser {AdvertiserId}", 
        //        body.CatalogId, body.AdvertiserId);

        //    return await _apiClient.CallApiAsync<Response<object>>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogEventSourcesEndpoints.UnbindEventSource}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get event source binding info of a catalog
        /// Use this endpoint to get the binding information about app or web event sources of a catalog in a Business Center.
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing event source binding information</returns>
        public async Task<CatalogEventSourceBindResponse> GetEventSourceBindingInfoAsync(
            string bcId,
            string catalogId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(bcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));
            if (string.IsNullOrWhiteSpace(catalogId))
                throw new ArgumentException("Catalog ID cannot be null or empty", nameof(catalogId));

            _logger?.LogInformation("Getting event source binding info for catalog {CatalogId} in BC {BcId}", 
                catalogId, bcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = bcId,
                ["catalog_id"] = catalogId
            };

            return await _apiClient.CallApiAsync<CatalogEventSourceBindResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogEventSourcesEndpoints.GetEventSourceBindingInfo}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
