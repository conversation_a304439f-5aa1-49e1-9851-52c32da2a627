/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
using System.Reflection.PortableExecutable;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Authentication operations
    /// </summary>
    public class AuthenticationApi : IAuthenticationApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<AuthenticationApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the AuthenticationApi class
        /// </summary>
        /// <param name="apiClient">API client instance</param>
        /// <param name="logger">Logger instance</param>
        public AuthenticationApi(IApiClient apiClient, ILogger<AuthenticationApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <inheritdoc />
        public async Task<LongTermAccessTokenResponse> GetLongTermAccessTokenAsync(
            LongTermAccessTokenRequest request, 
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting long-term access token for app_id: {AppId}", request.AppId);

            var response = await _apiClient.CallApiAsync<LongTermAccessTokenResponse>(
                ApiEndpoints.Authentication.GetLongTermAccessToken,
                HttpMethod.Post,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        public async Task<AccessTokenResponse> GetAccessTokenAsync(
            AccessTokenRequest request, 
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting access token for client_id: {ClientId}", request.ClientId);

            var response = await _apiClient.CallApiAsync<AccessTokenResponse>(
                ApiEndpoints.Authentication.GetAccessToken,
                HttpMethod.Post,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        public async Task<RevokeLongTermTokenResponse> RevokeLongTermAccessTokenAsync(
            RevokeLongTermTokenRequest request, 
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Revoking long-term access token for app_id: {AppId}", request.AppId);

            var headerParams = new System.Collections.Generic.Dictionary<string, string>
            {
                { "Access-Token", request.AccessToken },
                { "Content-Type", "application/json" }
            };


            var response = await _apiClient.CallApiAsync<RevokeLongTermTokenResponse>(
                ApiEndpoints.Authentication.RevokeLongTermAccessToken,
                HttpMethod.Post,
                queryParams: null,
                body: request,
                headerParams: headerParams,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        public async Task<ShortTermAccessTokenResponse> GetShortTermAccessTokenAsync(
            ShortTermAccessTokenRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting short-term access token for client_id: {ClientId}", request.ClientId);

            var response = await _apiClient.CallApiAsync<ShortTermAccessTokenResponse>(
                ApiEndpoints.Authentication.GetShortTermAccessToken,
                HttpMethod.Post,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        public async Task<RefreshShortTermTokenResponse> RefreshShortTermAccessTokenAsync(
            RefreshShortTermTokenRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Refreshing short-term access token for client_id: {ClientId}", request.ClientId);

            var response = await _apiClient.CallApiAsync<RefreshShortTermTokenResponse>(
                ApiEndpoints.Authentication.RefreshShortTermAccessToken,
                HttpMethod.Post,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }

        /// <inheritdoc />
        public async Task<RevokeShortTermTokenResponse> RevokeShortTermAccessTokenAsync(
            RevokeShortTermTokenRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Revoking short-term access token for client_id: {ClientId}", request.ClientId);

            var response = await _apiClient.CallApiAsync<RevokeShortTermTokenResponse>(
                ApiEndpoints.Authentication.RevokeShortTermAccessToken,
                HttpMethod.Post,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);

            return response;
        }
    }
}
