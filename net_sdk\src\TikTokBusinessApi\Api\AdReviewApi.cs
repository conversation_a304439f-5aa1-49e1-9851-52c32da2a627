/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Ad Review operations
    /// </summary>
    public class AdReviewApi : IAdReviewApi
    {
        private readonly IApiClient _apiClient;

        /// <summary>
        /// Initializes a new instance of the AdReviewApi class
        /// </summary>
        /// <param name="apiClient">API client for making requests</param>
        public AdReviewApi(IApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        }

        /// <summary>
        /// Get review information for ad groups. An ad group needs to be approved before it can be deployed,
        /// and it may get rejected due to various reasons, such as incorrect placement or targeting selections.
        /// Based on the rejection reasons and suggestions you get, you can adjust your ad group accordingly.
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="adGroupIds">List of ad group IDs (maximum 20)</param>
        /// <param name="language">Language for the response (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad group review information response</returns>
        public async Task<AdGroupReviewInfoResponse> GetAdGroupReviewInfoAsync(
            string advertiserId,
            List<string> adGroupIds,
            string? language = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (adGroupIds == null || adGroupIds.Count == 0)
                throw new ArgumentException("Ad group IDs cannot be null or empty", nameof(adGroupIds));
            if (adGroupIds.Count > 20)
                throw new ArgumentException("Maximum 20 ad group IDs are supported", nameof(adGroupIds));

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["adgroup_ids"] = string.Join(",", adGroupIds)
            };

            if (!string.IsNullOrEmpty(language))
                queryParams["lang"] = language;

            return await _apiClient.CallApiAsync<AdGroupReviewInfoResponse>(
                path: $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdReview.GetAdGroupReviewInfo}",
                method: HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get review information for ads.
        /// </summary>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="adIds">List of ad IDs (1-100)</param>
        /// <param name="language">Language for the response (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad review information response</returns>
        public async Task<AdReviewInfoResponse> GetAdReviewInfoAsync(
            string advertiserId,
            List<string> adIds,
            string? language = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (adIds == null || adIds.Count == 0)
                throw new ArgumentException("Ad IDs cannot be null or empty", nameof(adIds));
            if (adIds.Count > 100)
                throw new ArgumentException("Maximum 100 ad IDs are supported", nameof(adIds));

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["ad_ids"] = string.Join(",", adIds)
            };

            if (!string.IsNullOrEmpty(language))
                queryParams["lang"] = language;

            return await _apiClient.CallApiAsync<AdReviewInfoResponse>(
                path: $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdReview.GetAdReviewInfo}",
                method: HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Appeal the rejection decision for an ad group, requesting re-evaluation when your ad group is rejected during review.
        /// After the appeal, you need to wait for some time and call the GetAdGroupReviewInfoAsync method to check the appeal status
        /// and see if the review status is updated.
        /// </summary>
        /// <param name="body">Appeal request body</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Ad group appeal response</returns>
        // public async Task<AdGroupAppealResponse> AppealAdGroupRejectionAsync(
        //     AdGroupAppealRequest body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));
        //     if (string.IsNullOrWhiteSpace(body.AdvertiserId))
        //         throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(body));
        //     if (string.IsNullOrWhiteSpace(body.AdGroupId))
        //         throw new ArgumentException("Ad group ID cannot be null or empty", nameof(body));

        //     return await _apiClient.CallApiAsync<AdGroupAppealResponse>(
        //         path: $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{ApiEndpoints.AdReview.AppealAdGroupRejection}",
        //         method: HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }
    }
}
