# AdCommentApi Documentation

The `AdCommentApi` provides methods to manage comments on TikTok video ads, including retrieving, replying to, and moderating comments, as well as managing blocked words.

## Overview

The AdCommentApi allows you to:
- Get comments under video ads of an ad account
- Get related comments for original comments or replies
- Reply to comments on video ads
- Create and manage comment export tasks
- Hide, show, pin, unpin, and delete comments
- <PERSON><PERSON> blocked words for comment filtering

## Available Methods

### GetCommentsAsync

Retrieves comments under the video ads of an ad account.

**Endpoint:** `/comment/list/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| accessToken | string | Yes | Authorized access token |
| advertiserId | string | Yes | Advertiser ID |
| searchField | string | Yes | Field to search by (ADGROUP_ID) |
| searchValue | string | Yes | Value to search (ad group ID) |
| startTime | string | Yes | Start date (YYYY-MM-DD) |
| endTime | string | Yes | End date (YYYY-MM-DD) |
| commentType | List<string> | No | Comment type (ALL, COMMENT, REPLY) |
| sortField | string | No | Field to sort by (CREATE_TIME, LIKES, REPLIES) |
| sortType | string | No | Sorting order (ASC, DESC) |
| pageSize | int | No | Page size (1-100) |
| page | int | No | Current page number |
| cancellationToken | CancellationToken | No | Cancellation token |

#### Response

Returns `Response<AdCommentListResponse>` containing:
- `Comments` - List of comment data
- `PageInfo` - Pagination information

#### Example

```csharp
var response = await client.AdComment.GetCommentsAsync(
    accessToken: "your-access-token",
    advertiserId: "your-advertiser-id",
    searchField: "ADGROUP_ID",
    searchValue: "your-adgroup-id",
    startTime: "2023-01-01",
    endTime: "2023-01-31",
    commentType: new List<string> { "ALL" },
    sortField: "CREATE_TIME",
    sortType: "DESC",
    pageSize: 10,
    page: 1
);

foreach (var comment in response.Data.Comments)
{
    Console.WriteLine($"Comment: {comment.Content} by {comment.UserName}");
}
```

### GetRelatedCommentsAsync

Gets related comments for an original comment or reply.

**Endpoint:** `/comment/reference/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| accessToken | string | Yes | Authorized access token |
| advertiserId | string | Yes | Advertiser ID |
| commentId | string | Yes | Comment ID |
| commentType | string | Yes | Comment type (COMMENT or REPLY) |
| originalCommentId | string | No | ID of the original comment (for replies) |
| pageSize | int | No | Page size (1-1000) |
| page | int | No | Current page number |
| cancellationToken | CancellationToken | No | Cancellation token |

#### Example

```csharp
var response = await client.AdComment.GetRelatedCommentsAsync(
    accessToken: "your-access-token",
    advertiserId: "your-advertiser-id",
    commentId: "comment-id",
    commentType: "COMMENT"
);
```

### ReplyToCommentAsync

Replies to a comment on a video ad.

**Endpoint:** `/comment/post/`  
**Method:** POST  
**Version:** v1.3

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| accessToken | string | Yes | Authorized access token |
| body | AdCommentReplyRequest | Yes | Reply request body |
| cancellationToken | CancellationToken | No | Cancellation token |

#### Example

```csharp
var replyRequest = new AdCommentReplyRequest
{
    AdvertiserId = "your-advertiser-id",
    AdId = "your-ad-id",
    TikTokItemId = "tiktok-video-id",
    CommentId = "comment-to-reply-to",
    CommentType = "REPLY",
    Text = "Thank you for your comment!",
    IdentityType = "TT_USER",
    IdentityId = "your-identity-id"
};

var response = await client.AdComment.ReplyToCommentAsync(
    accessToken: "your-access-token",
    body: replyRequest
);
```

### CreateExportTaskAsync

Creates a comment export task for downloading comment data.

**Endpoint:** `/comment/task/create/`  
**Method:** POST  
**Version:** v1.3

#### Example

```csharp
var exportRequest = new AdCommentExportTaskRequest
{
    AdvertiserId = "your-advertiser-id",
    SearchField = "ADGROUP_ID",
    SearchValue = "your-adgroup-id",
    StartTime = "2023-01-01",
    EndTime = "2023-01-31",
    CommentType = new List<string> { "ALL" },
    CommentStatus = new List<string> { "ALL" },
    Language = "EN"
};

var response = await client.AdComment.CreateExportTaskAsync(
    accessToken: "your-access-token",
    body: exportRequest
);

string taskId = response.Data.TaskId;
```

### CheckExportTaskStatusAsync

Checks the status of a comment export task.

**Endpoint:** `/comment/task/check/`  
**Method:** GET  
**Version:** v1.3

#### Example

```csharp
var response = await client.AdComment.CheckExportTaskStatusAsync(
    accessToken: "your-access-token",
    advertiserId: "your-advertiser-id",
    taskId: "task-id"
);

Console.WriteLine($"Task Status: {response.Data.Status}");
```

### Comment Management Methods

The API provides several methods for managing comments:

- `HideCommentsAsync` - Hide comments
- `ShowCommentsAsync` - Show hidden comments
- `PinCommentsAsync` - Pin comments
- `UnpinCommentsAsync` - Unpin comments
- `DeleteCommentsAsync` - Delete comments

#### Example

```csharp
var managementRequest = new AdCommentManagementRequest
{
    AdvertiserId = "your-advertiser-id",
    CommentIds = new List<string> { "comment-id-1", "comment-id-2" }
};

// Hide comments
await client.AdComment.HideCommentsAsync(
    accessToken: "your-access-token",
    body: managementRequest
);

// Pin comments
await client.AdComment.PinCommentsAsync(
    accessToken: "your-access-token",
    body: managementRequest
);
```

### Blocked Words Management

Manage blocked words for automatic comment filtering:

- `CreateBlockedWordsAsync` - Add words to blocked list
- `GetBlockedWordsAsync` - Get current blocked words
- `UpdateBlockedWordsAsync` - Update blocked words
- `DeleteBlockedWordsAsync` - Remove blocked words

#### Example

```csharp
// Create blocked words
var createRequest = new AdCommentBlockedWordsCreateRequest
{
    AdvertiserId = "your-advertiser-id",
    BlockedWords = new List<string> { "spam", "inappropriate" }
};

await client.AdComment.CreateBlockedWordsAsync(
    accessToken: "your-access-token",
    body: createRequest
);

// Get blocked words
var blockedWords = await client.AdComment.GetBlockedWordsAsync(
    accessToken: "your-access-token",
    advertiserId: "your-advertiser-id"
);
```

## Error Handling

All methods may throw the following exceptions:
- `ArgumentException` - When required parameters are null or empty
- `ArgumentNullException` - When required objects are null
- `SdkException` - When the API returns an error response
- `ApiException` - When the HTTP request fails

## API Versions

The AdCommentApi uses TikTok Business API v1.3. Key features:
- String-based IDs (advertiser_id, comment_id, etc.)
- Enhanced response fields (user_id, video_play_url, etc.)
- Improved pagination and filtering options

For more information, refer to the [TikTok Business API documentation](https://business-api.tiktok.com/portal/docs).
