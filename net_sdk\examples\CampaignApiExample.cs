/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the Campaign API
    /// </summary>
    public class CampaignApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public CampaignApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example of getting campaigns
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task GetCampaignsExample(string advertiserId)
        {
            try
            {
                Console.WriteLine("=== Get Campaigns Example ===");

                // Set up filtering conditions (optional)
                var filtering = new CampaignFiltering
                {
                    ObjectiveType = "TRAFFIC",
                    PrimaryStatus = "ENABLE"
                };

                // Specify which fields to return (optional)
                var fields = new List<string>
                {
                    "campaign_id",
                    "campaign_name",
                    "objective_type",
                    "budget",
                    "budget_mode",
                    "operation_status",
                    "create_time"
                };

                var request = new CampaignGetRequest
                {
                    AdvertiserId = advertiserId,
                    Filtering = filtering,
                    Fields = fields,
                    Page = 1,
                    PageSize = 20
                };

                Console.WriteLine("Getting campaigns...");

                var response = await _client.Campaign.GetAsync(request);

                if (response.Data?.List != null)
                {
                    Console.WriteLine($"Found {response.Data.List.Count} campaigns:");
                    foreach (var campaign in response.Data.List)
                    {
                        Console.WriteLine($"- Campaign ID: {campaign.CampaignId}");
                        Console.WriteLine($"  Name: {campaign.CampaignName}");
                        Console.WriteLine($"  Objective: {campaign.ObjectiveType}");
                        Console.WriteLine($"  Budget: {campaign.Budget}");
                        Console.WriteLine($"  Status: {campaign.OperationStatus}");
                        Console.WriteLine();
                    }

                    if (response.Data.PageInfo != null)
                    {
                        Console.WriteLine($"Page: {response.Data.PageInfo.Page}");
                        Console.WriteLine($"Total: {response.Data.PageInfo.TotalNumber}");
                    }
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of creating a campaign
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="campaignName">Name for the new campaign</param>
        /// <returns>Task representing the async operation</returns>
        public async Task CreateCampaignExample(string advertiserId, string campaignName)
        {
            try
            {
                Console.WriteLine("=== Create Campaign Example ===");

                // Create campaign data
                var campaignData = new CampaignCreateData
                {
                    CampaignName = campaignName,
                    ObjectiveType = "TRAFFIC",
                    BudgetMode = "BUDGET_MODE_DAY",
                    Budget = 10000000, // $10 in micros
                    BudgetOptimizeOn = false,
                    BidType = "BID_TYPE_NO_BID",
                    OptimizationGoal = "CLICK"
                };

                var request = new CampaignCreateRequest
                {
                    AdvertiserId = advertiserId,
                    Campaigns = new List<CampaignCreateData> { campaignData }
                };

                Console.WriteLine($"Creating campaign: {campaignName}");

                var response = await _client.Campaign.CreateAsync(request);

                if (response.Data?.Data != null)
                {
                    Console.WriteLine("Campaign creation results:");
                    foreach (var result in response.Data.Data)
                    {
                        if (result.Code == 0)
                        {
                            Console.WriteLine($"✓ Campaign created successfully!");
                            Console.WriteLine($"  Campaign ID: {result.CampaignId}");
                        }
                        else
                        {
                            Console.WriteLine($"✗ Campaign creation failed:");
                            Console.WriteLine($"  Error Code: {result.Code}");
                            Console.WriteLine($"  Error Message: {result.Message}");
                        }
                    }
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of updating a campaign
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="campaignId">Campaign ID to update</param>
        /// <param name="newCampaignName">New campaign name</param>
        /// <param name="newBudget">New budget amount</param>
        /// <returns>Task representing the async operation</returns>
        public async Task UpdateCampaignExample(string advertiserId, string campaignId, string newCampaignName, float newBudget)
        {
            try
            {
                Console.WriteLine("=== Update Campaign Example ===");

                var campaignUpdate = new CampaignUpdateData
                {
                    CampaignId = campaignId,
                    CampaignName = newCampaignName,
                    Budget = newBudget
                };

                var request = new CampaignUpdateRequest
                {
                    AdvertiserId = advertiserId,
                    Campaigns = new List<CampaignUpdateData> { campaignUpdate }
                };

                Console.WriteLine($"Updating campaign: {campaignId}");
                Console.WriteLine($"New name: {newCampaignName}");
                Console.WriteLine($"New budget: {newBudget}");

                var response = await _client.Campaign.UpdateAsync(request);

                if (response.Data?.Data != null)
                {
                    Console.WriteLine("Campaign update results:");
                    foreach (var result in response.Data.Data)
                    {
                        if (result.Code == 0)
                        {
                            Console.WriteLine($"✓ Campaign updated successfully!");
                            Console.WriteLine($"  Campaign ID: {result.CampaignId}");
                        }
                        else
                        {
                            Console.WriteLine($"✗ Campaign update failed:");
                            Console.WriteLine($"  Error Code: {result.Code}");
                            Console.WriteLine($"  Error Message: {result.Message}");
                        }
                    }
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of updating campaign status
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="campaignIds">List of campaign IDs to update</param>
        /// <param name="operationStatus">New operation status (ENABLE, DISABLE, DELETE)</param>
        /// <returns>Task representing the async operation</returns>
        public async Task UpdateCampaignStatusExample(string advertiserId, List<string> campaignIds, string operationStatus)
        {
            try
            {
                Console.WriteLine("=== Update Campaign Status Example ===");

                var request = new CampaignStatusUpdateRequest
                {
                    AdvertiserId = advertiserId,
                    CampaignIds = campaignIds,
                    OperationStatus = operationStatus
                };

                Console.WriteLine($"Updating status for {campaignIds.Count} campaigns to: {operationStatus}");

                var response = await _client.Campaign.UpdateStatusAsync(request);

                if (response.Data?.Data != null)
                {
                    Console.WriteLine("Campaign status update results:");
                    foreach (var result in response.Data.Data)
                    {
                        if (result.Code == 0)
                        {
                            Console.WriteLine($"✓ Campaign status updated successfully!");
                            Console.WriteLine($"  Campaign ID: {result.CampaignId}");
                        }
                        else
                        {
                            Console.WriteLine($"✗ Campaign status update failed:");
                            Console.WriteLine($"  Campaign ID: {result.CampaignId}");
                            Console.WriteLine($"  Error Code: {result.Code}");
                            Console.WriteLine($"  Error Message: {result.Message}");
                        }
                    }
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of getting campaign quota information
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="adNetworkIds">List of ad network IDs (optional)</param>
        /// <returns>Task representing the async operation</returns>
        public async Task GetCampaignQuotaInfoExample(string advertiserId, List<string>? adNetworkIds = null)
        {
            try
            {
                Console.WriteLine("=== Get Campaign Quota Info Example ===");

                var request = new CampaignQuotaInfoGetRequest
                {
                    AdvertiserId = advertiserId,
                    AdNetworkIds = adNetworkIds
                };

                Console.WriteLine("Getting campaign quota information...");

                var response = await _client.Campaign.GetQuotaInfoAsync(request);

                if (response.Data?.List != null)
                {
                    Console.WriteLine($"Found quota info for {response.Data.List.Count} ad networks:");
                    foreach (var quotaInfo in response.Data.List)
                    {
                        Console.WriteLine($"- Ad Network: {quotaInfo.AdNetworkName} ({quotaInfo.AdNetworkId})");
                        Console.WriteLine($"  Quota: {quotaInfo.Quota}");
                        Console.WriteLine($"  Used: {quotaInfo.Used}");
                        Console.WriteLine($"  Available: {(quotaInfo.Quota ?? 0) - (quotaInfo.Used ?? 0)}");
                        Console.WriteLine();
                    }
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of creating a campaign copy task
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="sourceCampaignId">Source campaign ID to copy from</param>
        /// <param name="targetAdvertiserId">Target advertiser ID to copy to</param>
        /// <param name="newCampaignName">Name for the copied campaign</param>
        /// <returns>Task representing the async operation</returns>
        public async Task CreateCampaignCopyTaskExample(string advertiserId, string sourceCampaignId, string targetAdvertiserId, string newCampaignName)
        {
            try
            {
                Console.WriteLine("=== Create Campaign Copy Task Example ===");

                var request = new CampaignCopyTaskCreateRequest
                {
                    AdvertiserId = advertiserId,
                    SourceCampaignId = sourceCampaignId,
                    TargetAdvertiserId = targetAdvertiserId,
                    CampaignName = newCampaignName,
                    CopyAdgroup = true,
                    CopyAd = true
                };

                Console.WriteLine($"Creating copy task for campaign: {sourceCampaignId}");
                Console.WriteLine($"Target advertiser: {targetAdvertiserId}");
                Console.WriteLine($"New campaign name: {newCampaignName}");

                var response = await _client.Campaign.CreateCopyTaskAsync(request);

                if (response.Data?.TaskId != null)
                {
                    Console.WriteLine($"✓ Copy task created successfully!");
                    Console.WriteLine($"  Task ID: {response.Data.TaskId}");
                    Console.WriteLine($"  Use this Task ID to check the copy status");
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of checking campaign copy task status
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <param name="taskId">Task ID from the copy task creation</param>
        /// <returns>Task representing the async operation</returns>
        public async Task CheckCampaignCopyTaskExample(string advertiserId, string taskId)
        {
            try
            {
                Console.WriteLine("=== Check Campaign Copy Task Example ===");

                var request = new CampaignCopyTaskCheckRequest
                {
                    AdvertiserId = advertiserId,
                    TaskId = taskId
                };

                Console.WriteLine($"Checking copy task status: {taskId}");

                var response = await _client.Campaign.CheckCopyTaskAsync(request);

                if (response.Data != null)
                {
                    Console.WriteLine($"Task Status: {response.Data.Status}");

                    if (response.Data.Result != null)
                    {
                        var result = response.Data.Result;
                        Console.WriteLine($"New Campaign ID: {result.CampaignId}");

                        if (result.AdgroupList != null)
                        {
                            Console.WriteLine($"Copied Ad Groups: {result.AdgroupList.Count}");
                            foreach (var adgroup in result.AdgroupList)
                            {
                                if (adgroup.Code == 0)
                                {
                                    Console.WriteLine($"  ✓ Ad Group: {adgroup.SourceAdgroupId} → {adgroup.AdgroupId}");
                                }
                                else
                                {
                                    Console.WriteLine($"  ✗ Ad Group: {adgroup.SourceAdgroupId} - Error: {adgroup.Message}");
                                }
                            }
                        }

                        if (result.AdList != null)
                        {
                            Console.WriteLine($"Copied Ads: {result.AdList.Count}");
                            foreach (var ad in result.AdList)
                            {
                                if (ad.Code == 0)
                                {
                                    Console.WriteLine($"  ✓ Ad: {ad.SourceAdId} → {ad.AdId}");
                                }
                                else
                                {
                                    Console.WriteLine($"  ✗ Ad: {ad.SourceAdId} - Error: {ad.Message}");
                                }
                            }
                        }
                    }
                }
            }
            catch (ApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
                Console.WriteLine($"Status Code: {ex.StatusCode}");
            }
            catch (SdkException ex)
            {
                Console.WriteLine($"SDK Error: {ex.Message}");
                Console.WriteLine($"Error Code: {ex.ErrorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Complete example workflow demonstrating campaign management
        /// </summary>
        /// <param name="advertiserId">Your advertiser ID</param>
        /// <returns>Task representing the async operation</returns>
        public async Task CompleteWorkflowExample(string advertiserId)
        {
            Console.WriteLine("=== Complete Campaign Management Workflow ===");

            // 1. Get existing campaigns
            await GetCampaignsExample(advertiserId);

            // 2. Create a new campaign
            await CreateCampaignExample(advertiserId, "SDK Test Campaign");

            // 3. Get quota information
            await GetCampaignQuotaInfoExample(advertiserId);

            Console.WriteLine("Workflow completed!");
        }
    }
}
