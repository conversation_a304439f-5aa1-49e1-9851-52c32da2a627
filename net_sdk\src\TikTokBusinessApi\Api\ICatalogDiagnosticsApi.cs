/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Catalog Diagnostics operations
    /// </summary>
    public interface ICatalogDiagnosticsApi
    {
        /// <summary>
        /// Get synchronous catalog product diagnostic information
        /// </summary>
        /// <param name="catalogId">ID of a catalog that you have permission to access</param>
        /// <param name="bcId">ID of a Business Center that either owns the catalog, or has been granted access to it as an asset</param>
        /// <param name="feedId">Feed ID. If not specified, the ID of the default feed for the catalog will be used. To retrieve the diagnostics for all feeds for the catalog, set this field to "ALL".</param>
        /// <param name="filtering">Filtering conditions</param>
        /// <param name="lang">The language you want to set for the returned issue_title and reason_and_suggestion. Default value: "en".</param>
        /// <param name="page">Current page number. Default value: 1. Value range: ≥1.</param>
        /// <param name="pageSize">Page size. Default value: 10. Value range: [1, 20].</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Catalog diagnostic information</returns>
        Task<GetCatalogDiagnosticsResponse> GetCatalogDiagnosticsAsync(
            string catalogId,
            string bcId,
            string? feedId = null,
            CatalogDiagnosticsFiltering? filtering = null,
            string? lang = null,
            int? page = null,
            int? pageSize = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Create an asynchronous download task for catalog product diagnostic information
        /// </summary>
        /// <param name="request">Request containing catalog and task information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task creation response containing task ID</returns>
        //Task<CreateDiagnosticTaskResponse> CreateDiagnosticTaskAsync(
        //    CreateDiagnosticTaskRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Download asynchronous catalog product diagnostic information
        /// </summary>
        /// <param name="catalogId">Catalog ID that you used to create the download task for catalog diagnostic information</param>
        /// <param name="bcId">Business Center ID that you used to create the download task for catalog diagnostic information</param>
        /// <param name="taskId">ID of the download task for catalog diagnostic information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task status and download URL</returns>
        Task<GetDiagnosticTaskResponse> GetDiagnosticTaskAsync(
            string catalogId,
            string bcId,
            string taskId,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get catalog event source diagnostic information
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="eventSourceType">The event source type. Enum values: "APP", "PIXEL"</param>
        /// <param name="appId">Required when event_source_type is set to "APP". The App ID.</param>
        /// <param name="pixelCode">Required when event_source_type is set to "PIXEL". The Pixel code.</param>
        /// <param name="eventType">The event type that you want to retrieve data for. Enum values: "VIEW_CONTENT", "ADD_TO_CART", "PURCHASE". Default value: "VIEW_CONTENT".</param>
        /// <param name="timeRange">The time range that you want to retrieve data for. Enum values: "YESTERDAY", "LAST_7_DAYS", "LAST_30_DAYS". Default value: "LAST_7_DAYS".</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Event source diagnostic issues</returns>
        Task<GetEventSourceIssuesResponse> GetEventSourceIssuesAsync(
            string bcId,
            string catalogId,
            string eventSourceType,
            string? appId = null,
            string? pixelCode = null,
            string? eventType = null,
            string? timeRange = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get catalog event trends and match rate
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="eventSourceType">The event source type. Enum values: "APP", "PIXEL"</param>
        /// <param name="appId">Required when event_source_type is set to "APP". The App ID.</param>
        /// <param name="pixelCode">Required when event_source_type is set to "PIXEL". The Pixel code.</param>
        /// <param name="eventType">The event type that you want to retrieve data for. Enum values: "VIEW_CONTENT", "ADD_TO_CART", "PURCHASE". Default value: "VIEW_CONTENT".</param>
        /// <param name="timeRange">The time range that you want to retrieve data for. Enum values: "YESTERDAY", "LAST_7_DAYS", "LAST_30_DAYS". Default value: "LAST_7_DAYS".</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Event source metrics and trends</returns>
        Task<GetEventSourceMetricsResponse> GetEventSourceMetricsAsync(
            string bcId,
            string catalogId,
            string eventSourceType,
            string? appId = null,
            string? pixelCode = null,
            string? eventType = null,
            string? timeRange = null,
            CancellationToken cancellationToken = default);
    }
}
