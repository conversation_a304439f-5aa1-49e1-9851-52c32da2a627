/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Catalog Event Sources operations
    /// </summary>
    public interface ICatalogEventSourcesApi
    {
        /// <summary>
        /// Bind an event source to a catalog
        /// Use this endpoint to bind an app event or a website event to a catalog in a Business Center.
        /// </summary>
        /// <param name="body">Request body containing event source binding information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> BindEventSourceAsync(
        //    CatalogEventSourceBindBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Unbind an event source from a catalog
        /// Use this endpoint to unbind an app event or a website event from a catalog in a Business Center.
        /// </summary>
        /// <param name="body">Request body containing event source unbinding information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        //Task<Response<object>> UnbindEventSourceAsync(
        //    CatalogEventSourceUnbindBody body,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get event source binding info of a catalog
        /// Use this endpoint to get the binding information about app or web event sources of a catalog in a Business Center.
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing event source binding information</returns>
        Task<CatalogEventSourceBindResponse> GetEventSourceBindingInfoAsync(
            string bcId,
            string catalogId,
            CancellationToken cancellationToken = default);
    }
}
