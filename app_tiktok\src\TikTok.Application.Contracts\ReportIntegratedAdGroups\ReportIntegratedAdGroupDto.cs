using System;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.ReportIntegratedAdGroups
{
    /// <summary>
    /// DTO cho báo cáo tích hợp AdGroup
    /// </summary>
    public class ReportIntegratedAdGroupDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// ID tài khoản nhà quảng cáo - Được hỗ trợ ở cấp độ Nhà quảng cáo, Chiến dịch, Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdvertiserId { get; set; }

        /// <summary>
        /// ID chiến dịch - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CampaignId { get; set; }

        /// <summary>
        /// ID nhóm quảng cáo - Được hỗ trợ ở cấp độ Quảng cáo
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AdGroupId { get; set; }

        /// <summary>
        /// Tên nhóm quảng cáo - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [Required]
        [StringLength(255)]
        public string AdGroupName { get; set; }

        /// <summary>
        /// Ngày tổng hợp báo cáo
        /// </summary>
        [Required]
        public DateTime Date { get; set; }

        /// <summary>
        /// Chi tiêu - Tổng chi phí quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal Spend { get; set; }

        /// <summary>
        /// Lần hiển thị - Số lần quảng cáo của bạn được hiển thị
        /// </summary>
        [Required]
        public long Impressions { get; set; }

        /// <summary>
        /// Lần nhấp (đích đến) - Số lần nhấp từ quảng cáo của bạn đến một đích đến được chỉ định
        /// </summary>
        [Required]
        public long Clicks { get; set; }

        /// <summary>
        /// CTR (đích đến) - Tỷ lệ phần trăm lần hiển thị dẫn đến một lần nhấp đích đến trên tổng số lần hiển thị
        /// </summary>
        [Required]
        public decimal Ctr { get; set; }

        /// <summary>
        /// CPM - Số tiền trung bình bạn chi cho 1.000 lần hiển thị
        /// </summary>
        [Required]
        public decimal Cpm { get; set; }

        /// <summary>
        /// CPC (đích đến) - Chi phí trung bình cho mỗi lần nhấp đến một đích đến được chỉ định
        /// </summary>
        [Required]
        public decimal Cpc { get; set; }

        /// <summary>
        /// Chuyển đổi - Số lần quảng cáo của bạn dẫn đến sự kiện tối ưu hóa mà bạn đã chọn
        /// </summary>
        [Required]
        public long Conversion { get; set; }

        /// <summary>
        /// Chi phí mỗi chuyển đổi - Số tiền trung bình chi cho một chuyển đổi
        /// </summary>
        [Required]
        public decimal CostPerConversion { get; set; }

        /// <summary>
        /// Tỷ lệ chuyển đổi (CVR) - Tỷ lệ phần trăm chuyển đổi bạn nhận được trên tổng số lần hiển thị trên quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal ConversionRateV2 { get; set; }

        /// <summary>
        /// Tiếp cận - Số lượng người dùng duy nhất đã xem quảng cáo của bạn ít nhất một lần
        /// </summary>
        [Required]
        public long Reach { get; set; }

        /// <summary>
        /// Tần suất - Số lần trung bình mỗi người dùng xem quảng cáo của bạn trong một khoảng thời gian nhất định
        /// </summary>
        [Required]
        public decimal Frequency { get; set; }

        /// <summary>
        /// ROAS (Shop) - Lợi tức trên chi phí quảng cáo (ROAS) từ tổng doanh thu TikTok Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal OnsiteShoppingRoas { get; set; }

        /// <summary>
        /// Tổng doanh thu (Shop) - Tổng doanh thu của các đơn hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal TotalOnsiteShoppingValue { get; set; }

        /// <summary>
        /// Mua hàng (Shop) - Số lượng hành động gửi đơn hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public long OnsiteShopping { get; set; }

        /// <summary>
        /// Chi phí mỗi lần mua hàng (Shop) - Chi phí trung bình cho mỗi lần mua hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal CostPerOnsiteShopping { get; set; }

        /// <summary>
        /// Giá trị đơn hàng trung bình (Shop) - Giá trị đơn hàng trung bình cho mỗi lần mua hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public decimal ValuePerOnsiteShopping { get; set; }

        /// <summary>
        /// Lượt xem trang sản phẩm (Shop) - Số lượng lượt xem trang chi tiết sản phẩm Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public long OnsiteOnWebDetail { get; set; }

        /// <summary>
        /// Thêm vào giỏ hàng (Shop) - Số lượng hành động thêm vào giỏ hàng Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public long OnsiteOnWebCart { get; set; }

        /// <summary>
        /// Bắt đầu thanh toán (Shop) - Số lượng hành động bắt đầu thanh toán trong Shop được ghi nhận cho quảng cáo của bạn
        /// </summary>
        [Required]
        public long OnsiteInitiateCheckoutCount { get; set; }

        /// <summary>
        /// Loại vị trí đặt quảng cáo - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? PlacementType { get; set; }

        /// <summary>
        /// Ngân sách nhóm quảng cáo - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? Budget { get; set; }

        /// <summary>
        /// Mục tiêu tối ưu hóa - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? SmartTarget { get; set; }

        /// <summary>
        /// Sự kiện Thanh toán - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? BillingEvent { get; set; }

        /// <summary>
        /// Chiến lược đấu giá - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? BidStrategy { get; set; }

        /// <summary>
        /// Giá đấu - Được hỗ trợ ở cấp độ Nhóm quảng cáo và Quảng cáo
        /// </summary>
        [StringLength(100)]
        public string? Bid { get; set; }

        /// <summary>
        /// Tiền tệ - Mã tiền tệ, ví dụ: USD
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Currency { get; set; }
    }
} 