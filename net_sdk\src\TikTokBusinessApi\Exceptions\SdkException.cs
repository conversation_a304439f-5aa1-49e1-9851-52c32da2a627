/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;

namespace TikTokBusinessApi.Exceptions
{
    /// <summary>
    /// Exception thrown by the SDK for business logic errors
    /// </summary>
    public class SdkException : Exception
    {
        /// <summary>
        /// Request ID for tracking
        /// </summary>
        public string? RequestId { get; set; }

        /// <summary>
        /// Error code from the API
        /// </summary>
        public int ErrorCode { get; set; }

        /// <summary>
        /// Error category based on the error code
        /// </summary>
        public TikTokErrorCategory ErrorCategory => TikTokErrorCodes.GetErrorCategory(ErrorCode);

        /// <summary>
        /// Whether this error is retryable
        /// </summary>
        public bool IsRetryable => TikTokErrorCodes.IsRetryable(ErrorCode);

        /// <summary>
        /// Additional error context and metadata
        /// </summary>
        public Dictionary<string, object> ErrorContext { get; set; } = new();

        /// <summary>
        /// HTTP status code if available
        /// </summary>
        public int? HttpStatusCode { get; set; }

        /// <summary>
        /// Raw response content from the API
        /// </summary>
        public string? RawResponse { get; set; }

        /// <summary>
        /// Initializes a new instance of the SdkException class
        /// </summary>
        public SdkException()
        {
        }

        /// <summary>
        /// Initializes a new instance of the SdkException class with a message
        /// </summary>
        /// <param name="message">Exception message</param>
        public SdkException(string message) : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the SdkException class with a message and inner exception
        /// </summary>
        /// <param name="message">Exception message</param>
        /// <param name="innerException">Inner exception</param>
        public SdkException(string message, Exception innerException) : base(message, innerException)
        {
        }

        /// <summary>
        /// Initializes a new instance of the SdkException class with full details
        /// </summary>
        /// <param name="requestId">Request ID</param>
        /// <param name="errorCode">Error code</param>
        /// <param name="message">Exception message</param>
        public SdkException(string requestId, int errorCode, string message) : base(message)
        {
            RequestId = requestId;
            ErrorCode = errorCode;
        }

        /// <summary>
        /// Initializes a new instance of the SdkException class with enhanced details
        /// </summary>
        /// <param name="requestId">Request ID</param>
        /// <param name="errorCode">Error code</param>
        /// <param name="message">Exception message</param>
        /// <param name="httpStatusCode">HTTP status code</param>
        /// <param name="rawResponse">Raw response content</param>
        /// <param name="errorContext">Additional error context</param>
        public SdkException(string requestId, int errorCode, string message, int? httpStatusCode = null,
            string? rawResponse = null, Dictionary<string, object>? errorContext = null) : base(message)
        {
            RequestId = requestId;
            ErrorCode = errorCode;
            HttpStatusCode = httpStatusCode;
            RawResponse = rawResponse;
            ErrorContext = errorContext ?? new Dictionary<string, object>();
        }

        /// <summary>
        /// Creates an SdkException from a TikTok API response
        /// </summary>
        /// <param name="requestId">Request ID</param>
        /// <param name="errorCode">TikTok error code</param>
        /// <param name="message">Error message</param>
        /// <param name="httpStatusCode">HTTP status code</param>
        /// <param name="rawResponse">Raw response content</param>
        /// <returns>Configured SdkException</returns>
        public static SdkException FromTikTokResponse(string requestId, int errorCode, string message,
            int? httpStatusCode = null, string? rawResponse = null)
        {
            var errorContext = new Dictionary<string, object>
            {
                ["ErrorCategory"] = TikTokErrorCodes.GetErrorCategory(errorCode).ToString(),
                ["ErrorDescription"] = TikTokErrorCodes.GetErrorDescription(errorCode),
                ["IsRetryable"] = TikTokErrorCodes.IsRetryable(errorCode),
                ["Timestamp"] = DateTimeOffset.UtcNow
            };

            return new SdkException(requestId, errorCode, message, httpStatusCode, rawResponse, errorContext);
        }

        /// <summary>
        /// Returns a string representation of the exception
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"SdkException: {Message} (Code: {ErrorCode}, RequestId: {RequestId})";
        }
    }
}
