/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Upload Products Via File

    /// <summary>
    /// Request body for uploading products via file URL
    /// </summary>
    public class ProductFileUploadBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID (optional)
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string? FeedId { get; set; }

        /// <summary>
        /// The download address of the CSV file
        /// </summary>
        [JsonPropertyName("file_url")]
        public string FileUrl { get; set; } = string.Empty;

        /// <summary>
        /// Update mode. Enum values: OVERWRITE, INCREMENTAL. Default: INCREMENTAL
        /// </summary>
        [JsonPropertyName("update_mode")]
        public string? UpdateMode { get; set; }

        /// <summary>
        /// Initializes a new instance of the ProductFileUploadBody class
        /// </summary>
        public ProductFileUploadBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ProductFileUploadBody class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="fileUrl">File URL</param>
        /// <param name="feedId">Feed ID (optional)</param>
        /// <param name="updateMode">Update mode (optional)</param>
        public ProductFileUploadBody(string bcId, string catalogId, string fileUrl, string? feedId = null, string? updateMode = null)
        {
            BcId = bcId;
            CatalogId = catalogId;
            FileUrl = fileUrl;
            FeedId = feedId;
            UpdateMode = updateMode;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductFileUploadBody(BcId={BcId}, CatalogId={CatalogId}, FileUrl={FileUrl})";
        }
    }

    /// <summary>
    /// Response for product file upload operations
    /// </summary>
    public class ProductFileUploadResponse
    {
        /// <summary>
        /// Catalog handling log ID
        /// </summary>
        [JsonPropertyName("feed_log_id")]
        public string FeedLogId { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the ProductFileUploadResponse class
        /// </summary>
        public ProductFileUploadResponse()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ProductFileUploadResponse class with parameters
        /// </summary>
        /// <param name="feedLogId">Feed log ID</param>
        public ProductFileUploadResponse(string feedLogId)
        {
            FeedLogId = feedLogId;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductFileUploadResponse(FeedLogId={FeedLogId})";
        }
    }

    #endregion

    #region Upload Products Via JSON

    /// <summary>
    /// Request body for uploading products via JSON schema
    /// </summary>
    public class ProductUploadBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID (optional)
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string? FeedId { get; set; }

        /// <summary>
        /// List of products (max 5000)
        /// </summary>
        [JsonPropertyName("products")]
        public List<object> Products { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the ProductUploadBody class
        /// </summary>
        public ProductUploadBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ProductUploadBody class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="products">List of products</param>
        /// <param name="feedId">Feed ID (optional)</param>
        public ProductUploadBody(string bcId, string catalogId, List<object> products, string? feedId = null)
        {
            BcId = bcId;
            CatalogId = catalogId;
            Products = products;
            FeedId = feedId;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductUploadBody(BcId={BcId}, CatalogId={CatalogId}, ProductCount={Products.Count})";
        }
    }

    #endregion

    #region Update Products

    /// <summary>
    /// Request body for updating products
    /// </summary>
    public class ProductUpdateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID (optional)
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string? FeedId { get; set; }

        /// <summary>
        /// List of products to update (max 5000)
        /// </summary>
        [JsonPropertyName("products")]
        public List<object> Products { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the ProductUpdateBody class
        /// </summary>
        public ProductUpdateBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ProductUpdateBody class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="products">List of products to update</param>
        /// <param name="feedId">Feed ID (optional)</param>
        public ProductUpdateBody(string bcId, string catalogId, List<object> products, string? feedId = null)
        {
            BcId = bcId;
            CatalogId = catalogId;
            Products = products;
            FeedId = feedId;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductUpdateBody(BcId={BcId}, CatalogId={CatalogId}, ProductCount={Products.Count})";
        }
    }

    #endregion

    #region Delete Products

    /// <summary>
    /// Request body for deleting products
    /// </summary>
    public class ProductDeleteBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID (optional)
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string? FeedId { get; set; }

        /// <summary>
        /// SKU IDs of E-commerce products to delete (max 1000)
        /// </summary>
        [JsonPropertyName("sku_ids")]
        public List<string>? SkuIds { get; set; }

        /// <summary>
        /// Hotel IDs of products to delete (max 1000)
        /// </summary>
        [JsonPropertyName("hotel_ids")]
        public List<string>? HotelIds { get; set; }

        /// <summary>
        /// Flight IDs of products to delete (max 1000)
        /// </summary>
        [JsonPropertyName("flight_ids")]
        public List<string>? FlightIds { get; set; }

        /// <summary>
        /// Destination IDs of products to delete (max 1000)
        /// </summary>
        [JsonPropertyName("destination_ids")]
        public List<string>? DestinationIds { get; set; }

        /// <summary>
        /// Vehicle IDs of products to delete (max 1000)
        /// </summary>
        [JsonPropertyName("vehicle_ids")]
        public List<string>? VehicleIds { get; set; }

        /// <summary>
        /// Initializes a new instance of the ProductDeleteBody class
        /// </summary>
        public ProductDeleteBody()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            var totalIds = (SkuIds?.Count ?? 0) + (HotelIds?.Count ?? 0) + (FlightIds?.Count ?? 0) + 
                          (DestinationIds?.Count ?? 0) + (VehicleIds?.Count ?? 0);
            return $"ProductDeleteBody(BcId={BcId}, CatalogId={CatalogId}, TotalIds={totalIds})";
        }
    }

    #endregion

    #region Get Products

    /// <summary>
    /// Request parameters for getting products
    /// </summary>
    public class ProductGetRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Current page number
        /// </summary>
        public int? Page { get; set; }

        /// <summary>
        /// Page size (default 10, max 500)
        /// </summary>
        public int? PageSize { get; set; }

        /// <summary>
        /// Product IDs to get (max 1000)
        /// </summary>
        public List<string>? ProductIds { get; set; }

        /// <summary>
        /// SKU IDs to get (max 1000)
        /// </summary>
        public List<string>? SkuIds { get; set; }

        /// <summary>
        /// Product set IDs to get (max 100)
        /// </summary>
        public List<string>? ProductSetIds { get; set; }

        /// <summary>
        /// Ordering settings
        /// </summary>
        public ProductOrderSettings? Order { get; set; }

        /// <summary>
        /// Filtering conditions
        /// </summary>
        public ProductFilterConditions? Conditions { get; set; }

        /// <summary>
        /// Initializes a new instance of the ProductGetRequest class
        /// </summary>
        public ProductGetRequest()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductGetRequest(BcId={BcId}, CatalogId={CatalogId}, Page={Page}, PageSize={PageSize})";
        }
    }

    /// <summary>
    /// Product ordering settings
    /// </summary>
    public class ProductOrderSettings
    {
        /// <summary>
        /// Order-by condition (currently only PRODUCT_AVAILABILITY is supported)
        /// </summary>
        [JsonPropertyName("order_condition")]
        public string? OrderCondition { get; set; }

        /// <summary>
        /// Custom ordering conditions
        /// </summary>
        [JsonPropertyName("custom_order")]
        public List<ProductCustomOrder>? CustomOrder { get; set; }
    }

    /// <summary>
    /// Custom ordering condition
    /// </summary>
    public class ProductCustomOrder
    {
        /// <summary>
        /// Data field to order by
        /// </summary>
        [JsonPropertyName("field")]
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// Ordering type: ASC (ascending) or DES (descending)
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;
    }

    /// <summary>
    /// Product filtering conditions
    /// </summary>
    public class ProductFilterConditions
    {
        /// <summary>
        /// AND logic conditions
        /// </summary>
        [JsonPropertyName("and")]
        public List<ProductFilterCondition>? And { get; set; }

        /// <summary>
        /// OR logic conditions
        /// </summary>
        [JsonPropertyName("or")]
        public List<ProductFilterCondition>? Or { get; set; }
    }

    /// <summary>
    /// Individual product filter condition
    /// </summary>
    public class ProductFilterCondition
    {
        /// <summary>
        /// Data field to filter by
        /// </summary>
        [JsonPropertyName("field")]
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// Filter operator: EQ, NE, GT, GTE, LT, LTE, RNG, WILDCARD, NOT_WILDCARD, PREFIX, EXIST, IN, NOT_IN
        /// </summary>
        [JsonPropertyName("operator")]
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// Filter values
        /// </summary>
        [JsonPropertyName("values")]
        public List<string> Values { get; set; } = new();

        /// <summary>
        /// Whether it is case-sensitive
        /// </summary>
        [JsonPropertyName("case_senstive")]
        public bool? CaseSensitive { get; set; }
    }

    /// <summary>
    /// Response for getting products
    /// </summary>
    public class ProductGetResponse
    {
        /// <summary>
        /// Product list
        /// </summary>
        [JsonPropertyName("list")]
        public List<ProductInfo>? List { get; set; }

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo? PageInfo { get; set; }

        /// <summary>
        /// Initializes a new instance of the ProductGetResponse class
        /// </summary>
        public ProductGetResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductGetResponse(ProductCount={List?.Count ?? 0})";
        }
    }

    #endregion

    #region Product Log

    /// <summary>
    /// Request parameters for getting product handling log
    /// </summary>
    public class ProductLogRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Product handling ID
        /// </summary>
        public string FeedLogId { get; set; } = string.Empty;

        /// <summary>
        /// Language for error messages (default: en)
        /// </summary>
        public string? Language { get; set; }

        /// <summary>
        /// Initializes a new instance of the ProductLogRequest class
        /// </summary>
        public ProductLogRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ProductLogRequest class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="feedLogId">Feed log ID</param>
        /// <param name="language">Language (optional)</param>
        public ProductLogRequest(string bcId, string catalogId, string feedLogId, string? language = null)
        {
            BcId = bcId;
            CatalogId = catalogId;
            FeedLogId = feedLogId;
            Language = language;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductLogRequest(BcId={BcId}, CatalogId={CatalogId}, FeedLogId={FeedLogId})";
        }
    }

    #endregion

    #region Product Information Models

    /// <summary>
    /// Product information
    /// </summary>
    public class ProductInfo
    {
        /// <summary>
        /// Audit information
        /// </summary>
        [JsonPropertyName("audit")]
        public ProductAudit? Audit { get; set; }

        /// <summary>
        /// Whether the product can be used to create ads (E-commerce only)
        /// </summary>
        [JsonPropertyName("ad_creation_eligible")]
        public string? AdCreationEligible { get; set; }

        /// <summary>
        /// Whether the product is activated
        /// </summary>
        [JsonPropertyName("active_status")]
        public string? ActiveStatus { get; set; }

        /// <summary>
        /// SKU ID (E-commerce products)
        /// </summary>
        [JsonPropertyName("sku_id")]
        public string? SkuId { get; set; }

        /// <summary>
        /// Product ID
        /// </summary>
        [JsonPropertyName("product_id")]
        public string? ProductId { get; set; }

        /// <summary>
        /// Product title
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// Product description
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Inventory status
        /// </summary>
        [JsonPropertyName("availability")]
        public string? Availability { get; set; }

        /// <summary>
        /// Product image URL
        /// </summary>
        [JsonPropertyName("image_url")]
        public string? ImageUrl { get; set; }

        /// <summary>
        /// Video URL
        /// </summary>
        [JsonPropertyName("video_url")]
        public string? VideoUrl { get; set; }

        /// <summary>
        /// Brand information
        /// </summary>
        [JsonPropertyName("brand")]
        public string? Brand { get; set; }

        /// <summary>
        /// Additional image URLs
        /// </summary>
        [JsonPropertyName("additional_image_urls")]
        public List<string>? AdditionalImageUrls { get; set; }

        /// <summary>
        /// Product SPU ID
        /// </summary>
        [JsonPropertyName("item_group_id")]
        public string? ItemGroupId { get; set; }

        /// <summary>
        /// Google product category
        /// </summary>
        [JsonPropertyName("google_product_category")]
        public string? GoogleProductCategory { get; set; }

        /// <summary>
        /// Global Trade Item Number (GTIN)
        /// </summary>
        [JsonPropertyName("global_trade_item_number")]
        public string? GlobalTradeItemNumber { get; set; }

        /// <summary>
        /// Manufacturer Part Number (MPN)
        /// </summary>
        [JsonPropertyName("manufacturer_part_number")]
        public string? ManufacturerPartNumber { get; set; }

        /// <summary>
        /// Product detail information
        /// </summary>
        [JsonPropertyName("profession")]
        public ProductDetail? Profession { get; set; }

        /// <summary>
        /// Price information
        /// </summary>
        [JsonPropertyName("price")]
        public ProductPriceInfo? Price { get; set; }

        /// <summary>
        /// Landing page information
        /// </summary>
        [JsonPropertyName("landing_page")]
        public ProductLandingPage? LandingPage { get; set; }

        /// <summary>
        /// Extra information
        /// </summary>
        [JsonPropertyName("extra_info")]
        public ProductExtraInfo? ExtraInfo { get; set; }

        /// <summary>
        /// Image storage status
        /// </summary>
        [JsonPropertyName("image_status")]
        public string? ImageStatus { get; set; }

        // Hotel-specific fields
        /// <summary>
        /// Hotel ID (hotel catalogs)
        /// </summary>
        [JsonPropertyName("hotel_id")]
        public string? HotelId { get; set; }

        /// <summary>
        /// Hotel name (hotel catalogs)
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Hotel category (hotel catalogs)
        /// </summary>
        [JsonPropertyName("hotel_category")]
        public string? HotelCategory { get; set; }

        // Flight-specific fields
        /// <summary>
        /// Flight ID (flight catalogs)
        /// </summary>
        [JsonPropertyName("flight_id")]
        public string? FlightId { get; set; }

        /// <summary>
        /// Origin airport (flight catalogs)
        /// </summary>
        [JsonPropertyName("origin_airport")]
        public string? OriginAirport { get; set; }

        /// <summary>
        /// Destination airport (flight catalogs)
        /// </summary>
        [JsonPropertyName("destination_airport")]
        public string? DestinationAirport { get; set; }

        // Vehicle-specific fields
        /// <summary>
        /// Vehicle ID (auto catalogs)
        /// </summary>
        [JsonPropertyName("vehicle_id")]
        public string? VehicleId { get; set; }

        /// <summary>
        /// Vehicle make/brand (auto catalogs)
        /// </summary>
        [JsonPropertyName("make")]
        public string? Make { get; set; }

        /// <summary>
        /// Vehicle model (auto catalogs)
        /// </summary>
        [JsonPropertyName("model")]
        public string? Model { get; set; }

        /// <summary>
        /// Vehicle year (auto catalogs)
        /// </summary>
        [JsonPropertyName("year")]
        public int? Year { get; set; }

        /// <summary>
        /// Initializes a new instance of the ProductInfo class
        /// </summary>
        public ProductInfo()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductInfo(ProductId={ProductId}, Title={Title}, SkuId={SkuId})";
        }
    }

    #endregion

    #region Supporting Product Models

    /// <summary>
    /// Product audit information
    /// </summary>
    public class ProductAudit
    {
        /// <summary>
        /// Audit status
        /// </summary>
        [JsonPropertyName("audit_status")]
        public string? AuditStatus { get; set; }

        /// <summary>
        /// Rejection reasons
        /// </summary>
        [JsonPropertyName("reject_reasons")]
        public List<string>? RejectReasons { get; set; }

        /// <summary>
        /// Audit message
        /// </summary>
        [JsonPropertyName("audit_message")]
        public string? AuditMessage { get; set; }
    }

    /// <summary>
    /// Product detail information
    /// </summary>
    public class ProductDetail
    {
        /// <summary>
        /// Product condition
        /// </summary>
        [JsonPropertyName("condition")]
        public string? Condition { get; set; }

        /// <summary>
        /// Age group
        /// </summary>
        [JsonPropertyName("age_group")]
        public string? AgeGroup { get; set; }

        /// <summary>
        /// Gender
        /// </summary>
        [JsonPropertyName("gender")]
        public string? Gender { get; set; }

        /// <summary>
        /// Color
        /// </summary>
        [JsonPropertyName("color")]
        public string? Color { get; set; }

        /// <summary>
        /// Size
        /// </summary>
        [JsonPropertyName("size")]
        public string? Size { get; set; }

        /// <summary>
        /// Material
        /// </summary>
        [JsonPropertyName("material")]
        public string? Material { get; set; }

        /// <summary>
        /// Pattern
        /// </summary>
        [JsonPropertyName("pattern")]
        public string? Pattern { get; set; }

        /// <summary>
        /// Product category
        /// </summary>
        [JsonPropertyName("product_category")]
        public string? ProductCategory { get; set; }
    }

    /// <summary>
    /// Product price information
    /// </summary>
    public class ProductPriceInfo
    {
        /// <summary>
        /// Current price
        /// </summary>
        [JsonPropertyName("current_price")]
        public string? CurrentPrice { get; set; }

        /// <summary>
        /// Original price
        /// </summary>
        [JsonPropertyName("original_price")]
        public string? OriginalPrice { get; set; }

        /// <summary>
        /// Sale price
        /// </summary>
        [JsonPropertyName("sale_price")]
        public string? SalePrice { get; set; }

        /// <summary>
        /// Currency
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }

        /// <summary>
        /// Sale price effective date
        /// </summary>
        [JsonPropertyName("sale_price_effective_date")]
        public string? SalePriceEffectiveDate { get; set; }
    }

    /// <summary>
    /// Product landing page information
    /// </summary>
    public class ProductLandingPage
    {
        /// <summary>
        /// Landing page URL
        /// </summary>
        [JsonPropertyName("url")]
        public string? Url { get; set; }

        /// <summary>
        /// Mobile landing page URL
        /// </summary>
        [JsonPropertyName("mobile_url")]
        public string? MobileUrl { get; set; }
    }

    /// <summary>
    /// Product extra information
    /// </summary>
    public class ProductExtraInfo
    {
        /// <summary>
        /// Shipping information
        /// </summary>
        [JsonPropertyName("shipping")]
        public ProductShipping? Shipping { get; set; }

        /// <summary>
        /// Tax information
        /// </summary>
        [JsonPropertyName("tax")]
        public ProductTax? Tax { get; set; }

        /// <summary>
        /// Custom labels
        /// </summary>
        [JsonPropertyName("custom_labels")]
        public Dictionary<string, string>? CustomLabels { get; set; }
    }

    /// <summary>
    /// Product shipping information
    /// </summary>
    public class ProductShipping
    {
        /// <summary>
        /// Shipping cost
        /// </summary>
        [JsonPropertyName("cost")]
        public string? Cost { get; set; }

        /// <summary>
        /// Shipping country
        /// </summary>
        [JsonPropertyName("country")]
        public string? Country { get; set; }

        /// <summary>
        /// Shipping service
        /// </summary>
        [JsonPropertyName("service")]
        public string? Service { get; set; }
    }

    /// <summary>
    /// Product tax information
    /// </summary>
    public class ProductTax
    {
        /// <summary>
        /// Tax rate
        /// </summary>
        [JsonPropertyName("rate")]
        public decimal? Rate { get; set; }

        /// <summary>
        /// Tax country
        /// </summary>
        [JsonPropertyName("country")]
        public string? Country { get; set; }

        /// <summary>
        /// Tax region
        /// </summary>
        [JsonPropertyName("region")]
        public string? Region { get; set; }
    }

    #endregion

    #region Product Set Models

    /// <summary>
    /// Request parameters for getting product sets
    /// </summary>
    public class ProductSetGetRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Product set ID (optional - if not provided, returns all product sets)
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string? ProductSetId { get; set; }

        /// <summary>
        /// Whether to return the product count (default: true)
        /// </summary>
        [JsonPropertyName("return_product_count")]
        public bool? ReturnProductCount { get; set; }

        /// <summary>
        /// Initializes a new instance of the ProductSetGetRequest class
        /// </summary>
        public ProductSetGetRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ProductSetGetRequest class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="productSetId">Product set ID (optional)</param>
        /// <param name="returnProductCount">Whether to return product count</param>
        public ProductSetGetRequest(string bcId, string catalogId, string? productSetId = null, bool? returnProductCount = null)
        {
            BcId = bcId;
            CatalogId = catalogId;
            ProductSetId = productSetId;
            ReturnProductCount = returnProductCount;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductSetGetRequest(BcId={BcId}, CatalogId={CatalogId}, ProductSetId={ProductSetId})";
        }
    }

    /// <summary>
    /// Request parameters for getting products in a product set
    /// </summary>
    public class ProductSetProductGetRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Product set ID
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string ProductSetId { get; set; } = string.Empty;

        /// <summary>
        /// Page number (default: 1)
        /// </summary>
        [JsonPropertyName("page")]
        public int? Page { get; set; }

        /// <summary>
        /// Page size (default: 20)
        /// </summary>
        [JsonPropertyName("page_size")]
        public int? PageSize { get; set; }

        /// <summary>
        /// Initializes a new instance of the ProductSetProductGetRequest class
        /// </summary>
        public ProductSetProductGetRequest()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ProductSetProductGetRequest class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="productSetId">Product set ID</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        public ProductSetProductGetRequest(string bcId, string catalogId, string productSetId, int? page = null, int? pageSize = null)
        {
            BcId = bcId;
            CatalogId = catalogId;
            ProductSetId = productSetId;
            Page = page;
            PageSize = pageSize;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductSetProductGetRequest(BcId={BcId}, CatalogId={CatalogId}, ProductSetId={ProductSetId}, Page={Page}, PageSize={PageSize})";
        }
    }

    /// <summary>
    /// Filter condition for product sets
    /// </summary>
    public class ProductSetCondition
    {
        /// <summary>
        /// Field to filter on
        /// </summary>
        [JsonPropertyName("field")]
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// Filter operation
        /// </summary>
        [JsonPropertyName("operation")]
        public string Operation { get; set; } = string.Empty;

        /// <summary>
        /// Filter value (can be string or array of strings)
        /// </summary>
        [JsonPropertyName("value")]
        public object Value { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the ProductSetCondition class
        /// </summary>
        public ProductSetCondition()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ProductSetCondition class with parameters
        /// </summary>
        /// <param name="field">Field to filter on</param>
        /// <param name="operation">Filter operation</param>
        /// <param name="value">Filter value</param>
        public ProductSetCondition(string field, string operation, object value)
        {
            Field = field;
            Operation = operation;
            Value = value;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductSetCondition(Field={Field}, Operation={Operation}, Value={Value})";
        }
    }

    /// <summary>
    /// Product set filter conditions
    /// </summary>
    public class ProductSetConditions
    {
        /// <summary>
        /// AND conditions
        /// </summary>
        [JsonPropertyName("and")]
        public List<ProductSetCondition>? And { get; set; }

        /// <summary>
        /// OR conditions
        /// </summary>
        [JsonPropertyName("or")]
        public List<ProductSetCondition>? Or { get; set; }

        /// <summary>
        /// Initializes a new instance of the ProductSetConditions class
        /// </summary>
        public ProductSetConditions()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            var andCount = And?.Count ?? 0;
            var orCount = Or?.Count ?? 0;
            return $"ProductSetConditions(AndConditions={andCount}, OrConditions={orCount})";
        }
    }

    /// <summary>
    /// Request body for creating a product set
    /// </summary>
    public class ProductSetCreateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Product set name (max 28 characters)
        /// </summary>
        [JsonPropertyName("product_set_name")]
        public string ProductSetName { get; set; } = string.Empty;

        /// <summary>
        /// Filter conditions
        /// </summary>
        [JsonPropertyName("conditions")]
        public ProductSetConditions Conditions { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the ProductSetCreateBody class
        /// </summary>
        public ProductSetCreateBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ProductSetCreateBody class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="productSetName">Product set name</param>
        /// <param name="conditions">Filter conditions</param>
        public ProductSetCreateBody(string bcId, string catalogId, string productSetName, ProductSetConditions conditions)
        {
            BcId = bcId;
            CatalogId = catalogId;
            ProductSetName = productSetName;
            Conditions = conditions;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductSetCreateBody(BcId={BcId}, CatalogId={CatalogId}, ProductSetName={ProductSetName})";
        }
    }

    /// <summary>
    /// Request body for updating a product set
    /// </summary>
    public class ProductSetUpdateBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Product set ID
        /// </summary>
        [JsonPropertyName("product_set_id")]
        public string ProductSetId { get; set; } = string.Empty;

        /// <summary>
        /// Product set name (max 28 characters) - optional
        /// </summary>
        [JsonPropertyName("product_set_name")]
        public string? ProductSetName { get; set; }

        /// <summary>
        /// Filter conditions - optional
        /// </summary>
        [JsonPropertyName("conditions")]
        public ProductSetConditions? Conditions { get; set; }

        /// <summary>
        /// Initializes a new instance of the ProductSetUpdateBody class
        /// </summary>
        public ProductSetUpdateBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ProductSetUpdateBody class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="productSetId">Product set ID</param>
        /// <param name="productSetName">Product set name (optional)</param>
        /// <param name="conditions">Filter conditions (optional)</param>
        public ProductSetUpdateBody(string bcId, string catalogId, string productSetId, string? productSetName = null, ProductSetConditions? conditions = null)
        {
            BcId = bcId;
            CatalogId = catalogId;
            ProductSetId = productSetId;
            ProductSetName = productSetName;
            Conditions = conditions;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductSetUpdateBody(BcId={BcId}, CatalogId={CatalogId}, ProductSetId={ProductSetId}, ProductSetName={ProductSetName})";
        }
    }

    /// <summary>
    /// Request body for deleting product sets
    /// </summary>
    public class ProductSetDeleteBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Product set IDs to delete (1-10 IDs)
        /// </summary>
        [JsonPropertyName("product_set_ids")]
        public List<string> ProductSetIds { get; set; } = new();

        /// <summary>
        /// Initializes a new instance of the ProductSetDeleteBody class
        /// </summary>
        public ProductSetDeleteBody()
        {
        }

        /// <summary>
        /// Initializes a new instance of the ProductSetDeleteBody class with parameters
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="productSetIds">Product set IDs to delete</param>
        public ProductSetDeleteBody(string bcId, string catalogId, List<string> productSetIds)
        {
            BcId = bcId;
            CatalogId = catalogId;
            ProductSetIds = productSetIds;
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductSetDeleteBody(BcId={BcId}, CatalogId={CatalogId}, ProductSetCount={ProductSetIds.Count})";
        }
    }

    #endregion

    #region Product Log Response Models

    /// <summary>
    /// Response for getting product handling log
    /// </summary>
    public class ProductLogResponse
    {
        /// <summary>
        /// Product handling results
        /// </summary>
        [JsonPropertyName("product_feed_log")]
        public ProductFeedLog? ProductFeedLog { get; set; }

        /// <summary>
        /// Initializes a new instance of the ProductLogResponse class
        /// </summary>
        public ProductLogResponse()
        {
        }

        /// <summary>
        /// Returns a string representation of the object
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ProductLogResponse(Status={ProductFeedLog?.ProcessStatus})";
        }
    }

    /// <summary>
    /// Product feed log information
    /// </summary>
    public class ProductFeedLog
    {
        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string? CatalogId { get; set; }

        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string? FeedId { get; set; }

        /// <summary>
        /// Total number of new items
        /// </summary>
        [JsonPropertyName("add_count")]
        public int AddCount { get; set; }

        /// <summary>
        /// Total number of items modified
        /// </summary>
        [JsonPropertyName("update_count")]
        public int UpdateCount { get; set; }

        /// <summary>
        /// Total number of items deleted
        /// </summary>
        [JsonPropertyName("delete_count")]
        public int DeleteCount { get; set; }

        /// <summary>
        /// Number of errors
        /// </summary>
        [JsonPropertyName("error_count")]
        public int ErrorCount { get; set; }

        /// <summary>
        /// Number of warnings
        /// </summary>
        [JsonPropertyName("warn_count")]
        public int WarnCount { get; set; }

        /// <summary>
        /// Processing status
        /// </summary>
        [JsonPropertyName("process_status")]
        public string? ProcessStatus { get; set; }

        /// <summary>
        /// Update mode
        /// </summary>
        [JsonPropertyName("update_mode")]
        public string? UpdateMode { get; set; }

        /// <summary>
        /// Start time
        /// </summary>
        [JsonPropertyName("start_time")]
        public string? StartTime { get; set; }

        /// <summary>
        /// End time
        /// </summary>
        [JsonPropertyName("end_time")]
        public string? EndTime { get; set; }

        /// <summary>
        /// Detailed processing information
        /// </summary>
        [JsonPropertyName("feed_log_data")]
        public FeedLogData? FeedLogData { get; set; }
    }

    /// <summary>
    /// Detailed processing information
    /// </summary>
    public class FeedLogData
    {
        /// <summary>
        /// File download address with all error/warning messages
        /// </summary>
        [JsonPropertyName("download_path")]
        public Dictionary<string, string>? DownloadPath { get; set; }

        /// <summary>
        /// Error messages
        /// </summary>
        [JsonPropertyName("error_affected_products")]
        public List<AffectedProductInfo>? ErrorAffectedProducts { get; set; }

        /// <summary>
        /// Warning messages
        /// </summary>
        [JsonPropertyName("warn_affected_products")]
        public List<AffectedProductInfo>? WarnAffectedProducts { get; set; }
    }

    /// <summary>
    /// Information about affected products
    /// </summary>
    public class AffectedProductInfo
    {
        /// <summary>
        /// Number of products affected
        /// </summary>
        [JsonPropertyName("affected_product_count")]
        public int AffectedProductCount { get; set; }

        /// <summary>
        /// List of affected items (showing only the first five items)
        /// </summary>
        [JsonPropertyName("affected_product_item_list")]
        public List<AffectedProductItem>? AffectedProductItemList { get; set; }

        /// <summary>
        /// The field with issue
        /// </summary>
        [JsonPropertyName("field")]
        public string? Field { get; set; }

        /// <summary>
        /// Issue details
        /// </summary>
        [JsonPropertyName("issue")]
        public string? Issue { get; set; }

        /// <summary>
        /// Suggestions for solving the issue
        /// </summary>
        [JsonPropertyName("suggestion")]
        public string? Suggestion { get; set; }
    }

    /// <summary>
    /// Individual affected product item
    /// </summary>
    public class AffectedProductItem
    {
        /// <summary>
        /// Index that identifies the position of the product among the products uploaded
        /// </summary>
        [JsonPropertyName("index")]
        public int Index { get; set; }

        /// <summary>
        /// Product title (for E-commerce, entertainment, Auto-Inventory, Auto-Model catalogs, or mini series)
        /// </summary>
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        /// <summary>
        /// SKU ID (for E-commerce catalogs)
        /// </summary>
        [JsonPropertyName("sku_id")]
        public string? SkuId { get; set; }

        /// <summary>
        /// Hotel ID (for hotel catalogs)
        /// </summary>
        [JsonPropertyName("hotel_id")]
        public string? HotelId { get; set; }

        /// <summary>
        /// Hotel or destination name
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        /// <summary>
        /// Flight ID (for flight catalogs)
        /// </summary>
        [JsonPropertyName("flight_id")]
        public string? FlightId { get; set; }

        /// <summary>
        /// Airline company (for flight catalogs)
        /// </summary>
        [JsonPropertyName("airline_company")]
        public string? AirlineCompany { get; set; }

        /// <summary>
        /// Media title ID (for entertainment catalogs)
        /// </summary>
        [JsonPropertyName("media_title_id")]
        public string? MediaTitleId { get; set; }

        /// <summary>
        /// Vehicle ID (for Auto-Inventory or Auto-Model catalogs)
        /// </summary>
        [JsonPropertyName("vehicle_id")]
        public string? VehicleId { get; set; }

        /// <summary>
        /// Vehicle make/brand (for Auto-Inventory or Auto-Model catalogs)
        /// </summary>
        [JsonPropertyName("make")]
        public string? Make { get; set; }

        /// <summary>
        /// Series ID (for mini series catalogs)
        /// </summary>
        [JsonPropertyName("series_id")]
        public string? SeriesId { get; set; }

        /// <summary>
        /// Product link
        /// </summary>
        [JsonPropertyName("product_url")]
        public string? ProductUrl { get; set; }

        /// <summary>
        /// Product description
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Field value
        /// </summary>
        [JsonPropertyName("value")]
        public string? Value { get; set; }
    }

    #endregion
}
