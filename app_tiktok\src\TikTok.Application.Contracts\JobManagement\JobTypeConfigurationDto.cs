using System;
using System.ComponentModel.DataAnnotations;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.JobManagement
{
    /// <summary>
    /// DTO cho cấu hình loại công việc
    /// </summary>
    public class JobTypeConfigurationDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// Loại lệnh công việc
        /// </summary>
        public CommandType CommandType { get; set; }

        /// <summary>
        /// Tên hiển thị của loại công việc
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// Mô tả loại công việc
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Khoảng thời gian giữa các lần đăng ký công việc (tính bằng giây)
        /// </summary>
        public int IntervalSeconds { get; set; }

        /// <summary>
        /// Trạng thái hoạt động (active/inactive)
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Độ ưu tiên của công việc (số càng nhỏ càng ưu tiên cao)
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// Thời gian timeout riêng cho loại công việc này (phút)
        /// </summary>
        public int? TimeoutMinutes { get; set; }

        /// <summary>
        /// Số lần thử lại tối đa cho loại công việc này
        /// </summary>
        [Required]
        [Range(0, 10, ErrorMessage = "MaxRetryCountMustBeBetween0And10")]
        public int MaxRetryCount { get; set; }
    }

    /// <summary>
    /// DTO để tạo mới cấu hình loại công việc
    /// </summary>
    public class CreateJobTypeConfigurationDto
    {
        /// <summary>
        /// Loại lệnh công việc
        /// </summary>
        [Required]
        public CommandType CommandType { get; set; }

        /// <summary>
        /// Tên hiển thị của loại công việc
        /// </summary>
        [Required]
        [StringLength(100)]
        public string DisplayName { get; set; }

        /// <summary>
        /// Mô tả loại công việc
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Khoảng thời gian giữa các lần đăng ký công việc (tính bằng giây)
        /// </summary>
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "IntervalMustBeGreaterThan0")]
        public int IntervalSeconds { get; set; }

        /// <summary>
        /// Trạng thái hoạt động (active/inactive)
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Độ ưu tiên của công việc (số càng nhỏ càng ưu tiên cao)
        /// </summary>
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "PriorityMustBeGreaterThan0")]
        public int Priority { get; set; } = 1;

        /// <summary>
        /// Thời gian timeout riêng cho loại công việc này (phút)
        /// </summary>
        public int? TimeoutMinutes { get; set; }

        /// <summary>
        /// Số lần thử lại tối đa cho loại công việc này
        /// </summary>
        [Required]
        [Range(0, 10, ErrorMessage = "MaxRetryCountMustBeBetween0And10")]
        public int MaxRetryCount { get; set; } = 3;
    }

    /// <summary>
    /// DTO để cập nhật cấu hình loại công việc
    /// </summary>
    public class UpdateJobTypeConfigurationDto
    {
        /// <summary>
        /// Tên hiển thị của loại công việc
        /// </summary>
        [Required]
        [StringLength(100)]
        public string DisplayName { get; set; }

        /// <summary>
        /// Mô tả loại công việc
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Khoảng thời gian giữa các lần đăng ký công việc (tính bằng giây)
        /// </summary>
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "IntervalMustBeGreaterThan0")]
        public int IntervalSeconds { get; set; }

        /// <summary>
        /// Trạng thái hoạt động (active/inactive)
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Độ ưu tiên của công việc (số càng nhỏ càng ưu tiên cao)
        /// </summary>
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "PriorityMustBeGreaterThan0")]
        public int Priority { get; set; }

        /// <summary>
        /// Thời gian timeout riêng cho loại công việc này (phút)
        /// </summary>
        public int? TimeoutMinutes { get; set; }

        /// <summary>
        /// Số lần thử lại tối đa cho loại công việc này
        /// </summary>
        public int MaxRetryCount { get; set; }
    }
}
