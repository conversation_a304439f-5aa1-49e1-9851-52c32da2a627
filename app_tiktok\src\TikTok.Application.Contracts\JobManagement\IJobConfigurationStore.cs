using System;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace TikTok.JobManagement
{
    /// <summary>
    /// Interface cho JobConfigurationStore
    /// </summary>
    public interface IJobConfigurationStore : ITransientDependency
    {
        /// <summary>
        /// Khởi động hệ thống job
        /// </summary>
        Task StartJobSystemAsync();

        /// <summary>
        /// Dừng hệ thống job
        /// </summary>
        Task StopJobSystemAsync();

        /// <summary>
        /// Cấu hình lại hệ thống job dựa trên ID cấu hình
        /// </summary>
        /// <param name="id">ID của cấu hình</param>
        Task ReConfigurationAsync(Guid id);
    }
}