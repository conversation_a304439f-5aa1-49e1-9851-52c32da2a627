/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for uploading catalog videos via file URL
    /// </summary>
    public class CatalogVideoUploadBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// The download URL of the CSV file
        /// </summary>
        [JsonPropertyName("file_url")]
        public string FileUrl { get; set; } = string.Empty;

        /// <summary>
        /// The IDs of the ad accounts that you want to sync the videos to
        /// </summary>
        [JsonPropertyName("advertiser_ids")]
        public List<string>? AdvertiserIds { get; set; }
    }

    /// <summary>
    /// Response for uploading catalog videos
    /// </summary>
    public class CatalogVideoUploadResponse
    {
        /// <summary>
        /// Catalog video handling log ID
        /// </summary>
        [JsonPropertyName("feed_log_id")]
        public string FeedLogId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request parameters for getting catalog video log
    /// </summary>
    public class CatalogVideoLogRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog video handling log ID
        /// </summary>
        public string FeedLogId { get; set; } = string.Empty;

        /// <summary>
        /// Supported languages (default: en)
        /// </summary>
        public string? Language { get; set; }
    }

    /// <summary>
    /// Response for getting catalog video log
    /// </summary>
    public class CatalogVideoLogResponse
    {
        /// <summary>
        /// Video handling results
        /// </summary>
        [JsonPropertyName("video_feed_log")]
        public VideoFeedLog VideoFeedLog { get; set; } = new VideoFeedLog();

        /// <summary>
        /// Detailed processing information
        /// </summary>
        [JsonPropertyName("feed_log_data")]
        public VideoFeedLogData FeedLogData { get; set; } = new VideoFeedLogData();
    }

    /// <summary>
    /// Video handling results
    /// </summary>
    public class VideoFeedLog
    {
        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// Feed ID
        /// </summary>
        [JsonPropertyName("feed_id")]
        public string FeedId { get; set; } = string.Empty;

        /// <summary>
        /// Total number of new videos
        /// </summary>
        [JsonPropertyName("add_count")]
        public int AddCount { get; set; }

        /// <summary>
        /// Total number of videos modified
        /// </summary>
        [JsonPropertyName("update_count")]
        public int UpdateCount { get; set; }

        /// <summary>
        /// Total number of videos deleted
        /// </summary>
        [JsonPropertyName("delete_count")]
        public int DeleteCount { get; set; }

        /// <summary>
        /// Number of errors
        /// </summary>
        [JsonPropertyName("error_count")]
        public int ErrorCount { get; set; }

        /// <summary>
        /// Number of warnings
        /// </summary>
        [JsonPropertyName("warn_count")]
        public int WarnCount { get; set; }

        /// <summary>
        /// Processing status
        /// </summary>
        [JsonPropertyName("process_status")]
        public string ProcessStatus { get; set; } = string.Empty;

        /// <summary>
        /// Start time of the video handling task
        /// </summary>
        [JsonPropertyName("start_time")]
        public string StartTime { get; set; } = string.Empty;

        /// <summary>
        /// End time of the video handling task
        /// </summary>
        [JsonPropertyName("end_time")]
        public string EndTime { get; set; } = string.Empty;
    }

    /// <summary>
    /// Detailed processing information for video feeds
    /// </summary>
    public class VideoFeedLogData
    {
        /// <summary>
        /// File download address with all error and warning messages
        /// </summary>
        [JsonPropertyName("download_path")]
        public Dictionary<string, string> DownloadPath { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// Error message
        /// </summary>
        [JsonPropertyName("error_affected_videos")]
        public List<AffectedVideoError>? ErrorAffectedVideos { get; set; }

        /// <summary>
        /// Warning message
        /// </summary>
        [JsonPropertyName("warn_affected_videos")]
        public List<AffectedVideoWarning>? WarnAffectedVideos { get; set; }
    }

    /// <summary>
    /// Error message for affected videos
    /// </summary>
    public class AffectedVideoError
    {
        /// <summary>
        /// Number of videos affected
        /// </summary>
        [JsonPropertyName("affected_video_count")]
        public int AffectedVideoCount { get; set; }

        /// <summary>
        /// List of affected videos
        /// </summary>
        [JsonPropertyName("affected_video_item_list")]
        public List<AffectedVideoItem> AffectedVideoItemList { get; set; } = new List<AffectedVideoItem>();

        /// <summary>
        /// The field with issue
        /// </summary>
        [JsonPropertyName("field")]
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// Issue details
        /// </summary>
        [JsonPropertyName("issue")]
        public string Issue { get; set; } = string.Empty;

        /// <summary>
        /// Suggestions for solving the issue
        /// </summary>
        [JsonPropertyName("suggestion")]
        public string Suggestion { get; set; } = string.Empty;
    }

    /// <summary>
    /// Warning message for affected videos
    /// </summary>
    public class AffectedVideoWarning
    {
        /// <summary>
        /// Number of videos affected
        /// </summary>
        [JsonPropertyName("affected_video_count")]
        public int AffectedVideoCount { get; set; }

        /// <summary>
        /// List of affected videos
        /// </summary>
        [JsonPropertyName("affected_video_item_list")]
        public List<AffectedVideoItem> AffectedVideoItemList { get; set; } = new List<AffectedVideoItem>();

        /// <summary>
        /// The field with issue
        /// </summary>
        [JsonPropertyName("field")]
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// Issue details
        /// </summary>
        [JsonPropertyName("issue")]
        public string Issue { get; set; } = string.Empty;

        /// <summary>
        /// Suggestions for solving the issue
        /// </summary>
        [JsonPropertyName("suggestion")]
        public string Suggestion { get; set; } = string.Empty;
    }

    /// <summary>
    /// Affected video item details
    /// </summary>
    public class AffectedVideoItem
    {
        /// <summary>
        /// Index that identifies the position of the video
        /// </summary>
        [JsonPropertyName("index")]
        public int Index { get; set; }

        /// <summary>
        /// The name of the video
        /// </summary>
        [JsonPropertyName("video_name")]
        public string VideoName { get; set; } = string.Empty;

        /// <summary>
        /// The URL of the video file
        /// </summary>
        [JsonPropertyName("video_link")]
        public string VideoLink { get; set; } = string.Empty;

        /// <summary>
        /// The SKU ID list of E-commerce catalog products
        /// </summary>
        [JsonPropertyName("sku_id_list")]
        public string SkuIdList { get; set; } = string.Empty;

        /// <summary>
        /// A short description of the video
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// The product category for the video
        /// </summary>
        [JsonPropertyName("category")]
        public string Category { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request parameters for getting catalog videos
    /// </summary>
    public class CatalogVideosGetRequest
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// The ID list of catalog videos that you want to filter
        /// </summary>
        public List<string>? CatalogVideoIds { get; set; }

        /// <summary>
        /// Current page number (default: 1)
        /// </summary>
        public int? Page { get; set; }

        /// <summary>
        /// Page size (default: 10)
        /// </summary>
        public int? PageSize { get; set; }
    }

    /// <summary>
    /// Response for getting catalog videos
    /// </summary>
    public class CatalogVideosGetResponse
    {
        /// <summary>
        /// The list of catalog videos
        /// </summary>
        [JsonPropertyName("videos")]
        public List<CatalogVideo> Videos { get; set; } = new List<CatalogVideo>();

        /// <summary>
        /// Pagination information
        /// </summary>
        [JsonPropertyName("page_info")]
        public PageInfo PageInfo { get; set; } = new PageInfo();
    }

    /// <summary>
    /// Catalog video details
    /// </summary>
    public class CatalogVideo
    {
        /// <summary>
        /// The ID of the catalog video
        /// </summary>
        [JsonPropertyName("catalog_video_id")]
        public string CatalogVideoId { get; set; } = string.Empty;

        /// <summary>
        /// The name of the video
        /// </summary>
        [JsonPropertyName("video_name")]
        public string VideoName { get; set; } = string.Empty;

        /// <summary>
        /// The URL of the video file
        /// </summary>
        [JsonPropertyName("video_link")]
        public string VideoLink { get; set; } = string.Empty;

        /// <summary>
        /// The list of SKU IDs associated with the video
        /// </summary>
        [JsonPropertyName("sku_id_list")]
        public List<string> SkuIdList { get; set; } = new List<string>();

        /// <summary>
        /// The product category for the video
        /// </summary>
        [JsonPropertyName("category")]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// The brand name for the video
        /// </summary>
        [JsonPropertyName("brand")]
        public string Brand { get; set; } = string.Empty;

        /// <summary>
        /// The creator of the video
        /// </summary>
        [JsonPropertyName("creator")]
        public string Creator { get; set; } = string.Empty;

        /// <summary>
        /// The type of the video
        /// </summary>
        [JsonPropertyName("video_type")]
        public string VideoType { get; set; } = string.Empty;

        /// <summary>
        /// A short description of the video
        /// </summary>
        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// URL of the landing page
        /// </summary>
        [JsonPropertyName("landing_page_url")]
        public string LandingPageUrl { get; set; } = string.Empty;

        /// <summary>
        /// Additional information about the video
        /// </summary>
        [JsonPropertyName("custom_label_0")]
        public string CustomLabel0 { get; set; } = string.Empty;

        /// <summary>
        /// Additional information about the video
        /// </summary>
        [JsonPropertyName("custom_label_1")]
        public string CustomLabel1 { get; set; } = string.Empty;

        /// <summary>
        /// Additional information about the video
        /// </summary>
        [JsonPropertyName("custom_label_2")]
        public string CustomLabel2 { get; set; } = string.Empty;

        /// <summary>
        /// Additional information about the video
        /// </summary>
        [JsonPropertyName("custom_label_3")]
        public string CustomLabel3 { get; set; } = string.Empty;

        /// <summary>
        /// Additional information about the video
        /// </summary>
        [JsonPropertyName("custom_label_4")]
        public string CustomLabel4 { get; set; } = string.Empty;

        /// <summary>
        /// The video ID generated after the video extraction is complete
        /// </summary>
        [JsonPropertyName("video_id")]
        public string VideoId { get; set; } = string.Empty;

        /// <summary>
        /// The MD5 hash of the video
        /// </summary>
        [JsonPropertyName("video_signature")]
        public string VideoSignature { get; set; } = string.Empty;

        /// <summary>
        /// Video extraction status
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// The time when the video upload was completed
        /// </summary>
        [JsonPropertyName("create_time")]
        public string CreateTime { get; set; } = string.Empty;

        /// <summary>
        /// The user-controlled activation status of the video
        /// </summary>
        [JsonPropertyName("active_status")]
        public string ActiveStatus { get; set; } = string.Empty;

        /// <summary>
        /// The video preview link
        /// </summary>
        [JsonPropertyName("preview_url")]
        public string PreviewUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request body for deleting catalog videos
    /// </summary>
    public class CatalogVideosDeleteBody
    {
        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public string BcId { get; set; } = string.Empty;

        /// <summary>
        /// Catalog ID
        /// </summary>
        [JsonPropertyName("catalog_id")]
        public string CatalogId { get; set; } = string.Empty;

        /// <summary>
        /// The ID list of catalog videos that you want to delete
        /// </summary>
        [JsonPropertyName("catalog_video_ids")]
        public List<string> CatalogVideoIds { get; set; } = new List<string>();
    }
}
