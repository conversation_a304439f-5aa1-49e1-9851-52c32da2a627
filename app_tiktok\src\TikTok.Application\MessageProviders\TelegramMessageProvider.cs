using System;
using System.Collections.Generic;
using System.Net.Http;  
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TikTok.Application.Contracts.MessageProviders;

namespace TikTok.Application.MessageProviders
{
    public class TelegramMessageProvider : IMessageProvider
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<TelegramMessageProvider> _logger;
        private readonly TelegramOptions _options;

        private const int MAX_MESSAGE_LENGTH = 4090;

        public string Name => "Telegram";
        public bool IsEnabled => _options.Enabled;

        public TelegramMessageProvider(
            ILogger<TelegramMessageProvider> logger,
            IOptions<MessageProviderOptions> options,
            IHttpClientFactory httpClientFactory)
        {
            _httpClient = httpClientFactory.CreateClient("TelegramMessageProvider");
            _logger = logger;
            _options = options.Value.Telegram;
        }

        public async Task SendMessageAsync(string message)
        {
            await SendMessageAsync(message, null);
        }

        public async Task SendMessageAsync(string message, object additionalData)
        {
            if (!_options.Enabled)
            {
                _logger.LogWarning("Telegram provider is disabled");
                return;
            }

            if (string.IsNullOrEmpty(_options.BotToken) || string.IsNullOrEmpty(_options.ChatId))
            {
                _logger.LogError("Telegram bot token or chat ID is not configured");
                return;
            }

            // Split message into chunks if it's too long
            if (message.Length > MAX_MESSAGE_LENGTH)
            {
                var chunks = SplitMessageIntoChunks(message);
                foreach (var chunk in chunks)
                {
                    await SendMessageAsync(chunk, additionalData);
                }
                return;
            }

            try
            {
                var url = $"https://api.telegram.org/bot{_options.BotToken}/sendMessage";

                var requestData = new
                {
                    chat_id = _options.ChatId,
                    text = message,
                };

                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogDebug("Message sent successfully to Telegram");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to send message to Telegram. Status: {Status}, Error: {Error}",
                        response.StatusCode, errorContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message to Telegram");
            }
        }
        public async Task SendMessageByChatIdAsync(string message, string chatId)
        {
            if (!_options.Enabled)
            {
                _logger.LogWarning("Telegram provider is disabled");
                return;
            }

            if (string.IsNullOrEmpty(_options.BotToken))
            {
                _logger.LogError("Telegram bot token or chat ID is not configured");
                return;
            }

            // Split message into chunks if it's too long
            if (message.Length > MAX_MESSAGE_LENGTH)
            {
                var chunks = SplitMessageIntoChunks(message);
                foreach (var chunk in chunks)
                {
                    await SendMessageByChatIdAsync(chunk, chatId);
                }
                return;
            }

            try
            {
                var url = $"https://api.telegram.org/bot{_options.BotToken}/sendMessage";

                var requestData = new
                {
                    chat_id = chatId,
                    text = message,
                };

                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogDebug("Message sent successfully to Telegram");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to send message to Telegram. Status: {Status}, Error: {Error}",
                        response.StatusCode, errorContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message to Telegram");
            }
        }
        private List<string> SplitMessageIntoChunks(string message)
        {
            var chunks = new List<string>();
            for (int i = 0; i < message.Length; i += MAX_MESSAGE_LENGTH)
            {
                chunks.Add(message.Substring(i, Math.Min(MAX_MESSAGE_LENGTH, message.Length - i)));
            }
            return chunks;
        }
    }
}