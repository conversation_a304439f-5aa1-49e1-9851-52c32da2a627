/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Creative Reports operations
    /// </summary>
    public class CreativeReportsApi : ICreativeReportsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<CreativeReportsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the CreativeReportsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public CreativeReportsApi(IApiClient apiClient, ILogger<CreativeReportsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Run a basic report on creative assets
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="materialType">Material type. Enum values: VIDEO, IMAGE, INSTANT_PAGE</param>
        /// <param name="lifetime">Use to specify whether to query all data. If true, you do not need to specify start_date and end_date</param>
        /// <param name="startDate">Start time, closed interval. Format: 2020-01-01 (advertiser time zone). Required when lifetime is false</param>
        /// <param name="endDate">End time, closed interval. Format: 2020-01-01 (advertiser time zone). Required when lifetime is false</param>
        /// <param name="infoFields">Information you want to get about creatives. Default: [material_id, video_id, image_id, page_id]</param>
        /// <param name="metricsFields">The metrics or dimension data that you need. Default: [impressions, spend]</param>
        /// <param name="filtering">Filtering criteria</param>
        /// <param name="sortField">Field to sort by. Support sorting according to the creation time of the material and all the index data</param>
        /// <param name="sortType">Sorting order. Enum values: ASC, DESC. Default: DESC</param>
        /// <param name="page">Current number of pages. Default: 1, range: ≥ 1</param>
        /// <param name="pageSize">Page size. Default: 10, range: 1-1000</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing creative reports data</returns>
        public async Task<Response<CreativeReportsResponse>> GetCreativeReportsAsync(
            string accessToken,
            string advertiserId,
            string materialType,
            bool lifetime = false,
            string? startDate = null,
            string? endDate = null,
            List<string>? infoFields = null,
            List<string>? metricsFields = null,
            CreativeReportsFiltering? filtering = null,
            string? sortField = null,
            string? sortType = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (string.IsNullOrWhiteSpace(materialType))
                throw new ArgumentException("Material type cannot be null or empty", nameof(materialType));

            // Validate material type
            var validMaterialTypes = new[] { "VIDEO", "IMAGE", "INSTANT_PAGE" };
            if (!Array.Exists(validMaterialTypes, mt => mt.Equals(materialType, StringComparison.OrdinalIgnoreCase)))
                throw new ArgumentException($"Material type must be one of: {string.Join(", ", validMaterialTypes)}", nameof(materialType));

            // Validate date requirements
            if (!lifetime)
            {
                if (string.IsNullOrWhiteSpace(startDate))
                    throw new ArgumentException("Start date is required when lifetime is false", nameof(startDate));
                if (string.IsNullOrWhiteSpace(endDate))
                    throw new ArgumentException("End date is required when lifetime is false", nameof(endDate));
            }

            // Validate pagination
            if (page < 1)
                throw new ArgumentException("Page must be greater than 0", nameof(page));
            if (pageSize < 1 || pageSize > 1000)
                throw new ArgumentException("Page size must be between 1 and 1000", nameof(pageSize));

            // Validate sort type
            if (!string.IsNullOrEmpty(sortType))
            {
                var validSortTypes = new[] { "ASC", "DESC" };
                if (!Array.Exists(validSortTypes, st => st.Equals(sortType, StringComparison.OrdinalIgnoreCase)))
                    throw new ArgumentException($"Sort type must be one of: {string.Join(", ", validSortTypes)}", nameof(sortType));
            }

            _logger?.LogDebug("Getting creative reports for advertiser {AdvertiserId}, material type {MaterialType}, page {Page}, size {PageSize}",
                advertiserId, materialType, page, pageSize);

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = advertiserId,
                ["material_type"] = materialType,
                ["lifetime"] = lifetime.ToString().ToLowerInvariant(),
                ["page"] = page.ToString(),
                ["page_size"] = pageSize.ToString()
            };

            if (!lifetime)
            {
                queryParams["start_date"] = startDate!;
                queryParams["end_date"] = endDate!;
            }

            if (infoFields != null && infoFields.Count > 0)
            {
                queryParams["info_fields"] = JsonSerializer.Serialize(infoFields);
            }

            if (metricsFields != null && metricsFields.Count > 0)
            {
                queryParams["metrics_fields"] = JsonSerializer.Serialize(metricsFields);
            }

            if (filtering != null)
            {
                queryParams["filtering"] = JsonSerializer.Serialize(filtering);
            }

            if (!string.IsNullOrEmpty(sortField))
            {
                queryParams["sort_field"] = sortField;
            }

            if (!string.IsNullOrEmpty(sortType))
            {
                queryParams["sort_type"] = sortType;
            }

            var headerParams = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<CreativeReportsResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CreativeReportsEndpoints.GetCreativeReports}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headerParams,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
