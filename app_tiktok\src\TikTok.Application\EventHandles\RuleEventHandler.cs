using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Rules;
using TikTok.Domain.Entities.Rules;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Entities.Events;
using Volo.Abp.EventBus;
using Volo.Abp.EventBus.Distributed;

namespace TikTok.EventHandles
{
    public class RuleEventHandler : ILocalEventHandler<EntityChangedEventData<RuleEntity>>, ITransientDependency
    {
        private readonly IRuleCache _cache;

        public RuleEventHandler(IRuleCache cache)
        {
            _cache = cache;
        }

        public async Task HandleEventAsync(EntityChangedEventData<RuleEntity> eventData)
        {
            if (eventData == null || eventData.Entity == null)
            {
                return;
            }

            await _cache.CleanCache();
        }
    }
}
