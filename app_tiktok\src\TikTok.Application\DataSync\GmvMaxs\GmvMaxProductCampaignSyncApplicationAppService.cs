using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu báo cáo GMV Max Product Campaign
    /// </summary>
    public class GmvMaxProductCampaignSyncApplicationAppService : ApplicationService, IGmvMaxProductCampaignSyncApplicationAppService
    {
        private readonly IGmvMaxProductCampaignSyncService _gmvMaxProductCampaignSyncService;

        public GmvMaxProductCampaignSyncApplicationAppService(IGmvMaxProductCampaignSyncService gmvMaxProductCampaignSyncService)
        {
            _gmvMaxProductCampaignSyncService = gmvMaxProductCampaignSyncService;
        }

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Product Campaign theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate"><PERSON><PERSON><PERSON> bắt đầ<PERSON> (tùy chọn)</param>
        /// <param name="endDate">Ngày kết thúc (tùy chọn)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxProductCampaignSyncResult> SyncGmvMaxProductCampaignAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            return await _gmvMaxProductCampaignSyncService.SyncGmvMaxProductCampaignAsync(bcId, startDate, endDate);
        }

        /// <summary>
        /// Đồng bộ báo cáo GMV Max Product Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxProductCampaignSyncResult> SyncAllGmvMaxProductCampaignForAllBcsAsync()
        {
            return await _gmvMaxProductCampaignSyncService.SyncAllGmvMaxProductCampaignForAllBcsAsync();
        }
    }
}