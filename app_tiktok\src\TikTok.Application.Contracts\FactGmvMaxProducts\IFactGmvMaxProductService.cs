using System;
using System.Threading.Tasks;
using TikTok.FactGmvMaxProducts.Dtos;
using System.Collections.Generic;
using TikTok.Facts.FactGmvMaxProduct;
using TikTok.Facts.FactGmvMaxCampaign;

namespace TikTok.FactGmvMaxProducts
{
    public interface IFactGmvMaxProductService
    {
        Task<GetFactGmvMaxProductDataResponse> GetListAsync(DateTime fromDate, DateTime toDate);
        Task<GetFactGmvMaxProductDataResponse> GetListWithPermissionsAsync(DateTime fromDate, DateTime toDate);
        Task<FactGmvMaxProductSummaryDto> GetSummaryAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<GmvMaxProductTrendDto>> GetTrendsAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<GmvMaxProductTopSellingDto>> GetTopSellingAsync(DateTime fromDate, DateTime toDate, int limit);
        Task<GmvMaxProductDashboardDto> GetDashboardAsync(string? currency = "USD");
        Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD");
        Task<object> GetDetailedAnalysisDataAsync(string? currency = "USD");
        
        // ✅ NEW: Section-specific methods for independent loading
        Task<SummaryCardsDto> GetSummaryCardsAsync(string? currency = "USD");
        Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD");
        Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD");
        Task<DetailedChartsDto> GetDetailedChartsAsync();
        Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD");
    }
}
