using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service interface cho việc đồng bộ dữ liệu giao dịch
    /// </summary>
    public interface ITransactionSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ dữ liệu giao dịch theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<TransactionSyncResult> SyncTransactionsAsync(string bcId);

        /// <summary>
        /// Đồng bộ dữ liệu giao dịch cho tất cả Business Applications
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<TransactionSyncResult> SyncAllTransactionsAsync();
    }
} 