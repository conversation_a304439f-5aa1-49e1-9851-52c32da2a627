using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Repositories;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu báo cáo tích hợp Business Center
    /// </summary>
    public class ReportIntegratedBcSyncService : BaseSyncService, IReportIntegratedBcSyncService
    {
        private readonly IRawReportIntegratedBcRepository _reportIntegratedBcRepository;
        private readonly IRepository<RawReportIntegratedBcEntity, Guid> _reportIntegratedBcEntityRepository;

        public ReportIntegratedBcSyncService(
            IServiceProvider serviceProvider,
            IRawReportIntegratedBcRepository reportIntegratedBcRepository,
            IRepository<RawReportIntegratedBcEntity, Guid> reportIntegratedBcEntityRepository,
            ILogger<ReportIntegratedBcSyncService> logger) : base(serviceProvider, logger)
        {
            _reportIntegratedBcRepository = reportIntegratedBcRepository;
            _reportIntegratedBcEntityRepository = reportIntegratedBcEntityRepository;
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp BC cho tất cả Business Centers 
        /// </summary>
        /// <param name="startDate">Ngày bắt đầu (yyyy-MM-dd)</param>
        /// <param name="endDate">Ngày kết thúc (yyyy-MM-dd)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedBcSyncResult> SyncAllReportIntegratedBcForAllBcsAsync()
        {
            var result = new ReportIntegratedBcSyncResult
            {
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp BC cho tất cả BC ");

                var allBcs = await _businessApplicationCache.GetAllActiveAsync();
                var bcIds = allBcs.Select(x => x.BcId).Distinct().ToList();

                var totalResult = new ReportIntegratedBcSyncResult();

                foreach (var bcId in bcIds)
                {
                    var bcResult = await SyncReportIntegratedBcAsync(bcId);
                    totalResult.TotalSynced += bcResult.TotalSynced;
                    totalResult.NewRecords += bcResult.NewRecords;
                    totalResult.UpdatedRecords += bcResult.UpdatedRecords;
                    totalResult.DayCount += bcResult.DayCount;
                    totalResult.BcCount++;
                }

                result = totalResult;

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo tích hợp BC cho tất cả BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}, Ngày: {DayCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp BC cho tất cả BC");
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo tích hợp BC: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp BC cho tất cả BC");
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ báo cáo tích hợp BC theo BC ID 
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate">Ngày bắt đầu (yyyy-MM-dd)</param>
        /// <param name="endDate">Ngày kết thúc (yyyy-MM-dd)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<ReportIntegratedBcSyncResult> SyncReportIntegratedBcAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var result = new ReportIntegratedBcSyncResult
            {
                BcCount = 1
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo tích hợp BC cho BC: {BcId}", bcId);

                // Tạo TikTok client từ Base
                using var tikTokClient = await CreateTikTokBusinessApiClient(bcId);

                (DateTime startDate, DateTime endDate) rangeDateFiltering;
                if (startDate.HasValue && endDate.HasValue)
                {
                    if(startDate.Value > endDate.Value)
                    {
                        throw new UserFriendlyException("Ngày bắt đầu phải nhỏ hơn ngày kết thúc");
                    }
                    rangeDateFiltering = (startDate.Value, endDate.Value);
                }
                else
                {
                    rangeDateFiltering = await GetRangeDateFiltering(bcId);
                }

                // Gọi API để lấy dữ liệu báo cáo
                var apiResponse = await GetSyncReportIntegratedBcFromApiAsync(tikTokClient, bcId, rangeDateFiltering.startDate, rangeDateFiltering.endDate);

                if (apiResponse != null && apiResponse.Any())
                {
                    await ProcessReportDataAsync(bcId, apiResponse, result);
                }
                else
                {
                    _logger.LogDebug("Không có dữ liệu báo cáo cho BC: {BcId}", bcId);
                }

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo tích hợp BC cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Ngày: {DayCount}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp BC cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo tích hợp BC: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo tích hợp BC cho BC: {BcId}", bcId);
            }

            return result;
        }

        private async Task<(DateTime startDate, DateTime endDate)> GetRangeDateFiltering(string bcId)
        {
            string timezone = DateTimeService.UTC_TIMEZONE;
            var bc = await _businessCenterCache.GetByBcIdAsync(bcId);
            if (bc != null && !string.IsNullOrEmpty(timezone))
            {
                timezone = bc.Timezone;
            }

            var currentDate = _dateTimeService.GetDateNow(timezone);
            DateTime startDate = currentDate.AddDays(-LAST_SYNC_DAYS);
            DateTime endDate = currentDate;

            // Lấy dữ liệu báo cáo tích hợp bc mới nhất
            var latestReport = await _reportIntegratedBcRepository.GetLatestByBcIdAsync(bcId);
            if (latestReport != null)
            {
                startDate = latestReport.Date;
            }

            // Nếu khoảng thời gian lấy dữ liệu lớn hơn 30 ngày thì giới hạn lại startDate
            if ((endDate - startDate).TotalDays > 30)
            {
                startDate = endDate.AddDays(-30);
            }

            return (startDate, endDate);
        }

        /// <summary>
        /// Lấy dữ liệu bản ghi giao dịch BC từ TikTok API
        /// </summary>
        private async Task<List<ReportDataItem>> GetSyncReportIntegratedBcFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, DateTime startDate, DateTime endDate)
        {
            var records = new List<ReportDataItem>();
            var page = 1;
            const int pageSize = 500;
            var filtering = new TransactionFiltering()
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            };

            while (true)
            {
                var request = new SynchronousReportRequest
                {
                    BcId = bcId,
                    ReportType = "BC",
                    Dimensions = new List<string> { "stat_time_day" },
                    Metrics = new List<string>
                    {
                        "spend", "billed_cost", "cash_spend", "voucher_spend", "cashback_coupon_spend", "tax_spend",
                        "cpc", "cpm", "impressions", "clicks", "ctr", "conversion", "cost_per_conversion", "conversion_rate",
                        "reach", "real_time_conversion", "real_time_cost_per_conversion", "real_time_conversion_rate",
                        "skan_conversion", "skan_cost_per_conversion", "skan_conversion_rate",
                        "video_watched_2s", "video_watched_6s", "video_views_p100", "video_views_p75", "video_views_p50", "video_views_p25"
                    },
                    StartDate = startDate.ToString("yyyy-MM-dd"),
                    EndDate = endDate.ToString("yyyy-MM-dd"),
                    Page = page,
                    PageSize = pageSize
                };

                var response = await tikTokClient.Reporting.GetSynchronousReportAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu báo cáo tích hợp BC: {response.Message}");
                }

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break;
                }

                records.AddRange(response.Data.List);

                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            return records;
        }

        /// <summary>
        /// Xử lý dữ liệu báo cáo từ API
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="reportDataList">Danh sách dữ liệu báo cáo</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task ProcessReportDataAsync(string bcId, List<ReportDataItem> reportDataList, ReportIntegratedBcSyncResult result)
        {
            // sử dụng UnitOfWork để đảm bảo tính toàn vẹn dữ liệu cho mỗi 200 bản ghi
            if (reportDataList == null || !reportDataList.Any())
            {
                _logger.LogDebug("Không có dữ liệu báo cáo để xử lý cho BC: {BcId}", bcId);
                return;
            }

            var pageSize = PAGE_SIZE_HANDLE_SAVE_TO_DATABASE;
            var totalPages = (int)Math.Ceiling((double)reportDataList.Count / pageSize);
            _logger.LogDebug("Bắt đầu xử lý {TotalRecords} bản ghi báo cáo cho BC: {BcId}", reportDataList.Count, bcId);
            for (int page = 0; page < totalPages; page++)
            {
                var pageData = reportDataList.Skip(page * pageSize).Take(pageSize).ToList();
                if (pageData.Any())
                {
                    try
                    {
                        await ProcessPageDataAsync(bcId, pageData, result);
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý dữ liệu báo cáo cho BC: {BcId}, Trang: {Page}", bcId, page + 1);
                        result.ErrorMessage += ex.Message + Environment.NewLine;
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        continue; // Bỏ qua lỗi và tiếp tục với trang tiếp theo
                    }
                }
            }
        }

        private async Task ProcessPageDataAsync(string bcId, List<ReportDataItem> pageData, ReportIntegratedBcSyncResult result)
        {
            // use unit of work
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var mappedEntities = await MapListReportDataToEntitiesAsync(bcId, pageData);

                var existingEntities = await _reportIntegratedBcRepository.GetByBcIdAndDateRangeAsync(
                    bcId: bcId,
                    startDate: mappedEntities.Min(x => x.Date),
                    endDate: mappedEntities.Max(x => x.Date));

                var insertedEntities = new List<RawReportIntegratedBcEntity>();
                var updatedEntities = new List<RawReportIntegratedBcEntity>();

                foreach (var mappedEntity in mappedEntities)
                {
                    var currentEntity = existingEntities.FirstOrDefault(x => x.Date == mappedEntity.Date);
                    if (currentEntity == null)
                    {
                        insertedEntities.Add(mappedEntity);
                        result.NewRecords++;
                        result.TotalSynced++;
                    }
                    else
                    {
                        // Cập nhật nếu có thay đổi
                        if (currentEntity.HasChanged(mappedEntity))
                        {
                            UpdateEntityFromNewData(currentEntity, mappedEntity);
                            updatedEntities.Add(currentEntity);
                            result.UpdatedRecords++;
                            result.TotalSynced++;
                        }
                    }
                }

                // Thêm các bản ghi mới vào kho dữ liệu
                if (insertedEntities.Any())
                {
                    await _reportIntegratedBcEntityRepository.InsertManyAsync(insertedEntities);
                }
                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _reportIntegratedBcEntityRepository.UpdateManyAsync(updatedEntities);
                }

                result.DayCount += pageData.Count;

                await uow.CompleteAsync();
            }
        }

        private async Task<List<RawReportIntegratedBcEntity>> MapListReportDataToEntitiesAsync(string bcId, List<ReportDataItem> reportDataList)
        {
            var entities = new List<RawReportIntegratedBcEntity>();
            foreach (var reportData in reportDataList)
            {
                var entity = await MapReportDataToEntityAsync(bcId, reportData);
                if (entity != null)
                {
                    entities.Add(entity);
                }
            }
            return entities;
        }

        /// <summary>
        /// Map dữ liệu báo cáo từ API sang entity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="reportDate">Ngày báo cáo</param>
        /// <param name="reportData">Dữ liệu báo cáo</param>
        /// <returns>RawReportIntegratedBcEntity</returns>
        private async Task<RawReportIntegratedBcEntity?> MapReportDataToEntityAsync(string bcId, ReportDataItem reportData)
        {
            var date = reportData.Dimensions?.GetValueOrDefault("stat_time_day")?.ToString();
            if (!DateTime.TryParse(date, out var reportDate))
            {
                _logger.LogWarning("Ngày báo cáo không hợp lệ: {Date} cho BC: {BcId}", date, bcId);
                return null;
            }

            var entity = new RawReportIntegratedBcEntity(Guid.NewGuid())
            {
                BcId = bcId,
                Date = reportDate.Date
            };

            if (reportData.Metrics != null)
            {
                // Map các metrics từ API response
                entity.Spend = GetDecimalValue(reportData.Metrics, "spend");
                entity.BilledCost = GetDecimalValue(reportData.Metrics, "billed_cost");
                entity.CashSpend = GetDecimalValue(reportData.Metrics, "cash_spend");
                entity.VoucherSpend = GetDecimalValue(reportData.Metrics, "voucher_spend");
                entity.CashbackCouponSpend = GetDecimalValue(reportData.Metrics, "cashback_coupon_spend");
                entity.TaxSpend = GetDecimalValue(reportData.Metrics, "tax_spend");
                entity.Cpc = GetDecimalValue(reportData.Metrics, "cpc");
                entity.Cpm = GetDecimalValue(reportData.Metrics, "cpm");
                entity.Impressions = GetLongValue(reportData.Metrics, "impressions");
                entity.Clicks = GetLongValue(reportData.Metrics, "clicks");
                entity.Ctr = GetDecimalValue(reportData.Metrics, "ctr");
                entity.Conversion = GetLongValue(reportData.Metrics, "conversion");
                entity.CostPerConversion = GetDecimalValue(reportData.Metrics, "cost_per_conversion");
                entity.ConversionRate = GetDecimalValue(reportData.Metrics, "conversion_rate");
                entity.Reach = GetLongValue(reportData.Metrics, "reach");
                entity.RealTimeConversion = GetLongValue(reportData.Metrics, "real_time_conversion");
                entity.RealTimeCostPerConversion = GetDecimalValue(reportData.Metrics, "real_time_cost_per_conversion");
                entity.RealTimeConversionRate = GetDecimalValue(reportData.Metrics, "real_time_conversion_rate");
                entity.SkanConversion = GetLongValue(reportData.Metrics, "skan_conversion");
                entity.SkanCostPerConversion = GetDecimalValue(reportData.Metrics, "skan_cost_per_conversion");
                entity.SkanConversionRate = GetDecimalValue(reportData.Metrics, "skan_conversion_rate");
                entity.VideoWatched2s = GetLongValue(reportData.Metrics, "video_watched_2s");
                entity.VideoWatched6s = GetLongValue(reportData.Metrics, "video_watched_6s");
                entity.VideoViewsP100 = GetLongValue(reportData.Metrics, "video_views_p100");
                entity.VideoViewsP75 = GetLongValue(reportData.Metrics, "video_views_p75");
                entity.VideoViewsP50 = GetLongValue(reportData.Metrics, "video_views_p50");
                entity.VideoViewsP25 = GetLongValue(reportData.Metrics, "video_views_p25");
            }

            return entity;
        }

        /// <summary>
        /// Cập nhật entity từ dữ liệu mới
        /// </summary>
        /// <param name="existingEntity">Entity hiện tại</param>
        /// <param name="newEntity">Entity mới</param>
        private void UpdateEntityFromNewData(RawReportIntegratedBcEntity existingEntity, RawReportIntegratedBcEntity newEntity)
        {
            existingEntity.Spend = newEntity.Spend;
            existingEntity.BilledCost = newEntity.BilledCost;
            existingEntity.CashSpend = newEntity.CashSpend;
            existingEntity.VoucherSpend = newEntity.VoucherSpend;
            existingEntity.CashbackCouponSpend = newEntity.CashbackCouponSpend;
            existingEntity.TaxSpend = newEntity.TaxSpend;
            existingEntity.Cpc = newEntity.Cpc;
            existingEntity.Cpm = newEntity.Cpm;
            existingEntity.Impressions = newEntity.Impressions;
            existingEntity.Clicks = newEntity.Clicks;
            existingEntity.Ctr = newEntity.Ctr;
            existingEntity.Conversion = newEntity.Conversion;
            existingEntity.CostPerConversion = newEntity.CostPerConversion;
            existingEntity.ConversionRate = newEntity.ConversionRate;
            existingEntity.Reach = newEntity.Reach;
            existingEntity.RealTimeConversion = newEntity.RealTimeConversion;
            existingEntity.RealTimeCostPerConversion = newEntity.RealTimeCostPerConversion;
            existingEntity.RealTimeConversionRate = newEntity.RealTimeConversionRate;
            existingEntity.SkanConversion = newEntity.SkanConversion;
            existingEntity.SkanCostPerConversion = newEntity.SkanCostPerConversion;
            existingEntity.SkanConversionRate = newEntity.SkanConversionRate;
            existingEntity.VideoWatched2s = newEntity.VideoWatched2s;
            existingEntity.VideoWatched6s = newEntity.VideoWatched6s;
            existingEntity.VideoViewsP100 = newEntity.VideoViewsP100;
            existingEntity.VideoViewsP75 = newEntity.VideoViewsP75;
            existingEntity.VideoViewsP50 = newEntity.VideoViewsP50;
            existingEntity.VideoViewsP25 = newEntity.VideoViewsP25;
        }
    }
}