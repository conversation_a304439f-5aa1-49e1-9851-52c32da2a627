using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTok.Enums;
using Volo.Abp.Application.Services;

namespace TikTok.JobManagement
{
    /// <summary>
    /// Interface cho AppService quản lý cấu hình loại công việc
    /// </summary>
    public interface IJobTypeConfigurationAppService : IApplicationService
    {
        /// <summary>
        /// Lấy danh sách tất cả cấu hình loại công việc
        /// </summary>
        /// <returns>Danh sách cấu hình</returns>
        Task<List<JobTypeConfigurationDto>> GetListAsync();

        /// <summary>
        /// Lấy cấu hình theo ID
        /// </summary>
        /// <param name="id">ID cấu hình</param>
        /// <returns>Cấu hình</returns>
        Task<JobTypeConfigurationDto> GetAsync(Guid id);

        /// <summary>
        /// L<PERSON>y cấu hình theo CommandType
        /// </summary>
        /// <param name="commandType"><PERSON><PERSON><PERSON> l<PERSON>nh</param>
        /// <returns>C<PERSON>u hình</returns>
        Task<JobTypeConfigurationDto> GetByCommandTypeAsync(CommandType commandType);

        /// <summary>
        /// Tạo mới cấu hình
        /// </summary>
        /// <param name="input">Dữ liệu tạo mới</param>
        /// <returns>Cấu hình đã tạo</returns>
        Task<JobTypeConfigurationDto> CreateAsync(CreateJobTypeConfigurationDto input);

        /// <summary>
        /// Cập nhật cấu hình
        /// </summary>
        /// <param name="id">ID cấu hình</param>
        /// <param name="input">Dữ liệu cập nhật</param>
        /// <returns>Cấu hình đã cập nhật</returns>
        Task<JobTypeConfigurationDto> UpdateAsync(Guid id, UpdateJobTypeConfigurationDto input);

        /// <summary>
        /// Xóa cấu hình
        /// </summary>
        /// <param name="id">ID cấu hình</param>
        Task DeleteAsync(Guid id);

        /// <summary>
        /// Lấy danh sách cấu hình đang hoạt động
        /// </summary>
        /// <returns>Danh sách cấu hình</returns>
        Task<List<JobTypeConfigurationDto>> GetActiveConfigurationsAsync();

        /// <summary>
        /// Khởi tạo cấu hình mặc định cho tất cả CommandType
        /// </summary>
        Task InitializeDefaultConfigurationsAsync();
    }
}
