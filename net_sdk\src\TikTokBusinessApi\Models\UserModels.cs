/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Response model for user information
    /// </summary>
    public class UserInfoResponse
    {
        /// <summary>
        /// User's username
        /// </summary>
        [JsonPropertyName("display_name")]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// User's E-mail (data-masked)
        /// </summary>
        [JsonPropertyName("email")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// The TikTok for Business user ID for the App developer
        /// </summary>
        [JsonPropertyName("core_user_id")]
        public string CoreUserId { get; set; } = string.Empty;

        /// <summary>
        /// User's creation time (timestamp)
        /// </summary>
        [JsonPropertyName("create_time")]
        public long CreateTime { get; set; }

        /// <summary>
        /// The URL of the user's avatar
        /// </summary>
        [JsonPropertyName("avatar_url")]
        public string? AvatarUrl { get; set; }
    }
}
