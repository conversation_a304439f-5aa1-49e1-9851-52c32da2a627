/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Ad Diagnosis operations
    /// </summary>
    public class AdDiagnosisApi : IAdDiagnosisApi
    {
        private readonly IApiClient _apiClient;

        /// <summary>
        /// Initializes a new instance of the AdDiagnosisApi class
        /// </summary>
        /// <param name="apiClient">API client for making requests</param>
        public AdDiagnosisApi(IApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        }

        /// <inheritdoc />
        public async Task<AdDiagnosisResponse> GetDiagnosisAsync(
            string advertiserId,
            AdDiagnosisFiltering? filtering = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            // Validate filtering if provided
            if (filtering?.AdgroupIds != null && filtering.AdgroupIds.Count > 20)
                throw new ArgumentException("Maximum 20 ad group IDs are allowed", nameof(filtering));

            // Validate issue categories if provided
            if (filtering?.IssueCategory != null)
            {
                var validCategories = new HashSet<string> { "CREATIVE", "BID_AND_BUDGET", "EVENT_TRACK" };
                foreach (var category in filtering.IssueCategory)
                {
                    if (!validCategories.Contains(category))
                        throw new ArgumentException($"Invalid issue category: {category}. Valid values are: CREATIVE, BID_AND_BUDGET, EVENT_TRACK", nameof(filtering));
                }
            }

            var request = new AdDiagnosisRequest
            {
                AdvertiserId = advertiserId,
                Filtering = filtering
            };

            return await _apiClient.CallApiAsync<AdDiagnosisResponse>(
                $"{_apiClient.ApiPrefix}{ApiEndpoints.AdDiagnosis.Get}",
                System.Net.Http.HttpMethod.Get,
                queryParams: null,
                body: request,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
