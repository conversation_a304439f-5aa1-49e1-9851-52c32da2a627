using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.BalanceBusinessCenters
{
    /// <summary>
    /// Interface service cho ngân sách Business Center
    /// </summary>
    public interface IBalanceBusinessCenterAppService :
        ICrudAppService<
            BalanceBusinessCenterDto,
            Guid,
            GetBalanceBusinessCenterListDto,
            CreateBalanceBusinessCenterDto,
            UpdateBalanceBusinessCenterDto>
    {
        /// <summary>
        /// Lấy ngân sách Business Center theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Ngân sách Business Center</returns>
        Task<BalanceBusinessCenterDto> GetByBcIdAsync(string bcId);

        /// <summary>
        /// Lấy ngân sách Business Center theo BC ID và ngày
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="date"><PERSON><PERSON><PERSON> c<PERSON>n l<PERSON>y dữ liệ<PERSON></param>
        /// <returns>Ngân sách Business Center</returns>
        Task<BalanceBusinessCenterDto> GetByBcIdAndDateAsync(string bcId, DateTime date);

        /// <summary>
        /// Lấy bản ghi mới nhất theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Bản ghi mới nhất</returns>
        Task<BalanceBusinessCenterDto> GetLatestByBcIdAsync(string bcId);

        /// <summary>
        /// Lấy danh sách ngân sách Business Center theo tiền tệ
        /// </summary>
        /// <param name="currency">Tiền tệ</param>
        /// <returns>Danh sách ngân sách Business Center</returns>
        Task<PagedResultDto<BalanceBusinessCenterDto>> GetByCurrencyAsync(string currency);
    }
}