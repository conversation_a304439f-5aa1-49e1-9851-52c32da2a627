/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.IO;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Files operations
    /// </summary>
    public interface IFilesApi
    {
        /// <summary>
        /// Upload a file to TikTok's file repository
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">File upload request parameters</param>
        /// <param name="fileStream">File stream (required when upload_type is FILE)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing file upload information</returns>
        //Task<Response<FileUploadResponse>> UploadFileAsync(
        //    string accessToken,
        //    FileUploadRequest request,
        //    Stream? fileStream = null,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Start a file chunk upload task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Chunk upload start request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing upload task information</returns>
        //Task<Response<ChunkUploadStartResponse>> StartChunkUploadAsync(
        //    string accessToken,
        //    ChunkUploadStartRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Transfer a file chunk
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Chunk transfer request parameters</param>
        /// <param name="chunkStream">File chunk stream</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing chunk transfer information</returns>
        //Task<Response<ChunkTransferResponse>> TransferChunkAsync(
        //    string accessToken,
        //    ChunkTransferRequest request,
        //    Stream chunkStream,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Finish a chunk upload task
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">Chunk upload finish request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing finished upload information</returns>
        //Task<Response<ChunkUploadFinishResponse>> FinishChunkUploadAsync(
        //    string accessToken,
        //    ChunkUploadFinishRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Check whether one or more file names have been used for images or videos
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="request">File name check request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing file name check results</returns>
        //Task<Response<FileNameCheckResponse>> CheckFileNamesAsync(
        //    string accessToken,
        //    FileNameCheckRequest request,
        //    CancellationToken cancellationToken = default);
    }
}
