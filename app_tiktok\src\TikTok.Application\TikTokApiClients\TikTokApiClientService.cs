using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using TikTok.BusinessApplications;
using TikTok.Consts;
using TikTokBusinessApi;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Core.Resilience;
using Volo.Abp;

namespace TikTok.TikTokApiClients
{
    /// <summary>
    /// Singleton service để quản lý instance duy nhất của TikTokApiClient
    /// </summary>
    public class TikTokApiClientService : ITikTokApiClientService
    {
        private readonly ILogger<TikTokApiClientService> _logger;
        private readonly IBusinessApplicationCache _businessApplicationCache;
        private readonly IConfiguration _configuration;
        
        // Thread-safe singleton client
        private TikTokBusinessApiClient _client;
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        public TikTokApiClientService(
            ILogger<TikTokApiClientService> logger,
            IBusinessApplicationCache businessApplicationCache,
            IConfiguration configuration)
        {
            _logger = logger;
            _businessApplicationCache = businessApplicationCache;
            _configuration = configuration;
        }

        /// <summary>
        /// Lấy hoặc tạo TikTokBusinessApiClient singleton
        /// </summary>
        public async Task<TikTokBusinessApiClient> GetOrCreateClientAsync(string bcId, RateLimiterOptions? rateLimitOption = null, RetryPolicyOptions? retryPolicyOption = null)
        {
            if (string.IsNullOrEmpty(bcId))
            {
                throw new ArgumentException("Business Center ID không được null hoặc empty", nameof(bcId));
            }

            // Kiểm tra client đã tồn tại chưa
            if (_client != null)
            {
                _logger.LogDebug("Sử dụng TikTokApiClient singleton hiện có");
                return _client;
            }

            // Sử dụng SemaphoreSlim để đảm bảo thread-safe với async/await
            await _semaphore.WaitAsync();
            
            try
            {
                // Kiểm tra lại sau khi đã acquire lock
                if (_client != null)
                {
                    _logger.LogDebug("Sử dụng TikTokApiClient singleton hiện có (trong lock)");
                    return _client;
                }

                // Tạo client mới
                _client = await CreateNewClientAsync(bcId, rateLimitOption, retryPolicyOption);
                _logger.LogDebug("Tạo mới TikTokApiClient singleton cho BC: {BcId}", bcId);
            }
            finally
            {
                _semaphore.Release();
            }

            return _client;
        }

        /// <summary>
        /// Lấy hoặc tạo TikTokBusinessApiClient với cấu hình mặc định
        /// </summary>
        public async Task<TikTokBusinessApiClient> GetOrCreateClientAsync(string bcId)
        {
            var rateLimitOption = GetDefaultRateLimitOptions();
            var retryPolicyOption = GetDefaultRetryPolicyOptions();
            
            return await GetOrCreateClientAsync(bcId, rateLimitOption, retryPolicyOption);
        }

        /// <summary>
        /// Xóa client singleton (nếu cần refresh)
        /// </summary>
        public void RemoveClient()
        {
            _semaphore.Wait();
            try
            {
                if (_client != null)
                {
                    _logger.LogDebug("Xóa TikTokApiClient singleton");
                    _client.Dispose();
                    _client = null;
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// Xóa client singleton
        /// </summary>
        public void ClearClient()
        {
            RemoveClient();
        }

        /// <summary>
        /// Tạo TikTokBusinessApiClient mới
        /// </summary>
        private async Task<TikTokBusinessApiClient> CreateNewClientAsync(string bcId, RateLimiterOptions? rateLimitOption = null, RetryPolicyOptions? retryPolicyOption = null)
        {
            // Lấy cấu hình ứng dụng từ BC ID
            var activeApplication = await _businessApplicationCache.GetByBcIdAsync(bcId);
            if (activeApplication == null)
            {
                throw new BusinessException(TikTokApiCodes.ObjectNotExists.ToString(), "Business Application Not Found");
            }

            if (string.IsNullOrEmpty(activeApplication.AccessToken))
            {
                throw new BusinessException(TikTokApiCodes.AccessTokenExpired.ToString(), $"Access token không hợp lệ cho ứng dụng: {activeApplication.ApplicationId}");
            }

            // Tạo TikTok client
            var client = new TikTokBusinessApiClient(
                Configuration.CreateForProduction(activeApplication.ApplicationId, activeApplication.AccessToken));

            // Cấu hình rate limiter
            if (rateLimitOption != null)
            {
                client.ApiClient.RateLimiter = new DefaultRateLimiter(rateLimitOption);
            }

            // Cấu hình retry policy
            if (retryPolicyOption != null)
            {
                client.ApiClient.RetryPolicy = new DefaultRetryPolicy(retryPolicyOption);
            }

            return client;
        }

        /// <summary>
        /// Lấy cấu hình RateLimit mặc định từ appsettings
        /// </summary>
        private RateLimiterOptions GetDefaultRateLimitOptions()
        {
            var maxWaitTime = _configuration.GetValue<int>("APIClient:RateLimit:MaxWaitTime", 30);
            var requestsPerWindow = _configuration.GetValue<int>("APIClient:RateLimit:RequestsPerWindow", 8);
            var windowDuration = _configuration.GetValue<int>("APIClient:RateLimit:WindowDuration", 1);
            var useSlidingWindow = _configuration.GetValue<bool>("APIClient:RateLimit:UseSlidingWindow", true);

            return new RateLimiterOptions
            {
                MaxWaitTime = TimeSpan.FromSeconds(maxWaitTime),
                RequestsPerWindow = requestsPerWindow,
                WindowDuration = TimeSpan.FromSeconds(windowDuration),
                UseSlidingWindow = useSlidingWindow
            };
        }

        /// <summary>
        /// Lấy cấu hình RetryPolicy mặc định từ appsettings
        /// </summary>
        private RetryPolicyOptions GetDefaultRetryPolicyOptions()
        {
            var maxRetryAttempts = _configuration.GetValue<int>("APIClient:RetryPolicy:MaxRetryAttempts", 3);
            var baseDelay = _configuration.GetValue<int>("APIClient:RetryPolicy:BaseDelay", 1000);
            var maxDelay = _configuration.GetValue<int>("APIClient:RetryPolicy:MaxDelay", 10000);

            return new RetryPolicyOptions
            {
                MaxRetryAttempts = maxRetryAttempts,
                BaseDelay = TimeSpan.FromMilliseconds(baseDelay),
                MaxDelay = TimeSpan.FromMilliseconds(maxDelay)
            };
        }
    }
}
