using System;
using System.Threading.Tasks;
using TikTok.FactGmvMaxCampaigns.Dtos;
using TikTok.Facts.FactGmvMaxCampaign;

namespace TikTok.FactGmvMaxCampaigns
{
    public interface IFactGmvMaxCampaignService
    {
        // Existing methods
        Task<GetFactGmvMaxCampaignDataResponse> GetListAsync(DateTime fromDate, DateTime toDate, string? currency = "USD");
        Task<GetFactGmvMaxCampaignDataResponse> GetListWithPermissionsAsync(DateTime fromDate, DateTime toDate, string? currency = "USD");
        Task<GmvMaxCampaignDashboardDto> GetDashboardAsync(string? currency = "USD");
        Task<DashboardSummaryDto> GetDashboardSummaryAsync(string? currency = "USD");
        Task<object> GetDetailedAnalysisDataAsync();

        // ✅ NEW: Section-specific methods for independent loading
        /// <summary>
        /// Lấy dữ liệu cho Summary Cards section - Critical priority
        /// </summary>
        /// <param name="currency">Ti<PERSON><PERSON> tệ (USD/VND)</param>
        /// <returns>Summary cards data</returns>
        Task<SummaryCardsDto> GetSummaryCardsAsync(string? currency = "USD");

        /// <summary>
        /// Lấy dữ liệu cho Overview Section - High priority
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Overview section data</returns>
        Task<OverviewSectionDto> GetOverviewSectionAsync(string? currency = "USD");

        /// <summary>
        /// Lấy dữ liệu cho Charts Section - Medium priority (lazy load)
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Charts data</returns>
        Task<ChartsDataDto> GetChartsDataAsync(string? currency = "USD");

        /// <summary>
        /// Lấy dữ liệu cho Detailed Charts Section - Medium priority (lazy load)
        /// </summary>
        /// <returns>Detailed charts data</returns>
        Task<DetailedChartsDto> GetDetailedChartsAsync();

        /// <summary>
        /// Lấy dữ liệu cho Rankings Section - Low priority (background load)
        /// </summary>
        /// <param name="currency">Tiền tệ (USD/VND)</param>
        /// <returns>Rankings data</returns>
        Task<RankingsDataDto> GetRankingsDataAsync(string? currency = "USD");

    }
}
