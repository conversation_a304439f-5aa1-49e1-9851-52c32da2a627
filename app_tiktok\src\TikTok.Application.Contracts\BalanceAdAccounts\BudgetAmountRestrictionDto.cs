using System.ComponentModel.DataAnnotations;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// DTO cho các hạn chế về số tiền tối thiểu có thể thay đổi đối với ngân sách
    /// </summary>
    public class BudgetAmountRestrictionDto
    {
        /// <summary>
        /// Số tiền tối thiểu có thể thay đổi ngân sách
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "MinChangeAmount phải lớn hơn hoặc bằng 0")]
        public decimal MinChangeAmount { get; set; }

        /// <summary>
        /// Số tiền tối đa có thể thay đổi ngân sách
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "MaxChangeAmount phải lớn hơn hoặc bằng 0")]
        public decimal MaxChangeAmount { get; set; }

        /// <summary>
        /// Đơn vị tiền tệ
        /// </summary>
        [StringLength(10, ErrorMessage = "Currency không được vượt quá 10 ký tự")]
        public string Currency { get; set; }
    }
}