/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API BC Assets endpoints
    /// </summary>
    public static class BCAssetsEndpoints
    {
        /// <summary>
        /// Create an ad account endpoint (v1.3)
        /// </summary>
        public const string CreateAdvertiser = "/bc/advertiser/create/";

        /// <summary>
        /// Update an ad account endpoint (v1.3)
        /// </summary>
        public const string UpdateAdvertiser = "/advertiser/update/";

        /// <summary>
        /// Disable an ad account endpoint (v1.3)
        /// </summary>
        public const string DisableAdvertiser = "/bc/advertiser/disable/";

        /// <summary>
        /// Upload a business certificate endpoint (v1.3)
        /// </summary>
        public const string UploadBusinessCertificate = "/bc/image/upload/";

        /// <summary>
        /// Check UnionPay verification requirement endpoint (v1.3)
        /// </summary>
        public const string CheckUnionPayInfo = "/bc/advertiser/unionpay_info/check/";

        /// <summary>
        /// Submit UnionPay verification endpoint (v1.3)
        /// </summary>
        public const string SubmitUnionPayVerification = "/bc/advertiser/unionpay_info/submit/";

        /// <summary>
        /// Get UnionPay verification status endpoint (v1.3)
        /// </summary>
        public const string GetUnionPayStatus = "/bc/advertiser/unionpay_info/get/";

        /// <summary>
        /// Get assets endpoint (v1.3)
        /// </summary>
        public const string GetAssets = "/bc/asset/get/";

        /// <summary>
        /// Get assets as admin endpoint (v1.3)
        /// </summary>
        public const string GetAssetsAsAdmin = "/bc/asset/admin/get/";

        /// <summary>
        /// Share assets endpoint (v1.3)
        /// </summary>
        public const string ShareAssets = "/bc/asset/share/";

        /// <summary>
        /// Unshare assets endpoint (v1.3)
        /// </summary>
        public const string UnshareAssets = "/bc/asset/unshare/";

        /// <summary>
        /// Create asset group endpoint (v1.3)
        /// </summary>
        public const string CreateAssetGroup = "/bc/asset_group/create/";

        /// <summary>
        /// Get asset groups endpoint (v1.3)
        /// </summary>
        public const string GetAssetGroups = "/bc/asset_group/get/";

        /// <summary>
        /// List asset groups endpoint (v1.3)
        /// </summary>
        public const string ListAssetGroups = "/bc/asset_group/list/";

        /// <summary>
        /// Update asset group endpoint (v1.3)
        /// </summary>
        public const string UpdateAssetGroup = "/bc/asset_group/update/";

        /// <summary>
        /// Delete asset groups endpoint (v1.3)
        /// </summary>
        public const string DeleteAssetGroups = "/bc/asset_group/delete/";

        /// <summary>
        /// Get advertiser qualification endpoint (v1.3)
        /// </summary>
        public const string GetAdvertiserQualification = "/bc/advertiser/qualification/get/";

        /// <summary>
        /// Get shared assets endpoint (v1.3)
        /// </summary>
        public const string GetSharedAssets = "/bc/asset/shared/get/";

        /// <summary>
        /// Delete assets endpoint (v1.3)
        /// </summary>
        public const string DeleteAssets = "/bc/asset/delete/";

        /// <summary>
        /// Get asset sharing history endpoint (v1.3)
        /// </summary>
        public const string GetAssetSharingHistory = "/bc/asset/share/history/get/";
    }
}
