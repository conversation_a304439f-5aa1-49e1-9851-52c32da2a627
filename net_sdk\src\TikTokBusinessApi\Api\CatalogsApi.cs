/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Catalogs operations
    /// </summary>
    public class CatalogsApi : ICatalogsApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<CatalogsApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the CatalogsApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public CatalogsApi(IApiClient apiClient, ILogger<CatalogsApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Create a catalog
        /// </summary>
        /// <param name="body">Request body containing catalog information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created catalog ID</returns>
        //public async Task<CatalogOperationResponse> CreateCatalogAsync(
        //    CatalogCreateBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.BcId))
        //        throw new ArgumentException("Business Center ID cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.Name))
        //        throw new ArgumentException("Catalog name cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.CatalogType))
        //        throw new ArgumentException("Catalog type cannot be null or empty", nameof(body));
        //    if (body.CatalogConf == null)
        //        throw new ArgumentException("Catalog configuration cannot be null", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.CatalogConf.RegionCode))
        //        throw new ArgumentException("Region code cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.CatalogConf.Currency))
        //        throw new ArgumentException("Currency cannot be null or empty", nameof(body));

        //    _logger?.LogInformation("Creating catalog {Name} for BC {BcId}", body.Name, body.BcId);

        //    return await _apiClient.CallApiAsync<CatalogOperationResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogsEndpoints.CreateCatalog}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Update the name of a catalog
        /// </summary>
        /// <param name="body">Request body containing catalog update information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated catalog ID</returns>
        //public async Task<CatalogOperationResponse> UpdateCatalogAsync(
        //    CatalogUpdateBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.BcId))
        //        throw new ArgumentException("Business Center ID cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.CatalogId))
        //        throw new ArgumentException("Catalog ID cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.Name))
        //        throw new ArgumentException("Catalog name cannot be null or empty", nameof(body));

        //    _logger?.LogInformation("Updating catalog {CatalogId} for BC {BcId}", body.CatalogId, body.BcId);

        //    return await _apiClient.CallApiAsync<CatalogOperationResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogsEndpoints.UpdateCatalog}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Delete a catalog
        /// </summary>
        /// <param name="body">Request body containing catalog deletion information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing deleted catalog ID</returns>
        //public async Task<CatalogOperationResponse> DeleteCatalogAsync(
        //    CatalogDeleteBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.BcId))
        //        throw new ArgumentException("Business Center ID cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.CatalogId))
        //        throw new ArgumentException("Catalog ID cannot be null or empty", nameof(body));

        //    _logger?.LogInformation("Deleting catalog {CatalogId} for BC {BcId}", body.CatalogId, body.BcId);

        //    return await _apiClient.CallApiAsync<CatalogOperationResponse>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogsEndpoints.DeleteCatalog}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get catalogs
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID (optional - if not specified, all catalogs will be returned)</param>
        /// <param name="page">Current number of pages (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10, range: 1-1000)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing catalog information</returns>
        public async Task<CatalogGetResponse> GetCatalogsAsync(
            string bcId,
            string? catalogId = null,
            int? page = null,
            int? pageSize = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(bcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));

            if (page.HasValue && page.Value < 1)
                throw new ArgumentException("Page must be greater than or equal to 1", nameof(page));
            if (pageSize.HasValue && (pageSize.Value < 1 || pageSize.Value > 1000))
                throw new ArgumentException("Page size must be between 1 and 1000", nameof(pageSize));

            _logger?.LogInformation("Getting catalogs for BC {BcId}", bcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = bcId
            };

            if (!string.IsNullOrWhiteSpace(catalogId))
                queryParams["catalog_id"] = catalogId;
            if (page.HasValue)
                queryParams["page"] = page.Value.ToString();
            if (pageSize.HasValue)
                queryParams["page_size"] = pageSize.Value.ToString();

            return await _apiClient.CallApiAsync<CatalogGetResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogsEndpoints.GetCatalogs}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the lexicon list for a catalog
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing catalog lexicon information</returns>
        public async Task<CatalogLexiconResponse> GetCatalogLexiconAsync(
            string bcId,
            string catalogId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(bcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));
            if (string.IsNullOrWhiteSpace(catalogId))
                throw new ArgumentException("Catalog ID cannot be null or empty", nameof(catalogId));

            _logger?.LogInformation("Getting lexicon for catalog {CatalogId} in BC {BcId}", catalogId, bcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = bcId,
                ["catalog_id"] = catalogId
            };

            return await _apiClient.CallApiAsync<CatalogLexiconResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogsEndpoints.GetCatalogLexicon}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Migrate a catalog to a Business Center
        /// </summary>
        /// <param name="body">Request body containing migration information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        //public async Task MigrateCatalogToBCAsync(
        //    CatalogCapitalizeBody body,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (body == null)
        //        throw new ArgumentNullException(nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.BcId))
        //        throw new ArgumentException("Business Center ID cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.AdvertiserId))
        //        throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(body));
        //    if (string.IsNullOrWhiteSpace(body.CatalogId))
        //        throw new ArgumentException("Catalog ID cannot be null or empty", nameof(body));

        //    _logger?.LogInformation("Migrating catalog {CatalogId} from advertiser {AdvertiserId} to BC {BcId}",
        //        body.CatalogId, body.AdvertiserId, body.BcId);

        //    await _apiClient.CallApiAsync<object>(
        //        $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogsEndpoints.MigrateCatalogToBC}",
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: body,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}

        /// <summary>
        /// Get available regions
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing available region codes</returns>
        public async Task<CatalogAvailableCountryResponse> GetAvailableRegionsAsync(
            string bcId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(bcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));

            _logger?.LogInformation("Getting available regions for BC {BcId}", bcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = bcId
            };

            return await _apiClient.CallApiAsync<CatalogAvailableCountryResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogsEndpoints.GetAvailableRegions}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get locations and currencies
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing location and currency information</returns>
        public async Task<CatalogLocationCurrencyResponse> GetLocationsCurrenciesAsync(
            CancellationToken cancellationToken = default)
        {
            _logger?.LogInformation("Getting locations and currencies");

            return await _apiClient.CallApiAsync<CatalogLocationCurrencyResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogsEndpoints.GetLocationsCurrencies}",
                HttpMethod.Get,
                queryParams: null,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the overview of a catalog
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <param name="catalogId">Catalog ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing catalog overview information</returns>
        public async Task<CatalogOverviewResponse> GetCatalogOverviewAsync(
            string bcId,
            string catalogId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(bcId))
                throw new ArgumentException("Business Center ID cannot be null or empty", nameof(bcId));
            if (string.IsNullOrWhiteSpace(catalogId))
                throw new ArgumentException("Catalog ID cannot be null or empty", nameof(catalogId));

            _logger?.LogInformation("Getting overview for catalog {CatalogId} in BC {BcId}", catalogId, bcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = bcId,
                ["catalog_id"] = catalogId
            };

            return await _apiClient.CallApiAsync<CatalogOverviewResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{CatalogsEndpoints.GetCatalogOverview}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
