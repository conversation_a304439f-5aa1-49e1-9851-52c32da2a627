/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for deleting verified URL property ownership
    /// </summary>
    public class UrlPropertyDeleteRequest
    {
        /// <summary>
        /// ID of your developer application
        /// </summary>
        [JsonPropertyName("app_id")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// Information about the URL property that you want to delete the ownership verification for
        /// </summary>
        [JsonPropertyName("url_property_meta")]
        public UrlPropertyMeta UrlPropertyMeta { get; set; } = new UrlPropertyMeta();
    }
}
