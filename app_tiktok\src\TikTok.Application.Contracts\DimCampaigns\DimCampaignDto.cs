using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.DimCampaigns
{
    public class DimCampaignDto : AuditedEntityDto<Guid>
    {
        public string CampaignId { get; set; }
        public Guid DimAdAccountId { get; set; }
        public string AdvertiserId { get; set; }
        public string CampaignName { get; set; }
        public string ObjectiveType { get; set; }
        public string CampaignType { get; set; }
        public string BudgetMode { get; set; }
        public decimal? Budget { get; set; }
        public string OperationStatus { get; set; }
        public DateTime? CreateTime { get; set; }
        public DateTime? ModifyTime { get; set; }
        public bool IsCurrent { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public int RowVersion { get; set; }
    }
}