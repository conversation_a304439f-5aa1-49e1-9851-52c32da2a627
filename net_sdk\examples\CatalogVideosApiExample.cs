/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example usage of TikTok Business API Catalog Videos operations
    /// </summary>
    public class CatalogVideosApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public CatalogVideosApiExample()
        {
            _client = new TikTokBusinessApiClient();
        }

        /// <summary>
        /// Example: Upload catalog videos via file URL
        /// </summary>
        public async Task UploadCatalogVideosExample()
        {
            try
            {
                Console.WriteLine("=== Upload Catalog Videos Example ===");

                var uploadBody = new CatalogVideoUploadBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    FileUrl = "https://example.com/catalog-videos.csv",
                    AdvertiserIds = new List<string> { "your-advertiser-id-1", "your-advertiser-id-2" }
                };

                var response = await _client.CatalogVideos.UploadVideoFileAsync(
                    "your-access-token",
                    uploadBody);

                if (response.Data != null)
                {
                    Console.WriteLine($"Upload initiated successfully!");
                    Console.WriteLine($"Feed Log ID: {response.Data.FeedLogId}");
                    Console.WriteLine($"Request ID: {response.RequestId}");
                    Console.WriteLine("Use the Feed Log ID to check upload status.");
                }
                else
                {
                    Console.WriteLine("Failed to initiate upload - no data returned");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Upload failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get catalog video upload log
        /// </summary>
        public async Task GetVideoLogExample()
        {
            try
            {
                Console.WriteLine("=== Get Video Log Example ===");

                var logRequest = new CatalogVideoLogRequest
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    FeedLogId = "your-feed-log-id",
                    Language = "en"
                };

                var response = await _client.CatalogVideos.GetVideoLogAsync(
                    "your-access-token",
                    logRequest);

                if (response.Data?.VideoFeedLog != null)
                {
                    var log = response.Data.VideoFeedLog;
                    Console.WriteLine($"Processing Status: {log.ProcessStatus}");
                    Console.WriteLine($"Videos Added: {log.AddCount}");
                    Console.WriteLine($"Videos Updated: {log.UpdateCount}");
                    Console.WriteLine($"Videos Deleted: {log.DeleteCount}");
                    Console.WriteLine($"Errors: {log.ErrorCount}");
                    Console.WriteLine($"Warnings: {log.WarnCount}");
                    Console.WriteLine($"Start Time: {log.StartTime}");
                    Console.WriteLine($"End Time: {log.EndTime}");

                    if (response.Data.FeedLogData?.ErrorAffectedVideos != null)
                    {
                        Console.WriteLine("\nErrors found:");
                        foreach (var error in response.Data.FeedLogData.ErrorAffectedVideos)
                        {
                            Console.WriteLine($"- Field: {error.Field}, Issue: {error.Issue}");
                            Console.WriteLine($"  Suggestion: {error.Suggestion}");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("Failed to get video log - no data returned");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Get video log failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get catalog videos
        /// </summary>
        public async Task GetCatalogVideosExample()
        {
            try
            {
                Console.WriteLine("=== Get Catalog Videos Example ===");

                var getRequest = new CatalogVideosGetRequest
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    Page = 1,
                    PageSize = 10
                };

                var response = await _client.CatalogVideos.GetVideosAsync(
                    "your-access-token",
                    getRequest);

                if (response.Data?.Videos != null)
                {
                    Console.WriteLine($"Found {response.Data.Videos.Count} videos");
                    Console.WriteLine($"Total videos: {response.Data.PageInfo.TotalNumber}");
                    Console.WriteLine($"Page {response.Data.PageInfo.Page} of {response.Data.PageInfo.TotalPage}");

                    foreach (var video in response.Data.Videos)
                    {
                        Console.WriteLine($"\nVideo: {video.VideoName}");
                        Console.WriteLine($"  ID: {video.CatalogVideoId}");
                        Console.WriteLine($"  Status: {video.Status}");
                        Console.WriteLine($"  Active Status: {video.ActiveStatus}");
                        Console.WriteLine($"  Brand: {video.Brand}");
                        Console.WriteLine($"  Category: {video.Category}");
                        Console.WriteLine($"  Creator: {video.Creator}");
                        Console.WriteLine($"  SKU IDs: {string.Join(", ", video.SkuIdList)}");
                        Console.WriteLine($"  Created: {video.CreateTime}");
                    }
                }
                else
                {
                    Console.WriteLine("No videos found");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Get videos failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get specific catalog videos by IDs
        /// </summary>
        public async Task GetSpecificVideosExample()
        {
            try
            {
                Console.WriteLine("=== Get Specific Videos Example ===");

                var getRequest = new CatalogVideosGetRequest
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    CatalogVideoIds = new List<string> { "video-id-1", "video-id-2" }
                };

                var response = await _client.CatalogVideos.GetVideosAsync(
                    "your-access-token",
                    getRequest);

                if (response.Data?.Videos != null)
                {
                    Console.WriteLine($"Retrieved {response.Data.Videos.Count} specific videos");

                    foreach (var video in response.Data.Videos)
                    {
                        Console.WriteLine($"\nVideo: {video.VideoName}");
                        Console.WriteLine($"  ID: {video.CatalogVideoId}");
                        Console.WriteLine($"  Status: {video.Status}");
                        Console.WriteLine($"  Preview URL: {video.PreviewUrl}");
                    }
                }
                else
                {
                    Console.WriteLine("No videos found for the specified IDs");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Get specific videos failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Delete catalog videos
        /// </summary>
        public async Task DeleteCatalogVideosExample()
        {
            try
            {
                Console.WriteLine("=== Delete Catalog Videos Example ===");

                var deleteBody = new CatalogVideosDeleteBody
                {
                    BcId = "your-business-center-id",
                    CatalogId = "your-catalog-id",
                    CatalogVideoIds = new List<string> { "video-id-to-delete" }
                };

                var response = await _client.CatalogVideos.DeleteVideosAsync(
                    "your-access-token",
                    deleteBody);

                Console.WriteLine($"Delete operation completed");
                Console.WriteLine($"Request ID: {response.RequestId}");
                Console.WriteLine("Note: Videos synced to ad accounts will remain in creative libraries");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Delete videos failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Run all examples
        /// </summary>
        public static async Task Main(string[] args)
        {
            var example = new CatalogVideosApiExample();
            
            try
            {
                Console.WriteLine("=== TikTok Business API - Catalog Videos Examples ===\n");
                
                // Example 1: Upload catalog videos
                await example.UploadCatalogVideosExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 2: Get video log
                await example.GetVideoLogExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 3: Get catalog videos
                await example.GetCatalogVideosExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 4: Get specific videos
                await example.GetSpecificVideosExample();
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 5: Delete videos
                await example.DeleteCatalogVideosExample();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Example execution failed: {ex.Message}");
            }
        }
    }
}
