using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace TikTok.CostProfiles
{
    /// <summary>
    /// Interface service cho hồ sơ chi phí
    /// </summary>
    public interface ICostProfileAppService :
        ICrudAppService<
            CostProfileDto,                     // Used to show cost profiles
            Guid,                               // Primary key of the cost profile entity
            GetCostProfileListDto,              // Used for paging/sorting
            CreateCostProfileDto,               // Used to create a new cost profile
            UpdateCostProfileDto>               // Used to update a cost profile
    {
    }
}