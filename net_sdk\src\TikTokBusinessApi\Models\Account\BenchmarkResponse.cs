/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Response model for business benchmarks
    /// </summary>
    public class BenchmarkResponse
    {
        /// <summary>
        /// Business category
        /// </summary>
        [JsonPropertyName("business_category")]
        public string BusinessCategory { get; set; } = string.Empty;

        /// <summary>
        /// Average number of likes on owned videos across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_likes")]
        public double AverageLikes { get; set; }

        /// <summary>
        /// Average number of comments on owned videos across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_comments")]
        public double AverageComments { get; set; }

        /// <summary>
        /// Average number of shares on owned videos across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_shares")]
        public double AverageShares { get; set; }

        /// <summary>
        /// Average number of videos across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_video_count")]
        public double AverageVideoCount { get; set; }

        /// <summary>
        /// Average number of followers across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_follower_count")]
        public double AverageFollowerCount { get; set; }

        /// <summary>
        /// Average increase in followers over the last 30 days across Business Accounts in business category
        /// </summary>
        [JsonPropertyName("average_follower_growth")]
        public double AverageFollowerGrowth { get; set; }

        /// <summary>
        /// Average engagement rate across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_engagement_rate")]
        public double AverageEngagementRate { get; set; }

        /// <summary>
        /// Average video views across Business Accounts in the business category
        /// </summary>
        [JsonPropertyName("average_video_views")]
        public double AverageVideoViews { get; set; }
    }
}
