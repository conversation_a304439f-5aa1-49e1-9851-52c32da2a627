using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service implementation cho việc đồng bộ dữ liệu quản lý Business Center
    /// </summary>
    public class BcManagementSyncApplicationAppService : ApplicationService, IBcManagementSyncApplicationAppService
    {
        private readonly IBcManagementSyncService _bcManagementSyncService;

        public BcManagementSyncApplicationAppService(IBcManagementSyncService bcManagementSyncService)
        {
            _bcManagementSyncService = bcManagementSyncService;
        }

        /// <summary>
        /// Đồng bộ Business Center theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BcManagementSyncResult> SyncBusinessCenterAsync(string bcId)
        {
            return await _bcManagementSyncService.SyncBusinessCenterAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BcManagementSyncResult> SyncAllBusinessCentersAsync()
        {
            return await _bcManagementSyncService.SyncAllBusinessCentersAsync();
        }

        /// <summary>
        /// Đồng bộ changelog của Business Center
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="startDate">Ngày bắt đầu (optional)</param>
        /// <param name="endDate">Ngày kết thúc (optional)</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<BcManagementSyncResult> SyncBusinessCenterChangelogAsync(string bcId, string? startDate = null, string? endDate = null)
        {
            return await _bcManagementSyncService.SyncBusinessCenterChangelogAsync(bcId, startDate, endDate);
        }
    }
}