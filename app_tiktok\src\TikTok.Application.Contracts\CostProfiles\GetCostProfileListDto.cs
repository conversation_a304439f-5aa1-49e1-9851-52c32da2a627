using System;
using Volo.Abp.Application.Dtos;

namespace TikTok.CostProfiles
{
    /// <summary>
    /// DTO cho việc lấy danh sách hồ sơ chi phí với bộ lọc
    /// </summary>
    public class GetCostProfileListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// ID nhà quảng cáo (tìm kiếm)
        /// </summary>
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Tên nhà quảng cáo (tìm kiếm)
        /// </summary>
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// ID Business Center (tìm kiếm)
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// Tiền tệ (lọc chính xác)
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Ng<PERSON><PERSON> bắt đầu (tìm kiếm nâng cao)
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Ng<PERSON>y kết thúc (tìm kiếm nâng cao)
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Số tiền tối thiểu (tìm kiếm nâng cao)
        /// </summary>
        public decimal? MinAmount { get; set; }

        /// <summary>
        /// Số tiền tối đa (tìm kiếm nâng cao)
        /// </summary>
        public decimal? MaxAmount { get; set; }
    }
}