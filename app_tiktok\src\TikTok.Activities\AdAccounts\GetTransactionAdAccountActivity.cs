﻿using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Entities;
using TikTok.Repositories;
using TikTok.Transactions;
using Volo.Abp.ObjectMapping;

namespace TikTok.Activities.AdAccounts
{
    [Activity(Category = "Tài khoản quảng cáo", DisplayName = "Lấy giao dịch cuối cùng theo danh sách tài khoản quảng cáo", Outcomes = new[] { OutcomeNames.Done })]
    public class GetTransactionAdAccountActivity : UnitOfWorkActivity
    {
        private readonly ITransactionRepository _transactionRepository;
        private readonly IObjectMapper _objectMapper;

        public GetTransactionAdAccountActivity(ITransactionRepository transactionRepository, IObjectMapper objectMapper)
        {
            _transactionRepository = transactionRepository;
            _objectMapper = objectMapper;
        }

        [ActivityInput(Hint = "BcId", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public string BcId { get; set; }

        [ActivityInput(Hint = "AdvertiserIds", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public List<string> AdvertiserIds { get; set; }

        [ActivityOutput(Hint = "Output")]
        public List<TransactionDto> Output { get; set; } = new List<TransactionDto>();

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            if (string.IsNullOrEmpty(BcId))
            {
                throw new InvalidOperationException("BcId is required.");
            }

            var queryable = (await _transactionRepository.GetQueryableAsync())
                .Where(x => x.BcId == BcId);

            if (AdvertiserIds != null && AdvertiserIds.Any())
            {
                queryable = queryable.Where(x => x.AccountId != null && AdvertiserIds.Contains(x.AccountId));
            }

            // Lấy giao dịch cuối cùng cho mỗi tài khoản quảng cáo
            var latestTransactions = queryable
                .GroupBy(x => x.AccountId)
                .Select(g => g.OrderByDescending(x => x.CreateTime).First())
                .ToList();

            Output = _objectMapper.Map<List<RawTransactionEntity>, List<TransactionDto>>(latestTransactions);
            return Done();
        }
    }
}