using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.BusinessApplications;
using TikTok.Entities;
using TikTok.Enums;
using TikTok.JobManagement;
using TikTok.Repositories;
using Volo.Abp.DependencyInjection;

namespace TikTok.BackgroundJobs.Workers
{
    /// <summary>
    /// Background job để đăng ký công việc đồng bộ
    /// </summary>
    public class RegisterJob : ITransientDependency
    {
        private readonly ILogger<RegisterJob> _logger;
        private readonly IJobConfigurationCacheService _configurationCacheService;
        private readonly IJobActivityService _jobActivityService;
        private readonly IBusinessApplicationCache _businessApplicationCache;
        private readonly IJobRepository _jobRepository;
        private readonly IJobTypeConfigurationCache _jobTypeConfigurationCache;

        public RegisterJob(
            ILogger<RegisterJob> logger,
            IJobConfigurationCacheService configurationCacheService,
            IJobActivityService jobActivityService,
            IJobRepository jobRepository,
            IBusinessApplicationCache businessApplicationCache,
            IJobTypeConfigurationCache jobTypeConfigurationCache)
        {
            _logger = logger;
            _configurationCacheService = configurationCacheService;
            _jobActivityService = jobActivityService;
            _jobRepository = jobRepository;
            _businessApplicationCache = businessApplicationCache;
            _jobTypeConfigurationCache = jobTypeConfigurationCache;
        }

        public async Task ExecuteAsync(RegisterJobArgs args)
        {
            _logger.LogDebug("Starting RegisterJob");

            try
            {
                // Lấy cấu hình hiện tại từ cache
                var configuration = await _configurationCacheService.GetCurrentConfigurationWithCacheAsync();
                if (configuration == null || !configuration.IsActive)
                {
                    _logger.LogDebug("RegisterJob skipped - no active configuration");
                    return;
                }

                // Lấy danh sách Business Application cần đồng bộ
                var businessApplications = await _businessApplicationCache.GetAllActiveAsync();

                foreach (var businessApplication in businessApplications)
                {
                    await ProcessBusinessApplicationAsync(businessApplication);
                }

                // Cập nhật thời gian chạy cuối cùng
                await _jobActivityService.UpdateLastRegisterJobRunAsync(DateTime.Now);

                _logger.LogDebug("RegisterJob completed successfully. Processed {Count} business applications",
                    businessApplications.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RegisterJob failed");
            }
        }

        /// <summary>
        /// Xử lý một Business Application
        /// </summary>
        /// <param name="businessApplication">Business Application</param>
        private async Task ProcessBusinessApplicationAsync(BusinessApplicationDto businessApplication)
        {
            // Lấy tất cả cấu hình loại công việc đang hoạt động
            var jobTypeConfigurations = await _jobTypeConfigurationCache.GetAllActiveAsync() ?? new List<JobTypeConfigurationDto>();

            foreach (var jobTypeConfig in jobTypeConfigurations.OrderBy(c => c.Priority))
            {
                if (jobTypeConfig == null || !jobTypeConfig.IsActive)
                {
                    continue;
                }
                await RegisterJobForCommandTypeAsync(businessApplication, jobTypeConfig);
            }
        }

        /// <summary>
        /// Đăng ký công việc cho một loại CommandType cụ thể
        /// </summary>
        /// <param name="businessApplication">Business Application</param>
        /// <param name="jobTypeConfig">Cấu hình loại công việc</param>
        private async Task RegisterJobForCommandTypeAsync(BusinessApplicationDto businessApplication, JobTypeConfigurationDto jobTypeConfig)
        {
            try
            {
                // Kiểm tra xem đã có job đang chờ hoặc đang xử lý cho CommandType này chưa
                var existingJobs = await _jobRepository.GetByBusinessApplicationAsync(businessApplication.Id, jobTypeConfig.CommandType, new List<JobStatus> { JobStatus.Pending, JobStatus.InProcess });
                if (existingJobs.Count > 0)
                {
                    _logger.LogDebug("Đã có job {CommandType} đang hoạt động cho BusinessApplication {BusinessApplicationId}", jobTypeConfig.CommandType, businessApplication.Id);
                    return;
                }

                // Lấy job mới nhất của CommandType này cho BusinessApplication này
                var latestJob = await _jobRepository.GetLatestJobByCommandTypeAndBusinessApplicationAsync(jobTypeConfig.CommandType, businessApplication.Id);

                if (latestJob != null)
                {
                    // Tính thời gian bỏ qua dựa vào thời gian tạo công việc cuối cùng
                    var creationTime = latestJob.CreationTime;
                    var timeCheckPoint = creationTime.AddSeconds(jobTypeConfig.IntervalSeconds);
                    if (DateTime.Now < timeCheckPoint)
                    {
                        // log debug: commandType, businessApplicationId, intervalSeconds, và dự kiến thời gian chạy tiếp theo
                        _logger.LogDebug("Chưa đủ thời gian interval cho job {CommandType} của BusinessApplication {BusinessApplicationId}. " +
                                        "Lịch chạy: {IntervalSeconds}s, dự kiến thời gian chạy tiếp theo là {NextRunTime}",
                                        jobTypeConfig.CommandType, businessApplication.Id, jobTypeConfig.IntervalSeconds, timeCheckPoint.ToString("yyyy-MM-dd HH:mm:ss"));
                        return;
                    }
                }

                // Tạo job dựa trên CommandType
                var job = CreateJobEntity(businessApplication, jobTypeConfig);
                await _jobRepository.InsertAsync(job);

                _logger.LogDebug("Created {CommandType} job {JobId} for BusinessApplication {BusinessApplicationId}",
                    jobTypeConfig.CommandType, job.Id, businessApplication.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to register {CommandType} job for BusinessApplication {BusinessApplicationId}",
                    jobTypeConfig.CommandType, businessApplication.Id);
            }
        }

        /// <summary>
        /// Tạo JobEntity dựa trên CommandType
        /// </summary>
        /// <param name="businessApplication">Business Application</param>
        /// <param name="jobTypeConfig">Cấu hình loại công việc</param>
        /// <returns>JobEntity</returns>
        private JobEntity CreateJobEntity(BusinessApplicationDto businessApplication, JobTypeConfigurationDto jobTypeConfig)
        {
            var job = new JobEntity
            {
                CommandType = jobTypeConfig.CommandType,
                Status = JobStatus.Pending,
                BusinessApplicationId = businessApplication.Id,
                Priority = jobTypeConfig.Priority,
                Notes = $"Auto-registered {jobTypeConfig.CommandType} job for {businessApplication.ApplicationId}",
            };
            // Tạo parameters dựa trên CommandType
            job.Parameters = CreateJobParameters(businessApplication, jobTypeConfig.CommandType);

            return job;
        }

        /// <summary>
        /// Tạo parameters cho job dựa trên CommandType
        /// </summary>
        /// <param name="businessApplication">Business Application</param>
        /// <param name="commandType">Loại lệnh</param>
        /// <returns>JSON parameters</returns>
        private string CreateJobParameters(BusinessApplicationDto businessApplication, CommandType commandType)
        {
            return commandType switch
            {
                _ => System.Text.Json.JsonSerializer.Serialize(new DefaultParameters { BcId = businessApplication.BcId, SyncDate = DateTime.Now })
            };
        }
    }
}