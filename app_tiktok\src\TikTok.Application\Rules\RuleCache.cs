using Microsoft.Extensions.Caching.Distributed;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.Rules;
using TikTok.Consts;
using TikTok.Domain.Entities.Rules;
using TikTok.Domain.Shared.Rules;
using TikTok.Repositories;
using TikTok.Cache;
using Tsp.Zalo.Entities;
using Volo.Abp.Caching;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.ObjectMapping;

namespace TikTok.Rules
{
    public class RuleCache : IRuleCache, ICacheService
    {
        private readonly IRuleRepository _repository;
        private readonly IDistributedCache<List<RuleDto>, string> _ruleListCache;
        private readonly IObjectMapper _mapper;
        private const string RuleAllCacheKey = "Rule:All";

        public RuleCache(
            IRuleRepository repository,
            IDistributedCache<List<RuleDto>, string> ruleListCache,
            IObjectMapper mapper)
        {
            _repository = repository;
            _ruleListCache = ruleListCache;
            _mapper = mapper;
        }

        public async Task<RuleDto?> GetByRuleNameAsync(string ruleName)
        {
            var rules = await GetAllAsync();
            var rule = rules.FirstOrDefault(x => x.RuleName == ruleName);
            return rule;
        }

        public async Task<RuleDto?> GetById(Guid id)
        {
            var rules = await GetAllAsync();
            var rule = rules.FirstOrDefault(x => x.Id == id);
            return rule;
        }

        public async Task<List<RuleDto>> GetByTargetEntityAsync(RuleTarget targetEntity)
        {
            var rules = await GetAllAsync();
            var filteredRules = rules.Where(x => x.TargetEntity == targetEntity).ToList();
            return filteredRules;
        }

        public async Task CleanCache()
        {
            await _ruleListCache.RemoveAsync(RuleAllCacheKey, hideErrors: true);
        }

        // ICacheService implementation
        public string CacheName => "Rule Cache";

        public async Task<List<string>> GetCacheKeysAsync()
        {
            return new List<string> { RuleAllCacheKey };
        }

        public async Task<bool> ClearCacheAsync()
        {
            try
            {
                await _ruleListCache.RemoveAsync(RuleAllCacheKey, hideErrors: true);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ClearCacheByKeyAsync(string cacheKey)
        {
            try
            {
                if (cacheKey == RuleAllCacheKey)
                {
                    await _ruleListCache.RemoveAsync(cacheKey, hideErrors: true);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<CacheInfoDto> GetCacheInfoAsync()
        {
            var cacheKeys = await GetCacheKeysAsync();
            return new CacheInfoDto
            {
                CacheName = CacheName,
                ActiveKeysCount = cacheKeys.Count,
                CacheKeys = cacheKeys,
                CreatedAt = DateTime.UtcNow,
                LastAccessedAt = DateTime.UtcNow,
                LastModifiedAt = DateTime.UtcNow,
                Status = "Active",
                Description = "Cache cho danh sách Rules"
            };
        }

        public async Task<List<RuleDto>> GetAllAsync()
        {
            var cacheKey = RuleAllCacheKey;
            var rules = await _ruleListCache.GetOrAddAsync(
                cacheKey,
                async () =>
                {
                    var ruleEntities = await _repository.GetListAsync();
                    var output = _mapper.Map<List<RuleEntity>, List<RuleDto>>(ruleEntities);
                    return output;
                },
                () => new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = CacheConst.CACHE_EXPIRATION
                });
            return rules;
        }
    }
}