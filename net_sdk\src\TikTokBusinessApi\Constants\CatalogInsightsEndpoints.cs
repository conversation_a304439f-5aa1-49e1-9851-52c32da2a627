/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Catalog Insights endpoints
    /// </summary>
    public static class CatalogInsightsEndpoints
    {
        /// <summary>
        /// Get filters for catalog product insights endpoint (v1.3)
        /// </summary>
        public const string GetFilters = "/catalog/insight/filter/get/";

        /// <summary>
        /// Get trending catalog products endpoint (v1.3)
        /// </summary>
        public const string GetTrendingProducts = "/catalog/insight/product/get/";

        /// <summary>
        /// Get trending catalog product categories endpoint (v1.3)
        /// </summary>
        public const string GetTrendingCategories = "/catalog/insight/category/get/";
    }
}
