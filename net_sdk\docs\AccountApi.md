# AccountApi Documentation

The `AccountApi` provides methods to manage and retrieve information about TikTok Business Accounts, including profile data, video analytics, comments, and account settings.

## Overview

The AccountApi allows you to:
- Get token information and permission scopes
- Access detailed analytics and insights about a TikTok account's follower base and profile engagement
- Retrieve video data and analytics for all public video posts
- Get business benchmarks for performance comparison
- Manage video privacy settings
- Access and manage comments on videos
- Create comments and replies
- Like/unlike comments

## Available Methods

### GetTokenInfoAsync

Get the permission scopes of a TikTok Business Account or a TikTok Personal Account that are authorized by the TikTok account user.

**Endpoint:** `/tt_user/token_info/get/`  
**Method:** POST  
**Version:** v1.3

#### Parameters

- `request` (TokenInfoRequest): Token info request containing app_id and access_token

#### Returns

`TokenInfoResponse` containing:
- `AppId`: ID of your developer application
- `Scope`: Scope of permissions for the specified access token
- `CreatorId`: Application specific unique ID of the TikTok Account

#### Example

```csharp
var request = new TokenInfoRequest
{
    AppId = "your-app-id",
    AccessToken = "your-access-token"
};

var tokenInfo = await client.Account.GetTokenInfoAsync(request);
Console.WriteLine($"Scope: {tokenInfo.Scope}");
```

### GetBusinessProfileAsync

Access detailed analytics and insights about a TikTok account's follower base and profile engagement.

**Endpoint:** `/business/get/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

- `businessId` (string): Application specific unique identifier for the TikTok account
- `startDate` (string, optional): Query start date in YYYY-MM-DD format
- `endDate` (string, optional): Query end date in YYYY-MM-DD format  
- `fields` (List<string>, optional): Requested fields to return

#### Supported Fields

- `is_business_account`: Whether the TikTok account is a TikTok Business Account
- `profile_image`: The URL to the profile photo
- `username`: The username (handle) of the TikTok account
- `display_name`: The display name (nickname) of the TikTok account
- `bio_description`: The bio description of the TikTok account
- `is_verified`: Whether TikTok has provided a verified badge
- `following_count`: The number of accounts being followed
- `followers_count`: The number of followers
- `total_likes`: Total likes on all published videos
- `videos_count`: The lifetime number of public videos posted
- Daily metrics: `video_views`, `likes`, `comments`, `shares`, etc.
- Audience demographics: `audience_ages`, `audience_genders`, `audience_countries`, `audience_cities`

#### Example

```csharp
var fields = new List<string> 
{ 
    "display_name", "followers_count", "video_views", "likes" 
};

var profile = await client.Account.GetBusinessProfileAsync(
    "business-id", "2023-01-01", "2023-01-31", fields);

Console.WriteLine($"Followers: {profile.FollowersCount}");
```

### GetVideoListAsync

Get reach and engagement data for all the public video posts of a TikTok account.

**Endpoint:** `/business/video/list/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

- `businessId` (string): Application specific unique identifier for the TikTok account
- `fields` (List<string>, optional): Requested fields to return
- `filters` (VideoListFilters, optional): Filters to apply to the result set
- `cursor` (long, optional): Cursor for pagination
- `maxCount` (int, optional): Maximum number of videos per page (max: 20)

#### Supported Fields

- `item_id`: Unique identifier for the video
- `thumbnail_url`: Preview thumbnail URL for the video
- `share_url`: A shareable URL for the video
- `embed_url`: An embeddable URL for the video
- `caption`: The caption/description for the video
- `video_duration`: Duration of the video in seconds
- `likes`, `comments`, `shares`, `favorites`: Engagement metrics
- `create_time`: Unix timestamp when the video was posted
- `reach`: Number of people who watched the content at least once
- `video_views`: Number of times viewers watched the video
- Analytics: `total_time_watched`, `average_time_watched`, `full_video_watched_rate`
- Audience data: `audience_genders`, `audience_countries`, `audience_cities`

#### Example

```csharp
var fields = new List<string> 
{ 
    "item_id", "caption", "video_views", "likes", "comments" 
};

var videoList = await client.Account.GetVideoListAsync(
    "business-id", fields, maxCount: 10);

foreach (var video in videoList.Videos)
{
    Console.WriteLine($"Video: {video.Caption} - Views: {video.VideoViews}");
}
```

### GetBenchmarkAsync

Retrieve the benchmarks specific to a business category for performance comparison.

**Endpoint:** `/business/benchmark/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

- `businessId` (string): Application specific unique identifier for the TikTok account
- `businessCategory` (string): Business category (see supported categories below)

#### Supported Business Categories

- `ART_AND_CRAFTS`: Arts & Crafts
- `AUTOMOTIVE_AND_TRANSPORTATION`: Automotive & Transportation
- `BABY`: Baby
- `BEAUTY`: Beauty
- `CLOTHING_AND_ACCESSORIES`: Clothing & Accessories
- `EDUCATION_AND_TRAINING`: Education & Training
- `ELECTRONICS`: Electronics
- `FINANCE_AND_INVESTING`: Finance & Investing
- `FOOD_AND_BEVERAGE`: Food & Beverage
- `GAMING`: Gaming
- `HEALTH_AND_WELLNESS`: Health & Wellness
- `HOME_FURNITURE_AND_APPLIANCES`: Home, Furniture & Appliances
- And more...

#### Example

```csharp
var benchmark = await client.Account.GetBenchmarkAsync(
    "business-id", "BEAUTY");

Console.WriteLine($"Average Likes: {benchmark.AverageLikes:F2}");
Console.WriteLine($"Average Engagement Rate: {benchmark.AverageEngagementRate:F4}");
```

### GetVideoSettingsAsync

Obtain the post privacy settings of a TikTok account.

**Endpoint:** `/business/video/settings/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

- `businessId` (string): Application specific unique identifier for the TikTok account

#### Example

```csharp
var settings = await client.Account.GetVideoSettingsAsync("business-id");

Console.WriteLine($"Privacy Options: {string.Join(", ", settings.PrivacyLevelOptions)}");
Console.WriteLine($"Max Duration: {settings.MaxVideoPostDurationSec} seconds");
```

### GetCommentsAsync

Access all comments or specified comments (both public and hidden) on a specific video.

**Endpoint:** `/business/comment/list/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

- `businessId` (string): Application specific unique identifier for the TikTok account
- `videoId` (string): Unique identifier for the video
- `commentIds` (List<string>, optional): Filter by specific comment IDs
- `includeReplies` (bool, optional): Whether to include replies to top-level comments
- `status` (string, optional): Filter by visibility status ("PUBLIC" or "ALL")
- `sortField` (string, optional): Sort by "likes", "replies", or "create_time"
- `sortOrder` (string, optional): Sort order "asc", "desc", or "smart"
- `cursor` (int, optional): Cursor for pagination
- `maxCount` (int, optional): Maximum number of comments per page (max: 30)

#### Example

```csharp
var comments = await client.Account.GetCommentsAsync(
    "business-id", "video-id", includeReplies: true, maxCount: 20);

foreach (var comment in comments.Comments)
{
    Console.WriteLine($"{comment.Username}: {comment.Text} (Likes: {comment.Likes})");
}
```

### GetCommentRepliesAsync

Access all replies to a specific comment on a video.

**Endpoint:** `/business/comment/reply/list/`  
**Method:** GET  
**Version:** v1.3

#### Parameters

- `businessId` (string): Application specific unique identifier for the TikTok account
- `videoId` (string): Unique identifier for the video
- `commentId` (string): Unique identifier for the parent comment
- Additional parameters similar to GetCommentsAsync

#### Example

```csharp
var replies = await client.Account.GetCommentRepliesAsync(
    "business-id", "video-id", "comment-id");

foreach (var reply in replies.Comments)
{
    Console.WriteLine($"Reply: {reply.Text}");
}
```

### CreateCommentAsync

Create a new comment on a video posted by an owned TikTok Account.

**Endpoint:** `/business/comment/create/`  
**Method:** POST  
**Version:** v1.3

#### Parameters

- `request` (CommentCreateRequest): Comment creation request

#### Example

```csharp
var request = new CommentCreateRequest
{
    BusinessId = "business-id",
    VideoId = "video-id",
    Text = "This is a great video! 👍"
};

var newComment = await client.Account.CreateCommentAsync(request);
Console.WriteLine($"Created comment: {newComment.CommentId}");
```

### CreateCommentReplyAsync

Create a reply to an existing comment on a video.

**Endpoint:** `/business/comment/reply/create/`  
**Method:** POST  
**Version:** v1.3

#### Parameters

- `request` (CommentReplyCreateRequest): Comment reply creation request

#### Example

```csharp
var request = new CommentReplyCreateRequest
{
    BusinessId = "business-id",
    VideoId = "video-id",
    CommentId = "parent-comment-id",
    Text = "Thank you for your comment! 😊"
};

var newReply = await client.Account.CreateCommentReplyAsync(request);
Console.WriteLine($"Created reply: {newReply.CommentId}");
```

### LikeCommentAsync

Like or unlike an existing comment on a video.

**Endpoint:** `/business/comment/like/`  
**Method:** POST  
**Version:** v1.3

#### Parameters

- `request` (CommentLikeRequest): Comment like/unlike request

#### Example

```csharp
var request = new CommentLikeRequest
{
    BusinessId = "business-id",
    CommentId = "comment-id",
    Action = "LIKE" // or "UNLIKE"
};

await client.Account.LikeCommentAsync(request);
```

## Error Handling

All methods may throw `SdkException` for API errors. Always wrap calls in try-catch blocks:

```csharp
try
{
    var profile = await client.Account.GetBusinessProfileAsync("business-id");
    // Process profile data
}
catch (SdkException ex)
{
    Console.WriteLine($"API Error: {ex.Message}");
    Console.WriteLine($"Error Code: {ex.ErrorCode}");
}
```

## Rate Limiting

The TikTok Business API has rate limits. The SDK automatically handles retries with exponential backoff for rate limit errors.

## Authentication

All Account API methods require proper authentication. Ensure your access token has the necessary permissions:

- `user.info.basic`: For basic profile information
- `user.info.username`: For username access
- `user.info.profile`: For profile details
- `user.info.stats`: For follower and engagement statistics
- `user.insights`: For analytics and insights data
- `video.list`: For video data access
- `comment.list`: For comment access
- `comment.list.manage`: For comment management

## Data Latency

- Profile-level metrics have a 24-48 hour delay
- Video data stops updating 365 days after the video is published
- Real-time data includes profile information, usernames, and basic video metadata

For more information, refer to the [TikTok Business API documentation](https://business-api.tiktok.com/portal/docs).
