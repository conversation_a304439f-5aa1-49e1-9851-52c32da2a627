﻿using System.Threading.Tasks;
using TikTok.BusinessApplications;
using TikTok.Entities;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Entities.Events;
using Volo.Abp.EventBus;

namespace TikTok.EventHandles
{
    public class JobTypeConfigurationEventHandler : ILocalEventHandler<EntityChangedEventData<JobTypeConfigurationEntity>>, ITransientDependency
    {
        private readonly IJobTypeConfigurationCache _cache;

        public JobTypeConfigurationEventHandler(IJobTypeConfigurationCache cache)
        {
            _cache = cache;
        }

        public async Task HandleEventAsync(EntityChangedEventData<JobTypeConfigurationEntity> eventData)
        {
            if (eventData == null || eventData.Entity == null)
            {
                return;
            }

            await _cache.CleanCache();
        }
    }
}