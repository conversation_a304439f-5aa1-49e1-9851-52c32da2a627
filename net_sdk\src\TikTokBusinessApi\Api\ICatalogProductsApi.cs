/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Catalog Products operations
    /// </summary>
    public interface ICatalogProductsApi
    {
        /// <summary>
        /// Upload products via a file URL
        /// </summary>
        /// <param name="request">Product file upload request</param>
        /// <returns>Product file upload response</returns>
        // Task<ProductFileUploadResponse> UploadProductsViaFileAsync(ProductFileUploadBody request);

        /// <summary>
        /// Upload products via JSON schema
        /// </summary>
        /// <param name="request">Product upload request</param>
        /// <returns>Product file upload response</returns>
        // Task<ProductFileUploadResponse> UploadProductsViaJsonAsync(ProductUploadBody request);

        /// <summary>
        /// Update products
        /// </summary>
        /// <param name="request">Product update request</param>
        /// <returns>Product file upload response</returns>
        // Task<ProductFileUploadResponse> UpdateProductsAsync(ProductUpdateBody request);

        /// <summary>
        /// Delete products
        /// </summary>
        /// <param name="request">Product delete request</param>
        /// <returns>Product file upload response</returns>
        // Task<ProductFileUploadResponse> DeleteProductsAsync(ProductDeleteBody request);

        /// <summary>
        /// Get products
        /// </summary>
        /// <param name="request">Product get request</param>
        /// <returns>Product get response</returns>
        Task<ProductGetResponse> GetProductsAsync(ProductGetRequest request);

        /// <summary>
        /// Get product handling log
        /// </summary>
        /// <param name="request">Product log request</param>
        /// <returns>Product log response</returns>
        Task<ProductLogResponse> GetProductLogAsync(ProductLogRequest request);
    }
}
