/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    /// <summary>
    /// Request body for updating an advertiser account
    /// </summary>
    public class AdvertiserUpdateBody
    {
        /// <summary>
        /// Address
        /// </summary>
        [JsonPropertyName("address")]
        public string? Address { get; set; }

        /// <summary>
        /// Advertiser budgets
        /// </summary>
        [JsonPropertyName("advertiser_budgets")]
        public List<AdvertiserupdateAdvertiserBudgets>? AdvertiserBudgets { get; set; }

        /// <summary>
        /// Advertiser ID
        /// </summary>
        [JsonPropertyName("advertiser_id")]
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Advertiser name
        /// </summary>
        [JsonPropertyName("advertiser_name")]
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// Business Center ID
        /// </summary>
        [JsonPropertyName("bc_id")]
        public long? BcId { get; set; }

        /// <summary>
        /// Budget update type
        /// </summary>
        [JsonPropertyName("budget_update_type")]
        public string? BudgetUpdateType { get; set; }

        /// <summary>
        /// Child Business Center ID
        /// </summary>
        [JsonPropertyName("child_bc_id")]
        public long? ChildBcId { get; set; }

        /// <summary>
        /// Company name
        /// </summary>
        [JsonPropertyName("company")]
        public string? Company { get; set; }

        /// <summary>
        /// Contact email
        /// </summary>
        [JsonPropertyName("contact_email")]
        public string? ContactEmail { get; set; }

        /// <summary>
        /// Contact name
        /// </summary>
        [JsonPropertyName("contact_name")]
        public string? ContactName { get; set; }

        /// <summary>
        /// Contact number
        /// </summary>
        [JsonPropertyName("contact_number")]
        public string? ContactNumber { get; set; }

        /// <summary>
        /// License image ID
        /// </summary>
        [JsonPropertyName("license_image_id")]
        public string? LicenseImageId { get; set; }

        /// <summary>
        /// License number
        /// </summary>
        [JsonPropertyName("license_no")]
        public string? LicenseNo { get; set; }

        /// <summary>
        /// Whether need to submit certificate
        /// </summary>
        [JsonPropertyName("need_submit_certificate")]
        public bool? NeedSubmitCertificate { get; set; }

        /// <summary>
        /// Promotion link
        /// </summary>
        [JsonPropertyName("promotion_link")]
        public string? PromotionLink { get; set; }

        /// <summary>
        /// Qualification images
        /// </summary>
        [JsonPropertyName("qualification_images")]
        public List<AdvertiserupdateQualificationImages>? QualificationImages { get; set; }

        /// <summary>
        /// Tax map
        /// </summary>
        [JsonPropertyName("tax_map")]
        public Dictionary<string, string>? TaxMap { get; set; }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="obj">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object? obj)
        {
            if (obj is not AdvertiserUpdateBody other)
                return false;

            return Address == other.Address &&
                   AdvertiserId == other.AdvertiserId &&
                   AdvertiserName == other.AdvertiserName &&
                   BcId == other.BcId &&
                   BudgetUpdateType == other.BudgetUpdateType &&
                   ChildBcId == other.ChildBcId &&
                   Company == other.Company &&
                   ContactEmail == other.ContactEmail &&
                   ContactName == other.ContactName &&
                   ContactNumber == other.ContactNumber &&
                   LicenseImageId == other.LicenseImageId &&
                   LicenseNo == other.LicenseNo &&
                   NeedSubmitCertificate == other.NeedSubmitCertificate &&
                   PromotionLink == other.PromotionLink;
        }

        /// <summary>
        /// Returns the hash code for this instance
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            var hash = new HashCode();
            hash.Add(Address);
            hash.Add(AdvertiserId);
            hash.Add(AdvertiserName);
            hash.Add(BcId);
            hash.Add(BudgetUpdateType);
            hash.Add(ChildBcId);
            hash.Add(Company);
            hash.Add(ContactEmail);
            hash.Add(ContactName);
            hash.Add(ContactNumber);
            hash.Add(LicenseImageId);
            hash.Add(LicenseNo);
            hash.Add(NeedSubmitCertificate);
            hash.Add(PromotionLink);
            return hash.ToHashCode();
        }

        /// <summary>
        /// Returns the string representation of the object
        /// </summary>
        /// <returns>String representation of the object</returns>
        public override string ToString()
        {
            return $"AdvertiserUpdateBody {{ AdvertiserId = {AdvertiserId}, AdvertiserName = {AdvertiserName}, Company = {Company} }}";
        }
    }
}
