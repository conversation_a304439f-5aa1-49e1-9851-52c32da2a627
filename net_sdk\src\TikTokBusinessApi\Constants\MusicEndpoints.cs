/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Music endpoints
    /// </summary>
    public static class MusicEndpoints
    {
        /// <summary>
        /// Upload a piece of music endpoint (v1.3)
        /// </summary>
        public const string UploadMusic = "/file/music/upload/";

        /// <summary>
        /// Get the music list endpoint (v1.3)
        /// </summary>
        public const string GetMusicList = "/file/music/get/";
    }
}
