/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API AutomatedRules operations
    /// </summary>
    public class AutomatedRulesApi : IAutomatedRulesApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<AutomatedRulesApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the AutomatedRulesApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public AutomatedRulesApi(IApiClient apiClient, ILogger<AutomatedRulesApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Create automated rules
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing rules to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing created rule IDs</returns>
        // public async Task<Response<AutomatedRulesResponse>> CreateRulesAsync(
        //     string accessToken,
        //     AutomatedRulesCreateBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Creating automated rules for advertiser {AdvertiserId}", body.AdvertiserId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<AutomatedRulesResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{AutomatedRulesEndpoints.CreateRules}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get rules by rule IDs
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="ruleIds">IDs of the rules to get</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing rule information</returns>
        public async Task<Response<AutomatedRulesGetResponse>> GetRulesByIdAsync(
            string accessToken,
            string advertiserId,
            List<string> ruleIds,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));
            if (ruleIds == null || ruleIds.Count == 0)
                throw new ArgumentException("Rule IDs cannot be null or empty", nameof(ruleIds));

            _logger?.LogInformation("Getting rules by ID for advertiser {AdvertiserId}", advertiserId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var requestBody = new
            {
                advertiser_id = advertiserId,
                rule_ids = ruleIds
            };

            return await _apiClient.CallApiAsync<Response<AutomatedRulesGetResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{AutomatedRulesEndpoints.GetRulesById}",
                HttpMethod.Get,
                queryParams: null,
                body: requestBody,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get rules based on filter values
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="filtering">Filtering options</param>
        /// <param name="tzone">User timezone</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing filtered rules</returns>
        public async Task<Response<AutomatedRulesGetResponse>> GetRulesByFiltersAsync(
            string accessToken,
            string advertiserId,
            AutomatedRulesFiltering? filtering = null,
            string? tzone = null,
            int page = 1,
            int pageSize = 10,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogInformation("Getting rules by filters for advertiser {AdvertiserId}", advertiserId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var requestBody = new
            {
                advertiser_id = advertiserId,
                filtering = filtering,
                tzone = tzone,
                page = page,
                page_size = pageSize
            };

            return await _apiClient.CallApiAsync<Response<AutomatedRulesGetResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{AutomatedRulesEndpoints.GetRulesByFilters}",
                HttpMethod.Get,
                queryParams: null,
                body: requestBody,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get execution results of rules based on filter values
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="filtering">Filtering options</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="lang">Language of error messages</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing rule execution results</returns>
        public async Task<Response<AutomatedRulesGetResponse>> GetRuleResultsAsync(
            string accessToken,
            string advertiserId,
            AutomatedRulesFiltering? filtering = null,
            int page = 1,
            int pageSize = 10,
            string? lang = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrWhiteSpace(advertiserId))
                throw new ArgumentException("Advertiser ID cannot be null or empty", nameof(advertiserId));

            _logger?.LogInformation("Getting rule results for advertiser {AdvertiserId}", advertiserId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            var requestBody = new
            {
                advertiser_id = advertiserId,
                filtering = filtering,
                page = page,
                page_size = pageSize,
                lang = lang
            };

            return await _apiClient.CallApiAsync<Response<AutomatedRulesGetResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{AutomatedRulesEndpoints.GetRuleResults}",
                HttpMethod.Get,
                queryParams: null,
                body: requestBody,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get rule execution results by execution IDs
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing result details to get</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing result details</returns>
        public async Task<Response<AutomatedRulesResultDetailsResponse>> GetResultDetailsAsync(
            string accessToken,
            AutomatedRulesResultDetailsBody body,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (body == null)
                throw new ArgumentNullException(nameof(body));

            _logger?.LogInformation("Getting result details for advertiser {AdvertiserId}", body.AdvertiserId);

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<AutomatedRulesResultDetailsResponse>>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{AutomatedRulesEndpoints.GetResultDetails}",
                HttpMethod.Get,
                queryParams: null,
                body: body,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Update the details of rules
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing rules to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated rule IDs</returns>
        // public async Task<Response<AutomatedRulesResponse>> UpdateRulesAsync(
        //     string accessToken,
        //     AutomatedRulesUpdateBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Updating automated rules for advertiser {AdvertiserId}", body.AdvertiserId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<AutomatedRulesResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{AutomatedRulesEndpoints.UpdateRules}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Turn on, turn off, or delete a group of rules
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing rule status updates</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing updated rule IDs</returns>
        // public async Task<Response<AutomatedRulesResponse>> UpdateRuleStatusesAsync(
        //     string accessToken,
        //     AutomatedRulesStatusUpdateBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Updating rule statuses for advertiser {AdvertiserId}", body.AdvertiserId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<AutomatedRulesResponse>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{AutomatedRulesEndpoints.UpdateRuleStatuses}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Bind objects to an existing rule, or unbind objects from a rule
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="body">Request body containing bind/unbind information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        // public async Task<Response<object>> BindUnbindRulesAsync(
        //     string accessToken,
        //     AutomatedRulesBindBody body,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (string.IsNullOrWhiteSpace(accessToken))
        //         throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //     if (body == null)
        //         throw new ArgumentNullException(nameof(body));

        //     _logger?.LogInformation("Binding/unbinding rules for advertiser {AdvertiserId}", body.AdvertiserId);

        //     var headers = new Dictionary<string, string>
        //     {
        //         ["Access-Token"] = accessToken
        //     };

        //     return await _apiClient.CallApiAsync<Response<object>>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{AutomatedRulesEndpoints.BindUnbindRules}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: body,
        //         headerParams: headers,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }
    }
}
