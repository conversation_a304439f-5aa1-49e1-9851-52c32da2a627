/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Store endpoints
    /// </summary>
    public static class StoreEndpoints
    {
        /// <summary>
        /// Get available stores under an ad account endpoint (v1.3)
        /// </summary>
        public const string GetStoreList = "/store/list/";

        /// <summary>
        /// Get products within a TikTok Shop endpoint (v1.3)
        /// </summary>
        public const string GetStoreProducts = "/store/product/get/";
    }
}
