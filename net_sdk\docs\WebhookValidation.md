# TikTok Business API Webhook Validation

The TikTok Business API SDK provides comprehensive webhook validation utilities to help you securely handle webhook events from TikTok.

## Overview

The `WebhookValidator` class provides methods to:
- Validate webhook signatures using HMAC-SHA256
- Parse webhook event payloads
- Validate timestamps to prevent replay attacks
- Handle different webhook event types

## Quick Start

### Basic Webhook Validation

```csharp
using TikTokBusinessApi.Utilities;

// Create a webhook validator
var validator = new WebhookValidator();

// Validate a webhook signature
var isValid = validator.ValidateSignature(
    payload: requestBody,
    signature: Request.Headers["X-TikTok-Signature"],
    secret: "your-webhook-secret",
    timestamp: Request.Headers["X-TikTok-Timestamp"]
);

if (isValid)
{
    // Process the webhook event
    var campaignEvent = validator.ParseWebhookEvent<CampaignWebhookEvent>(requestBody);
    // Handle the event...
}
```

### ASP.NET Core Controller Example

```csharp
[ApiController]
[Route("api/[controller]")]
public class WebhookController : ControllerBase
{
    private readonly WebhookValidator _webhookValidator;
    private readonly IConfiguration _configuration;

    public WebhookController(WebhookValidator webhookValidator, IConfiguration configuration)
    {
        _webhookValidator = webhookValidator;
        _configuration = configuration;
    }

    [HttpPost("tiktok")]
    public async Task<IActionResult> HandleTikTokWebhook()
    {
        // Read the raw request body
        using var reader = new StreamReader(Request.Body);
        var payload = await reader.ReadToEndAsync();

        // Get headers
        var signature = Request.Headers["X-TikTok-Signature"].FirstOrDefault();
        var timestamp = Request.Headers["X-TikTok-Timestamp"].FirstOrDefault();
        var webhookSecret = _configuration["TikTok:WebhookSecret"];

        // Validate the webhook
        if (!_webhookValidator.ValidateSignature(payload, signature, webhookSecret, timestamp))
        {
            return Unauthorized("Invalid signature");
        }

        // Process the webhook event
        await ProcessWebhookEventAsync(payload);

        return Ok();
    }
}
```

## Webhook Event Types

The SDK provides strongly-typed models for different webhook events:

### Campaign Events
```csharp
var campaignEvent = validator.ParseWebhookEvent<CampaignWebhookEvent>(payload);
Console.WriteLine($"Campaign {campaignEvent.Data.CampaignName} was {campaignEvent.EventType}");
```

### Ad Group Events
```csharp
var adGroupEvent = validator.ParseWebhookEvent<AdGroupWebhookEvent>(payload);
Console.WriteLine($"Ad Group {adGroupEvent.Data.AdGroupName} was {adGroupEvent.EventType}");
```

### Ad Events
```csharp
var adEvent = validator.ParseWebhookEvent<AdWebhookEvent>(payload);
Console.WriteLine($"Ad {adEvent.Data.AdName} was {adEvent.EventType}");
```

### Comment Events
```csharp
var commentEvent = validator.ParseWebhookEvent<CommentWebhookEvent>(payload);
Console.WriteLine($"Comment by {commentEvent.Data.Username}: {commentEvent.Data.Text}");
```

### Advertiser Account Events
```csharp
var accountEvent = validator.ParseWebhookEvent<AdvertiserAccountWebhookEvent>(payload);
Console.WriteLine($"Account {accountEvent.Data.AdvertiserName} was {accountEvent.EventType}");
```

## Event Type Constants

Use the `WebhookEventTypes` class for event type constants:

```csharp
switch (eventType)
{
    case WebhookEventTypes.CampaignCreation:
        // Handle campaign creation
        break;
    case WebhookEventTypes.CampaignUpdate:
        // Handle campaign update
        break;
    case WebhookEventTypes.AdGroupCreation:
        // Handle ad group creation
        break;
    // ... other event types
}
```

Available event types:
- `ADVERTISER_ACCOUNT_CREATION`, `ADVERTISER_ACCOUNT_UPDATE`, `ADVERTISER_ACCOUNT_DELETION`
- `CAMPAIGN_CREATION`, `CAMPAIGN_UPDATE`, `CAMPAIGN_DELETION`
- `ADGROUP_CREATION`, `ADGROUP_UPDATE`, `ADGROUP_DELETION`
- `AD_CREATION`, `AD_UPDATE`, `AD_DELETION`
- `COMMENT_CREATION`, `COMMENT_UPDATE`, `COMMENT_DELETION`

## Security Features

### Signature Validation
The webhook validator uses HMAC-SHA256 to verify that webhooks are genuinely from TikTok:

```csharp
// The signature is computed as: HMAC-SHA256(timestamp + payload, secret)
var computedSignature = validator.ComputeSignature(payload, secret, timestamp);
```

### Timestamp Validation
Prevents replay attacks by validating that the timestamp is within an acceptable tolerance:

```csharp
// Default tolerance is 300 seconds (5 minutes)
var isValidTimestamp = validator.ValidateTimestamp(timestamp, toleranceSeconds: 300);
```

### Constant-Time Comparison
Uses constant-time string comparison to prevent timing attacks when comparing signatures.

## Configuration

### Dependency Injection Setup

```csharp
// In Program.cs or Startup.cs
services.AddSingleton<WebhookValidator>();
```

### Configuration Settings

```json
{
  "TikTok": {
    "WebhookSecret": "your-webhook-secret-from-tiktok-developer-console"
  }
}
```

## Error Handling

The webhook validator provides comprehensive error handling:

```csharp
try
{
    var isValid = validator.ValidateSignature(payload, signature, secret, timestamp);
    if (!isValid)
    {
        // Log security incident
        logger.LogWarning("Invalid webhook signature received from {IP}", Request.HttpContext.Connection.RemoteIpAddress);
        return Unauthorized();
    }

    var eventData = validator.ParseWebhookEvent<CampaignWebhookEvent>(payload);
    // Process event...
}
catch (JsonException ex)
{
    logger.LogError(ex, "Failed to parse webhook payload");
    return BadRequest("Invalid payload format");
}
catch (ArgumentException ex)
{
    logger.LogError(ex, "Invalid webhook parameters");
    return BadRequest("Missing required parameters");
}
```

## Best Practices

1. **Always validate signatures** before processing webhook events
2. **Use HTTPS** for your webhook endpoints
3. **Implement idempotency** to handle duplicate webhook deliveries
4. **Log security events** for monitoring and debugging
5. **Set appropriate timeouts** for webhook processing
6. **Return appropriate HTTP status codes** (200 for success, 4xx for client errors)

## Testing

The SDK includes comprehensive tests for webhook validation. You can run them with:

```bash
dotnet test --filter "FullyQualifiedName~WebhookValidatorTests"
```

## Complete Example

See `WebhookValidationExample.cs` in the examples folder for a complete implementation showing how to handle different webhook event types.
