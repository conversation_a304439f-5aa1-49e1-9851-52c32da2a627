/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Split Test operations
    /// </summary>
    public interface ISplitTestApi
    {
        // /// <summary>
        // /// Create a split test group
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing split test configuration</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing split test group ID</returns>
        // Task<Response<SplitTestCreateResponse>> CreateSplitTestAsync(
        //     string accessToken,
        //     SplitTestCreateBody body,
        //     CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Update the start time and end time of a split test
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing updated times</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response indicating success</returns>
        // Task<Response<object>> UpdateSplitTestAsync(
        //     string accessToken,
        //     SplitTestUpdateBody body,
        //     CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Stop an ad group level or campaign level split test
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing split test group ID</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response indicating success</returns>
        // Task<Response<object>> EndSplitTestAsync(
        //     string accessToken,
        //     SplitTestEndBody body,
        //     CancellationToken cancellationToken = default);

        /// <summary>
        /// Get test results, including p-values for each metric
        /// </summary>
        /// <param name="accessToken">Authorized access token</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <param name="splitTestGroupId">Split test group ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing split test results</returns>
        Task<Response<SplitTestResultResponse>> GetSplitTestResultAsync(
            string accessToken,
            string advertiserId,
            string splitTestGroupId,
            CancellationToken cancellationToken = default);

        // /// <summary>
        // /// Run the winning ad group in your ad group level split test
        // /// </summary>
        // /// <param name="accessToken">Authorized access token</param>
        // /// <param name="body">Request body containing winning object ID</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response indicating success</returns>
        // Task<Response<object>> PromoteSplitTestAsync(
        //     string accessToken,
        //     SplitTestPromoteBody body,
        //     CancellationToken cancellationToken = default);
    }
}
