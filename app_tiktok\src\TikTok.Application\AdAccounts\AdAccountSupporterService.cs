﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TikTok.Entities.AdAccounts;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace TikTok.AdAccounts
{
    [Obsolete]
    public class AdAccountSupporterService : CrudAppService<AdAccountSupporterEntity, AdAccountSupporterDto, Guid>,
        IAdAccountSupporterService
    {
        private readonly ILogger<AdAccountSupporterService> _logger;
        public AdAccountSupporterService(IRepository<AdAccountSupporterEntity, Guid> repository
            , ILogger<AdAccountSupporterService> logger) : base(repository)
        {
            _logger = logger;
        }
    }
}
