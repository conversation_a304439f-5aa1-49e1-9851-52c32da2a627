using System;
using TikTok.Enums;
using Volo.Abp.Application.Dtos;

namespace TikTok.BalanceAdAccounts
{
    /// <summary>
    /// DTO để lấy danh sách tài khoản quảng cáo với thông tin số dư và ngân sách mới nhất
    /// </summary>
    public class GetLatestBalanceAdAccountListDto : PagedAndSortedResultRequestDto
    {
        /// <summary>
        /// Từ khóa tìm kiếm
        /// </summary>
        public string? Filter { get; set; }

        /// <summary>
        /// ID tài khoản nhà quảng cáo
        /// </summary>
        public string? AdvertiserId { get; set; }

        /// <summary>
        /// Tên tài khoản nhà quảng cáo
        /// </summary>
        public string? AdvertiserName { get; set; }

        /// <summary>
        /// ID của Business Center
        /// </summary>
        public string? BcId { get; set; }

        /// <summary>
        /// Trạng thái tài khoản nhà quảng cáo
        /// </summary>
        public AdvertiserAccountStatus? AdvertiserStatus { get; set; }

        /// <summary>
        /// Loại tài khoản nhà quảng cáo
        /// </summary>
        public AdAccountType? AdvertiserType { get; set; }

        /// <summary>
        /// Chế độ ngân sách
        /// </summary>
        public BudgetMode? BudgetMode { get; set; }

        public string? Company { get; set; }

        /// <summary>
        /// Tiền tệ
        /// </summary>
        public string? Currency { get; set; }

        /// <summary>
        /// Múi giờ
        /// </summary>
        public string? Timezone { get; set; }

        /// <summary>
        /// Thời gian tạo từ
        /// </summary>
        public DateTime? CreateTimeFrom { get; set; }

        /// <summary>
        /// Thời gian tạo đến
        /// </summary>
        public DateTime? CreateTimeTo { get; set; }

        /// <summary>
        /// Ngày đồng bộ từ
        /// </summary>
        public DateTime? DateFrom { get; set; }

        /// <summary>
        /// Ngày đồng bộ đến
        /// </summary>
        public DateTime? DateTo { get; set; }

        /// <summary>
        /// Số dư tài khoản từ
        /// </summary>
        public decimal? AccountBalanceFrom { get; set; }

        /// <summary>
        /// Số dư tài khoản đến
        /// </summary>
        public decimal? AccountBalanceTo { get; set; }

        /// <summary>
        /// Ngân sách từ
        /// </summary>
        public decimal? BudgetFrom { get; set; }

        /// <summary>
        /// Ngân sách đến
        /// </summary>
        public decimal? BudgetTo { get; set; }

        /// <summary>
        /// Số ngày mở tài khoản từ
        /// </summary>
        public int? AccountOpenDaysFrom { get; set; }

        /// <summary>
        /// Số ngày mở tài khoản đến
        /// </summary>
        public int? AccountOpenDaysTo { get; set; }
    }
}
