using System;
using System.Threading.Tasks;
using Custom.BaseActivities;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Expressions;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using TikTok.Application.Contracts.MessageProviders;

namespace TikTok.Activities.SendMessages
{
    [Activity(Category = "Messages", DisplayName = "Gửi tin nhắn (mặc định)", Outcomes = new[] { OutcomeNames.Done })]
    public class SendMessageActivity : UnitOfWorkActivity
    {
        private readonly IMessageService _messageService;
        private readonly ILogger<SendMessageActivity> _logger;

        public SendMessageActivity(
            IMessageService messageService,
            ILogger<SendMessageActivity> logger)
        {
            _messageService = messageService;
            _logger = logger;
        }

        [ActivityInput(Hint = "Nội dung tin nhắn", SupportedSyntaxes = [SyntaxNames.JavaScript, SyntaxNames.Liquid, SyntaxNames.Json])]
        public string Message { get; set; }

        protected override async ValueTask<IActivityExecutionResult> ProcessOnExecuteAsync(ActivityExecutionContext context)
        {
            var message = Message ?? context.Input as string;

            if (string.IsNullOrWhiteSpace(message))
            {
                _logger?.LogWarning("SendMessageActivity: Message is null or empty");
                return Done();
            }

            await _messageService.SendMessageAsync(message);
            _logger?.LogDebug("SendMessageActivity: Message sent");
            return Done();
        }
    }
}


