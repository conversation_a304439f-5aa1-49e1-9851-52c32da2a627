# RawRecordTransactionBcEntity

## Thông tin bảng dữ liệu

-   **Table**: `RecordTransactionBcs`
-   **TablePrefix**: `Raw_`
-   **TableName**: `Raw_RecordTransactionBcs`
-   **DbSchema**: `null`

## Mô tả

Entity đại diện cho bản ghi giao dịch BC (Business Center Transaction Record). Lưu trữ thông tin về các giao dịch tài chính tổng hợp của Business Center, bao gồm chi tiết giao dịch, thông tin hóa đơn và loại quỹ.

## Bảng dữ liệu: RawRecordTransactionBc

| Tên Field            | Kiểu dữ liệu | Bắt buộc | Độ dài | Mô tả                                                             |
| -------------------- | ------------ | -------- | ------ | ----------------------------------------------------------------- |
| Id                   | Guid         | ✅       | -      | ID duy nhất của bản ghi (Primary Key)                             |
| BcId                 | string       | ✅       | 50     | Business Center ID - Định danh Business Center                    |
| Date                 | DateTime     | ✅       | -      | Ngày giao dịch (khóa chính kết hợp với BcId), UTC time            |
| Amount               | decimal      | ✅       | 18,2   | Số tiền giao dịch                                                 |
| Timezone             | string       | ✅       | 20     | Múi giờ đúng theo múi giờ của BC để tham chiếu đến ngày giao dịch |
| Currency             | string       | ✅       | 10     | Tiền tệ được sử dụng trong giao dịch                              |
| FundsType            | FundsType    | ✅       | -      | Loại quỹ                                                          |
| InvoiceId            | string       | ❌       | 100    | ID hóa đơn                                                        |
| InvoiceSerialNumber  | string       | ❌       | 100    | Số sê-ri hóa đơn                                                  |
| CreationTime         | DateTime     | ✅       | -      | Thời gian tạo bản ghi (Audit)                                     |
| CreatorId            | Guid?        | ❌       | -      | ID người tạo (Audit)                                              |
| LastModificationTime | DateTime?    | ❌       | -      | Thời gian sửa đổi cuối (Audit)                                    |
| LastModifierId       | Guid?        | ❌       | -      | ID người sửa đổi cuối (Audit)                                     |

## Cấu trúc dữ liệu

### Thông tin định danh

-   **BcId**: Business Center ID - Định danh Business Center
-   **Date**: Ngày giao dịch (khóa chính kết hợp với BcId để tránh trùng lặp)
-   **Timezone**: Múi giờ gốc để tham chiếu khi convert từ UTC

### Thông tin giao dịch

-   **Amount**: Số tiền giao dịch với độ chính xác 2 chữ số thập phân
-   **Currency**: Đơn vị tiền tệ của giao dịch
-   **FundsType**: Loại quỹ (CASH, CREDIT, GRANT, etc.)

### Thông tin hóa đơn

-   **InvoiceId**: ID hóa đơn (optional)
-   **InvoiceSerialNumber**: Số sê-ri hóa đơn (optional)

## Mục đích sử dụng

-   Lưu trữ bản ghi giao dịch tài chính tổng hợp cho Business Center
-   Theo dõi tổng chi tiêu và thu nhập của Business Center
-   Phân tích luồng tiền tổng hợp
-   Cung cấp dữ liệu cho báo cáo tài chính cấp Business Center
-   Hỗ trợ kiểm toán và đối soát tài chính tổng hợp

## Mối quan hệ

-   **RawBusinessCenterEntity**: Liên kết với Business Center
-   **RawRecordTransactionAdAccountEntity**: Bản ghi giao dịch chi tiết cấp tài khoản quảng cáo
-   **RawTransactionEntity**: Entity tổng hợp chứa tất cả các loại giao dịch

## Indexes

-   **Unique Index**: `BcId + Date` - Đảm bảo không có giao dịch trùng lặp cho cùng một Business Center và ngày
-   **Index**: `BcId` - Tối ưu truy vấn theo Business Center
-   **Index**: `Date` - Tối ưu truy vấn theo ngày giao dịch
-   **Index**: `Amount` - Tối ưu truy vấn theo số tiền giao dịch
-   **Index**: `FundsType` - Tối ưu truy vấn theo loại quỹ
-   **Index**: `Currency` - Tối ưu truy vấn theo tiền tệ

## Constructors

### Constructor mặc định

```csharp
public RawRecordTransactionBcEntity()
```

### Constructor với ID

```csharp
public RawRecordTransactionBcEntity(Guid id)
```

**Tham số:**

-   `id` (Guid): ID của entity

## So sánh với RawRecordTransactionAdAccountEntity

| Đặc điểm               | RawRecordTransactionBcEntity | RawRecordTransactionAdAccountEntity |
| ---------------------- | ---------------------------- | ----------------------------------- |
| **Phạm vi**            | Business Center level        | Ad Account level                    |
| **Khóa chính**         | Composite key (BcId + Date)  | Composite key (AdvertiserId + Date) |
| **Thông tin chi tiết** | Tổng hợp theo ngày           | Chi tiết theo từng tài khoản        |
| **Sử dụng**            | Báo cáo tổng hợp BC          | Báo cáo chi tiết Ad Account         |
| **Hóa đơn**            | Có thông tin hóa đơn         | Không có thông tin hóa đơn          |

## Lưu ý

-   Entity này kế thừa từ AuditedEntity để có đầy đủ thông tin audit
-   Sử dụng BcId + Date làm khóa chính kết hợp để đảm bảo tính duy nhất theo Business Center và ngày
-   Amount được cấu hình với precision 18,2 để đảm bảo độ chính xác tài chính
-   Date được lưu trữ theo múi giờ UTC để đảm bảo tính nhất quán
-   Timezone lưu trữ múi giờ gốc để có thể convert ngược lại khi cần thiết
-   BcId bắt buộc để xác định chính xác Business Center nào sở hữu giao dịch
-   Entity này cung cấp cái nhìn tổng hợp về giao dịch tài chính ở cấp độ Business Center
-   Bao gồm thông tin hóa đơn để hỗ trợ kiểm toán và đối soát
-   Các trường hóa đơn có thể null để đảm bảo tính linh hoạt
