/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok One (TTO) Business API operations
    /// </summary>
    public class TikTokOneApi : ITikTokOneApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<TikTokOneApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the TikTokOneApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public TikTokOneApi(IApiClient apiClient, ILogger<TikTokOneApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get Creator access token
        /// </summary>
        /// <param name="request">Request body containing client credentials and authorization code</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing access token and related information</returns>
        // public async Task<Response<CreatorAccessTokenResponse>> GetCreatorAccessTokenAsync(
        //     CreatorAccessTokenRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogInformation("Getting Creator access token");

        //     var endpoint = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TikTokOneEndpoints.GetCreatorAccessToken}";
            
        //     return await _apiClient.CallApiAsync<Response<CreatorAccessTokenResponse>>(
        //         endpoint,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Renew Creator access token
        /// </summary>
        /// <param name="request">Request body containing client credentials and refresh token</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing renewed access token and related information</returns>
        // public async Task<Response<CreatorAccessTokenResponse>> RenewCreatorAccessTokenAsync(
        //     CreatorAccessTokenRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogInformation("Renewing Creator access token");

        //     var endpoint = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TikTokOneEndpoints.RenewCreatorAccessToken}";
            
        //     return await _apiClient.CallApiAsync<Response<CreatorAccessTokenResponse>>(
        //         endpoint,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Revoke Creator access token
        /// </summary>
        /// <param name="request">Request body containing client credentials and access token to revoke</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response indicating success or failure</returns>
        // public async Task<Response<object>> RevokeCreatorAccessTokenAsync(
        //     RevokeCreatorAccessTokenRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogInformation("Revoking Creator access token");

        //     var endpoint = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TikTokOneEndpoints.RevokeCreatorAccessToken}";
            
        //     return await _apiClient.CallApiAsync<Response<object>>(
        //         endpoint,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <summary>
        // /// Get authorized Creator permissions
        // /// </summary>
        // /// <param name="request">Request body containing app ID and access token</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response containing Creator token information</returns>
        // public async Task<Response<CreatorTokenInfoResponse>> GetCreatorTokenInfoAsync(
        //     CreatorTokenInfoRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     _logger?.LogInformation("Getting Creator token info");

        //     var endpoint = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TikTokOneEndpoints.GetCreatorTokenInfo}";
            
        //     return await _apiClient.CallApiAsync<Response<CreatorTokenInfoResponse>>(
        //         endpoint,
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get authorized TTO Creator Marketplace accounts
        /// </summary>
        /// <param name="accessToken">Access token authorized by a TikTok One Creator Marketplace account</param>
        /// <param name="appId">ID of your developer application</param>
        /// <param name="secret">Secret of your developer application</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TTO Creator Marketplace account IDs</returns>
        public async Task<Response<TtoCreatorMarketplaceAccountsResponse>> GetTtoCreatorMarketplaceAccountsAsync(
            string accessToken,
            string appId,
            string secret,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(appId))
                throw new ArgumentException("App ID cannot be null or empty", nameof(appId));
            if (string.IsNullOrEmpty(secret))
                throw new ArgumentException("Secret cannot be null or empty", nameof(secret));

            _logger?.LogInformation("Getting TTO Creator Marketplace accounts");

            var endpoint = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TikTokOneEndpoints.GetTtoCreatorMarketplaceAccounts}";
            var queryParams = new Dictionary<string, string>
            {
                ["app_id"] = appId,
                ["secret"] = secret
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<TtoCreatorMarketplaceAccountsResponse>>(
                endpoint,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the details of a TTO Creator Marketplace account
        /// </summary>
        /// <param name="accessToken">Access token authorized by a TTO Creator Marketplace account</param>
        /// <param name="ttoTcmAccountId">The ID of the TTO Creator Marketplace account</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TTO account information</returns>
        public async Task<Response<TtoAccountInfoResponse>> GetTtoAccountInfoAsync(
            string accessToken,
            string ttoTcmAccountId,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(ttoTcmAccountId))
                throw new ArgumentException("TTO TCM Account ID cannot be null or empty", nameof(ttoTcmAccountId));

            _logger?.LogInformation("Getting TTO account info for account: {AccountId}", ttoTcmAccountId);

            var endpoint = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TikTokOneEndpoints.GetTtoAccountInfo}";
            var queryParams = new Dictionary<string, string>
            {
                ["tto_tcm_account_id"] = ttoTcmAccountId
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<TtoAccountInfoResponse>>(
                endpoint,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get information and insights about a TikTok creator who has joined TikTok One (TTO)
        /// </summary>
        /// <param name="accessToken">Access token authorized by TikTok creators</param>
        /// <param name="creatorId">Application specific unique ID of the TikTok account</param>
        /// <param name="fields">Requested fields</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing authorized TTO Creator insights</returns>
        public async Task<Response<AuthorizedTtoCreatorInsightsResponse>> GetAuthorizedTtoCreatorInsightsAsync(
            string accessToken,
            string creatorId,
            List<string>? fields = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(creatorId))
                throw new ArgumentException("Creator ID cannot be null or empty", nameof(creatorId));

            _logger?.LogInformation("Getting authorized TTO Creator insights for creator: {CreatorId}", creatorId);

            var endpoint = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TikTokOneEndpoints.GetAuthorizedTtoCreatorInsights}";
            var queryParams = new Dictionary<string, string>
            {
                ["creator_id"] = creatorId
            };

            if (fields != null && fields.Count > 0)
            {
                queryParams["fields"] = string.Join(",", fields);
            }

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<AuthorizedTtoCreatorInsightsResponse>>(
                endpoint,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the reporting insights of videos of a TikTok creator who has joined TikTok One (TTO)
        /// </summary>
        /// <param name="accessToken">Access token authorized by TikTok creators</param>
        /// <param name="creatorId">Application specific unique ID of the TikTok account</param>
        /// <param name="videoIds">The IDs of the videos to retrieve information for</param>
        /// <param name="limit">The number of videos that you want to retrieve per request</param>
        /// <param name="cursor">Timestamp cursor</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing authorized TTO Media insights</returns>
        public async Task<Response<AuthorizedTtoMediaInsightsResponse>> GetAuthorizedTtoMediaInsightsAsync(
            string accessToken,
            string creatorId,
            List<string>? videoIds = null,
            int? limit = null,
            long? cursor = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(creatorId))
                throw new ArgumentException("Creator ID cannot be null or empty", nameof(creatorId));

            _logger?.LogInformation("Getting authorized TTO Media insights for creator: {CreatorId}", creatorId);

            var endpoint = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TikTokOneEndpoints.GetAuthorizedTtoMediaInsights}";
            var queryParams = new Dictionary<string, string>
            {
                ["creator_id"] = creatorId
            };

            if (videoIds != null && videoIds.Count > 0)
            {
                queryParams["video_ids"] = string.Join(",", videoIds);
            }

            if (limit.HasValue)
            {
                queryParams["limit"] = limit.Value.ToString();
            }

            if (cursor.HasValue)
            {
                queryParams["cursor"] = cursor.Value.ToString();
            }

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<AuthorizedTtoMediaInsightsResponse>>(
                endpoint,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Get the basic profile information of a creator on TikTok One (TTO) Creator Marketplace
        /// </summary>
        /// <param name="accessToken">Access token authorized by a TTO Creator Marketplace account</param>
        /// <param name="ttoTcmAccountId">The ID of the TTO Creator Marketplace account</param>
        /// <param name="handleName">The handle name of the TTO Creator Marketplace creator</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TTO Public Account insights</returns>
        public async Task<Response<TtoPublicAccountInsightsResponse>> GetTtoPublicAccountInsightsAsync(
            string accessToken,
            string ttoTcmAccountId,
            string handleName,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(ttoTcmAccountId))
                throw new ArgumentException("TTO TCM Account ID cannot be null or empty", nameof(ttoTcmAccountId));
            if (string.IsNullOrEmpty(handleName))
                throw new ArgumentException("Handle name cannot be null or empty", nameof(handleName));

            _logger?.LogInformation("Getting TTO Public Account insights for handle: {HandleName}", handleName);

            var endpoint = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TikTokOneEndpoints.GetTtoPublicAccountInsights}";
            var queryParams = new Dictionary<string, string>
            {
                ["tto_tcm_account_id"] = ttoTcmAccountId,
                ["handle_name"] = handleName
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<TtoPublicAccountInsightsResponse>>(
                endpoint,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Retrieve the creator industry or content tag labels associated with TikTok One (TTO) creators
        /// </summary>
        /// <param name="accessToken">Access token authorized by a TikTok One Creator Marketplace account</param>
        /// <param name="ttoTcmAccountId">The ID of the TikTok One Creator Marketplace account</param>
        /// <param name="labelType">The type of label (RANKING or SEARCH)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TTO creator labels</returns>
        public async Task<Response<TtoCreatorLabelsResponse>> GetTtoCreatorLabelsAsync(
            string accessToken,
            string ttoTcmAccountId,
            string labelType,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(ttoTcmAccountId))
                throw new ArgumentException("TTO TCM Account ID cannot be null or empty", nameof(ttoTcmAccountId));
            if (string.IsNullOrEmpty(labelType))
                throw new ArgumentException("Label type cannot be null or empty", nameof(labelType));

            _logger?.LogInformation("Getting TTO creator labels for account: {AccountId}, type: {LabelType}", ttoTcmAccountId, labelType);

            var endpoint = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TikTokOneEndpoints.GetTtoCreatorLabels}";
            var queryParams = new Dictionary<string, string>
            {
                ["tto_tcm_account_id"] = ttoTcmAccountId,
                ["label_type"] = labelType
            };

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<TtoCreatorLabelsResponse>>(
                endpoint,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Obtain a list of up to 100 US creators who rank highest for a ranking label on TikTok One (TTO) Creator Leaderboard
        /// </summary>
        /// <param name="accessToken">Access token authorized by a TikTok One Creator Marketplace account</param>
        /// <param name="ttoTcmAccountId">The ID of the TikTok One Creator Marketplace account</param>
        /// <param name="rankingType">The type of ranking (BRANDED_CONTENT or ORGANIC_CONTENT)</param>
        /// <param name="timePeriod">The time period for the ranking (WEEK or MONTH)</param>
        /// <param name="timePeriodLookback">The specific lookback period for the ranking (ONE, TWO, or THREE)</param>
        /// <param name="labelId">Creator label ID</param>
        /// <param name="countryCode">The code of the country or region that the creator ranking is for</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing TTO creator rankings</returns>
        public async Task<Response<TtoCreatorRankingsResponse>> GetTtoCreatorRankingsAsync(
            string accessToken,
            string ttoTcmAccountId,
            string rankingType,
            string timePeriod,
            string timePeriodLookback,
            string labelId,
            string countryCode,
            int? page = null,
            int? pageSize = null,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(accessToken))
                throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
            if (string.IsNullOrEmpty(ttoTcmAccountId))
                throw new ArgumentException("TTO TCM Account ID cannot be null or empty", nameof(ttoTcmAccountId));
            if (string.IsNullOrEmpty(rankingType))
                throw new ArgumentException("Ranking type cannot be null or empty", nameof(rankingType));
            if (string.IsNullOrEmpty(timePeriod))
                throw new ArgumentException("Time period cannot be null or empty", nameof(timePeriod));
            if (string.IsNullOrEmpty(timePeriodLookback))
                throw new ArgumentException("Time period lookback cannot be null or empty", nameof(timePeriodLookback));
            if (string.IsNullOrEmpty(labelId))
                throw new ArgumentException("Label ID cannot be null or empty", nameof(labelId));
            if (string.IsNullOrEmpty(countryCode))
                throw new ArgumentException("Country code cannot be null or empty", nameof(countryCode));

            _logger?.LogInformation("Getting TTO creator rankings for account: {AccountId}, ranking type: {RankingType}", ttoTcmAccountId, rankingType);

            var endpoint = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TikTokOneEndpoints.GetTtoCreatorRankings}";
            var queryParams = new Dictionary<string, string>
            {
                ["tto_tcm_account_id"] = ttoTcmAccountId,
                ["ranking_type"] = rankingType,
                ["time_period"] = timePeriod,
                ["time_period_lookback"] = timePeriodLookback,
                ["label_id"] = labelId,
                ["country_code"] = countryCode
            };

            if (page.HasValue)
            {
                queryParams["page"] = page.Value.ToString();
            }

            if (pageSize.HasValue)
            {
                queryParams["page_size"] = pageSize.Value.ToString();
            }

            var headers = new Dictionary<string, string>
            {
                ["Access-Token"] = accessToken
            };

            return await _apiClient.CallApiAsync<Response<TtoCreatorRankingsResponse>>(
                endpoint,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: headers,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        /// <summary>
        /// Search for TikTok One (TTO) creators
        /// </summary>
        /// <param name="accessToken">Access token authorized by a TikTok One Creator Marketplace account</param>
        /// <param name="ttoTcmAccountId">The ID of the TikTok One Creator Marketplace account</param>
        /// <param name="countryCodes">Filter by the country or region codes of the creators</param>
        /// <param name="stateProvinces">Filter by the states or provinces of the creators</param>
        /// <param name="contentLabelIds">Filter by the IDs of video content tags</param>
        /// <param name="industryLabelIds">Filter by the IDs of industry tags</param>
        /// <param name="minFollowers">Filter by the minimum number of followers</param>
        /// <param name="maxFollowers">Filter by the maximum number of followers</param>
        /// <param name="languages">Filter by language</param>
        /// <param name="minCreatorPrice">Filter by the minimum creator starting price</param>
        /// <param name="maxCreatorPrice">Filter by the maximum creator starting price</param>
        /// <param name="creatorPriceCurrency">The currency for creator starting price filters</param>
        /// <param name="minAvgViews">Filter by the minimum average views</param>
        /// <param name="maxAvgViews">Filter by the maximum average views</param>
        /// <param name="minMedianViews">Filter by the minimum median views</param>
        /// <param name="maxMedianViews">Filter by the maximum median views</param>
        /// <param name="minEngagementRate">Filter by the minimum engagement rate</param>
        /// <param name="maxEngagementRate">Filter by the maximum engagement rate</param>
        /// <param name="followerCountryCode">Filter by the code of followers' location</param>
        /// <param name="followerGenderRatio">Filter by followers' gender ratio</param>
        /// <param name="followerAge">Filter by followers' majority age group</param>
        /// <param name="keywordSearch">The keyword to search for creators</param>
        /// <param name="sortField">The specific metric by which to sort the results</param>
        /// <param name="sortOrder">The order in which to sort the results</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Current page size</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing discovered TTO creators</returns>
        //public async Task<Response<DiscoverTtoCreatorsResponse>> DiscoverTtoCreatorsAsync(
        //    string accessToken,
        //    string ttoTcmAccountId,
        //    List<string> countryCodes,
        //    List<string>? stateProvinces = null,
        //    List<string>? contentLabelIds = null,
        //    List<string>? industryLabelIds = null,
        //    int? minFollowers = null,
        //    int? maxFollowers = null,
        //    List<string>? languages = null,
        //    decimal? minCreatorPrice = null,
        //    decimal? maxCreatorPrice = null,
        //    string? creatorPriceCurrency = null,
        //    int? minAvgViews = null,
        //    int? maxAvgViews = null,
        //    int? minMedianViews = null,
        //    int? maxMedianViews = null,
        //    decimal? minEngagementRate = null,
        //    decimal? maxEngagementRate = null,
        //    string? followerCountryCode = null,
        //    string? followerGenderRatio = null,
        //    string? followerAge = null,
        //    string? keywordSearch = null,
        //    string? sortField = null,
        //    string? sortOrder = null,
        //    int? page = null,
        //    int? pageSize = null,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (string.IsNullOrEmpty(accessToken))
        //        throw new ArgumentException("Access token cannot be null or empty", nameof(accessToken));
        //    if (string.IsNullOrEmpty(ttoTcmAccountId))
        //        throw new ArgumentException("TTO TCM Account ID cannot be null or empty", nameof(ttoTcmAccountId));
        //    if (countryCodes == null || countryCodes.Count == 0)
        //        throw new ArgumentException("Country codes cannot be null or empty", nameof(countryCodes));

        //    _logger?.LogInformation("Discovering TTO creators for account: {AccountId}", ttoTcmAccountId);

        //    var endpoint = $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{TikTokOneEndpoints.DiscoverTtoCreators}";
        //    var queryParams = new Dictionary<string, string>
        //    {
        //        ["tto_tcm_account_id"] = ttoTcmAccountId,
        //        ["country_codes"] = string.Join(",", countryCodes)
        //    };

        //    // Add optional parameters
        //    if (stateProvinces != null && stateProvinces.Count > 0)
        //        queryParams["state_provinces"] = string.Join(",", stateProvinces);
        //    if (contentLabelIds != null && contentLabelIds.Count > 0)
        //        queryParams["content_label_ids"] = string.Join(",", contentLabelIds);
        //    if (industryLabelIds != null && industryLabelIds.Count > 0)
        //        queryParams["industry_label_ids"] = string.Join(",", industryLabelIds);
        //    if (minFollowers.HasValue)
        //        queryParams["min_followers"] = minFollowers.Value.ToString();
        //    if (maxFollowers.HasValue)
        //        queryParams["max_followers"] = maxFollowers.Value.ToString();
        //    if (languages != null && languages.Count > 0)
        //        queryParams["languages"] = string.Join(",", languages);
        //    if (minCreatorPrice.HasValue)
        //        queryParams["min_creator_price"] = minCreatorPrice.Value.ToString();
        //    if (maxCreatorPrice.HasValue)
        //        queryParams["max_creator_price"] = maxCreatorPrice.Value.ToString();
        //    if (!string.IsNullOrEmpty(creatorPriceCurrency))
        //        queryParams["creator_price_currency"] = creatorPriceCurrency;
        //    if (minAvgViews.HasValue)
        //        queryParams["min_avg_views"] = minAvgViews.Value.ToString();
        //    if (maxAvgViews.HasValue)
        //        queryParams["max_avg_views"] = maxAvgViews.Value.ToString();
        //    if (minMedianViews.HasValue)
        //        queryParams["min_median_views"] = minMedianViews.Value.ToString();
        //    if (maxMedianViews.HasValue)
        //        queryParams["max_median_views"] = maxMedianViews.Value.ToString();
        //    if (minEngagementRate.HasValue)
        //        queryParams["min_engagement_rate"] = minEngagementRate.Value.ToString();
        //    if (maxEngagementRate.HasValue)
        //        queryParams["max_engagement_rate"] = maxEngagementRate.Value.ToString();
        //    if (!string.IsNullOrEmpty(followerCountryCode))
        //        queryParams["follower_country_code"] = followerCountryCode;
        //    if (!string.IsNullOrEmpty(followerGenderRatio))
        //        queryParams["follower_gender_ratio"] = followerGenderRatio;
        //    if (!string.IsNullOrEmpty(followerAge))
        //        queryParams["follower_age"] = followerAge;
        //    if (!string.IsNullOrEmpty(keywordSearch))
        //        queryParams["keyword_search"] = keywordSearch;
        //    if (!string.IsNullOrEmpty(sortField))
        //        queryParams["sort_field"] = sortField;
        //    if (!string.IsNullOrEmpty(sortOrder))
        //        queryParams["sort_order"] = sortOrder;
        //    if (page.HasValue)
        //        queryParams["page"] = page.Value.ToString();
        //    if (pageSize.HasValue)
        //        queryParams["page_size"] = pageSize.Value.ToString();

        //    var headers = new Dictionary<string, string>
        //    {
        //        ["Access-Token"] = accessToken
        //    };

        //    return await _apiClient.CallApiAsync<Response<DiscoverTtoCreatorsResponse>>(
        //        endpoint,
        //        HttpMethod.Get,
        //        queryParams: queryParams,
        //        body: null,
        //        headerParams: headers,
        //        authNames: null,
        //        cancellationToken: cancellationToken);
        //}
    }
}
