/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API Identity operations
    /// </summary>
    public class IdentityApi : IIdentityApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<IdentityApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the IdentityApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public IdentityApi(IApiClient apiClient, ILogger<IdentityApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Create a Custom User (CUSTOMIZED_USER) identity
        /// </summary>
        /// <param name="request">Identity creation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the created identity ID</returns>
        //public async Task<IdentityCreateResponse> CreateIdentityAsync(
        //    IdentityCreateRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogDebug("Creating identity for advertiser {AdvertiserId}", request.AdvertiserId);

        //    var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{IdentityEndpoints.CreateIdentity}";

        //    return await _apiClient.CallApiAsync<IdentityCreateResponse>(
        //        path,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken);
        //}

        /// <summary>
        /// Delete a customized user identity (identity_type = CUSTOMIZED_USER)
        /// </summary>
        /// <param name="request">Identity deletion request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the asynchronous operation</returns>
        //public async Task DeleteIdentityAsync(
        //    IdentityDeleteRequest request,
        //    CancellationToken cancellationToken = default)
        //{
        //    if (request == null)
        //        throw new ArgumentNullException(nameof(request));

        //    _logger?.LogDebug("Deleting identity {IdentityId} for advertiser {AdvertiserId}", 
        //        request.IdentityId, request.AdvertiserId);

        //    var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{IdentityEndpoints.DeleteIdentity}";

        //    await _apiClient.CallApiAsync<object>(
        //        path,
        //        HttpMethod.Post,
        //        queryParams: null,
        //        body: request,
        //        headerParams: null,
        //        authNames: null,
        //        cancellationToken);
        //}

        /// <summary>
        /// Get a list of identities under an ad account
        /// </summary>
        /// <param name="request">Identity list request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the list of identities and pagination information</returns>
        public async Task<IdentityGetResponse> GetIdentityListAsync(
            IdentityGetRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting identity list for advertiser {AdvertiserId}", request.AdvertiserId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{IdentityEndpoints.GetIdentityList}";

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId
            };

            if (!string.IsNullOrEmpty(request.IdentityType))
                queryParams["identity_type"] = request.IdentityType;

            if (!string.IsNullOrEmpty(request.IdentityAuthorizedBcId))
                queryParams["identity_authorized_bc_id"] = request.IdentityAuthorizedBcId;

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();

            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();

            if (request.Filtering != null && !string.IsNullOrEmpty(request.Filtering.Keyword))
                queryParams["filtering"] = System.Text.Json.JsonSerializer.Serialize(request.Filtering);

            return await _apiClient.CallApiAsync<IdentityGetResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }

        /// <summary>
        /// Retrieve details about an identity
        /// </summary>
        /// <param name="request">Identity info request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing detailed identity information</returns>
        public async Task<IdentityInfoResponse> GetIdentityInfoAsync(
            IdentityInfoRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting identity info for identity {IdentityId} and advertiser {AdvertiserId}", 
                request.IdentityId, request.AdvertiserId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{IdentityEndpoints.GetIdentityInfo}";

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["identity_id"] = request.IdentityId,
                ["identity_type"] = request.IdentityType
            };

            if (!string.IsNullOrEmpty(request.IdentityAuthorizedBcId))
                queryParams["identity_authorized_bc_id"] = request.IdentityAuthorizedBcId;

            return await _apiClient.CallApiAsync<IdentityInfoResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }

        /// <summary>
        /// Get all posts under an identity
        /// </summary>
        /// <param name="request">Identity videos request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the list of posts under the identity</returns>
        public async Task<IdentityVideosResponse> GetIdentityVideosAsync(
            IdentityVideosRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting identity videos for identity {IdentityId} and advertiser {AdvertiserId}", 
                request.IdentityId, request.AdvertiserId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{IdentityEndpoints.GetIdentityVideos}";

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["identity_id"] = request.IdentityId,
                ["identity_type"] = request.IdentityType
            };

            if (!string.IsNullOrEmpty(request.IdentityAuthorizedBcId))
                queryParams["identity_authorized_bc_id"] = request.IdentityAuthorizedBcId;

            if (!string.IsNullOrEmpty(request.ItemType))
                queryParams["item_type"] = request.ItemType;

            if (!string.IsNullOrEmpty(request.Keyword))
                queryParams["keyword"] = request.Keyword;

            if (!string.IsNullOrEmpty(request.Cursor))
                queryParams["cursor"] = request.Cursor;

            if (request.Count.HasValue)
                queryParams["count"] = request.Count.Value.ToString();

            return await _apiClient.CallApiAsync<IdentityVideosResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }

        /// <summary>
        /// Get live videos under an identity
        /// </summary>
        /// <param name="request">Identity live videos request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the list of live videos under the identity</returns>
        public async Task<IdentityLiveVideosResponse> GetIdentityLiveVideosAsync(
            IdentityLiveVideosRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting identity live videos for identity {IdentityId} and advertiser {AdvertiserId}",
                request.IdentityId, request.AdvertiserId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{IdentityEndpoints.GetIdentityLiveVideos}";

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["identity_id"] = request.IdentityId,
                ["identity_type"] = request.IdentityType
            };

            if (!string.IsNullOrEmpty(request.IdentityAuthorizedBcId))
                queryParams["identity_authorized_bc_id"] = request.IdentityAuthorizedBcId;

            if (request.Cursor.HasValue)
                queryParams["cursor"] = request.Cursor.Value.ToString();

            return await _apiClient.CallApiAsync<IdentityLiveVideosResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }

        /// <summary>
        /// Get music authorization information for videos under an identity
        /// </summary>
        /// <param name="request">Music authorization request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing music authorization information</returns>
        public async Task<MusicAuthorizationResponse> GetMusicAuthorizationAsync(
            MusicAuthorizationRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting music authorization for item {ItemId} and advertiser {AdvertiserId}",
                request.ItemId, request.AdvertiserId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{IdentityEndpoints.GetMusicAuthorization}";

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["item_id"] = request.ItemId,
                ["identity_id"] = request.IdentityId,
                ["identity_type"] = request.IdentityType,
                ["locations"] = System.Text.Json.JsonSerializer.Serialize(request.Locations),
                ["start_time"] = request.StartTime,
                ["end_time"] = request.EndTime
            };

            if (!string.IsNullOrEmpty(request.IdentityAuthorizedBcId))
                queryParams["identity_authorized_bc_id"] = request.IdentityAuthorizedBcId;

            return await _apiClient.CallApiAsync<MusicAuthorizationResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }

        /// <summary>
        /// Get the information about one or more TikTok posts that you published using the
        /// AUTH_CODE, TT_USER or BC_AUTH_TT identity
        /// </summary>
        /// <param name="request">Video info request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing detailed information about TikTok posts</returns>
        public async Task<VideoInfoResponse> GetVideoInfoAsync(
            VideoInfoRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            _logger?.LogDebug("Getting video info for identity {IdentityId} and advertiser {AdvertiserId}",
                request.IdentityId, request.AdvertiserId);

            var path = $"/{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{IdentityEndpoints.GetVideoInfo}";

            var queryParams = new Dictionary<string, string>
            {
                ["advertiser_id"] = request.AdvertiserId,
                ["identity_type"] = request.IdentityType,
                ["identity_id"] = request.IdentityId
            };

            if (!string.IsNullOrEmpty(request.IdentityAuthorizedBcId))
                queryParams["identity_authorized_bc_id"] = request.IdentityAuthorizedBcId;

            if (!string.IsNullOrEmpty(request.ItemId))
                queryParams["item_id"] = request.ItemId;

            if (request.ItemIds != null && request.ItemIds.Count > 0)
                queryParams["item_ids"] = System.Text.Json.JsonSerializer.Serialize(request.ItemIds);

            if (!string.IsNullOrEmpty(request.ItemType))
                queryParams["item_type"] = request.ItemType;

            return await _apiClient.CallApiAsync<VideoInfoResponse>(
                path,
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken);
        }
    }
}
