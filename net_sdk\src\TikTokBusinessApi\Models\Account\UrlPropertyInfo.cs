/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Information about a URL property
    /// </summary>
    public class UrlPropertyInfo
    {
        /// <summary>
        /// Type of the URL property
        /// Enum values: 1 (Domain), 2 (URL prefix)
        /// </summary>
        [JsonPropertyName("property_type")]
        public int PropertyType { get; set; }

        /// <summary>
        /// An owned URL
        /// </summary>
        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// Verification status of the URL property
        /// Enum values: 0 (Pending for verification), 1 (Verified), 2 (Not verified - failed)
        /// </summary>
        [JsonPropertyName("property_status")]
        public int PropertyStatus { get; set; }

        /// <summary>
        /// Signature string to be used in the ownership verification process
        /// </summary>
        [JsonPropertyName("signature")]
        public string Signature { get; set; } = string.Empty;

        /// <summary>
        /// Signature file name to be used in the ownership verification process
        /// </summary>
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;
    }
}
