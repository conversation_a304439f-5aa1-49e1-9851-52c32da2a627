using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTok.Entities;
using TikTok.Repositories;
using TikTok.TikTokApiClients;
using TikTokBusinessApi;
using TikTokBusinessApi.Models;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    /// <summary>
    /// Service implementation cho việc đồng bộ dữ liệu báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign 
    /// </summary>
    public class GmvMaxProductDetailProductSyncService : BaseSyncService, IGmvMaxProductDetailProductSyncService
    {
        private readonly IRawGmvMaxProductDetailProductReportRepository _gmvMaxProductDetailProductReportRepository;
        private readonly IAdAccountRepository _adAccountRepository;
        private readonly IRawGmvMaxCampaignsRepository _gmvMaxCampaignsRepository;
        private readonly ITikTokApiClientService _tikTokApiClientService;

        public GmvMaxProductDetailProductSyncService(
            IServiceProvider serviceProvider,
            IRawGmvMaxProductDetailProductReportRepository gmvMaxProductDetailProductReportRepository,
            ILogger<GmvMaxProductDetailProductSyncService> logger,
            IAdAccountRepository adAccountRepository,
            IRawGmvMaxCampaignsRepository gmvMaxCampaignsRepository,
            ITikTokApiClientService tikTokApiClientService) : base(serviceProvider, logger)
        {
            _gmvMaxProductDetailProductReportRepository = gmvMaxProductDetailProductReportRepository;
            _adAccountRepository = adAccountRepository;
            _gmvMaxCampaignsRepository = gmvMaxCampaignsRepository;
            _tikTokApiClientService = tikTokApiClientService;
        }

        /// <summary>
        /// Đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxProductDetailProductSyncResult> SyncAllGmvMaxProductDetailProductForAllBcsAsync()
        {
            var result = new GmvMaxProductDetailProductSyncResult
            {
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho tất cả BC");

                var allBcs = await _businessApplicationCache.GetAllActiveAsync();
                var bcIds = allBcs.Select(x => x.BcId).Distinct().ToList();

                var totalResult = new GmvMaxProductDetailProductSyncResult
                {
                };

                foreach (var bcId in bcIds)
                {
                    var bcResult = await SyncGmvMaxProductDetailProductAsync(bcId);
                    totalResult.TotalSynced += bcResult.TotalSynced;
                    totalResult.NewRecords += bcResult.NewRecords;
                    totalResult.UpdatedRecords += bcResult.UpdatedRecords;
                    totalResult.DayCount += bcResult.DayCount;
                    totalResult.CampaignCount += bcResult.CampaignCount;
                    totalResult.StoreCount += bcResult.StoreCount;
                    totalResult.ProductCount += bcResult.ProductCount;
                    totalResult.BcCount++;
                }

                result = totalResult;

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho tất cả BC. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, BC: {BcCount}, Campaign: {CampaignCount}, Store: {StoreCount}, Product: {ProductCount}, Ngày: {DayCount}",
                    result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.BcCount, result.CampaignCount, result.StoreCount, result.ProductCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho tất cả BC");
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho tất cả BC");
            }

            return result;
        }

        /// <summary>
        /// Đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign theo BC ID và khoảng thời gian
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        public async Task<GmvMaxProductDetailProductSyncResult> SyncGmvMaxProductDetailProductAsync(string bcId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var result = new GmvMaxProductDetailProductSyncResult
            {
                BcCount = 1
            };

            try
            {
                _logger.LogDebug("Bắt đầu đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho BC: {BcId}", bcId);

                // Tạo TikTok client từ Singleton
                using var tikTokClient = await _tikTokApiClientService.GetOrCreateClientAsync(bcId);

                string bcTimezone = DateTimeService.UTC_TIMEZONE;

                var bc = await _businessCenterCache.GetByBcIdAsync(bcId);
                if (bc != null && !string.IsNullOrEmpty(bc.Timezone))
                {
                    bcTimezone = bc.Timezone;
                }

                var advertiserIds = await _adAccountRepository.GetByBcIdAndStatusAsync(bcId, Enums.AdAccountStatus.STATUS_ENABLE);

                // Duyệt từng nhà quảng cáo
                foreach (var advertiserId in advertiserIds)
                {
                    (DateTime startDate, DateTime endDate) rangeDateFiltering;
                    if (startDate.HasValue && endDate.HasValue)
                    {
                        if(startDate.Value > endDate.Value)
                        {
                            throw new UserFriendlyException("Ngày bắt đầu phải nhỏ hơn ngày kết thúc");
                        }
                        rangeDateFiltering = (startDate.Value, endDate.Value);
                    }
                    else
                    {
                        rangeDateFiltering = await GetRangeDateFiltering(bcId, advertiserId, bcTimezone);
                    }

                    var currentDate = rangeDateFiltering.startDate.Date;
                    var end = rangeDateFiltering.endDate.Date;

                    while (currentDate <= end)
                    {
                        var pageEnd = currentDate.AddDays(30);
                        var daysDifference = (end - currentDate).TotalDays;
                        if (daysDifference < 30)
                        {
                            pageEnd = end;
                        }
                        _logger.LogDebug("Đồng bộ dữ liệu báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho Advertiser: {AdvertiserId}, Ngày: {Date}", advertiserId, currentDate.ToString("yyyy-MM-dd"));

                        var apiResponse = await GetSyncGmvMaxProductDetailProductFromApiAsync(tikTokClient, bcId, advertiserId, currentDate, pageEnd);
                        if (apiResponse != null && apiResponse.Any())
                        {
                            await ProcessReportDataAsync(bcId, advertiserId, bcTimezone, apiResponse, result);
                        }
                        else
                        {
                            _logger.LogDebug("Không có dữ liệu báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho Advertiser: {AdvertiserId}, Ngày: {Date}", advertiserId, currentDate.ToString("yyyy-MM-dd"));
                        }
                        if (pageEnd == end)
                        {
                            break;
                        }
                        currentDate = currentDate.AddDays(29);
                    }
                }

                _logger.LogDebug("Hoàn thành đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho BC: {BcId}. Tổng: {Total}, Mới: {New}, Cập nhật: {Updated}, Campaign: {CampaignCount}, Store: {StoreCount}, Product: {ProductCount}, Ngày: {DayCount}",
                    bcId, result.TotalSynced, result.NewRecords, result.UpdatedRecords, result.CampaignCount, result.StoreCount, result.ProductCount, result.DayCount);
            }
            catch (BusinessException ex)
            {
                result.Code = ex.Code ?? string.Empty;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho BC: {BcId}", bcId);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Lỗi khi đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign: {ex.Message}";
                _logger.LogError(ex, "Lỗi khi đồng bộ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho BC: {BcId}", bcId);
            }

            return result;
        }

        private async Task<(DateTime startDate, DateTime endDate)> GetRangeDateFiltering(string bcId, string advertiserId, string timezone)
        {
            // Lấy ngày hiện tại theo timezone của BC
            DateTime currentDateInTimezone = _dateTimeService.GetDateNow(timezone);
            DateTime endDate = currentDateInTimezone;

            // Lấy dữ liệu báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign mới nhất (theo giờ)
            //var latestReport = await GetLatestReportByBcIdAsync(bcId, advertiserId);

            DateTime startDate= currentDateInTimezone;
            //if (latestReport == null)
            //{
            //    // Nếu chưa có dữ liệu trong DB thì lấy khoảng 1 tuần từ ngày hiện tại
            //    startDate = currentDateInTimezone.AddDays(-LAST_SYNC_DAYS);
            //    _logger.LogDebug("Chưa có dữ liệu báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign trong DB cho BC: {BcId}, Advertiser: {AdvertiserId}. Lấy dữ liệu 1 tuần từ {StartDate} đến {EndDate}",
            //        bcId, advertiserId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            //}
            //else
            //{
            //    // Nếu có dữ liệu trong DB thì lấy từ ngày trong DB đến ngày hiện tại
            //    // Convert từ UTC (trong DB) sang timezone của BC để so sánh
            //    var latestReportDateInTimezone = _dateTimeService.ConvertFromUtc(latestReport.Date, timezone).Date;
            //    startDate = latestReportDateInTimezone;
            //    _logger.LogDebug("Có dữ liệu báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign trong DB cho BC: {BcId}, Advertiser: {AdvertiserId}. Lấy từ {StartDate} đến {EndDate}",
            //        bcId, advertiserId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            //}

            return (startDate, endDate);
        }

        /// <summary>
        /// Lấy báo cáo mới nhất theo BC ID và advertiserId
        /// </summary>
        /// <param name="bcId">BC ID</param>
        /// <param name="advertiserId">Advertiser ID</param>
        /// <returns>Báo cáo mới nhất</returns>
        private async Task<RawGmvMaxProductDetailProductReportEntity?> GetLatestReportByBcIdAsync(string bcId, string advertiserId)
        {
            var query = await _gmvMaxProductDetailProductReportRepository.GetLatestByBcIdAndAdvertiserIdAsync(bcId, advertiserId);

            return query;
        }

        /// <summary>
        /// Lấy dữ liệu báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign từ TikTok API
        /// Lưu ý: Khi lấy báo cáo theo giờ, startDate và endDate phải cùng một ngày
        /// </summary>
        private async Task<List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)>> GetSyncGmvMaxProductDetailProductFromApiAsync(TikTokBusinessApiClient tikTokClient, string bcId, string advertiserId, DateTime startDate, DateTime endDate)
        {

            var records = new List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)>();

            // Lấy danh sách StoreId và CampaignId từ GMV Max Campaigns theo AdvertiserId
            var gmvMaxCampaigns = await _gmvMaxCampaignsRepository.GetByAdvertiserIdAndOperationStatusAsync(advertiserId, "ENABLE");
            var campaignStoreMapping = gmvMaxCampaigns
                .Where(x => !string.IsNullOrEmpty(x.StoreId) && !string.IsNullOrEmpty(x.CampaignId))
                .GroupBy(x => new { x.StoreId, x.CampaignId })
                .Select(g => new { g.Key.StoreId, g.Key.CampaignId })
                .ToList();

            if (!campaignStoreMapping.Any())
            {
                _logger.LogDebug("Không có Campaign và Store ID nào cho Advertiser: {AdvertiserId}", advertiserId);
                return records;
            }

            _logger.LogDebug("Tìm thấy {Count} Campaign-Store mapping cho Advertiser: {AdvertiserId}", campaignStoreMapping.Count, advertiserId);

            // Duyệt từng Campaign-Store mapping vì API chỉ hỗ trợ 1 StoreId mỗi lần gọi
            foreach (var mapping in campaignStoreMapping)
            {
                var pageRecords = await GetReportFromApiByAdvertiserStoreAndCampaignAsync(tikTokClient, advertiserId, mapping.StoreId, mapping.CampaignId, startDate, endDate);
                records.AddRange(pageRecords);
            }

            return records;
        }

        /// <summary>
        /// Lấy báo cáo từ API theo advertiser, store và campaign (Campain Report-Product)
        /// </summary>
        private async Task<List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)>> GetReportFromApiByAdvertiserStoreAndCampaignAsync(TikTokBusinessApiClient tikTokClient, string advertiserId, string storeId, string campaignId, DateTime startDate, DateTime endDate)
        {
            var records = new List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)>();
            var page = 1;
            const int pageSize = PAGE_SIZE_SYNC_REPORT;

            while (true)
            {
                var request = new GMVMaxReportRequest
                {
                    AdvertiserId = advertiserId,
                    StoreIds = new List<string> { storeId },
                    StartDate = startDate.ToString("yyyy-MM-dd"),
                    EndDate = endDate.ToString("yyyy-MM-dd"),
                    Dimensions = new List<string> { "item_group_id", "stat_time_day" },
                    Metrics = new List<string>
                    {
                        "currency", "product_name", "item_group_id", "product_image_url",
                        "product_status", "bid_type", "orders", "gross_revenue"
                    },
                    Filtering = new GMVMaxReportFiltering
                    {
                        CampaignIds = new List<string> { campaignId }
                    },
                    Page = page,
                    PageSize = pageSize
                };

                var response = await tikTokClient.GMVMax.GetReportAsync(request);
                if (!TikTokApiCodes.IsSuccess(response.Code))
                {
                    throw new BusinessException(response.Code.ToString(), $"Lỗi khi lấy dữ liệu báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign: {response.Message}");
                }

                if (response?.Data?.List == null || !response.Data.List.Any())
                {
                    break;
                }

                // Thêm AdvertiserId, StoreId và CampaignId vào mỗi item
                foreach (var item in response.Data.List)
                {
                    records.Add((item, advertiserId, storeId, campaignId));
                }

                // Kiểm tra xem còn trang tiếp theo không
                if (response.Data.PageInfo?.TotalPage <= page)
                {
                    break;
                }

                page++;
            }

            return records;
        }

        /// <summary>
        /// Xử lý dữ liệu báo cáo từ API
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="advertiserId">ID nhà quảng cáo</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportDataList">Danh sách dữ liệu báo cáo</param>
        /// <param name="result">Kết quả đồng bộ</param>
        private async Task ProcessReportDataAsync(string bcId, string advertiserId, string bcTimezone, List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)> reportDataList, GmvMaxProductDetailProductSyncResult result)
        {
            // sử dụng UnitOfWork để đảm bảo tính toàn vẹn dữ liệu cho mỗi 200 bản ghi
            if (reportDataList == null || !reportDataList.Any())
            {
                _logger.LogDebug("Không có dữ liệu báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign để xử lý cho {Total} bản ghi", reportDataList?.Count ?? 0);
                return;
            }

            var pageSize = PAGE_SIZE_HANDLE_SAVE_TO_DATABASE;
            var totalPages = (int)Math.Ceiling((double)reportDataList.Count / pageSize);
            _logger.LogDebug("Bắt đầu xử lý {TotalRecords} bản ghi báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho {TotalPages} trang", reportDataList.Count, totalPages);

            for (int page = 0; page < totalPages; page++)
            {
                var pageData = reportDataList.Skip(page * pageSize).Take(pageSize).ToList();
                if (pageData.Any())
                {
                    try
                    {
                        await ProcessPageDataAsync(bcId, bcTimezone, pageData, result);
                    }
                    catch (BusinessException ex)
                    {
                        _logger.LogError(ex, "Lỗi khi xử lý dữ liệu báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign cho {Total} bản ghi, Trang: {Page}", pageData.Count, page + 1);
                        result.ErrorMessage += ex.Message + Environment.NewLine;
                        result.Code = TikTokApiCodes.PartialSuccess.ToString();
                        continue; // Bỏ qua lỗi và tiếp tục với trang tiếp theo
                    }
                }
            }
        }

        private async Task ProcessPageDataAsync(string bcId, string bcTimezone, List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)> pageData, GmvMaxProductDetailProductSyncResult result)
        {
            // use unit of work
            using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: false))
            {
                var mappedEntities = await MapListReportDataToEntitiesAsync(bcId, bcTimezone, pageData);

                var existingEntities = await GetExistingEntitiesAsync(bcId, mappedEntities);

                var insertedEntities = new List<RawGmvMaxProductDetailProductReportEntity>();
                var updatedEntities = new List<RawGmvMaxProductDetailProductReportEntity>();

                foreach (var mappedEntity in mappedEntities)
                {
                    // So sánh theo CampaignId, ItemGroupId và Date với độ chính xác đến giờ
                    var currentEntity = existingEntities.FirstOrDefault(x => x.CampaignId == mappedEntity.CampaignId && x.ItemGroupId == mappedEntity.ItemGroupId && x.Date == mappedEntity.Date);
                    if (currentEntity == null)
                    {
                        insertedEntities.Add(mappedEntity);
                        result.NewRecords++;
                        result.TotalSynced++;
                    }
                    else
                    {
                        // Cập nhật nếu có thay đổi
                        if (currentEntity.HasChanged(mappedEntity))
                        {
                            currentEntity.UpdateFromNewData(mappedEntity);
                            updatedEntities.Add(currentEntity);
                            result.UpdatedRecords++;
                            result.TotalSynced++;
                        }
                    }
                }

                // Thêm các bản ghi mới vào kho dữ liệu
                if (insertedEntities.Any())
                {
                    await _gmvMaxProductDetailProductReportRepository.InsertManyAsync(insertedEntities);
                }
                // Cập nhật các bản ghi đã tồn tại
                if (updatedEntities.Any())
                {
                    await _gmvMaxProductDetailProductReportRepository.UpdateManyAsync(updatedEntities);
                }

                result.DayCount += pageData.Count;
                result.CampaignCount += pageData.Select(x => x.campaignId).Distinct().Count();
                result.StoreCount += pageData.Select(x => x.storeId).Distinct().Count();
                result.ProductCount += pageData.Select(x => x.reportItem.Dimensions?.GetValueOrDefault("item_group_id")?.ToString()).Distinct().Count();

                await uow.CompleteAsync();
            }
        }

        /// <summary>
        /// Lấy các entity hiện có trong database
        /// </summary>
        private async Task<List<RawGmvMaxProductDetailProductReportEntity>> GetExistingEntitiesAsync(string bcId, List<RawGmvMaxProductDetailProductReportEntity> mappedEntities)
        {
            if (!mappedEntities.Any())
                return new List<RawGmvMaxProductDetailProductReportEntity>();

            var campaignIds = mappedEntities.Select(x => x.CampaignId).Distinct().ToList();
            var advertiserIds = mappedEntities.Select(x => x.AdvertiserId).Distinct().ToList();
            var itemGroupIds = mappedEntities.Select(x => x.ItemGroupId).Distinct().ToList();

            var minDate = mappedEntities.Min(x => x.Date);
            var maxDate = mappedEntities.Max(x => x.Date);

            var query = await _gmvMaxProductDetailProductReportRepository.GetQueryableAsync();
            return query.Where(x => x.BcId == bcId &&
                                    campaignIds.Contains(x.CampaignId) &&
                                    advertiserIds.Contains(x.AdvertiserId) &&
                                    itemGroupIds.Contains(x.ItemGroupId) &&
                                    x.Date >= minDate && x.Date <= maxDate).ToList();
        }

        private Task<List<RawGmvMaxProductDetailProductReportEntity>> MapListReportDataToEntitiesAsync(string bcId, string bcTimezone, List<(GMVMaxReportItem reportItem, string advertiserId, string storeId, string campaignId)> reportDataList)
        {
            var entities = new List<RawGmvMaxProductDetailProductReportEntity>();
            foreach (var (reportItem, advertiserId, storeId, campaignId) in reportDataList)
            {
                var entity = MapReportDataToEntity(bcId, bcTimezone, reportItem, advertiserId, storeId, campaignId);
                if (entity != null)
                {
                    entities.Add(entity);
                }
            }
            return Task.FromResult(entities);
        }

        /// <summary>
        /// Map dữ liệu báo cáo từ API sang entity
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <param name="bcTimezone">Timezone của Business Center</param>
        /// <param name="reportData">Dữ liệu báo cáo</param>
        /// <param name="advertiserId">ID của Advertiser</param>
        /// <param name="storeId">ID của Store</param>
        /// <param name="campaignId">ID của Campaign</param>
        /// <returns>RawGmvMaxProductDetailProductReportEntity</returns>
        private RawGmvMaxProductDetailProductReportEntity? MapReportDataToEntity(string bcId, string bcTimezone, GMVMaxReportItem reportData, string advertiserId, string storeId, string campaignId)
        {
            var dateTimeStr = reportData.Dimensions?.GetValueOrDefault("stat_time_day")?.ToString();

            if (string.IsNullOrEmpty(dateTimeStr) || !DateTime.TryParse(dateTimeStr, out var reportDateTime))
            {
                _logger.LogWarning("Ngày giờ báo cáo chi tiết cấp sản phẩm GMV Max Product Campaign không hợp lệ: {DateTime} cho BC: {BcId}", dateTimeStr, bcId);
                return null;
            }

            // Convert datetime từ timezone của campaign sang UTC
            var reportDateTimeUtc = _dateTimeService.ConvertToUtc(reportDateTime, bcTimezone);

            var itemGroupId = reportData.Dimensions?.GetValueOrDefault("item_group_id")?.ToString();
            if (string.IsNullOrEmpty(itemGroupId))
            {
                _logger.LogWarning("Item Group ID không hợp lệ cho BC: {BcId}", bcId);
                return null;
            }

            var entity = new RawGmvMaxProductDetailProductReportEntity(Guid.NewGuid())
            {
                BcId = bcId,
                AdvertiserId = advertiserId,
                StoreId = storeId,
                CampaignId = campaignId,
                ItemGroupId = itemGroupId,
                Date = reportDateTimeUtc,  // Lưu datetime UTC với giờ
                Currency = "USD" // Default currency
            };

            if (reportData.Metrics != null)
            {
                // Map các metrics từ API response
                entity.ProductName = GetStringValue(reportData.Metrics, "product_name");
                entity.ProductImageUrl = GetStringValue(reportData.Metrics, "product_image_url");

                // Map product status
                var productStatusStr = GetStringValue(reportData.Metrics, "product_status");
                if (!string.IsNullOrEmpty(productStatusStr) && Enum.TryParse<TikTok.Enums.ProductStatus>(productStatusStr, true, out var productStatus))
                {
                    entity.ProductStatus = productStatus;
                }

                // Map bid type
                var bidTypeStr = GetStringValue(reportData.Metrics, "bid_type");
                if (!string.IsNullOrEmpty(bidTypeStr) && Enum.TryParse<TikTok.Enums.ProductCampaignBidType>(bidTypeStr, true, out var bidType))
                {
                    entity.BidType = bidType;
                }

                entity.Orders = GetIntValue(reportData.Metrics, "orders");
                entity.GrossRevenue = GetDecimalValue(reportData.Metrics, "gross_revenue");

                var currency = GetStringValue(reportData.Metrics, "currency");
                if (!string.IsNullOrEmpty(currency))
                {
                    entity.Currency = currency;
                }
            }

            return entity;
        }
    }
}