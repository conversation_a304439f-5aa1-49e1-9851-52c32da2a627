using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// App Service interface cho việc đồng bộ dữ liệu hồ sơ chi phí
    /// </summary>
    public interface ICostProfileSyncApplicationAppService : IApplicationService
    {
        /// <summary>
        /// Đồng bộ hồ sơ chi phí theo BC ID
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ</returns>
        Task<CostProfileSyncResult> SyncCostProfilesAsync(string bcId);

        /// <summary>
        /// Đồng bộ hồ sơ chi phí cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ</returns>
        Task<CostProfileSyncResult> SyncAllCostProfilesAsync();
    }
}