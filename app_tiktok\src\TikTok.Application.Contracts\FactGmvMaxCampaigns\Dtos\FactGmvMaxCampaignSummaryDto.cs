using System;

namespace TikTok.FactGmvMaxCampaigns.Dtos
{
    public class FactGmvMaxCampaignSummaryDto
    {
        // ========== Date Range ==========
        
        /// <summary>
        /// Ngày bắt đầu
        /// </summary>
        public DateTime FromDate { get; set; }

        /// <summary>
        /// Ngày kết thúc
        /// </summary>
        public DateTime ToDate { get; set; }

        /// <summary>
        /// Tổng số bản ghi
        /// </summary>
        public int TotalRecords { get; set; }

        // ========== Financial Metrics (USD) ==========

        /// <summary>
        /// Tổng chi phí quảng cáo (USD)
        /// </summary>
        public decimal TotalCostUSD { get; set; }

        /// <summary>
        /// Tổng doanh thu (USD)
        /// </summary>
        public decimal TotalGrossRevenueUSD { get; set; }

        /// <summary>
        /// Tổng doanh thu quảng cáo (USD)
        /// </summary>
        public decimal TotalAdsRevenueUSD { get; set; }

        /// <summary>
        /// Tổng doanh thu tự nhiên (USD)
        /// </summary>
        public decimal TotalOrganicRevenueUSD { get; set; }

        /// <summary>
        /// Tổng ngân sách (USD)
        /// </summary>
        public decimal TotalBudgetUSD { get; set; }

        /// <summary>
        /// Chi phí trung bình mỗi đơn hàng (USD)
        /// </summary>
        public decimal AvgCostPerOrderUSD { get; set; }

        // ========== Performance Metrics ==========

        /// <summary>
        /// ROAS trung bình
        /// </summary>
        public decimal AverageROAS { get; set; }

        /// <summary>
        /// ACOS trung bình (%)
        /// </summary>
        public decimal AverageACOS { get; set; }

        /// <summary>
        /// ROI trung bình (%)
        /// </summary>
        public decimal AverageROI { get; set; }

        /// <summary>
        /// CTR trung bình (%)
        /// </summary>
        public decimal AverageCTR { get; set; }

        /// <summary>
        /// CPM trung bình (USD)
        /// </summary>
        public decimal AverageCPM { get; set; }

        /// <summary>
        /// CPC trung bình (USD)
        /// </summary>
        public decimal AverageCPC { get; set; }

        // ========== Volume Metrics ==========

        /// <summary>
        /// Tổng số đơn hàng
        /// </summary>
        public int TotalOrders { get; set; }

        /// <summary>
        /// Tổng lượt hiển thị
        /// </summary>
        public long TotalImpressions { get; set; }

        /// <summary>
        /// Tổng lượt nhấp
        /// </summary>
        public long TotalClicks { get; set; }

        // ========== Campaign Metrics ==========

        /// <summary>
        /// Số chiến dịch duy nhất
        /// </summary>
        public int UniqueCampaigns { get; set; }

        /// <summary>
        /// Số store duy nhất
        /// </summary>
        public int UniqueStores { get; set; }

        /// <summary>
        /// Số tài khoản quảng cáo duy nhất
        /// </summary>
        public int UniqueAdAccounts { get; set; }

        /// <summary>
        /// Số sản phẩm duy nhất
        /// </summary>
        public int UniqueProducts { get; set; }

        /// <summary>
        /// Số ngày có dữ liệu
        /// </summary>
        public int UniqueDates { get; set; }

        // ========== Performance Classification ==========

        /// <summary>
        /// Số chiến dịch có ROAS > 3.0 (Xuất sắc)
        /// </summary>
        public int ExcellentROASCampaigns { get; set; }

        /// <summary>
        /// Số chiến dịch có ROAS 2.0-3.0 (Tốt)
        /// </summary>
        public int GoodROASCampaigns { get; set; }

        /// <summary>
        /// Số chiến dịch có ROAS < 2.0 (Cần cải thiện)
        /// </summary>
        public int PoorROASCampaigns { get; set; }

        /// <summary>
        /// Số chiến dịch có ACOS > 50% (Chi phí cao)
        /// </summary>
        public int HighACOSCampaigns { get; set; }

        /// <summary>
        /// Phần trăm ngân sách đã sử dụng (%)
        /// </summary>
        public decimal BudgetUtilizationPercentage { get; set; }

        /// <summary>
        /// Tỷ lệ chuyển đổi trung bình (%)
        /// </summary>
        public decimal AverageConversionRate { get; set; }
    }
}
