/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Creative Insights endpoints
    /// </summary>
    public static class CreativeInsightsEndpoints
    {
        /// <summary>
        /// Get ad benchmarks endpoint (v1.3)
        /// </summary>
        public const string GetAdBenchmarks = "/report/ad_benchmark/get/";

        /// <summary>
        /// Get in-second performance endpoint (v1.3)
        /// </summary>
        public const string GetVideoPerformance = "/report/video_performance/get/";
    }
}
