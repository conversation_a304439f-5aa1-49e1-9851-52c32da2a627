/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models.Account
{
    /// <summary>
    /// Request model for extending the authorization validity period of a TikTok post
    /// </summary>
    public class PostAuthorizationExtensionRequest
    {
        /// <summary>
        /// Application specific unique identifier for the TikTok account
        /// </summary>
        [JsonPropertyName("business_id")]
        public string BusinessId { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the TikTok post
        /// </summary>
        [JsonPropertyName("item_id")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// The authorization extension period or validity period of new authorization code in days
        /// Supported values: 7, 30, 60, 180, 365
        /// </summary>
        [JsonPropertyName("authorization_days")]
        public int? AuthorizationDays { get; set; }
    }
}
