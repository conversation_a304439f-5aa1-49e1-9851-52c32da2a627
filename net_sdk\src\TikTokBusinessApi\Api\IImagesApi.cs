/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.IO;
using System.Threading;
using System.Threading.Tasks;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Interface for TikTok Business API Images operations
    /// </summary>
    public interface IImagesApi
    {
        /// <summary>
        /// Upload an image to the Asset Library and use the obtained image ID for creating ads
        /// </summary>
        /// <param name="request">Image upload request parameters</param>
        /// <param name="fileStream">Image file stream (required when upload_type is UPLOAD_BY_FILE)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing image upload information</returns>
        //Task<ImageUploadResponse> UploadImageAsync(
        //    ImageUploadRequest request,
        //    Stream? fileStream = null,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Update the name of an image
        /// </summary>
        /// <param name="request">Image update request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the asynchronous operation</returns>
        //Task UpdateImageAsync(
        //    ImageUpdateRequest request,
        //    CancellationToken cancellationToken = default);

        /// <summary>
        /// Get information about images from the Asset Library
        /// </summary>
        /// <param name="request">Image info request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing image information</returns>
        Task<ImageInfoResponse> GetImageInfoAsync(
            ImageInfoRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Search for image creatives in an advertising account's Asset Library
        /// </summary>
        /// <param name="request">Image search request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing search results</returns>
        Task<ImageSearchResponse> SearchImagesAsync(
            ImageSearchRequest request,
            CancellationToken cancellationToken = default);
    }
}
