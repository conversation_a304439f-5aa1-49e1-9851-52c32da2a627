/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TikTokBusinessApi.Models
{
    #region Music Authorization Models

    /// <summary>
    /// Music authorization information for TikTok posts
    /// </summary>
    public class MusicAuthorizationInfo
    {
        /// <summary>
        /// TikTok post ID
        /// </summary>
        [JsonPropertyName("item_id")]
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// The aggregated authorization status for all the music in the TikTok post
        /// Enum values: WITHOUT_SONG_ID, AUTHORIZATION_MISSING, WITH_FULL_AUTHORIZATION
        /// </summary>
        [JsonPropertyName("music_status")]
        public string MusicStatus { get; set; } = string.Empty;

        /// <summary>
        /// Authorization information for each music
        /// </summary>
        [JsonPropertyName("authorization_infos")]
        public List<AuthorizationInfo> AuthorizationInfos { get; set; } = new();
    }

    /// <summary>
    /// Authorization information for individual music
    /// </summary>
    public class AuthorizationInfo
    {
        /// <summary>
        /// Song ID
        /// </summary>
        [JsonPropertyName("music_id")]
        public string MusicId { get; set; } = string.Empty;

        /// <summary>
        /// Performer
        /// </summary>
        [JsonPropertyName("author")]
        public string Author { get; set; } = string.Empty;

        /// <summary>
        /// Song title
        /// </summary>
        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Labels (tags) for this song
        /// </summary>
        [JsonPropertyName("labels")]
        public List<string> Labels { get; set; } = new();

        /// <summary>
        /// Lyricist
        /// </summary>
        [JsonPropertyName("lyricist")]
        public string Lyricist { get; set; } = string.Empty;

        /// <summary>
        /// Music composer
        /// </summary>
        [JsonPropertyName("composer")]
        public string Composer { get; set; } = string.Empty;

        /// <summary>
        /// Music publisher
        /// </summary>
        [JsonPropertyName("publisher")]
        public string Publisher { get; set; } = string.Empty;

        /// <summary>
        /// Authorization type
        /// Enum values: NOT_AUTHORIZED, AUTHORIZED
        /// </summary>
        [JsonPropertyName("authorization_type")]
        public string AuthorizationType { get; set; } = string.Empty;
    }

    #endregion
}
