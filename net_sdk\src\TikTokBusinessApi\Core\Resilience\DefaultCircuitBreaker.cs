/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Exceptions;

namespace TikTokBusinessApi.Core.Resilience
{
    /// <summary>
    /// Default implementation of circuit breaker pattern
    /// </summary>
    public class DefaultCircuitBreaker : ICircuitBreaker
    {
        private readonly CircuitBreakerOptions _options;
        private readonly ILogger<DefaultCircuitBreaker>? _logger;
        private readonly object _lock = new();
        
        private CircuitBreakerState _state = CircuitBreakerState.Closed;
        private DateTimeOffset _lastStateChange = DateTimeOffset.UtcNow;
        private long _totalRequests;
        private long _successfulRequests;
        private long _failedRequests;
        private int _consecutiveSuccesses;

        /// <summary>
        /// Current state of the circuit breaker
        /// </summary>
        public CircuitBreakerState State
        {
            get
            {
                lock (_lock)
                {
                    return _state;
                }
            }
        }

        /// <summary>
        /// Event raised when circuit breaker state changes
        /// </summary>
        public event EventHandler<CircuitBreakerStateChangedEventArgs>? StateChanged;

        /// <summary>
        /// Initializes a new instance of the DefaultCircuitBreaker class
        /// </summary>
        /// <param name="options">Circuit breaker options</param>
        /// <param name="logger">Logger instance</param>
        public DefaultCircuitBreaker(CircuitBreakerOptions? options = null, ILogger<DefaultCircuitBreaker>? logger = null)
        {
            _options = options ?? new CircuitBreakerOptions();
            _logger = logger;
        }

        /// <summary>
        /// Executes an operation through the circuit breaker
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="operation">Operation to execute</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        /// <exception cref="CircuitBreakerOpenException">Thrown when circuit is open</exception>
        public async Task<T> ExecuteAsync<T>(Func<CancellationToken, Task<T>> operation, CancellationToken cancellationToken = default)
        {
            CheckAndUpdateState();

            if (_state == CircuitBreakerState.Open)
            {
                var metrics = GetMetrics();
                var retryAfter = _lastStateChange.Add(_options.OpenTimeout);
                _logger?.LogWarning("Circuit breaker is open, rejecting request");
                throw new CircuitBreakerOpenException("Circuit breaker is open", metrics, retryAfter);
            }

            try
            {
                _logger?.LogDebug("Executing operation through circuit breaker (state: {State})", _state);
                var result = await operation(cancellationToken);
                RecordSuccess();
                return result;
            }
            catch (Exception ex)
            {
                RecordFailure(ex);
                throw;
            }
        }

        /// <summary>
        /// Manually opens the circuit breaker
        /// </summary>
        public void Open()
        {
            ChangeState(CircuitBreakerState.Open, "Manually opened");
        }

        /// <summary>
        /// Manually closes the circuit breaker
        /// </summary>
        public void Close()
        {
            ChangeState(CircuitBreakerState.Closed, "Manually closed");
        }

        /// <summary>
        /// Resets the circuit breaker to closed state
        /// </summary>
        public void Reset()
        {
            lock (_lock)
            {
                _totalRequests = 0;
                _successfulRequests = 0;
                _failedRequests = 0;
                _consecutiveSuccesses = 0;
                ChangeState(CircuitBreakerState.Closed, "Reset");
            }
        }

        /// <summary>
        /// Gets the current circuit breaker metrics
        /// </summary>
        public CircuitBreakerMetrics GetMetrics()
        {
            lock (_lock)
            {
                return new CircuitBreakerMetrics
                {
                    TotalRequests = _totalRequests,
                    SuccessfulRequests = _successfulRequests,
                    FailedRequests = _failedRequests,
                    LastOpenedAt = _state == CircuitBreakerState.Open ? _lastStateChange : null,
                    LastClosedAt = _state == CircuitBreakerState.Closed ? _lastStateChange : null,
                    TimeInCurrentState = DateTimeOffset.UtcNow - _lastStateChange
                };
            }
        }

        private void CheckAndUpdateState()
        {
            lock (_lock)
            {
                var now = DateTimeOffset.UtcNow;

                // Check if we should transition from Open to HalfOpen
                if (_state == CircuitBreakerState.Open && 
                    now - _lastStateChange >= _options.OpenTimeout)
                {
                    ChangeState(CircuitBreakerState.HalfOpen, "Open timeout expired");
                }

                // Note: Metrics are not reset here based on sampling duration
                // The sampling duration is used for calculating failure rates within a window
                // Metrics are reset when the circuit transitions to Closed state
            }
        }

        private void RecordSuccess()
        {
            lock (_lock)
            {
                _totalRequests++;
                _successfulRequests++;
                _consecutiveSuccesses++;

                _logger?.LogDebug("Circuit breaker recorded success (consecutive: {ConsecutiveSuccesses})", _consecutiveSuccesses);

                // Transition from HalfOpen to Closed if we have enough consecutive successes
                if (_state == CircuitBreakerState.HalfOpen && 
                    _consecutiveSuccesses >= _options.SuccessThreshold)
                {
                    ChangeState(CircuitBreakerState.Closed, "Success threshold reached");
                }
            }
        }

        private void RecordFailure(Exception exception)
        {
            lock (_lock)
            {
                // Check if this exception should count as a failure
                if (_options.IsFailureException != null && !_options.IsFailureException(exception))
                {
                    _logger?.LogDebug("Exception ignored by circuit breaker: {ExceptionType}", exception.GetType().Name);
                    return;
                }

                _totalRequests++;
                _failedRequests++;
                _consecutiveSuccesses = 0;

                _logger?.LogDebug("Circuit breaker recorded failure: {ExceptionType}", exception.GetType().Name);

                // Transition from HalfOpen to Open on any failure
                if (_state == CircuitBreakerState.HalfOpen)
                {
                    ChangeState(CircuitBreakerState.Open, $"Failure in half-open state: {exception.GetType().Name}");
                }
                // Transition from Closed to Open if failure threshold is exceeded
                else if (_state == CircuitBreakerState.Closed && 
                         _totalRequests >= _options.MinimumThroughput)
                {
                    var failureRate = (double)_failedRequests / _totalRequests;
                    if (failureRate >= _options.FailureThreshold)
                    {
                        ChangeState(CircuitBreakerState.Open, $"Failure threshold exceeded: {failureRate:P}");
                    }
                }
            }
        }

        private void ChangeState(CircuitBreakerState newState, string reason)
        {
            var previousState = _state;
            _state = newState;
            _lastStateChange = DateTimeOffset.UtcNow;

            _logger?.LogInformation("Circuit breaker state changed from {PreviousState} to {NewState}: {Reason}", 
                previousState, newState, reason);

            StateChanged?.Invoke(this, new CircuitBreakerStateChangedEventArgs
            {
                PreviousState = previousState,
                NewState = newState,
                Reason = reason
            });
        }
    }

    /// <summary>
    /// No-operation circuit breaker that always allows requests
    /// </summary>
    public class NoOpCircuitBreaker : ICircuitBreaker
    {
        /// <summary>
        /// Always returns Closed state
        /// </summary>
        public CircuitBreakerState State => CircuitBreakerState.Closed;

        /// <summary>
        /// Event that is never raised
        /// </summary>
        public event EventHandler<CircuitBreakerStateChangedEventArgs>? StateChanged;

        /// <summary>
        /// Always executes the operation
        /// </summary>
        public async Task<T> ExecuteAsync<T>(Func<CancellationToken, Task<T>> operation, CancellationToken cancellationToken = default)
        {
            return await operation(cancellationToken);
        }

        /// <summary>
        /// No-op open
        /// </summary>
        public void Open() { }

        /// <summary>
        /// No-op close
        /// </summary>
        public void Close() { }

        /// <summary>
        /// No-op reset
        /// </summary>
        public void Reset() { }

        /// <summary>
        /// Returns empty metrics
        /// </summary>
        public CircuitBreakerMetrics GetMetrics() => new();
    }
}
