/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TikTokBusinessApi;
using TikTokBusinessApi.Exceptions;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Examples
{
    /// <summary>
    /// Example demonstrating how to use the Creative Insights API
    /// </summary>
    public class CreativeInsightsApiExample
    {
        private readonly TikTokBusinessApiClient _client;

        public CreativeInsightsApiExample(string accessToken)
        {
            _client = TikTokBusinessApiClient.Create(accessToken);
        }

        /// <summary>
        /// Example: Get ad benchmarks
        /// </summary>
        public async Task GetAdBenchmarksExample()
        {
            try
            {
                Console.WriteLine("=== Get Ad Benchmarks Example ===");

                const string accessToken = "your-access-token";
                
                var request = new AdBenchmarkRequest
                {
                    AdvertiserId = "your-advertiser-id",
                    Dimensions = new List<string> { "LOCATION", "PLACEMENT" },
                    Filtering = new AdBenchmarkFiltering
                    {
                        AdIds = new List<string> { "ad-id-1", "ad-id-2" }
                    },
                    CompareTimeWindow = "14", // 14 days
                    MetricsFields = new List<string> { "ctr", "cpm", "cvr" },
                    Page = 1,
                    PageSize = 10
                };

                var response = await _client.CreativeInsights.GetAdBenchmarksAsync(accessToken, request);

                if (response.Data?.List != null)
                {
                    Console.WriteLine($"Found {response.Data.List.Count} benchmark results");
                    Console.WriteLine($"Comparison date: {response.Data.CompareDate}");

                    foreach (var result in response.Data.List)
                    {
                        if (result.Info != null)
                        {
                            Console.WriteLine($"\nAd ID: {result.Info.AdId}");
                            Console.WriteLine($"Location: {result.Info.Location}");
                            Console.WriteLine($"Placement: {result.Info.Placement}");
                        }

                        if (result.Metrics != null)
                        {
                            Console.WriteLine("Benchmark metrics:");
                            foreach (var metric in result.Metrics)
                            {
                                Console.WriteLine($"  {metric.Key}: {metric.Value:F2} (percentile)");
                            }
                        }
                    }

                    if (response.Data.PageInfo != null)
                    {
                        Console.WriteLine($"\nPage {response.Data.PageInfo.Page} of {response.Data.PageInfo.TotalPage}");
                        Console.WriteLine($"Total results: {response.Data.PageInfo.TotalNumber}");
                    }
                }
            }
            catch (TikTokBusinessApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get video performance (in-second data)
        /// </summary>
        public async Task GetVideoPerformanceExample()
        {
            try
            {
                Console.WriteLine("=== Get Video Performance Example ===");

                const string accessToken = "your-access-token";
                
                var request = new VideoPerformanceRequest
                {
                    AdvertiserId = "your-advertiser-id",
                    ReportType = "VIDEO", // Use VIDEO for Video Insights data
                    Filtering = new VideoPerformanceFiltering
                    {
                        VideoIds = new List<string> { "video-id-1" },
                        Lifetime = true // Get lifetime data
                    },
                    MetricsFields = new List<string> { "clicks", "conversions", "drop_off", "retain" },
                    Page = 1,
                    PageSize = 10
                };

                var response = await _client.CreativeInsights.GetVideoPerformanceAsync(accessToken, request);

                if (response.Data?.List != null)
                {
                    Console.WriteLine($"Found {response.Data.List.Count} video performance results");

                    foreach (var result in response.Data.List)
                    {
                        if (result.Info != null)
                        {
                            Console.WriteLine($"\nVideo ID: {result.Info.VideoId}");
                            Console.WriteLine($"Ad ID: {result.Info.AdId}");
                            Console.WriteLine($"Duration: {result.Info.Duration} seconds");
                        }

                        if (result.Metrics != null)
                        {
                            Console.WriteLine("In-second performance metrics:");
                            foreach (var metric in result.Metrics)
                            {
                                Console.WriteLine($"  {metric.Key}: [{string.Join(", ", metric.Value)}]");
                                
                                // Show summary for first few seconds
                                if (metric.Value.Count > 0)
                                {
                                    var first5Seconds = metric.Value.Take(Math.Min(5, metric.Value.Count));
                                    Console.WriteLine($"    First 5 seconds: [{string.Join(", ", first5Seconds)}]");
                                }
                            }
                        }
                    }

                    if (response.Data.PageInfo != null)
                    {
                        Console.WriteLine($"\nPage {response.Data.PageInfo.Page} of {response.Data.PageInfo.TotalPage}");
                        Console.WriteLine($"Total results: {response.Data.PageInfo.TotalNumber}");
                    }
                }
            }
            catch (TikTokBusinessApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Get ad performance (legacy AD report type)
        /// </summary>
        public async Task GetAdPerformanceExample()
        {
            try
            {
                Console.WriteLine("=== Get Ad Performance Example ===");

                const string accessToken = "your-access-token";
                
                var request = new VideoPerformanceRequest
                {
                    AdvertiserId = "your-advertiser-id",
                    ReportType = "AD", // Use AD for ad performance data (to be deprecated)
                    Filtering = new VideoPerformanceFiltering
                    {
                        AdIds = new List<string> { "ad-id-1", "ad-id-2" }
                    },
                    MetricsFields = new List<string> { "view_count", "click_count", "like_count", "share_count" },
                    Page = 1,
                    PageSize = 10
                };

                var response = await _client.CreativeInsights.GetVideoPerformanceAsync(accessToken, request);

                if (response.Data?.List != null)
                {
                    Console.WriteLine($"Found {response.Data.List.Count} ad performance results");

                    foreach (var result in response.Data.List)
                    {
                        if (result.Info != null)
                        {
                            Console.WriteLine($"\nAd ID: {result.Info.AdId}");
                            Console.WriteLine($"Video ID: {result.Info.VideoId}");
                            Console.WriteLine($"Duration: {result.Info.Duration} seconds");
                        }

                        if (result.Metrics != null)
                        {
                            Console.WriteLine("In-second ad performance metrics:");
                            foreach (var metric in result.Metrics)
                            {
                                var total = metric.Value.Sum();
                                Console.WriteLine($"  {metric.Key}: Total = {total}, Peak = {metric.Value.Max()}");
                            }
                        }
                    }
                }
            }
            catch (TikTokBusinessApiException ex)
            {
                Console.WriteLine($"API Error: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _client?.Dispose();
        }
    }

    /// <summary>
    /// Program entry point for running examples
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            const string accessToken = "your-access-token-here";
            
            using var example = new CreativeInsightsApiExample(accessToken);
            
            try
            {
                Console.WriteLine("=== TikTok Business API - Creative Insights Examples ===\n");
                
                // Example 1: Get ad benchmarks
                await example.GetAdBenchmarksExample();
                
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 2: Get video performance (Video Insights)
                await example.GetVideoPerformanceExample();
                
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // Example 3: Get ad performance (legacy)
                await example.GetAdPerformanceExample();
                
                Console.WriteLine("\nAll examples completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error running examples: {ex.Message}");
            }
        }
    }
}
