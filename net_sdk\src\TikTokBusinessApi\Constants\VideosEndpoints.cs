/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

namespace TikTokBusinessApi.Constants
{
    /// <summary>
    /// Constants for TikTok Business API Videos endpoints
    /// </summary>
    public static class VideosEndpoints
    {
        /// <summary>
        /// Upload a video endpoint (v1.3)
        /// </summary>
        public const string Upload = "/file/video/ad/upload/";

        /// <summary>
        /// Update the name of a video endpoint (v1.3)
        /// </summary>
        public const string Update = "/file/video/ad/update/";

        /// <summary>
        /// Get info about videos endpoint (v1.3)
        /// </summary>
        public const string GetInfo = "/file/video/ad/info/";

        /// <summary>
        /// Search for videos endpoint (v1.3)
        /// </summary>
        public const string Search = "/file/video/ad/search/";

        /// <summary>
        /// Get suggested thumbnails for a video endpoint (v1.3)
        /// </summary>
        public const string GetSuggestedThumbnails = "/file/video/suggestcover/";
    }
}
