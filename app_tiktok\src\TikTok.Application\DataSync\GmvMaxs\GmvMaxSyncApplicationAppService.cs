using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace TikTok.DataSync
{
    /// <summary>
    /// Application Service implementation cho việc đồng bộ tối ưu dữ liệu GMV Max
    /// </summary>
    public class GmvMaxSyncApplicationAppService : ApplicationService, IGmvMaxSyncApplicationAppService
    {
        private readonly IGmvMaxSyncService _gmvMaxSyncService;

        public GmvMaxSyncApplicationAppService(IGmvMaxSyncService gmvMaxSyncService)
        {
            _gmvMaxSyncService = gmvMaxSyncService;
        }

        /// <summary>
        /// Đồng bộ tất cả dữ liệu GMV Max cho một Business Center
        /// </summary>
        /// <param name="bcId">ID của Business Center</param>
        /// <returns>Kết quả đồng bộ tổng hợp</returns>
        public async Task<GmvMaxSyncResult> SyncGmvMaxAsync(string bcId)
        {
            return await _gmvMaxSyncService.SyncGmvMaxAsync(bcId);
        }

        /// <summary>
        /// Đồng bộ tất cả dữ liệu GMV Max cho tất cả Business Centers
        /// </summary>
        /// <returns>Kết quả đồng bộ tổng hợp</returns>
        public async Task<GmvMaxSyncResult> SyncAllGmvMaxAsync()
        {
            return await _gmvMaxSyncService.SyncAllGmvMaxAsync();
        }
    }
} 