/*
 * Copyright 2023 TikTok Pte. Ltd.
 *
 * This source code is licensed under the MIT license found in
 * the LICENSE file in the root directory of this source tree.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TikTokBusinessApi.Constants;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Models;

namespace TikTokBusinessApi.Api
{
    /// <summary>
    /// Implementation of TikTok Business API BC Partners operations
    /// </summary>
    public class BCPartnersApi : IBCPartnersApi
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<BCPartnersApi>? _logger;

        /// <summary>
        /// Initializes a new instance of the BCPartnersApi class
        /// </summary>
        /// <param name="apiClient">API client for making HTTP requests</param>
        /// <param name="logger">Logger instance (optional)</param>
        public BCPartnersApi(IApiClient apiClient, ILogger<BCPartnersApi>? logger = null)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _logger = logger;
        }

        /// <summary>
        /// Get the list of partners of a Business Center. You need to be an Admin of the Business Center.
        /// </summary>
        /// <param name="request">Request parameters for getting partners</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the list of partners</returns>
        public async Task<GetPartnersResponse> GetPartnersAsync(
            GetPartnersRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BcId))
                throw new ArgumentException("BcId is required", nameof(request));

            _logger?.LogInformation("Getting partners for BC {BcId}", request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId
            };

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();
            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering);

            return await _apiClient.CallApiAsync<GetPartnersResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCPartnersEndpoints.GetPartners}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }

        // /// <summary>
        // /// Add a partner and optionally share your assets to a Business Center. A Business Center can only share assets it owns. You need to be an Admin of the Business Center.
        // /// </summary>
        // /// <param name="request">Request parameters for adding a partner</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response confirming the partner was added</returns>
        // public async Task<AddPartnerResponse> AddPartnerAsync(
        //     AddPartnerRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.BcId))
        //         throw new ArgumentException("BcId is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.PartnerId))
        //         throw new ArgumentException("PartnerId is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.AssetType))
        //         throw new ArgumentException("AssetType is required", nameof(request));

        //     _logger?.LogInformation("Adding partner {PartnerId} to BC {BcId}", request.PartnerId, request.BcId);

        //     return await _apiClient.CallApiAsync<AddPartnerResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCPartnersEndpoints.AddPartner}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <summary>
        // /// Delete a partner in the Business Center. You need to be an Admin of the Business Center.
        // /// </summary>
        // /// <param name="request">Request parameters for deleting a partner</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response confirming the partner was deleted</returns>
        // public async Task<DeletePartnerResponse> DeletePartnerAsync(
        //     DeletePartnerRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.BcId))
        //         throw new ArgumentException("BcId is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.PartnerId))
        //         throw new ArgumentException("PartnerId is required", nameof(request));

        //     _logger?.LogInformation("Deleting partner {PartnerId} from BC {BcId}", request.PartnerId, request.BcId);

        //     return await _apiClient.CallApiAsync<DeletePartnerResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCPartnersEndpoints.DeletePartner}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        // /// <summary>
        // /// Cancel the sharing of assets with a partner. You need to be an Admin of the Business Center.
        // /// </summary>
        // /// <param name="request">Request parameters for canceling asset sharing</param>
        // /// <param name="cancellationToken">Cancellation token</param>
        // /// <returns>Response confirming the asset sharing was canceled</returns>
        // public async Task<CancelAssetSharingResponse> CancelAssetSharingAsync(
        //     CancelAssetSharingRequest request,
        //     CancellationToken cancellationToken = default)
        // {
        //     if (request == null)
        //         throw new ArgumentNullException(nameof(request));

        //     if (string.IsNullOrEmpty(request.BcId))
        //         throw new ArgumentException("BcId is required", nameof(request));

        //     if (string.IsNullOrEmpty(request.PartnerId))
        //         throw new ArgumentException("PartnerId is required", nameof(request));

        //     if (request.AssetIds == null || request.AssetIds.Count == 0)
        //         throw new ArgumentException("AssetIds is required and cannot be empty", nameof(request));

        //     if (string.IsNullOrEmpty(request.AssetType))
        //         throw new ArgumentException("AssetType is required", nameof(request));

        //     _logger?.LogInformation("Canceling asset sharing for partner {PartnerId} in BC {BcId}", request.PartnerId, request.BcId);

        //     return await _apiClient.CallApiAsync<CancelAssetSharingResponse>(
        //         $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCPartnersEndpoints.CancelAssetSharing}",
        //         HttpMethod.Post,
        //         queryParams: null,
        //         body: request,
        //         headerParams: null,
        //         authNames: null,
        //         cancellationToken: cancellationToken);
        // }

        /// <summary>
        /// Get assets that you have shared with a partner, or assets that are shared by a partner. You need to be an Admin of the Business Center.
        /// </summary>
        /// <param name="request">Request parameters for getting partner assets</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response containing the list of partner assets</returns>
        public async Task<GetPartnerAssetsResponse> GetPartnerAssetsAsync(
            GetPartnerAssetsRequest request,
            CancellationToken cancellationToken = default)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrEmpty(request.BcId))
                throw new ArgumentException("BcId is required", nameof(request));

            if (string.IsNullOrEmpty(request.PartnerId))
                throw new ArgumentException("PartnerId is required", nameof(request));

            if (string.IsNullOrEmpty(request.AssetType))
                throw new ArgumentException("AssetType is required", nameof(request));

            if (string.IsNullOrEmpty(request.ShareType))
                throw new ArgumentException("ShareType is required", nameof(request));

            _logger?.LogInformation("Getting partner assets for partner {PartnerId} in BC {BcId}", request.PartnerId, request.BcId);

            var queryParams = new Dictionary<string, string>
            {
                ["bc_id"] = request.BcId,
                ["partner_id"] = request.PartnerId,
                ["asset_type"] = request.AssetType,
                ["share_type"] = request.ShareType
            };

            if (request.Page.HasValue)
                queryParams["page"] = request.Page.Value.ToString();
            if (request.PageSize.HasValue)
                queryParams["page_size"] = request.PageSize.Value.ToString();
            if (request.Filtering != null)
                queryParams["filtering"] = JsonSerializer.Serialize(request.Filtering);

            return await _apiClient.CallApiAsync<GetPartnerAssetsResponse>(
                $"{_apiClient.ApiPrefix}/{_apiClient.ApiVersion}{BCPartnersEndpoints.GetPartnerAssets}",
                HttpMethod.Get,
                queryParams: queryParams,
                body: null,
                headerParams: null,
                authNames: null,
                cancellationToken: cancellationToken);
        }
    }
}
