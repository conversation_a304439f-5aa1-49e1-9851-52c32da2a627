using System;
using TikTok.Enums;

namespace TikTok.Application.Contracts.LatestNotifications
{
    /// <summary>
    /// DTO cho thông báo mới nhất
    /// </summary>
    public class LatestNotificationDto
    {
        /// <summary>
        /// ID của thông báo
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Id của đối tượng
        /// </summary>
        public string ObjectId { get; set; } = string.Empty;

        /// <summary>
        /// Loại thông báo: Balance, transaction,....
        /// </summary>
        public NotificationType Type { get; set; }

        /// <summary>
        /// Thông tin
        /// </summary>
        public string? Payload { get; set; }

        /// <summary>
        /// Rule thông báo
        /// </summary>
        public string? Rule { get; set; }

        /// <summary>
        /// Thời gian gửi thông báo cuối cùng
        /// </summary>
        public DateTime LastNotifiedTime { get; set; }

        /// <summary>
        /// Thời gian tạo
        /// </summary>
        public DateTime CreationTime { get; set; }
    }
}
